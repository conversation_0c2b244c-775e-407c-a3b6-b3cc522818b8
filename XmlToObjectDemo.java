import java.math.BigDecimal;
import java.util.List;

/**
 * XML转换为对象的演示程序
 * 展示优化后的XmlMessageParser功能
 */
public class XmlToObjectDemo {
    
    // 模拟TransferOrderResponse类
    public static class TransferOrderResponse {
        private String status;
        private String statusText;
        private Integer sucTotalNum;
        private BigDecimal sucTotalAmt;
        private Integer errTotalNum;
        private BigDecimal errTotalAmt;
        private List<TransferOrderResponseRow> row;
        
        // Getters and Setters
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getStatusText() { return statusText; }
        public void setStatusText(String statusText) { this.statusText = statusText; }
        public Integer getSucTotalNum() { return sucTotalNum; }
        public void setSucTotalNum(Integer sucTotalNum) { this.sucTotalNum = sucTotalNum; }
        public BigDecimal getSucTotalAmt() { return sucTotalAmt; }
        public void setSucTotalAmt(BigDecimal sucTotalAmt) { this.sucTotalAmt = sucTotalAmt; }
        public Integer getErrTotalNum() { return errTotalNum; }
        public void setErrTotalNum(Integer errTotalNum) { this.errTotalNum = errTotalNum; }
        public BigDecimal getErrTotalAmt() { return errTotalAmt; }
        public void setErrTotalAmt(BigDecimal errTotalAmt) { this.errTotalAmt = errTotalAmt; }
        public List<TransferOrderResponseRow> getRow() { return row; }
        public void setRow(List<TransferOrderResponseRow> row) { this.row = row; }
        
        @Override
        public String toString() {
            return "TransferOrderResponse{" +
                    "status='" + status + '\'' +
                    ", statusText='" + statusText + '\'' +
                    ", sucTotalNum=" + sucTotalNum +
                    ", sucTotalAmt=" + sucTotalAmt +
                    ", errTotalNum=" + errTotalNum +
                    ", errTotalAmt=" + errTotalAmt +
                    ", row=" + row +
                    '}';
        }
    }
    
    // 模拟TransferOrderResponseRow类
    public static class TransferOrderResponseRow {
        private String clientID;
        private String status;
        private String statusText;
        
        // Getters and Setters
        public String getClientID() { return clientID; }
        public void setClientID(String clientID) { this.clientID = clientID; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getStatusText() { return statusText; }
        public void setStatusText(String statusText) { this.statusText = statusText; }
        
        @Override
        public String toString() {
            return "TransferOrderResponseRow{" +
                    "clientID='" + clientID + '\'' +
                    ", status='" + status + '\'' +
                    ", statusText='" + statusText + '\'' +
                    '}';
        }
    }
    
    public static void main(String[] args) {
        System.out.println("=== XML转换为对象功能演示 ===");
        
        // 演示XML结构
        String xmlContent = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<stream>\n" +
                "    <status>SUCCESS</status>\n" +
                "    <statusText>交易成功</statusText>\n" +
                "    <sucTotalNum>2</sucTotalNum>\n" +
                "    <sucTotalAmt>1500.00</sucTotalAmt>\n" +
                "    <errTotalNum>0</errTotalNum>\n" +
                "    <errTotalAmt>0.00</errTotalAmt>\n" +
                "    <list name=\"userDataList\">\n" +
                "        <row>\n" +
                "            <clientID>TXN001</clientID>\n" +
                "            <status>SUCCESS</status>\n" +
                "            <statusText>转账成功</statusText>\n" +
                "        </row>\n" +
                "        <row>\n" +
                "            <clientID>TXN002</clientID>\n" +
                "            <status>SUCCESS</status>\n" +
                "            <statusText>转账成功</statusText>\n" +
                "        </row>\n" +
                "    </list>\n" +
                "</stream>";
        
        System.out.println("输入的XML结构:");
        System.out.println(xmlContent);
        
        System.out.println("\n=== 优化功能说明 ===");
        System.out.println("1. 支持根据传入的类映射字段进行XML转换");
        System.out.println("2. 对于List<T>类型的字段，使用 <list name=\"字段名\"><row></row></list> 格式");
        System.out.println("3. 智能匹配List字段名：");
        System.out.println("   - 直接匹配字段名（如：row字段匹配userDataList）");
        System.out.println("   - 匹配字段名 + \"List\"");
        System.out.println("   - 向后兼容userDataList");
        System.out.println("4. 支持多个List字段的处理");
        System.out.println("5. 保持向后兼容性");
        
        System.out.println("\n=== 期望的解析结果 ===");
        System.out.println("TransferOrderResponse对象:");
        System.out.println("  - status: SUCCESS");
        System.out.println("  - statusText: 交易成功");
        System.out.println("  - sucTotalNum: 2");
        System.out.println("  - sucTotalAmt: 1500.00");
        System.out.println("  - errTotalNum: 0");
        System.out.println("  - errTotalAmt: 0.00");
        System.out.println("  - row: List<TransferOrderResponseRow> (2条记录)");
        System.out.println("    - row[0]: {clientID: TXN001, status: SUCCESS, statusText: 转账成功}");
        System.out.println("    - row[1]: {clientID: TXN002, status: SUCCESS, statusText: 转账成功}");
        
        System.out.println("\n=== 核心优化点 ===");
        System.out.println("✓ 智能字段映射：自动识别List字段并映射到对应的<list>元素");
        System.out.println("✓ 灵活的匹配策略：支持多种命名规则的list元素匹配");
        System.out.println("✓ 类型安全：利用泛型确保List元素类型正确");
        System.out.println("✓ 向后兼容：保持与现有代码的兼容性");
        System.out.println("✓ 错误处理：优雅处理解析异常和类型转换");
        
        System.out.println("\n=== 使用方法 ===");
        System.out.println("// 直接解析为对象");
        System.out.println("TransferOrderResponse response = XmlMessageParser.parseXmlToEntity(xmlContent, TransferOrderResponse.class);");
        System.out.println("");
        System.out.println("// 使用XmlParseResult包装解析");
        System.out.println("XmlParseResult<TransferOrderResponse> result = XmlMessageParser.parseXmlResponse(xmlContent, TransferOrderResponse.class);");
        System.out.println("TransferOrderResponse response = result.getSingleData();");
        
        System.out.println("\n🎉 XML转换为对象功能优化完成！");
        System.out.println("现在可以更灵活地处理包含List字段的XML到对象转换。");
    }
}
