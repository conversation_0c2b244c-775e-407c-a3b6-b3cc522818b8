{"name": "ruoyi", "version": "3.8.1", "description": "若依管理系统", "author": "若依", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "oauth": "vue-cli-service serve --mode oauth", "test": "vue-cli-service serve --mode testbeta", "test2": "vue-cli-service serve --mode testbeta2", "uat": "vue-cli-service serve --mode uat", "uat2": "vue-cli-service serve --mode uat2", "uat3": "vue-cli-service serve --mode uat3", "prod": "vue-cli-service serve --mode production", "build:prod": "vue-cli-service build", "build:oauth": "vue-cli-service build --mode oauth", "build:test": "vue-cli-service build --mode testbeta", "build:test2": "vue-cli-service build --mode testbeta2", "build:uat": "vue-cli-service build --mode uat", "build:uat2": "vue-cli-service build --mode uat2", "build:uat3": "vue-cli-service build --mode uat3", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "dependencies": {"@babel/polyfill": "^7.4.4", "axios": "0.24.0", "chinese-to-pinyin": "^1.3.1", "clipboard": "2.0.8", "core-js": "^3.25.3", "decimal.js": "^10.4.3", "echarts": "4.9.0", "element-ui": "^2.15.14", "file-saver": "2.0.5", "font-awesome": "^4.7.0", "hey-global": "^1.0.0", "hey-utils": "^1.0.2", "heyui": "^1.28.0", "jquery": "^3.7.0", "js-cookie": "3.0.1", "lodash.debounce": "^4.0.8", "moment": "^2.24.0", "qs": "^6.11.2", "string-utilz": "^1.4.0", "vcolorpicker": "^2.0.12", "vue": "2.6.12", "vue-router": "3.4.9", "vuex": "3.6.0", "wpk-reporter": "^0.9.3"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "less": "^3.0.4", "less-loader": "^4.1.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "style-resources-loader": "^1.5.0", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}