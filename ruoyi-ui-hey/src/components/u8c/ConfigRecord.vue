<template>
  <div>
    <el-dialog
      title="智慧财务系统与用友U8C关联关系修改记录"
      :visible.sync="showDialog"
      append-to-body
      width="1000px"
      :before-close="handleCancel"
    >
      <el-table :data="datas" style="width: 100%; margin-top: 12px" border="">
        <el-table-column
          prop="ruleName"
          align="left"
          label="规则名称"
          width="180"
        >
          <template slot-scope="scope">
            {{ scope.row.newData.ruleName }}
          </template>
        </el-table-column>

        <el-table-column
          prop="ruleName"
          align="left"
          label="修改前规则所属智慧财务账套"
          width="180"
        >
          <template slot-scope="scope">
            <span
              class="tag"
              v-for="item in scope.row.oldData.accounts"
              :key="item"
            >
              {{ filterAcc(item).companyName }} <br />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="ruleName"
          align="left"
          label="修改后规则所属智慧财务账套"
          width="180"
        >
          <template slot-scope="scope">
            <span
              class="tag"
              v-for="item in scope.row.newData.accounts"
              :key="item"
            >
              {{ filterAcc(item).companyName }} <br />
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="ruleName" align="left" label="修改前规则类型">
          <template slot-scope="scope">
            {{
              ruleTypes.filter(
                (v) => scope.row.oldData.ruleType == v.dictValue
              )[0].dictLabel
            }}
          </template>
        </el-table-column>
        <el-table-column prop="ruleName" align="left" label="修改后规则类型">
          <template slot-scope="scope">
            {{
              ruleTypes.filter(
                (v) => scope.row.newData.ruleType == v.dictValue
              )[0].dictLabel
            }}
          </template>
        </el-table-column>
        <el-table-column prop="ruleName" align="left" label="修改前启用状态">
          <template slot-scope="scope">
            {{
              ruleStart.filter((v) => scope.row.oldData.start == v.dictValue)[0]
                .dictLabel
            }}
          </template>
        </el-table-column>
        <el-table-column prop="ruleName" align="left" label="修改后启用状态">
          <template slot-scope="scope">
            {{
              ruleStart.filter((v) => scope.row.newData.start == v.dictValue)[0]
                .dictLabel
            }}
          </template>
        </el-table-column>
        <el-table-column prop="updateReason" align="left" label="修改原因">
          <template slot-scope="scope">
            {{ scope.row.newData.updateReason }}
          </template>
        </el-table-column>
        <el-table-column prop="updateByName" align="left" label="修改人">
          <template slot-scope="scope">
            {{ scope.row.newData.updateByName }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" align="left" label="修改时间">
          <template slot-scope="scope">
            {{ scope.row.newData.updateTime }}
          </template>
        </el-table-column>
      </el-table>

      <Pagination
        style="margin-top: 20px"
        v-model="pagination"
        @change="getList"
        layout="total,sizes,pager,jumper"
        align="center"
      />
      <template #footer>
        <Button style="margin-right: 16px" @click="handleCancel">取消</Button>
      </template>
    </el-dialog>
  </div>
</template>
      
      <script>
import Dialog from "./Dialog.vue";
export default {
  components: {
    Dialog,
  },
  props: {
    code: String,
  },
  data() {
    return {
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
      showDialog: true,
      datas: [],
      ruleTypes: [],
      ruleStart: [],
    };
  },
  computed: {
    myAccountSets() {
      return this.$store.state.financial.myAccountSets;
    },
  },
  mounted() {
    this.getDatas();
    this.getList();
  },
  methods: {
    filterAcc(e) {
      let data = this.myAccountSets.find((item) => {
        return item.id == e;
      });
      return data;
    },
    handleCancel() {
      this.$emit("close");
    },
    getDatas() {
      this.$api.u8c.getDicts("u8c_rule_type").then((res) => {
        this.ruleTypes = res.data;
      });
      this.$api.u8c.getDicts("u8c_rule_start").then((res) => {
        this.ruleStart = res.data;
      });
    },
    getList() {
      let data = {
        pageNum: this.pagination.page,
        pageSize: this.pagination.size,
        code: this.code,
      };
      this.$api.u8c.getRecordList({ ...data }).then((res) => {
        let arr = [];
        res.rows.forEach((item) => {
          res.rows.forEach((i) => {
            if (item.idBefor == i.id) {
              arr.push({
                newData: item,
                oldData: i,
              });
            }
          });
        });
        this.datas = arr;
        console.log(this.datas);
        this.pagination.total = res.total / 2;
      });
    },
  },
};
</script>
      
      <style>
</style>