<template>
  <div>
    <Dialog
      width="600"
      :visible.sync="showDialog"
      @close="handleCancel"
      title="配置银行账户关联"
    >
      <div class="item">
        <span>智慧财务系统银行账户/账号：</span>
        <input
          disabled
          type="text"
          style="width: 300px"
          v-model="params.name"
        />
      </div>

      <div class="item" style="align-items: start">
        <span><i>*</i>用友U8C系统银行账户/账号：</span>
        <div>
          <Select
            style="width: 300px"
            v-model="params.u8cAccountCode"
            @change="changeMerchant"
            placeholder="请输入/选择用友U8C银行账户"
            :datas="merchantNames"
            keyName="account"
            titleName="accountname"
            filterable
          />
        </div>
      </div>
      <div class="item">
        <span>关联方式：</span>
        <Select
          style="width: 300px"
          v-model="params.relevanceType"
          placeholder="选择用友U8C客商后自动回显"
          disabled
          :datas="types.relevanceTypes"
          keyName="dictValue"
          titleName="dictLabel"
          filterable
        />
      </div>
      <div class="item" style="align-items: start">
        <span><i>*</i>修改原因：</span>
        <textarea
          style="width: 300px"
          v-model="params.updateReason"
          type="text"
        />
      </div>
      <template #footer>
        <Button style="margin-right: 16px" @click="handleCancel">取消</Button>
        <Button @click="handleOk" color="blue">确定</Button>
      </template>
    </Dialog>
  </div>
</template>
    
    <script>
import Dialog from "./Dialog.vue";
export default {
  props: {
    itemData: Object,

    types: Object,
  },
  components: {
    Dialog,
  },
  data() {
    return {
      merchantNames: [],
      params: {
        accountId: "",
        pkGlorgbook: "",
        traderId: "",
        accountNumber: "",
        bankOfDeposit: "",
        pkBankaccbas: "",
        u8cAccountName: "",
        u8cAccountCode: "",
        u8cAccount: "",
        updateReason: "",
      },
      checkbox: false,
      showDialog: true,
    };
  },
  mounted() {
    
    this.params = Object.assign(this.params, this.itemData);
    this.params.name = this.params.bankOfDeposit+this.params.accountNumber
    if (this.params.relevanceType === undefined) {
      this.params.relevanceType = 1;
    } else {
      this.params.relevanceType = this.params.relevanceType * 1;
    }
    console.log(this.params);
    this.getWisdomBusi();
  },

  methods: {
    changeMerchant(e) {
      console.log(e);
      this.params.pkBankaccbas = e.pk_bankaccbas;
      this.params.u8cAccountName = e.accountname;
      this.params.u8cAccountCode = e.accountcode;
      this.params.u8cAccount = e.account;
    },
    handleCancel() {
      this.$emit("close");
    },
    handleOk() {
      if (!this.params.pkBankaccbas) {
        this.$Message({ type: "warn", text: "请选择用友U8C系统银行账户/账号" });
        return;
      }
      if (!this.params.updateReason) {
        this.$Message({ type: "warn", text: "请填写修改原因" });
        return;
      }

      this.params.isInterior = this.params.merchantType == 4 ? 0 : 1;
      console.log(this.params);
     
      this.$api.u8c.bankaccountAddEdit({ ...this.params }).then((res) => {
        this.$Message({ type: "success", text: "修改成功" });
        this.$emit("close");
      });
      console.log(this.params);
    },
    getWisdomBusi() {
      this.$api.u8c.getU8CBankaccount({pkGlorgbook:this.itemData.pkGlorgbook}).then((res) => {
        this.merchantNames = res.rows;
      });
    },
  },
};
</script>
    
    <style lang="less" scoped>
.item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  span {
    margin-right: 10px;
    display: inline-block;
    width: 197px;
    text-align: right;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
</style>