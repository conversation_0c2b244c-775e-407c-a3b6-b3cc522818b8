<template>
  <div>
    <el-dialog
      title="配置凭证推送规则"
      :visible.sync="showDialog"
      width="1100"
      append-to-body
      :before-close="handleCancel"
    >
      <div class="item">
        <span><i>*</i>规则名称：</span>
        <input
          type="text"
          :disabled="detailType"
          placeholder="请输入规则名称"
          v-model="params.ruleName"
          style="width: 300px"
        />
      </div>
      <div class="item">
        <span><i>*</i>所属智慧财务系统账套：</span>
        <Select
          style="width: 300px"
          v-model="params.accounts"
          :disabled="detailType"
          placeholder=" "
          multiple
          keyName="id"
          titleName="companyName"
          :datas="myAccountSets"
          filterable
        ></Select>
      </div>
      <div class="item" style="margin-bottom: 0">
        <span>规则类型：</span>
        <Radio
          v-for="item in ruleTypes"
          :disabled="detailType"
          :key="item.dictValue"
          style="margin-right: 20px"
          :value="item.dictValue"
          v-model="params.ruleType"
          >{{ item.dictLabel }}</Radio
        >
      </div>
      <div style="margin-left: 127px; color: #999">
        共享规则为对应账套下，所有用户都可以使用的规则。私有规则只有配置人自己可使用
      </div>
      <div class="item">
        <span>启用状态：</span>
        <Radio
          v-for="item in ruleStart"
          :disabled="detailType"
          :key="item.dictValue"
          style="margin-right: 20px"
          :value="item.dictValue"
          v-model="params.start"
          >{{ item.dictLabel }}</Radio
        >
      </div>
      <div class="item">
        <span>用友U8C借方科目生成规则：</span>
        <el-tooltip placement="top">
          <div slot="content">
            系统会按照您配置的规则，生成U8C的科目。<br />
            若您的凭证中包含多借/多贷的关系：<br />
            只配置一条借/贷规则：借/贷方科目全部按照该规则生成<br />
            配置多条借/贷规则：按照凭证中的借/贷方顺序，依次生成科目
          </div>
          <i class="h-icon-help"></i>
        </el-tooltip>
        <Button
          color="blue"
          v-if="!detailType"
          icon="h-icon-plus"
          @click="jfList.push({ summaryList: [] })"
          style="margin-left: 20px"
          >添加</Button
        >
      </div>
      <div
        v-for="(item, index) in jfList"
        :key="index"
        style="margin-left: 15px; margin-bottom: 12px"
      >
        <span style="font-weight: bold">
          <i v-if="index == 0" style="color: red; margin-right: 5px">*</i>规则{{
            index + 1
          }}：</span
        >
        <div class="word_box">
          <div
            style="display: flex; align-items: center"
            v-for="(v, i) in item.summaryList"
            :key="i"
          >
            <div class="word_box_item">
              <div v-if="v.type == '-'">-</div>
              <input
                type="text"
                style="width: 220px"
                v-if="v.type === 'input'"
                v-model="v.ruleType"
                :disabled="detailType"
                placeholder="请输入"
              />
              <Select
                style="width: 220px"
                v-if="v.type === 'select'"
                v-model="v.ruleType"
                :disabled="detailType"
                :deletable="false"
                keyName="code"
                titleName="name"
                :datas="rules"
                filterable
              />
              <i
                class="h-icon-close-min"
                v-if="!detailType"
                @click="delItem(index, i)"
              ></i>
            </div>
          </div>
        </div>
        <div style="margin-top: 16px" v-if="!detailType">
          <Button color="blue" @click="addRuleItem(index, 1)" icon="h-icon-plus"
            >增加固定文本</Button
          >
          <Button
            color="blue"
            @click="addRuleItem(index, 3)"
            icon="h-icon-plus"
            style="margin-left: 20px"
            >增加科目分级标识</Button
          >
          <Button
            color="blue"
            @click="addRuleItem(index, 2)"
            icon="h-icon-plus"
            style="margin-left: 20px"
            >增加动态字段</Button
          >
          <Button
            color="blue"
            v-if="index > 0"
            @click="delRule(index)"
            icon="h-icon-minus"
            style="margin-left: 20px"
            >删除规则</Button
          >
        </div>
      </div>
      <div class="solid"></div>
      <div class="item">
        <span>用友U8C贷方科目生成规则：</span>
        <el-tooltip placement="top">
          <div slot="content">
            系统会按照您配置的规则，生成U8C的科目。<br />
            若您的凭证中包含多借/多贷的关系：<br />
            只配置一条借/贷规则：借/贷方科目全部按照该规则生成<br />
            配置多条借/贷规则：按照凭证中的借/贷方顺序，依次生成科目
          </div>
          <i class="h-icon-help"></i>
        </el-tooltip>
        <Button
          color="blue"
          icon="h-icon-plus"
          v-if="!detailType"
          @click="dfList.push({ summaryList: [] })"
          style="margin-left: 20px"
          >添加</Button
        >
      </div>
      <div
        v-for="(item, index) in dfList"
        :key="index + 'flag'"
        style="margin-left: 15px; margin-bottom: 12px"
      >
        <span style="font-weight: bold">
          <i v-if="index == 0" style="color: red; margin-right: 5px">*</i>规则{{
            index + 1
          }}：</span
        >
        <div class="word_box">
          <div
            style="display: flex; align-items: center"
            v-for="(v, i) in item.summaryList"
            :key="i"
          >
            <div class="word_box_item">
              <div v-if="v.type == '-'">-</div>
              <input
                type="text"
                style="width: 220px"
                v-if="v.type === 'input'"
                v-model="v.ruleType"
                :disabled="detailType"
                placeholder="请输入"
              />
              <Select
                style="width: 220px"
                v-if="v.type === 'select'"
                :disabled="detailType"
                v-model="v.ruleType"
                :deletable="false"
                keyName="code"
                titleName="name"
                :datas="rules"
                filterable
              />
              <i
                class="h-icon-close-min"
                v-if="!detailType"
                @click="delItem2(index, i)"
              ></i>
            </div>
          </div>
        </div>
        <div style="margin-top: 16px" v-if="!detailType">
          <Button
            color="blue"
            @click="addRuleItem2(index, 1)"
            icon="h-icon-plus"
            >增加固定文本</Button
          >
          <Button
            color="blue"
            @click="addRuleItem2(index, 3)"
            icon="h-icon-plus"
            style="margin-left: 20px"
            >增加科目分级标识</Button
          >
          <Button
            color="blue"
            @click="addRuleItem2(index, 2)"
            icon="h-icon-plus"
            style="margin-left: 20px"
            >增加动态字段</Button
          >
          <Button
            color="blue"
            v-if="index > 0"
            @click="delRule2(index, 4)"
            icon="h-icon-minus"
            style="margin-left: 20px"
            >删除规则</Button
          >
        </div>
      </div>
      <div
        class="item"
        style="align-items: start"
        v-if="itemData && !detailType"
      >
        <span style="width: 95px"><i>*</i>修改原因：</span>
        <textarea
          type="text"
          placeholder="请输入修改原因"
          v-model="updateReason"
          style="width: 600px"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">关 闭</el-button>
        <el-button type="primary" @click="handleOk" v-if="!detailType"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    itemData: Object,
    detailType: Boolean,
  },
  data() {
    return {
      updateReason: "",
      ruleTypes: [],
      ruleStart: [],
      jfList: [{ summaryList: [] }],
      dfList: [{ summaryList: [] }],
      params: {
        ruleName: "",
        ruleType: "0",
        start: "0",
        accounts: [],
        detailsList: [],
      },
      showDialog: true,
      rules: [],
    };
  },
  computed: {
    myAccountSets() {
      return this.$store.state.financial.myAccountSets;
    },
  },
  mounted() {
    this.getRulesEnum();
    console.log(this.itemData);
    if (this.itemData) {
      this.params.ruleName = this.itemData.ruleName;
      this.params.ruleType = this.itemData.ruleType;
      this.params.start = this.itemData.start ? "0" : "1";
      this.params.accounts = this.itemData.accounts;
    }
    if (this.itemData.detailsList) {
      this.itemData.detailsList.forEach((item) => {
        if (item.setType == 0) {
          item.type = "input";
        } else if (item.setType == 1) {
          item.type = "-";
        } else {
          item.type = "select";
        }
      });
      let arr = this.itemData.detailsList.filter((item) => item.loanType == 1);
      let arr2 = this.itemData.detailsList.filter((item) => item.loanType == 2);
      //找出arr中order的最大值是几
      let max = Math.max(...arr.map((item) => item.order));
      //找出arr2中order的最大值是几
      let max2 = Math.max(...arr2.map((item) => item.order));
      console.log(max, max2);
      this.jfList = [];
      this.dfList = [];
      for (let i = 0; i < max; i++) {
        this.jfList.push({ summaryList: [] });
      }
      for (let i = 0; i < max2; i++) {
        this.dfList.push({ summaryList: [] });
      }
      console.log(this.jfList, this.dfList);
      arr.forEach((item) => {
        this.jfList[item.order - 1].summaryList.push(item);
      });
      arr2.forEach((item) => {
        this.dfList[item.order - 1].summaryList.push(item);
      })
      
 
     
      // this.jfList[0].summaryList = arr;
      // this.dfList[0].summaryList = arr2;
      // console.log(arr, arr2);
    }
  },
  methods: {
    addRuleItem(index, type) {
      this.jfList[index].summaryList.push({
        loanType: 1,
        type: type == 1 ? "input" : type == 2 ? "select" : "-",
        setType: type == 1 ? 0 : type == 2 ? 2 : 1,
        ruleType: "",
        ruleItem: this.jfList[index].summaryList.length + 1,
        order: index + 1,
      });
    },
    addRuleItem2(index, type) {
      this.dfList[index].summaryList.push({
        loanType: 2,
        type: type == 1 ? "input" : type == 2 ? "select" : "-",
        setType: type == 1 ? 0 : type == 2 ? 2 : 1,
        ruleType: "",
        ruleItem: this.dfList[index].summaryList.length + 1,
        order: index + 1,
      });
    },
    getRulesEnum() {
      this.$api.u8c.getRulesEnum().then((res) => {
        this.rules = res.rows;
      });

      this.$api.u8c.getDicts("u8c_rule_type").then((res) => {
        this.ruleTypes = res.data;
      });
      this.$api.u8c.getDicts("u8c_rule_start").then((res) => {
        this.ruleStart = res.data;
      });
    },
    delItem(index, i) {
      this.jfList[index].summaryList.splice(i, 1);
    },
    delItem2(index, i) {
      this.dfList[index].summaryList.splice(i, 1);
    },
    delRule(index) {
      this.jfList.splice(index, 1);
    },
    delRule2(index) {
      this.dfList.splice(index, 1);
    },
    handleCancel() {
      this.$emit("close");
    },
    handleOk() {
      if (!this.params.ruleName) {
        this.$message.warning("请输入规则名称");
        return;
      }
      if (this.params.accounts.length == 0) {
        this.$message.warning("请选择账套");

        return;
      }
      if (this.itemData && !this.updateReason) {
        this.$message.warning("请输入修改原因");
        return;
      }
      if (this.jfList[0].summaryList.length == 0) {
        this.$message.warning("请输入借方规则");
        return;
      }
      if (this.dfList[0].summaryList.length == 0) {
        this.$message.warning("请输入贷方规则");
        return;
      }
      let arr = [...this.jfList, ...this.dfList];
      arr.forEach((item) => {
        item.summaryList.forEach((i) => {
          this.params.detailsList.push(i);
        });
      });
      console.log(this.params);
      if (this.itemData) {
        this.$api.u8c
          .ruleEdit({
            ...this.params,
            updateReason: this.updateReason,
            id: this.itemData.id,
            version: this.itemData.version,
            code: this.itemData.code,
            createBy: this.itemData.createBy,
            createTime: this.itemData.createTime,
          })
          .then((res) => {
            this.$Message({ type: "success", text: "修改成功" });
            this.$emit("close");
          });
      } else {
        this.$api.u8c.ruleAdd({ ...this.params }).then((res) => {
          this.$Message({ type: "success", text: "新增成功" });
          this.$emit("close");
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  span {
    display: inline-block;
    width: 200px;
    text-align: right;
    font-weight: bold;
    margin-right: 10px;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
.word_box {
  width: 100%;
  min-height: 50px;
  border: 1px solid #cccccc;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding-left: 16px;
}
.word_box_item {
  margin: 8px 0;
  height: 40px;
  background: #e8f4ff;
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin-right: 12px;
}
.h-icon-close-min {
  cursor: pointer;
  display: inline-block;
  background: #cccccc;
  color: #fff;
  border-radius: 50%;
  margin-left: 9px;
}
.solid {
  width: 100%;
  height: 1px;
  background: #f0f0f0;
  margin: 16px 0;
}
</style>