/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : </li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月25日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
import Ajax from '@/assets/financial/js/common/ajax';
import request from "@/utils/request";

export default {
	template: {
		list(params = {}) {
      return request({
        url: '/report/template',
        method: 'get',
        params: params
      })
		},
		load(id, accountSetsId) {
      return request({
        url: '/report/template/'+`${id}`,
        method: 'get',
        params: {accountSetsId: accountSetsId}
      })
		},
		delete(id) {
      return request({
        url: '/report/template/'+`${id}`,
        method: 'delete'
      })
		},
		save(params = {}) {
      return request({
        url: '/report/template',
        method: 'post',
        data: params
      })
		},
		update(params = {}) {
      return request({
        url: '/report/template',
        method: 'put',
        data: params
      })
		},
		items: {
			save(params = {}) {
        return request({
          url: '/report/template/items',
          method: 'post',
          data: params
        })
			},
			update(params = {}) {
        return request({
          url: '/report/template/items',
          method: 'put',
          data: params
        })
			},
			delete(id) {
        return request({
          url: '/report/template/items/'+`${id}`,
          method: 'delete'
        })
			},
			formula(params) {
        return request({
          url: '/report/template/items/formula',
          method: 'post',
          data: params
        })
			}
		}
	},
	view(id, params = {}) {
    return request({
      url: '/report/template/view/'+`${id}`,
      method: 'get',
      params: params
    })
	}
}
