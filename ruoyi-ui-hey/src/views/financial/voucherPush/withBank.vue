<template>
  <div style="padding: 16px">
    <div
      style="padding: 20px; background: #fff; height: calc(100vh - 145px)"
      v-if="!nextType"
    >
      <div style="text-align: center; margin-top: 250px">
        <div
          style="display: flex; align-items: center; justify-content: center"
        >
          <span>请选择智慧财务系统账套：</span>
          <Select
            style="width: 250px"
            v-model="params.accountId"
            placeholder="请输入/选择智慧财务系统账套"
            keyName="id"
            titleName="companyName"
            :datas="myAccountSets"
            filterable
          />
        </div>
        <p style="margin: 20px 0">
          请选择您要查看银行账户的所属账套，选择后点击"下一步"，查看该账套内的银行账户
        </p>
        <div style="text-align: center">
          <Button
            style="height: 32px; margin-right: 12px"
            @click="next"
            color="blue"
            >下一步</Button
          >
        </div>
      </div>
    </div>
    <div v-else style="padding: 20px; background: #fff">
      <div>
        <div class="item" style="display: flex; align-items: center">
          <span style="font-weight: bold">所属账套：</span>
          <Select
            style="width: 250px"
            v-model="params.accountId"
            placeholder="请选择关联方式"
            @change="getList"
            keyName="id"
            titleName="companyName"
            :datas="myAccountSets"
            filterable
          />
        </div>
      </div>
      <div class="solid" style="margin: 20px 0"></div>
      <div class="search">
        <div class="item">
          <span>智慧财务系统银行账户</span>
          <input
            style="width: 250px"
            v-model="params.bankOfDeposit"
            placeholder="请输入银行账户"
            type="text"
          />
        </div>
        <div class="item">
          <span>智慧财务系统银行账号</span>
          <input
            style="width: 250px"
            v-model="params.accountNumber"
            placeholder="请输入银行账号"
            type="text"
          />
        </div>
        <div class="item">
          <span>用友U8C系统银行账户</span>
          <input
            style="width: 250px"
            v-model="params.u8cAccountName"
            placeholder="请输入银行账户"
            type="text"
          />
        </div>
        <div class="item">
          <span>用友U8C系统银行账号</span>
          <input
            style="width: 250px"
            v-model="params.u8cAccount"
            placeholder="请输入银行账号"
            type="text"
          />
        </div>
        <div class="item">
          <span>关联方式</span>
          <Select
            style="width: 250px"
            v-model="params.relevanceType"
            placeholder="请选择关联方式"
            keyName="dictValue"
            titleName="dictLabel"
            :datas="relevanceTypes"
            filterable
          />
        </div>
        <Button
          style="height: 32px; margin-right: 12px"
          @click="getList"
          color="blue"
          icon="h-icon-search"
          >搜索</Button
        >
        <Button style="height: 32px" icon="h-icon-refresh" @click="reset"
          >重置</Button
        >
      </div>
      <div class="solid"></div>
      <el-table :data="datas" style="width: 100%; margin-top: 12px" border="">
        <el-table-column
          prop="u8cMerchantName"
          align="left"
          label="智慧财务系统银行账户/账号"
        >
          <template slot-scope="scope">
            {{ scope.row.bankOfDeposit }}{{ scope.row.accountNumber }}
          </template>
        </el-table-column>
        <el-table-column
          prop="u8cMerchantName"
          align="left"
          label="用友U8C系统银行账户/账号"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.u8cAccountName"
              >{{ scope.row.u8cAccountName }}{{ scope.row.u8cAccount }}</span
            >
            <span v-else style="color: red">未关联</span>
          </template>
        </el-table-column>
        <el-table-column prop="" align="left" label="关联方式">
          <template slot-scope="scope">
            {{
              scope.row.relevanceType && relevanceTypes[scope.row.relevanceType]
                ? relevanceTypes[scope.row.relevanceType].dictLabel
                : "-"
            }}
          </template>
        </el-table-column>
        <el-table-column prop="" align="left" label="最近关联时间">
          <template slot-scope="scope">
            {{ scope.row.createTime ? scope.row.createTime : "-" }}
          </template>
        </el-table-column>
        <el-table-column prop="" align="center" label="操作">
          <template slot-scope="scope">
            <Button
              v-if="
                permissions.includes('withBank:edit') ||
                permissions.includes('*:*:*')
              "
              noBorder
              text-color="blue"
              @click="edit(scope.row)"
              >修改</Button
            >
            <Button noBorder text-color="blue" @click="record(scope.row)"
              >查看修改记录</Button
            >
          </template>
        </el-table-column>
      </el-table>

      <Pagination
        style="margin-top: 20px"
        v-model="pagination"
        @change="getList"
        layout="total,sizes,pager,jumper"
        align="center"
      />
    </div>
    <BankEdit
      :itemData="itemData"
      :types="{ patronTypes, relevanceTypes }"
      v-if="CustomerEditType"
      @close="close"
    />
    <BankRecord
      v-if="CustomerRecordType"
      @close="close"
      :id="itemData.traderId"
      :accountId="params.accountId"
    />
  </div>
</template>
  
  <script>
import BankRecord from "../../../components/u8c/BankRecord.vue";
import BankEdit from "../../../components/u8c/BankEdit.vue";

export default {
  name: "withBank",
  components: {
    BankEdit,
    BankRecord,
  },
  data() {
    return {
      nextType: false,
      CustomerRecordType: false,
      itemData: null,
      CustomerEditType: false,
      u8cmerchantNames: [],
      merchantNames: [],
      patronTypes: [],
      relevanceTypes: [],
      datas: [],
      params: {
        accountId: "",
        accountNumber: "",
        bankOfDeposit: "",
        u8cAccount: "",
        u8cAccountName: "",
        relevanceType: "",
      },
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
    };
  },
  computed: {
    permissions() {
      return this.$store.getters.permissions;
    },
    myAccountSets() {
      return this.$store.state.financial.myAccountSets;
    },
  },
  mounted() {
    console.log(this.myAccountSets);
    this.$api.u8c.list({}).then((res) => {
      this.u8cmerchantNames = res.rows;
      const str = "merchantId";
      this.merchantNames = this.u8cmerchantNames.filter((item) => str in item);
    });
    this.getDatas();
  },
  methods: {
    next() {
      if (!this.params.accountId) {
        this.$Message({ type: "warn", text: "请选择账套" });
        return;
      }
      this.nextType = true;
      this.getList();
    },
    close() {
      this.CustomerEditType = false;
      this.CustomerRecordType = false;
      this.getList();
    },
    record(v) {
      this.itemData = v;
      this.CustomerRecordType = true;
    },
    edit(v) {
      console.log(v);
      this.itemData = v;
      this.itemData.accountId = this.params.accountId;
      this.CustomerEditType = true;
    },
    reset() {
      this.pagination = {
        page: 1,
        size: 10,
        total: 0,
      };
      this.params = {
        accountId: this.params.accountId,
        merchantName: "",
        u8cMerchantName: "",
        u8cInteriorType: "",
        relevanceType: "",
      };
      this.getList();
    },
    getList() {
      let data = {
        pageSize: this.pagination.size,
        pageNum: this.pagination.page,
        ...this.params,
      };
      this.$api.u8c.bankList(data).then((res) => {
        this.datas = res.rows;
        this.pagination.total = res.total;
      });
    },
    getDatas() {
      this.$api.u8c.getDicts("u8c_patron_type").then((res) => {
        this.patronTypes = res.data;
      });
      this.$api.u8c.getDicts("u8c_relevance_type").then((res) => {
        this.relevanceTypes = res.data;
      });
    },
  },
};
</script>
  
  <style lang="less" scoped>
.search {
  display: flex;
  flex-wrap: wrap;
  .item {
    margin-right: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    span {
      margin-right: 5px;
      font-weight: bold;
    }
  }
}
.solid {
  width: 100%;
  height: 1px;
  background: #f0f0f0;
}
</style>