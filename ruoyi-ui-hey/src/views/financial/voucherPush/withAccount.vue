<template>
  <div style="padding: 16px">
    <div style="padding: 20px; background: #fff">
      <div class="search">
        <div class="item">
          <span>智慧财务系统账套</span>
          <Select
            style="width: 250px"
            v-model="params.accountId"
            placeholder="请输入/选择账套名称"
            :datas="myAccountSets"
            keyName="id"
            titleName="companyName"
            filterable
          />
        </div>
        <div class="item">
          <span>用友U8C系统账套</span>
          <Select
            style="width: 250px"
            v-model="params.glorgbookName"
            placeholder="请输入/选择账套名称"
            :datas="u8cList"
            keyName="glorgbookName"
            titleName="glorgbookName"
            filterable
          />
        </div>

        <Button
          style="height: 32px; margin-right: 12px"
          @click="getList"
          color="blue"
          icon="h-icon-search"
          >搜索</Button
        >
        <Button style="height: 32px" icon="h-icon-refresh" @click="reset"
          >重置</Button
        >
      </div>
      <div class="solid"></div>
      <el-table :data="datas" style="width: 100%; margin-top: 12px" border="">
        <el-table-column
          align="left"
          label="智慧财务系统账套"
          prop="accountName"
        ></el-table-column>
        <el-table-column align="left" label="用友U8C系统账套">
          <template slot-scope="scope">
            <span v-if="scope.row.glorgbookName">{{
              scope.row.glorgbookName
            }}</span>
            <span v-else style="color: red">未关联</span>
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="最近关联时间"
          prop="createTime"
        ></el-table-column>

        <el-table-column align="left" label="操作">
          <template slot-scope="scope">
            <Button
              v-if="
                permissions.includes('withAccount:edit') ||
                permissions.includes('*:*:*')
              "
              noBorder
              text-color="blue"
              @click="edit(scope.row)"
              >修改</Button
            >
            <Button noBorder text-color="blue" @click="record(scope.row)"
              >查看修改记录</Button
            >
          </template>
        </el-table-column>
      </el-table>

      <Pagination
        style="margin-top: 20px"
        v-model="pagination"
        @change="getList"
        layout="total,sizes,pager,jumper"
        align="center"
      />
    </div>
    <AccountEdit
      :itemData="itemData"
      :u8cList="u8cList"
      :types="{ patronTypes, relevanceTypes }"
      v-if="CustomerEditType"
      @close="close"
    />
    <AccountRecord
      v-if="CustomerRecordType"
      @close="close"
      :id="itemData.accountId"
    />
  </div>
</template>
  
  <script>
import AccountRecord from "../../../components/u8c/AccountRecord.vue";
import AccountEdit from "../../../components/u8c/AccountEdit.vue";

export default {
  name: "withAccount",
  components: {
    AccountEdit,
    AccountRecord,
  },
  data() {
    return {
      CustomerRecordType: false,
      itemData: null,
      CustomerEditType: false,
      u8cmerchantNames: [],
      merchantNames: [],
      patronTypes: [],
      relevanceTypes: [],
      datas: [],
      params: {
        accountId: "",
        accountName: "",
        glorgbookName: "",
      },
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
      u8cList: [],
    };
  },
  computed: {
    permissions() {
      return this.$store.getters.permissions;
    },
    myAccountSets() {
      return this.$store.state.financial.myAccountSets;
    },
  },

  mounted() {
    this.getDatas();
    this.getList();
  },
  methods: {
    close() {
      this.CustomerEditType = false;
      this.CustomerRecordType = false;
      this.getList();
    },
    record(v) {
      this.itemData = v;
      this.CustomerRecordType = true;
    },
    edit(v) {
      console.log(v);
      this.itemData = v;
      this.CustomerEditType = true;
    },
    reset() {
      this.pagination = {
        page: 1,
        size: 10,
        total: 0,
      };
      this.params = {
        merchantName: "",
        u8cMerchantName: "",
        u8cInteriorType: "",
        relevanceType: "",
      };
      this.getList();
    },
    getList() {
      let data = {
        pageSize: this.pagination.size,
        pageNum: this.pagination.page,
        ...this.params,
      };
      this.$api.u8c.getLedgerList(data).then((res) => {
        this.datas = res.rows;
        this.pagination.total = res.total;
      });
    },
    getDatas() {
      this.$api.u8c.getU8CGlorgbook({}).then((res) => {
        this.u8cList = res.rows;
      });
      this.$api.u8c.getDicts("u8c_patron_type").then((res) => {
        this.patronTypes = res.data;
      });
      this.$api.u8c.getDicts("u8c_relevance_type").then((res) => {
        this.relevanceTypes = res.data;
      });
    },
  },
};
</script>
  
  <style lang="less" scoped>
.search {
  display: flex;
  flex-wrap: wrap;
  .item {
    margin-right: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    span {
      margin-right: 5px;
      font-weight: bold;
    }
  }
}
.solid {
  width: 100%;
  height: 1px;
  background: #f0f0f0;
}
</style>