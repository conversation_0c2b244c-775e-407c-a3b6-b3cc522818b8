<template>
  <div style="padding: 16px">
    <div style="padding: 20px; background: #fff">
      <div class="search">
        <div class="item">
          <span>规则名称</span>
          <input
            style="width: 250px"
            v-model="params.ruleName"
            placeholder="请输入规则名称"
            type="text"
          />
        </div>
        <div class="item">
          <span>所属账套</span>
          <Select
            style="width: 250px"
            v-model="params.accountId"
            placeholder="请输入/选择账套"
            keyName="id"
            titleName="companyName"
            :datas="myAccountSets"
            filterable
          />
        </div>
        <div class="item">
          <span>规则类型</span>
          <Select
            style="width: 250px"
            v-model="params.ruleType"
            placeholder="请选择规则类型"
            :datas="ruleTypes"
            keyName="dictValue"
            titleName="dictLabel"
            filterable
          />
        </div>
        <div class="item">
          <span>启用状态</span>
          <Select
            style="width: 250px"
            v-model="params.start"
            placeholder="请选择启用状态"
            :datas="ruleStart"
            keyName="dictValue"
            titleName="dictLabel"
            filterable
          />
        </div>
        <div class="item">
          <span>创建时间</span>
          <DateRangePicker
            style="width: 250px"
            v-model="date"
            format="YYYY-MM-DD"
          ></DateRangePicker>
        </div>
        <Button
          style="height: 32px; margin-right: 12px"
          @click="getList"
          color="blue"
          icon="h-icon-search"
          >搜索</Button
        >
        <Button style="height: 32px" icon="h-icon-refresh" @click="reset"
          >重置</Button
        >
      </div>
      <div class="solid" style="margin-bottom: 16px"></div>
      <Button
        style="height: 32px; margin-right: 12px"
        @click="add"
        v-if="
          permissions.includes('configVoucher:addEdit') ||
          permissions.includes('*:*:*')
        "
        class="h-btn h-btn-text-blue"
        icon="h-icon-plus"
        >新增规则</Button
      >

      <el-table :data="datas" style="width: 100%; margin-top: 12px" border="">
        <el-table-column
          prop="ruleName"
          align="left"
          label="规则名称"
          width="180"
        >
        </el-table-column>
        <el-table-column
          prop=""
          align="left"
          label="所属智慧财务系统账套"
          min-width="320"
        >
          <template slot-scope="scope">
            <span v-for="item in scope.row.accounts" :key="item">
              <span class="tag" v-if="filterAcc(item)">
                <span>
                  {{ filterAcc(item).companyName }}
                  <i
                    @click="delItem(scope.row, item)"
                    v-if="
                      permissions.includes('configVoucher:addEdit') ||
                      permissions.includes('*:*:*')
                    "
                    style="cursor: pointer"
                    class="h-icon-close-min"
                  ></i>
                </span>
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="" align="left" label="规则类型" width="120">
          <template slot-scope="scope">
            {{
              ruleTypes.filter((v) => scope.row.ruleType == v.dictValue)[0]
                .dictLabel
            }}
          </template>
        </el-table-column>
        <el-table-column prop="" align="left" label="启用状态" width="120">
          <template slot-scope="scope">
            <h-switch
              v-model="scope.row.start"
              @change="changeStart($event, scope.row)"
            ></h-switch>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          align="left"
          label="创建时间"
          width="180"
        />
        <el-table-column
          prop="createByName"
          align="left"
          label="创建人"
          width="120"
        />
        <el-table-column
          prop="updateTime"
          align="left"
          label="近期修改时间"
          width="180"
        />
        <el-table-column
          prop="updateByName"
          align="left"
          label="近期修改人"
          width="120"
        />
        <el-table-column
          prop=""
          align="center"
          fixed="right"
          label="操作"
          width="200"
        >
          <template slot-scope="scope">
            <Button
              v-if="
                permissions.includes('configVoucher:addEdit') ||
                permissions.includes('*:*:*')
              "
              noBorder
              text-color="blue"
              @click="edit(scope.row)"
              >修改</Button
            >
            <Button noBorder text-color="blue" @click="detail(scope.row)"
              >查看详情</Button
            >
            <DropdownMenu @click="trigger(scope.row, $event)" :datas="btns">
              <Button noBorder text-color="blue">>>更多</Button>
            </DropdownMenu>
          </template>
        </el-table-column>
      </el-table>

      <Pagination
        style="margin-top: 20px"
        v-model="pagination"
        @change="getList"
        layout="total,sizes,pager,jumper"
        align="center"
      />
    </div>
    <AddEditConfig
      :detailType="detailType"
      v-if="AddEditConfigType"
      @close="close"
      :itemData="itemData"
    />
    <ConfigRecord
      v-if="AccountRecordType"
      @close="close"
      :code="itemData.code"
    />
  </div>
</template>
  
  <script>
import AddEditConfig from "../../../components/u8c/AddEditConfig.vue";
import ConfigRecord from "../../../components/u8c/ConfigRecord.vue";

export default {
  name: "configVoucher",
  components: {
    AddEditConfig,
    ConfigRecord,
  },
  data() {
    return {
      detailType: false,
      AddEditConfigType: false,
      AccountRecordType: false,
      date: {},
      btns: ["查看修改记录", "删除"],

      itemData: null,

      ruleTypes: [],
      ruleStart: [],
      datas: [],
      params: {
        ruleName: "",
        ruleType: "",
        start: "",
        beginCreateTime: "",
        endCreateTime: "",
        accountId: "",
      },
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
    };
  },

  computed: {
    permissions() {
      return this.$store.getters.permissions;
    },
    myAccountSets() {
      return this.$store.state.financial.myAccountSets;
    },
  },
  mounted() {
    this.getDatas();
    this.getList();
  },
  methods: {
    changeStart(v, data) {
      console.log(v, data);
      this.$api.u8c.ruleEdit({ ...data, start: v ? 0 : 1 }).then((res) => {
        this.$Message({ type: "success", text: "操作成功" });
        this.$emit("close");
      });
    },
    delItem(data, item) {
      console.log(data, item);
      if (data.accounts.length == 1) {
        this.$message.warning("不可删除所有账套");
        return;
      }
      this.$confirm(
        `您确定删除【${data.ruleName}】的所属账套【${
          this.filterAcc(item).companyName
        }】吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          let arr = data.accounts;
          arr.forEach((v, index) => {
            if (v == item) {
              arr.splice(index, 1);
            }
          });
          let data1 = {
            ...data,
            accounts: arr,
            start: data.start ? 0 : 1,
          };
          console.log(data1);
          this.$api.u8c.ruleEdit({ ...data1 }).then((res) => {
            this.$Message({ type: "success", text: "操作成功" });
            this.$emit("close");
          });
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    add() {
      this.itemData = null;
      this.detailType = false;
      this.AddEditConfigType = true;
    },
    filterAcc(e) {
      let data = this.myAccountSets.find((item) => {
        return item.id == e;
      });
      return data;
    },
    trigger(data, e) {
      console.log(data, e);
      this.itemData = data;
      if (e == "查看修改记录") {
        this.AccountRecordType = true;
      } else {
        this.$confirm(
          `此操作将永久删除该【${data.ruleName}】规则, 是否继续?`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            this.$api.u8c.deleterule(data.id);
            this.$message({
              type: "success",
              message: "删除成功!",
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      }
    },
    close() {
      this.AddEditConfigType = false;
      this.AccountRecordType = false;
      this.getList();
    },
    detail(v) {
      this.itemData = v;
      this.detailType = true;
      this.AddEditConfigType = true;
    },
    edit(v) {
      console.log(v);
      this.itemData = v;
      this.detailType = false;
      this.AddEditConfigType = true;
    },
    reset() {
      this.pagination = {
        page: 1,
        size: 10,
        total: 0,
      };
      this.date = {};
      this.params = {
        ruleName: "",
        ruleType: "",
        start: "",
        beginCreateTime: "",
        endCreateTime: "",
        accountId: "",
      };
      this.getList();
    },
    getList() {
      let data = {
        pageSize: this.pagination.size,
        pageNum: this.pagination.page,
        ...this.params,
        beginCreateTime: this.date.start,
        endCreateTime: this.date.end,
      };
      this.$api.u8c.ruleList(data).then((res) => {
        this.datas = res.rows;
        this.datas.forEach((item) => {
          item.start = item.start == 0 ? true : false;
        });
        this.pagination.total = res.total;
      });
    },
    getDatas() {
      this.$api.u8c.getDicts("u8c_rule_type").then((res) => {
        this.ruleTypes = res.data;
      });
      this.$api.u8c.getDicts("u8c_rule_start").then((res) => {
        this.ruleStart = res.data;
      });
    },
  },
};
</script>
  
  <style lang="less" scoped>
.search {
  display: flex;
  flex-wrap: wrap;
  .item {
    margin-right: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    span {
      margin-right: 5px;
      font-weight: bold;
    }
  }
}
.solid {
  width: 100%;
  height: 1px;
  background: #f0f0f0;
}
/deep/ .h-table .h-icon-down {
  display: none !important;
}
/deep/ .h-dropdownmenu-item {
  color: #2d7bf4 !important;
}
.tag {
  padding: 2px 6px;
  display: inline-block;
  border: 1px solid #d7d7d7;
  background: #e8f4ff;
  border-radius: 4px;
  margin-right: 10px;
  i {
    margin-left: 5px;
  }
}
</style>