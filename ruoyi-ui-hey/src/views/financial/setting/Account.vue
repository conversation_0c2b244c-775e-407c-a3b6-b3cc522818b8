<template>
	<app-content class="h-panel">
		<div class="h-panel-bar"><span class="h-panel-title">账套管理</span></div>
    <div style="height: 10px"></div>
    <Form v-width="450" :labelWidth="75" >
      <FormItem label="账套名称:">
        <Row type="flex">
          <Cell width="15" style="margin-right: 5px">
            <Select v-model="accountsSetsName" :datas="accountSetsList" filterable :deletable="true" @change="accountsSetsChange"></Select>
          </Cell>
        </Row>
      </FormItem>
    </Form>
		<div class="margin-top margin-left">
			<Button color="primary" @click="showForm=true">新建账套</Button>&nbsp;&nbsp;当前账套数量为{{datas.length}}个&nbsp;&nbsp;
		</div>
		<div class="h-panel-body">
			<Table :datas="datas" :border="true">
				<TableItem :width="80" align="center">
					<template slot-scope="{data}">
						<span class="h-tag h-tag-bg-green" v-if="data.id == User.accountSetsId">当前</span>
					</template>
				</TableItem>
				<TableItem title="单位名称" prop="companyName"></TableItem>
				<TableItem title="关联公司" prop="platformCode" :width="120" :format="platformCodeFormat"></TableItem>
				<TableItem title="当前记账年月" prop="currentAccountDate" :width="120" :format="dateFormat"></TableItem>
				<TableItem title="账套启用年月" prop="enableDate" :width="120" :format="dateFormat"></TableItem>
				<TableItem title="会计准则" prop="accountingStandards" dict="accountingStandards" :width="200"></TableItem>
				<TableItem title="凭证审核" prop="voucherReviewed" dict="enableRadios" :width="100"></TableItem>
				<TableItem title="操作" :width="100" align="center">
					<div class="actions" slot-scope="{data}">
						<span @click="edit(data)">编辑</span>
						<span @click="remove(data)">删除</span>
					</div>
				</TableItem>
			</Table>
		</div>
		<Modal v-model="showForm" type="drawer-right" hasCloseIcon>
			<div slot="header">账套信息</div>
			<Form ref="form" v-width="800" mode="twocolumn" :labelWidth="150" :model="form" :rules="validationRules">
				<FormItem label="账套名称:" prop="companyName">
					<input type="text" v-model="form.companyName">
				</FormItem>
        <FormItem label="关联公司:"  prop="platformCode">
          <Select v-model="form.platformCode" :datas="companyDict" @change="changeRoleTypeUser"></Select>
        </FormItem>
				<FormItem label="账套启用年月:" prop="enableDate">
					<DatePicker type="month" v-model="form.enableDate" :disabled="used"/>
				</FormItem>
        <FormItem label="会计准则:" prop="accountingStandards">
          <Select v-model="form.accountingStandards" :datas="accountingStandards"></Select>
        </FormItem>
				<FormItem label="统一社会信用代码:">
					<input type="text" v-model="form.creditCode"><br/>
				</FormItem>
        <FormItem label="顺序号:" prop="voucherReviewed">
          <NumberInput v-model="form.orderNum" :useOperate="true"></NumberInput>
        </FormItem>
        <FormItem label="增值税种类:" prop="vatType">
          <Radio v-model="form.vatType" dict="vatRadios"></Radio>
        </FormItem>
        <FormItem label="凭证是否需要审核:" prop="voucherReviewed">
          <Radio v-model="form.voucherReviewed" dict="needRadios"></Radio>
        </FormItem>
<!--        <FormItem label="会计:" prop="voucherReviewed">-->
<!--          <Select v-model="form.accountings" :datas="accountings" :filterable="true" :multiple="true"></Select>-->
<!--          <span class="fontStyle">可查看该账套下的报表、账簿、凭证信息</span>-->
<!--        </FormItem>-->
<!--        <FormItem label="会计主管:" prop="voucherReviewed">-->
<!--          <Select v-model="form.accountingManagers" :datas="accountingManagers" :filterable="true" :multiple="true"></Select>-->
<!--          <span class="fontStyle">可查所有信息，审核凭证、导入数据生成凭证，设置系统参数</span>-->
<!--        </FormItem>-->

<!--				<FormItem label="单位所在地:">
					<CategoryPicker ref="CategoryPicker" :option="pickerOption" type="key" showAllLevels v-model="form.address"></CategoryPicker>
				</FormItem>-->
				<!--	<FormItem label="固定资产模块:" prop="fixedAssetModule">
						<Radio v-model="form.fixedAssetModule" dict="enableRadios"></Radio>
					</FormItem>-->
<!--				<FormItem label="行业:">
					<Select v-model="form.industry" dict="industry"></Select>
				</FormItem>-->
				<!--	<FormItem label="是否启用出纳模块:" prop="cashierModule">
						<Radio v-model="form.cashierModule" dict="enableRadios"></Radio>
					</FormItem>-->
			</Form>
      <div class="h-panel-bar">
        <span>角色人员</span>
        <br>
        <span class="fontStyle">根据 [通用授权] 功能自动进行分配，如需修改请访问 </span>
        <span class="fontStyle2"> 业务信息配置-[通用授权-个例]-智慧财务系统 </span>
        <span class="fontStyle">，由拥有权限的用户对其他用户进行授权</span>
      <Table v-width="800" :datas="roleInfos" :border="true">
        <TableItem title="角色" prop="roleTypeName" :width="100"></TableItem>
        <TableItem title="说明" prop="desc" :width="400"></TableItem>
        <TableItem title="人员" prop="userName" :width="250"></TableItem>
      </Table>
      </div>
			<div class="text-center">
				<Button color="green" @click="submit" :loading="loading">{{form.id?'更新':'创建'}}账套</Button>
				<Button @click="showForm=false">取消</Button>
			</div>
		</Modal>
		<Modal v-model="showModal" hasCloseIcon>
			<div slot="header">删除账套</div>
			<div>
				<div>您正在删除账套：<b>{{rmData.companyName}}</b><br>为保障数据安全，需要短信验证身份。</div>
				<Row type="flex" :space="10" class="margin">
					<Cell><input type="text" v-model="User.mobile" disabled></Cell>
					<Cell>
						<SmsVerificationCode :mobile="User.mobile"/>
					</Cell>
				</Row>
				<Row type="flex" :space="10" class="margin">
					<Cell><input v-model="msgCode" type="number" placeholder="请输入验证码"></Cell>
				</Row>
			</div>
			<div class="text-center">
				<Button color="red" :disabled="!this.msgCode" @click="doRemove" :loading="loading">删除</Button>
				<Button @click="showModal=false">取消</Button>
			</div>
		</Modal>
	</app-content>
</template>

<!--css样式-->
<style type="text/css">
.fontStyle {
  margin: 10px 0px;
  font-weight: normal;
  font-size: 13px;
  color: #d9d9d9
}
.fontStyle2{
  margin: 10px 0px;
  font-weight: normal;
  font-size: 13px;
  color: #5487e3
}
</style>

<script>
	import {getTotalData} from '@/assets/financial/js/locations/district';
	import moment from 'moment';
	import {mapState} from 'vuex';
	import SmsVerificationCode from "@/components/Financial/SmsVerificationCode";

	const emptyForm = {
		"accountingStandards": "0",
		"address": null,
		"companyName": "",
		"creditCode": "",
		"enableDate": null,
    "platformCode":null,
		"fixedAssetModule": "0",
		"cashierModule": "0",
		"voucherReviewed": "0",
		"industry": "",
		"vatType": "0"
	};

export default {
	name: 'Account',
	components: {SmsVerificationCode},
	data() {
		return {
			datas: [],
			pickerOption: {
				keyName: 'id',
				titleName: 'title',
				dataMode: 'list',
				parentName: 'parentId',
				datas: getTotalData()
			},
      roleInfos:[],
      roleTypeUser:[],
			form: Object.assign({}, emptyForm),
			validationRules: {
				required: ["companyName", "platformCode","enableDate", "accountingStandards", "fixed_asset_module", "cashier_module", "voucherReviewed", "vatType"]
			},
			showModal: false,
			rmData: {},
			msgCode: '',
			showForm: false,
			used: false,
			loading: false,
      accountingStandards:[{ title: '小企业会计准则', key: 0 }, { title: '企业会计准则', key: 1}, { title: '科技会计准则', key: 2}, { title: '担保会计准则', key: 3}],
      accountings:[],
      accountingManagers:[],
      companyDict:[],
      accountsSetsName: null,
      accountSetsList: [],
		};
	},
	watch: {
		showForm(val) {
			if (!val) {
				this.reset();
			}
		}
	},
	computed: {
    User() {
      return this.$store.state.financial.User;
    },
    currentAccountSets() {
      return this.$store.state.financial.currentAccountSets;
    },
    myAccountSets() {
      return this.$store.state.financial.myAccountSets;
    },
	},
  methods: {
		loadList(id) {
			this.$api.setting.accountSets.list({id: id}).then(({data}) => {
				this.datas = data || [];
			})
		},
		submit() {
			let validResult = this.$refs.form.valid();
			if (validResult.result) {
				this.loading = true;
				this.form.enableDate = moment(this.form.enableDate).format("YYYY-MM-DD");
				this.$api.setting.accountSets[this.form.id ? 'update' : 'save'](this.form).then(() => {
          this.$Message(this.form.id ? '修改账套成功！' : "创建账套成功！");
					this.$store.dispatch('init', this.currentAccountSets.id);
					this.loadList();
					this.showForm = false;
					this.loading = false;
				}).catch((err) => {
					this.loading = false;
          this.$Message(err);
				})
			}
		},
		reset() {
			this.form = Object.assign({}, emptyForm);
			this.used = false;
		},
		dateFormat(val) {
			return val ? moment(val).format("YYYY年MM月") : '';
		},
		remove(data) {
			this.showModal = true;
			this.rmData = data;
		},
		doRemove() {
			if (this.msgCode) {
				this.$Confirm("确认删除?").then(() => {
					this.$api.setting.accountSets.delete(this.rmData.id, this.msgCode).then(() => {
            this.$Message("删除成功 ！");
						this.loadList();
						this.$store.dispatch('init');
						this.showModal = false;
						this.rmData = {};
						this.msgCode = '';
					})
				})
			}
		},
		edit(row) {
			Api.setting.accountSets.checkUse(row.id).then(({data}) => {
				this.used = data;
				this.form = Object.assign({}, row);
				this.showForm = true;
        this.changeRoleTypeUser();
			})

		},
    setAccounting(){
		  Api.common.getConfig().then(({data}) => {
        this.accountings = data.accounting;
        this.accountingManagers = data.accountingManager;
        this.companyDict = data.companyDict;
        this.queryRoleTypeUser();
      })
    },
    platformCodeFormat(value){
      let companyDictMap={}
      this.companyDict.forEach(company => {companyDictMap[company.key] =  company.title})
		  return companyDictMap[value];
    },
    changeRoleTypeUser(){
      this.roleTypeUser.forEach((item, index) => {
        if(item.companyId === parseInt(this.form.platformCode)){
          this.roleInfos = item.roleList;
          this.roleInfos.forEach((item, index) => {
            this.form[item.roleType] = item.accUserIds;
          })
        }
      })
    },
    queryRoleTypeUser(){
      let companyIds =[];
      this.companyDict.forEach((item, index) => {
        companyIds.push(item.key)
      })
      this.$api.setting.user.queryRoleTypeUser({'companyIds':companyIds.toString()}).then(({data}) => {
        this.roleTypeUser = data;
      })
    },
    pushDatas(){
      this.accountSetsList = [];
      //账套列表
      this.myAccountSets.forEach(e=>{
        let accountSet = {
          title: e.companyName,
          key: e.id
        }
        this.accountSetsList.push(accountSet)
      })
    },
    accountsSetsChange(data) {
      if(data === undefined){
        this.loadList();
      }else{
        this.loadList(data.key);
      }
    },
	},
	mounted() {
    this.pushDatas()
		this.loadList();
		this.setAccounting();
	}
};
</script>
