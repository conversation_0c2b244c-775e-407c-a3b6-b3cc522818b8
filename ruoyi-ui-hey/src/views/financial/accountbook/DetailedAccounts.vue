<template>
	<app-content class="h-panel">
<!--    <div class="h-input h-input-prefix-icon" style="float: right">
      <input type="text" placeholder="输入账套名称" v-model="accountSetsName" @keyup.enter="searchAccountSets"/>
      <i class="h-icon-search"></i>
    </div>
    <Tabs :datas="accountSetsList" v-model="accountsSetsSelected" @change="accountsSetsChange"></Tabs>-->
    <div style="height: 10px"></div>
<!--    <Form v-width="450" :labelWidth="60" >
      <FormItem label="账套:" prop="accountSetsId">
        <Row type="flex">
          <Cell width="20" style="margin-right: 5px">
            <Select v-model="accountsSetsSelected" :datas="accountSetsList" filterable :deletable="false" @change="accountsSetsChange"></Select>
          </Cell>
        </Row>
      </FormItem>
    </Form>-->

		<div class="h-panel-bar">
			<span class="h-panel-title"><template v-if="subject">{{subject.name}}</template><template v-if="subject && subject.unit">(单位:{{subject.unit}})</template> - 明细账 </span>
		</div>
		<div class="margin-right-left margin-top">
<!--			<account-date-choose v-model="accountDate"/>-->
      <Row>
        <Cell>
          <DateRangePicker v-model="value" :format="format" @confirm="doSearch"></DateRangePicker>
          <Button text-color="yellow" style="margin-left: 5px" @click="handleExport">导出</Button>
        </Cell>
      </Row>
			<div class="float-right">
				<Checkbox :disabled="numPriceDisabled" v-model="showNumPrice">显示数量金额</Checkbox>
			</div>
		</div>
		<Row class="h-panel-body" type="flex" :space-x="10">
			<Cell :flex="1" style="overflow-x: auto;">
				<table class="cus-table" v-if="!showNumPrice">
					<thead class="header">
					<tr>
						<td>日期</td>
						<td>凭证字号</td>
						<td>科目</td>
						<td>摘要</td>
						<td>借方</td>
						<td>贷方</td>
						<td>方向</td>
						<td>余额</td>
					</tr>
					</thead>
					<tbody>
					<tr v-for="item in datalist" :key="item.subjecId">
						<td>{{item.voucherDate}}</td>
						<td>
							<router-link v-if="item.voucherId" :to="{name:'VoucherForm',params:{voucherId:item.voucherId}}">{{item.word}}-{{item.code}}</router-link>
						</td>
						<td>{{item.subjectName}}</td>
						<td>{{item.summary}}</td>
						<td class="text-right">{{item.debitAmount|numFormat}}</td>
						<td class="text-right">{{item.creditAmount|numFormat}}</td>
						<td class="text-center">{{item.balanceDirection}}</td>
						<td class="text-right">{{item.balance|numFormat}}</td>
					</tr>
					<tr v-if="!datalist.length">
						<td colspan="8" class="text-center padding">暂无数据</td>
					</tr>
					</tbody>
				</table>
				<table class="cus-table" style="width: 1550px;" v-else>
					<thead class="header">
					<tr>
						<td rowspan="2" width="100">日期</td>
						<td rowspan="2" width="100">凭证字号</td>
						<td rowspan="2" width="150">科目</td>
						<td rowspan="2" width="200">摘要</td>
						<td colspan="3">借方发生额</td>
						<td colspan="3">贷方发生额</td>
						<td colspan="4">余额</td>
					</tr>
					<tr>
						<td width="100">数量</td>
						<td width="100">单价</td>
						<td width="100">金额</td>
						<td width="100">数量</td>
						<td width="100">单价</td>
						<td width="100">金额</td>
						<td width="100">方向</td>
						<td width="100">数量</td>
						<td width="100">单价</td>
						<td width="100">金额</td>
					</tr>
					</thead>
					<tbody>
					<tr v-for="item in datalist" :key="item.subjecId">
						<td>{{item.voucherDate}}</td>
						<td>
							<router-link v-if="item.voucherId" :to="{name:'VoucherForm',params:{voucherId:item.voucherId}}">{{item.word}}-{{item.code}}</router-link>
						</td>
						<td class="nowrap">{{item.subjectName}}</td>
						<td class="nowrap">{{item.summary}}</td>
						<td class="text-right">{{item.debitAmount&&item.balanceDirection=='借'?item.num:''}}</td>
						<td class="text-right">{{item.debitAmount&&item.balanceDirection=='借'?item.price:''}}</td>
						<td class="text-right">{{item.debitAmount|numFormat}}</td>
						<td class="text-right">{{item.creditAmount&&item.balanceDirection=='贷'?item.num:''}}</td>
						<td class="text-right">{{item.creditAmount&&item.balanceDirection=='贷'?item.price:''}}</td>
						<td class="text-right">{{item.creditAmount|numFormat}}</td>
						<td class="text-center">{{item.balanceDirection}}</td>
						<td class="text-right">{{item.numBalance|numFormat}}</td>
						<td class="text-right">{{item.price|numFormat}}</td>
						<td class="text-right">{{item.balance|numFormat}}</td>
					</tr>
					<tr v-if="!datalist.length">
						<td colspan="8" class="text-center padding">暂无数据</td>
					</tr>
					</tbody>
				</table>
			</Cell>
			<Cell v-width="200">
				<Tree v-model="subjectId" @select="subjectSelect" :option="param" ref="subject" filterable selectOnClick className="h-tree-theme-row-selected"></Tree>
			</Cell>
		</Row>
	</app-content>
</template>

<script>
	import moment from "moment";
	import { download } from "@/utils/request";

  export default {
		name: 'DetailedAccounts',
		data() {
			return {
				loading: false,
				param: {
					keyName: 'id',
					parentName: 'parentId',
					titleName: 'subjectFullName',
					dataMode: 'list',
					datas: []
				},
				datalist: [],
				subjectId: null,
				showNumPrice: false,
				numPriceDisabled: true,
				accountDate: null,
				subject: null,
        //账套选中标识
        accountsSetsSelected: null,
        accountSetsList: [],
        accountSetsName: "",
        format: 'YYYY-MM-DD',
        value:{ "start": '', "end": ''}
			};
		},
		watch: {
			subject() {
				let org = this.showNumPrice;
				if (this.subject.unit) {
					this.numPriceDisabled = false;
					this.showNumPrice = true;
				} else {
					this.showNumPrice = false;
				}
				if (org == this.showNumPrice) {
					this.loadList();
				}
			},
			showNumPrice() {
				this.loadList();
			},
			accountDate() {
				this.loadSubject();
			}
		},
    computed: {
      currentAccountSets() {
        return this.$store.state.financial.currentAccountSets;
      },
      myAccountSets() {
        return this.$store.state.financial.myAccountSets;
      }
    },
    mounted() {
      //账套列表
      this.pushDatas()
      this.doSearch()
    },
		methods: {
			loadSubject() {
				this.datalist = [];
				let reqData = {
				  startTime: this.value.start,
          endTime: this.value.end,
          accountSetsId: this.currentAccountSets.id
        }
				this.$api.accountbook.loadSubject(reqData).then(({data}) => {
					this.$set(this.param, "datas", data);
					if (data.length) {
						this.subjectId = data[0].id;
						this.subject = data[0];
					}
				});
			},
			loadList() {
				this.loading = true;
        let reqData = {
          startTime: this.value.start,
          endTime: this.value.end,
          accountSetsId: this.currentAccountSets.id,
          subjectId: this.subjectId,
          showNumPrice: this.showNumPrice,
          subjectCode: this.subject.code
        }
				this.$api.accountbook.loadVoucherDetails(reqData).then(({data}) => {
					this.datalist = data;
					this.loading = false;
				});
			},
			subjectSelect(node) {
				this.subject = node;
			},
      accountsSetsChange(data) {
        //查询
        this.$store.dispatch('init', data.key).then(() => {
          this.doSearch();
        })
      },
      pushDatas(){
        this.accountSetsList = [];
        //账套列表
        this.myAccountSets.forEach(e=>{
          let accountSet = {
            title: e.companyName,
            key: e.id
          }
          this.accountSetsList.push(accountSet)
        })
        this.accountsSetsSelected = this.currentAccountSets.id;
      },
      searchAccountSets(event){
        if(this.accountSetsName != ''){
          let accountSetsMap = {};
          this.myAccountSets.forEach(accountSets => {accountSetsMap[accountSets.companyName] =  accountSets.id})
          //查询
          this.$store.dispatch('init', accountSetsMap[this.accountSetsName]).then(() => {
            this.accountSetsList = [];
            let accountSet = {
              title: this.currentAccountSets.companyName,
              key: this.currentAccountSets.id
            }
            this.accountSetsList.push(accountSet)
            this.doSearch();
          })

        }else{
          this.pushDatas()
        }
      },
      doSearch(){
        //this.accountDate = moment(this.currentAccountSets.currentAccountDate).format('YYYY-MM-DD');
        if(this.value.start == '' || this.value.start == undefined){
          this.value.start = moment(this.currentAccountSets.currentAccountDate).startOf('month').format('YYYY-MM-DD');
          this.value.end = moment(this.currentAccountSets.currentAccountDate).endOf('month').format('YYYY-MM-DD');
        }
        this.loadSubject();
      },
      /** 导出按钮操作 */
      handleExport() {
        let queryParams = {
          startTime: this.value.start,
          endTime: this.value.end,
          accountSetsId: this.currentAccountSets.id,
          showNumPrice: this.showNumPrice,
          subjectId: this.subjectId,
          subjectCode: this.subject.code
        }
        this.$Confirm('是否确认导出?', '提⽰', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          download('accountBook/exportDetail', {
            ...queryParams
          }, `detailAccounts_${new Date().getTime()}.xlsx`)
        }).catch(() => {})
        /*this.$api.accountbook.getEarliestTime(this.currentAccountSets.id).then(({data}) => {

        });*/
      }
		}
	};
</script>
