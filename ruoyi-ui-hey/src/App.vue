<template>
  <div id="app">
    <Layout
      class="app-frame"
      :siderCollapsed="sliderCollapsed"
      :siderFixed="layoutConfig.siderFixed"
    >
      <Sider :theme="layoutConfig.siderTheme">
        <appMenu :theme="layoutConfig.siderTheme"></appMenu>
      </Sider>
      <Layout :headerFixed="layoutConfig.headerFixed">
        <HHeader theme="white">
          <appHead
            @openSetting="openSetting = true"
            :layoutConfig="layoutConfig"
          ></appHead>
        </HHeader>
        <SysTabs v-if="layoutConfig.showSystab" homePage="Home"></SysTabs>
        <Content>
          <div class="app-frame-content">
            <keep-alive :include="cachedViews">
              <router-view v-if="isRouterAlive"></router-view>
            </keep-alive>
          </div>
          <HFooter>
            <appFooter></appFooter>
          </HFooter>
        </Content>
      </Layout>
    </Layout>
  </div>
</template>
<script>
import appHead from "@/views/financial/app/app-header";
import appMenu from "@/views/financial/app/app-menu";
import appFooter from "@/views/financial/app/app-footer";
import { mapState } from "vuex";
import SysTabs from "@/views/financial/app/sys-tabs/sys-tabs";

export default {
  name: "FXY",
  provide() {
    return {
      reload: this.reload,
    };
  },
  data() {
    return {
      title: "财务系统1",
      openSetting: false,
      isRouterAlive: true,
      layoutConfig: {
        siderTheme: "dark",
        showSystab: true,
        headerFixed: true,
        siderFixed: true,
      },
    };
  },
  methods: {
    updateLayoutConfig({ key, value }) {
      this.layoutConfig[key] = value;
    },
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(() => {
        this.isRouterAlive = true;
      });
    },
  },
  mounted() {
    window.document.title = "智慧财务系统";
  },
  computed: {
    ...mapState(["sliderCollapsed"]),
    cachedViews() {
      return this.$store.state.tagsView.cachedViews.map((item) => item.name);
    },
  },
  components: {
    appHead,
    appMenu,
    appFooter,
    SysTabs,
  },
};
</script>
