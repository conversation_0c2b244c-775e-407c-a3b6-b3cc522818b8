package com.ruoyi.common.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模板公司分类枚举
 * <AUTHOR> add 20240425
 */
public enum AuthUnitEnum {

    RDGS("RDGS","融担公司","融担公司","0",1,"0"),
    KJGS("KJGS","科技公司","科技公司","0",2,"0");

    /**
     * 公司编码
     */
    private final String unitCode;
    /**
     * 公司名称
     */
    private final String unitName;
    /**
     * 公司简称
     */
    private final String unitAliasName;
    /**
     * 是否是担保公司  0：是  1：不是
     */
    private final String isGuarantee;
    /**
     * 排序
     */
    private final Integer order;
    /**
     * 状态 0正常 1停用
     */
    private final String status;

    AuthUnitEnum(String unitCode, String unitName, String unitAliasName, String isGuarantee, Integer order, String status) {
        this.unitCode = unitCode;
        this.unitName = unitName;
        this.unitAliasName = unitAliasName;
        this.isGuarantee = isGuarantee;
        this.order = order;
        this.status = status;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public String getUnitAliasName() {
        return unitAliasName;
    }

    public String getIsGuarantee() {
        return isGuarantee;
    }

    public Integer getOrder() {
        return order;
    }

    public String getStatus() {
        return status;
    }

    public static List<Map<String, Object>> getList(){
        List<Map<String, Object>> list = new ArrayList<Map<String,Object>>();
        Map<String, Object> map = null;
        for (AuthUnitEnum be : values()) {
            map = new HashMap<String, Object>();
            map.put("unitCode", be.getUnitCode());
            map.put("unitName", be.getUnitName());
            map.put("unitAliasName", be.getUnitAliasName());
            map.put("isGuarantee", be.getIsGuarantee());
            map.put("order", be.getOrder());
            map.put("status", be.getStatus());
            list.add(map);
        }
        return list;
    }

    /**
     * 获取生效的公司分类枚举
     */
    public static List<Map<String, Object>> getStartList(){
        List<Map<String, Object>> list = new ArrayList<Map<String,Object>>();
        Map<String, Object> map = null;
        for (AuthUnitEnum be : values()) {
            if("0".equals(be.getStatus())){
                map = new HashMap<String, Object>();
                map.put("unitCode", be.getUnitCode());
                map.put("unitName", be.getUnitName());
                map.put("unitAliasName", be.getUnitAliasName());
                map.put("isGuarantee", be.getIsGuarantee());
                map.put("order", be.getOrder());
                list.add(map);
            }
        }
        return list;
    }

}
