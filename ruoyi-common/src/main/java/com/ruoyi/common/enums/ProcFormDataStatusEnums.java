package com.ruoyi.common.enums;

import lombok.Getter;

/**
 * Description：表单申请记录状态枚举
 * CreateTime：2024/2/21
 * Author：yu-qiang
 */
@Getter
public enum ProcFormDataStatusEnums {
    IN_APPROVAL("0","审批中"),
    APPROVED("1","审批通过"),
    NO_PASS("2","审批不通过"),
    TERMINATION("3","终止"),
    REJECTED("4","驳回"),
    ROUGH_DRAFT("5","草稿");

    private String code;

    private String message;

    ProcFormDataStatusEnums(String code, String message) {
        this.code=code;
        this.message = message;
    }
}
