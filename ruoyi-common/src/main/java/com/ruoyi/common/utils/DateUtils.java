package com.ruoyi.common.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
            "yyyyMMdd"};

    public enum DateFormat {
        YYYYMMDD("yyyyMMdd"),
        YYYY_MM_DD("yyyy-MM-dd"),
        YYYY_MM("yyyy-MM"),
        YYYYMM("yyyyMM"),
        YYYYMMDD_HHMMSS("yyyyMMdd HH:mm:ss"),
        YYYY_MM_DD_HHMMSS("yyyy-MM-dd HH:mm:ss"),
        YYYY_MM_DD_HHMM("yyyy-MM-dd HH:mm"),
        YYYYMMDDHHMMSS("yyyyMMddHHmmss");
        private String format;

        DateFormat(String format) {
            this.format = format;
        }

        public String getFormat() {
            return format;
        }

        protected void setFormat(String format) {
            this.format = format;
        }
    }

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate()
    {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }



    /**
     * @param str 时间
     * @return 汉字年月日
     */
    public static String formatStr(String str) {
        String[] split = str.split("-");
        String year = split[0];
        String month = split[1];
        String formatStr = year + "年" + month + "月";
        return formatStr;
    }


    /**
     * @param str 时间
     * @return 汉字年月日
     */
    public static Date formatYMRStr(String str) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy'年'MM'月'dd'日'", Locale.ENGLISH);

        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        Date myDate1 = null;
        try {
            myDate1 = sdf1.parse(str);

        }catch (Exception e){
            e.printStackTrace();
        }
        sdf.format(myDate1);

        return myDate1;
    }
    /**
     * @param
     * @return
     */
    public static String dateForStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String str =  sdf.format(date);
        String[] split = str.split("-");
        String year = split[0];
        String month = split[1];
        String formatStr = year+ "-"+month;
        return formatStr;
    }

    /**
     * 求出此前十一个月的时间
     * @param date
     * @return String
     */
    public static String monthSubtract(Date date) {
        Calendar c = Calendar.getInstance();
        // 设置为当前时间
        c.setTime(date);
        c.add(Calendar.MONTH, -11);
        return parseDateToStr(YYYY_MM, c.getTime());
    }

    /**
     * 求出输入日期以及前面十一个月的日期的List
     * @param date
     * @return List<String>
     */
    public static List<String> monthSubtractList(Date date) {
        Calendar c = Calendar.getInstance();
        List<String> list = new ArrayList<>();
        list.add(parseDateToStr(YYYY_MM, date));
        // 设置为当前时间
        c.setTime(date);
        for (int i = 0; i < 11; i++) {
            c.add(Calendar.MONTH, -1);
            String s = parseDateToStr(YYYY_MM, c.getTime());
            list.add(s);
        }
        return list;
    }

    /**
     * 求出输入日期以周为单位相差的时间List
     * @param date
     * @return List<String>
     */
    public static List<String> diffDayList(Date date,  int day) {
        Calendar c = Calendar.getInstance();
        List<String> list = new ArrayList<>();
        list.add(parseDateToStr(YYYY_MM_DD, date));
        // 设置为当前时间
        c.setTime(date);
        for (int i = 0; i < day; i = i+7) {
            c.add(Calendar.DAY_OF_MONTH, -7);
            String s = parseDateToStr(YYYY_MM_DD, c.getTime());
            list.add(s);
        }
        return list;
    }

    /**
     * 求出此前天的时间
     * @param date
     * @param day
     * @return String
     */
    public static String diffDay(Date date, int day) {
        Calendar c = Calendar.getInstance();
        // 设置为当前时间
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, -day);
        return parseDateToStr(YYYY_MM_DD, c.getTime());
    }

    /**
     * 获取当前日期的本周一是几号
     *
     * @return 本周一的日期
     */
    public static Date getThisWeekMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        if (date == null) {
            cal.setTime(new Date());
        } else {
            cal.setTime(date);
        }
        // 获得当前日期是一个星期的第几天 使用cal.get(Calendar.DAY_OF_WEEK);
        //获取的数表示的是每个星期的第几天，不能改变，其中星期日为第一天
        // 如果是星期日则获取天数时获取到的数字为1 在后面进行相减的时候出错
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        //  cal.getFirstDayOfWeek()根据前面的设置 来动态的改变此值
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }

    /**
     * 传入日期的年-月-日 获取上月的 年-月
     *
     * @return 上个月的年-月 字符串
     */
    public static String findLastMonthByStr(String monthStr) {
        Date date = parseDate(monthStr);
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.add(Calendar.MONTH, -1);
        return parseDateToStr(YYYY_MM, instance.getTime());
    }

    /**
     * 传入日期的年-月-日 获取上月最后一天
     *
     * @return 上月最后一天 字符串
     */
    public static String findLastMonthEndDayByStr(String dateStr) {
        Date date = parseDate(dateStr);
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.add(Calendar.MONTH, -1);
        instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DATE));
        return parseDateToStr(YYYY_MM_DD, instance.getTime());
    }

    /**
     * 得到当前月最后一天
     *
     * @param year  一年
     * @param month 月
     * @return {@link String}
     */
    public  static String getFirstday(String year,String month){
        LocalDate firstDayOfCurrentDate = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
        return firstDayOfCurrentDate.toString();
    }

    /**
     * 得到当前月最后一天
     *
     * @param year  一年
     * @param month 月
     * @return {@link String}
     */
    public  static String getLastday(String year,String month){
        LocalDate firstDayOfCurrentDate = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
        LocalDate lastDayOfCurrentDate = firstDayOfCurrentDate.with(TemporalAdjusters.lastDayOfMonth());
        return lastDayOfCurrentDate.toString();
    }

    /**
     * 找出开始和结束时间间 每一个 年-月（包含开始和结束月份）
     *
     * @param beginDateStr  开始时间 字符串
     * @param endDateStr 结束时间 字符串
     * @return 两时间中间所有的 年-月 字符串集合
     */
    public static List<String> findYearAndMonthByBeginAndEnd(String beginDateStr, String endDateStr) {
        List<String> result = new ArrayList<>();
        Date beginDate = parseDate(beginDateStr);
        Date endDate = parseDate(endDateStr);
        Calendar startInstance = Calendar.getInstance();
        startInstance.setTime(beginDate);
        startInstance.set(startInstance.get(Calendar.YEAR), startInstance.get(Calendar.MONTH), 1);
        Calendar endInstance = Calendar.getInstance();
        endInstance.setTime(endDate);
        endInstance.set(endInstance.get(Calendar.YEAR), endInstance.get(Calendar.MONTH), 2);
        while (startInstance.before(endInstance)) {
            result.add(parseDateToStr(YYYY_MM, startInstance.getTime()));
            startInstance.add(Calendar.MONTH, 1);
        }
        return  result;
    }


    /**
     * 根据所传参数获取多少周之前的日期 返回为开始日期
     * 比如传2则获取的是除本周外两周前周一的日期
     * @param weekIndex 周指数
     * @return {@link String}
     */
    public static String getWeekTimeSection(int weekIndex)
    {
        //weekIndex为前第几周的周数，如：获取当前时间前第二周的时间区间 weekIndex=2
        //获取本周第一天
        Calendar cal=Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 0);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        Date first=cal.getTime();
        Date last=cal.getTime();
        String firstString= new SimpleDateFormat("yyyy-MM-dd").format(first)+" 00:00:00";
        String lastString= new SimpleDateFormat("yyyy-MM-dd").format(last)+" 23:59:59";
        LocalDateTime firstTime = LocalDateTime.parse(firstString,
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime lastTime = LocalDateTime.parse(lastString,
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        //当前时间前第二周开始时间
        firstTime=firstTime.plusDays(-(weekIndex* 7L));
        //当前时间前第二周结束时间
        lastTime=lastTime.plusDays(-(weekIndex*7L-6));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String localDateNowStr = firstTime.format(formatter);
        return localDateNowStr;
    }

    public static String getWeekendTimeSection(int weekIndex)
    {
        //weekIndex为前第几周的周数，如：获取当前时间前第二周的时间区间 weekIndex=2
        //获取本周第一天
        Calendar cal=Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 0);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        Date first=cal.getTime();
        Date last=cal.getTime();
        String firstString= new SimpleDateFormat("yyyy-MM-dd").format(first)+" 00:00:00";
        String lastString= new SimpleDateFormat("yyyy-MM-dd").format(last)+" 23:59:59";
        LocalDateTime firstTime = LocalDateTime.parse(firstString,
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime lastTime = LocalDateTime.parse(lastString,
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        //当前时间前第二周开始时间
        firstTime=firstTime.plusDays(-(weekIndex* 7L));
        //当前时间前第二周结束时间
        lastTime=lastTime.plusDays(-(weekIndex*7L-6));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String localDateNowStr = lastTime.format(formatter);
        return localDateNowStr;
    }

    /**
     * 获取上个月月份
     * 比如传入2就获取的是除过当前月的前两个月月份
     * @param mon 我
     * @return {@link String}
     */
    public static  String getLastMonthEndDate(int mon) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-mon);
        // 设置为上一个月
        //calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        date = calendar.getTime();
        String format1 = format.format(date);
        String year =  format1.substring(0,4);
        String month = format1.substring(5,7);
        String lastDayOfMonth = getLastDayOfMonth(Integer.parseInt(year), Integer.parseInt(month));
        return lastDayOfMonth;
    }

    public static  String getLastMonth(int mon) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-mon);
        // 设置为上一个月
        //calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        date = calendar.getTime();
        return format.format(date);
    }

    /**
     * <AUTHOR>
     * @Description 获取过去或未来月份
     * @Date 2024/10/24 11:43
     * @Param [mon]
     * @return java.lang.String
     **/
    public static String getNextMonth(String monthStr, int num) {
        String monthBeginDate = monthStr+"-01";
        Date date = parseDate(monthBeginDate);
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, num);
        // 设置为上一个月
        //calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        date = calendar.getTime();

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        return format.format(date);
    }

    /**
     * 当前日期获取前X个月的日期 Date对象
     *
     * @param mon 要获取的X月
     * @return {@link String}
     */
    public static Date getLastMonthDate(int mon) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-mon);
        // 设置为上一个月
        //calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        date = calendar.getTime();
        return date;
    }

    public static String getLastDayOfMonth(int year,int month)
    {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR,year);
        //设置月份
        cal.set(Calendar.MONTH, month);
        //获取当月最小值
        int lastDay = cal.getMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中的月份，当月+1月-1天=当月最后一天
        cal.set(Calendar.DAY_OF_MONTH, lastDay-1);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());
        return lastDayOfMonth;
    }

    /**
     * 获取去年年份
     *
     * @param year 一年
     * @return {@link String}
     */
    public static  String getLastYear(int year) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, -year);
        // 设置为上一个月
        //calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        date = calendar.getTime();
        return format.format(date);
    }

    /**
     * <AUTHOR>
     * @Description 获取过去或未来年份
     * @Date 2024/10/24 11:20
     * @Param [year, num]
     * @return java.lang.String
     **/
    public static String getNextYear(String year, int num){
        String yearBeginDate = year+"-01-01";
        Date date = parseDate(yearBeginDate);
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, num);
        // 设置为上一个月
        //calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        date = calendar.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        return format.format(date);
    }

    public static  String getLastYearEnd(int year) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, -year);
        // 设置为上一个月
        //calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        date = calendar.getTime();
        String format1 = format.format(date);
        format1 = format1+"-12-31";
        return format1;
    }

    /**
     * 获取月末日期
     *
     * @param date 日期
     * @return {@link List}<{@link String}>
     */
    public static  List<String> getMonthEndDate(List<String> date){
        List<String> objects = new ArrayList<>();
        for (String s : date) {
            String year =  s.substring(0,4);
            String month = s.substring(5,7);
            String lastDayOfMonth = getLastDayOfMonth(Integer.parseInt(year), Integer.parseInt(month));
            objects.add(lastDayOfMonth);
        }
        return objects;
    }

    public static List<String> getweekDays(String date1, String date2){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        //首先得到两个日期之间的所有日期信息
        List<String> allDays = new ArrayList<>();
        int dayTime = 24*60*60*1000;
        long d1 = 0;
        long d2 = 0;
        try {
            d1 = format.parse(date1).getTime();
           d2 = format.parse(date2).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }

        while(d2>=d1){
            String time = format.format(d1);
            allDays.add(time);
            d1+=dayTime;
        }
        //将得到的所有日期遍历，将每个日期的星期信息取出
        Calendar calendar = Calendar.getInstance();
        Map<String,List<String>> stringListMap  = new HashMap<>();
        List<String> workdays = new ArrayList<>();
        List<String> weekenddays = new ArrayList<>();
        for(String str : allDays){
            try {
                calendar.setTime(format.parse(str));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            int week = calendar.get(Calendar.DAY_OF_WEEK);
            if(week!=1 && week!=7){
                workdays.add(str);//工作日
            }
            if(week==1){
                weekenddays.add(str);//周末
            }
        }

        stringListMap.put("workdays",workdays);
        stringListMap.put("weekenddays",weekenddays);

        return weekenddays;
    }

    public static String getWeekLastDate() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        if(cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY ){
            return simpleDateFormat.format(cal.getTime());
        }
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.DATE, cal.get(Calendar.DATE) + 6);
        return simpleDateFormat.format(cal.getTime());
    }

    /**
     * 获取当前月的开始时间
     *
     * @return
     */
    public static Date getMonthBegin() {
        return getMonthBegin(new Date());
    }

    /**
     * 获取给定日期月的开始时间
     *
     * @param date
     * @return
     */
    public static Date getMonthBegin(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DATE, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date getMonthEnd(Date date) {
        return getMonthEndWithTime(date, true);
    }

    /**
     * 获取给定日期月的结束时间
     *
     * @param date
     * @return
     */
    public static Date getMonthEndWithTime(Date date, boolean withTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
        if (withTime) {
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
        } else {
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
        }
        return calendar.getTime();
    }

    /**
     * 获取给定日期年的开始时间
     *
     * @param date
     * @return
     */
    public static Date getYearBegin(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DATE, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * <AUTHOR>
     * @Description 获取日期相差天数
     * @Date 2023/7/20 16:29
     * @Param [date, otherDate]
     * @return long
     **/
    public static int getIntervalDays(Date date, Date otherDate) {
        return Math.abs(getIntervalDaysActual(date, otherDate));
    }

    public static int getIntervalDaysActual(Date date, Date otherDate) {
        if (date == null || otherDate == null) {
            throw new IllegalArgumentException("日期不能为空");
        }
        long day = 24L * 3600 * 1000;
        int d1 = (int) (date.getTime() / day);
        int d2 = (int) (otherDate.getTime() / day);
        return d1 - d2;
    }

    /**
     * 计算两个时间差,精确到秒
     */
    public static String getDatePoorForSec(Date endDate, Date nowDate)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        long ns = 1000;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        long sec = diff % nd % nh % nm / ns;
        StringBuffer result = new StringBuffer();
        if(day>0){
            result.append(day + "天");
        }
        if(hour>0){
            result.append(hour + "小时");
        }
        if(min>0){
            result.append(min + "分");
        }
        if(sec>0){
            result.append(sec +"秒");
        }
        return result.toString();
    }

    /**
     * 获取某一月份结束时间
     *
     * @return
     */
    public static String getMonthEndDate(String dateStr) {
        Calendar c = getMonthTime(dateStr, 0);
        int lastMonthMaxDay = c.getActualMaximum(Calendar.DAY_OF_MONTH);
        c.set(c.get(Calendar.YEAR), c.get(Calendar.MONTH), lastMonthMaxDay, 23, 59, 59);
        return new SimpleDateFormat("yyyy-MM-dd").format(c.getTime());
    }

    /**
     * @param dateStr 2021-08
     * @param month
     * @return
     */
    private static Calendar getMonthTime(String dateStr, int month) {
        return getMonthTime(dateStr, month, "yyyy-MM");
    }

    private static Calendar getMonthTime(String dateStr, int month, String format) {
        Calendar c = Calendar.getInstance();
        if (null != dateStr) {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            try {
                Date date = sdf.parse(dateStr);
                c.setTime(date);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        c.add(Calendar.MONTH, month);
        return c;
    }

    /**
     * 获取日期范围集合
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param dateFormat 格式化
     * @return
     */
    public static List<String> getTimeRange(String startTime, String endTime, DateFormat dateFormat){
        Date startDate = parse(startTime, dateFormat);//开始日期
        Date endDate = parse(endTime, dateFormat);//结束日期
        int intervalDays = getIntervalDays(startDate, endDate);
        List<String> dateList = new ArrayList<>();
        dateList.add(startTime);
        while (intervalDays>0){
            startDate = getNextDate(startDate, 1);
            startTime = DateUtils.format(startDate, dateFormat);
            dateList.add(startTime);
            intervalDays--;
        }
        return dateList;
    }

    public static Date parse(String date, DateFormat format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format.getFormat());
        try {
            return sdf.parse(date);
        } catch (ParseException e) {
            throw new IllegalArgumentException("日期转换异常", e);
        }
    }

    public static Date getNextDate(Date date, int n) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int day = c.get(Calendar.DATE);
        c.set(Calendar.DATE, day + n);
        return c.getTime();
    }

    public static String format(Date date, DateFormat format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format.getFormat());
        return sdf.format(date);
    }

    /**
     * <AUTHOR>
     * @Description 根据日期获取季度
     * @Date 2024/10/17 17:06
     * @Param [dateStr]
     * @return java.lang.String
     **/
    public static String getQuarter(String dateStr){
        String year = dateStr.split("-")[0];
        Integer month = Integer.valueOf(dateStr.split("-")[1]);
        String quarterStr = "";
        switch (month){
            case 1: case 2: case 3:
                quarterStr = year+"-01";
                break;
            case 4: case 5: case 6:
                quarterStr = year+"-02";
                break;
            case 7: case 8: case 9:
                quarterStr = year+"-03";
                break;
            case 10: case 11: case 12:
                quarterStr = year+"-04";
                break;
        }
        return quarterStr;
    }

    /**
     * <AUTHOR>
     * @Description 根据日期获取月份
     * @Date 2024/10/24 11:12
     * @Param [dateStr]
     * @return java.lang.String
     **/
    public static String getMonthForDate(String dateStr){
        return dateStr.substring(0,7);
    }

    /**
     * <AUTHOR>
     * @Description 计算月份相差
     * @Date 2024/10/24 11:52
     * @Param [date1, date2]
     * @return java.lang.Integer
     **/
    public static Integer calcMob(String startDate, String endDate) {
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            throw new IllegalArgumentException("日期不能为空");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(parseDate(startDate));
        Calendar c1 = Calendar.getInstance();
        c1.setTime(parseDate(endDate));
        return Math.abs((c1.get(Calendar.YEAR) - c.get(Calendar.YEAR)) * 12 + (c1.get(Calendar.MONTH) - c.get(Calendar.MONTH)));
    }

    /**
     * <AUTHOR>
     * @Description 计算季度相差
     * @Date 2024/10/24 11:52
     * @Param [date1, date2]
     * @return java.lang.Integer
     **/
    public static Integer calcQuarter(String startDate, String endDate) {
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            throw new IllegalArgumentException("日期不能为空");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(parseDate(startDate));
        Calendar c1 = Calendar.getInstance();
        c1.setTime(parseDate(endDate));

        String start = getQuarter(startDate).split("-")[1];
        String end = getQuarter(endDate).split("-")[1];

        return Math.abs((c1.get(Calendar.YEAR) - c.get(Calendar.YEAR)) * 4 + (Integer.parseInt(end) - Integer.parseInt(start)));
    }

    /**
     * <AUTHOR>
     * @Description 获取过去或未来季度
     * @Date 2024/10/24 11:43
     * @Param [mon]
     * @return java.lang.String
     **/
    public static String getNextQuarter(String quarterStr, int num) {
        String[] split = quarterStr.split("-");
        int startYear = Integer.parseInt(split[0]);
        int addYear = num/4;
        int startQuarter = Integer.parseInt(split[1]);
        int addQuarter = num%4;

        int year = ((startQuarter+addQuarter)%4==0?0:(startQuarter+addQuarter)/4);
        int quarter = ((startQuarter+addQuarter)%4==0?4:(startQuarter+addQuarter)%4);
        return (startYear+addYear+year)+"-0"+quarter;
    }

    /**
     * <AUTHOR>
     * @Description 获取时间范围集合
     * @Date 2024/10/28 15:18
     * @Param [startTime, endTime, timeType(year,month)]
     * @return java.util.List<java.lang.String>
     **/
    public static List<String> getTimeRange(String startTime, String endTime, String timeType){
        List<String> list = new ArrayList<>();
        if("month".equals(timeType)){
            Integer count = calcMob(startTime, endTime);
            for (int i=0; i<=count; i++){
                list.add(getNextMonth(startTime, i));
            }
        }else if("quarter".equals(timeType)){
            Integer count = calcQuarter(startTime, endTime);
            for (int i=0; i<=count; i++){
                list.add(getNextQuarter(startTime, i));
            }
        }else if("year".equals(timeType)){
            int startYear = Integer.parseInt(startTime);
            Integer count = Integer.parseInt(endTime) - startYear;
            for (int i=0; i<=count; i++){
                list.add(String.valueOf(startYear+i));
            }
        }else{
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor)
    {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor)
    {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    public static void main(String[] args) {
        /*String lastday = getLastday("2023", "02");
        System.out.println(lastday);*/

        System.out.println(getNextYear("2012", 5));
        System.out.println(getNextMonth("2024-01", 2));
        System.out.println(getNextQuarter("2024-1", 1));

        //System.out.println(calcMob("2023-01", "2023-12"));
        System.out.println(calcQuarter("2023-10-01", "2024-10-24"));

        System.out.println(getTimeRange("2025-01", "2025-01", "month"));
        System.out.println("2024-01-01".substring(0,7));
    }
}
