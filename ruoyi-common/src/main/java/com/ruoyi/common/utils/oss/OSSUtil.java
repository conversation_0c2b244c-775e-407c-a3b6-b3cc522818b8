package com.ruoyi.common.utils.oss;

/**
 * <AUTHOR> wangXin
 * @date : 2022/1/14 17:00
 */

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.*;
import com.ruoyi.common.properties.OSSProperties;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Authoer: wangxin
 * @Description: OSS操作工具类
 * @Date: 2022/01/14 15:34
 **/
@Slf4j
@Component
public class OSSUtil {

    private static String endpoint;
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String uploadPath;

    private static OSS ossClient;

    @Autowired
    public void init(OSSProperties ossProperties) {
        OSSUtil.endpoint = ossProperties.getEndpoint();
        OSSUtil.accessKeyId = ossProperties.getAccessKeyId();
        OSSUtil.accessKeySecret = ossProperties.getAccessKeySecret();
        OSSUtil.bucketName = ossProperties.getBucketName();
        OSSUtil.uploadPath = ossProperties.getUploadPath();
    }

    /**
     * <AUTHOR>
     * @Description 获取OSS上传路径
     * @Date 2023/11/27 10:53
     * @Param []
     * @return java.lang.String
     **/
    public static String getUploadPath(){
        return uploadPath;
    }

    /**
     * <AUTHOR>
     * @Description 获取OSS上传路径
     * @Date 2023/11/27 10:53
     * @Param []
     * @return java.lang.String
     **/
    public static String getUrl(String path, String fileName){
        return  "/common/oss/"+ uploadPath + path + fileName;
    }

    /**
     *
     * @return
     */
    private static OSS getOssClient(){
        /*if(ossClient != null){
            return ossClient;
        }*/
        ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        return ossClient;
    }

    /**
     * 判断文件是否存在
     * @param filePath 文件路径：目录+文件名
     * @return
     */
    public static boolean isFileExists(String filePath) {
        OSS ossClient = getOssClient();
        return ossClient.doesObjectExist(bucketName , filePath);
    }

    public static List<String> getFileList(String filePath){
        OSS ossClient = getOssClient();
        //ObjectListing objectListing = ossClient.listObjects(bucketName, filePath);
        List<String> list = new ArrayList<>();
        String nextMarker = null;
        ObjectListing objectListing ;
        do{
            objectListing = ossClient.listObjects(new ListObjectsRequest(bucketName).withMarker(nextMarker).withPrefix(filePath).withMaxKeys(500));
            List<OSSObjectSummary> sums = objectListing.getObjectSummaries();
            List<String> fileList = sums.stream().map(OSSObjectSummary::getKey).collect(Collectors.toList());
            list.addAll(fileList);
            nextMarker = objectListing.getNextMarker();
        } while (objectListing.isTruncated());
        return list;
    }

    /**
     * 上传目录
     * @param uploadDir 上传目录
     * @param localFileDir 本地文件目录
     */
    public static void uploadDir(String uploadDir,String localFileDir){
        uploadDir(uploadDir+"/" , new File(localFileDir));
    }

    private static void uploadDir(String uploadDir, File file){
        File[] files = file.listFiles();
        for (File f: files) {
            if(f.isDirectory()){
                uploadDir(uploadDir+f.getName()+"/",f);
            }else{
                uploadFile(uploadDir+f.getName(), f.getPath());
            }
        }
    }


    /**
     * 上传文件
     * @param filePath 上传文件路径：目录+文件名
     * @param body byte数组
     * @return
     */
    public static void uploadFile(String filePath, byte[] body) {
        OSS ossClient = getOssClient();
        try {
            ossClient.putObject(bucketName , filePath , new ByteArrayInputStream(body));
        } catch (Exception e) {
            log.error("ex:{}" , e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
    }

    /**
     * 上传文件
     * @param filePath 上传文件路径：目录+文件名
     * @param inputStream 输入流
     * @return
     */
    public static void uploadFile(String filePath, InputStream inputStream) {
        OSS ossClient = getOssClient();
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, filePath, inputStream);
            ossClient.putObject(putObjectRequest);
        } catch (Exception e) {
            log.error("ex:{}" , e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
    }

    /**
     *上传文件
     * @param filePath 上传文件路径：目录+文件名
     * @param localPath 本地文件路径: 目录+文件名
     * @return
     */
    public static void uploadFile(String filePath, String localPath) {
        OSS ossClient = getOssClient();
        try {
            ossClient.putObject(bucketName , filePath , new File(localPath));
        } catch (Exception e) {
            log.error("ex:{}" , e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
    }

    /**
     * 下载文件
     * @param filePath
     */
    public static void downloadFile(String filePath, String localPath) {
        OSS ossClient = getOssClient();
        try {
            ossClient.getObject(new GetObjectRequest(bucketName, filePath), new File(localPath));
        } catch (Exception e) {
            log.error("ex:{}" , e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
    }

    /**
     *  下载文件
     * @param filePath 文件目录
     * @param fileName 文件名
     * @param response
     */
    public static void downloadFile(String filePath, String fileName, HttpServletResponse response) {
        OSS ossClient = getOssClient();
        try {
            String sourceFileName = response.getHeader("fileName");
            if(StringUtils.isBlank(sourceFileName)){
                sourceFileName = fileName;
            }
            System.out.println(sourceFileName);
            response.reset();
            OSSObject ossObject = ossClient.getObject(bucketName, filePath+fileName);
            response.setContentType(ossObject.getObjectMetadata().getContentType());
            response.setCharacterEncoding("utf-8");
            String[] extensionArr = {"pdf", "jpg", "jpeg", "png", "gif", "webp", "bmp"};
            if(Arrays.asList(extensionArr).contains(FilenameUtils.getExtension(sourceFileName))){
                //浏览器内展示
                FileUtils.setInlineResponseHeader(response, sourceFileName);
            }else{
                //下载文件
                FileUtils.setAttachmentResponseHeader(response, sourceFileName);
            }
            try(BufferedInputStream reader = new BufferedInputStream(ossObject.getObjectContent());
                OutputStream os = response.getOutputStream();){
                byte[] buffer = new byte[1024];
                int length = 0;
                while ((length = reader.read(buffer)) != -1) {
                    os.write(buffer, 0, length);
                }
            }
        } catch (Exception e) {
            log.error("ex:{}" , e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
    }

    /**
     * 读取文件
     * @param filePath 文件目录
     * @param response
     */
    public static void readFile(String filePath, HttpServletResponse response) {
        OSS ossClient = getOssClient();
        try {
            OSSObject ossObject = ossClient.getObject(bucketName, filePath);
            try(BufferedInputStream reader = new BufferedInputStream(ossObject.getObjectContent());
                OutputStream os = response.getOutputStream();){
                byte[] buffer = new byte[1024];
                int length = 0;
                while ((length = reader.read(buffer)) != -1) {
                    os.write(buffer, 0, length);
                }
            }
        } catch (Exception e) {
            log.error("ex:{}" , e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
    }

    /**
     *  删除文件,小心使用
     * @param filePath 文件目录
     */
    public static void deleteFile(String filePath) {
        OSS ossClient = getOssClient();
        try {
            ossClient.deleteObject(bucketName, filePath);
        } catch (Exception e) {
            log.error("ex:{}" , e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
    }

    /**
     * 重命名
     * @param sourceKey 源文件名
     * @param targetKey 目标文件名
     */
    public static void refactorName(String sourceKey, String targetKey){
        OSS ossClient = getOssClient();
        try {
            ossClient.copyObject(bucketName, sourceKey, bucketName, targetKey);
            ossClient.deleteObject(bucketName, sourceKey);
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 档案历史数据专用方法
     * @return
     */
    public static OSS getDAOssClient(){
        /*if(ossClient != null){
            return ossClient;
        }*/
        ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        return ossClient;
    }
}
