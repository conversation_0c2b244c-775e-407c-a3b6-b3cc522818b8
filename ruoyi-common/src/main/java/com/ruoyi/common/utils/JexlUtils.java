package com.ruoyi.common.utils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.jexl2.Expression;
import org.apache.commons.jexl2.JexlContext;
import org.apache.commons.jexl2.JexlEngine;
import org.apache.commons.jexl2.MapContext;

public class JexlUtils {

	
	private static final Pattern NUMBER_PATTERN = Pattern.compile("-?\\d+(\\.\\d+)?");
	/**
	 * 统一的公式计算方法入口
	 * @param jexlExp
	 * @param map
	 * @return
	 */
	public static String calculation(String jexlExp, Map<String, BigDecimal> map){
		try {
			JexlEngine jexl = new JexlEngine();
		    Expression expression = jexl.createExpression(jexlExp);
	    
		    JexlContext jc = new MapContext();
		    for (String key : map.keySet()) {
		        jc.set(key, map.get(key));
		    }
		    if (null == expression.evaluate(jc)) {
		        return "---表达式计算异常---";
		    }
		    return expression.evaluate(jc).toString();
	    } catch (Exception  e) {
	    	e.printStackTrace();
	    	return "---表达式配置错误---";
		}
	}
	
	
	
	
	
	
	
	/**
	 * 计算数字并保留指定小数位
	 * @param formula
	 * @param map
	 */
	public static void bigDecimalScale(String formula,Map<String, BigDecimal> map,int roundingMode) {
		String result = calculation(formula,map);
	    // 计算结果保留两位小数
	    if(result!=null && !result.equals("---表达式计算异常---") && isNumeric4(result)) {
	    	BigDecimal bNum = new BigDecimal(result).setScale(2,roundingMode);
	    }
	}
	
	
	/**
	 * 检查字符串是否为数字
	 * @param str
	 * @return
	 */
	public static boolean isNumeric4(String str) {
        //return str != null && str.chars().allMatch(Character::isDigit);
        return str != null && NUMBER_PATTERN.matcher(str).matches();
    }
	/**
	 * 检查字符串是否为布尔值
	 * @param str
	 * @return
	 */
	public static boolean isBoolean(String str) {
        return str != null && ( str.equalsIgnoreCase("true") || str.equalsIgnoreCase("false")) ;
    }
}
