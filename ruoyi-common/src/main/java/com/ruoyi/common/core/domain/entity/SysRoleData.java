package com.ruoyi.common.core.domain.entity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2023-08-30
 */
public class SysRoleData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自定义主键id */
    private Long customId;

    /** 角色id */
    @Excel(name = "角色id")
    private Long roleId;

    /** 外部系统编码 */
    @Excel(name = "外部系统编码")
    private String platformNo;

    /** 担保公司编码 */
    @Excel(name = "担保公司编码")
    private String custNo;

    /** 合作方编码 */
    @Excel(name = "合作方编码")
    private String partnerNo;

    /** 资金方编码 */
    @Excel(name = "资金方编码")
    private String fundNo;

    public void setCustomId(Long customId) 
    {
        this.customId = customId;
    }

    public Long getCustomId() 
    {
        return customId;
    }
    public void setRoleId(Long roleId) 
    {
        this.roleId = roleId;
    }

    public Long getRoleId() 
    {
        return roleId;
    }
    public void setPlatformNo(String platformNo) 
    {
        this.platformNo = platformNo;
    }

    public String getPlatformNo() 
    {
        return platformNo;
    }
    public void setCustNo(String custNo) 
    {
        this.custNo = custNo;
    }

    public String getCustNo() 
    {
        return custNo;
    }
    public void setPartnerNo(String partnerNo) 
    {
        this.partnerNo = partnerNo;
    }

    public String getPartnerNo() 
    {
        return partnerNo;
    }
    public void setFundNo(String fundNo) 
    {
        this.fundNo = fundNo;
    }

    public String getFundNo() 
    {
        return fundNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("customId", getCustomId())
            .append("roleId", getRoleId())
            .append("platformNo", getPlatformNo())
            .append("custNo", getCustNo())
            .append("partnerNo", getPartnerNo())
            .append("fundNo", getFundNo())
            .toString();
    }
}
