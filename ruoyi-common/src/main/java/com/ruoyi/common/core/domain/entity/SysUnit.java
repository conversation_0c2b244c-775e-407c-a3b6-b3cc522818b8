package com.ruoyi.common.core.domain.entity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 全量公司信息对象 sys_units
 * 
 * <AUTHOR>
 * @date 2023-08-09
 */
public class SysUnit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long unitId;

    /** 公司编码 */
    @Excel(name = "公司编码")
    private String unitCode;
    
    /** 公司名称 */
    @Excel(name = "公司名称")
    private String unitName;

    /** 公司简称 */
    @Excel(name = "公司简称")
    private String unitShortName;

    /** 统一社会信用代码 */
    @Excel(name = "统一社会信用代码")
    private String unitNo;

    /** 联系人 */
    @Excel(name = "联系人")
    private String linkman;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    private String email;

    /** 邮政编码 */
    @Excel(name = "邮政编码")
    private String postcode;

    /** 办公地址 */
    @Excel(name = "办公地址")
    private String businessAddress;

    /** 网站 */
    @Excel(name = "网站")
    private String website;

    /** 注册地址 */
    @Excel(name = "注册地址")
    private String registeredAddress;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;

    public Long getUnitId() {
		return unitId;
	}

	public void setUnitId(Long unitId) {
		this.unitId = unitId;
	}

	public void setUnitName(String unitName) 
    {
        this.unitName = unitName;
    }

    public String getUnitName() 
    {
        return unitName;
    }
    public void setUnitShortName(String unitShortName) 
    {
        this.unitShortName = unitShortName;
    }

    public String getUnitShortName() 
    {
        return unitShortName;
    }
    public void setUnitNo(String unitNo) 
    {
        this.unitNo = unitNo;
    }

    public String getUnitNo() 
    {
        return unitNo;
    }
    public void setLinkman(String linkman) 
    {
        this.linkman = linkman;
    }

    public String getLinkman() 
    {
        return linkman;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setEmail(String email) 
    {
        this.email = email;
    }

    public String getEmail() 
    {
        return email;
    }
    public void setPostcode(String postcode) 
    {
        this.postcode = postcode;
    }

    public String getPostcode() 
    {
        return postcode;
    }
    public void setBusinessAddress(String businessAddress) 
    {
        this.businessAddress = businessAddress;
    }

    public String getBusinessAddress() 
    {
        return businessAddress;
    }
    public void setWebsite(String website) 
    {
        this.website = website;
    }

    public String getWebsite() 
    {
        return website;
    }
    public void setRegisteredAddress(String registeredAddress) 
    {
        this.registeredAddress = registeredAddress;
    }

    public String getRegisteredAddress() 
    {
        return registeredAddress;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getUnitCode() {
		return unitCode;
	}

	public void setUnitCode(String unitCode) {
		this.unitCode = unitCode;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("unitId", getUnitId())
            .append("unitName", getUnitName())
            .append("unitShortName", getUnitShortName())
            .append("unitNo", getUnitNo())
            .append("linkman", getLinkman())
            .append("phone", getPhone())
            .append("email", getEmail())
            .append("postcode", getPostcode())
            .append("businessAddress", getBusinessAddress())
            .append("website", getWebsite())
            .append("registeredAddress", getRegisteredAddress())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
