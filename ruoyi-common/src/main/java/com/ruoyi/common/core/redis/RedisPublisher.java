package com.ruoyi.common.core.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class RedisPublisher {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    public void publish(String channel, String message) {
        redisTemplate.convertAndSend(channel, message);
    }
}