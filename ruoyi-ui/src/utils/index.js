import { parseTime } from "./ruoyi";
import XEUtils from "xe-utils";
/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
  if (cellValue == null || cellValue == "") return "";
  var date = new Date(cellValue);
  var year = date.getFullYear();
  var month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;
  var day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  var hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  var minutes =
    date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  var seconds =
    date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  return (
    year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds
  );
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (("" + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    return "1天前";
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url;
  const search = url.substring(url.lastIndexOf("?") + 1);
  const obj = {};
  const reg = /([^?&=]+)=([^?&=]*)/g;
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length;
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i);
    if (code > 0x7f && code <= 0x7ff) s++;
    else if (code > 0x7ff && code <= 0xffff) s += 2;
    if (code >= 0xdc00 && code <= 0xdfff) i--;
  }
  return s;
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return "";
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return "";
      return encodeURIComponent(key) + "=" + encodeURIComponent(json[key]);
    })
  ).join("&");
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split("?")[1]).replace(/\+/g, " ");
  if (!search) {
    return {};
  }
  const obj = {};
  const searchArr = search.split("&");
  searchArr.forEach((v) => {
    const index = v.indexOf("=");
    if (index !== -1) {
      const name = v.substring(0, index);
      const val = v.substring(index + 1, v.length);
      obj[name] = val;
    }
  });
  return obj;
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement("div");
  div.innerHTML = val;
  return div.textContent || div.innerText;
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== "object") {
    target = {};
  }
  if (Array.isArray(source)) {
    return source.slice();
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property];
    if (typeof sourceProperty === "object") {
      target[property] = objectMerge(target[property], sourceProperty);
    } else {
      target[property] = sourceProperty;
    }
  });
  return target;
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return;
  }
  let classString = element.className;
  const nameIndex = classString.indexOf(className);
  if (nameIndex === -1) {
    classString += "" + className;
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length);
  }
  element.className = classString;
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === "start") {
    return new Date().getTime() - 3600 * 1000 * 24 * 90;
  } else {
    return new Date(new Date().toDateString());
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function (...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "deepClone");
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === "object") {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr));
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + "";
  const randomNum = parseInt((1 + Math.random()) * 65536) + "";
  return (+(randomNum + timestamp)).toString(32);
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += " " + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    ele.className = ele.className.replace(reg, " ");
  }
}

export function makeMap(str, expectsLowerCase) {
  const map = Object.create(null);
  const list = str.split(",");
  for (let i = 0; i < list.length; i++) {
    map[list[i]] = true;
  }
  return expectsLowerCase ? (val) => map[val.toLowerCase()] : (val) => map[val];
}

export const exportDefault = "export default ";

export const beautifierConf = {
  html: {
    indent_size: "2",
    indent_char: " ",
    max_preserve_newlines: "-1",
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: "separate",
    brace_style: "end-expand",
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: "110",
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true,
  },
  js: {
    indent_size: "2",
    indent_char: " ",
    max_preserve_newlines: "-1",
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: "normal",
    brace_style: "end-expand",
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: "110",
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true,
  },
};

// 首字母大小
export function titleCase(str) {
  return str.replace(/( |^)[a-z]/g, (L) => L.toUpperCase());
}

// 下划转驼峰
export function camelCase(str) {
  return str.replace(/_[a-z]/g, (str1) => str1.substr(-1).toUpperCase());
}

export function isNumberStr(str) {
  return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str);
}
export function format(time, format = "yyyy-MM-dd HH:mm:ss") {
  if (!time) return "--";
  var t = new Date(time);
  var tf = function (i) {
    return (i < 10 ? "0" : "") + i;
  };
  return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
    switch (a) {
      case "yyyy":
        return tf(t.getFullYear());
        break;
      case "MM":
        return tf(t.getMonth() + 1);
        break;
      case "mm":
        return tf(t.getMinutes());
        break;
      case "dd":
        return tf(t.getDate());
        break;
      case "HH":
        return tf(t.getHours());
        break;
      case "ss":
        return tf(t.getSeconds());
        break;
    }
  });
}
/** 格式化金额 */
export function formaterMoney(data) {
  if (!data) return "0.00";
  if (data === "-") return "-";
  // 将数据分割，保留两位小数
  data = data.toFixed(2);
  // 获取整数部分
  const intPart = Math.trunc(data);
  // 整数部分处理，增加,
  const intPartFormat = intPart
    .toString()
    .replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
  // 预定义小数部分
  let floatPart = ".00";
  // 将数据分割为小数部分和整数部分
  const newArr = data.toString().split(".");
  if (newArr.length === 2) {
    // 有小数部分
    floatPart = newArr[1].toString(); // 取得小数部分
    if (1 / intPart < 0 && intPart === 0) {
      return "-" + intPartFormat + "." + floatPart;
    }
    return intPartFormat + "." + floatPart;
  }
  if (1 / intPart < 0 && intPart === 0) {
    return "-" + intPartFormat + "." + floatPart;
  }
  return intPartFormat + floatPart;
}
//身份证号获取出生日期
export function getBirthdatByIdNo(iIdNo) {
  var tmpStr = "";
  var idDate = "";
  var tmpInt = 0;
  var strReturn = "";

  iIdNo = iIdNo.replace(/^\s+|\s+$/g, "");

  if (iIdNo.length == 15) {
    tmpStr = iIdNo.substring(6, 12);
    tmpStr = "19" + tmpStr;
    tmpStr =
      tmpStr.substring(0, 4) +
      "-" +
      tmpStr.substring(4, 6) +
      "-" +
      tmpStr.substring(6);
    return tmpStr;
  } else {
    tmpStr = iIdNo.substring(6, 14);
    tmpStr =
      tmpStr.substring(0, 4) +
      "-" +
      tmpStr.substring(4, 6) +
      "-" +
      tmpStr.substring(6);
    return tmpStr;
  }
}

/**
 * 指定日期增加n月
 * @param dateStr 指定日期
 * @param num 月
 * @param type 0:2023-01-30   1：2022-01-30 11:11:11
 * @returns {string}
 * @author: chenguowu
 */
export function commonAddMouth(dateStr, num, type = 0) {
  var monthNum = 0;
  if (typeof num == "string") {
    monthNum = parseInt(num);
  } else {
    monthNum = num;
  }
  var date = new Date(dateStr);
  //获取原日
  var day = date.getDate();
  //获取原月份
  var month = date.getMonth();
  //设置增加月份
  date.setMonth(date.getMonth() + monthNum * 1, 1);
  //获取增加的后的月份
  var Jmonth = date.getMonth() + 1;
  //获取增加的后的年份
  var Jyear = date.getFullYear();
  if (Jmonth == 4 || Jmonth == 6 || Jmonth == 9 || Jmonth == 11) {
    //小月
    if (day > 30) {
      day = 30;
    }
  } else if (Jmonth == 2) {
    //2月判断是否闰年
    if ((Jyear % 4 == 0 && Jyear % 100 != 0) || Jyear % 400 == 0) {
      if (day > 29) {
        day = 29;
      }
    }
    if (day > 28) {
      day = 28;
    }
  } else {
    //大月
    if (day > 31) {
      day = 31;
    }
  }
  var tHours = date.getHours();
  var tMinutes = date.getMinutes();
  var tSeconds = date.getSeconds();
  tHours = tHours < 10 ? "0" + tHours : tHours;
  tMinutes = tMinutes < 10 ? "0" + tMinutes : tMinutes;
  tSeconds = tSeconds < 10 ? "0" + tSeconds : tSeconds;
  Jmonth = doHandleMonth(Jmonth);
  day = doHandleMonth(day);
  if (type == "0") {
    return `${Jyear}-${Jmonth}-${day}`;
  }

  return `${Jyear}-${Jmonth}-${day} ${tHours}:${tMinutes}:${tSeconds}`;
}

/**
 *  日或月补0
 * @param month
 * @returns {string}
 */
function doHandleMonth(month) {
  var m = month;
  if (month.toString().length == 1) {
    m = "0" + month;
  }
  return m;
}

//限制输入正数，保留limit位小数
export function decimal(num, limit = 1000000) {
  var str =String(num) ;
  var len1 = str.substr(0, 1);
  var len2 = str.substr(1, 1);
  //如果第一位是0，第二位不是点，就用数字把点替换掉
  if (str.length > 1 && len1 == 0 && len2 != ".") {
    str = str.substr(1, 1);
  }
  //第一位不能是.
  if (len1 == ".") {
    str = "";
  }
  //限制只能输入一个小数点
  if (str.indexOf(".") != -1) {
    var str_ = str.substr(str.indexOf(".") + 1);
    if (str_.indexOf(".") != -1) {
      str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
    }
  }
  var reg = new RegExp("^\\D*([0-9]\\d*\\.?\\d{0," + limit + "})?.*$");
  //正则替换
  str = str.replace(/[^\d^\.]+/g, ""); // 保留数字和小数点
  str = str.replace(reg, "$1"); // 小数点后只能输 limit 位
  return str;
}

//强制保留两位小数，不足补零
export function returnFloat(value) {
  var s = value.toString().split(".");
  console.log(s);
  if (s.length == 1) {
    value = value.toString() + ".00";
    return value;
  }
  if (s.length > 1) {
    if (s[1].length < 2) {
      value = value.toString() + "0";
    }
    if (s[1] == "") {
      value = value.toString() + "00";
    }
    return value;
  }
}

//日期比较
export function compareDate(s1, s2) {
  return new Date(s1.replace(/-/g, "/")) > new Date(s2.replace(/-/g, "/"));
}

export function getTreeName(treeList, id) {
  for (let i = 0; i < treeList.length; i++) {
    let treeItem = treeList[i];
    if (treeItem.id === id) {
      return treeItem;
    } else {
      if (treeItem.children && treeItem.children.length > 0) {
        let res = getTreeName(treeItem.children, id);
        if (res) {
          return res;
        }
      }
    }
  }
}

export function arrToObj(data) {
  const result = data.reduce((acc, item) => {
    acc[item.dictCode] = item.dictLabel;
    return acc;
  }, {});
  return result;
}

export function filterTreeData(tree, bList) {
  return tree
    .filter((item) => {
      return bList.indexOf(item.id) > -1;
    })
    .map((item) => {
      item = Object.assign({}, item);
      if (item.children) {
        item.children = filterTreeData(item.children, bList);
      }
      return item;
    });
}
export function uniqueArrObj(arr, id = "id") {
  return arr.reduce((accumulator, current) => {
    const length = accumulator.filter((obj) => obj[id] === current[id]).length;
    if (length === 0) {
      accumulator.push(current);
    }
    return accumulator;
  }, []);
}

export function generateKVMap(data) {
  return data.reduce(
    (map, { dictType, dictValue, dictLabel }) => ({
      ...map,
      [dictType]: {
        ...map[dictType],
        [dictValue]: dictLabel,
      },
    }),
    {}
  );
}
export function generateKVMapCode(data) {
  return data.reduce(
    (map, { dictType, dictCode, dictLabel }) => ({
      ...map,
      [dictType]: {
        ...map[dictType],
        [dictCode]: dictLabel,
      },
    }),
    {}
  );
}
export function generateOptionsMap(data) {
  return data.reduce(
    (map, { dictType, dictValue, dictLabel, dictCode }) => ({
      ...map,
      [dictType]: [
        ...(map[dictType] || []),
        { label: dictLabel, value: dictValue, code: dictCode },
      ],
    }),
    {}
  );
}
export function generateFormOptionsMap(data) {
  const optionsMap = generateOptionsMap(data);
  return Object.keys(optionsMap).reduce((map, key) => {
    const option = XEUtils.clone(optionsMap[key]);
    option.unshift({ label: "全部", value: undefined, code: undefined });
    map[key] = option;
    return map;
  }, {});
}

/**
 * 将科学计数法的数字转为字符串
 * 说明：运算精度丢失方法中处理数字的时候，如果出现科学计数法，就会导致结果出错
 * 4.496794759834739e-9  ==> 0.000000004496794759834739
 * 4.496794759834739e+9  ==> 4496794759.834739
 * @param  num
 */
export function toNonExponential(num) {
  if (num == null) {
    return num;
  }
  if (typeof num == "number") {
    const m = num.toExponential().match(/\d(?:\.(\d*))?e([+-]\d+)/);
    return num.toFixed(Math.max(0, (m[1] || "").length - m[2]));
  } else {
    return num;
  }
}

/**
 * 乘法 - js运算精度丢失问题
 * @param arg1  数1
 * @param arg2  数2
 * 0.0023 * 100 ==> 0.22999999999999998
 * {{ 0.0023 | multiply(100) }} ==> 0.23
 */
export function floatMultiply(arg1, arg2) {
  arg1 = Number(arg1);
  arg2 = Number(arg2);
  if ((!arg1 && arg1 !== 0) || (!arg2 && arg2 !== 0)) {
    return null;
  }
  arg1 = toNonExponential(arg1);
  arg2 = toNonExponential(arg2);
  let r1;
  let r2; // 小数位数
  try {
    r1 = arg1?.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2?.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  const n1 = Number(arg1?.toString().replace(".", ""));
  const n2 = Number(arg2?.toString().replace(".", ""));
  return (n1 * n2) / Math.pow(10, r1 + r2);
}

/**
 * 除法 - js运算精度丢失问题
 * @param arg1  数1
 * @param arg2  数2
 * 0.0023 / 0.00001 ==> 229.99999999999997
 * {{ 0.0023 | divide(0.00001) }} ==> 230
 */
export function floatDivide(arg1, arg2) {
  arg1 = Number(arg1);
  arg2 = Number(arg2);
  if (!arg2) {
    return null;
  }
  if (!arg1 && arg1 !== 0) {
    return null;
  } else if (arg1 === 0) {
    return 0;
  }
  arg1 = toNonExponential(arg1);
  arg2 = toNonExponential(arg2);
  let r1;
  let r2; // 小数位数
  try {
    r1 = arg1?.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2?.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  const n1 = Number(arg1?.toString().replace(".", ""));
  const n2 = Number(arg2?.toString().replace(".", ""));
  return floatMultiply(n1 / n2, Math.pow(10, r2 - r1));
  // return (n1 / n2) * Math.pow(10, r2 - r1);   // 直接乘法还是会出现精度问题
}

/**
 * 加法 - js运算精度丢失问题
 * @param arg  数
 * 0.0023 + 0.00000000000001 ==> 0.0023000000000099998
 * {{ 0.0023 | plus(0.00000000000001) }} ==> 0.00230000000001
 */
export function floatAdd(...args) {
  // 将所有参数转换为非指数形式
  args = args.map(arg => toNonExponential(Number(arg) || 0));

  // 获取每个参数的小数位数
  const r = args.reduce((acc, arg) => {
    let decimalPlaces;
    try {
      decimalPlaces = arg?.toString().split(".")[1].length;
    } catch (e) {
      decimalPlaces = 0;
    }
    return Math.max(acc, decimalPlaces);
  }, 0);

  // 将所有参数乘以 10^r，相加后再除以 10^r，以避免精度问题
  const m = Math.pow(10, r);
  const sum = args.reduce((acc, arg) => acc + floatMultiply(arg, m), 0);

  return sum / m;
}
/**
 * 减法 - js运算精度丢失问题
 * @param arg1  数1
 * @param arg2  数2
 * 0.0023 - 0.00000011  ==>  0.0022998899999999997
 * {{ 0.0023 | minus( 0.00000011 ) }}  ==>  0.00229989
 */
export function floatSub(arg1, arg2) {
  arg1 = Number(arg1) || 0;
  arg2 = Number(arg2) || 0;
  arg1 = toNonExponential(arg1);
  arg2 = toNonExponential(arg2);
  let r1;
  let r2;
  try {
    r1 = arg1?.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2?.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  const m = Math.pow(10, Math.max(r1, r2));
  // 动态控制精度长度
  const n = r1 >= r2 ? r1 : r2;
  return Number(((floatMultiply(arg1, m) - floatMultiply(arg2, m)) / m).toFixed(n));
}

/**
 * 取余 - js运算精度丢失问题
 * @param arg1  数1
 * @param arg2  数2
 * 12.24 % 12  ==> 0.2400000000000002
 * {{ 12.24 | mod( -12 ) }}  ==>  0.24
 */
export function floatMod(arg1, arg2) {
  arg1 = Number(arg1);
  arg2 = Number(arg2);
  if (!arg2) {
    return null;
  }
  if (!arg1 && arg1 !== 0) {
    return null;
  } else if (arg1 === 0) {
    return 0;
  }
  let intNum = arg1 / arg2;
  intNum = intNum < 0 ? Math.ceil(arg1 / arg2) : Math.floor(arg1 / arg2); // -1.02 取整为 -1; 1.02取整为1
  const intVal = floatMultiply(intNum, arg2);
  return floatSub(arg1, intVal);
}

export function addTotal(obj, arr) {
  let sum = 0;
  arr.forEach((item) => {
    sum = floatAdd(sum, obj[item]);
  });
  return sum;
}
export function highlightKeyword(text,keyWord) {
  if (!keyWord) return text;
  if (text==undefined||text==null) return ;
  const regex = new RegExp(`(${keyWord})`, "gi");
  return String(text).replace(
    regex,
    '<span style="background-color: yellow;">$1</span>'
  );
}
export function formatNumberWithCommas(number) {
  // 将数字转换为字符串，并将其按照千分位格式化
  return number?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
export function toMillennials(data, values) {
  data.forEach((item) => {
    values.forEach((item1) => {
      const data=item[item1]
      item[item1] = String(data).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    });
  });
}
export function convertDateToQuarter(dateString) {
  const [year, month] = dateString.split('-').map(Number);
  const quarter = Math.ceil(month / 3);
  return `${year}.0${quarter}`;
}
export function findNodeById(data, id) {
  // 遍历树形数据
  for (let node of data) {
    // 如果当前节点的id匹配，直接返回该节点
    if (node.id == id) {
      return node;
    }
    // 如果当前节点有children，则递归查找子节点
    if (node.children) {
      const found = findNodeById(node.children, id);
      // 如果在子节点中找到了匹配的节点，直接返回
      if (found) {
        return found;
      }
    }
  }
  // 如果未找到匹配的节点，返回null或者自定义的空值
  return null;
}
export function renameField(node, oldField, newField) {
  // 遍历当前节点的每个子节点
  if (node.hasOwnProperty(oldField)) {
    // 如果当前节点有目标字段，重命名它
    node[newField] = node[oldField];
    delete node[oldField];  // 删除原始字段
  }

  // 如果节点有子节点，递归调用
  if (Array.isArray(node.children)) {
    node.children.forEach(child => renameField(child, oldField, newField));
  }
}

export function setEmptyArrayToUndefined(tree,children) {
  // 遍历树形结构
  tree.forEach(node => {
    // 如果 node.children 是空数组，则将其设置为 undefined
    if (node[children] && node[children].length === 0) {
      
      node[children] = undefined;
    }
    // 如果 node.children 存在且是数组，递归调用
    if (node[children] && Array.isArray(node[children])) {
      setEmptyArrayToUndefined(node[children],children);
    }
  });
}
export function getNameById(data, targetId) {
  let result = '';
  // 遍历数据查找目标id
  function findItem(arr, parentName = '') {
    for (let item of arr) {
      const currentName = parentName ? `${parentName}-${item.dataName}` : item.dataName;
      if (item.id === targetId) {
        result = currentName;  // 找到目标id并拼接name
        return true;
      }
      // 如果当前项有子级，就递归查找
      if (item.fPiattaformas) {
        const found = findItem(item.fPiattaformas, currentName);
        if (found) return true;
      }
    }
    return false;  // 没有找到目标id
  }
  findItem(data);
  return result;
}