<template>
  <div>
    <!-- <div class="fixed">
      <el-radio-group v-model="organizationalType">
        <el-radio-button label="人员组织架构" v-hasPermi="['home:organizationalAll:user']"></el-radio-button>
        <el-radio-button label="组织架构"  v-hasPermi="['home:organizationalAll:unit']"></el-radio-button>
      </el-radio-group>
    </div> -->
    <organizationalUserCommon
      :api="getPersonnelOrganizationList"
    ></organizationalUserCommon>
    <!-- <organizationalCommon
      :api="getOrganizationList"
      v-show="organizationalType == '组织架构'"
    ></organizationalCommon> -->
  </div>
</template>
<script>
// import { getOrganizationList } from "@/api/system/dept";
import { getPersonnelOrganizationList } from "@/api/personnel/archives";
// import OrganizationalCommon from "@/views/organizationalCommon/index.vue";
import organizationalUserCommon from "@/views/organizationalUserCommon/index.vue";
// import { checkPermi } from "@/utils/permission"; // 权限判断函数

export default {
  name: "OrganizationalAll",
  components: { organizationalUserCommon },
  data() {
    return {
      // getOrganizationList,
      getPersonnelOrganizationList,
      // organizationalType: "人员组织架构",
    };
  },
  computed: {},
  created() {
    // this.init()
  },
  methods: {
    // init(){
    //  this.organizationalType=checkPermi(["home:organizationalAll:user"])?'人员组织架构':'组织架构';
    // }
  },
};
</script>
<style lang="scss" scoped>
// .fixed {
//   position: absolute;
//   right: 20px;
//   top: 20px;
// }
</style>