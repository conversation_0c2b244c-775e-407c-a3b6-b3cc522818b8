<template>
  <div class="content">
    <div class="content_left">
      <div class="content_left_company">
        <div class="content_left_company_title">{{ currentMessage.label }}</div>
        <div v-show="['company'].includes(currentMessage.type)">
          部门: <span>{{ currentMessage.deptNum }}</span>
        </div>
        <div v-show="['company', 'dept'].includes(currentMessage.type)">
          岗位: <span>{{ currentMessage.postNum }}</span>
        </div>
        <div v-show="['company', 'dept', 'post'].includes(currentMessage.type)">
          人员:
          <span>{{
            partTimeJob
              ? currentMessage.partTimeJobUserNum
              : currentMessage.userNum
          }}</span>
        </div>
      </div>
      <div
        v-show="['user'].includes(currentMessage.type)"
        class="content_left_company"
      >
        <div style="margin: auto; width: 100px; margin-bottom: 30px">
          <el-image
            style="width: 100px; height: 100px; border-radius: 50%"
            :src="currentMessage.avatar"
            :preview-src-list="[currentMessage.avatar]"
          >
          </el-image>
        </div>
        <div class="content_left_user_content">
          <span class="content_left_user_title"> 姓名:</span>
          {{ currentMessage.label }}
        </div>
        <div class="content_left_user_content">
          <span class="content_left_user_title"> 手机:</span>
          {{ currentMessage.phone }}
        </div>
        <div class="content_left_user_content">
          <span class="content_left_user_title"> 邮箱:</span>
          {{ currentMessage.email }}
        </div>
        <div class="content_left_user_content" style="display: flex">
          <div class="content_left_user_title">部门:</div>
          <div style="flex: 1; min-width: 0">
            <div
              v-for="(item, index) in currentMessage.deptList"
              :key="index"
              style="margin-bottom: 2px"
            >
              {{ item }}
            </div>
          </div>
        </div>
        <div class="content_left_user_content" style="display: flex">
          <div class="content_left_user_title">岗位:</div>
          <div style="flex: 1; min-width: 0">
            <div
              v-for="(item, index) in currentMessage.postList"
              :key="index"
              style="margin-bottom: 2px"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content_right">
      <div style="display: flex; padding: 10px 0">
        <div style="margin-right: 10px">
          <el-switch
            v-model="horizontal"
            @change="horizontalChange"
          ></el-switch>
          横向
        </div>
        <div style="margin-right: 10px">
          <el-switch v-model="expendDep" @change="expendDepChange"></el-switch>
          展开部门
        </div>
        <div style="margin-right: 10px">
          <el-switch
            v-model="expendPost"
            @change="expendPostChange"
          ></el-switch>
          展开岗位
        </div>
        <div style="margin-right: 10px">
          <el-switch
            v-model="expendUser"
            @change="expendUserChange"
          ></el-switch>
          展开人员
        </div>
        <div style="margin-right: 10px">
          <el-checkbox
            v-model="partTimeJob"
            @change="partTimeJobChange"
          ></el-checkbox>
          兼岗
        </div>
      </div>
      <div style="padding-bottom: 10px">
        <el-input
          type="text"
          style="width: 200px; margin-right: 10px"
          v-model.trim="keyword"
          placeholder="请输入搜索内容"
          clearable
          @keyup.enter.native="handleEnter"
          @input="handleEnterInput"
        />
        <span v-show="isActiveList.length"
          >{{ isActiveCurrent }}/{{ isActiveList.length }}</span
        >
        <el-button
          type="primary"
          size="small"
          @click="search"
          style="margin-left: 20px"
          >搜索</el-button
        >
        <el-button size="small" @click="resetSearch">重置</el-button>
        <el-select
          v-model="companySearch"
          placeholder="请选择所属公司"
          clearable
          @change="changeCompany"
          multiple
          style="width: 300px; margin-left: 20px"
        >
          <el-option
            v-for="(item, index) in unitListEnableList"
            :key="index"
            :label="item"
            :value="item"
          />
        </el-select>
        <div
          style="
            display: flex;
            float: right;
            position: relative;
            top: 20px;
            right: 20px;
          "
        >
          <div
            v-for="(item, index) in identification"
            style="margin-left: 10px; display: flex"
          >
            <div
              style="
                width: 16px;
                height: 16px;
                margin-right: 2px;
                border-radius: 5px;
                position: relative;
                top: 2px;
              "
              :style="{ background: item.value }"
            ></div>
            <div>{{ item.label }}</div>
          </div>
        </div>
      </div>
      <div
        style="height: 80vh; border: 1px solid #eee; position: relative"
        :class="[isHorizontalOne ? 'isHorizontalOne' : '']"
      >
        <i
          @click="exportPic"
          class="el-icon-download"
          style="
            font-size: 24px;
            position: absolute;
            right: 20px;
            top: 2px;
            z-index: 100;
            cursor: pointer;
          "
        ></i>
        <zm-tree-org
          ref="organizational"
          class="organizational"
          :data="data"
          :horizontal="horizontal"
          :node-draggable="false"
          :define-menus="defineMenus"
          :default-expand-level="defaultLevel"
          :toolBar="toolBar"
          disabled
          collapsable
          @on-expand="onExpand"
        >
          <template v-slot="{ node }">
            <div
              @click="onNodeClick(node)"
              :style="{
                background: colorList[node.type],
                width: widthList[node.type],
                padding: paddingList[node.type],
              }"
              :class="[
                keyword && node.label && node.label.indexOf(keyword) != -1
                  ? 'isActive'
                  : 'common',
                'tree-org-node__text node-label',
              ]"
            >
              <div>
                <span
                  :style="{
                    color:
                      keyword && node.label && node.label.indexOf(keyword) != -1
                        ? 'rgb(64, 158, 255)'
                        : 'black',
                  }"
                  >{{ node.label }}</span
                >
                <span v-show="node.type == 'post'">
                  <span v-show="partTimeJob && node.partTimeJobUserNum"
                    >({{ node.partTimeJobUserNum }})</span
                  >
                  <span v-show="!partTimeJob && node.userNum"
                    >({{ node.userNum }})</span
                  >
                </span>
                <span v-show="node.partTimeJob">( 兼岗 )</span>
                <!-- <span v-show="node.status == 1">( 已停用 )</span> -->
              </div>
              <div v-show="node.type == 'dept'">
                <div>
                  岗位: {{ node.postNum }} 员工:
                  {{ partTimeJob ? node.partTimeJobUserNum : node.userNum }}
                </div>
              </div>
            </div>
          </template>
          <!-- <template v-slot:expand="{node}">
          <div>{{node.children.length}}</div>
        </template>  -->
        </zm-tree-org>
      </div>
    </div>
  </div>
</template>
<script>
import XEUtils from "xe-utils";
import { mapGetters } from "vuex";
import { exportPic } from "@/utils/exportPic";
import { getReturnSelfInfo } from "@/api/system/dept";

export default {
  name: "OrganizationalCommon",
  props: {
    api: {
      type: Function,
      required: true,
    },
    isOneCompany: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentMessage: {},
      toolBar: Object.freeze({
        scale: false,
        expand: false,
      }),
      identification: Object.freeze([
        { label: "部门", value: "rgb(232, 244, 255)" },
        { label: "岗位", value: "rgb(217, 240, 221)" },
        { label: "人员", value: "rgb(248, 247, 226)" },
      ]),
      colorList: Object.freeze({
        dept: "rgb(232, 244, 255)",
        post: "rgb(217, 240, 221)",
        user: "rgb(248, 247, 226)",
      }),
      widthList: Object.freeze({
        company: "150px",
        dept: "160px",
        post: "160px",
        user: "140px",
      }),
      paddingList: Object.freeze({
        company: "",
        dept: "",
        post: "4px 0",
        user: "2px 0",
      }),
      defaultLevel: 1,
      data: {},
      dataFilter: {},
      dataAll: {},
      horizontal: true,
      expendDep: false,
      expendPost: false,
      expendUser: false,
      partTimeJob: false,
      unitListEnableList: [],
      keyword: "",
      companySearch: "",
      isHorizontalOne: false, //是否是横向排列且仅有一个根节点
      isActiveList: [], //高亮数组
      isActiveCurrent: 0, //当前搜索的是第几个
      currentDom: null,
    };
  },
  computed: {
    ...mapGetters(["avatar"]),
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.getData();
      this.getUnitListEnable();
      this.getReturnSelfInfo();
    },
    addAvatar() {
      this.currentMessage.avatar = this.currentMessage.avatar
        ? process.env.VUE_APP_BASE_API + this.currentMessage.avatar
        : this.avatar;
    },
    async getReturnSelfInfo() {
      const { data } = await getReturnSelfInfo();
      this.currentMessage = { ...data };
      this.addAvatar();
    },
    async getData() {
      const { data } = await this.api();
      const treeData = {
        id: 0,
        children: data,
      };
      this.dataAll = XEUtils.clone(treeData, true);
      this.filterJob(treeData);
      // this.defaultLevel = this.getNodeDepth(treeData);
    },
    async getUnitListEnable() {
      const { children } = this.data;
      this.unitListEnableList = children.map((item) => item.label);
    },
    filterTreeArray(tree, key) {
      return tree
        .filter((item) => {
          return !item[key];
        })
        .map((item) => {
          item = Object.assign({}, item);
          if (item.children) {
            item.children = this.filterTreeArray(item.children, key);
          }
          return item;
        });
    },
    filterJob(treeData) {
      const dataFilter = XEUtils.clone(
        this.filterTreeArray(treeData.children, "partTimeJob"),
        true
      );
      this.dataFilter = {
        id: 0,
        children: dataFilter,
      };
      this.data = XEUtils.clone(this.dataFilter, true);
    },
    changeCompany() {
      this.horizontalChange();
      this.getChangeCompany();
      this.$refs.organizational.restoreOrgchart();
    },
    getChangeCompany() {
      if (!this.companySearch.length) {
        this.data = this.partTimeJob
          ? XEUtils.clone(this.dataAll, true)
          : XEUtils.clone(this.dataFilter, true);
        return;
      }
      let tempData;
      tempData = this.partTimeJob
        ? XEUtils.clone(this.dataAll.children, true)
        : XEUtils.clone(this.dataFilter.children, true);
      tempData = tempData.filter((item) =>
        this.companySearch.includes(item.label)
      );
      const dataChildren = [];
      tempData.forEach((item) => {
        dataChildren.push(item);
      });
      this.data = {
        id: 0,
        children: dataChildren,
      };
    },
    getNodeDepth(node) {
      let depth = 0;
      function calculateDepth(currentNode, currentDepth) {
        if (currentDepth > depth) {
          depth = currentDepth;
        }
        if (currentNode.children) {
          currentNode.children.forEach((child) => {
            calculateDepth(child, currentDepth + 1);
          });
        }
      }
      calculateDepth(node, 0);
      return depth;
    },
    addExpendFalse(tree) {
      if (tree.id == 0) {
        this.$set(tree, "expand", true);
      } else {
        this.$set(tree, "expand", false);
      }
      if (tree.children) {
        tree.children.forEach((child) => {
          this.addExpendFalse(child);
        });
      }
    },
    addExpendToNumNodes(tree, typeList, value, childrenType) {
      if (childrenType) {
        if (typeList.includes(tree.type) && childrenType == tree.childrenType) {
          this.$set(tree, "expand", value);
        }
      } else {
        if (typeList.includes(tree.type)) {
          this.$set(tree, "expand", value);
        }
      }
      if (tree.children) {
        tree.children.forEach((child) => {
          this.addExpendToNumNodes(child, typeList, value, childrenType);
        });
      }
    },
    expendDepChange() {
      if (!this.expendDep) {
        this.expendPost = false;
        this.expendUser = false;
      }
      this.addExpendFalse(this.data);
      this.addExpendToNumNodes(
        this.data,
        ["company", "dept"],
        this.expendDep,
        "dept"
      );

      this.resetOrganizationalXY();
    },
    expendPostChange() {
      if (this.expendPost) this.expendDep = true;
      if (!this.expendPost) this.expendUser = false;
      this.addExpendFalse(this.data);
      this.addExpendToNumNodes(this.data, ["company", "dept"], this.expendPost);

      this.resetOrganizationalXY();
    },
    expendUserChange() {
      if (this.expendUser) {
        this.expendPost = true;
        this.expendDep = true;
      }
      this.addExpendFalse(this.data);
      this.toggleExpand(this.data, this.expendUser);

      this.resetOrganizationalXY();
    },

    toggleExpand(data, val) {
      if (Array.isArray(data)) {
        data.forEach((item) => {
          this.$set(item, "expand", val);
          if (item.children) {
            this.toggleExpand(item.children, val);
          }
        });
      } else {
        this.$set(data, "expand", val);
        if (data.id == 0) this.$set(data, "expand", true);
        if (data.children) {
          this.toggleExpand(data.children, val);
        }
      }
    },
    horizontalChange() {
      if (
        !this.horizontal &&
        (this.companySearch.length == 1 || this.isOneCompany)
      ) {
        this.isHorizontalOne = true;
      } else {
        this.isHorizontalOne = false;
      }
      this.keyword = "";
      this.handleEnterInput();
    },
    partTimeJobChange() {
      this.expendDep = false;
      this.expendPost = false;
      this.expendUser = false;
      this.changeCompany();
    },
    onNodeClick(data) {
      this.currentMessage = { ...data };
      this.addAvatar();
    },
    defineMenus() {},
    search() {
      this.handleEnter();
    },
    resetSearch() {
      this.keyword = "";
      this.handleEnterInput();
    },
    handleEnter() {
      this.toggleExpand(this.data, true);
      this.$nextTick(() => {
        this.$refs.organizational.scale = 1;
        const initX = 1040; //初始x偏移量
        const initY = 540; //初始y偏移量
        const isActiveList = document.querySelectorAll(
          ".organizational .isActive"
        );
        if (isActiveList.length && !this.isActiveList.length) {
          isActiveList.forEach((item) => {
            const top =
              item.getBoundingClientRect().top < 0
                ? 0
                : item.getBoundingClientRect().top;
            const left =
              item.getBoundingClientRect().left < 0
                ? 0
                : item.getBoundingClientRect().left;
            this.isActiveList.push([left, top]);
          });
        }
        if (this.isActiveList.length) {
          if (!(this.isActiveList.length > this.isActiveCurrent))
            this.isActiveCurrent = 0;
          const x = -this.isActiveList[this.isActiveCurrent][0] + initX;
          const y = -this.isActiveList[this.isActiveCurrent][1] + initY;
          this.$refs.organizational.onDrag(x, y);
          this.removeAll(isActiveList);
          this.setCurrent(isActiveList);
          this.isActiveCurrent++;
        }
      });
    },
    setCurrent(dom) {
      this.currentDom = dom[this.isActiveCurrent];
      this.currentDom.classList.add("isCurrenActive");
    },
    removeAll(dom) {
      dom.forEach((item) => {
        item.classList.remove("isCurrenActive");
      });
    },
    removeCurrent() {
      if (this.currentDom) {
        this.currentDom.classList.remove("isCurrenActive");
        this.currentDom = null;
      }
    },
    resetOrganizationalXY() {
      this.$refs.organizational.scale = 1;
      this.$refs.organizational.onDrag(0, 0);
    },
    handleEnterInput() {
      this.removeCurrent();
      this.isActiveList = [];
      this.isActiveCurrent = 0;
      this.$refs.organizational.scale = 1;
      this.$refs.organizational.onDrag(0, 0);
    },
    exportPic() {
      const dom = document.querySelector(".organizational .tree-org-node");
      exportPic(dom, "组织架构图");
    },
    onExpand(e) {
      if (e.target._prevClass?.indexOf("expanded") != -1) {
        this.$refs.organizational.scale = 1;
        this.$refs.organizational.onDrag(0, 0);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  .content_left {
    width: 17%;
    .content_left_company {
      padding: 20px;
      text-align: left;
      > div {
        margin-bottom: 15px;
      }
      .content_left_company_title {
        font-size: 16px;
        color: black;
        font-weight: 600;
        margin-bottom: 20px;
      }
      .content_left_user_title {
        font-weight: 600;
        color: rgb(31, 30, 30);
        margin-right: 10px;
      }
      .content_left_user_content {
        border-bottom: 1px solid rgb(223, 215, 215);
        padding-bottom: 10px;
      }
      span {
        color: black;
      }
    }
  }
  .content_right {
    flex: 1;
    min-width: 0;
    ::v-deep .is-empty {
      display: none;
    }
    ::v-deep .is-empty + .tree-org-node__children::before {
      border: none;
    }
    ::v-deep
      .is-empty
      + .tree-org-node__children
      > .tree-org-node:first-child:before {
      border: none;
    }
    ::v-deep .is-empty + .tree-org-node__children::after {
      border-left: none;
    }

    ::v-deep .isHorizontalOne {
      .is-empty {
        + .tree-org-node__children > .tree-org-node:first-child::after {
          border-left: none;
        }
      }
    }
    ::v-deep .tree-org > .tree-org-node {
      border: 1px dashed rgba(0, 0, 0, 0.2);
      padding: 20px;
    }
    .common {
      color: rgba(156, 153, 153, 0.986);
      cursor: pointer;
    }
    .isActive {
      color: rgb(64, 158, 255);
      border: 1px solid #ccc;
      box-shadow: 0px 0px 5px #bbb;
      cursor: pointer;
    }
    .isCurrenActive {
      background: rgb(221, 194, 144) !important;
    }
  }
}
</style>