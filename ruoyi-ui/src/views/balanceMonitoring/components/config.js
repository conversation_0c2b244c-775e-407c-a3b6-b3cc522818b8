export default {
  columns: [
    { label: "项目名称", prop: "projectName", minWidth: "200px" },
    { label: "当期可放款金额（元）", prop: "availableLoanAmount", minWidth: "180px" },
    { label: "未占用保证金金额（元）", prop: "unappropriatedAmount", minWidth: "180px" },
    { label: "项目保证金账户总额（元）", key: "totalMargin", minWidth: "180px" },
    { label: "保证金已占用金额", prop: "takenUpAmount", minWidth: "180px" },
    { label: "机构保证金占用比例", key: "occupancyRatioStr", minWidth: "180px" },
    { label: "当前在保金额", prop: "loanBalance", minWidth: "180px" },
    { label: "保证金比例", prop: "marginRateStr", minWidth: "150px" },
    { label: "昨日实际放款金额（元）", prop: "yesterdayLentActualAmount", minWidth: "180px" },
    { label: "本月实际放款金额（元）", prop: "monthLentActualAmount", minWidth: "180px" },
    { label: "数据更新时间", prop: "updateTime", minWidth: "180px" },
    { label: "操作", key: "operate", minWidth: "200px" }
  ],
  columnsDialog: [
    { label: "修改时间", prop: "updateTime", minWidth: "180px" },
    { label: "追加保证金金额（元）", prop: "operateAmount", minWidth: "180px" },
    { label: "保证金实际追加时间", prop: "realityDate", minWidth: "180px" },
    { label: "初始保证金金额", prop: "originalAmount", minWidth: "180px" },
    { label: "修改后保证金金额（元）", prop: "updatedAmount", minWidth: "180px" },
    { label: "操作人", prop: "operatorNickName", minWidth: "130px" }
  ],
};
