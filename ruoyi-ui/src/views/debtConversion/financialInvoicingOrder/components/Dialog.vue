<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      width="550px"
      @open="handleOpen"
    >
      <div class="pr-5">
        <MyForm
          v-model="invoiceInfo"
          :columns="invoiceDetailDialogColumns"
          formType="false"
          ref="form"
          :rules="invoiceDetailDialogRule"
        />
      </div>
      <span slot="footer">
        <div class="flex justify-end">
          <el-button @click="innerValue = false">返回</el-button>
          <el-button @click="submit" type="primary">确定</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
  
  <script>
import vModelMixin from "@/mixin/v-model";
import config from "./config";

export default {
  mixins: [vModelMixin],
  name: "Dialog",
  props: {},
  computed: {},
  data() {
    return {
      ...config,
      invoiceInfo: {},
    };
  },
  mounted() {},
  methods: {
    handleOpen() {},
    submit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
         this.$emit('dialogSubmit',this.invoiceInfo.reasonDeleteFile);
         this.innerValue=false;
        }
      });
    },
  },
};
</script>
  <style lang="less" scoped>
</style>