export default {
  pushStatusObj: {
    1: "未推送",
    2: "推送中",
    3: "推送完成",
  },
  formColumns: [
    {
      label: "创建日期",
      prop: "createTime",
      type: "datePicker",
      dateType: "daterange",
      valueFormat: "yyyy-MM-dd",
      startPlaceholder: "开始日期",
      endPlaceholder: "结束日期",
      rangeSeparator: "至",
    },
    {
      label: "担保公司",
      prop: "custId",
      type: "select",
      placeholder: "请选择担保公司",
      filterable: true,
      options: [],
      dataProp: { value: "id", label: "companyShortName" }, //非必写
    },
    {
      label: "债转主题",
      prop: "debtConversionTheme",
      type: "input",
      placeholder: "请输入债转主题",
    },
  ],
  columns: Object.freeze([
    {
      label: "*债转主题",
      prop: "debtConversionTheme",
    },
    {
      label: "担保公司",
      prop: "companyShortName",
    },
    {
      label: "债转通知发起时间",
      prop: "noticeLaunchTime",
    },
    {
      label: "债转通知完成时间",
      prop: "noticeCompleteTime",
    },
    {
      label: "推送状态",
      key: "pushStatus",
    },
    {
      label: "创建人",
      prop: "createByName",
    },
    {
      label: "创建日期",
      prop: "createTime",
    },
    {
      label: "操作",
      key: "operation",
      width: 150,
    },
  ]),
  title: Object.freeze([
    [
      "序号",
      "借款申请编号",
      "借款人",
      "身份证号",
      "手机号",
      "借款日期",
      "资产方",
      "资金方",
      "担保公司",
      "借款金额（元）",
      "债权接收方",
    ],
  ]),
  valueArr: Object.freeze([
    "index",
    "loanCode",
    "borrower",
    "idCard",
    "phoneNum",
    "loanTime",
    "partnerName",
    "fundName",
    "custName",
    "loanAmount",
    "debtRecipientName"
  ]),
  fileName: "债转文件",
  downloadUrl: "/debt/conversion/file/importTemplate",
  // 表单配置
  columnsAddFile: [
    {
      label: "担保公司",
      prop: "custId",
      type: "select",
      filterable: true,
      placeholder: "请选择担保公司",
      options: [],
      dataProp: { value: "id", label: "companyShortName" },
      span: 24,
    },
    {
      label: "债转主题",
      prop: "debtConversionTheme",
      type: "input",
      placeholder: "请输入债转主题",
      maxlength: 25,
      span: 24,
    },
    {
      type: "slot",
      slotName: "fileUpload",
      span: 24,
    },
  ],
  rules: Object.freeze({
    custId: [
      { required: true, message: "请选择担保公司", trigger: "change" },
    ],
    debtConversionTheme: [
      { required: true, message: "请输入债转主题", trigger: "change" },
    ],
  }),
  formColumsFileDetail: [
    {
      label: "借款人",
      prop: "borrower",
      type: "input",
      placeholder: "请输入借款人",
    },
    {
      label: "手机号",
      prop: "phoneNum",
      type: "input",
      placeholder: "请输入手机号",
    },
    {
      label: "身份证",
      prop: "idCard",
      type: "input",
      placeholder: "请输入身份证",
    },
  ],
  columnsFileDetail: [
    {
      label: "*借款申请编号",
      prop: "loanCode",
    },
    {
      label: "*借款人",
      prop: "borrower",
    },
    {
      label: "*身份证号",
      prop: "idCard",
    },
    {
      label: "*手机号",
      prop: "phoneNum",
    },
    {
      label: "*借款日期",
      prop: "loanTime",
    },
    {
      label: "*担保日期",
      prop: "guaranteeTime",
    },
    {
      label: "*资产方",
      prop: "partnerName",
    },
    {
      label: "*资金方",
      prop: "fundName",
    },
    {
      label: "*担保公司",
      prop: "custName",
    },
    {
      label: "*借款金额（元）",
      prop: "loanAmount",
    },
    {
      label: "*债权接收方",
      prop: "debtRecipientName",
    },
  ],
};
