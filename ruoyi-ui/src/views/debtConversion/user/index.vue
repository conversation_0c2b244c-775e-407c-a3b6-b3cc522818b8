<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>

    <MyTable
      ref="table"
      :columns="columns"
      :source="userList"
      :showIndex="true"
    >
      <template #dataSource="{ record }">
        {{$store.state.data.KV_MAP.debt_user_source[record.dataSource]}}
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  
  </div>
</template>

<script>
import { listUser } from "@/api/debtConversion/user";
import config from "./components/config";

export default {
  name: "User",
  data() {
    return {
      ...config,
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        phoneNum: undefined,
        idCard: undefined,
      },

    };
  },
  created() {
    // this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams).then((response) => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
 
  },
};
</script>
