<template>
  <commonPayment :header="header" :columns="columns" :columnsHeader="columnsHeader" :otherQueryParams="otherQueryParams" :hasPermi="hasPermi"></commonPayment>
</template>

<script>
import commonPayment from "@/views/financialInquiry/components/commonPayment";
export default {
  name: "CompensationAmount",
  components: { commonPayment },

  data() {
    return {
      hasPermi: ["compensationAmount:add"],
      header: Object.freeze({
        title: "通道业务代偿金额",
        content: [
          "数据来源：智慧财务系统-科目余额表，仅限通道业务",
          "收到代偿款：科目余额表中的  (负债类-收到代偿款)",
          "实际代偿款：科目余额表中的  (资产类-实际代偿款)",
          "代偿款余额 = 期初金额 + 收到代偿款 - 实际代偿款",
        ],
      }),
      columns: Object.freeze([
        { label: "项目名称", prop: "projectName", minWidth: "500px" },
        { label: "项目类型", prop: "projectType", width: "200px" },
        {
          label: "期初金额",
          prop: "qichu",
          width: "200px",
        },
        {
          label: "收到代偿款",
          prop: "shoudao",
          isHSlot: true,
          width: "200px",
        },
        {
          label: "实际代偿款",
          prop: "shiji",
          isHSlot: true,
          width: "200px",
        },
        {
          label: "代偿款余额",
          prop: "yue",
          isHSlot: true,
          width: "200px",
        },
        {
          label: "操作",
          key: "opertion",
          width: "200px",
        },
      ]),
      columnsHeader:Object.freeze({
        shoudao:'收到代偿款 = 科目余额表中的（负债类-收到代偿款-项目名称），仅限项目类型为通道业务的项目',
        shiji:'实际代偿款 = 科目余额表中的（资产类-实际代偿款-项目名称），仅限项目类型为通道业务的项目',
        yue:'代偿款余额 = 上期末余额 + 收到代偿款 - 实际代偿款',
      
      }),
      otherQueryParams:Object.freeze({
        type:'tongdao'
      
      })
    };
  },

  created() {},
  methods: {},
};
</script>
