export default {
  header: Object.freeze({
    title: "第三方技术服务费",
    content: [
      "数据来源：智慧财务系统-科目余额表",
      "第三方技术服务费：科目余额表中的（应付账款3-项目名称-单位名称）借方发生额",
    ],
  }),
  columns: [],
  monthColums:[],
  columnsProjectDetails: Object.freeze([
    { label: "记账日期", prop: "dataDay" },
    {
      label: "金额",
      prop: "totalAmt",
    },
    {
      label: "收款单位",
      prop: "beneficiary",
    },
  ]),
  pickerOptions: Object.freeze({
    shortcuts: [
      {
        text: "最近一周",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近一个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近三个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          picker.$emit("pick", [start, end]);
        },
      },
    ],
  }),
};
