<template>
  <div class="p-5">
    <ShowHeader :header="header" />
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="70px"
      style="margin-bottom: 10px"
    >
      <el-form-item label="银行账户" prop="bankAccount">
        <el-select
          v-model="queryParams.bankAccount"
          placeholder="请选择银行账户"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in accountSetsList"
            :key="index"
            :label="item.companyName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账套" prop="accountSetsId">
        <el-select
          v-model="queryParams.accountSetsId"
          placeholder="请选择账套"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in accountSetsList"
            :key="index"
            :label="item.companyName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间维度" prop="timeDimension">
        <el-select
          v-model="queryParams.timeDimension"
          placeholder="请选择时间维度"
          @change="handleTime"
        >
          <el-option
            v-for="(item, index) in timeDimensionList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="统计时间" prop="time">
        <el-date-picker
          v-if="['month', 'day'].includes(queryParams.timeDimension)"
          v-model="queryParams.time"
          :type="
            queryParams.timeDimension == 'month' ? 'monthrange' : 'daterange'
          "
          range-separator="至"
          :start-placeholder="
            queryParams.timeDimension == 'month' ? '开始月份' : '开始日期'
          "
          :end-placeholder="
            queryParams.timeDimension == 'month' ? '结束月份' : '结束日期'
          "
          :value-format="
            queryParams.timeDimension == 'month' ? 'yy-MM' : 'yy-MM-dd'
          "
        >
        </el-date-picker>
        <div v-if="queryParams.timeDimension == 'year'">
          <el-date-picker
            v-model="queryParams.time[0]"
            type="year"
            placeholder="开始年份"
            :pickerOptions="pickerOptionsStart"
            value-format="yyyy"
          >
          </el-date-picker>
          <span class="mx-1">至</span>
          <el-date-picker
            v-model="queryParams.time[1]"
            type="year"
            placeholder="结束年份"
            :pickerOptions="pickerOptionsEnd"
            value-format="yyyy"
          >
          </el-date-picker>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <ShowMoney :moneyList="totalFinancialist" :money="totalFinancia" />

    <el-button
      type="warning"
      plain
      class="mb-2"
      icon="el-icon-download"
      size="mini"
      @click="handleExport"
      >导出</el-button
    >
    <MyTable :columns="columns" :source="configList" :queryParams="queryParams">
      <template #h_shouru="{ record }">
        收入（借方金额）
        <el-tooltip
          class="item"
          effect="dark"
          content="银行存款科目的借方金额"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>
      <template #h_zhichu="{ record }">
        支出（贷方金额）
        <el-tooltip
          class="item"
          effect="dark"
          content="银行存款科目的贷方金额"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>
      <template #h_yue="{ record }">
        余额
        <el-tooltip
          class="item"
          effect="dark"
          content="余额 = 期初余额 + 收入 - 支出"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>
      <template #h_shijiyue="{ record }">
        实际余额
        <el-tooltip
          class="item"
          effect="dark"
          content="银行存款科目的借方金额"
          placement="top-start"
        >
          <span class="relative bottom-1">①</span>
        </el-tooltip>
      </template>
      <template #opertion="{ record }">
        <div class="flex justify-between">
          <el-button type="text" @click="openDialog(record, 'view')"
            >查看明细</el-button
          >
          <el-button type="text" @click="openDialog(record, 'removeSettings')"
            >剔除设置</el-button
          >
          <el-button type="text" @click="openDialog(record, 'removeDetails')"
            >剔除明细</el-button
          >
        </div>
      </template>
    </MyTable>
    <DetailDialog
      v-model="openDialogDeail"
      :detail="detail"
    ></DetailDialog>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getAllAccountSetsList } from "@/api/oa/voucharRules";
import { projectParameter } from "@/api/businessInformation/productInformation";
import ShowMoney from "../../components/showMoney";
import ShowHeader from "../../components/showHeader";
import DetailDialog from "./components/DetailDialog";

import config from "./components/config";
import XEUtils from "xe-utils";
import form from "@/views/financialInquiry/mixin/form";

export default {
  name: "FinancialStatistics",
  components: { ShowMoney, ShowHeader, DetailDialog },
  mixins: [form],
  data() {
    return {
      ...config,
      totalFinancialist: [
        { label: "收入合计", value: "totalshouru" },
        { label: "支出合计", value: "totalzhichu" },
        { label: "余额合计", value: "yue" },
        { label: "实际余额合计", value: "shijiyue" },
      ],
      totalFinancia: {},
      accountSetsList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bankAccount: undefined,
        accountSetsId: undefined,
        time: [],
        timeDimension: "day",
      },
      configList: [],
      total: 0,
      openDialogDeail:false,
      detail:{},
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getAllAccountSetsList();
      this.getMoney();
      this.getDefaultDay();
      this.getList();
    },
    getDefaultDay() {
      this.queryParams.time = [
        `${new Date().getFullYear()}-01-01`,
        XEUtils.toDateString(new Date(), "yyyy-MM-dd"),
      ];
    },
    getDefaultMonth() {
      this.queryParams.time = [
        `${new Date().getFullYear()}-01`,
        XEUtils.toDateString(new Date(), "yyyy-MM"),
      ];
    },
    getDefaultYear() {
      this.queryParams.time = ["2019", `${new Date().getFullYear()}`];
    },
    async getAllAccountSetsList() {
      const data = await getAllAccountSetsList();
      this.accountSetsList = [...data];
    },
    getMoney() {
      this.totalFinancia = {
        totalshouru: "23,456,789.00",
        totalzhichu: "23,456,789.00",
        yue: "23,456,789.00",
        shijiyue: "23,456,789.00",
      };
    },

    async getList() {
      const { rows, total } = await projectParameter(this.queryParams);
      this.configList = rows;
      this.total = total;
    },
    handleTime(value) {
      const obj = {
        day: () => {
          this.getDefaultDay();
        },
        month: () => {
          this.getDefaultMonth();
        },
        year: () => {
          this.getDefaultYear();
        },
      };
      obj[value]();
      this.handleQuery();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    openDialog(record, type) {
      this.detail={...record,type};
      this.openDialogDeail=true;
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleExport() {
      this.download(
        "personnel/onboarding/export",
        {
          ...this.queryParams,
        },
        `资金收支统计.xlsx`
      );
    },
  },
};
</script>
