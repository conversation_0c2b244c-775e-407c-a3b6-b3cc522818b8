<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="echartsDetail.title"
      :visible.sync="innerValue"
      @open="handelOpen"
      width="800px"
    >
      <MyEchart
      class="m-auto"
        :opationDate="opationDate"
        style="width: 600px; height: 400px"
      ></MyEchart>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";

export default {
  name: "DetailDialog",

  mixins: [vModelMixin],
  props: {
    echartsDetail: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      opationDate: {},
    };
  },
  watch: {},
  mounted() {},
  methods: {
    async init() {},
    handelOpen() {
      this.getOpationDate();
    },
    getOpationDate() {
      const xData = [
        "2024年1月",
        "2024年2月",
        "2024年3月",
        "2024年4月",
        "2024年5月",
        "2024年6月",
        "2024年7月",
      ];
      const rateList = [
        10345600, 12356700, 12378900, 12345600, 12356700, 12378900, 12379900,
      ];
     
      this.opationDate = {
        tooltip: {
          trigger: "axis",
        },
        toolbox: {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ["line", "bar"] },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
     
        xAxis: [
          {
            type: "category",
            data: xData,
            axisPointer: {
              type: "shadow",
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "比率",
          },
        ],
        series: [
          {
            name: "销售净利率",
            type: "line",
            data: rateList,
          },
         
        ],
      };
    },
  },
};
</script>
