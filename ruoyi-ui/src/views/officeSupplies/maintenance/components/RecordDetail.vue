<template>
  <div>
    <el-dialog title="物品修改详情" :visible.sync="visible" width="800px" append-to-body :before-close="close">
      <el-row :gutter="20">
        <!-- 左侧信息 -->
        <el-col :span="12">
          <div class="detail-section">
            <div class="detail-item">
              <span class="label">物品名称：</span>
              <span class="value">{{ newData.itemName }}</span>
              <div class="original-value">
                原物品名称：{{ oldData.itemName }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">物品类型：</span>
              <span class="value">{{ newData.itemType == 'LP' ? '礼品' : '办公用品' }}</span>
              <div class="original-value">
                原物品类型：{{ oldData.itemType == 'LP' ? '礼品' : '办公用品' }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">型号/规格：</span>
              <span class="value">{{ newData.specification }}</span>
              <div class="original-value">
                原型号/规格：{{ oldData.specification }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">计量单位：</span>
              <span class="value">{{ newData.measureUnit }}</span>
              <div class="original-value">
                原计量单位：{{ oldData.measureUnit }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">保质期：</span>
              <span class="value">{{ newData.expirationDate }}</span>
              <div class="original-value">
                原保质期：{{ oldData.expirationDate }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">负库存申请：</span>
              <span class="value">{{
                newData.negativeInventory == 0 ? "允许" : "禁止"
              }}</span>
              <div class="original-value">
                原负库存申请：{{
                  oldData.negativeInventory == 0 ? "允许" : "禁止"
                }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">通知形式：</span>
              <span class="value">{{
                newData.notifyType == 3
                  ? "系统代办通知、企业微信通知"
                  : newData.notifyType == 0
                    ? "系统代办通知"
                    : newData.notifyType == 1 ? "企业微信通知" : ''
              }}</span>
              <div class="original-value">
                原通知形式：{{
                  oldData.notifyType == 3
                    ? "系统代办通知、企业微信通知"
                    : newData.notifyType == 0
                      ? "系统代办通知"
                      : newData.notifyType == 1 ? "企业微信通知" : ''
                }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">物品图片：</span>
              <span class="value"><el-button type="text" @click="handlePreview(newData.supplyPicture)">{{
                newData.supplyPicture && newData.supplyPicture.fileName
                  ? newData.supplyPicture.fileName
                  : "" }}</el-button></span>
              <div class="original-value">
                原物品图片：<el-button type="text" @click="handlePreview(oldData.supplyPicture)">{{
                  oldData.supplyPicture && oldData.supplyPicture.fileName
                    ? oldData.supplyPicture.fileName
                    : ""
                }}</el-button>
              </div>
            </div>
            <div class="detail-item">
              <span class="label">物品附件：</span>
              <span class="value" v-if="newData.supplyFiles && newData.supplyFiles.length > 0">
                <el-button type="text" @click="handlePreview(item)" v-for="(item, index) in newData.supplyFiles"
                  :key="index">{{ item.fileName
                  }}</el-button>
              </span>
              <div class="original-value">
                原物品附件：
                <br />
                <span v-if="oldData.supplyFiles && oldData.supplyFiles.length > 0">
                  <el-button @click="handlePreview(item)" type="text" v-for="(item, index) in oldData.supplyFiles"
                    :key="index">{{ item.fileName
                    }}</el-button>
                </span>
              </div>
            </div>
            <div class="detail-item">
              <span class="label">备注：</span>
              <span class="value">{{ newData.remark }}</span>
              <div class="original-value">原备注：{{ oldData.remark }}</div>
            </div>
          </div>
        </el-col>

        <!-- 右侧信息 -->
        <el-col :span="12">
          <div class="detail-section">
            <div class="detail-item">
              <span class="label">所属类别：</span>
              <span class="value">{{ newData.categoryName }}</span>
              <div class="original-value">
                原所属类别：{{ oldData.categoryName }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">系统编号：</span>
              <span class="value">{{ newData.sysCode }}</span>
              <div class="original-value">
                原系统编号：{{ oldData.sysCode }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">数量：</span>
              <span class="value">{{ newData.amount }}</span>
              <div class="original-value">原数量：{{ oldData.amount }}</div>
            </div>
            <div class="detail-item">
              <span class="label">到期提醒：</span>
              <span class="value">{{
                newData.isUseNotify ? "开启" : "关闭"
              }}</span>
              <div class="original-value">
                原到期提醒：{{ oldData.isUseNotify ? "开启" : "关闭" }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">启用状态：</span>
              <span class="value">{{ newData.status ? "启用" : "停用" }}</span>
              <div class="original-value">
                原启用状态：{{ oldData.status ? "启用" : "停用" }}
              </div>
            </div>
            <div class="detail-item">
              <span class="label">库存警示阈值：</span>
              <span class="value">{{ newData.itemWarning }}</span>
              <div class="original-value">
                原库存警示阈值：{{ oldData.itemWarning }}
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-divider></el-divider>
      <div class="reason-section">
        <div class="reason-item">
          <span class="label">修改原因：</span>
          <span class="value">{{ newData.updateRemark }}</span>
        </div>
      </div>
    </el-dialog>
    <el-image ref="previewImg" v-show="false" :src="photoUrl" :preview-src-list="imagePreviewUrls"></el-image>
  </div>
</template>

<script>
import { downloadByUrl } from "@/api/oa/processTemplate";
import { getFilesPathMapping } from "@/api/cdlb/files";
export default {
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return { visible: true, oldData: null, newData: null, photoUrl: '', imagePreviewUrls: [] };
  },
  mounted() {
    this.oldData = JSON.parse(this.item.oldJson);
    this.newData = JSON.parse(this.item.newJson);
    console.log(this.newData.updateRemark);
    console.log(this.oldData);
  },
  methods: {
    handlePreview(file) {
      console.log(file);
      if (file.hasOwnProperty("fileName")) {
        file.name = file.fileName || file.name;
      }
      if (file.name.endsWith(".pdf") || file.name.endsWith(".html")) {
        //文件是pdf格式
        getFilesPathMapping().then((resp) => {
          let url = resp.msg + file.filePath
          window.open(url);
          return;
        });
        return;
      } else if (
        file.name.endsWith(".jpg") ||
        file.name.endsWith(".jpeg") ||
        file.name.endsWith(".png") ||
        file.name.endsWith(".gif")
      ) {
        //文件是图片格式
        getFilesPathMapping().then((resp) => {
          this.photoUrl = resp.msg + file.filePath

          let array = new Set([]);
          array.add(this.photoUrl);
          let from = Array.from(array);
          this.imagePreviewUrls = from;
          this.$refs.previewImg.showViewer = true;
        });
        // this.showImgViewer = true;
      } else {
        //文件下载
        this.handleDownload(file);
      }
    },

    handleDownload(file) {
      if (file.type) {
        this.downFile(file.url, file.fileName);
      } else {
        if (file.hasOwnProperty("fileName")) {
          file.name = file.fileName;
        }
        const url = file.url || file.filePath; //图片的https链接
        downloadByUrl({
          url: url,
        }).then((res) => {
          let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
          const link = document.createElement("a"); //创建一个隐藏的a标签
          link.target = "_blank";
          link.href = href; //设置下载的url
          link.download = file.name; //设置下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(href); // 释放掉blob对象
        });
      }
    },
    close() {
      this.visible = false;
      this.$emit("close");
    },
  },
};
</script>
<style lang="less" scoped>
.detail-section {
  border-right: 1px solid #eee;
  padding-right: 20px;

  &:last-child {
    border-right: none;
  }
}

.detail-item {
  margin-bottom: 15px;

  label {
    display: block;
    color: #333;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .original-value {
    color: #999;
    font-size: 12px;
    margin-top: 3px;
  }
}

// 新增样式
.reason-section {
  margin: 20px 0;
  width: 100%;

  .reason-item {
    display: flex;
    align-items: center;

    .label {
      width: 100px;
      color: #333;
      font-weight: 500;
      text-align: right;
      padding-right: 10px;
    }

    .value {
      flex: 1;
      color: #666;
    }
  }
}
</style>
