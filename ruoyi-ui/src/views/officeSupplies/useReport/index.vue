<template>
    <div class="app-container">
        <!-- 搜索栏 -->
        <el-form :model="queryParams" ref="queryForm" :inline="true">
            <el-form-item label="所属公司" prop="companyId">
                <el-select v-model="queryParams.companyId" placeholder="请选择所属公司" clearable style="width: 200px">
                    <el-option v-for="item in projects" :key="item.id" :label="item.companyShortName"
                        :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="领用时间" prop="dateRange">
                <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" />
            </el-form-item>
            <el-form-item label="物品名称" prop="itemName">
                <el-input v-model="queryParams.itemName" placeholder="请输入物品名称" clearable style="width: 200px" />
            </el-form-item>
            <el-form-item label="领用人" prop="receiveUserName">
                <el-input v-model="queryParams.receiveUserName" placeholder="请输入领用人" clearable style="width: 200px" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-divider />

        <!-- 操作按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" icon="el-icon-download" @click="handleExport"
                    v-hasPermi="['useReport:export']">导出报表</el-button>
            </el-col>
        </el-row>

        <!-- 表格数据 -->
        <el-table v-loading="loading" :data="list" :span-method="objectSpanMethod" border>
            <el-table-column label="所属公司" align="center" prop="companyShortName" />
            <el-table-column label="物品名称" align="center" prop="itemName">
                <template slot-scope="scope">
                    <el-button type="text" @click="todetail(scope.row)">{{ scope.row.itemName }}</el-button>
                </template>
            </el-table-column>
            <el-table-column label="库存数量" align="center" prop="amount">
                <template slot-scope="scope">
                    {{ scope.row.amount }}
                    <br>
                    <span v-if="scope.row.approvalStatus == 1" style="color: #f79a23;">存在{{ scope.row.applyNum
                        }}个审核中的库存</span>
                </template>
            </el-table-column>
            <el-table-column label="计量单位" align="center" prop="measureUnit" />
            <el-table-column label="领用人" align="center" prop="recipientUser" />
            <el-table-column label="领用时间" align="center" prop="approvalTime" width="180">
                <template slot-scope="scope">
                    <span v-if="scope.row.approvalStatus == 1" style="color: #f79a23;">审核中</span>
                    <span v-else>{{ scope.row.approvalTime }}</span>
                </template>
            </el-table-column>
            <el-table-column label="领用数量" align="center" prop="applyNum" />
            <el-table-column label="领用后剩余数量" align="center" prop="residueNum" />
            <el-table-column label="申请事由" align="center" prop="cause" />
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :page-sizes="[10, 20, 50, 100]"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script>
import {
    supplyReceiveReport,
    getAuthCompany
} from "@/api/officeSupplies/officeSupplies";
export default {
    name: 'UseReport',
    data() {
        return {
            projects: [],
            spanMap: [],// 存储合并规则,
            // 遮罩层
            loading: true,
            // 总条数
            total: 0,
            // 表格数据
            list: [],
            // 公司下拉选项
            companyOptions: [
                { value: '1', label: '总公司' },
                { value: '2', label: '分公司A' },
                { value: '3', label: '分公司B' }
            ],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                companyId: undefined,
                itemName: undefined,
                receiveUserName: undefined,
                dateRange: [],
                receiveBeginTime: '',
                receiveEndTime: ''
            }
        }
    },
    created() {
        getAuthCompany().then((res) => {
            // this.treeSelect = res.data.dept;
            this.projects = res.rows
        });
        this.getList()
    },
    methods: {
        todetail(v) {
            this.$router.push({ path: '/officeSuppliesOther/maintenanceDetail', query: { id: v.itemId } })
        },
        generateSpanMap(data) {
            this.spanMap = []
            let pos = 0
            let prevKey = ''

            data.forEach((item, index) => {
                // 添加空值处理
                const currentKey = `${item.companyShortName || ''}-${item.itemName || ''}`
                if (index === 0) {
                    prevKey = currentKey
                    this.spanMap.push(1)
                    pos = 0
                } else {
                    if (currentKey === prevKey) {
                        this.spanMap[pos] += 1
                        this.spanMap.push(0)
                    } else {
                        this.spanMap.push(1)
                        prevKey = currentKey
                        pos = index  // 此处需要修正为当前索引
                    }
                }
            })
        },
        /** 查询列表 */
        getList() {
            this.loading = true
            if (this.queryParams.dateRange && this.queryParams.dateRange.length > 0) {
                this.queryParams.receiveBeginTime = this.queryParams.dateRange[0]
                this.queryParams.receiveEndTime = this.queryParams.dateRange[1]
            } else {
                this.queryParams.receiveBeginTime = ''
                this.queryParams.receiveEndTime = ''
            }
            supplyReceiveReport(this.queryParams).then(response => {
                // 添加排序逻辑
                // 添加排序逻辑
                this.list = response.rows.sort((a, b) => {
                    const keyA = `${a.companyShortName}-${a.itemName}`
                    const keyB = `${b.companyShortName}-${b.itemName}`
                    return keyA.localeCompare(keyB)
                })
                this.total = response.total
                this.loading = false
                this.generateSpanMap(this.list)
            })
                .catch(() => {
                    this.loading = false
                })

        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            let data = this.list; //拿到当前table中数据
            let cellValue = row[column.property]; //当前位置的值
            let SortArr = ["companyShortName"];
            if (cellValue && SortArr.includes(column.property)) {
                let prevRow = data[rowIndex - 1]; //获取到上一条数据
                let nextRow = data[rowIndex + 1]; //下一条数据
                if (prevRow && prevRow[column.property] === cellValue) {
                    //当有上一条数据，并且和当前值相等时
                    return { rowspan: 0, colspan: 0 };
                } else {
                    let countRowspan = 1;
                    while (nextRow && nextRow[column.property] === cellValue) {
                        //当有下一条数据并且和当前值相等时,获取新的下一条
                        nextRow = data[++countRowspan + rowIndex];
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 };
                    }
                }
            }
            let SortArrId = ["itemName", "amount", "measureUnit"];
            if (cellValue && SortArrId.includes(column.property)) {
                let prevRow = data[rowIndex - 1]; //获取到上一条数据
                let nextRow = data[rowIndex + 1]; //下一条数据
                if (prevRow && prevRow["itemId"] === row["itemId"]) {
                    //当有上一条数据，并且informationId和当前值相等时
                    return { rowspan: 0, colspan: 0 };
                } else {
                    let countRowspan = 1;
                    while (nextRow && nextRow["itemId"] === row["itemId"]) {
                        //当有下一条数据并且informationId和当前值相等时,获取新的下一条
                        nextRow = data[++countRowspan + rowIndex];
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 };
                    }
                }
            }

        },



        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.queryForm.resetFields()
            this.handleQuery()
        },
        handleExport() {
            this.download(
                "/offReceiveMain/receiveMain/exportSupplyReceiveReport",
                {
                    ...this.queryParams,
                },
                `物品领用报表_${new Date().getTime()}.xlsx`
            );
        },


    }
}
</script>
