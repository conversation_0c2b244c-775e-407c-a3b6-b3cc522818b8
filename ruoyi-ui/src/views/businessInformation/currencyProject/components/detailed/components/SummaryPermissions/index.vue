<template>
  <div>
    <p class="mb-0">
      本页面展示您在智慧平台中已拥有数据权限汇总，它们来自于您的上级用户对您的授权
    </p>
    <p class="mb-0">只要您在某个功能模块拥有该项目的权限，则会在此展示</p>

    <p class="mb-0">
      用户只能为自己的下级用户授权，点击 [个人中心]
      可查看本人上下级关系，及已拥有权限
    </p>
    <div class="mt-3">
      项目名称：
      <el-input
        class="mr-3"
        v-model="queryParams.queryName"
        style="width: 200px"
        placeholder="请输入项目名称"
      ></el-input>
      担保公司：
      <el-select
        class="mr-3"
        v-model="queryParams.custId"
        style="width: 200px"
        placeholder="全部"
      >
        <el-option
          v-for="item in companyList"
          :key="item.id"
          :label="item.companyName"
          :value="item.id"
        ></el-option>
      </el-select>
      <el-button icon="el-icon-search" type="primary" @click="getList()"
        >搜索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
    </div>
    <div class="mt-3">
      <!-- <span class="mr-6">
        <el-button
          size="mini"
          v-hasPermi="['currencyProject:allAuth']"
          type="primary"
          @click="changeAuthType"
          >批量授权</el-button
        >
      </span> -->
      <el-switch class="mr-3" v-model="changeUserType" @change="getList">
      </el-switch
      >仅看直属下级
      <el-switch class="mr-3 ml-8" v-model="changeType" @change="getList">
      </el-switch
      >未对他人分配权限的公司
    </div>

    <MyTable
      class="mt-3"
      v-show="userList.length > 0"
      :columns="dataColumns"
      :queryParams="queryParams"
      :source="dataList"
      :showIndex="true"
    >
      <template v-slot:h_authorizedUserList="">
        <div>
          授权用户
          <el-tooltip
            class="item"
            effect="dark"
            content="仅显示您和您已授权的下级用户，不属于您的下级用户不在此显示"
            placement="top"
          >
            <i class="el-icon-info"></i>
          </el-tooltip>
        </div>
      </template>
      <template #authorizedUserList="{ record }">
        <div
          class="item_div"
          v-for="(item, i) in record.authorizedUserList"
          :key="i"
        >
          <span
            class="user"
            @click="getUserData(item.authorizedUserId,item)"
            :style="{
              background:
                item.authorizedUserIsCurrentUserFlag != 1 ||
                item.authorizedUserHaveAllPermissionFlag == 1 ||
                item.authorizedUserHaveCompanyPermissionFlag == 1
                  ? '#f2f2f2'
                  : '',
            }"
          >
            {{ item.authorizedUserId | user
            }}<span style="color: #cccccc">{{
              item.authorizedUserId | userStatus
            }}</span
            ><i
              @click.stop="delUser(record, item)"
              class="el-icon-close"
              v-if="
                item.authorizedUserIsCurrentUserFlag == 1 &&
                item.authorizedUserHaveAllPermissionFlag != 1 &&
                item.authorizedUserHaveCompanyPermissionFlag != 1
              "
            ></i>
          </span>
        </div>
      </template>
      <template #userName="{ record }">
        <div
          class="item_div"
          v-for="(item, i) in record.authorizedUserList"
          :key="i"
        >
          <span>
            {{ item.authorizedUserId | userName }}
          </span>
        </div>
      </template>
      <template #userPermisson="{ record }">
        <div
          style="overflow: hidden; white-space: nowrap; overflow-x: auto"
          class="item_div"
          v-for="(item, i) in record.authorizedUserList"
          :key="i"
        >
          <span
            v-for="v in item.authorizedModuleFeatureList"
            :key="v"
            :style="{
              background:
                item.authorizedUserIsCurrentUserFlag != 1 ||
                item.authorizedUserHaveAllPermissionFlag == 1 ||
                item.authorizedUserHaveCompanyPermissionFlag == 1
                  ? '#f2f2f2'
                  : '',
              color: '#333',
            }"
            class="user mt-2"
            >{{ v | authCode }}
            <i
              @click="delPermisson(record, item, v)"
              class="el-icon-close"
              v-if="
                item.authorizedUserIsCurrentUserFlag == 1 &&
                item.authorizedUserHaveAllPermissionFlag != 1 &&
                item.authorizedUserHaveCompanyPermissionFlag != 1
              "
            ></i
          ></span>
        </div>
      </template>
      <template #opertion="{ record }">
        <div
          class="item_div"
          v-for="(item, i) in record.authorizedUserList"
          :key="i"
        >
          <el-button
            type="text"
            v-hasPermi="['currencyProject:addPerson']"
            v-if="item.authorizedUserIsCurrentUserFlag == 0"
            @click="addUser(record)"
            >+ 添加用户</el-button
          >
          <el-button
            v-hasPermi="['currencyProject:cencelAllAuth']"
            v-if="
              item.authorizedUserIsCurrentUserFlag == 1 &&
              item.authorizedUserHaveAllPermissionFlag != 1 &&
              item.authorizedUserHaveCompanyPermissionFlag != 1
            "
            type="text"
            @click="delAllUser(record, item)"
            >- 取消所有授权</el-button
          >
        </div>
      </template>
    </MyTable>

    <pagination
      v-show="queryParams.total > 0"
      :total="queryParams.total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <AddingUsers
      :subordinateList="subordinateList"
      v-if="addingUsersType"
      @close="addingUsersType = false"
      @submit="submitAddUser"
    />
    <AuthMethod
      v-if="authMethodType"
      @close="authMethodType = false"
      @submitMeth="submitMeth"
      type="proj"
    />
    <SelectAuthTime
      @submit="submitDate"
      v-if="selectAuthTimeTpye"
      @close="selectAuthTimeTpye = false"
    />
    <SelectDelAuth
      :selectUserName="selectUserName"
      v-if="selectDelAuthType"
      @submit="delAllAuth"
      @close="selectDelAuthType = false"
    />
    <UserDetail2
      :userId="userId"
      :authType="authTypeName"
      v-if="userDetailType"
      @close="userDetailType = false"
    />
  </div>
</template>
<script>
import { companyList } from "@/api/businessInformation/companyInformation";
import { getDicts } from "@/api/system/dict/data";
import SelectDelAuth from "./SelectDelAuth.vue";
import {
  newAuthority,
  getUserListAll,
  subordinate,
  newAuthTemp,
  queryCancelUser,
  cancelAuthorization,
  queryAllCancelUser,
  cancelAllAuthorization,
} from "@/api/businessInformation/currencyCompany";
let that = "";
export default {
  components: {
    SelectDelAuth,
  },
  props: {
    code: String,
    tabsList: Array,
  },
  data() {
    return {
      userId: "",
      userDetailType: false,
      companyList: [],
      authTypeName:'',
      selectDelAuthType: false,
      changeUserType: false,
      subordinateList: [],
      userList: [],
      selectAuthTimeTpye: false,
      allSelectType: false,
      showCheckbox: false,
      //批量授权状态
      authType: false,
      queryParams: {
        pageNum: 1,
        custId: null,
        pageSize: 10,
        total: 0,
        queryType: 1,
        queryName: "",
        queryCode: "ALL",
        queryThirdType: 1,
        unassignedCompaniesFlag: 0,
        subordinateFlag: 0,
      },
      dataList: [],
      dataColumns: [
        {
          label: "项目名称",
          prop: "responseName",
          width: "200",
        },

        {
          label: "授权用户",
          prop: "authorizedUserList",
          key: "authorizedUserList",
          width: "170",
          isHSlot: true,
        },
        {
          label: "账号",
          key: "userName",
          width: "120",
        },
        {
          label: "有权限的功能模块",
          key: "userPermisson",
          width: "400",
        },
        {
          label: "操作",
          key: "opertion",
          width: "200",
        },
      ],
      companyType: "1",
      changeType: false,
      authMethodType: false,
      addingUsersType: false,

      openSelect: false,
      selectList: [],

      //添加用户选择授权人
      selectAuthUsers: [],
      //添加用户选择的模板
      selectAuthMethList: [],

      selectUserName: null,
      selectItemData: null,
      selectUserData: null,
    };
  },
  created() {
    that = this;
    this.getUser();
  },
  watch: {
   
   "$store.state.principalId": {
     handler(newval, oldval) {
       console.log(newval);
       this.init()
     },
     deep: true,
   },
 },
  mounted() {
    setTimeout(() => {
      console.log(this.tabsList);
    }, 1000);
    this.init();
  },
  filters: {
    authCode(e) {
      if (that.tabsList && that.tabsList.length > 0) {
        let data = that.tabsList.find((item) => {
          return item.code == e;
        });

        return data.info || "";
      }
    },
    user(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
       if(data){
        return data.nickName;
      }
    },
    userName(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
       if(data){
 return data.userName;
}
     
    },
    userStatus(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data&&data.status == 0) {
        return "";
      } else {
        return "（已停用）";
      }
    },
  },
  methods: {
    getUserData(e,v) {
      if (v.authorizedUserHaveAllPermissionFlag == 1) {
        this.authTypeName = "所有";
      }
      if (
        !v.authorizedUserHaveAllPermissionFlag &&
        !v.authorizedUserHaveCompanyPermissionFlag
      ) {
        this.authTypeName = "项目";
      }
      if (v.authorizedUserHaveCompanyPermissionFlag == 1) {
        this.authTypeName = "公司";
      }
      console.log(e);
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      console.log(data);
      this.userId = data.userName;
      this.userDetailType = true;
    },
    reset() {
      this.changeType = false;
      this.changeUserType = false;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        queryType: 1,
        queryName: "",
        custId: null,
        queryCode: "ALL",
        queryThirdType: 1,
        unassignedCompaniesFlag: 0,
        subordinateFlag: 0,
      };
      this.getList();
    },
    toTemp() {
      this.$router.push({
        path: "/businessInformationOther/authTemplate",
        query: {
          type: "proj",
        },
      });
    },
    getNickName(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
       if(data){
        return data.nickName;
      }
    },
    getPermissonName(e) {
      let data = this.tabsList.find((item) => {
        return item.code == e;
      });
      return data.info;
    },
    delAllUser(row, v) {
      this.selectItemData = row;
      this.selectUserData = v;
      this.selectUserName = this.getNickName(v.authorizedUserId);
      this.selectDelAuthType = true;
    },
    delAllAuth(v) {
      let params = {
        id: this.selectItemData.id,
        businessType: 1,
        businessCode: "ALL",
        unAuthorizedUserId: this.selectUserData.authorizedUserId,
        unAuthorizedType: v,
      };
      queryAllCancelUser({ ...params,principalId:this.$store.state.principalId }).then((res) => {
        if (res.msg == 0) {
          if (v == 1) {
            this.$confirm(
              `是否取消您对[${this.getNickName(
                this.selectUserData.authorizedUserId
              )}]在项目维度的所有授权？此操作不影响他人对 [${this.getNickName(
                this.selectUserData.authorizedUserId
              )}] 的授权，点击确定后，取消授权将立即生效！`,
              "取消项目维度的所有授权",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            ).then(() => {
              this.cancelAllAuthorization(params);
            });
          } else {
            this.$confirm(
              `是否取消您对[${this.getNickName(
                this.selectUserData.authorizedUserId
              )}]在所有维度（公司、项目、部门、数据范围等）的所有授权？此操作不影响他人对 [${this.getNickName(
                this.selectUserData.authorizedUserId
              )}] 的授权，点击确定后，取消授权将立即生效！`,
              "取消所有维度的所有授权",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            ).then(() => {
              this.cancelAllAuthorization(params);
            });
          }
        } else {
          if (v == 1) {
            this.$confirm(
              `是否取消您对[${this.getNickName(
                this.selectUserData.authorizedUserId
              )}]在项目维度的所有授权？[${this.getNickName(
                this.selectUserData.authorizedUserId
              )}] 已经将部分权限授权于其下级用户，取消后其下级用户也将失去此权限。此操作不影响他人对 [${this.getNickName(
                this.selectUserData.authorizedUserId
              )}] 的授权，点击确定后，取消授权将立即生效！`,
              "取消项目维度的所有授权",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            ).then(() => {
              this.cancelAllAuthorization(params);
            });
          } else {
            this.$confirm(
              `是否取消您对[${this.getNickName(
                this.selectUserData.authorizedUserId
              )}]在所有维度（公司、项目、部门、数据范围等）的所有授权？[${this.getNickName(
                this.selectUserData.authorizedUserId
              )}] 已经将部分权限授权于其下级用户，取消后其下级用户也将失去此权限。此操作不影响他人对 [${this.getNickName(
                this.selectUserData.authorizedUserId
              )}] 的授权，点击确定后，取消授权将立即生效！`,
              "取消所有维度的所有授权",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            ).then(() => {
              this.cancelAllAuthorization(params);
            });
          }
        }
      });
    },
    delPermisson(row, v, i) {
      console.log(row, v, i);
      let params = {
        id: row.id,
        authorizedType: 1,
        authorizedCode: "ALL",
        unAuthorizedUserId: v.authorizedUserId,
        authorizedModule: i,
      };
      queryCancelUser({ ...params,principalId:this.$store.state.principalId }).then((res) => {
        if (res.msg == 0) {
          this.$confirm(
            `是否取消[${this.getNickName(
              v.authorizedUserId
            )}]在[${this.getPermissonName(
              i
            )}]模块的授权？点击确定后，取消授权将立即生效！`,
            "取消授权",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            this.cancelAuthorization(params);
          });
        } else if (res.msg > 0) {
          this.$confirm(
            `是否取消[${this.getNickName(
              v.authorizedUserId
            )}]在[${this.getPermissonName(i)}]模块的授权？ [${this.getNickName(
              i.authorizedUserId
            )}]已经将本模块权限授权于其下级用户，取消后其下级用户也将失去此权限,点击确定后，取消授权将立即生效！ `,
            "取消授权",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            this.cancelAuthorization(params);
          });
        }
      });
    },
    cancelAllAuthorization(e) {
      cancelAllAuthorization({ ...e,principalId:this.$store.state.principalId }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.selectDelAuthType = false;
          this.getList();
        }
      });
    },
    cancelAuthorization(e) {
      cancelAuthorization({ ...e,principalId:this.$store.state.principalId }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.getList();
        }
      });
    },
    delUser(v, i) {
      console.log(v, i);
      let params = {
        id: v.id,
        authorizedType: 1,
        authorizedCode: "ALL",
        unAuthorizedUserId: i.authorizedUserId,
      };
      queryCancelUser({ ...params,principalId:this.$store.state.principalId }).then((res) => {
        if (res.msg == 0) {
          this.$confirm(
            `是否取消[${this.getNickName(
              i.authorizedUserId
            )}]在本项目所有模块的授权？点击确定后，取消授权将立即生效！`,
            "取消授权",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            this.cancelAuthorization(params);
          });
        } else if (res.msg > 0) {
          this.$confirm(
            `是否取消[${this.getNickName(
              i.authorizedUserId
            )}]在本项目所有模块的授权？ [${this.getNickName(
              i.authorizedUserId
            )}]已经将本项目部分权限授权于其下级用户，取消后其下级用户也将失去此权限,点击确定后，取消授权将立即生效！ `,
            "取消授权",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            this.cancelAuthorization(params);
          });
        }
      });
    },
    submitDate(e) {
      let data = {
        id: this.selectAuthMethList[0].id,
        authDate: e ? this.$format(e, "yyyy-MM-dd 23:59:59") : null,
        authType: "proj",
        authId: !this.authType
          ? [this.selectItemData.id]
          : this.selectList.map((item) => item.id),
        authUserIds: this.selectAuthUsers.map((item) => item.userId),
        agencyUserId: this.$store.state.principalId,
      };
      console.log(data);
      newAuthTemp({ ...data }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.getList();
          this.selectAuthTimeTpye = false;
          this.authMethodType = false;
          this.addingUsersType = false;
        }
      });
    },
    submitMeth(e) {
      console.log(e);
      this.selectAuthMethList = e;
      this.selectAuthTimeTpye = true;
    },
    submitAddUser(e) {
      console.log(e);
      this.selectAuthUsers = e;
      this.authMethodType = true;
    },
    allSelect() {},
    tableSelect(e) {
      console.log(e);
      this.selectList = [...e];
    },
    changeAuthType() {
      this.$emit("authType");
    },

    init() {
      this.getCompany();
      this.getList();
    },
    getCompany() {
      getDicts("company_type").then((res) => {
        let data = res.data.find((item) => {
          return item.dictLabel == "担保公司";
        });
        companyList({ companyTypeCode: data.dictCode }).then((res) => {
          this.companyList = res.rows;
        });
      });
    },
    getList() {
      this.queryParams.subordinateFlag = this.changeUserType ? 1 : 0;
      this.queryParams.unassignedCompaniesFlag = this.changeType ? 1 : 0;
      newAuthority({ ...this.queryParams,queryUserId:this.$store.state.principalId }).then((res) => {
        if (res.code == 200) {
          this.dataList = res.rows;
          this.queryParams.total = res.total;
        }
      });
    },
    addMostTemp() {
      subordinate({principalId:this.$store.state.principalId}).then((res) => {
        if (res.code == 200) {
          if (res.data.subordinateList && res.data.subordinateList.length > 0) {
            this.subordinateList = res.data;
            this.addingUsersType = true;
          } else {
            this.$message.warning("您没有下级用户，无法添加用户给他人授权！");
          }
        }
      });
    },
    addUser(e) {
      console.log(e);
      this.selectItemData = e;
      subordinate({principalId:this.$store.state.principalId}).then((res) => {
        if (res.code == 200) {
          if (res.data.subordinateList && res.data.subordinateList.length > 0) {
            this.subordinateList = res.data;
            this.addingUsersType = true;
          } else {
            this.$message.warning("您没有下级用户，无法添加用户给他人授权！");
          }
        }
      });
    },
    getUser() {
      getUserListAll().then((res) => {
        if (res.code == 200) {
          this.userList = res.data;
        }
      });
    },
    submitDelet(e) {
      console.log(e);
      this.selectList.forEach((item, index) => {
        e.forEach((i) => {
          if (item.id == i) {
            this.selectList.splice(index, 1);
            console.log(this.selectList);
          }
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.item_div {
  height: 58px;
  line-height: 52px;
  border-bottom: 1px solid #e6ebf5;
}
.user {
  display: inline-block;
  height: 25px;
  line-height: 25px;
  border: 1px solid #cccccc;
  cursor: pointer;
  padding: 0 10px;
  color: #409eff;
  border-radius: 4px;
  margin-right: 10px;
  i {
    color: #666666;
    margin-left: 5px;
    cursor: pointer;
  }
}
/deep/ .el-table .cell {
  padding: 0 !important;
  margin: 0 !important;
}
/deep/ .el-table .el-table__cell {
  padding: 0 !important;
}
</style>
