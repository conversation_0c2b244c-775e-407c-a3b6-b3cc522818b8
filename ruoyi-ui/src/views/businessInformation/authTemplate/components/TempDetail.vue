<template>
  <div>
    <el-dialog
      title="模板详情"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <p>
        <span class="font-blod">模板名称：</span>{{ detailData.templateName }}
      </p>
      <p>
        <span class="font-blod">公司分类：</span>{{ detail.companyTypeName }}
      </p>
      <p>
        <span class="font-blod">职能分类：</span>{{ detail.functionTypeName }}
      </p>
      <p>
        <span class="font-blod">模板说明：</span
        >{{ detailData.templateExplain }}
      </p>
      <p>
        <span class="font-blod">作用范围：</span
        >{{
          detailData.templateType == "unit"
            ? "通用授权-公司"
            : detailData.templateType == "proj"
            ? "通用授权-项目"
            : "通用授权-所有"
        }}
      </p>
      <p><span class="font-blod">授权明细：</span></p>
      <MyTable
        v-if="dataList.length > 0"
        class="mt-3"
        :columns="dataColumns"
        :source="dataList"
      >
        <template #check="{ record }">
          {{ record.check ? record.check : "禁止" }}
        </template>
        <template #word="{ record }">
          {{ record.word ? record.word : " " }}
        </template>
      </MyTable>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTempDetail,
  getTemConfig,
} from "@/api/businessInformation/authTemplate";
export default {
  props: {
    detail: Object,
  },
  data() {
    return {
      detailData: null,
      dataList: [],
      dialogVisible: true,
      dataColumns: [
        {
          label: "模块",
          prop: "moduleName",
          minWidth: "130",
        },
        {
          label: "授权情况",
          key: "check",
        },
        {
          label: "指定功能角色",
          key: "word",
          showOverflowTooltipField: true,
          minWidth: "340",
        },
      ],
    };
  },
  mounted() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      getTempDetail(this.detail.id).then((res) => {
        this.detailData = res.data;
        getTemConfig({
          flag: this.$route.query.type,
          agencyUserId: this.$store.state.principalId,
        }).then((r) => {
          this.dataList = r.data;
          this.dataList.forEach((item) => {
            this.detailData.authTemplateDetailList.forEach((i) => {
              if (item.code == i.moduleType) {
                item.check = "允许";
                if (item.tempCheckData[0].tempCheckData.length > 0) {
                  item.tempCheckData[0].tempCheckData.forEach((c, x) => {
                    if (c.roleCode == i.permissionRule) {
                      item.word = c.choiceDescription;
                    }
                  });
                }
              }
            });
          });
        });
      });
    },

    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>