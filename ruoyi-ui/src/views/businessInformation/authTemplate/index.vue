<template>
  <div class="p-5">
    <p class="mb-0">
      将每个功能模块是否授权的规则设为模板，在授权时直接使用模板，以实现快速配置
    </p>
    <p>
      以下为 [通用授权-{{
        $route.query.type == "all"
          ? "所有"
          : $route.query.type == "unit"
          ? "公司"
          : "项目"
      }}] 模式可用的模板
    </p>
    <div class="mt-3">
      模板名称
      <el-input
        class="mr-3"
        v-model="queryParams.templateName"
        style="width: 200px"
        placeholder="请输入模板名称"
      ></el-input>
      公司分类
      <el-select
        class="mr-3"
        placeholder="请选择"
        v-model="queryParams.companyTypes"
        multiple=""
        clearable=""
        filterable=""
        collapse-tags=""
      >
        <el-option
          v-for="item in functList"
          :key="item.unitCode"
          :value="item.unitCode"
          :label="item.unitAliasName"
        ></el-option
      ></el-select>
      职能分类
      <el-select
        class="mr-3"
        placeholder="请选择"
        v-model="queryParams.functionTypes"
        multiple=""
        clearable=""
        filterable=""
        collapse-tags=""
      >
        <el-option
          v-for="item in unitList"
          :key="item.code"
          :value="item.code"
          :label="item.info"
        ></el-option
      ></el-select>
      <el-button icon="el-icon-search" type="primary" @click="getList"
        >搜索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
    </div>
    <el-button
      style="margin-top: 12px"
      size="mini"
      v-if="$route.query.type == 'proj'"
      v-hasPermi="['authTemplate:addEditProj']"
      type="primary"
      plain
      @click="addTemp"
      >+ 新增模板</el-button
    >
    <el-button
      style="margin-top: 12px"
      size="mini"
      v-if="$route.query.type == 'unit'"
      v-hasPermi="['authTemplate:addEditUnit']"
      type="primary"
      plain
      @click="addTemp"
      >+ 新增模板</el-button
    >
    <el-button
      style="margin-top: 12px"
      size="mini"
      v-if="$route.query.type == 'all'"
      v-hasPermi="['authTemplate:addEditAll']"
      type="primary"
      plain
      @click="addTemp"
      >+ 新增模板</el-button
    >
    <MyTable
      class="mt-3"
      :columns="dataColumns"
      :queryParams="queryParams"
      :source="dataList"
      :showIndex="true"
    >
      <template #authUser="{ record }">
        <span class="user" v-for="(item, index) in record.authUser" :key="index"
          >{{ item.nickName
          }}<i class="el-icon-close" @click="delUser(record, item)"></i
        ></span>
      </template>
      <template #opertion="{ record }">
        <el-button type="text" @click="detail(record)">查看详情</el-button>
        <el-button
          type="text"
          v-if="$route.query.type == 'proj'"
          v-hasPermi="['authTemplate:addEditProj']"
          @click="edit(record)"
          >编辑</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'unit'"
          v-hasPermi="['authTemplate:addEditUnit']"
          @click="edit(record)"
          >编辑</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'all'"
          v-hasPermi="['authTemplate:addEditAll']"
          @click="edit(record)"
          >编辑</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'proj'"
          v-hasPermi="['authTemplate:copyProj']"
          @click="copy(record)"
          >复制</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'unit'"
          v-hasPermi="['authTemplate:copyUnit']"
          @click="copy(record)"
          >复制</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'all'"
          v-hasPermi="['authTemplate:copyAll']"
          @click="copy(record)"
          >复制</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'proj'"
          v-hasPermi="['authTemplate:delProj']"
          @click="del(record)"
          >删除</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'unit'"
          v-hasPermi="['authTemplate:delUnit']"
          @click="del(record)"
          >删除</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'all'"
          v-hasPermi="['authTemplate:delAll']"
          @click="del(record)"
          >删除</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'proj'"
          v-hasPermi="['authTemplate:addPersonProj']"
          @click="addUser(record)"
          >+添加用户</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'unit'"
          v-hasPermi="['authTemplate:addPersonUnit']"
          @click="addUser(record)"
          >+添加用户</el-button
        >
        <el-button
          type="text"
          v-if="$route.query.type == 'all'"
          v-hasPermi="['authTemplate:addPersonAll']"
          @click="addUser(record)"
          >+添加用户</el-button
        >
      </template>
    </MyTable>
    <pagination
      v-show="queryParams.total > 0"
      :total="queryParams.total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <TempDetail
      :detail="detailData"
      v-if="tempDetailType"
      @close="tempDetailType = false"
    />
    <UserDepPostSelect
      rowKey="userId"
      title="user"
      v-model="userDepPostSelectType"
      @on-submit-success-user="userSuccess"
    />
  </div>
</template>

<script>
import {
  templateList,
  authTempUser,
  delTemp,
  getUnit,
  getFunct,
} from "@/api/businessInformation/authTemplate";
import TempDetail from "./components/TempDetail";
export default {
  components: {
    TempDetail,
  },
  data() {
    return {
      detailData: null,
      addTempId: "",
      userDepPostSelectType: false,
      tempDetailType: false,
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
        templateName: "",
        companyTypes: [],
        functionTypes: [],
      },
      dataList: [],
      dataColumns: [
        {
          label: "模板名称",
          prop: "templateName",
          minWidth: "140",
        },
        {
          label: "公司分类",
          prop: "companyTypeName",
          minWidth: "140",
        },
        {
          label: "职能分类",
          prop: "functionTypeName",
          minWidth: "140",
        },
        {
          label: "模板说明",
          prop: "templateExplain",
          minWidth: "220",
          showOverflowTooltipField: true,
        },
        {
          label: "创建人",
          prop: "createName",
          minWidth: "140",
        },
        {
          label: "可使用的用户",
          key: "authUser",
          minWidth: "300",
        },
        {
          label: "操作",
          key: "opertion",
          minWidth: "240",
        },
      ],
      unitList: [],
      functList: [],
    };
  },
  mounted() {
    getUnit().then((res) => {
      if (res.code == 200) {
        this.unitList = res.data;
      }
    });
    getFunct().then((res) => {
      if (res.code == 200) {
        this.functList = res.data;
      }
    });
    this.getList();
  },
  methods: {
    reset() {
      this.queryParams = {
        pageSize: 10,
        pageNum: 1,
        total: 0,
        templateName: "",
        companyTypes: [],
        functionTypes: [],
      };
      this.getList();
    },
    userSuccess(e) {
      console.log(e);
      let data = {
        id: this.addTempId,
        authUserIds: e.map((item) => item.userId),
        operationFlag: 0,
      };
      authTempUser({ ...data }).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: res.msg ? res.msg : "操作成功",
          });
          this.getList();
        }
      });
    },
    addTemp() {
      this.$router.push({
        path: "/businessInformationOther/addTemplate",
        query: {
          type: this.$route.query.type,
        },
      });
    },
    getList() {
      templateList({
        templateType: this.$route.query.type,
        ...this.queryParams,
        agencyUserId:this.$store.state.principalId
      }).then((res) => {
        if (res.code == 200) {
          this.dataList = res.rows;
          if (this.dataList.length > 0) {
            if (this.dataList[0].useAuthFlag != 1) {
              this.dataColumns = [
                {
                  label: "模板名称",
                  prop: "templateName",
                  minWidth: "140",
                },
                {
                  label: "公司分类",
                  prop: "companyTypeName",
                  minWidth: "140",
                },
                {
                  label: "职能分类",
                  prop: "functionTypeName",
                  minWidth: "140",
                },
                {
                  label: "模板说明",
                  prop: "templateExplain",
                  minWidth: "220",
                  showOverflowTooltipField: true,
                },

                {
                  label: "操作",
                  key: "opertion",
                  minWidth: "240",
                },
              ];
            } else {
              this.dataColumns = [
                {
                  label: "模板名称",
                  prop: "templateName",
                  minWidth: "140",
                },
                {
                  label: "公司分类",
                  prop: "companyTypeName",
                  minWidth: "140",
                },
                {
                  label: "职能分类",
                  prop: "functionTypeName",
                  minWidth: "140",
                },
                {
                  label: "模板说明",
                  prop: "templateExplain",
                  minWidth: "220",
                  showOverflowTooltipField: true,
                },
                {
                  label: "创建人",
                  prop: "createName",
                  minWidth: "140",
                },
                {
                  label: "可使用的用户",
                  key: "authUser",
                  minWidth: "300",
                },
                {
                  label: "操作",
                  key: "opertion",
                  minWidth: "240",
                },
              ];
            }
          }
          this.queryParams.total = res.total;
        }
      });
    },
    detail(e) {
      this.detailData = e;

      this.tempDetailType = true;
    },
    delUser(v, e) {
      console.log(v, e);
      this.$confirm(
        `确定要删除${v.templateName}模板中的${e.nickName}用户吗?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        let data = {
          id: v.id,
          authUserIds: [e.userId],
          operationFlag: 1,
        };
        authTempUser({ ...data }).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.getList();
          }
        });
      });
    },
    edit(e) {
      sessionStorage.setItem("updateAuthTemp", true);
      this.$router.push({
        path: "/businessInformationOther/addTemplate",
        query: {
          type: this.$route.query.type,
          id: e.id,
        },
      });
    },
    copy(e) {
      sessionStorage.setItem("copyAuthTemp", true);
      this.$router.push({
        path: "/businessInformationOther/addTemplate",
        query: {
          type: this.$route.query.type,
          id: e.id,
          copy: true,
        },
      });
    },
    del(e) {
      this.$confirm(`确定要删除${e.templateName}模板吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delTemp(e.id).then((res) => {
          if (res.code == 200) {
            this.$message.success("删除成功");
            this.getList();
          }
        });
      });
    },
    addUser(e) {
      this.addTempId = e.id;
      this.userDepPostSelectType = true;
    },
  },
};
</script>
<style lang="less" scoped>
.user {
  display: inline-block;
  border: 1px solid #cccccc;
  padding: 0 10px;
  cursor: pointer;
  color: #409eff;
  border-radius: 4px;
  margin-right: 10px;
  margin-top: 3px;
  i {
    color: #666666;
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>