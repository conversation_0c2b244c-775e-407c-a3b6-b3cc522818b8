<template>
  <div class="p-5">
    <div @click="openSelect = true">查看选择{{ multipleSelection.length }}</div>
    <MyTable
      ref="ceshia"
      :columns="columns"
      :source="configList"
      :showIndex="true"
      :showCheckbox="true"
      @selection-change="handleSelectionChange"
    >
    </MyTable>
    <TableSelect
      :columns="columnsSelect"
      :tableData="multipleSelection"
      v-model="openSelect"
      @on-submit-success-row="submitDelet"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";

export default {
  data() {
    return {
      columns: Object.freeze([{ label: "产品", prop: "ceshi" }]),
      configList: [
        { ceshi: "1", id: 1 },
        { ceshi: "2", id: 2 },
        { ceshi: "3", id: 3 },
        { ceshi: "4", id: 4 },
      ],
      openSelect: false,
      multipleSelection: [],
      columnsSelect: Object.freeze([{ label: "产品", prop: "ceshi" }]),
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {},
    handleSelectionChange(v) {
      this.multipleSelection = v;
    },
    submitDelet(e) {
      e.forEach((row) => {
        this.$refs.ceshia.toggleRowSelection(row, false);
      });
    },
  },
};
</script>
