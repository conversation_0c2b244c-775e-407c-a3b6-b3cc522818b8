<template>
  <div>
    <el-dialog :title="editData ? '编辑代理' : '选择代理模块和代理有效期'" :visible.sync="dialogVisible" width="700px"
      :before-close="handleClose">
      <p v-if="!editData">请选择允许代理人代理您的哪些模块的授权，支持多选</p>

      <p>
        已选择：{{ selectList.length }}
        <el-button type="text" style="margin-left: 16px" @click="selectAll">全选/全不选</el-button>
        <el-button type="text" style="margin-left: 16px" @click="encel">取消</el-button>
      </p>
      <el-divider></el-divider>
      <el-checkbox-group v-model="selectList">
        <el-checkbox style="width: 120px; margin-top: 10px" v-for="item in authList" :label="item" :key="item">{{ item |
          moduleName }}</el-checkbox>
      </el-checkbox-group>
      <el-divider></el-divider>
      <div class="flex">
        <p class="font-bold">代理有效期：</p>
        <div>
          <el-radio @change="changeDate" v-model="radio" label="1">永久有效</el-radio><br /><br />
          <el-radio @change="changeDate" v-model="radio" label="2">设置到期日</el-radio>
          <div class="mt-4" v-if="radio == 2">
            <el-date-picker v-model="date" type="date" placeholder="选择日期">
            </el-date-picker>
            <p class="mt-2">授权到期后，用户将自动失去权限</p>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit" :disabled="selectList.length == 0">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAuthCode } from "@/api/businessInformation/proxyAuth";
export default {
  props: {
    editData: Object,
  },
  data() {
    return {
      date: "",
      radio: "1",
      selectList: [],
      dialogVisible: true,
      authList: [],
    };
  },
  mounted() {
    console.log(this.editData);
    if (this.editData) {
      this.selectList = this.editData.agencyModules;
      this.radio = this.editData.permissionTime ? "2" : "1";
      this.date = this.editData.permissionTime
        ? this.$format(this.editData.permissionTime, "yyyy-MM-dd")
        : "";
    }
    getAuthCode().then((res) => {
      this.authList = res.data;
    });
  },
  filters: {
    moduleName(e) {
      if (e == "OALAUNCH") {
        return "OA流程发起权限";
      } else if (e == "OAVIEW") {
        return "OA流程查看权限";
      } else if (e == "FINANCESYS") {
        return "智慧财务系统";
      } else if (e == "FINANCEPROJ") {
        return "财务项目管理";
      } else if (e == "CARBOOLMANGER") {
        return "车贷绿本出入库";
      } else if (e == "PROJSETUP") {
        return "项目立项管理";
      } else if (e == "ARCHIVES") {
        return "档案管理";
      } else if (e == "INFORMATION") {
        return "资料管理";
      } else if (e == "PERSONNEL") {
        return "人事管理";
      } else if (e == "LICENSE") {
        return "证照管理";
      } else if (e == "ATTENDANCE") {
        return "考勤管理";
      } else if (e == "PAYROLL") {
        return "薪酬中心";
      } else if (e == "DATAREPORT") {
        return "数据报表";
      } else if (e == "ECHARTS") {
        return "Echarts";
      } else if (e == "DATATOP") {
        return "智慧数据系统首页";
      } else if (e == "PROJNAME") {
        return "项目名称";
      } else if (e == "NOTICE") {
        return "通知公告";
      } else if (e == "PERFORMANCE") {
        return "绩效考核";
      } else if (e == "JGINFORMATION") {
        return "监管报送资料";
      } else if (e == "OFFSUPPLY") {
        return "办公用品领用";
      } else if (e == "BADSYSTEM") {
        return "不良资产系统";
      }else if (e == "SXINFORMATION") {
        return "授信及贷后资料";
      }else if (e == "DEBTCONVERSION") {
        return "债转小程序";
      }
    },
    user(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data) {
        return data.nickName;
      }
    },
  },
  methods: {
    changeDate() {
      if (this.radio == 1) {
        this.date = ''
      }
    },
    selectAll() {
      if (this.selectList.length == this.authList.length) {
        this.selectList = [];
      } else {
        this.selectList = [...this.authList];
      }
    },
    encel() {
      this.selectList = [];
    },
    handleClose() {
      this.$emit("close");
    },
    submit() {
      if (this.editData) {
        this.$emit("edit", this.selectList, this.date, this.editData.ids, this.editData.agentId);
      } else {
        this.$emit("submit", this.selectList, this.date);
      }
    },
  },
};
</script>

<style></style>
