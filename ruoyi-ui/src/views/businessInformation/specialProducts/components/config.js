export default {
  formColumns: [
    {
      label: "产品分类",
      prop: "productClassificationId",
      type: "treeselect",
      options: [],
      placeholder: "请选择产品分类",
    },
  ],
  columns: Object.freeze([
    {
      label: "产品分类",
      prop: "productClassificationName",
    },
    { label: "组合", prop: "ruleName" },
    { label: "是否默认", prop: "isDefaultLabel" },
    { label: "备注说明", prop: "remark" },
    { label: "启用状态", key: "enableStatus" },
    { label: "操作", key: "operate" },
  ]),
  columnsDialog: Object.freeze([
    {
      label: "顺序号",
      prop: "sequenceNumber",
      width: 50,
    },
    { label: "公司类型", key: "companyType" },
    { label: "是否必选", key: "isMandatory" },
    { label: "是否允许多个同类公司", key: "isMultipleChoice" },
    { label: "简称是否作为项目名称", key: "isAbbreviation" },
    { label: "操作", key: "operate" },
  ]),
  whetherOrNot: Object.freeze([
    { label: "是", value: '1' },
    { label: "否", value: '2' },
  ]),
  otherCompanyObj:Object.freeze({
    1:'有',
    2:'无',
  }),
  defaultObj:Object.freeze({
    1:'是',
    2:'否',
  }),
  enableStatusObj:Object.freeze({
    1:'正常',
    2:'禁用',
  }),
  formColumnsDialog: Object.freeze([
    {
      slotName: "productClassificationId",
      type: "slot",
      span: 16,
    },
    {
      label: "是否默认:",
      prop: "isDefault",
      type: "switch",
      inactiveText: "",
      span: 8,
      activeValue:1,
      inactiveValue:2,
    },
    {
      slotName: "oaProjectNameRuleSlaveList",
      type: "slot",
      span: 22,
    },
    {
      slotName: "isOtherCompany",
      type: "slot",
      span: 22,
    },
    {
      label: "备注说明:",
      prop: "remark",
      type: "textarea",
      maxlength: 500,
      placeholder: "备注说明，限500字",
      style: { width: "600px" },
      span: 22,
    },
    {
      label: "启用状态:",
      prop: "enableStatus",
      type: "switch",
      span: 22,
      activeValue:1,
      inactiveValue:2,
      activeText:"启用"
    },
  ]),
  formColumnsDialogView: Object.freeze([
    {
      label: "产品分类:",
      prop: "productClassificationName",
      type: "divText",
      span: 16,
    },
    {
      label: "是否默认:",
      prop: "isDefaultLabel",
      type: "divText",
      span: 8,
    },
    {
      slotName: "oaProjectNameRuleSlaveList",
      type: "slot",
      span: 22,
    },
    {
      label: "其他公司:",
      prop: "isOtherCompanyLabel",
      type: "divText",
      span: 22,
    },
    {
      label: "备注说明:",
      prop: "remark",
      type: "divText",
      span: 22,
    },
    {
      label: "启用状态:",
      prop: "enableStatusLabel",
      type: "divText",
      span: 22,
    },
  ]),
  columnsDialogView: Object.freeze([
    {
      label: "顺序号",
      prop: "sequenceNumber",
      width: 50,
    },
    { label: "公司类型", prop: "companTypeName" },
    { label: "是否必选", prop: "isMandatoryLabel" },
    { label: "是否允许多个同类公司", prop: "isMultipleChoiceLabel" },
    { label: "简称是否作为项目名称", prop: "isAbbreviationLabel" },
  ]),
};
