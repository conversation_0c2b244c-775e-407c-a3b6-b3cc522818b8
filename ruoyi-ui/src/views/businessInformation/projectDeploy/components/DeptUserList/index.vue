<template>
  <el-dialog
    title="选择人员"
    :visible.sync="dialogVisible"
    width="900px"
    :before-close="handleClose"
  >
    <div style="display: flex">
      <div class="left">
        <el-tree
          :data="treeData"
          :props="defaultProps"
          @node-click="handleNodeClick"
        ></el-tree>
      </div>
      <div class="right">
        <el-table
          border
          @selection-change="handleSelectionChange"
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="nickName" label="姓名" />
          <el-table-column prop="postName" label="岗位" v-if="userList&&userList.length" />
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getAssignDeptUserList } from "@/api/oa/deploy";
export default {
  props: {
    deptName: String,
    userList: Array,
  },
  data() {
    return {
      tableData: [],
      treeData: [],
      dialogVisible: true,
      defaultProps: {
        children: "children",
        label: "deptName",
      },
      selectList: [],
    };
  },
  mounted() {
    if (this.userList?.length) {
      this.treeData = this.userList;
      this.defaultProps = {
        children: "children",
        label: "companyShortName",
      };
      return;
    }
    getAssignDeptUserList({ deptName: this.deptName }).then((res) => {
      this.treeData = res.rows;
    });
  },
  methods: {
    handleSelectionChange(e) {
      this.selectList = e;
    },
    handleNodeClick(data) {
      console.log(data);
      if (data.parentId == 0) {
        return;
      }
      if (this.userList?.length) {
        this.tableData = data.xmglChannelUser;
        return;
      }
      this.tableData = data.userPostDeptVos;
    },
    handleClose() {
      this.$emit("close");
    },
    submit() {
      this.$emit("submit", this.selectList);
    },
  },
};
</script>

<style lang="less" scoped>
.left {
  width: 300px;
  flex-shrink: 0;
  max-height:55vh;
  overflow-y:auto
}
.right {
  flex: 1;
}
</style>