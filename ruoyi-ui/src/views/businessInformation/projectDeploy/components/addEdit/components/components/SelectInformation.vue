<template>
  <div>
    <el-dialog
      title="选择信息费公司"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div>请从 [业务信息配置-公司信息] 中选择公司</div>
      <div>
        如果选择暂不确定公司，依然可在 [财务项目管理]
        中完成收入的录入，并确认收入金额，但在实际支付信息费前必须完善信息
      </div>
      <div><el-checkbox v-model="noWap">暂不确定公司</el-checkbox></div>
      <div style="display: flex; margin: 12px 0">
        <div class="item">
          <span>公司名称</span
          ><el-input placeholder="请输入" style="width: 200px"></el-input>
        </div>

        <el-button type="primary" @click="search">搜索</el-button>
        <el-button @click="reset">重置</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column width="50" align="center" label="选择">
          <template slot-scope="scope">
            <el-radio
              v-model="active"
              :label="scope.row.id"
              @input="changeRadio($event, scope.row)"
            ></el-radio>
          </template>
        </el-table-column>
        <el-table-column label="公司全称" prop="companyName"></el-table-column>
        <el-table-column
          label="公司简称"
          prop="companyShortName"
        ></el-table-column>
        <el-table-column label="类型" prop="">
          <template slot-scope="scope">
            <div class="flex flex-wrap items-center">
              <div
                v-for="(item, index) in scope.row.companyTypeMappingList"
                :key="index"
                class="mr-3 mt-1 border border-solid rounded px-2"
                :style="{
                  borderColor: '#8fce8c',
                  backgroundColor: '#f0f9f0',
                  fontSize: '13px',
                }"
              >
                {{ item && item.dictLabel }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="params.total > 0"
        :total="params.total"
        :page.sync="params.pageNum"
        :limit.sync="params.pageSize"
        @pagination="getList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button
          size="mini"
          type="primary"
          @click="submit"
          :disabled="!noWap && !active"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
  
  <script>
import { companyList } from "@/api/businessInformation/companyInformation";

export default {
  data() {
    return {
      noWap: false,
      active: null,
      dialogVisible: true,
      params: {
        total: 0,
        pageNum: 1,
        pageSize: 20,
        auxiliaryField: "外部",
        companyName: "",
      },
      tableData: [],
      selectData: null,
    };
  },
  mounted() {
    this.getList();
  },
  watch: {
    noWap(newval, oldval) {
      if (newval) {
        this.active = null;
        this.selectData = null;
      }
    },
    active(newval, oldval) {
      if (newval) {
        this.noWap = false;
      }
    },
  },
  methods: {
    changeRadio(e, v) {
      this.selectData = v;
    },
    async getList() {
      const { rows, total } = await companyList(this.params);
      this.tableData = rows;
      this.params.total = total;
    },
    handleClose() {
      this.$emit("close");
    },
    submit() {
      this.$emit("submit", this.selectData);
    },
    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    reset() {
      this.params = {
        total: 0,
        pageNum: 1,
        pageSize: 20,
        auxiliaryField: "外部",
        companyName: "",
      };
      this.getList();
    },
  },
};
</script>
  
  <style lang="less" scoped>
.item {
  margin: 0;
  margin-right: 16px;
  span {
    margin-right: 5px;
  }
}
/deep/.el-radio__label {
  display: none !important;
}
</style>