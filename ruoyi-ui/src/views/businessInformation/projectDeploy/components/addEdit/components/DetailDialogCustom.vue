<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="自定义项目名称"
      :visible.sync="innerValue"
      width="550px"
      @open="handleOpen"
    >
      <div class="flex">
          <el-input v-model.trim="projectName" class="mr-2" maxlength="50" clearable   show-word-limit></el-input>
          <el-button type="text" @click="rest">恢复默认</el-button>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
         <el-button type="primary"  @click="submit" 
        >确 定</el-button
      >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
export default {
  mixins: [vModelMixin],
  props: {
     currentProjectName: {
      required: true,
      default: '',

    },
  },
  data() {
    return {
      projectNameInit:'',
      projectName:''
    };
  },
  watch: {},
  mounted() {},
  methods: {
    handleOpen(){
      this.projectNameInit=this.currentProjectName;
      this.projectName=this.currentProjectName;
    },
    rest(){
      this.projectName=this.projectNameInit;
    },
    submit(){
      this.innerValue=false;
      this.$emit('submitCustom',this.projectName);
    }
  },
};
</script>
