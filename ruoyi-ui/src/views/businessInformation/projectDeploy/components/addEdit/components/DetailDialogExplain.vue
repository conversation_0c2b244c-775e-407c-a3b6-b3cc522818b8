<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="说明"
      :visible.sync="innerValue"
      width="550px"
    >
      <div>
        <div>管理员已开启编辑修改项目名称需审核功能</div>
        <div>如果新增、修改、删除项目名称，必须由业务总监进行审核，并且通知相关业务责任人和财务责任人</div>
        <div>步骤1：业务人员提交新增、修改、删除的申请</div>
        <div>
          步骤2：业务总监进行审核。如果是新增的项目，需由业务总监添加该项目的业务责任人和财务责任人信息。发生修改、删除操作都会通知责任人
        </div>
        <div>步骤3：审核通过后，会有系统待办消息通知该项目的业务责任人和财务责任人</div>
        <div class="mt-3">审核可以被驳回，驳回后本次编辑无效</div>
        <div>审核中的项目，将锁定无法被编辑</div>
        <div>审核通过后新项目信息即时生效</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
export default {
  mixins: [vModelMixin],
  props: {},
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  methods: {},
};
</script>
