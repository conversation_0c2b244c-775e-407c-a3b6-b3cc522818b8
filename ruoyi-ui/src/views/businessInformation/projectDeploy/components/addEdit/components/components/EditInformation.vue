<template>
  <div class="app-container">
    <div>
      <el-button size="mini" @click="$router.push('/businessInformation/projectDeploy')">取消</el-button>
      <el-button
        size="mini"
        type="primary"
        @click="next"
        :disabled="
          cwProjectFeeFlag === null ||
          (this.tableData.length == 0 && cwProjectFeeFlag == 1)
        "
        >下一步</el-button
      >
    </div>
    <div style="margin-top: 12px; color: #777">
      编辑项目信息费信息需要OA流程审核，编辑完成后点击 [下一步] 发起审核流程
    </div>
    <div style="margin-top: 12px">
      <span>是否有信息费：</span>
      <el-radio v-model="cwProjectFeeFlag" label="1">有</el-radio>
      <el-radio v-model="cwProjectFeeFlag" label="0">无</el-radio>
    </div>
    <div v-if="cwProjectFeeFlag==0" style="margin-left: 190px;color: #777;">
       <div>本项目无信息费</div>
       <div>项目每个期次信息费为0，无需打款</div>
    </div>
    <div v-if="cwProjectFeeFlag == 1">
      <el-table
        :data="tableData"
        style="width: 100%; margin-top: 12px"
        border=""
      >
        <el-table-column label="序号" width="50">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="信息费公司" width="270">
          <template slot-scope="scope">
            <el-input
              :class="
                scope.row.companyName == '暂不确定公司' ? 'custom-input' : ''
              "
              v-model="scope.row.companyName"
              placeholder="请选择"
              style="width: 100%"
              suffix-icon="el-icon-arrow-down"
              @click.native="select(scope.$index)"
            >
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="账号" width="270">
          <template slot-scope="scope">
            <el-input
              v-if="!scope.row.noWap"
              style="width: 100%"
              v-model="scope.row.accountNumber"
              placeholder="请输入"
            ></el-input>
            <div v-else style="color: red">{{ scope.row.accountNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column label="开户行" width="270">
          <template slot-scope="scope">
            <el-input
              v-if="!scope.row.noWap"
              style="width: 100%"
              v-model="scope.row.bankOfDeposit"
              placeholder="请输入"
            ></el-input>
            <div v-else style="color: red">{{ scope.row.accountNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column label="费率" width="120">
          <template slot-scope="scope">
            <el-input
              oninput="value=value.replace(/[^0-9.]/g,'')"
              @blur="limitNumbersEvent($event, scope.$index)"
              style="width: 70px; margin-right: 4px"
              v-model="scope.row.rate"
            ></el-input
            >%
          </template>
        </el-table-column>
        <el-table-column label="税率" width="120">
          <template slot-scope="scope">
            <el-input
              oninput="value=value.replace(/[^0-9.]/g,'')"
              @blur="limitNumbersEvent2($event, scope.$index)"
              style="width: 70px; margin-right: 4px"
              v-model="scope.row.taxRate"
            ></el-input
            >%
          </template>
        </el-table-column>
        <el-table-column width="140" label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="del(scope.row, scope.$index)"
              >删除</el-button
            >
            <el-button
              type="text"
              v-if="scope.$index != 0"
              @click="moveUp(scope.$index)"
              >上移</el-button
            >
            <el-button
              type="text"
              @click="moveDown(scope.$index)"
              v-if="scope.$index != tableData.length - 1"
              >下移</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-button
        style="margin-top: 12px"
        size="mini"
        type="primary"
        @click="add"
        >+ 添加</el-button
      >
      <div style="margin-top: 15px">注意：</div>
      <div>
        如果不填写税率、税率，依然可在 [财务项目管理]
        中完成收入的录入，并确认收入金额，但在实际支付信息费前必须完善信息
      </div>
      <div>
        如果修改已存在条目的信息费公司，项目中未完结期次中包含该公司的，名称也将被修改为新公司名（但已录入的费率、税率、计算出的信息费不变）。因此如果要添加新的信息费公司，建议删除旧公司条目，而不是在原条目上进行修改。
      </div>
    </div>
    <SelectInformation
      v-if="selectType"
      @close="selectType = false"
      @submit="submitSelect"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="selectCompanyType = false"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import { getDataByTemplName } from "@/api/oa/deploy";

import { getCwProjectFeeInfo } from "@/api/oa/deploy";
import SelectInformation from "./SelectInformation.vue";
export default {
  components: {
    SelectInformation,
  },
  name: "EditInformation",
  data() {
    return {
      selectCompanyType: false,
      cwProjectFeeFlag: null,
      selectType: false,
      tableData: [],
      selecIndex: null,
      allData: null,
      oldData: null,
      newData: null,
      editList: [],
    };
  },
  mounted() {
    getCwProjectFeeInfo(this.$route.query.id).then((res) => {
      this.oldData = JSON.parse(JSON.stringify(res.data));
      this.oldData.oaProjectDeployCwProjectFeeInfoList.forEach((item) => {
        if (item.rate) {
          item.rate = item.rate.toFixed(2);
        }
        if (item.taxRate) {
          item.taxRate = item.taxRate.toFixed(2);
        }
        if (item.id && !item.companyId) {
          this.$set(item, "noWap", true);
          item.companyName = "暂不确定公司";
          item.accountNumber = "暂不确定";
          item.bankOfDeposit = "暂不确定";
        }
      });
      this.newData = res.data;
      this.cwProjectFeeFlag = res.data.cwProjectFeeFlag;
      this.tableData = res.data.oaProjectDeployCwProjectFeeInfoList;
      this.tableData.forEach((item) => {
        if (item.rate) {
          item.rate = item.rate.toFixed(2);
        }
        if (item.taxRate) {
          item.taxRate = item.taxRate.toFixed(2);
        }
        if (item.id && !item.companyId) {
          this.$set(item, "noWap", true);
          item.companyName = "暂不确定公司";
          item.accountNumber = "暂不确定";
          item.bankOfDeposit = "暂不确定";
        }
      });
    });
  },
  methods: {
    limitNumbersEvent(e, index) {
      // 通过正则保留两位小数点
      let val = e.target.value;
      console.log(val);

      if (val.length > 6) val = val.slice(0, 6);
      if (val * 1 > 100) val = 100 + "";

      this.tableData[index].rate = val.match(/^\d*(\.?\d{0,2})/g)[0] || null;
    },
    limitNumbersEvent2(e, index) {
      // 通过正则保留两位小数点
      let val = e.target.value;
      console.log(val);

      if (val.length > 6) val = val.slice(0, 6);
      if (val * 1 > 100) val = 100 + "";

      this.tableData[index].taxRate = val.match(/^\d*(\.?\d{0,2})/g)[0] || null;
    },
    next() {
      let flag = false;
      if (this.cwProjectFeeFlag == 0) {
        this.tableData = [];
      }
      if (this.tableData.length > 0) {
        this.tableData.forEach((item) => {
          if (!item.companyName) {
            flag = true;
          }
          if (item.companyId && (!item.accountNumber || !item.bankOfDeposit)) {
            flag = true;
          }
        });
      }

      if (flag) {
        this.$message.warning("请填写完整表单");
        return;
      }
      this.newData.oaProjectDeployCwProjectFeeInfoList = this.tableData;
      console.log(this.oldData, this.newData);

      this.selectCompanyType = true;
    },
    submitCompany(v) {
      getDataByTemplName({
        companyId: v,
        templateName: "业务信息配置-修改项目信息费信息申请",
        isEnableCompanyId: 1,
      }).then((res) => {
        this.selectCompanyType = false;
        console.log(this.editList);
        this.newData.oaProjectDeployCwProjectFeeInfoList.forEach((item) => {
          if (!item.id) {
            this.editList.push({
              newData: item,
              oldData: null,
              editType: "add",
            });
          }
          this.oldData.oaProjectDeployCwProjectFeeInfoList.forEach((v) => {
            if (
              item.id == v.id &&
              (item.companyName != v.companyName ||
                item.accountNumber != v.accountNumber ||
                item.bankOfDeposit != v.bankOfDeposit ||
                item.rate != v.rate ||
                item.taxRate != v.taxRate)
            ) {
              this.editList.push({
                newData: item,
                oldData: v,
                editType: "edit",
              });
            }
          });
        });
        let obj = {
          editList: this.editList,
          oldData: this.oldData,
          newData: {
            ...this.newData,
            id: this.$route.query.id,
            cwProjectFeeFlag: this.cwProjectFeeFlag,
          },
          projectName: this.$route.query.name,
        };
        sessionStorage.setItem("editInformationData", JSON.stringify(obj));
        this.$router.push({
          path: "/oaWork/updateProcessForm",
          query: {
            templateId: res.templateId,
            classificationId: res.classificationId,
            companyId: res.companyId,
            editInformation: true,
          },
        });
      });
    },
    submitSelect(v) {
      console.log(v);
      if (!v) {
        this.tableData[this.selecIndex].companyId = "";
        this.tableData[this.selecIndex].companyName = "暂不确定公司";
        this.tableData[this.selecIndex].accountNumber = "暂不确定";
        this.tableData[this.selecIndex].bankOfDeposit = "暂不确定";
        this.tableData[this.selecIndex].rate = "";
        this.tableData[this.selecIndex].taxRate = "";
        this.$set(this.tableData[this.selecIndex], "noWap", true);
      } else {
        this.tableData[this.selecIndex].companyId = v.id;
        this.tableData[this.selecIndex].companyName = v.companyName;
        this.tableData[this.selecIndex].accountNumber = v.accountNumber;
        this.tableData[this.selecIndex].bankOfDeposit = v.bankOfDeposit;
        this.$set(this.tableData[this.selecIndex], "noWap", false);
      }
      this.tableData.forEach((item, index) => {
        item.orderNum = index + 1;
      });
      this.newData.oaProjectDeployCwProjectFeeInfoList = this.tableData;

      this.selectType = false;
    },
    add() {
      this.tableData.push({
        accountNumber: null,
        bankOfDeposit: null,
        companyName: null,
        companyId: null,
        oaProjectDeployId: this.$route.query.id,

        rate: null,
        status: "0",
        taxRate: null,
        addType: true,
      });
    },
    del(v, index) {
      if (v.id) {
        this.editList.push({
          newData: null,
          oldData: v,
          editType: "del",
        });
      }
      this.tableData.splice(index, 1);
    },
    select(index) {
      this.selecIndex = index;
      this.selectType = true;
    },
    moveUp(index) {
      if (index > 0) {
        const temp = this.tableData[index];
        this.$set(this.tableData, index, this.tableData[index - 1]);
        this.$set(this.tableData, index - 1, temp);
      }
    },
    moveDown(index) {
      if (index < this.tableData.length - 1) {
        const temp = this.tableData[index];
        this.$set(this.tableData, index, this.tableData[index + 1]);
        this.$set(this.tableData, index + 1, temp);
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .custom-input .el-input__inner {
  color: red; /* 你可以根据需要修改颜色 */
}
</style>