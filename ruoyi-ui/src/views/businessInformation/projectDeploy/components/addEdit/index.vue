<template>
  <div class="pb-28">
    <div v-if="openTypes == 'edit'">
      <el-form :model="myForm" :rules="rules" label-width="100px">
        <el-form-item label="" class="w-1/2 mx-auto">
          <div style="color: #999; line-height: 18px">
            不同产品分类的项目有不同的名称组成规则 <el-button  type="text" size="mini" @click="openMore = true">更多说明</el-button>
          </div>
          <div style="color: #999; line-height: 18px">
           如果找不到所需的项目类型、产品分类，请联系技术部门进行添加
          </div>
          <div style="color: #999; line-height: 18px">
            如果找不到所需公司，请先在 [公司信息管理] 菜单中进行创建
          </div>
         </el-form-item>
         <el-form-item label="项目类型:" class="w-1/2 mx-auto" prop="projectTypeList">
            <ProjectTypes :myForm="myForm" />
        </el-form-item>
         <el-form-item label="产品分类:" class="w-1/2 mx-auto" prop="businessTypeList">
            <ProductClassification :myForm="myForm" @save-success="getTableByRule"/>
        </el-form-item>
        <el-form-item label="项目名称:" class="w-1/2 mx-auto">
          <div class="flex">
            <div>{{ myForm.projectName || "-" }}</div>
            <el-button
              v-if="myForm.projectName"
              type="text"
              v-hasPermi="['projectDeploy:custom']"
              @click="openCustom = true"
              class="ml-3"
              >自定义</el-button
            >
          </div>
          <div style="color: red; width: 50vw" v-show="repetitiveProjectName">
            已经存在使用相同公司创建的项目！不允许重复创建
          </div>
         
        </el-form-item>
        <el-form-item>
          <el-table
            :data="myForm.projectList"
            border
            :span-method="objectSpanMethod"
            :key="tableKey"
            class="projectTable"
          >
            <el-table-column
              label="组成项目的公司"
              align="left"
              width="160"
              prop="isNecessity"
            >
              <template #default="{ row }">
                {{ isNecessityList[row.isNecessity] }}
                <el-tooltip
                  v-if="row.isNecessity == 1"
                  class="item"
                  effect="dark"
                  content="本项目是否还包含的其他类型的公司，例某些项目的技术服务方"
                  placement="top-start"
                >
                  <span class="relative bottom-1">①</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              label="公司类型"
              align="left"
              prop="companyType"
              width="180"
            >
              <template #default="{ row }">
                <div v-if="row.companyType != 'other'">
                  <span style="color: red" v-show="row.isMandatory==1">*</span>
                  {{ row.companTypeName }}
                </div>
                <div v-else>
                  <el-select
                    v-for="(item, index) in row.tableItem"
                    v-model="item.unitTypeId"
                    placeholder="请选择"
                    @change="changeOtherList(item)"
                    class="mb-2"
                  >
                    <el-option
                      v-for="(item, index) in otherUnitTypeListAll"
                      :key="index"
                      :label="item.dictLabel"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="选择公司" align="left" width="200">
              <template #default="{ row }">
                <div v-for="(item, index) in row.tableItem" :key="index">
                  <div v-if="(item.unitTypeId == ''||item.unitTypeId == undefined)&&(item.companyType=='other'||item.unitType=='3'||item.unitType=='other')" style="height: 40px"></div>
                  <el-input
                    v-else
                    v-model="item.companyShortName"
                    size="small"
                    class="mb-2"
                    style="z-index: 200; height: 40px; margin: auto"
                    @click.native="focusCompany(row, item)"
                  >
                    <i class="el-icon-arrow-down el-input__icon" slot="suffix">
                    </i>
                  </el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="公司全称" align="left" prop="companyName">
              <template #default="{ row }">
                <div v-for="(item, index) in row.tableItem" :key="index">
                  <div style="height: 40px">{{ item.companyName }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="占比" align="left" width="130">
              <template #default="{ row }">
                <div v-show="row.isNecessity == 0">
                  <div v-if="row.tableItem.length == 1">
                    {{ row.tableItem[0].proportion }}%
                  </div>
                  <div v-else>
                    <div v-for="(item, index) in row.tableItem" :key="index">
                      <el-input
                        placeholder="1-99"
                        style="height: 40px; width: 80px; margin: auto"
                        class="mb-2"
                        v-model="item.proportion"
                        size="small"
                        oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/\D/g,'');if(value>90)value=99;if(value<1)value=null"
                      >
                      </el-input>
                      %
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="left">
              <template #default="{ row }">
                <div v-if="row.companyType != 'other'">
                  <div>
                    <div
                      v-for="(item, index) in row.tableItem"
                      :key="index"
                      style="height: 40px"
                    >
                      <el-button
                        size="small"
                        type="text"
                        style="font-size: 14px"
                        class="relative top-1"
                        v-if="index == 0&&row.isMultipleChoice==1"
                        @click="addList(row,item)"
                        >+ 添加多个{{row.companTypeName}}</el-button
                      >
                      <el-button
                        style="font-size: 14px"
                        size="small"
                        type="text"
                        v-if="index != 0"
                        @click="deletList(row, item)"
                        >- 删除</el-button
                      >
                    </div>
                  </div>
                </div>
                <div v-if="row.companyType == 'other'">
                  <div>
                    <div
                      v-for="(item, index) in row.tableItem"
                      :key="index"
                      style="height: 40px"
                    >
                      <el-button
                        style="font-size: 14px"
                        class="relative top-1"
                        size="small"
                        type="text"
                        v-if="index == 0"
                        @click="addOtherUnitList(row)"
                        >+ 添加其他公司</el-button
                      >
                      <el-button
                        style="font-size: 14px"
                        size="small"
                        type="text"
                        v-if="index != 0"
                        @click="deletOtherUnitList(row, item)"
                        >- 删除</el-button
                      >
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
       
       
        <!-- <el-form-item label="业务类型:" class="w-1/2 mx-auto">
          <div>
            <div class="flex flex-wrap">
              <div
                v-for="(item, index) in myForm.businessTypeList"
                :key="index"
                class="mr-3 mb-3 border border-solid rounded px-2 flex h-6 items-center relative top-2"
                style="
                  border-color: #cccccc;
                  background-color: #f2f2f2;
                  font-size: 13px;
                "
              >
                <div class="h-6 leading-6">{{ item.dictLabel }}</div>
                <div
                  @click="deletType(item, 'business')"
                  class="cursor-pointer"
                >
                  <i class="el-icon-close"></i>
                </div>
              </div>
              <el-button
                style="height: 30px"
                class="relative top-1"
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="openTypeAdd('business')"
                >添加</el-button
              >
            </div>
          </div>
          <div style="color: #999; line-height: 18px" class="relative top-1">
            非必选项，如果找不到所需的业务类型，请在[业务类型]菜单中添加
          </div>
        </el-form-item> -->
        
        <!-- <el-form-item label="渠道方" prop="channelType" class="w-1/2 mx-auto">
          <div style="display: flex">
            <div>
              <el-radio v-model="myForm.channelType" label="0">内部</el-radio>
              <div style="color: #999" class="relative top-1">
                由渠道部岗位用户担任
              </div>
              <div v-if="myForm.channelType == 0">
                <el-select
                  @change="changeQudaofang"
                  v-model="qudaofangIds"
                  multiple=""
                  collapse-tags
                >
                  <el-option
                    v-for="item in qudaofangList"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.userId"
                  ></el-option>
                </el-select>
                <el-button type="primary" size="mini" @click="selectPerson"
                  >+ 选择用户</el-button
                >
                <div style="color: #999" class="relative top-1">
                  渠道方属性会影响 [项目立项管理] 功能
                </div>
              </div>
            </div>
            <div style="margin-left: 120px">
              <el-radio v-model="myForm.channelType" label="1">外部</el-radio>
              <div style="color: #999" class="relative top-1">
                输入自定义的外部渠道方的名称
              </div>
              <div v-if="myForm.channelType == 1">
                <el-input
                  placeholder="请输入"
                  v-model="myForm.channelName"
                ></el-input>
                <div style="color: #999" class="relative top-1">
                  渠道方属性会影响 [项目立项管理] 功能
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="授信金额" class="w-1/2 mx-auto">
          <el-input
            style="width: 180px"
            v-model="myForm.creditAmount"
          ></el-input
          >万元
        </el-form-item> -->

        <el-form-item label="启用状态" class="w-1/2 mx-auto">
          <el-switch
            v-model="myForm.isEnable"
            active-value="Y"
            inactive-value="N"
            >启用</el-switch
          >
        </el-form-item>

        <div v-if="changeEditType" class="w-1/2 mx-auto">
          <el-divider></el-divider>
          <p style="padding-left: 80px">
            编辑项目名称，需要由管理员进行审核，点击下一步，将发起OA流程
          </p>
          <p style="padding-left: 80px">
            审核结果请关注 [OA办公-我的流程]
            <br />如果审核通过也将通过系统待办通知 [业务责任人] 和 [财务责任人]
            <el-button type="text" @click="open = true">规则说明</el-button>
          </p>
          <div
            v-if="myForm.id"
            class="flex justify-between"
            style="padding: 0 80px"
          >
            <div class="flex">
              <div class="font-bold">财务责任人：</div>
              <div>
                {{
                  myForm.caiwuList &&
                  myForm.caiwuList.map((item) => item.userNickName).join()
                }}
              </div>
            </div>
            <div class="flex">
              <div class="font-bold">业务责任人:</div>
              <div>
                {{
                  myForm.yewuList &&
                  myForm.yewuList.map((item) => item.userNickName).join()
                }}
              </div>
            </div>
          </div>
        </div>
      </el-form>
      <InBody>
        <div
          class="flex justify-center fixed bottom-0 bg-white z-10 w-full pb-2"
          style="left: 130px"
        >
          <!-- <el-button
            v-show="myForm.id"
            type="warning"
            style="background: #ff9900; border: none"
            @click="deletePro"
            v-hasPermi="['projectDeploy:delet']"
            >删除项目名称</el-button
          > -->
          <el-button @click="cancel">取 消</el-button>
          <el-button
            type="primary"
            @click="submitForm"
            v-if="!myForm.id"
            :disabled="(!(tableAll))&&!Boolean(myForm.projectTypeList.length)&&!Boolean(myForm.businessTypeList.length)"
            >确 定</el-button
          >
          <el-button
            type="primary"
            v-if="myForm.id"
            @click="next"
            :disabled="(!(tableAll))&&!Boolean(myForm.projectTypeList.length)&&!Boolean(myForm.businessTypeList.length)"
            >下一步</el-button
          >
        </div>
      </InBody>
      <DetailDialogExplainAddEdit v-model="open" />
      <DetailDialogExplainAddEditMore v-model="openMore" />
      <DetailDialogCustom
        v-model="openCustom"
        :currentProjectName="myForm.projectName"
        @submitCustom="submitCustom"
      />
      <DetailDialogSelectCompany
        v-model="openSelectCompany"
        :tableList="tableListCompany"
        :currentTableType="currentTableType"
        @submitCompany="submitCompany"
      />
      <DetailDialogType
        v-model="openType"
        :bussOrProject="bussOrProject"
        @on-save-success="typeCallBack"
        :selectBusiness="myForm.typeList"
      />
    </div>
    <div v-else>
      <Views
        @viewCancel="cancel"
        @edit="edit"
        :form="form"
        :changeEditType="changeEditType"
        :otherUnitTypeListAll="companytypeList"
        :companytypeList="companytypeList"
        :companyList="companyList"
      />
    </div>
    <DeptUserList
      deptName="渠道"
      @close="deptUserListType = false"
      v-if="deptUserListType"
      @submit="selectQDF"
    />
  </div>
</template>

<script>
import {
  getDeploy,
  getDataByTemplName,
  getAssignDeptUserList,
} from "@/api/oa/deploy";
import XEUtils from "xe-utils";
import config from "../config";
import { queryData } from "@/api/oa/deploy";
import DeptUserList from "../DeptUserList/index.vue";
import DetailDialogExplainAddEdit from "./components/DetailDialogExplain.vue";
import DetailDialogExplainAddEditMore from "./components/DetailDialogExplainMore.vue";
import DetailDialogCustom from "./components/DetailDialogCustom.vue";
import DetailDialogType from "./components/DetailDialogType.vue";
import DetailDialogSelectCompany from "./components/DetailDialogSelectCompany.vue";
import Views from "./components/Views.vue";
import { projectNameRuleGetRule } from "@/api/businessInformation/specialProducts";

import { companyList,newCompanySelectList } from "@/api/businessInformation/companyInformation";

export default {
  components: {
    DetailDialogExplainAddEdit,
    DetailDialogExplainAddEditMore,
    DetailDialogSelectCompany,
    DetailDialogCustom,
    DetailDialogType,
    Views,
    DeptUserList,
  },
  props: {
    openTypes: {
      type: String,
      required: true,
      default: "edit",
    },
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    projects: {
      type: Array,
      required: true,
      default: () => [],
    },
    companyList: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    companytypeList: {
      type: Array,
      required: true,
      default: () => [],
    },
    changeEditType: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  computed: {
    //是否全部填写完成
    tableAll() {
      let temp = true;
      const tableList = this.myForm.projectList?.filter(
        (item) => item.companyType != 'other'
      );
      if(!tableList)temp = false;
      tableList?.some((item) => {
        item.tableItem.some((item1) => {
          if (item.isMandatory==1&&(!item1.proportion || !item1.companyShortName)) {
            temp = false;
            return false;
          }
        });
      });
      
      return temp;
    },
  },
  data() {
    return {
      qudaofangList: [],
      deptUserListType: false,
      qudaofangIds: [],
      ...config,
      myForm: {},
      open: false,
      openMore: false,
      openSelectCompany: false,
      openCustom: false,
      openType: false,
      custNoListAll: [],
      fundNoListAll: [],
      partnerNoListAll: [],
      otherUnitListAll: [],
      otherUnitTypeListAll: [],
      tableListCompany: [],
      currentTableId: undefined,
      currentTableType: undefined,
      currentTableListId: undefined,
      repetitiveProjectName: false,
      bussOrProject: "",
      isCustom: false, //禁止更新名称
      tableKey: Math.random(),
      oldForm: null,
      byRule:{}
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    changeQudaofang(e) {
      this.myForm.qudaofangList = [];
      this.qudaofangList.forEach((item) => {
        this.qudaofangIds.forEach((i) => {
          if (item.userId == i) {
            this.myForm.qudaofangList.push({
              userId: item.userId,
              name: item.nickName,
            });
          }
        });
      });
    },
    selectQDF(e) {
      console.log(e);
      this.qudaofangList = e;
      this.qudaofangIds = this.qudaofangList.map((item) => item.userId);
      this.myForm.qudaofangList = this.qudaofangList.map((item) => {
        return {
          userId: item.userId,
          name: item.nickName,
        };
      });
      this.deptUserListType = false;
    },
    selectPerson() {
      this.deptUserListType = true;
      // getAssignDeptUserList({deptName:'渠道'}).then(res=>{

      // })
    },
    edit() {
      this.$emit("edit");
    },
    async init() {
      this.getOtherCompanyList();
      
      if (this.form.id) {
        const { data } = await getDeploy(this.form.id);
        this.qudaofangList = data.qudaofangList;
        this.qudaofangIds = this.qudaofangList.map((item) => item.userId);
        this.myForm.qudaofangList = this.qudaofangList.map((item) => {
          return {
            userId: item.userId,
            name: item.nickName,
          };
        });
        this.oldForm = JSON.parse(JSON.stringify(data));
        if(data?.businessTypeList.length){
          const datas= await projectNameRuleGetRule({productClassificationId:data.businessTypeList.filter(item=>item.typeId)?.map(item=>item.typeId)});
          this.byRule=datas.data;
        }
      }
      this.getMyFormTable();
    },
    // table合并列
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let data = this.myForm.projectList; //拿到当前table中数据
      let cellValue = row[column.property]; //当前位置的值
      let sortArr = ["isNecessity"];

      if (cellValue && sortArr.includes(column.property)) {
        let prevRow = data[rowIndex - 1]; //获取到上一条数据
        let nextRow = data[rowIndex + 1]; //下一条数据
        if (prevRow && prevRow[column.property] === cellValue) {
          //当有上一条数据，并且和当前值相等时
          return { rowspan: 0, colspan: 0 };
        } else {
          let countRowspan = 1;
          while (nextRow && nextRow[column.property] === cellValue) {
            //当有下一条数据并且和当前值相等时,获取新的下一条
            nextRow = data[++countRowspan + rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
    },

    getOtherCompanyList() {
      const otherCompanyList = this.companytypeList.filter(
        (item) => !this.paramsLabel.includes(item.dictLabel)
      );
      const unshiftArr = { dictLabel: "无", dictValue: "" };
      otherCompanyList.unshift(unshiftArr);
      this.otherUnitTypeListAll = otherCompanyList;
    },
    getTableByRule(value){
      if(value)this.byRule=value;
      this.projectListTableInit=this.byRule?.oaProjectNameRuleSlaveList?.map(item=>{
        return {
          myId:item.id,
          isNecessity:"0",
          companyType:item.companyType,
          companTypeName:item.companTypeName,
          isMandatory:item.isMandatory,
          isMultipleChoice:item.isMultipleChoice,
          isAbbreviation:item.isAbbreviation,
          tableItem: [
            {
              myId: item.id,
              companyId: "",
              companyName: "",
              companyShortName: "",
              companyType: item.companyType,
              proportion: "100",
              isNecessity: "0",
            },
          ],
        }
      })
      if(this.byRule.isOtherCompany==1){
        this.projectListTableInit.push({
          myId: 'otherId',
          isNecessity: "1",
          companyType: 'other',
          tableItem: [
            {
              myId: 'otherId',
              unitTypeId: "",
              companyId: "",
              companyName: "",
              companyShortName: "",
              companyType: 'other',
              proportion: "100",
              isNecessity: "1",
            },
          ],
        })
        
      }
      this.setProjectList();
    },
    getMyFormTable() {
      if (this.form.id) this.isCustom = true; //编辑时不触发更新项目名称
      this.myForm = XEUtils.clone(this.form, true);
      if (!this.myForm.id) {
        const tableInit = XEUtils.clone(this.projectListTableInit, true);
        this.$set(this.myForm, "projectList", tableInit);
        this.$set(this.myForm, "isEnable", "Y");
        this.$set(this.myForm, "channelType", "0");
        this.$set(this.myForm, "creditAmount", "");
      } else {
        this.getTableByRule();
      }
    },
    setProjectList() {
      const tableInit = XEUtils.clone(this.projectListTableInit, true);
      let tempArr=this.byRule.oaProjectNameRuleSlaveList?.map(item=>{
        return item.companyType+'List';
      })
      if(this.byRule.isOtherCompany==1){
        tempArr.push('otherUnitList')
      }
      const tempObj = {};
      this.byRule.oaProjectNameRuleSlaveList?.forEach(item => {
        tempObj[item.companyType+'List'] = item.companTypeName;  
      })
      tempArr?.forEach((item) => {
        if(this.myForm.tableList){
          this.myForm.tableList[item]?.forEach((item1) => {
            item1.myId = item1.id;
            item1.companyName = item1.unitName;
            item1.companyShortName =
              item1.unitShortName || `暂不确定${tempObj[item]}`;
            item1.companyType = item1.unitType;
            item1.companyId = item1.unitId;
            item1.unitTypeId = item1.unitTypeId;
          });
        }
      });
      tableInit?.forEach(item=>{
        if(item.companyType!='other'){
          if(this.myForm.tableList&&this.myForm.tableList[item.companyType+'List'])item.tableItem=this.myForm.tableList[item.companyType+'List']
        }
      })
      if (this.myForm.otherUnitList?.length > 0) {
        this.myForm.otherUnitList.forEach((item) => {
          item.companyShortName =
            item.unitShortName ||
            `暂不确定${
              this.otherUnitTypeListAll.filter(
                (item1) => item1.dictCode == item.unitTypeId
              )[0]?.dictLabel
            }`;
        });
        tableInit[tableInit.length-1].tableItem = this.myForm.otherUnitList;
      }
      this.$set(this.myForm, "projectList", tableInit);
    },
    changeOtherList(value) {
      if (value.unitTypeId == "add") {
        this.$nextTick(() => {
          this.$set(value,'unitTypeId',"");
        });
        this.$router.push({ path: "/businessInformation/companyInformation" });
        return;
      }
      this.$set(value,'companyId',undefined);
      this.$set(value,'companyName','');
      this.$set(value,'companyShortName','');
    },
    async focusCompany(value, item) {
      this.currentTableId = value.myId;
      this.currentTableListId = item.myId||item.id;
      if(value.companyType=='other'){
         const companyTypeCode = this.otherUnitTypeListAll.filter(
            (item1) => item1.dictCode == item.unitTypeId
          )[0].dictCode;
          const { rows } = await companyList({ companyTypeCode, status: 0 });
          this.tableListCompany = XEUtils.clone(rows, true);
          this.currentTableType =
            "暂不确定" +
            this.otherUnitTypeListAll.filter(
              (item1) => item1.dictCode == item.unitTypeId
            )[0].dictLabel;
      }else{
        this.tableListCompany=await newCompanySelectList({ selectCode: value.companyType, modelCode: "PROJECTNAMERULE" })
        this.currentTableType = `暂不确定${value.companTypeName}`;
      }
      this.openSelectCompany = true;
    },
    submitCompany(value) {
      this.isCustom = false;
      this.myForm.projectList.forEach((item) => {
        if (item.myId == this.currentTableId) {
          if (this.currentTableListId) {
            item.tableItem.forEach((item1) => {
              if ((item1.myId||item1.id) == this.currentTableListId) {
                this.$set(item1,'companyId',value.id)
                this.$set(item1,'companyName',value.companyName)
                this.$set(item1,'companyShortName',value.companyShortName)
              }
            });
          }
        }
      });
      this.tableKey++;
      this.getProjectName();
    },
    getProjectName() {
      let temp = true;
      const tableList = this.myForm.projectList?.filter(
        (item) => item.companyType != 'other'
      );
      tableList?.some((item) => {
        item.tableItem.some((item1) => {
          if (!item1.proportion || !item1.companyShortName) {
            temp = false;
            return false;
          }
        });
      });
      if (!temp) return;
      if (this.isCustom) return;
      if (this.myForm.projectList) {
        if(this.byRule.isDefault==1){
          //默认规则特殊处理
          const technicalServiceCode = this.otherUnitTypeListAll.filter(
            (item) => item.dictValue == "4"
          )[0].dictCode;
          const backflowSquareCode = this.otherUnitTypeListAll.filter(
            (item) => item.dictValue == "5"
          )[0].dictCode;

          let technicalServiceName = this.myForm.projectList[this.myForm.projectList.length-1].tableItem
            .map((item) => {
              if (item.unitTypeId == technicalServiceCode) {
                return item.companyShortName;
              }
            })
            ?.join("");
          let backflowSquareName = this.myForm.projectList[this.myForm.projectList.length-1].tableItem
            .map((item) => {
              if (item.unitTypeId == backflowSquareCode) {
                return item.companyShortName;
              }
            })
            ?.join("");

          let projectName="";
          this.myForm.projectList.forEach(item=>{
            if(item.isAbbreviation==1){
              let names=item.tableItem.map(
                (item1) => item1.companyShortName
              );
              if(item.companyType=='cust'){
                if(names.length > 1 && names[1]){
                  //若有多个担保公司则加联合融担
                  names=names.join("") + "联合融担-"
                }else{
                  names=names.join("")+'-';
                }
              }else if(item.companyType=='partner'){
                //若有资金方则需检查是否存在技术服务方
                if(technicalServiceName){
                  names=`${names}(${technicalServiceName})-`
                }else{
                  names=names.join("")+'-';
                }
              }
              else if(item.companyType=='fund'){
                if(names.length > 1 && names[1]){
                  //若有多个资产方则加联合贷
                  names=names.join("") + "联合贷-"
                }else{
                  names=names.join("")+'-';
                }
                if(backflowSquareName){
                  //若有资产方则需检查是否存在倒流方
                  names=`${names}${backflowSquareName}-`
                }
              }else{
                names=names.join("")+'-';
              }
              projectName+=names;
            }
          })
          projectName=projectName.replace(/-$/, '');
          this.myForm.projectName=projectName;
         
        }else{
          //非默认规则
          let projectName="";
          this.myForm.projectList.forEach(item=>{
            if(item.companyType!='other'){
              //其他公司不改变项目名称
              if(item.isAbbreviation==1){
                const names=item.tableItem.map(
                  (item1) => item1.companyShortName
                )?.join()+'-';
                projectName+=names;
              }
            }
          })
          projectName=projectName.replace(/-$/, '');
          this.myForm.projectName=projectName;
        }
        this.getRepetitiveProjectName(temp);
      }
    },
    submitCustom(value) {
      this.isCustom = true;
      this.myForm.projectName = value;
    },
    async getRepetitiveProjectName(value) {
      if (value) {
        //发请求校验重复为true
        var dataform = {
          projectName: this.myForm.projectName,
        };
        const { isOk } = await queryData(dataform);
        this.repetitiveProjectName = isOk > 0 ? true : false;
      }
    },
    addCommon(value) {
      const myId = Math.random();
      const obj = {
        myId,
        companyId: undefined,
        companyName: undefined,
        companyShortName: undefined,
        companyType: value.companyType,
        proportion: "1",
        isNecessity: value.isNecessity,
      };
      return obj;
    },
    addList(value){
      const obj = this.addCommon(value);
      this.myForm.projectList.forEach(item=>{
        if(item.companyType==value.companyType){
          item.tableItem.push(obj)
        }
      })
    },
    deletList(value, row){
       value.tableItem = value.tableItem.filter((item) => item.myId != row.myId);
      if (value.tableItem.length == 1) value.tableItem[0].proportion = 100;
    },
    addOtherUnitList(value) {
      const obj = this.addCommon(value);
      obj.unitTypeId = "";
      this.myForm.projectList[this.myForm.projectList.length-1].tableItem.push(obj);
    },
    deletOtherUnitList(value, row) {
      value.tableItem = value.tableItem.filter((item) => item.myId != row.myId);
    },
    openTypeAdd(value) {
      this.bussOrProject = value;

      this.myForm.typeList =
        value == "project"
          ? XEUtils.clone(this.myForm.projectTypeList, true)
          : XEUtils.clone(this.myForm.businessTypeList, true);
      this.openType = true;
    },
    typeCallBack(value) {
      if (this.bussOrProject == "project") {
        const projectTypeList = this.myForm.projectTypeList
          ? this.myForm.projectTypeList.concat(value)
          : [...value];
        this.$set(this.myForm, "projectTypeList", projectTypeList);
      } else if (this.bussOrProject == "business") {
        const businessTypeList = this.myForm.businessTypeList
          ? this.myForm.businessTypeList.concat(value)
          : [...value];
        this.$set(this.myForm, "businessTypeList", businessTypeList);
      }
    },

    deletePro() {
      const params = this.handerFormSubmit();
      this.$emit("deletePro", params);
    },
    // 取消按钮
    cancel() {
      this.$emit("cancel");
    },
    handerFormSubmit() {
      const projectTypeList = this.myForm.projectTypeList?.map((item) => {
        return {
          typeId: item.typeId,
          typeName: item.typeName ,
          dataType: 0,
        };
      });
      const businessTypeList = this.myForm.businessTypeList?.map((item) => {
        return {
          typeId: item.typeId,
          typeName: item.typeName ,
          dataType: 1,
        };
      });
      let otherUnitList = this.myForm.projectList[this.myForm.projectList.length-1].tableItem
        .map((item) => {
          if (item.unitTypeId) {
            const unitTypeName = this.otherUnitTypeListAll.filter(
              (item1) => item1.dictCode == item.unitTypeId
            )[0].dictLabel;
            return {
              unitId: item.companyId,
              unitType: item.companyType,
              proportion: item.proportion,
              unitTypeId: item.unitTypeId,
              isNecessity: item.isNecessity,
              unitShortName: `${unitTypeName}:${
                item.unitShortName || item.companyShortName
              }`,
            };
          }
        })
        .filter((item) => item);
      const tableList = {};
      this.myForm.projectList.map((item) => {
        console.log(item,'---');
        if(item.companyType!='other'){
          tableList[item.companyType+'List']=item.tableItem.map(item1=>{
            return {
              unitId: item1.companyId,
              unitType: item1.companyType,
              proportion: item1.proportion,
              isNecessity: item1.isNecessity,
              unitShortName: item1.companyShortName|| item1.unitShortName ,
            };
          })
        }
      });
      return {
        projectName: this.myForm.projectName,
        projectTypeList,
        businessTypeList,
        isEnable: this.myForm.isEnable || "N",
        id: this.myForm.id || undefined,
        otherUnitList,
        tableList,
        editType: this.myForm.id ? 1 : 0,
        companyNo: this.myForm.companyNo,
        channelType: this.myForm.channelType,
        channelName: this.myForm.channelName,
        creditAmount: this.myForm.creditAmount,
        qudaofangList: this.myForm.qudaofangList,
      };
    },
    /** 提交按钮 */
    submitForm() {
      const params = this.handerFormSubmit();
      this.$emit("submitForm", {
        ...params,
        oaProjectDeployBoOldDataId: this.form.oaProjectDeployBoOldDataId,
      });
    },
    next() {
      const params = this.handerFormSubmit();
      console.log(params);
      let data = {
        oldData: this.oldForm,
        newData: params,
      };

      sessionStorage.setItem("editProData", JSON.stringify(data));
      console.log(data);
      getDataByTemplName({
        templateName: "业务信息配置-修改项目名称申请",
      }).then((res) => {
        if (res.code == 200) {
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              editProject: true,
            },
          });
          this.cancel()
        }
      });
      return;
      this.$emit("next", params);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__error {
  position: absolute;
  top: 8px;
  left: 202px;
}
::v-deep .el-table--medium .el-table__cell {
  padding: 4px 0;
}
.projectTable {
  ::v-deep .el-input__inner {
    position: relative;
    top: 3px;
  }
}
</style>
