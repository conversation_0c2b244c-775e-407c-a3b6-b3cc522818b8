<template>
  <div>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div style="display: flex; justify-content: space-around">
        <div>
          <span style="font-weight: bold">提交人</span>：{{
            detailData.editUserNickName
          }}
        </div>
        <div>
          <span style="font-weight: bold">申请类型</span>：{{
            detailData.applyType == 0
              ? "新增项目名称"
              : detailData.applyType == 1
              ? "修改项目名称"
              : "删除项目名称"
          }}
        </div>
        <div>
          <span style="font-weight: bold">提交时间</span>：{{
            detailData.editTime
          }}
        </div>
      </div>
      <el-divider></el-divider>
      <div v-if="type == 0">
        <div>
          <span style="font-weight: bold">项目名称</span>：{{
            dataDetail.projectName
          }}
        </div>
        <MyTable
          border
          :columns="columnsExamine"
          :source="necessityCompanyList"
          :span-method="({ row, column, rowIndex, columnIndex})=>{return objectSpanMethod({ row, column, rowIndex, columnIndex,data:necessityCompanyList,sortArr:['unitType']})} "
        >
        </MyTable>
        <div class="font-bold" v-if="otherUnitList && otherUnitList.length">
          其他公司：
        </div>
        <MyTable
          v-if="otherUnitList && otherUnitList.length"
          border
          :columns="columnsExamine"
          :source="otherUnitList"
          :show-header="false"
        >
        </MyTable>
        <div class="flex flex-wrap">
          <div class="font-bold">项目类型:</div>
          <div
            v-for="(item1, index1) in dataDetail.projectTypeList"
            :key="index1"
            class="mr-3 flex h-6 items-center"
          >
            <div class="h-8 leading-8">{{ item1.typeName }}</div>
          </div>
        </div>
        <div class="flex flex-wrap">
          <div class="font-bold">业务类型:</div>
          <div
            v-for="(item, index) in dataDetail.businessTypeList"
            :key="index"
            class="mr-3 mb-3 border border-solid rounded-sm px-2 flex h-6 items-center"
            style="border-color: #cccccc; backgroundColor: #f2f2f2"
          >
            <div class="h-6 leading-6">{{ item.typeName }}</div>
          </div>
        </div>

        <div>
          <span style="font-weight: bold">所属公司</span>：
          {{ detailData.companyName }}
        </div>
        <div>
          <span style="font-weight: bold">启用状态</span>：{{
            dataDetail.isEnable == "Y" ? "启用" : "禁用"
          }}
        </div>
        <div>
          <span style="font-weight: bold">新增项目说明</span>：
          {{ detailData.editInfo }}
        </div>
      </div>
      <div class="data_content">
        <div v-if="type == 1">
          <p class="title2">修改内容：</p>
          <div class="table">
            <div class="left">
              <div style="background: #e4e4e4"></div>
              <div>项目名称</div>
              <div
                :style="{
                  height:
                    Math.max(oldData.custList.length, newData.custList.length) *
                      30 +
                    'px',
                }"
              >
                担保公司
              </div>
              <div>资产方</div>
              <div
                :style="{
                  height:
                    Math.max(oldData.fundList.length, newData.fundList.length,1) *
                      30 +
                    'px',
                }"
              >
                资金方
              </div>
              <div
                :style="{
                  height:
                    Math.max(
                      oldData.otherUnitList.length,
                      newData.otherUnitList.length,
                      1
                    ) *
                      30 +
                    'px',
                }"
              >
                其他公司
              </div>
              <div
                :style="{
                  height:
                    Math.max(
                      oldData.projectTypeList.length,
                      newData.projectTypeList.length,
                      1
                    ) *
                      30 +
                    'px',
                }"
              >
                项目类型
              </div>
              <div
                :style="{
                  height:
                    Math.max(
                      oldData.businessTypeList.length,
                      newData.businessTypeList.length,
                      1
                    ) *
                      30 +
                    'px',
                }"
              >
                业务类型
              </div>

              <div>启用状态</div>
            </div>
            <div class="center">
              <div style="background: #e4e4e4">修改前</div>
              <el-tooltip
                class="item"
                effect="dark"
                :content="oldData.projectName"
                placement="top-start"
                style="padding: 0 0 0 10px; margin: 0"
              >
                <div class="truncate ...">{{ oldData.projectName }}</div>
              </el-tooltip>

              <div
                :style="{
                  height:
                    Math.max(oldData.custList.length, newData.custList.length,1) *
                      30 +
                    'px',
                }"
              >
                <div v-for="(item, index) in oldData.custList">
                  {{ item.unitShortName }}
                </div>
              </div>
              <div>{{ oldData.partnerList[0].unitShortName }}</div>
              <div
                :style="{
                  height:
                    Math.max(oldData.fundList.length, newData.fundList.length,1) *
                      30 +
                    'px',
                }"
              >
                <div v-for="(item, index) in oldData.fundList">
                  {{ item.unitShortName }}
                </div>
              </div>
              <div
                :style="{
                  height:
                    Math.max(
                      oldData.otherUnitList.length,
                      newData.otherUnitList.length,
                      1
                    ) *
                      30 +
                    'px',
                }"
              >
                <div v-for="(item, index) in oldData.otherUnitList">
                  {{ item.unitShortName }}
                </div>
              </div>
              <div
                :style="{
                  height:
                    Math.max(
                      oldData.projectTypeList.length,
                      newData.projectTypeList.length,
                      1
                    ) *
                      30 +
                    'px',
                }"
              >
                <div v-for="(item, index) in oldData.projectTypeList">
                  {{ item.typeName }}
                </div>
              </div>
              <div
                :style="{
                  height:
                    Math.max(
                      oldData.businessTypeList.length,
                      newData.businessTypeList.length,
                      1
                    ) *
                      30 +
                    'px',
                }"
              >
                <div v-for="(item, index) in oldData.businessTypeList">
                  {{ item.typeName }}
                </div>
              </div>

              <div>{{ oldData.isEnable == "Y" ? "开启" : "关闭" }}</div>
            </div>
            <div class="right">
              <div style="background: #e4e4e4">修改后</div>
              <el-tooltip
                effect="dark"
                :content="newData.projectName"
                placement="top-start"
                style="padding: 0 0 0 10px; margin: 0"
              >
                <div class="truncate ...">{{ newData.projectName }}</div>
              </el-tooltip>
              <div
                :style="{
                  height:
                    Math.max(oldData.custList.length, newData.custList.length,1) *
                      30 +
                    'px',
                }"
              >
                <div v-for="(item, index) in newData.custList">
                  {{ item.unitShortName }}
                </div>
              </div>
              <div>{{ newData.partnerList[0].unitShortName }}</div>
              <div
                :style="{
                  height:
                    Math.max(oldData.fundList.length, newData.fundList.length,1) *
                      30 +
                    'px',
                }"
              >
                <div v-for="(item, index) in newData.fundList">
                  {{ item.unitShortName }}
                </div>
              </div>
              <div
                :style="{
                  height:
                    Math.max(
                      oldData.otherUnitList.length,
                      newData.otherUnitList.length,
                      1
                    ) *
                      30 +
                    'px',
                }"
              >
                <div v-for="(item, index) in newData.otherUnitList">
                  {{ item.unitShortName }}
                </div>
              </div>
              <div
                :style="{
                  height:
                    Math.max(
                      oldData.projectTypeList.length,
                      newData.projectTypeList.length,
                      1
                    ) *
                      30 +
                    'px',
                }"
              >
                <div v-for="(item, index) in newData.projectTypeList">
                  {{ item.typeName }}
                </div>
              </div>
              <div
                :style="{
                  height:
                    Math.max(
                      oldData.businessTypeList.length,
                      newData.businessTypeList.length,
                      1
                    ) *
                      30 +
                    'px',
                }"
              >
                <div v-for="(item, index) in newData.businessTypeList">
                  {{ item.typeName }}
                </div>
              </div>

              <div>{{ newData.isEnable == "Y" ? "开启" : "关闭" }}</div>
            </div>
          </div>
        </div>
        <div v-if="type == 1">
          <p style="font-weight: bold">修改说明：</p>
          {{ detailData.editInfo }}
        </div>
        <div v-if="type == 2">
          李四申请删除本条记账凭证规则！
          <p style="font-weight: bold">删除原因说明：</p>
          {{ detailData.editInfo }}
        </div>
      </div>

      <div v-if="changeEditType">
        <el-divider></el-divider>
        <p v-if="type == 0">
          您需要为每个项目设置业务责任人和财务责任人，新增项目后，系统会通过待办通知责任人
        </p>
        <p v-if="type == 1">
          每个项目都必须存在业务责任人和财务责任人，当发生修改时，系统会通过待办通知责任人
        </p>
        <p v-if="type == 2">
          每个项目都必须存在业务责任人和财务责任人，删除项目时，系统会通过待办通知责任人
        </p>
        <!-- <div class="item">
          <span><i>*</i>财务负责人</span>
          <el-select
            v-model="form.salesmanList"
            size="mini"
            placeholder="请选择"
            filterable
            multiple=""
          >
            <el-option
              v-for="item in userList.caiwu"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="item">
          <span><i>*</i>业务负责人</span>
          <el-select
            v-model="form.financialStaffList"
            size="mini"
            placeholder="请选择"
            filterable
            multiple=""
          >
            <el-option
              v-for="item in userList.yewu"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit(0)">通过</el-button>
        <el-button
          type="primary"
          @click="dialogVisible2 = true"
          style="background-color: #ff9900; border: none"
          >驳回</el-button
        >
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="审核驳回"
      :visible.sync="dialogVisible2"
      width="800px"
      :before-close="handleClose2"
    >
      <p>请输入驳回原因</p>
      <el-input
        type="textarea"
        placeholder="请输入内容"
        v-model="textarea"
        maxlength="200"
        show-word-limit
      >
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit(1)" :disabled="!textarea"
          >提 交</el-button
        >
        <el-button @click="dialogVisible2 = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import config from "./components/config";
import { getDeploy } from "@/api/oa/deploy";
import tableSpanMethod from "@/mixin/tableSpanMethod";

export default {
  mixins: [tableSpanMethod],
  props: {
    detailData: Object,
    userList: Object,
    changeEditType: Boolean,
    companytypeList: Array,
    companyList: Object,
    examineId: [String, Number],
    examineData: Object,
  },
  data() {
    return {
      ...config,
      dataDetail: null,
      dialogVisible: true,
      dialogVisible2: false,
      oldData: {},
      newData: {},
      type: null,
      textarea: "",
      form: {
        salesmanList: [],
        financialStaffList: [],
      },
      caiwuType: false,
      yewuType: false,
      userId: null,
      necessityCompanyList: [],
      otherUnitList: [],
    };
  },
  mounted() {
    this.userId = Number(sessionStorage.getItem("userId"));
    this.type = this.detailData.applyType;

    this.dataDetail = JSON.parse(this.detailData.oaApplyRecordsNewData);
    this.newData = JSON.parse(this.detailData.oaApplyRecordsNewData);
    this.oldData = JSON.parse(this.detailData.oaApplyRecordsOldData);
    this.getTable();
    this.handleForm();
    this.getCaiYeList();
    if (!this.form.financialStaffList.includes(this.detailData.editUserId)) {
        this.form.financialStaffList.push(this.detailData.editUserId);
      }
    console.log(this.form.financialStaffList);
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleClose2() {
      this.dialogVisible2 = false;
    },
    getCaiYeList(){
      this.form.salesmanList=this.examineData.caiwuList.map(item=>item.id);
      this.form.financialStaffList=this.examineData.yewuList.map(item=>item.id);
    },
    async getTable() {
      if(this.type!=0) return;
      getDeploy(this.examineId).then((response) => {
        const data=response.data;
        this.necessityCompanyList = data.custList.concat(
          data.partnerList.concat(data.fundList)
        );
        const tempObj = {
          0: "担保公司",
          1: "资产方",
          2: "资金方",
        };
        this.necessityCompanyList.forEach((item) => {
          item.unitType = tempObj[item.unitType];
          item.proportion = item.proportion + "%";
          item.unitShortName = item.unitShortName || `暂不确定${item.unitType}`;
        });
        const otherCompanyList = this.companytypeList.filter(
          (item) => !this.paramsLabel.includes(item.dictLabel)
        );
        this.otherUnitList=data.otherUnitList;
        this.otherUnitList.forEach((item) => {
          item.proportion = item.proportion + "%";
          item.unitType = otherCompanyList.filter(
            (item1) => item1.dictCode == item.unitTypeId
          )[0].dictLabel;
          item.unitShortName = item.unitShortName || `暂不确定${item.unitType}`;
        });
        this.detailData.companyName=data.companyName
      });
    },
    handleForm() {
      if(this.type!=1) return;
      const tempArr = ["oldData", "newData"];
      tempArr.forEach((item2) => {
        this[item2].custList.forEach((item) => {
          this.companyList.custNoList.forEach((item1) => {
            if (item.unitId == item1.id) {
              item.unitShortName = item1.companyShortName || "暂不确定担保公司";
            }
          });
        });
        this[item2].partnerList.forEach((item) => {
          this.companyList.partnerNoList.forEach((item1) => {
            if (item.unitId == item1.id) {
              item.unitShortName = item1.companyShortName || "暂不确定资产方";
            }
          });
        });
        this[item2].fundList.forEach((item) => {
          this.companyList.fundNoList.forEach((item1) => {
            if (item.unitId == item1.id) {
              item.unitShortName = item1.companyShortName || "暂不确定资金方";
            }
          });
        });
        this[item2].otherUnitList.forEach((item) => {
          this.companytypeList.forEach((item1) => {
            if (item1.dictCode == item.unitTypeId) {
              item.unitShortName = `${
                item.unitShortName || "暂不确定" + item1.dictLabel
              }`;
            }
          });
        });
      });
    },
    submit(v) {
      if (
        (this.form.salesmanList.length == 0 ||
          this.form.financialStaffList.length == 0) &&
        v == 0
      ) {
        this.$message.warning("请添加财务负责人");
        return;
      }
      if (!this.form.financialStaffList.includes(this.detailData.editUserId)) {
        this.form.financialStaffList.push(this.detailData.editUserId);
      }
      this.$emit("submit", v, this.textarea, this.form);
    },

  },
};
</script>

<style lang="less" scoped>
.item {
  margin-bottom: 12px;
  /deep/ .el-input__inner {
    width: 250px !important;
  }
  span {
    display: inline-block;
    width: 100px;
    font-weight: bold;
    text-align: right;
    margin-right: 12px;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
.data_content {
  width: 95%;
  margin: 0 auto;
  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 16px;
  }
  .table {
    display: flex;
    justify-content: space-between;
    border: 1px solid #cccccc;
    border-bottom: none;
    .left,
    .center,
    .right {
      width: 45%;
      > div {
        height: 30px;
        width: 100%;
        border-right: 1px solid #ccc;
        text-align: center;
        line-height: 30px;
        border-bottom: 1px solid #ccc;
        text-align: left;
        padding-left: 10px;
      }
    }
    .left div {
      font-weight: bold;
    }
    .left {
      width: 10%;
    }
  }
}
</style>
