export default {
  columns:Object.freeze([
    { label: "产品名称", prop: "productName", minWidth: "220" },
    { label: "产品编码", prop: "productNo", minWidth: "120" },
    { label: "系统", key: "systemNo", minWidth: "120" },
    { label: "项目名称", prop: "projectName", minWidth: "220" },
    { label: "说明", prop: "description", minWidth: "580" },
    { label: "审核状态", key: "checkStatus", minWidth: "130" },
    { label: "启用状态", key: "status", minWidth: "120" },
    { label: "操作", key: "operate", fixed: "right", align: "left", minWidth: "240" },
  ]),
  statusList: Object.freeze([
    { label: "正常", value: "0", raw: { listClass: 'primary' } },
    { label: "停用", value: "1", raw: { listClass: 'danger' } },
  ]),
  rules: Object.freeze( {
    projectId: [
      { required: true, message: "请输入项目名称", trigger: "blur" },
    ],
    productName: [
      { required: true, message: "请输入产品名称", trigger: "blur" },
    ],
    productNo: [
      { required: true, message: "请输入产品编码", trigger: "blur" },
    ],
    systemNo: [
      { required: true, message: "请选择系统", trigger: "blur" },
    ],
    
  }),
 
};
