<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="98px"
    >
      <el-form-item label="人员姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入人员姓名"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属部门" prop="deptId">
        <treeselect
          v-model="queryParams.deptId"
          :options="deptList"
          style="width: 240px"
          :normalizer="normalizer"
          noOptionsText="暂无数据"
          :show-count="true"
          placeholder="请输入/选择所属部门"
        />
      </el-form-item>
      <el-form-item label="奖惩类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择审核状态"
          clearable
          @clear="handleQuery"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.bonus_penalty_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="奖惩事由" prop="reason">
        <el-input
          v-model="queryParams.reason"
          placeholder="请输入奖惩事由"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="奖惩金额(元)" prop="amount">
        <el-input
          v-model="queryParams.amountMin"
          placeholder="请输入金额"
          clearable
          size="small"
          style="width: 120px"
          @input="
            queryParams.amountMin = queryParams.amountMin.replace(/[^\d.]/g, '')
          "
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
        <span> 至 </span>
        <el-input
          v-model="queryParams.amountMax"
          placeholder="请输入金额"
          clearable
          size="small"
          style="width: 120px"
          @input="
            queryParams.amountMax = queryParams.amountMax.replace(/[^\d.]/g, '')
          "
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRangeCreate"
          value-format="yyyy-MM-dd"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择审核状态"
          clearable
          @clear="handleQuery"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.check_work_approve_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="生效状态" prop="effective">
        <el-select
          v-model="queryParams.effective"
          placeholder="请选择生效状态"
          clearable
          @clear="handleQuery"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in effectiveList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核完成时间">
        <el-date-picker
          v-model="auditCompletionTime"
          value-format="yyyy-MM-dd"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-divider></el-divider>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['bonusPenalty:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="add"
          >新建</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['bonusPenalty:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出列表</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          size="mini"
          @click="batchSubmit"
          :disabled="multipleSelection.length == 0"
          >批量提交奖惩申请</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button @click="openSelect = true" type="primary" size="mini"
          >已选择({{ multipleSelection.length }})条</el-button
        >
      </el-col>

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table
      :data="configList"
      @selection-change="handleSelectionChange"
      row-key="id"
      ref="multipleTable"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        fixed="left"
        reserve-selection
      />
      <el-table-column
        type="index"
        label="序号"
        width="50"
        fixed="left"
        :index="columnIndex"
      />

      <el-table-column label="人员姓名" align="center" width="130" fixed="left">
        <template #default="{ row }">
          <el-button size="mini" type="text" @click="getUserData(row)">{{
            row.nickName
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="奖惩单号" align="center" width="130">
        <template #default="{ row }">
          <el-button size="mini" type="text" @click="viewProcessId(row)">{{
            row.rewardPunishmentCode
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="所属部门"
        align="center"
        prop="deptChain"
        width="260"
      />
      <el-table-column label="奖惩类型" align="center" width="120">
        <template #default="{ row }">
          <div>
            {{ dict.label.bonus_penalty_type[row.type] }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="奖惩金额/物品" align="center" width="280">
        <template #default="{ row }">
          <div v-if="row.measure == 1">
            <div v-if="row.type == 1" style="color: #d9001b">
              +{{ row.amount }}元
            </div>
            <div v-else style="color: #bfbf00">-{{ row.amount }}元</div>
          </div>
          <div v-else>{{ row.itemName }}×{{ row.itemNum }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="奖惩事由"
        align="center"
        prop="reason"
        min-width="400"
      />
      <el-table-column label="创建人" align="center" width="130">
        <template #default="{ row }">
          <el-button size="mini" type="text" @click="getUserDataCreate(row)">{{
            row.createNickName
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="160"
      />

      <el-table-column label="审核状态" align="center" width="120">
        <template #default="{ row }">
          <div>
            {{ dict.label.check_work_approve_status[row.status] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="生效状态" align="center" width="120">
        <template #default="{ row }">
          <div>
            {{ effectiveObj[row.effective] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="审核完成时间"
        align="center"
        prop="auditCompletionTime"
        width="160"
      />
      <el-table-column label="操作" align="center" fixed="right" width="180px">
        <template #default="{ row }">
          <el-button
            size="mini"
            type="text"
            @click="submit(row)"
            v-if="['1'].includes(row.status)"
            >提交</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleUpdateOpear(row)"
            v-hasPermi="['bonusPenalty:update']"
            v-if="['1', '4'].includes(row.status)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['bonusPenalty:delete']"
            size="mini"
            type="text"
            style="color: red"
            @click="handleDelete(row)"
            v-if="['4'].includes(row.status)"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="viewProcessId(row)"
            v-if="['2', '3'].includes(row.status)"
            >查看流程</el-button
          >
          <el-dropdown @command="handleCommand($event, row)" class="ml-2">
            <span class="el-dropdown-link mr-2" style="font-size: 14px">
              >>更多
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="submit"
                v-if="['4'].includes(row.status)"
                >提交</el-dropdown-item
              >

              <el-dropdown-item
                command="view"
                v-if="['1', '4'].includes(row.status)"
                >查看详情</el-dropdown-item
              >
              <el-dropdown-item
                v-hasPermi="['bonusPenalty:cancel']"
                command="cancel"
                v-if="
                  ['2', '3'].includes(row.status) &&
                  row.createBy == $store.getters.name &&
                  row.effective == 0
                "
                >作废奖惩申请</el-dropdown-item
              >
              <el-dropdown-item
                v-hasPermi="['bonusPenalty:exportWord']"
                command="exportWord"
                v-if="['1', '2', '3'].includes(row.status)"
                >导出word表单</el-dropdown-item
              >
              <el-dropdown-item
                v-hasPermi="['bonusPenalty:delete']"
                command="delete"
                style="color: red"
                v-if="['1'].includes(row.status)"
                >删除</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <TableSelect
      :tableData="multipleSelection"
      v-model="openSelect"
      @on-submit-success-row="submitDelet"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
    <UserDetail
      v-model="userDetailType"
      @close="userDetailType = false"
      :id="userId"
    />
    <DetailDialog v-model="detailDialog" :form="detailForm" />
    <DetailDialogVoid
      v-model="detailDialogVoid"
      @close="detailDialogVoid = false"
      :form="detailDialogVoidForm"
    />
  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import {
  punishmentList,
  deletePunishment,
  getRewardsPunishment,
  getRewardsPunishmentList,
} from "@/api/checkWork/bonusPenalty";
import { treeselect } from "@/api/system/dept";
import DetailDialog from "./components/DetailDialog.vue";
import DetailDialogVoid from "./components/DetailDialogVoid.vue";
import tableSelect from "@/mixin/table-select";
import config from "./components/config";
import TableSelect from "./components/TableSelect";
import { clone } from "xe-utils";
export default {
  name: "BonusPenalty",
  components: { Treeselect, TableSelect, DetailDialog, DetailDialogVoid },
  mixins: [tableSelect],
  dicts: ["bonus_penalty_type", "check_work_approve_status"],
  data() {
    return {
      ...config,
      // 显示搜索条件
      showSearch: true,
      // 参数表格数据
      configList: [],
      // 查询参数
      total: 0,
      dateRangeCreate: [],
      auditCompletionTime: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: "",
        deptId: undefined,
        type: "",
        reason: "",
        amountMin: "",
        amountMax: "",
        status: "",
        effective: "",
      },
      deptList: [],
      openSelect: false,
      multipleSelection: [],
      selectCompanyType: false,
      userDetailType: false,
      userId: "",
      multipleSelectionAdd: null,
      detailDialog: false,
      detailForm: {},
      detailDialogVoidForm: {},
      detailDialogVoid: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getdeptList();
      this.getList();
    },
    async getdeptList() {
      const { data } = await treeselect();
      this.deptList = data;
    },
    /** 查询参数列表 */
    async getList() {
      const { rows, total } = await punishmentList(this.getParams());
      this.configList = rows;
      this.total = total;
    },
    getParams() {
      const params = clone(this.queryParams);
      params.createTimeStart = this.dateRangeCreate && this.dateRangeCreate[0];
      params.createTimeEnd = this.dateRangeCreate && this.dateRangeCreate[1];
      params.auditCompletionTimeStart =
        this.auditCompletionTime && this.auditCompletionTime[0];
      params.auditCompletionTimeEnd =
        this.auditCompletionTime && this.auditCompletionTime[1];
      return params;
    },
    columnIndex(index) {
      return (
        index + 1 + (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      );
    },
    submitDelet(e) {
      e.forEach((row) => {
        this.$refs.multipleTable.toggleRowSelection(row, false);
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.amountMin = "";
      this.queryParams.amountMax = "";
      this.dateRangeCreate = [];
      this.auditCompletionTime = [];
      this.handleQuery();
    },
    add() {
      this.$router.push({
        path: "/checkWorkOther/bonusPenaltyAdd/add",
        query: {
          type: "add",
          title: "新增奖惩申请",
        },
      });
    },
    handleUpdateOpear(row) {
      this.$router.push({
        path: "/checkWorkOther/bonusPenaltyAdd/" + row.id,
        query: {
          id: row.id,
          type: "update",
          title: row.createNickName + "修改奖惩申请",
        },
      });
    },
    handleCommand(command, record) {
      const obj = {
        submit: this.submit,
        view: this.view,
        cancel: this.cancel,
        exportWord: this.exportWord,
        delete: this.handleDelete,
      };
      obj[command](record);
    },
    exportWord(row) {
      this.download(
        `/reward/punishment/export/word/${row.id}`,
        {
          ...this.getParams(),
        },
        `${row.rewardPunishmentCode}.docx`,
        "get"
      );
    },
    view(row) {
      this.detailForm = row;
      this.detailDialog = true;
    },
    cancel(row) {
      this.detailDialogVoidForm = row;
      this.detailDialogVoid = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = [row.id];
      const idNames = row.nickName;
      this.$modal
        .confirm('是否确认删除人员姓名为"' + idNames + '"的数据项？')
        .then(function () {
          return deletePunishment(ids);
        })
        .then(async () => {
          await this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    getUserData(data) {
      this.userId = data.userId;
      this.userDetailType = true;
    },
    getUserDataCreate(data) {
      this.userId = data.createUserId;
      this.userDetailType = true;
    },
    batchSubmit() {
      let temp = false;
      this.multipleSelection.some((item) => {
        if (item.status == 2 || item.status == 3) {
          this.$modal.msgWarning("请选择未提交或审核不通过的数据");
          temp = true;
          return false;
        }
      });
      if (temp) return;
      this.multipleSelectionAdd = null;
      this.selectCompanyType = true;
    },
    async submit(data) {
      this.submitUpdata(data);
    },
    viewProcessId(value) {
      if (!value.processId) {
        return;
      }
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: value.processId,
          businessId: value.processId,
        },
      });
    },
    async submitUpdata(value) {
      const processId = value?.processId;
      if (processId && value.status != 4) {
        this.$router.push({
          path: "/oaWork/processFormView",
          query: {
            oid: processId,
            myActiviteType: true,
          },
        });
        return;
      }
      try {
        this.multipleSelectionAdd = value;
        this.selectCompanyType = true;
      } catch (error) {}
    },
    submitCompany(e) {
      const isMuiltiple =
        this.multipleSelectionAdd ||
        (this.multipleSelection && this.multipleSelection.length == 1)
          ? false
          : true;
      let api = isMuiltiple ? getRewardsPunishmentList : getRewardsPunishment;
      api({ companyId: e }).then((res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          //单个流程多个流程存入数据不同
          const data =
            this.multipleSelectionAdd ||
            (this.multipleSelection.length == 1
              ? this.multipleSelection[0]
              : this.multipleSelection);
          sessionStorage.setItem(
            "oa-checkWorkBonusPenalty",
            JSON.stringify(data)
          );
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              checkWorkBonusPenalty: isMuiltiple ? "" : true,
              checkWorkBonusPenaltyList: isMuiltiple ? true : "",
            },
          });
        }
      });
    },
    closeCompany() {
      this.multipleSelectionAdd = null;
      this.selectCompanyType = false;
      this.getList();
    },
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    handleExport() {
      this.download(
        "reward/punishment/export",
        {
          ...this.getParams(),
          ids: this.multipleSelection?.map((item) => item.id)?.join(","),
        },
        `奖惩申请.xlsx`
      );
    },
  },
};
</script>
<style lang="less" scoped>
</style>
