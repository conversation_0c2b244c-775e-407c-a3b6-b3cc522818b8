<template>
  <div class="app-container">
    <div class="flex">
      <div style="font-size:14px;font-weight:700;line-height: 36px;margin-right: 5px;">展示维度</div>
      <div class="mr-2">
        <el-tabs v-model="showType" type="card" @tab-click="handleClick">
          <el-tab-pane label="列表" name="list"></el-tab-pane>
          <el-tab-pane label="图表" name="chart"></el-tab-pane>
        </el-tabs>
      </div>
      <div v-hasPermi="['logSearch:groupSet']">
        <el-button
          type="text"
          @click="$router.push('/checkWorkOther/groupingSet')"
          >快捷分组设置</el-button
        >
        <el-tooltip
          class="item"
          effect="dark"
          content="点击“快捷分组配置”按钮，进行用户配置后，可生成快捷查看按钮，点击此按钮即可直接查看对应用户的日志"
          placement="top-start"
        >
          <i class="el-icon-question cursor-pointer relative bottom-1"></i>
        </el-tooltip>
      </div>
    </div>
    <el-divider class="mt-0"></el-divider>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="98px"
    >
      <el-form-item label="员工姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          autocomplete="new-password"
          placeholder="请输入员工姓名"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          @focus.stop
        />
      </el-form-item>
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          autocomplete="new-password"
          placeholder="请输入部门名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          @focus.stop
        />
      </el-form-item>
      <el-form-item label="日志关键字" prop="logContent">
        <el-input
          v-model="queryParams.logContent"
          autocomplete="new-password"
          placeholder="请输入日志关键字"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          @focus.stop
        />
      </el-form-item>
      <el-form-item label="日志所属日期" v-show="showType == 'list'">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          @change="handleQuery"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          @focus.prevent
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="日志填报时间" v-show="showType == 'list'">
        <el-date-picker
          v-model="timesRange"
          type="datetimerange"
          @change="handleQuery"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="pickerOptions"
          @focus.prevent
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="分组搜索" prop="groupId" v-show="showType == 'list'">
        <el-select
          v-model="queryParams.groupId"
          autocomplete="new-password"
          placeholder="请输入/选择分组名称"
          clearable
          filterable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          @focus.stop
        >
          <el-option
            v-for="item in groupList"
            :key="item.id"
            :label="item.groupName"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出设置</el-button
        >
      </el-form-item>
    </el-form>
    <div v-show="showType == 'list'">
      <MyTable :columns="columns" :source="configList">
        <template #nickName="{ record }">
          <el-button type="text" @click="onUserDetail(record)">{{
            record.nickName
          }}</el-button>
        </template>
        <template #dayLogList="{ record }">
          <div
            v-for="(item, index) in record.dayLogList"
            :key="index"
            class="mb-3"
          >
            <div>时间: {{ item.startTime }}-{{ item.endTime }}</div>
            <div v-show="item.dataFormat == '1'">
              内容: {{ item.logContent }}
            </div>
            <div v-show="item.dataFormat == '2'" class="flex">
              <div style="flex-shrink: 0">内容:</div>
              <div>
                <div
                  v-for="(item1, index1) in item.logContentList.filter(
                    (item) => item.logContent
                  )"
                  :key="index1"
                >
                  {{ index1 + 1 }}. {{ item1.logContent }}
                </div>
              </div>
            </div>
          </div>
        </template>
        <template #operate="{ record }">
          <el-button type="text" @click="onDetaill(record)">查看详情</el-button>
        </template>
      </MyTable>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <ExportlDialog v-model="openExport" />
    <div v-if="showType == 'chart'">
      <Chart
        :queryParams="queryParams"
        :groupList="groupList"
        @onDetaill="onDetaill"
        @changList="changList"
      />
    </div>
    <div v-if="open">
      <DetailDialog
        formTitle="日志填报详情"
        :form="form"
        v-model="open"
        type="view"
      />
    </div>
    <LogDetailDialog :userDetail="userDetail" v-model="openUserDetail" />
  </div>
</template>

<script>
import { groupList } from "@/api/checkWork/groupingSet";
import { getDayLogListOfLeader } from "@/api/checkWork/log";
import { treeselect } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import DetailDialog from "../components/DetailDialogLog.vue";
import ExportlDialog from "./components/exportDialog.vue";
import LogDetailDialog from "./components/logDetailDialog.vue";
import Chart from "./components/chart.vue";
import config from "./components/config";

export default {
  name: "LogSearch",
  components: { Treeselect, DetailDialog, Chart, ExportlDialog,LogDetailDialog },
  data() {
    return {
      ...config,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 参数表格数据
      configList: [],
      // 查询参数
      dateRange: undefined,
      timesRange: undefined,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: undefined,
        deptName: undefined,
        groupId: "",
        logContent: "",
      },
      total: 0,
      deptOptions: [],
      open: false,
      form: {},
      openUserDetail: false,
      userDetail: {},
      groupList: [],
      showType: "list",
      openExport: false,
    };
  },
  watch: {},
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
      this.getTreeselect();
      this.getGroupList();
    },
    getGroupList() {
      groupList().then((res) => {
        this.groupList = res.rows;
      });
    },
    /** 查询参数列表 */
    async getList() {
      this.loading = true;
      const timesParam = {
        reportTimeStart: this.timesRange ? this.timesRange[0] : undefined,
        reportTimeEnd: this.timesRange ? this.timesRange[1] : undefined,
        logDateStart: this.dateRange ? this.dateRange[0] : undefined,
        logDateEnd: this.dateRange ? this.dateRange[1] : undefined,
      };
      const { rows, total } = await getDayLogListOfLeader({
        ...this.queryParams,
        ...timesParam,
      });
      this.configList = rows;
      this.total = total;

      this.loading = false;
    },
    async getTreeselect() {
      const { data } = await treeselect();
      this.deptOptions = [...data];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    handleClick() {},
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.dateRange = undefined;
      this.timesRange = undefined;
      this.handleQuery();
    },
    changList(){
      this.showType='list';
    },
    onUserDetail(row) {
      this.userDetail=row;
      this.openUserDetail = true;
    },
    onDetaill(row) {
      this.form = { ...row };
      this.form.dayLogList.forEach((item) => {
        this.$set(item, "isDelete", 1);
      });
      this.$set(this.form, "date", this.form.logDate);
      this.open = true;
    },
    handleExport() {
      this.openExport = true;
    },
  },
};
</script>
