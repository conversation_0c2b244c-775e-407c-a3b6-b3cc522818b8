<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="formTitle"
      :visible.sync="innerValue"
      width="1150px"
      @close="handleClose"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div style="max-height: 60vh" class="pr-10">
          <Form
            :editeForm="editeForm"
            :formTitle="formTitle"
            ref="form"
            :key="formKey"
          />
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button
            type="primary"
            @click="onSave"
            v-if="formTitle != '查看加班申请'"
            >保存</el-button
          >
          <el-button
            type="primary"
            @click="onSubmit"
            v-if="formTitle != '查看加班申请'"
            >保存并提交</el-button
          >
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import Form from "./Form.vue";
import { toDateString, clone } from "xe-utils";
import { addOvertime } from "@/api/checkWork/overTime";
export default {
  mixins: [vModelMixin],
  components: { Form },
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    formTitle: {
      type: String,
      required: true,
      default: "",
    },
  },
  data() {
    return {
      editeForm: {},
      formKey: Math.random(),
    };
  },
  mounted() {},
  methods: {
    handleOpen() {
      this.init();
    },
    async init() {
      this.formKey = Math.random();
      this.editeForm = clone(this.form, true);
      this.$nextTick(() => {
        this.$refs.form.$refs.form.clearValidate();
      });
    },

    handelParams() {
      function getDateTimeStamp(dateString, timeString) {
        return new Date(dateString + "T" + timeString).getTime();
      }
      // 遍历每个时间段对象
      for (let i = 0; i < this.editeForm.workOvertimeSlaveList.length; i++) {
        // 设置默认 repeat 属性为 false
        this.$set(this.editeForm.workOvertimeSlaveList[i], "repeat", false);
        // 将开始时间和结束时间转换为时间戳
        const startDateTime = getDateTimeStamp(
          this.editeForm.workOvertimeSlaveList[i].startTime,
          this.editeForm.workOvertimeSlaveList[i].startTimePeriod
        );
        const endDateTime = getDateTimeStamp(
          this.editeForm.workOvertimeSlaveList[i].endTime,
          this.editeForm.workOvertimeSlaveList[i].endTimePeriod
        );
        // 再次遍历其他时间段对象，与当前对象比较
        for (let j = 0; j < this.editeForm.workOvertimeSlaveList.length; j++) {
          if (i !== j) {
            // 不和自己比较
            // 将开始时间和结束时间转换为时间戳
            const compareStartDateTime = getDateTimeStamp(
              this.editeForm.workOvertimeSlaveList[j].startTime,
              this.editeForm.workOvertimeSlaveList[j].startTimePeriod
            );
            const compareEndDateTime = getDateTimeStamp(
              this.editeForm.workOvertimeSlaveList[j].endTime,
              this.editeForm.workOvertimeSlaveList[j].endTimePeriod
            );

            // 检查时间段是否重叠的条件
            if (
              (startDateTime < compareEndDateTime &&
                endDateTime > compareStartDateTime) ||
              (compareStartDateTime < endDateTime &&
                compareEndDateTime > startDateTime)
            ) {
              // 如果时间段重叠，则设置 repeat 属性为 true
              this.$set(
                this.editeForm.workOvertimeSlaveList[i],
                "repeat",
                true
              );
              break; // 一旦发现重叠，停止检查
            }
          }
        }
      }
      const isRepeat = this.editeForm.workOvertimeSlaveList.some(
        (item) => item.repeat
      );

      return {
        params: this.editeForm,
        isRepeat,
      };
    },
    onSave() {
      this.$refs.form.$refs.form.validate(async (valid) => {
        if (valid) {
          const { params, isRepeat } = this.handelParams();
          if (isRepeat) return;
          await addOvertime(params);
          this.innerValue = false;
          this.$modal.msgSuccess("操作成功");
          this.$emit("on-save-success");
        }
      });
    },
    onSubmit() {
      this.$refs.form.$refs.form.validate(async (valid) => {
        if (valid) {
          const { params, isRepeat } = this.handelParams();
          if (isRepeat) return;
          const { data } = await addOvertime(params);
          this.innerValue = false;
          this.$emit("on-submit-success", { ...data });
        }
      });
    },
    handleClose() {
      this.$refs.form.$refs.form.resetFields();
    },
  },
};
</script>

