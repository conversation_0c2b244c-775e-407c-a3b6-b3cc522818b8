<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="新增月报评审"
      :visible.sync="innerValue"
      width="1150px"
      @close="handleClose"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div class="content">
          <div class="table_search">
            <div>
              <el-select
                v-model="editeForm.monthShow"
                class="month"
                @change="changeMonth"
              >
                <el-option
                  v-for="item in monthOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="table_title">
            <el-form :model="editeForm" :rules="rules" ref="form">
              <div>{{ editeForm.logDateShow }}工作量统计表</div>
              <div class="table_title_content">
                <div>姓名: {{ editeForm.nickName }}</div>
                <div>岗位: {{ editeForm.postName }}</div>
                <div>部门: {{ editeForm.deptName }}</div>
                <div class="flex">
                  <div>考核分数:</div>
                  <el-form-item prop="checkScore">
                    <el-input
                      :disabled="editeForm.isShow !== 'review'"
                      v-model="editeForm.checkScore"
                      class="input"
                      oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/\D/g,'');if(value>100)value=100;if(value<0)value=null"
                    ></el-input>
                  </el-form-item>
                </div>
                <div class="flex">
                  <div>复核分数:</div>
                  <el-form-item prop="reviewScore">
                    <el-input
                      disabled
                      v-model="editeForm.reviewScore"
                      class="input"
                      oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/\D/g,'');if(value>100)value=100;if(value<0)value=null"
                    ></el-input>
                    <!-- <el-input
                      :disabled="(editeForm.isShow !== 'reexamine')||(!checkPermi(['checkWork:review:reexamine']))"
                      v-model="editeForm.reviewScore"
                      class="input"
                      oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/\D/g,'');if(value>100)value=100;if(value<0)value=null"
                    ></el-input> -->
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <el-table :data="editeForm.logList">
            <el-table-column label="工作角色" align="center" width="100">
              <template #default="{ row }">
                <div size="mini" type="text">
                  {{ dict.label.check_work_role[row.jobRole] }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="工作内容" align="left" prop="workContent" />
            <el-table-column label="完成情况" align="center" width="100">
              <template #default="{ row }">
                <div size="mini" type="text">{{ row.complete }}%</div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button
            type="primary"
            @click="onSubmit"
            v-if="editeForm.isShow != 'detail'"
            >提交</el-button
          >
          <el-button
            type="primary"
            @click="onSave"
            v-if="editeForm.isShow != 'detail'"
            >保存</el-button
          >
          <el-button @click="back" v-if="editeForm.isShow != 'detail'"
            >退回</el-button
          >
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import { logmMin, getMonthLogMainByUserId } from "@/api/checkWork/review";
import { checkPermi} from "@/utils/permission"; // 权限判断函数
import XEUtils from "xe-utils";
export default {
  mixins: [vModelMixin],
  dicts: ["check_work_role"],

  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      ...config,
      editeForm: {},
      rules: {},
    };
  },
  mounted() {},
  methods: {
    checkPermi,
    handleOpen() {
      this.init();
    },
    async init() {
      this.handlInitEditeForm();
      this.updateResetFields();
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    updateResetFields() {
      if (this.editeForm.isShow == "review") {
        this.rules = {
          checkScore: [
            { required: true, message: "请输入考核分数", trigger: "blur" },
          ],
        };
      } else if (this.editeForm.isShow == "reexamine") {
        this.rules = {
          reviewScore: [
            { required: true, message: "请输入复核分数", trigger: "blur" },
          ],
        };
      } else {
        this.rules = {};
      }
    },
    handlInitEditeForm() {
      this.editeForm = JSON.parse(JSON.stringify(this.form));
      this.$set(
        this.editeForm,
        "monthShow",
        this.editeForm.logDate?.split("-")[1]
      );
      if(!this.editeForm.isCheck){
        this.editeForm.checkScore=this.editeForm.localScore;
      }
    },
    async changeMonth() {
      const time = `${this.editeForm.logDate?.split("-")[0]}-${
        this.editeForm.monthShow
      }`;
      const params = {
        userId: this.editeForm.userId,
        logDate: time,
      };
      const { data } = await getMonthLogMainByUserId(params);
      this.editeForm.logList = data.logList;
    },
    getParams() {
      if (this.editeForm.isShow == "review") {
        return {
          checkScore:this.editeForm.isCheck? this.editeForm.checkScore:undefined,
          localScore:this.editeForm.isCheck?undefined: this.editeForm.checkScore,
          auditingTime: XEUtils.toDateString(new Date()),
          id: this.editeForm.id,
        };
      } else if (this.editeForm.isShow == "reexamine") {
        return {
          reviewScore: this.editeForm.reviewScore,
          id: this.editeForm.id,
        };
      }
    },
    getParamsSubmit() {
      if (this.editeForm.isShow == "review") {
        return {
          auditingStatus:'2',
          ...this.getParams(),
        };
      } else if (this.editeForm.isShow == "reexamine") {
        return {
          reviewStatus:'2',
          ...this.getParams(),
        };
      }
    },
    getParamsBack() {
      if (this.editeForm.isShow == "review") {
        return {
          reportingStatus:'1',
          ...this.getParams(),
        };
      } else if (this.editeForm.isShow == "reexamine") {
        return {
          auditingStatus:'1',
         ...this.getParams(),
        };
      }
    },
    onSave() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const params = this.getParams();
          await logmMin(params);
          this.innerValue = false;
          this.$modal.msgSuccess("操作成功");
          this.$emit("on-save-success");
        }
      });
    },
    onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const params = this.getParamsSubmit();
          await logmMin(params);
          this.innerValue = false;
          this.$modal.msgSuccess("操作成功");
          this.$emit("on-submit-success");
        }
      });
    },
    async back() {
      const params = this.getParamsBack();
      await logmMin(params);
      this.innerValue = false;
      this.$modal.msgSuccess("操作成功");
      this.$emit("on-back-success");
    },
    handleClose() {
      this.$refs.form.resetFields();
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  max-height: 70vh;
  padding: 0px 40px;
  // overflow-y: auto;
  .table_search {
    display: flex;
    .month {
      width: 120px;
      ::v-deep .el-input__inner {
        border: none;
        font-size: 22px;
        font-weight: 700;
        color: black;
      }
      ::v-deep .el-select__caret {
        position: relative;
        left: -7px;
        top: 5px;
      }
    }
  }
  .table_title {
    text-align: center;
    font-size: 22px;
    font-weight: 700;
    color: black;
    .table_title_content {
      display: flex;
      font-size: 18px;
      justify-content: center;
      > div {
        margin: 20px 0;
        margin-right: 40px;
        font-weight: 500;
        .input {
          width: 60px;
          position: relative;
          left: 5px;
          top: -2px;
        }
      }
    }
    ::v-deep .el-input__inner {
      height: 26px;
    }
    ::v-deep .el-form-item__error {
      font-size: 10px;
      width: 80px;
    }
  }
  .flex {
    display: flex;
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>