<template>
  <div>
    <el-dialog
      title="编辑内容"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <div class="data_content">
        <div class="table">
          <div class="left">
            <div style="background: #e4e4e4"></div>
            <div>收款人类型</div>
            <div>收款人</div>
            <div>开户行</div>
            <div>账号</div>
            <div>简称</div>
            <div>启用状态</div>
          </div>
          <div class="center">
            <div style="background: #e4e4e4">修改前</div>
            <div>{{ oldData.type == 0 ? "公司" : "个人" }}</div>
            <el-tooltip
              class="item"
              effect="dark"
              :content="oldData.userName"
              placement="top-start"
            >
              <div>{{ oldData.userName }}</div>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="oldData.bankOfDeposit"
              placement="top-start"
            >
              <div>{{ oldData.bankOfDeposit }}</div>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="oldData.accountNumber"
              placement="top-start"
            >
              <div>{{ oldData.accountNumber }}</div>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="oldData.abbreviation"
              placement="top-start"
            >
              <div>{{ oldData.abbreviation }}</div>
            </el-tooltip>

            <div>{{ oldData.isEnable == "Y" ? "开启" : "关闭" }}</div>
          </div>
          <div class="right">
            <div style="background: #e4e4e4">修改后</div>
            <div>{{ newData.type == 0 ? "公司" : "个人" }}</div>
            <el-tooltip
              class="item"
              effect="dark"
              :content="newData.userName"
              placement="top-start"
            >
              <div>{{ newData.userName }}</div>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="newData.bankOfDeposit"
              placement="top-start"
            >
              <div>{{ newData.bankOfDeposit }}</div>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="newData.accountNumber"
              placement="top-start"
            >
              <div>{{ newData.accountNumber }}</div>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              :content="newData.abbreviation"
              placement="top-start"
            >
              <div>{{ newData.abbreviation }}</div>
            </el-tooltip>

            <div>{{ newData.isEnable == "Y" ? "开启" : "关闭" }}</div>
          </div>
        </div>
        <p class="title2">修改说明：{{ detail.editInfo }}</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    detail: Object,
  },
  mounted() {
    this.newData = JSON.parse(this.detail.oaApplyRecordsNewData);
    this.oldData = JSON.parse(this.detail.oaApplyRecordsOldData);
  },
  data() {
    return {
      dialogVisible: true,
      newData: {},
      oldData: {},
    };
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.data_content {
  width: 650px;
  margin: 0 auto;
  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 16px;
  }
  .table {
    display: flex;
    justify-content: space-between;
    border: 1px solid #cccccc;
    border-bottom: none;
    .left,
    .center,
    .right {
      width: 33.33%;
      div {
        height: 30px;
        width: 100%;
        border-right: 1px solid #ccc;
        text-align: center;
        line-height: 30px;
        border-bottom: 1px solid #ccc;
        text-align: left;
        padding-left: 10px;
        white-space: nowrap;
        /*2. 超出的部分隐藏*/
        overflow: hidden;
        /*3. 文字用省略号替代超出的部分*/
        text-overflow: ellipsis;
        margin: 0 !important;
      }
    }
    .left div {
      font-weight: bold;
    }
  }
}
</style>