<template>
  <div>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <div style="display: flex; justify-content: space-around">
        <div>
          <span style="font-weight: bold">提交人</span>：{{
            detailData.editUserNickName
          }}
        </div>
        <div>
          <span style="font-weight: bold">提交身份人</span>：{{
            detailData.identity
          }}
        </div>
        <div>
          <span style="font-weight: bold">提交时间</span>：{{
            detailData.editTime
          }}
        </div>
      </div>
      <el-divider></el-divider>
      <div v-if="type == 1">
        <p style="font-weight: bold">修改内容：</p>
        <div class="table" v-if="data1 && data2">
          <div class="left">
            <div style="background: #e4e4e4"></div>
            <div>公司</div>
            <div>流程模板</div>
            <div>账套</div>
            <div>备注说明</div>
          </div>
          <div class="center">
            <div style="background: #e4e4e4">修改前</div>
            <div>{{ company(data1.companyNo) }}</div>
            <div>{{ data1.flowModelName }}</div>
            <div>{{ accountSets(data1.accountSetsId) }}</div>
            <div>{{ data1.remark }}</div>
          </div>
          <div class="right">
            <div style="background: #e4e4e4">修改后</div>
            <div>{{ company(data2.companyNo) }}</div>
            <div>{{ data2.flowModelName }}</div>
            <div>{{ accountSets2(data2.accountSetsId) }}</div>
            <div>{{ data2.remark }}</div>
          </div>
        </div>
        <p style="font-weight: bold; margin-top: 20px">修改前：</p>
        <el-tabs v-model="tabsValue" type="card">
          <el-tab-pane
            :key="item.name"
            v-for="(item, index) in data1.oaVoucherRulesViceVos"
            :label="item.name"
            :name="item.name"
          >
            <div class="content">
              <div
                v-if="item.rulerType == 1"
                style="margin-bottom: 12px; color: #333"
              >
                <p style="color: #999">在OA流程的收款方账套下生成凭证的规则</p>
                收款方字段:
                {{ item.accountName }}
              </div>
              <div>字： {{ item.finanicalWord || "--" }}</div>

              <span>摘要： </span>
              <div class="word_box">
                <div
                  style="display: flex; align-items: center"
                  v-for="(v, i) in item.summaryList"
                  :key="i"
                >
                  <div v-if="v.type == 'input'">
                    {{ v.value }}
                  </div>
                  <div v-else style="color: #409eff">[{{ v.label }}]</div>
                </div>
              </div>
              <p style="margin-top: 16px">科目规则：</p>

              <div style="width: 100%; overflow: auto">
                <div
                  class="table_data"
                  :style="{ width: item.tableWidth + 'px' }"
                >
                  <div class="head">
                    <div class="title2" style="width: 120px"></div>
                    <div
                      class="title2"
                      style="width: 500px"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      {{ v.subjectType == 0 ? "借方" : "贷方" }}
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">记账金额字段</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      {{ v.accountName }}
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">一级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      {{ v.firstSubjectName || "--" }}
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">二级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <span v-if="v.secondSubjectJson&&JSON.parse(v.secondSubjectJson).length > 0">
                        <span
                          v-for="item in JSON.parse(v.secondSubjectJson)"
                          :key="item"
                        >
                          <span v-if="item.type == 'input'">{{
                            item.value
                          }}</span>
                          <span style="color: #409eff" v-else
                            >[{{ item.label }}]</span
                          >
                        </span>
                      </span>
                      <span v-else>{{ v.secondSubjectName }}</span>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">三级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <span v-if="v.thirdSubjectJson&&JSON.parse(v.thirdSubjectJson).length > 0">
                        <span
                          v-for="item in JSON.parse(v.thirdSubjectJson)"
                          :key="item"
                        >
                          <span v-if="item.type == 'input'">{{
                            item.value
                          }}</span>
                          <span style="color: #409eff" v-else
                            >[{{ item.label }}]</span
                          ></span
                        >
                      </span>
                      <span v-else>{{ v.thirdSubjectName }}</span>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">四级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <span v-if="v.fourthSubjectJson&&JSON.parse(v.fourthSubjectJson).length > 0">
                        <span
                          v-for="item in JSON.parse(v.fourthSubjectJson)"
                          :key="item"
                          ><span v-if="item.type == 'input'">{{
                            item.value
                          }}</span>
                          <span style="color: #409eff" v-else
                            >[{{ item.label }}]</span
                          ></span
                        >
                      </span>
                      <span v-else>{{ v.fourthSubjectName }}</span>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">科目汇总</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      {{ v.subjectCollect }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <p style="font-weight: bold; margin-top: 20px">修改后：</p>
        <el-tabs v-model="tabsValue2" type="card">
          <el-tab-pane
            :key="item.name"
            v-for="(item, index) in data2.oaVoucherRulesViceVos"
            :label="item.name"
            :name="item.name"
          >
            <div class="content">
              <div
                v-if="item.rulerType == 1"
                style="margin-bottom: 12px; color: #333"
              >
                <p style="color: #999">在OA流程的收款方账套下生成凭证的规则</p>
                收款方字段:
                {{ item.accountName }}
              </div>
              <div>字： {{ item.finanicalWord || "--" }}</div>

              <span>摘要： </span>
              <div class="word_box">
                <div
                  style="display: flex; align-items: center"
                  v-for="(v, i) in item.summaryList"
                  :key="i"
                >
                  <div v-if="v.type == 'input'">
                    {{ v.value }}
                  </div>
                  <div v-else style="color: #409eff">[{{ v.label }}]</div>
                </div>
              </div>
              <p style="margin-top: 16px">科目规则：</p>

              <div style="width: 100%; overflow: auto">
                <div
                  class="table_data"
                  :style="{ width: item.tableWidth + 'px' }"
                >
                  <div class="head">
                    <div class="title2" style="width: 120px"></div>
                    <div
                      class="title2"
                      style="width: 500px"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      {{ v.subjectType == 0 ? "借方" : "贷方" }}
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">记账金额字段</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      {{ v.accountName }}
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">一级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      {{ v.firstSubjectName || "--" }}
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">二级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <span v-if="v.secondSubjectJson&&JSON.parse(v.secondSubjectJson).length > 0">
                        <span
                          v-for="item in JSON.parse(v.secondSubjectJson)"
                          :key="item"
                        >
                          <span v-if="item.type == 'input'">{{
                            item.value
                          }}</span>
                          <span style="color: #409eff" v-else
                            >[{{ item.label }}]</span
                          >
                        </span>
                      </span>
                      <span v-else>{{ v.secondSubjectName }}</span>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">三级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <span v-if="v.thirdSubjectJson&&JSON.parse(v.thirdSubjectJson).length > 0">
                        <span
                          v-for="item in JSON.parse(v.thirdSubjectJson)"
                          :key="item"
                        >
                          <span v-if="item.type == 'input'">{{
                            item.value
                          }}</span>
                          <span style="color: #409eff" v-else
                            >[{{ item.label }}]</span
                          ></span
                        >
                      </span>
                      <span v-else>{{ v.thirdSubjectName }}</span>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">四级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <span v-if="v.fourthSubjectJson&&JSON.parse(v.fourthSubjectJson).length > 0">
                        <span
                          v-for="item in JSON.parse(v.fourthSubjectJson)"
                          :key="item"
                          ><span v-if="item.type == 'input'">{{
                            item.value
                          }}</span>
                          <span style="color: #409eff" v-else
                            >[{{ item.label }}]</span
                          ></span
                        >
                      </span>
                      <span v-else>{{ v.fourthSubjectName }}</span>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">科目汇总</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      {{ v.subjectCollect }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <p style="font-weight: bold">修改说明：</p>
        {{ detailData.editInfo }}
      </div>
      <div v-if="type == 2">
          {{ detailData.editUserNickName }}申请删除本条记账凭证规则！
        <p style="font-weight: bold">删除原因说明：</p>
        {{ detailData.editInfo }}
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit(0)">通过</el-button>
        <el-button
          type="primary"
          @click="dialogVisible2 = true"
          style="background-color: #ff9900; border: none"
          >驳回</el-button
        >
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="审核驳回"
      :visible.sync="dialogVisible2"
      width="800px"
      :before-close="handleClose2"
    >
      <p>请输入驳回原因</p>
      <el-input
        type="textarea"
        placeholder="请输入内容"
        v-model="textarea"
        maxlength="200"
        show-word-limit
      >
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit(1)" :disabled="!textarea"
          >提 交</el-button
        >
        <el-button @click="dialogVisible2 = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAccountSetsList } from "@/api/oa/voucharRules";
import { allCompanyList } from "@/api/oa/processTemplate";
export default {
  props: {
    detailData: Object,
  },
  data() {
    return {
      dialogVisible: true,
      dialogVisible2: false,
      type: null,
      textarea: "",
      tabsValue: "记账凭证规则1",
      tabsValue2: "记账凭证规则1",
      projects:[],
      accountSetsList:[],
      accountSetsList2:[],
    };
  },
  mounted() {
    this.data1 = JSON.parse(this.detailData.oaVoucherRulesRecordsOldData)
    this.data2 = JSON.parse(this.detailData.oaVoucherRulesRecordsNewData)
    if (
      this.detailData.oaVoucherRulesRecordsNewId &&
      this.detailData.oaVoucherRulesRecordsOldId
    ) {
      //修改
      this.type = 1;
    } else if (!this.detailData.oaVoucherRulesRecordsNewId) {
      //删除
      this.type = 2;
    }
    allCompanyList().then((response) => {
      this.projects = response;
    });
    getAccountSetsList(this.data1.companyNo).then((response) => {
      this.accountSetsList = response;
    });
    getAccountSetsList(this.data2.companyNo).then((response) => {
      this.accountSetsList2 = response;
    });
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleClose2() {
      this.dialogVisible2 = false;
    },
    submit(v) {
      this.$emit("submit", v, this.textarea);
    },
    company(e) {
      let data = this.projects.find((item) => {
        return item.companyId == e;
      });
      return data.name;
    },
    accountSets(e) {
      let data = this.accountSetsList.find((item) => {
        return item.id == e;
      });
      return data.companyName;
    },
    accountSets2(e) {
      let data = this.accountSetsList2.find((item) => {
        return item.id == e;
      });
      return data.companyName;
    },
  },
};
</script>

<style lang="less" scoped>
.table {
  display: flex;
  justify-content: space-between;
  border: 1px solid #cccccc;
  border-bottom: none;
  .left,
  .center,
  .right {
    width: 33.33%;
    div {
      height: 30px;
      width: 100%;
      border-right: 1px solid #ccc;
      text-align: center;
      line-height: 30px;
      border-bottom: 1px solid #ccc;
      text-align: left;
      padding-left: 10px;
    }
  }
  .left div {
    font-weight: bold;
  }
}
.table_data {
  overflow: auto;
  border: 1px solid #ccc;
  border-bottom: none;
  box-sizing: border-box;
  .head {
    display: flex;

    .title2 {
      padding: 12px;
      border-right: 1px solid #ccc;
      border-bottom: 1px solid #ccc;
      box-sizing: border-box;
      text-align: center;
      background: #f2f2f2;
      overflow: auto;
    }
  }
}
.word_box {
  width: 100%;
  height: 50px;
  border: 1px solid #cccccc;
  display: flex;
  align-items: center;
  padding-left: 16px;
}
.word_box_item {
  width: 270px;
  height: 40px;
  background: #e8f4ff;
  display: flex;
  align-items: center;
  padding-left: 12px;
}
.btns {
  padding: 20px;
}
.solid2 {
  width: 100%;
  height: 10px;
  background: #f2f2f2;
}
.el-icon-delete {
  margin-left: 5px;
  font-size: 16px;
  cursor: pointer;
}
.paybox,
.shoukbox {
  .pay_item {
    margin-bottom: 12px;
    padding-bottom: 12px;
    .solid {
      width: 98%;
      height: 2px;
      background: #f2f2f2;
      margin: 0 auto;
      margin-top: 12px;
      margin-bottom: 12px;
    }
    .subject {
      width: 98%;
      margin: 0 auto;
      .title2 {
        font-size: 14px;
        font-weight: bold;
      }
      .title3 {
        color: #999;
        margin-bottom: 0px;
      }
    }
    border: 1px solid #e0e7ed;
    .item_title {
      font-size: 16px;
      font-weight: bold;
      background: #f8f8f9;
      height: 40px;
      line-height: 40px;
      padding-left: 12px;
      border-bottom: 1px solid #e0e7ed;
      margin-bottom: 12px;
    }
  }
  .item {
    margin-bottom: 12px;
    /deep/ .el-input__inner {
      width: 250px !important;
      height: 32px !important;
    }
    span {
      display: inline-block;
      width: 200px;
      text-align: right;
      margin-right: 12px;
      i {
        color: red;
        margin-right: 5px;
      }
    }
  }
}
.shoukbox {
}
.header {
  .item {
    margin-bottom: 12px;
    /deep/ .el-input__inner {
      width: 250px !important;
    }
    span {
      display: inline-block;
      width: 200px;
      text-align: right;
      margin-right: 12px;
      i {
        color: red;
        margin-right: 5px;
      }
    }
  }
}
.el-form-item {
  margin-bottom: 0;
}
.btn-container {
  margin: 10px 0;
  width: 100%;
  text-align: center;
}
</style>