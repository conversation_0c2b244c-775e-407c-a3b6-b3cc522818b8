<template>
  <div class="app-container" id="tallyVoucher">
    <div style="width: 100%; height: 80px">
      <span style="color: #9d9d9d; margin-left: 20px"
        >说明：配置OA系统中生成记账凭证的规则。OA流程模板类型如果为财务流程，可以根据以下规则，在流程结束后生成记账凭证。</span
      >
      <br />
      <span style="color: #9d9d9d; margin-left: 20px"
        >注意：需要先配置好流程模板，再在此配置规则</span
      >
      <br />
    </div>

    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="公司" prop="companyNo">
        <!-- <el-input
          v-model="queryParams.companyNo"
          placeholder="请输入公司编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        /> -->
        <el-select
          v-model="queryParams.companyNo"
          size="mini"
          filterable=""
          placeholder="请选择公司"
        >
          <el-option
            v-for="item in projects"
            :key="item.companyId"
            :label="item.name"
            :value="item.companyId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="流程模板" prop="flowModelName">
        <el-input
          v-model="queryParams.flowModelName"
          placeholder="请输入关键字"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8"></el-row>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <span v-if="changeEditType" style="margin-right: 20px;">
          视图：<span
            class="tabItem"
            @click="activeName = 0"
            :class="activeName == 0 ? 'tabsActive' : ''"
            >全部</span
          >
          <span
            class="tabItem"
            @click="activeName = 1"
            :class="activeName == 1 ? 'tabsActive' : ''"
            >待我审核<span v-if="countData.myCheckCount > 0"
              >({{ countData.myCheckCount }})</span
            ></span
          >
          <span
            class="tabItem"
            @click="activeName = 2"
            :class="activeName == 2 ? 'tabsActive' : ''"
            >我的提交<span v-if="countData.mySubmitCount > 0"
              >({{ countData.mySubmitCount }})</span
            ></span
          >
        </span>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
          >新增记账凭证</el-button
        >
        <el-button type="primary" size="mini" @click="historySet"
          >历史数据处理</el-button
        >
        <el-button
          v-hasRole="['OA']"
          type="primary"
          size="mini"
          @click="editTypeChange"
          >编辑需审核（{{ changeEditType ? "已开启" : "已关闭" }}）</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      v-show="activeName == 0"
      :data="rulesList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="公司" align="left" prop="companyNo">
        <template slot-scope="scope">
          <span v-for="dict in projects" :key="dict.companyId">
            <span v-if="scope.row.companyNo == dict.companyId">{{
              dict.name
            }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="流程模板名称"
        align="left"
        width="180"
        prop="flowModelName"
      />
      <el-table-column
        show-overflow-tooltip=""
        width="220"
        class-name="commodityDiscountAmount1"
        label="摘要"
        align="left"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            {{ item.json || "--" }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="借方一级科目名称"
        class-name="commodityDiscountAmount1"
        width="220"
        align="left"
        show-overflow-tooltip=""
        prop="firstBorrowSubjectName"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            <div
              class="cell_div"
              v-for="(v, i) in item.oaVoucherRulesSubjects"
              :key="i"
            >
              <div
                v-if="v.subjectType == 0"
                style="
                  height: 42px;
                  line-height: 42px;
                  border-bottom: 1px solid #ebeef5 !important;
                "
              >
                {{ v.firstSubjectName || "--" }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="借方二级科目名称"
        width="220"
        align="left"
        show-overflow-tooltip=""
        class-name="commodityDiscountAmount1"
        prop="secondBorrowSubjectName"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            <div
              class="cell_div"
              v-for="(v, i) in item.oaVoucherRulesSubjects"
              :key="i"
            >
              <div
                v-if="v.subjectType == 0"
                style="
                  height: 42px;
                  line-height: 42px;
                  border-bottom: 1px solid #ebeef5 !important;
                "
              >
                {{ v.secondSubjectName || "--" }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="借方三级科目名称"
        width="220"
        align="left"
        show-overflow-tooltip=""
        class-name="commodityDiscountAmount1"
        prop="thirdBorrowSubjectName"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            <div
              class="cell_div"
              v-for="(v, i) in item.oaVoucherRulesSubjects"
              :key="i"
            >
              <div
                v-if="v.subjectType == 0"
                style="
                  height: 42px;
                  line-height: 42px;
                  border-bottom: 1px solid #ebeef5 !important;
                "
              >
                {{ v.thirdSubjectName || "--" }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="贷方一级科目名称"
        width="220"
        align="left"
        show-overflow-tooltip=""
        class-name="commodityDiscountAmount1"
        prop="firstLoanSubjectName"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            <div
              class="cell_div"
              v-for="(v, i) in item.oaVoucherRulesSubjects"
              :key="i"
            >
              <div
                v-if="v.subjectType == 1"
                style="
                  height: 42px;
                  line-height: 42px;
                  border-bottom: 1px solid #ebeef5 !important;
                "
              >
                {{ v.firstSubjectName || "--" }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="贷方二级科目名称"
        width="220"
        align="left"
        show-overflow-tooltip=""
        class-name="commodityDiscountAmount1"
        prop="secondLoanSubjectName"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            <div
              class="cell_div"
              v-for="(v, i) in item.oaVoucherRulesSubjects"
              :key="i"
            >
              <div
                v-if="v.subjectType == 1"
                style="
                  height: 42px;
                  line-height: 42px;
                  border-bottom: 1px solid #ebeef5 !important;
                "
              >
                {{ v.secondSubjectName || "--" }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="贷方三级科目名称"
        class-name="commodityDiscountAmount1"
        align="left"
        width="220"
        show-overflow-tooltip=""
        prop="thirdLoanSubjectName"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            <div
              class="cell_div"
              v-for="(v, i) in item.oaVoucherRulesSubjects"
              :key="i"
            >
              <div
                v-if="v.subjectType == 1"
                style="
                  height: 42px;
                  line-height: 42px;
                  border-bottom: 1px solid #ebeef5 !important;
                "
              >
                {{ v.thirdSubjectName || "--" }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="摘要" align="left" prop="abstractJson" /> -->

      <el-table-column
        label="备注"
        width="180"
        show-overflow-tooltip=""
        align="left"
        prop="remark"
      >
      </el-table-column>
      <el-table-column
        label="业务责任人"
        v-if="changeEditType"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="financialStaff"
      />
      <el-table-column
        label="财务责任人"
        width="160"
        v-if="changeEditType"

        show-overflow-tooltip=""
        align="left"
        prop="salesman"
      />
      <el-table-column
        label="审核状态"
        width="120"
        v-if="changeEditType"

        show-overflow-tooltip=""
        align="left"
        prop="remark"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.rejectFlag !== null && scope.row.confirmFlag == 0"
            >{{ scope.row.rejectFlag == 0 ? "已通过" : "已驳回" }}</span
          >
          <span
            v-if="scope.row.rejectFlag === null && scope.row.confirmFlag == 0"
            >{{
              scope.row.checkStatus == 0 ? "待业务审核" : "待财务审核"
            }}</span
          >
          <span v-if="scope.row.confirmFlag == 1">-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="收款方生成凭证"
        width="120"
        align="left"
        prop="isVoucher"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.isVoucher == 'Y'">是</span>
          <span v-if="scope.row.isVoucher == 'N'">否</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="200"
        v-if="changeEditType"
        align="left"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="dataDetils(scope.row)">详情</el-button>
          <el-button
            style="color: #ff9900"
            type="text"
            v-if="
              scope.row.rejectFlag === null && scope.row.checkStatus !== null&&scope.row.showCheckFlag==1
            "
            @click="getDetail(scope.row)"
            >审核</el-button
          >
          <el-button
            style="color: #ff9900"
            type="text"
            v-if="scope.row.confirmFlag == 0 && scope.row.rejectFlag !== null"
            @click="rulesConfirm(scope.row)"
            >已知悉</el-button
          >
          <el-button
            v-if="scope.row.confirmFlag == 1"
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            type="text"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            type="text"
            @click="record(scope.row)"
            >编辑记录</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="150"
        v-else
        align="left"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="dataDetils(scope.row)">详情</el-button>

          <el-button
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            type="text"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            type="text"
            @click="record(scope.row)"
            >编辑记录</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-table
      v-loading="loading"
      border
      v-show="activeName == 1"
      :data="rulesList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="公司" align="left" prop="companyNo">
        <template slot-scope="scope">
          <span v-for="dict in projects" :key="dict.companyId">
            <span v-if="scope.row.companyNo == dict.companyId">{{
              dict.name
            }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="流程模板"
        align="left"
        width="180"
        prop="flowModelName"
      />
      <el-table-column
        label="业务责任人"
        v-if="changeEditType"

        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="financialStaff"
      />
      <el-table-column
        label="财务责任人"
        v-if="changeEditType"

        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="salesman"
      />
      <el-table-column
        label="提交人"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="editUserNickName"
      />
      <el-table-column
        label="提交时间"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="editTime"
      />
      <el-table-column
        label="修改类型"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="salesman"
      >
        <template slot-scope="scope">
          {{ scope.row.updateType == 1 ? "修改" : "删除" }}
        </template>
      </el-table-column>
      <el-table-column
        label="修改说明"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="editInfo"
      />
      <el-table-column
        label="审核状态"
        width="120"
        show-overflow-tooltip=""
        align="left"
        prop="remark"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.rejectFlag !== null && scope.row.confirmFlag == 0"
            >{{ scope.row.rejectFlag == 0 ? "已通过" : "已驳回" }}</span
          >
          <span
            v-if="scope.row.rejectFlag === null && scope.row.confirmFlag == 0"
            >{{
              scope.row.checkStatus == 0 ? "待业务审核" : "待财务审核"
            }}</span
          >
          <span v-if="scope.row.confirmFlag == 1">-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="借方科目"
        width="220"
        align="left"
        show-overflow-tooltip=""
        class-name="commodityDiscountAmount1"
        prop="thirdBorrowSubjectName"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            <div
              class="cell_div"
              v-for="(v, i) in item.oaVoucherRulesSubjects"
              :key="i"
            >
              <div
                v-if="v.subjectType == 0"
                style="
                  height: 42px;
                  line-height: 42px;
                  border-bottom: 1px solid #ebeef5 !important;
                "
              >
                {{ v.subjectCollect || "--" }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="贷方科目"
        width="220"
        align="left"
        show-overflow-tooltip=""
        class-name="commodityDiscountAmount1"
        prop="firstLoanSubjectName"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            <div
              class="cell_div"
              v-for="(v, i) in item.oaVoucherRulesSubjects"
              :key="i"
            >
              <div
                v-if="v.subjectType == 1"
                style="
                  height: 42px;
                  line-height: 42px;
                  border-bottom: 1px solid #ebeef5 !important;
                "
              >
                {{ v.subjectCollect || "--" }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="200"
        v-if="changeEditType"
        align="left"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="dataDetils(scope.row)">详情</el-button>
          <el-button
            style="color: #ff9900"
            type="text"
            v-if="
              scope.row.rejectFlag === null && scope.row.checkStatus !== null&&scope.row.showCheckFlag==1
            "
            @click="getDetail(scope.row)"
            >审核</el-button
          >
          <el-button
            style="color: #ff9900"
            type="text"
            v-if="scope.row.confirmFlag == 0 && scope.row.rejectFlag !== null"
            @click="rulesConfirm(scope.row)"
            >已知悉</el-button
          >
          <el-button
            v-if="scope.row.confirmFlag == 1"
            type="text"
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            type="text"
            @click="record(scope.row)"
            >编辑记录</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="150"
        v-else
        align="left"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="dataDetils(scope.row)">详情</el-button>

          <el-button
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            type="text"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            type="text"
            @click="record(scope.row)"
            >编辑记录</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-table
      v-loading="loading"
      border
      v-show="activeName == 2"
      :data="rulesList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="公司" align="left" prop="companyNo">
        <template slot-scope="scope">
          <span v-for="dict in projects" :key="dict.companyId">
            <span v-if="scope.row.companyNo == dict.companyId">{{
              dict.name
            }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="流程模板"
        align="left"
        width="180"
        prop="flowModelName"
      />
      <el-table-column
        label="业务责任人"
        v-if="changeEditType"

        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="financialStaff"
      />
      <el-table-column
        label="财务责任人"
        v-if="changeEditType"

        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="salesman"
      />
      <el-table-column
        label="审核人"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="checkUserNickName"
      />
      <el-table-column
        label="审核时间"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="checkTime"
      />
      <el-table-column
        label="修改类型"
        width="160"
        show-overflow-tooltip=""
        align="left"
        prop="salesman"
      >
        <template slot-scope="scope">
          {{ scope.row.updateType == 1 ? "修改" : "删除" }}
        </template>
      </el-table-column>
      <el-table-column
        label="审核状态"
        width="120"
        show-overflow-tooltip=""
        align="left"
        prop="remark"
      >
        <template slot-scope="scope">
          <span
            v-if="scope.row.rejectFlag !== null && scope.row.confirmFlag == 0"
            >{{ scope.row.rejectFlag == 0 ? "已通过" : "已驳回" }}</span
          >
          <span
            v-if="scope.row.rejectFlag === null && scope.row.confirmFlag == 0"
            >{{
              scope.row.checkStatus == 0 ? "待业务审核" : "待财务审核"
            }}</span
          >
          <span v-if="scope.row.confirmFlag == 1">-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="借方科目"
        width="220"
        align="left"
        show-overflow-tooltip=""
        class-name="commodityDiscountAmount1"
        prop="thirdBorrowSubjectName"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            <div
              class="cell_div"
              v-for="(v, i) in item.oaVoucherRulesSubjects"
              :key="i"
            >
              <div
                v-if="v.subjectType == 0"
                style="
                  height: 42px;
                  line-height: 42px;
                  border-bottom: 1px solid #ebeef5 !important;
                "
              >
                {{ v.subjectCollect || "--" }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="贷方科目"
        width="220"
        align="left"
        show-overflow-tooltip=""
        class-name="commodityDiscountAmount1"
        prop="firstLoanSubjectName"
      >
        <template slot-scope="scope">
          <div
            class="commodityDiscountAmount"
            :style="{
              height: listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
              lineHeight:
                listLength(item.oaVoucherRulesSubjects, 0) * 42 + 'px',
            }"
            v-for="(item, index) in scope.row.oaVoucherRulesViceVos"
            :key="index"
          >
            <div
              class="cell_div"
              v-for="(v, i) in item.oaVoucherRulesSubjects"
              :key="i"
            >
              <div
                v-if="v.subjectType == 1"
                style="
                  height: 42px;
                  line-height: 42px;
                  border-bottom: 1px solid #ebeef5 !important;
                "
              >
                {{ v.subjectCollect || "--" }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="200"
        v-if="changeEditType"
        align="left"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="dataDetils(scope.row)">详情</el-button>
          <el-button
            style="color: #ff9900"
            type="text"
            v-if="
              scope.row.rejectFlag === null && scope.row.checkStatus !== null&&scope.row.showCheckFlag==1
            "
            @click="getDetail(scope.row)"
            >审核</el-button
          >
          <el-button
            style="color: #ff9900"
            type="text"
            v-if="scope.row.confirmFlag == 0 && scope.row.rejectFlag !== null"
            @click="rulesConfirm(scope.row)"
            >已知悉</el-button
          >
          <el-button
            v-if="scope.row.confirmFlag == 1"
            type="text"
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-hasRole="['caiwuAdmin', 'kuaiji', 'chuna', 'yewu', 'yewuAdmin']"
            type="text"
            @click="record(scope.row)"
            >编辑记录</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="150"
        v-else
        align="left"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="dataDetils(scope.row)">详情</el-button>

          <el-button type="text" @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button type="text" @click="record(scope.row)">编辑记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="提示"
      :visible.sync="remarkDialog"
      width="30%"
      :before-close="handleClose"
    >
      <span>{{ dataRemark }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="remarkDialog = false">取 消</el-button>
        <el-button type="primary" @click="remarkDialog = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <el-dialog :visible.sync="rulesDeilogs" width="40%">
      <div style="width: 100%; height: 100%">
        <span style="margin-left: 45%; font-size: 18px; font-weight: bold"
          >公司：</span
        >

        <span v-for="dict in projects" :key="dict.companyId">
          <span v-if="rulesDataD.company == dict.companyId">{{
            dict.name
          }}</span>
        </span>
        <!-- <span>{{rulesDataD.company}}</span> -->
        <br />
        <span style="margin-left: 41%; font-size: 18px; font-weight: bold"
          >流程模板：</span
        >
        <span>{{ rulesDataD.modelName }}</span>
        <br />
        <span style="margin-left: 37.5%; font-size: 18px; font-weight: bold"
          >记账金额字段：</span
        >
        <span>{{ rulesDataD.feild }}</span>
        <br />
        <span style="margin-left: 47%; font-size: 18px; font-weight: bold"
          >字：</span
        >
        <span>{{ rulesDataD.finanicalWord }}</span>
        <br />
        <span style="margin-left: 41.5%; font-size: 18px; font-weight: bold"
          >科目类型：</span
        >
        <span>{{ rulesDataD.subjectType }}</span>
        <br />
        <span style="margin-left: 45%; font-size: 18px; font-weight: bold"
          >摘要：</span
        >
        <span>{{ rulesDataD.abstractJson }}</span>
        <br />

        <span style="margin-left: 36%; font-size: 18px; font-weight: bold"
          >收款方生成凭证：</span
        >
        <span v-if="rulesDataD.isVoucher == 'Y'">是</span>
        <span v-if="rulesDataD.isVoucher == 'N'">否</span>
        <br />
        <span style="margin-left: 41.5%; font-size: 18px; font-weight: bold"
          >收款方字：</span
        >
        <span>{{ rulesDataD.collFinanicalWord }}</span>
        <br />
        <span style="margin-left: 36%; font-size: 18px; font-weight: bold"
          >收款方科目类型：</span
        >
        <span>{{ rulesDataD.collSubjectType }}</span>
        <br />
      </div>
      <span></span>
      <br />
      <span></span>
      <br />
      <el-table v-loading="loading" :data="rulesDetilsList">
        <el-table-column label align="left" prop="subject" />
        <el-table-column label="借方科目" align="left" prop="borrowSubject" />
        <el-table-column label="贷方科目" align="left" prop="loanSubject" />
      </el-table>
      <span></span>
      <br />
      <span></span>
      <br />
      <el-table v-loading="loading" :data="rulesCollDetilsList">
        <el-table-column label align="left" prop="subject" />
        <el-table-column label="借方科目" align="left" prop="borrowSubject" />
        <el-table-column label="贷方科目" align="left" prop="loanSubject" />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="rulesDeilogs = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="设置"
      :show-close="false"
      :visible.sync="editDialogType"
      width="600px"
    >
      <p>
        编辑需审核：<el-switch v-model="changeEditType2"> </el-switch
        >{{ changeEditType2 ? "已开启" : "已关闭" }}
      </p>
      <p>
        开启[编辑需审核]功能后，如果记账凭证规则发生修改，必须由所属的财务、业务责任人审核后才能生效
      </p>
      <p>如果财务责任人进行了修改，则需要业务责任人审核确认</p>
      <p>如果业务责任人进行了修改，则需要财务责任人审核确认</p>
      <p>如果本条规则尚未设置财务、业务责任人，则需先设置才能进行修改</p>
      <span slot="footer" class="dialog-footer">
        <el-button
          @click="
            editDialogType = false;
            changeEditType2 = false;
          "
          >取 消</el-button
        >
        <el-button
          type="primary"
          :disabled="!changeEditType2"
          @click="submitChangeEdit"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <examine
      v-if="examineType"
      :detailData="detailData"
      @close="examineType = false"
      @submit="submitExamine"
    />
    <know
      v-if="knowType"
      :detailData="detailData"
      @close="knowType = false"
      @confirm="submitKnow"
    />
    <editRecord
      :editList="editList"
      :changeEditType="changeEditType"
      v-if="editRecordType"
      @close="editRecordType = false"
    />
  </div>
</template>

<script>
import examine from "./examine.vue";
import know from "./know.vue";
import editRecord from "./editRecord.vue";
import {
  listRules,
  getRules,
  delRules,
  addRules,
  updateRules,
  handleHistoryData,
  checkEditExamine,
  editExamine,
  getVoucherdetail,
  rulesCheck,
  rulesConfirm2,
  selectEditRecord,
  rulesviewCount,
} from "@/api/oa/voucharRules";

import { allCompanyList } from "@/api/oa/processTemplate";
export default {
  name: "TallyVoucher",
  components: {
    examine,
    know,
    editRecord,
  },
  data() {
    return {
      countData: null,
      editRecordType: false,
      knowType: false,
      examineType: false,
      activeName: 0,
      changeEditType: null,
      changeEditType2: false,
      editDialogType: false,
      remarkDialog: false,
      dataRemark: "",
      rulesDataD: {
        company: "",
        modelName: "",
        feild: "",
      },
      projects: [],
      rulesDetilsList: [],
      rulesCollDetilsList: [],
      //详情deilog
      rulesDeilogs: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      rulesList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        selectType: 0,
        companyNo: null,
        flowModelId: null,
        flowModelName: null,
        accountingField: null,
        firstBorrowSubjectName: null,
        firstBorrowSubjectId: null,
        isSecondBorrowSubject: null,
        secondBorrowValueMode: null,
        secondBorrowSubjectName: null,
        secondBorrowNounId: null,
        secondBorrowNounName: null,
        isThirdBorrowSubject: null,
        thirdBorrowValueMode: null,
        thirdBorrowSubjectName: null,
        thirdBorrowNounId: null,
        thirdBorrowNounName: null,
        isFourthBorrowSubject: null,
        fourthBorrowValueMode: null,
        fourthBorrowSubjectName: null,
        fourthBorrowNounId: null,
        fourthBorrowNounName: null,
        firstLoanSubjectId: null,
        firstLoanSubjectName: null,
        isSecondLoanSubject: null,
        secondLoanValueMode: null,
        secondLoanSubjectName: null,
        secondLoanNounId: null,
        secondLoanNounName: null,
        isThirdLoanSubject: null,
        thirdLoanValueMode: null,
        thirdLoanSubjectName: null,
        thirdLoanNounId: null,
        thirdLoanNounName: null,
        isFourthLoanSubject: null,
        fourthLoanValueMode: null,
        fourthLoanSubjectName: null,
        fourthLoanNounId: null,
        fourthLoanNounName: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      detailData: null,
      editList: [],
    };
  },
  created() {
    if (this.$route.query.oaNotifyStep) {
      this.activeName = this.$route.query.oaNotifyStep;
    }
    this.checkEditExamine();
    this.getList();
  },
  watch: {
    "$store.state.editTemplate": {
      handler(newval, oldval) {
        console.log(newval);
        this.getList();
      },
      deep: true,
    },
    activeName: {
      handler(newval, oldval) {
        this.getList();
      },
    },
  },
  methods: {
    record(v) {
      selectEditRecord({ oaVoucherRulesMainId: v.id }).then((res) => {
        if (res.code == 200) {
          this.editList = res.data || [];
          this.editRecordType = true;
        }
      });
    },
    submitKnow() {
      rulesConfirm2({ ...this.detailData, confirmFlag: 1 }).then((res) => {
        if (res.code == 200) {
          this.$message.success("本条通知已删除");
          this.knowType = false;
          this.getList();
        }
      });
    },
    rulesConfirm(v) {
      getVoucherdetail({ oaVoucherRulesMainId: v.id }).then((res) => {
        if (res.code == 200) {
          this.detailData = res.data;
          this.knowType = true;
        }
      });
    },
    submitExamine(v, i) {
      //0通过1驳回
      if (v == 0) {
        this.$confirm("点击确定，本次修改将立即生效?", "审核通过", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            rulesCheck({ ...this.detailData, rejectFlag: v }).then((res) => {
              if (res.code == 200) {
                this.examineType = false;
                this.$message.success("审核已通过");
                this.activeName = 1;
                this.getList();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消",
            });
          });
      } else {
        rulesCheck({
          ...this.detailData,
          rejectFlag: v,
          checkRejectInfo: i,
        }).then((res) => {
          if (res.code == 200) {
            this.examineType = false;
            this.$message.success("审核已驳回");
            this.activeName = 1;
            this.getList();
          }
        });
      }
    },
    getDetail(v) {
      getVoucherdetail({ oaVoucherRulesMainId: v.id }).then((res) => {
        if (res.code == 200) {
          this.detailData = res.data;
          this.examineType = true;
          // if (
          //   res.data.oaVoucherRulesRecordsNewId &&
          //   res.data.oaVoucherRulesRecordsOldId
          // ) {
          //   //修改
          // } else if (!res.data.oaVoucherRulesRecordsNewId) {
          //   //删除
          // }
        }
      });
    },
    checkEditExamine() {
      checkEditExamine({ projectType: 0 }).then((res) => {
        if (res.code == 200) {
          this.changeEditType = res.msg == 0 ? false : true;
          if (!this.changeEditType) {
            this.activeName = 0;
          }
        }
      });
    },
    submitChangeEdit() {
      editExamine({ projectType: 0, isEnable: 1 }).then((res) => {
        if (res.code == 200) {
          this.$message.success("审核需编辑已开启");
          this.editDialogType = false;
          this.checkEditExamine();
          this.getList();
        }
      });
    },
    editTypeChange() {
      if (!this.changeEditType) {
        this.editDialogType = true;
      } else {
        this.$confirm(
          "关闭后，当前处于审核中的记账凭证规则编辑申请将会恢复为提交前的状态?",
          "关闭编辑需审核功能",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            editExamine({ projectType: 0, isEnable: 0 }).then((res) => {
              if (res.code == 200) {
                this.$message.success("审核需编辑已关闭");
                this.checkEditExamine();
                this.getList();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消",
            });
          });
      }
    },
    listLength(list, type) {
      let length = 0;
      list.forEach((item) => {
        if (item.subjectType == type) {
          length++;
        }
      });
      return length;
    },
    historySet() {
      handleHistoryData().then((res) => {
        if (res.code == 200) {
          this.$message.success(res.msg);
        }
      });
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    /** 备注显示更多按钮 */
    textDetail(remark) {
      this.remarkDialog = true;
      this.dataRemark = remark;
    },
    //点击跳转编辑表单
    closeSelectedTag(view) {
      console.log(view);
      this.$tab.closePage(view).then(({ visitedViews }) => {});
    },
    dataDetils(row) {
      setTimeout(() => {
        this.$router.push({
          path: "/oa/tallyVoucherDetail",
          query: {
            id: row.id,
            changeEditType: this.changeEditType,
          },
        });
      }, 150);

      return;
      this.rulesDataD.company = row.companyNo;
      this.rulesDataD.feild = row.accountingField;
      this.rulesDataD.modelName = row.flowModelName;
      this.rulesDataD.finanicalWord = row.finanicalWord;
      this.rulesDataD.subjectType = row.subjectType;
      this.rulesDataD.abstractJson = row.abstractJson;
      this.rulesDataD.isVoucher = row.isVoucher;
      this.rulesDataD.collFinanicalWord = row.collFinanicalWord;
      this.rulesDataD.collSubjectType = row.collSubjectType;
      this.rulesDetilsList = [];
      this.rulesCollDetilsList = [];
      if (row.isVoucher == "Y") {
        this.rulesCollDetilsList[0] = {
          subject: "一级科目",
          borrowSubject: row.collBorrowFirstName,
          loanSubject: row.collLoanFirstName,
        };

        if (
          row.isCollBorrowSecondSubject == 2 ||
          row.isCollLoanSecondSubject == 2
        ) {
          this.rulesCollDetilsList[1] = {
            subject: "",
            borrowSubject: "",
            loanSubject: "",
          };
          this.rulesCollDetilsList[1].subject = "二级科目";
          this.rulesCollDetilsList[1].borrowSubject = row.collBorrowSecondName;
          this.rulesCollDetilsList[1].loanSubject = row.collLoanSecondName;
          //三级必须在二级里
          if (
            row.isCollBorrowThirdSubject == 2 ||
            row.isCollLoanThirdSubject == 2
          ) {
            this.rulesCollDetilsList[2] = {
              subject: "",
              borrowSubject: "",
              loanSubject: "",
            };
            this.rulesCollDetilsList[2].subject = "三级科目";
            this.rulesCollDetilsList[2].borrowSubject = row.collBorrowThiedName;
            this.rulesCollDetilsList[2].loanSubject = row.collLoanThiedName;

            if (
              row.isCollBorrowFourthSubject == 2 ||
              row.isCollLoanFourthSubject == 2
            ) {
              this.rulesCollDetilsList[3] = {
                subject: "",
                borrowSubject: "",
                loanSubject: "",
              };
              this.rulesCollDetilsList[3].subject = "四级科目";

              this.rulesCollDetilsList[3].borrowSubject =
                row.collBorrowFourthName;

              this.rulesCollDetilsList[3].loanSubject = row.collLoanFourthName;
            }
          }
        }
      }
      this.rulesDetilsList[0] = {
        subject: "一级科目",
        borrowSubject: row.firstBorrowSubjectName,
        loanSubject: row.firstLoanSubjectName,
      };
      if (row.isSecondBorrowSubject == 2 || row.isSecondLoanSubject == 2) {
        this.rulesDetilsList[1] = {
          subject: "",
          borrowSubject: "",
          loanSubject: "",
        };
        this.rulesDetilsList[1].subject = "二级科目";
        if (row.secondBorrowValueMode == 1) {
          this.rulesDetilsList[1].borrowSubject = row.secondBorrowSubjectName;
        } else if (row.secondBorrowValueMode == 2) {
          this.rulesDetilsList[1].borrowSubject =
            row.secondBorrowSubjectName + row.secondBorrowNounName;
        }

        if (row.secondLoanValueMode == 1) {
          this.rulesDetilsList[1].loanSubject = row.secondLoanSubjectName;
        } else if (row.secondLoanValueMode == 2) {
          this.rulesDetilsList[1].loanSubject =
            row.secondLoanSubjectName + row.secondLoanNounName;
        }
        //三级必须在二级里
        if (row.isThirdBorrowSubject == 2 || row.isThirdLoanSubject == 2) {
          this.rulesDetilsList[2] = {
            subject: "",
            borrowSubject: "",
            loanSubject: "",
          };
          this.rulesDetilsList[2].subject = "三级科目";
          if (row.thirdBorrowValueMode == 1) {
            this.rulesDetilsList[2].borrowSubject = row.thirdBorrowSubjectName;
          } else if (row.thirdBorrowValueMode == 2) {
            this.rulesDetilsList[2].borrowSubject =
              row.thirdBorrowSubjectName + row.thirdBorrowNounName;
          }

          if (row.thirdLoanValueMode == 1) {
            this.rulesDetilsList[2].loanSubject = row.thirdLoanSubjectName;
          } else if (row.thirdLoanValueMode == 2) {
            this.rulesDetilsList[2].loanSubject =
              row.thirdLoanSubjectName + row.thirdLoanNounName;
          }
          //三级必须在二级里
          if (row.isFourthBorrowSubject == 2 || row.isFourthLoanSubject == 2) {
            this.rulesDetilsList[3] = {
              subject: "",
              borrowSubject: "",
              loanSubject: "",
            };
            this.rulesDetilsList[3].subject = "四级科目";
            if (row.fourthBorrowValueMode == 1) {
              this.rulesDetilsList[3].borrowSubject =
                row.fourthBorrowSubjectName;
            } else if (row.fourthBorrowValueMode == 2) {
              this.rulesDetilsList[3].borrowSubject =
                row.fourthBorrowSubjectName + row.fourthBorrowNounName;
            }

            if (row.fourthLoanValueMode == 1) {
              this.rulesDetilsList[3].loanSubject = row.fourthLoanSubjectName;
            } else if (row.fourthLoanValueMode == 2) {
              this.rulesDetilsList[3].loanSubject =
                row.fourthLoanSubjectName + row.fourthLoanNounName;
            }
          }
        }
      }
      this.rulesDeilogs = true;
    },
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      this.queryParams.selectType = this.activeName;
      listRules(this.queryParams).then((response) => {
        this.rulesList = response.rows;
        this.rulesList.forEach((item) => {
          item.oaVoucherRulesViceVos.forEach((v) => {
            v.json = "";
            JSON.parse(v.abstractJson).forEach((i) => {
              if (i.type == "input") {
                v.json += i.value;
              } else {
                v.json += "[" + i.label + "]";
              }
            });
          });
        });
        this.total = response.total;
        this.loading = false;
      });

      allCompanyList().then((response) => {
        this.projects = response;
      });
      rulesviewCount(this.queryParams).then((res) => {
        this.countData = res.data;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        companyNo: null,
        flowModelId: null,
        flowModelName: null,
        accountingField: null,
        firstBorrowSubjectName: null,
        firstBorrowSubjectId: null,
        isSecondBorrowSubject: null,
        secondBorrowValueMode: null,
        secondBorrowSubjectName: null,
        secondBorrowNounId: null,
        secondBorrowNounName: null,
        isThirdBorrowSubject: null,
        thirdBorrowValueMode: null,
        thirdBorrowSubjectName: null,
        thirdBorrowNounId: null,
        thirdBorrowNounName: null,
        isFourthBorrowSubject: null,
        fourthBorrowValueMode: null,
        fourthBorrowSubjectName: null,
        fourthBorrowNounId: null,
        fourthBorrowNounName: null,
        firstLoanSubjectId: null,
        firstLoanSubjectName: null,
        isSecondLoanSubject: null,
        secondLoanValueMode: null,
        secondLoanSubjectName: null,
        secondLoanNounId: null,
        secondLoanNounName: null,
        isThirdLoanSubject: null,
        thirdLoanValueMode: null,
        thirdLoanSubjectName: null,
        thirdLoanNounId: null,
        thirdLoanNounName: null,
        isFourthLoanSubject: null,
        fourthLoanValueMode: null,
        fourthLoanSubjectName: null,
        fourthLoanNounId: null,
        fourthLoanNounName: null,
        remark: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      // this.open = true;
      // this.title = "添加【请填写功能名称】";
      this.$router.push({
        path: "/oa/tallyVoucheradd",
        query: { pid: "", changeEditType: this.changeEditType },
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let route = {
        fullPath: "/oa/tallyVoucheradd",
        name: "TallyVoucheradd",
        path: "/oa/tallyVoucheradd",
      };
      this.closeSelectedTag(route);
      setTimeout(() => {
        this.$router.push({
          path: "/oa/tallyVoucheradd",
          query: { pid: row.id, changeEditType: this.changeEditType },
        });
      }, 150);

      // this.reset();
      // const id = row.id || this.ids
      // getRules(id).then(response => {
      //   this.form = response.data;
      //   this.open = true;
      //   this.title = "修改【请填写功能名称】";
      // });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateRules(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRules(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除【请填写功能名称】编号为"' + ids + '"的数据项？')
        .then(function () {
          return delRules(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/rules/export",
        {
          ...this.queryParams,
        },
        `rules_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .el-table__cell {
  padding: 0 !important;
}

/deep/ .commodityDiscountAmount1 .is-center .is-leaf .el-table__cell {
  line-height: 42px !important;
  height: 42px;
}

/deep/ .commodityDiscountAmount1 .cell {
  padding: 0;

  .commodityDiscountAmount {
    border-bottom: 1px solid #ebeef5;
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-left: 10px;
  }

  div {
  }

  div:last-child {
    border: 1px solid #fff !important;
  }
}
.cell_div {
}
.tabItem {
  display: inline-block;
  height: 30px;
  line-height: 30px;
  padding: 0 12px;
  border: 1px solid #ccc;
  cursor: pointer;
}
.tabsActive {
  color: #409eff !important;
  border-color: #409eff !important;
}
</style>
