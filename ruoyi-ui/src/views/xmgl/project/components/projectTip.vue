<template>
  <div>
    <el-dialog
      title="立项流程说明"
      :visible.sync="dialogVisible"
      width="1200px"
      :before-close="handleClose"
    >
      <img style="width: 100%;" :src="projectTipImg" alt="" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import projectTipImg1 from "../components/projectTipImg.png";
export default {
  data() {
    return {
      projectTipImg: projectTipImg1,
      dialogVisible: true,
    };
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>