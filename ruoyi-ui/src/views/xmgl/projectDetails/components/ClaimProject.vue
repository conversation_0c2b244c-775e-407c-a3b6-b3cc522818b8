<template>
  <div>
    <el-dialog
      title="认领项目"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div class="item" style="display: flex">
        <span>项目名称：</span>
        <div>{{ detail.projectForm.projectName }}</div>
      </div>
      <div class="item" style="display: flex">
        <span>原项目负责人：</span>
        <div>
          <div
            style="display: inline-block"
            v-for="item in detail.projectForm.projectPrincipalList"
            :key="item.nickName"
          >
            {{ item.nickName }}，
          </div>
        </div>
      </div>
      <div class="item" style="display: flex">
        <div style="display: flex">
          <span>原渠道方：</span>
          <div v-if="detail.projectForm.channelType == 1">
            <div
              style="display: inline-block"
              v-for="item in detail.projectForm.projectChannelList"
              :key="item.id"
            >
              {{ item.nickName }}，
            </div>
          </div>
          <div v-else>{{ detail.projectForm.channelSide }}</div>
        </div>
        <div>
          <span>渠道方类型：</span>
          {{ detail.projectForm.channelType == 1 ? "内部" : "外部" }}
        </div>
      </div>
      <el-divider></el-divider>
      <div class="item">
        <span><i>*</i> 项目负责人：</span>
        <el-select
          v-model="params.projectUser"
          multiple=""
          collapse-tags=""
          style="width: 300px"
        >
          <el-option
            v-for="item in projectUserList"
            :key="item.userId"
            :value="item.userId"
            :label="item.nickName || item.userNickName || item.userName"
          ></el-option>
        </el-select>
        <el-button
          @click="addProjectUser"
          style="margin-left: 16px"
          type="primary"
          size="mini"
          >+选择用户</el-button
        >
      </div>
      <div class="item" style="display: flex">
        <span><i>*</i> 渠道方：</span>
        <div style="display: flex">
          <div>
            <el-radio
              @change="changeRadio"
              v-model="params.channelType"
              label="1"
              >内部</el-radio
            >
            <div style="color: #999" class="relative top-1">
              内部渠道方为指定公司员工
            </div>
            <div style="margin-top: 12px" v-if="params.channelType == 1">
              <el-select v-model="params.channelForm" multiple="" collapse-tags>
                <el-option
                  v-for="item in qudaofangList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId"
                ></el-option>
              </el-select>
              <el-button
                type="primary"
                style="margin-left: 12px"
                size="mini"
                @click="addQDF"
                >+ 选择用户</el-button
              >
            </div>
          </div>
          <div style="margin-left: 120px">
            <el-radio
              @change="changeRadio"
              v-model="params.channelType"
              label="2"
              >外部</el-radio
            >
            <div style="color: #999" class="relative top-1">
              输入自定义的外部渠道方的名称
            </div>
            <div style="margin-top: 12px" v-if="params.channelType == 2">
              <el-input
                placeholder="请输入"
                v-model="params.channelSide"
              ></el-input>
            </div>
          </div>
        </div>
      </div>
      <div class="item" style="display: flex">
        <span>项目描述：</span>
        <el-input
          type="textarea"
          :rows="4"
          style="width: 700px"
          placeholder="请输入项目描述，限500字"
          v-model="params.projectDescribe"
        >
        </el-input>
      </div>
      <el-divider></el-divider>
      <div style="font-weight: bold; margin-bottom: 12px">联系方式</div>
      <div style="color: #999">
        联系方式信息仅项目负责人、内部渠道方、录入人本人、或管理员可见
      </div>
      <div style="color: #999">
        资金方与资产方联系方式至少有一方需要填写姓名与电话
      </div>
      <div class="project_phone">
        <div class="item">
          <div class="title2">资金方</div>
          <div class="phone">
            <span>联系人姓名：</span
            ><el-input
              v-model="userForm.fundName"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>联系人电话：</span
            ><el-input
              v-model="userForm.fundTel"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>微信：</span
            ><el-input
              v-model="userForm.fundWx"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>所属部门：</span
            ><el-input
              v-model="userForm.fundDept"
              style="width: 200px"
            ></el-input>
          </div>
        </div>
        <div class="item">
          <div class="title2">资产方</div>
          <div class="phone">
            <span>联系人姓名：</span
            ><el-input
              v-model="userForm.productName"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>联系人电话：</span
            ><el-input
              v-model="userForm.productTel"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>微信：</span
            ><el-input
              v-model="userForm.productWx"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>所属部门：</span
            ><el-input
              v-model="userForm.productDept"
              style="width: 200px"
            ></el-input>
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div style="margin-left: 31%">
        <div>修改立项信息，需要由管理员进行审核，点击下一步，将发起OA流程</div>
        <div>申请如果被通过，立即生效</div>

        <div>
          审核结果，请关注 [OA办公-我的流程]，或在 [我的项目日志] 中查看
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">下一步</el-button>
      </span>
    </el-dialog>
    <DeptUserList
      :deptName="deptName"
      @close="deptUserListTypeClose"
      v-if="deptUserListType"
      @submit="selectQDF"
      :userList="userList"
    />
    <SelectCompany
      v-if="SelectCompanyType"
      @close="SelectCompanyType = false"
      @submit="submitCompany"
    />
  </div>
</template>

  <script>
import { getXmglProcessFlow, queryProcessCompany } from "@/api/oa/deploy";
import { listSelectData,getChannelTree } from "@/api/xmgl/project";
import DeptUserList from "../../../businessInformation/projectDeploy/components/DeptUserList/index.vue";
export default {
  components: {
    DeptUserList,
  },
  props: {
    detail: Object,
  },
  data() {
    return {
      SelectCompanyType: false,
      projectUserList: [],
      deptName: "",
      qudaofangList: [],
      deptUserListType: false,
      dialogVisible: true,
      params: {
        id: "",
        deployId: "",
        projectUser: [],
        projectUserName: [],
        channelForm: [],
        channelFormName: [],

        channelType: null,
        channelSide: "",
      },
      userList:[],
      userForm: {
        userId: "",
        fundName: "",
        fundTel: "",
        fundWx: "",
        fundDept: "",
        productName: "",
        productTel: "",
        productWx: "",
        productDept: "",
      },
      oaModuleType:undefined
    };
  },
  created() {
    this.listSelectData();
  },
  mounted() {
    console.log(this.detail);
    this.params.id = this.detail.projectForm.id;
    this.params.deployId = this.detail.projectForm.deployId;
    this.userForm.userId = sessionStorage.getItem("userId") * 1;
  },
  methods: {
    listSelectData() {
      listSelectData().then((res) => {
        if (res.isOperation) {
          this.params.projectUser.push(sessionStorage.getItem("userId") * 1);
          this.projectUserList.push({
            userId: sessionStorage.getItem("userId") * 1,
            userNickName: sessionStorage.getItem("userNickName"),
          });
          this.params.projectUserName = this.projectUserList.map(
            (item) => item.userName || item.userNickName
          );
        }
        if (res.isChannel) {
          this.params.channelType = "1";
          this.params.channelForm.push(sessionStorage.getItem("userId") * 1);
          this.qudaofangList.push({
            userId: sessionStorage.getItem("userId") * 1,
            nickName: sessionStorage.getItem("userNickName"),
          });
          this.params.channelFormName.push(
            sessionStorage.getItem("userNickName")
          );
        }
        this.oaModuleType=(res.isChannel&&!res.isOperation)?36:30;
      });
    },
    addProjectUser() {
      this.userList=[];
      this.deptName = "运营";
      this.deptUserListType = true;
    },
    async addQDF() {
      const { rows } = await getChannelTree();
      this.userList = rows;
      this.deptName = "渠道";
      this.deptUserListType = true;
    },
    changeRadio() {
      if (this.params.channelType == 1) {
        this.params.channelSide = "";
      } else {
        this.params.channelForm = [];
      }
    },
    submitCompany(v) {
      this.SelectCompanyType = false;
      getXmglProcessFlow({ oaModuleType: this.oaModuleType, companyId: v }).then((res) => {
        let data = {
          projectForm: this.params,
          userform: this.userForm,
          oldData: this.detail,
        };

        sessionStorage.setItem("claimLxProData", JSON.stringify(data));
        this.$router.push({
          path: "/oaWork/updateProcessForm",
          query: {
            templateId: res.data.id,
            classificationId: res.data.classificationId,
            companyId: v,
            claimLxProject: true,
          },
        });
        this.$emit("close");
      });
    },
    submit() {
      if (this.params.projectUser.length == 0) {
        this.$message.warning("请选择项目负责人");
        return;
      }
      if (!this.params.channelType) {
        this.$message.warning("请选择渠道方");
        return;
      }
      if (this.params.channelType == 1 && this.params.channelForm.length == 0) {
        this.$message.warning("请选择渠道方");
        return;
      }
      if (this.params.channelType == 2 && !this.params.channelSide) {
        this.$message.warning("请输入渠道方");
        return;
      }
      if (
        (this.userForm.fundName && this.userForm.fundTel) ||
        (this.userForm.productName && this.userForm.productTel)
      ) {
        this.SelectCompanyType = true;
      } else {
        this.$message.warning(
          "资金方与资产方联系方式至少有一方需要填写姓名与电话"
        );
      }
    },
    handleClose() {
      this.$emit("close");
    },
    selectQDF(e) {
      console.log(e);
      if (this.deptName == "运营") {
        e.forEach((item) => {
          if (!this.params.projectUser.includes(item.userId)) {
            this.projectUserList.push(item);
          }
        });
        this.params.projectUser = this.projectUserList.map(
          (item) => item.userId
        );
        this.params.projectUserName = this.projectUserList.map(
          (item) => item.nickName || item.userNickName
        );
        console.log(this.params.projectUser);
      } else {
        e.forEach((item) => {
          if (!this.params.channelForm.includes(item.userId)) {
            this.qudaofangList.push(item);
          }
        });

        this.params.channelForm = this.qudaofangList.map((item) => item.userId);
        this.params.channelFormName = this.qudaofangList.map(
          (item) => item.nickName || item.userNickName
        );
      }
      this.deptUserListType = false;
    },
    deptUserListTypeClose(){
      this.deptUserListType=false;
      this.userList=[];
    }
  },
};
</script>

  <style lang="less" scoped>
.item {
  margin-top: 16px;
  span {
    display: inline-block;
    font-weight: bold;
    width: 100px;
    text-align: right;
    margin-right: 9px;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
.project_phone {
  display: flex;
  justify-content: space-between;
  .item {
    width: 46%;
    .title2 {
      padding-left: 16px;
      font-weight: bold;
      font-size: 16px;
      width: 100%;
      height: 40px;
      line-height: 40px;
      background: #f8f8f9;
    }
    .phone {
      margin-top: 20px;
      span {
        font-weight: bold;
        display: inline-block;
        margin-left: 9px;
        width: 90px;
        text-align: right;
      }
    }
  }
}
</style>
