<template>
  <div style="padding: 10px 16px">
    <div style="margin-bottom: 4px">所有立项项目的汇总列表</div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model.trim="queryParams.projectName"
          placeholder="请输入"
          clearable
          size="small"
          @clear="handleQuery"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="担保公司">
        <el-select
          v-model="queryParams.custNo"
          placeholder="请选择"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in companyList.custNoList"
            :key="dict.id"
            :label="dict.companyShortName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="资产方">
        <el-select
          v-model="queryParams.partnerNo"
          placeholder="请选择"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in companyList.partnerNoList"
            :key="dict.id"
            :label="dict.companyShortName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资金方" prop="fundFullName">
        <el-select
          v-model="queryParams.fundNo"
          placeholder="请选择"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="dict in companyList.fundNoList"
            :key="dict.id"
            :label="dict.companyShortName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      
       <el-form-item label="项目类型" prop="projectTypeList">
          <el-select
            v-model="queryParams.projectTypeList"
            placeholder="请选择"
            filterable
            clearable
            multiple
            @change="handleQuery"
            size="small"
          >
            <el-option
              v-for="dict in projectTypesList"
             :key="dict.id"
              :label="dict.dataName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
      <el-form-item label="产品分类">
        <el-select
          v-model="queryParams.businessTypeList"
          placeholder="请选择产品分类"
          filterable
          clearable
          multiple
          size="small"
        >
          <el-option
            v-for="dict in businessTypeList"
             :key="dict.id"
              :label="dict.dataName"
              :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <MoreSearch modelCode="PROJSETUP" :params="queryParams" v-show="showMoreSearch"></MoreSearch>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
         <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
            >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
          >
      </el-form-item>
    </el-form>
    <div>
      <el-button
        type="warning"
        v-hasPermi="['projectSummary:export']"
        plain
        icon="el-icon-download"
        size="mini"
        @click="handleExport"
        >导出</el-button
      >

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </div>

    <el-table
      v-loading="loading"
      :data="projectList"
    >
      <el-table-column
        type="index"
        label="序号"
        width="50"
        :index="columnIndex"
      />
      <el-table-column label="项目名称" show-overflow-tooltip="">
        <template slot-scope="scope">
          {{ scope.row.projectName }}
        </template>
      </el-table-column>

     <el-table-column label="项目类型" width="200" prop="projectType">
        <template slot-scope="scope">
          <div v-if="Boolean(scope.row.xmglProjectTypeList)">
            <div v-for="item in scope.row.xmglProjectTypeList" :key="item.id">
              {{ item.typeName }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="产品分类" width="200" prop="businessType">
        <template slot-scope="scope">
          <div v-if="Boolean(scope.row.xmglBusinessTypeList)">
            <div v-for="item in scope.row.xmglBusinessTypeList" :key="item.id">
              {{ item.typeName }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="立项时间" prop="projectDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.projectDate, "{y}年{m}月{d}日") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="进度状态" prop="projectStatus" width="200">
        <template slot="header">
          <div>
            进度状态
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content">
                进度状态：<br />1.对接中<br />2.尽调中<br />3.待上会<br />4.合同签署<br />5.已上线<br />特殊状态：<br />新增立项审核中<br />认领项目审核中<br />延期项目审核中<br />终止项目审核中<br />修改立项审核中<br />修改项目审核中<br />已终止<br />
              </div>
              <span class="relative bottom-1">①</span>
            </el-tooltip>
          </div>
        </template>
        <template slot-scope="scope">
          <span v-if="scope.row.projectStatus == '1'"
            ><i class="status-point" style="background-color: #9ddc88"></i
            >{{ " 对接中" }}</span
          >
          <span v-if="scope.row.projectStatus == '2'"
            ><i class="status-point" style="background-color: #9ddc88"></i
            >{{ "尽调中" }}</span
          >
          <span v-if="scope.row.projectStatus == '3'"
            ><i class="status-point" style="background-color: #9ddc88"></i
            >{{ " 待上会" }}</span
          >
          <span v-if="scope.row.projectStatus == '4'"
            ><i class="status-point" style="background-color: #9ddc88"></i
            >{{ " 合同签署" }}</span
          >
          <span v-if="scope.row.projectStatus == '5'"
            ><i class="status-point" style="background-color: #70b603"></i
            >{{ " 已上线" }}</span
          >
          <span v-if="scope.row.projectStatus == '12'"
            ><i class="status-point" style="background-color: #dcdcdc"></i
            >{{ " 已终止" }}</span
          >
          <span
            v-if="
              scope.row.projectStatus == '6' ||
              scope.row.projectStatus == '7' ||
              scope.row.projectStatus == '8' ||
              scope.row.projectStatus == '9' ||
              scope.row.projectStatus == '10' ||
              scope.row.projectStatus == '11'
            "
            ><i class="status-point" style="background-color: #ffc23e"></i
            >{{ scope.row.projectStatusLabel }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="进度" width="280px" prop="scheduleStatus">
        <template slot-scope="scope">
          <span v-if="scope.row.scheduleStatus == '1'">
            <el-image style="width:120px" :src="xmgljd1png"></el-image>
          </span>
          <span v-if="scope.row.scheduleStatus == '2'">
            <el-image  style="width:120px" :src="xmgljd2png"></el-image>
          </span>
          <span v-if="scope.row.scheduleStatus == '3'">
            <el-image  style="width:120px" :src="xmgljd3png"></el-image>
          </span>
          <span v-if="scope.row.scheduleStatus == '4'">
            <el-image  style="width:120px" :src="xmgljd4png"></el-image>
          </span>
          <span v-if="scope.row.scheduleStatus == '5'">
            <el-image  style="width:140px;margin-left:-10px" :src="xmgljd5png"></el-image>
          </span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getCompanyBusinessTypeList } from "@/api/businessInformation/companyInformation";

import { companyList,newCompanySelectList } from "@/api/businessInformation/companyInformation";
import { getDicts } from "@/api/system/dict/data";
import ClaimProject from "../projectDetails/components/ClaimProject.vue";
import { LoginIsAdmin, listProjectSummary } from "@/api/xmgl/project";
import xmgljd1 from "@/assets/images/xmgljd1.png";
import xmgljd2 from "@/assets/images/xmgljd2.png";
import xmgljd3 from "@/assets/images/xmgljd3.png";
import xmgljd4 from "@/assets/images/xmgljd4.png";
import xmgljd5 from "@/assets/images/xmgljd5.jpg";
import wsd from "@/assets/images/wsd.png";
import ysd from "@/assets/images/ysd.png";
import projectTip from "../project/components/projectTip.vue";
import {
  splicingListByCode
} from "@/api/businessInformation/productInformation";
export default {
  name: "ProjectSummary",
  components: {
    projectTip,
    ClaimProject,
  },
  data() {
    return {
      changeType: false,
      detail: null,
      ClaimProjectType: false,
      businessTypeList: [],
      companyList: {
        custNoList: [],
        partnerNoList: [],
        fundNoList: [],
      },
      projectTipType: false,
      userDepPostSelectType: false,
      multipleSelectionProp: [],
      openImport: false,
      UploadImporUrl: Object.freeze({
        importUrl: "/xmgl/project/importData",
        downloadUrl: "",
      }),
      //图片
      xmgljd1png: "",
      xmgljd2png: "",
      xmgljd3png: "",
      xmgljd4png: "",
      xmgljd5png: "",
      ysd: "",
      wsd: "",
      //是否是管理员
      isAdmin: false,
      //当前登录人id
      loginUserId: "",
      //认领的项目id
      claimprojectid: "",
      //认领项目deilog
      claimprojectdeilog: false,
      //资金方下拉框
      fundFullNameList: [],
      //资产方下拉框
      productFullName: [],
      //担保公司下拉框
      custFullNameList: [],
      //项目负责人下拉框
      userIdList: [],
      //渠道方下拉框
      channelSideList: [],
      paramsLabel: Object.freeze(["担保公司", "资产方", "资金方"]),
      //业务类型
      businessTypesList: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "消金",
          value: "0",
        },
        {
          label: "小微企业金融",
          value: "1",
        },
        {
          label: "房贷",
          value: "2",
        },
        {
          label: "车贷",
          value: "3",
        },
        {
          label: "电子保函",
          value: "4",
        },
      ],
      //项目状态
      projectStatusList: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "对接中",
          value: "0",
        },
        {
          label: "已停止",
          value: "1",
        },
        {
          label: "已删除",
          value: "2",
        },
        {
          label: "已上线",
          value: "3",
        },
      ],
      //进度状态
      scheduleStatusList: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "已联系",
          value: "0",
        },
        {
          label: "洽谈中",
          value: "1",
        },
        {
          label: "准入中",
          value: "2",
        },
        {
          label: "合作中",
          value: "3",
        },
      ],
      //锁定状态
      lockStatusList: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "已锁定",
          value: "0",
        },
        {
          label: "未锁定，可认领",
          value: "1",
        },
      ],
      // 遮罩层
      loading: true,
      // 选中数组
     
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      projectTypesList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        scope: "all",
        projectName: "",
        businessTypeList: [],
        projectTypeList: [],
        channelSide: "",
        fundShortName: null,
        fundFullName: "",
        productShortName: null,
        productFullName: "",
        custShortName: "",
        custFullName: "",
        userId: "",
        projectDate: null,
        projectStatus: "",
        scheduleStatus: "",
        lockStatus: "",
        overTime: null,
        status: null,
        createBr: null,
        updateBr: null,
      },
      showMoreSearch:false,
    };
  },
  created() {
    this.getList();
    this.getselectData();
  },
  methods: {
     async getProjectTypesList(){
      const {rows}=await splicingListByCode({firstDataCode:'project_type'});
      this.projectTypesList=rows;
    },
    async getBusinessTypeList(){
      const {rows}=await splicingListByCode({firstDataCode:'business_type'});
      this.businessTypeList =rows
    },
    columnIndex(index) {
      return (
        index + 1 + (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      );
    },

    async getDicts() {
      return new Promise((resolve) => {
        const tasks = [];
        const params = [ "company_type"];
        params.forEach((item) => {
          tasks.push(getDicts(item));
        });
        Promise.all(
          tasks.map((p) => {
            //.then返回需要的东西 .catch返回一些错误信息
            return p
              .then((e) => {
                return p;
              })
              .catch((err) => {
                return "错误了";
              });
          })
        )
          .then((res) => {
            
            this.companytypeList = res[0].data;
            resolve();
          })
          .catch((reason) => {
            console.log(reason);
          });
      });
    },
    getCompanyList() {
      let taskParams = ['cust','partner','fund'];
      
      const tasks = [];
      taskParams.forEach((item) => {
        tasks.push(newCompanySelectList({ selectCode: item, modelCode: 'PROJNAME' }));
      });
      Promise.all(
        tasks.map((p) => {
          //.then返回需要的东西 .catch返回一些错误信息
          return p
            .then((e) => {
              return p;
            })
            .catch((err) => {
              return "错误了";
            });
        })
      )
        .then((res) => {
          this.companyList.custNoList = res[0];
          this.companyList.partnerNoList = res[1];
          this.companyList.fundNoList = res[2];
        })
        .catch((reason) => {
          console.log(reason);
        });
    },
    async handlerOpen() {
      // const { rows } = await getCompanyBusinessTypeList();
      // this.businessTypeList = rows;
    },
    /** 查询下拉框数据*/
    async getselectData() {
       this.getProjectTypesList();
      this.getBusinessTypeList();
      this.handlerOpen();
      await this.getDicts();
      this.getCompanyList();
    },

    /** 查询【请填写功能名称】列表 */
    async getList() {
      this.loading = true;
      listProjectSummary({
        ...this.queryParams,
        scope: this.changeType ? "won" : "all",
      }).then((response) => {
        this.projectList = response.rows;

        this.total = response.total;
        this.loading = false;
      });
      this.loginUserId = window.sessionStorage.getItem("userId");
      //查询当前登录角色是否是管理员
      await LoginIsAdmin().then((response) => {
        this.isAdmin = response.data.isAdmin;
      });

      this.xmgljd1png = xmgljd1;
      this.xmgljd2png = xmgljd2;
      this.xmgljd3png = xmgljd3;
      this.xmgljd4png = xmgljd4;
      this.xmgljd5png = xmgljd5;
      this.ysd = ysd;
      this.wsd = wsd;
      this.ClaimProjectType = false;
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        scope: "all",
        projectName: null,
        businessTypeList: [],
        projectTypeList: [],
        channelSide: "",
        fundShortName: null,
        fundFullName: "",
        productShortName: null,
        productFullName: "",
        custShortName: "",
        custFullName: "",
        userId: "",
        projectDate: null,
        projectStatus: "",
        scheduleStatus: "",
        lockStatus: "",
        overTime: null,
        status: null,
        createBr: null,
        updateBr: null,
      };
      this.queryParams.moreSearch=undefined;
      this.handleQuery();
    },


    /** 导出按钮操作 */
    handleExport() {
      let nowDate = new Date();

      let date = {
        // 获取当前年份
        year: nowDate.getFullYear(),
        //获取当前月份
        month:
          nowDate.getMonth() + 1 < 10
            ? "0" + (nowDate.getMonth() + 1)
            : nowDate.getMonth() + 1,
        //获取当前日期
        date: nowDate.getDate(),
      };
      //拼接
      var datetime = date.year + "-" + date.month + "-" + date.date;

      this.download(
        "xmgl/project/exportSummary",
        {
          ...this.queryParams,
          pageSize:null,
          pageNum:null
        },
        `项目汇总_${datetime}.xlsx`
      );
    },
  },
};
</script>
<style lang="less" scoped>
.el-form-item {
  margin-bottom: 16px !important;
}
.el-row {
  margin-bottom: 6px !important;
}
.status-point {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}
/deep/ .el-image__inner {
  margin-top: 10px !important;
}
/deep/ .el-row {
  margin-bottom: 4px !important;
}
</style>
