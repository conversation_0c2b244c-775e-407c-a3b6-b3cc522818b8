<template>
  <div style="height: 100%">
    <div class="search">
      <div class="item">
        <span>目录名称</span>
        <el-input
          v-model="params.catalogueName"
          clearable=""
          placeholder="请输入目录名称"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>目录编号</span>
        <el-input
          v-model="params.catalogueCode"
          clearable=""
          placeholder="请输入目录编号"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
       <span>所属公司</span>
        <el-select
          placeholder="请选择所属公司"
          clearable=""
          style="width: 200px"
          v-model="params.unitId"
          filterable
        >
          <el-option
            v-for="item in projects"
            :key="item.id"
            :label="item.companyShortName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <div class="item">
        <span>所属部门</span>
        <el-select
          placeholder="请选择所属部门"
          clearable=""
          style="width: 200px"
          v-model="params.deptId"
          ref="selectUpResId"
          filterable
        >
          <el-option
            hidden
            :value="params.deptId"
            :label="params.pertainDeptName"
          >
          </el-option>
          <el-tree
            :data="treeSelect"
            :props="defaultProps2"
            :expand-on-click-node="false"
            :check-on-click-node="true"
            @node-click="dehandleNodeClick"
          >
          </el-tree>
        </el-select>
      </div>
      <el-button type="primary" icon="el-icon-search" @click="search"
        >搜 索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重 置</el-button>
    </div>
    <div class="solid"></div>
    <div class="content">
      <div class="left">
        <el-input
          v-model="filterText"
          placeholder="请输入目录名称"
          style="width: 210px"
        ></el-input>
        <el-tree
          class="filter-tree"
          :data="leftTreeList"
          :props="defaultProps"
          :default-expand-all="false"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
          ref="tree"
          node-key="id"
          :highlight-current="true"
        >
        </el-tree>
      </div>
      <div class="right">
        <div class="header_btn" style="display: flex; align-items: center">
          <el-button
            style="border-color: #aed8ff; background: #e8f4ff; color: #3fa1ff"
            icon="el-icon-plus"
            type="primary"
            size="mini"
            @click="newData"
          >新建</el-button
          >
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="multipleSelection.length != 1"
            @click="edit(false)"
          >修改</el-button
          >
          <el-button
            style="border-color: #aed8ff; background: #e8f4ff; color: #3fa1ff;position:absolute;right:0"
            type="primary"
            size="mini"
            @click="reset"
          >返回至主目录</el-button
          >
        </div>
        <el-table
          :data="tableData"
          style="width: 100%; margin-top: 16px; margin-left: 4px"
        >
          <el-table-column align="center" prop="date" width="55">
            <template slot="header" slot-scope="scope">
              <img
                v-if="multipleSelection.length == 0"
                @click="selectionChange(scope.row, 'allact')"
                class="selsct"
                :src="require('@/assets/images/omo_none.png')"
                alt=""
              />
              <img
                v-show="tableData.length > 0 && allType"
                @click="selectionChange(scope.row, 'alldel')"
                class="selsct"
                :src="require('@/assets/images/omo_act.png')"
                alt=""
              />
              <img
                v-show="multipleSelection.length > 0 && !allType"
                @click="selectionChange(scope.row, 'allact')"
                class="selsct"
                :src="require('@/assets/images/omo_show.png')"
                alt=""
              />
            </template>
            <template slot-scope="scope">
              <img
                v-show="!scope.row.acttype"
                @click="selectionChange(scope.row, 'act')"
                class="selsct"
                :src="require('@/assets/images/omo_none.png')"
                alt=""
              />
              <img
                v-show="scope.row.acttype"
                @click="selectionChange(scope.row, 'del')"
                class="selsct"
                :src="require('@/assets/images/omo_act.png')"
                alt=""
              />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="xh"
            label="序号"
            width="50"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="catalogueCode"ok
            label="目录编号"
            width="180"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="catalogueName"
            label="目录名称"
            width="180"
          > <template #default="{ row }">
          <div style="color: rgb(63, 161, 255);cursor: pointer" @click="edit(row)">{{row.catalogueName }}</div>
        </template>
        </el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="orderNum"
            label="排序号"
            width="100"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="parentCatalogueName"
            label="上级目录"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="catalogueSystemCode"
            label="系统目录编号"
            width="140"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="unitName"
            label="所属公司"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="deptName"
            label="所属部门"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="createBy"
            label="创建人"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="createTime"
            label="创建时间"
          />
            <el-table-column
            align="center"
            fixed="right"
            width="170"
            label="操作"
          >
            <template  #default="{ row }">
              <el-button @click="edit(row)" type="text" size="small"
                >查看详情</el-button
              >
              <el-button v-hasPermi="['directory:delete']" @click="del(row)" type="text" size="small" style="color:red"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="params.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :limit.sync="params.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <addItem
      :leftTreeList="leftTreeList"
      :editData="editData"
      @addItem="addItem"
      :disabled="disabledView"
      v-if="addItemType"
      :deTreeList="treeSelect"
      @close="addItemType = false"
      :parentId="params.id"
      :parentCatalogueName="params.parentCatalogueName"
    />
    <DepartmentAuthority
      @confimrDe="confimrDe"
      :auDeptIds="auDeptIds"
      :multipleSelection="multipleSelection"
      v-if="DepartmentAuthorityType"
      @close="DepartmentAuthorityType = false"
    />
    <PersonnelAuthority
      :auUserIds="auUserIds"
      :multipleSelection="multipleSelection"
      v-if="PersonnelAuthorityType"
      @confrimPer="confrimPer"
      @close="PersonnelAuthorityType = false"
    />
    <PostAuthority
      :auPostIds="auPostIds"
      @confirmPost="confirmPost"
      :multipleSelection="multipleSelection"
      v-if="PostAuthorityType"
      @close="PostAuthorityType = false"
    />
    <SelectItem
      :multipleSelection="multipleSelection"
      @confirm="confirmSelect"
      v-if="selectItemType"
      @close="selectItemType = false"
    />
  </div>
</template>

<script>
import Cookies from "js-cookie";
import SelectItem from "./components/SelectItem.vue";
import addItem from "./components/addItem.vue";
import DepartmentAuthority from "./components/DepartmentAuthority.vue";
import PersonnelAuthority from "./components/PersonnelAuthority.vue";
import PostAuthority from "./components/PostAuthority.vue";
import { authTreeList } from "@/api/directoryMation/directoryMation";
import {
  catalogueList,
  getTreeList,
  addLicenseCatalogueCatalogue,
  updateLicenseCatalogueCatalogue,
  delCatalogue,
  licenseCatalogueCatalogueDetail,
  authority,
  initAuthority,
  initDeptAuthority,
  initPostAuthority,
  initUserAuthority
} from "@/api/certificate/directory";

export default {
  name:"Directory",
  components: {
    addItem,
    DepartmentAuthority,
    PersonnelAuthority,
    PostAuthority,
    SelectItem,
  },
  data() {
    return {
      defaultProps2: {
        children: "children",
        label: "label",
      },
       deTreeList: [],
      DepartmentAuthorityType: false,
      PostAuthorityType: false,
      PersonnelAuthorityType: false,
      disabledView:false,
      editData: null,
      treeSelect: [],
      addItemType: false,
      defaultProps: {
        children: "fPiattaformas",
        label: "catalogueName",
      },
      projects: [],
      leftTreeList: [],
      multipleSelection: [],
      allType: false,
      selectItemType: false,
      total: 0,
      params: {
        catalogueName: "",
        catalogueCode: "",
        unitId: "",
        deptId: "",
        pertainDeptName: "",
        pageNum: 1,
        pageSize: 10,
        id: "",
        parentCatalogueName: "",
      },
      tableData: [],
      selectList: [],
      time: [],
      filterText: "",
      auUserIds: [],
      auPostIds: [],
      auDeptIds: [],
    };
  },
  watch: {
    multipleSelection(newval, oldval) {
      if (newval.length == 0) {
        this.allType = false;
      }
    },
    tableData(newval, oldval) {
      var flag = newval.every((item) => {
        return item.acttype;
      });
      this.allType = flag ? true : false;
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {},

 activated() {
  console.log(this.$refs.tree.setCurrentKey)
  this.$refs.tree.setCurrentKey(this.params.id)
  console.log(this.params.id,111111);
 },
  mounted() {
    this.getTreeList();
    this.getList();
  },
  methods: {
    confirmPost(e) {
      console.log(e);
      let ids = e.map((item) => item.postId);
      let list = [];
      this.multipleSelection.forEach((item) => {
        list.push({
          billId: item.id,
          billType: 0,
          authorityType: 1,
          authorityIds: ids,
          impower: Cookies.get("username"),
          impowerTime: this.$format(
            new Date().getTime(),
            "yyyy-MM-dd HH:mm:ss"
          ),
        });
      });
      console.log(list);
      authority({ authority: list }).then((res) => {
        if (res.code == 200) {
          this.$message.success("授权成功");
          this.getList();
          this.getTreeList();
          this.PostAuthorityType = false;
        }
      });
    },
    confrimPer(e) {
      console.log(e);
      let ids = e.map((item) => item.userId);
      let list = [];
      this.multipleSelection.forEach((item) => {
        list.push({
          billId: item.id,
          billType: 0,
          authorityType: 2,
          authorityIds: ids,
          impower: Cookies.get("username"),
          impowerTime: this.$format(
            new Date().getTime(),
            "yyyy-MM-dd HH:mm:ss"
          ),
        });
      });
      console.log(list);
      authority({ authority: list }).then((res) => {
        if (res.code == 200) {
          this.$message.success("授权成功");
          this.getList();
          this.getTreeList();
          this.PersonnelAuthorityType = false;
        }
      });
    },
    confimrDe(e) {
      console.log(e);
      let ids = e.map((item) => item.id);
      let list = [];
      this.multipleSelection.forEach((item) => {
        list.push({
          billId: item.id,
          billType: 0,
          authorityType: 0,
          authorityIds: ids,
          impower: Cookies.get("username"),
          impowerTime: this.$format(
            new Date().getTime(),
            "yyyy-MM-dd HH:mm:ss"
          ),
        });
      });
      console.log(list);
      authority({ authority: list }).then((res) => {
        if (res.code == 200) {
          this.$message.success("授权成功");
          this.getList();
          this.getTreeList();
          this.DepartmentAuthorityType = false;
        }
      });
    },
    authNew(v){
      if(v==0){
          initDeptAuthority();
      }else if(v==1){
          initPostAuthority();
      }else{
            initUserAuthority();

      }
    },
    auth(v) {
      if (this.multipleSelection.length == 0) {
        this.$message.warning("请选择数据");
        return;
      }
      if (this.multipleSelection.length == 1) {
        licenseCatalogueCatalogueDetail(this.multipleSelection[0].id).then(
          (res) => {
            if (res.code == 200) {
              if(res.data.auDeptIds)  {
                this.auDeptIds=res.data.auDeptIds.split(',').map(Number)||[]
                }else{
                  this.auDeptIds=[];
                };
              if(res.data.auPostIds) {this.auPostIds=res.data.auPostIds.split(',').map(Number)||[]

              }else{
                  this.auPostIds=[];
              };
              if(res.data.auUserIds) {this.auUserIds=res.data.auUserIds.split(',').map(Number)||[]

              }else{
                 this.auUserIds=[];
              };
              if (v == 0) {
                this.DepartmentAuthorityType = true;
              } else if (v == 1) {
                this.PostAuthorityType = true;
              } else {
                this.PersonnelAuthorityType = true;
              }
            }
          }
        );
      } else {
        this.auDeptIds = [];
        this.auPostIds = [];
        this.auUserIds = [];
        if (v == 0) {
          this.DepartmentAuthorityType = true;
        } else if (v == 1) {
          this.PostAuthorityType = true;
        } else {
          this.PersonnelAuthorityType = true;
        }
      }
    },
    newData() {
      this.editData = null;
      this.disabledView=false;
      this.addItemType = true;
    },
    addItem(e) {
      console.log(e,1);
      if (e.id) {
        const params={...e};
        delete params.createBy;
        updateLicenseCatalogueCatalogue(params).then((res) => {
          if (res.code == 200) {
            this.$message.success("修改成功");
            this.addItemType = false;
            this.getList();
            this.getTreeList();
          }
        });
      } else {
        addLicenseCatalogueCatalogue({ ...e }).then((res) => {
          if (res.code == 200) {
            this.$message.success("新建成功");
            this.addItemType = false;
            this.getList();
            this.getTreeList();
          }
        });
      }
    },
    edit(value) {
      const item = value ? value : this.multipleSelection[0];
      this.disabledView= value ? true : false;
      licenseCatalogueCatalogueDetail(item.id).then((res) => {
        if (res.code == 200) {
          console.log(res.data,22222222222)
         if(res.data.auDeptIds) res.data.auDeptIds=res.data.auDeptIds.split(',').map(Number)||[];
         if(res.data.auPostIds) res.data.auPostIds=res.data.auPostIds.split(',').map(Number)||[];
         if(res.data.auUserIds) res.data.auUserIds=res.data.auUserIds.split(',').map(Number)||[];
          this.editData = Object.assign(item, res.data);
          this.addItemType = true;
        }
      });
    },
    del(value) {
      const ids = value.id;
      this.$confirm("此操作将永久删除该目录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delCatalogue(ids).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              this.getTreeList();
              this.getList();
              this.multipleSelection=[];
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    confirmSelect(v) {
      this.multipleSelection = [...v];
      this.tableData.forEach((item) => {
        item.acttype = false;
      });
      this.getArrEqual(this.tableData, this.multipleSelection);
      this.selectItemType = false;
    },
    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    reset() {
      this.params = {
        catalogueName: "",
        catalogueCode: "",
        unitId: "",
        deptId: "",
        pertainDeptName: "",
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    async handleInitAuthority() {
      try {
        const { code } = await initAuthority();
        if (code == "200") {
          this.$modal.msgSuccess(response.msg);
        } else {
          this.$modal.msgError(response.msg);
        }
      } catch (error) {}
    },
    dehandleNodeClick(data) {
      console.log(data);
      this.params.pertainDeptName = data.label;
      this.params.deptId = data.id;
      this.$refs.selectUpResId.blur();
    },
    handleNodeClick(data) {
      console.log(data);
      this.params.id = data.id;
      this.params.parentCatalogueName = data.catalogueName;
      this.getList();
    },
    getTreeList() {
      getTreeList().then((res) => {
        this.leftTreeList = res.data;
      });
      authTreeList().then((res) => {
        this.treeSelect = res.data.dept;
        this.projects = res.data.company;
      });

    },
    getList() {
      let params = {
        ...this.params,
      };
      catalogueList({ ...params }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.tableData.forEach((item, index) => {
            item.acttype = false;
            item.xh = (this.params.pageNum - 1) * 10 + index + 1;
          });
          this.total = res.total;
          if (this.multipleSelection.length > 0) {
            this.getArrEqual(this.tableData, this.multipleSelection);
          }
        }
      });
    },
    getArrEqual(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        for (let j = 0; j < arr1.length; j++) {
          if (arr1[j].id === arr2[i].id) {
            arr1[j].acttype = true;
          }
        }
      }
    },
    selectionChange(v, type) {
      var list = [...this.tableData];
      switch (type) {
        case "act":
          list.map((val, idx) => {
            if (val.id == v.id) {
              val.acttype = true;
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          this.multipleSelection.push(v);
          break;
        case "del":
          list.map((val, idx) => {
            if (val.id == v.id) {
              delete val.acttype;
            }
          });
          this.multipleSelection.map((val, idx) => {
            if (val.id == v.id) {
              this.multipleSelection.splice(idx, 1);
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "alldel":
          list.map((val, idx) => {
            delete val.acttype;
          });
          let arr = [...this.multipleSelection];
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < list.length; j++) {
              if (arr[i].id == list[j].id) {
                arr.splice(i, 1);
              }
            }
          }
          this.multipleSelection = [...arr];
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "allact":
          var flag = list.every((item) => {
            return item.acttype;
          });
          if (flag) {
            this.allType = true;
            list.map((val, idx) => {
              delete val.acttype;
            });
            let arr = [...this.multipleSelection];
            for (let i = 0; i < arr.length; i++) {
              for (let j = 0; j < list.length; j++) {
                if (arr[i].id == list[j].id) {
                  arr.splice(i, 1);
                }
              }
            }
            this.multipleSelection = [...arr];
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          } else {
            let datalist = [...list, ...this.multipleSelection];
            let obj = {};
            let peon = datalist.reduce((cur, next) => {
              obj[next.id] ? "" : (obj[next.id] = true && cur.push(next));
              return cur;
            }, []); //设置cur默认类型为数组，并且初始值为空的数组

            this.multipleSelection = [...peon];
            list.map((val, idx) => {
              val.acttype = true;
              // this.multipleSelection.push(val);
            });
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          }

          break;
      }
      console.log(this.multipleSelection, "---");
    },
    toDetail(v) {
      getDetail(v.id).then((res) => {});
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.catalogueName.indexOf(value) !== -1;
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  width: 100%;
  padding: 16px;
  .left {
    width: 280px;
    height: 650px;
    overflow-y: auto;
    border: 1px solid #ccc;
    flex-shrink: 0;
    padding: 16px;
    box-sizing: border-box;
  }
  .right {
    width: calc(100% - 280px);
    padding-left: 12px;
    .el-button {
      height: 32px;
    }
  }
}
.search {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    display: flex;
    align-items: center;
    margin-right: 16px;
    span {
      margin-right: 9px;
    }
  }
}
.el-button {
  height: 36px;
  margin-left: 4px;
  margin-right: 12px;
}
.solid {
  width: 100%;
  height: 1px;
  background: #f2f2f2;
  margin-top: 12px;
}
.selsct {
  width: 14px;
  cursor: pointer;
}
</style>
