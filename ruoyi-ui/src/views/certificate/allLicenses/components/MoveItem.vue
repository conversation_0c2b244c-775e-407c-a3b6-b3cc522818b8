<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="500px"
      :before-close="handleClose"
    >
      <el-tree
        class="filter-tree"
        :data="treeData"
        :props="defaultProps"
      
        @node-click="handleNodeClick"
        ref="tree"
      >
      </el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="move(2)">移动到该目录</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  licenseLicenseMove,
} from "@/api/certificate/allLicenses";
export default {
  props: {
    multipleSelection: Array,
    treeData: Array,
  },
  data() {
    return {
      dialogVisible: true,
      moveId: null,
      defaultProps: {
        children: "fPiattaformas",
        label: "catalogueName",
      },
    };
  },
  methods: {
    handleNodeClick(data) {
      console.log(data);
      this.moveId = data.id;
    },
    move() {
      if (!this.moveId) {
        this.$message.warning("请选择目录");
        return;
      }
      let list = [];
      this.multipleSelection.map((item) => {
        list.push({
          licenseSystemCode: item.licenseSystemCode,
          targetCataId: this.moveId,
        });
      });
      let params = {
        oldZzLicenseMoveVo: list,
      };
      licenseLicenseMove({ ...params }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.$emit("success");
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>