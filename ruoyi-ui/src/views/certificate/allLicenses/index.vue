<template>
  <div style="height: 100%">
    <div class="search">
      <div class="item">
        <span>证照名称</span>
        <el-input
          v-model="params.licenseName"
          clearable=""
          placeholder="请输入证照名称"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>证照状态</span>
        <el-select
          v-model="params.licenseStatus"
          placeholder="请选择证照状态"
          clearable
          @clear="search"
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.license_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </div>
      <div class="item">
        <span>证照保管人</span>
        <el-input
          v-model="params.custodyName"
          clearable=""
          placeholder="请输入证照保管人"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>发证日期</span>
        <el-date-picker
          v-model="time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </div>
      <el-button type="primary" icon="el-icon-search" @click="search"
        >搜 索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重 置</el-button>
    </div>
    <div class="solid"></div>
    <div class="content">
      <div class="left">
        <el-input
          v-model="filterText"
          placeholder="请输入目录名称"
          style="width: 210px"
        ></el-input>
        <el-tree
          class="filter-tree"
          :data="leftTreeList"
          :props="defaultProps"
          :default-expand-all="false"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
          ref="tree"
        >
        </el-tree>
      </div>
      <div class="right">
        <div class="header_btn justify-between" style="display: flex; ">
          <div style="display: flex; align-items: center">
            <el-button
              type="primary"
              style="border-color: #aed8ff; background: #e8f4ff; color: #3fa1ff"
              icon="el-icon-plus"
              size="mini"
              @click="newData"
              v-hasPermi="['certificate:all:add']"
              >新建</el-button
            >

            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="multipleSelection.length != 1"
              @click="edit"
              v-hasPermi="['certificate:all:update']"
              >修改</el-button
            >
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="!multipleSelection.length"
              @click="del"
              v-hasPermi="['certificate:all:delete']"
              >废弃</el-button
            >
            <el-button
              @click="moveItemClick"
              type="primary"
              icon="el-icon-folder-opened"
              size="mini"
              :disabled="multipleSelection.length == 0"
              v-hasPermi="['certificate:all:arrange']"
              >证照整理</el-button
            >
            <el-button
              type="primary"
              style="border-color: #409eff; background: #c8e3ff; color: #409eff"
              icon="el-icon-check"
              size="mini"
              @click="submit"
              >发起批量借用流程</el-button
            >
            <el-button @click="selectItemType = true" type="primary" size="mini"
              >已选择({{ multipleSelection.length }})条</el-button
            >
          </div>
          <div>
            <el-button
              type="primary"
              style="border-color: #409eff; background: #c8e3ff; color: #409eff"
              size="mini"
              @click="go"
              v-hasPermi="['certificate:all:Jump']"
              >跳转至证照领用</el-button
            >
          </div>

          <!-- <el-button
            type="warning"
            plain
            icon="el-icon-upload2"
            size="mini"
            @click="handleImport"
            >一键导入</el-button
          > -->
        </div>
        <el-table
          :data="tableData"
          style="width: 100%; margin-top: 16px; margin-left: 4px"
        >
          <el-table-column align="center" prop="date" width="55">
            <template slot="header" slot-scope="scope">
              <img
                v-if="multipleSelection.length == 0"
                @click="selectionChange(scope.row, 'allact')"
                class="selsct"
                :src="require('@/assets/images/omo_none.png')"
                alt=""
              />
              <img
                v-show="tableData.length > 0 && allType"
                @click="selectionChange(scope.row, 'alldel')"
                class="selsct"
                :src="require('@/assets/images/omo_act.png')"
                alt=""
              />
              <img
                v-show="multipleSelection.length > 0 && !allType"
                @click="selectionChange(scope.row, 'allact')"
                class="selsct"
                :src="require('@/assets/images/omo_show.png')"
                alt=""
              />
            </template>
            <template slot-scope="scope">
              <img
                v-show="!scope.row.acttype"
                @click="selectionChange(scope.row, 'act')"
                class="selsct"
                :src="require('@/assets/images/omo_none.png')"
                alt=""
              />
              <img
                v-show="scope.row.acttype"
                @click="selectionChange(scope.row, 'del')"
                class="selsct"
                :src="require('@/assets/images/omo_act.png')"
                alt=""
              />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="xh"
            label="序号"
            width="50"
          />

          <el-table-column
            align="center"
            min-width="250"
            show-overflow-tooltip=""
            prop="licenseName"
            label="证照名称"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="see(scope.row)">{{
                scope.row.licenseName
              }}</el-button>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="issuingTime"
            label="发证日期"
            width="100"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="issuingUnit"
            label="发证单位"
            width="250"
          />

          <el-table-column
            align="center"
            show-overflow-tooltip=""
            label="状态"
            width="140"
          >
            <template #default="{ row }">
              {{ dict.label.license_status[row.licenseStatus] }}
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="custodyName"
            label="证照保管人"
            width="120"
          />

          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="version"
            label="证照版本"
            width="120"
          />

          <el-table-column
            align="center"
            fixed="right"
            width="270"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button @click="see(scope.row)" type="text" size="small"
                >查看详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="params.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :limit.sync="params.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <addItem
      :editData="editData"
      @save="addItem"
      :catalogueName="params.catalogueName"
      :catalogueId="params.catalogueId"
      :seeType="seeType"
      :uploadType="true"
      v-if="addItemType"
      :deTreeList="leftTreeList"
      @close="addItemType = false"
    />
    <SelectItem
      :multipleSelection="multipleSelection"
      @confirm="confirmSelect"
      v-if="selectItemType"
      @close="selectItemType = false"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="selectCompanyType = fasle"
      @submit="submitCompany"
    />
    <MoveItem
      :treeData="leftTreeList"
      @success="success"
      :multipleSelection="multipleSelection"
      v-if="moveItemType"
      @close="moveItemType = false"
    />
  </div>
</template>

<script>
import SelectCompany from "./components/SelectCompany.vue";
import addItem from "./components/addItem.vue";
import SelectItem from "./components/SelectItem.vue";
import MoveItem from "./components/MoveItem.vue";

import { getTreeList } from "@/api/certificate/directory";
import {
  licenseList,
  addLicense,
  licenseMainLicenseeDetail,
  getInformationFlow,
  zzCommonNotifyNotify,
  licenseHistoryExport,
} from "@/api/certificate/allLicenses";
export default {
  components: {
    SelectItem,
    addItem,
    SelectCompany,
    MoveItem,
  },
  dicts: ["license_status"],
  data() {
    return {
      selectCompanyType: false,
      seeType: false,
      editData: null,

      addItemType: false,
      defaultProps: {
        children: "fPiattaformas",
        label: "catalogueName",
      },
      projects: [],
      leftTreeList: [],
      multipleSelection: [],
      allType: false,
      selectItemType: false,
      moveItemType: false,
      total: 0,
      params: {
        licenseName: "",
        custodyName: "",
        catalogueId: "",
        catalogueName: "",
        pageNum: 1,
        pageSize: 10,
        licenseStatus: "",
      },
      tableData: [],
      selectList: [],
      time: [],
      filterText: "",
    };
  },
  watch: {
    multipleSelection(newval, oldval) {
      if (newval.length == 0) {
        this.allType = false;
      }
    },
    tableData(newval, oldval) {
      var flag = newval.every((item) => {
        return item.acttype;
      });
      this.allType = flag ? true : false;
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getTreeList();
      this.getList();
      this.openEdit();
    },
    openEdit() {
      if (this.$route.query.licenseId) {
        this.openEditDialog();
      }
    },
    openEditDialog() {
      this.seeType = false;
      licenseMainLicenseeDetail(this.$route.query.licenseId).then((res) => {
        if (res.code == 200) {
          this.editData = { ...res.data };
          this.$delete(this.editData, "revisionNote", "");
          this.addItemType = true;
        }
      });
    },
    saveEditDialog() {
      if (this.$route.query.id)
        zzCommonNotifyNotify({ id: this.$route.query.id });
    },
    submitCompany(e) {
      getInformationFlow({ companyId: e }).then((res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          sessionStorage.setItem(
            "oa-allLicensesTable",
            JSON.stringify(this.multipleSelection)
          );
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              cerAllLicenses: true,
            },
          });
        }
      });
    },
    async handleImport() {
      try {
        const { code } = await licenseHistoryExport();
        if (code == "200") {
          this.$modal.msgSuccess(response.msg);
        } else {
          this.$modal.msgError(response.msg);
        }
      } catch (error) {}
    },
    submit() {
      if (this.multipleSelection.length == 0) {
        this.$message.warning("请选择数据");
        return;
      }
      // const noAllow = ["2","3", "4"];
      // const allowBorrowing = this.multipleSelection.some((item) =>
      //   noAllow.includes(item.licenseStatus)
      // );
      // if (allowBorrowing) {
      //   this.$message.warning("请选择证照状态为在库的数据");
      //   return;
      // }
      this.selectCompanyType = true;
    },

    see(v) {
      console.log(v, 111111);
      licenseMainLicenseeDetail(v.id).then((res) => {
        if (res.code == 200) {
          this.editData = Object.assign({ ...v }, res.data);
          this.addItemType = true;
          this.seeType = true;
        }
      });
    },
    newData() {
      this.editData = null;
      this.seeType = false;
      this.addItemType = true;
    },
    async addItem(e) {
      console.log(e);
      addLicense({ ...e }).then(async (res) => {
        if (res.code == 200) {
          if (e.id) {
            this.$message.success("修改成功");
          } else {
            this.$message.success("新建成功");
          }

          this.addItemType = false;
          this.getTreeList();
          this.saveEditDialog();
          await this.getList();
          this.multipleSelection = [];
        }
      });
    },
    edit() {
      this.seeType = false;
      const item = { ...this.multipleSelection[0] };
      licenseMainLicenseeDetail(item.id).then((res) => {
        if (res.code == 200) {
          this.editData = Object.assign(item, res.data);
          this.$delete(this.editData, "revisionNote", "");
          this.addItemType = true;
        }
      });
    },
    del() {
      const ids = this.multipleSelection.map((item) => item.id);
      const params = {
        licenseSystemCode: this.multipleSelection[0].licenseSystemCode,
        licenseStatusTo: 3,
        licenseIdList: ids,
      };
      this.$confirm("此操作将永久废弃该证照, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          addLicense(params).then(async (res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              await this.getList();
              this.multipleSelection = [];
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消废弃",
          });
        });
    },
    success() {
      this.moveItemType = false;
      this.multipleSelection = [];
      this.getList();
    },
    confirmSelect(v) {
      this.multipleSelection = [...v];
      this.tableData.forEach((item) => {
        item.acttype = false;
      });
      this.getArrEqual(this.tableData, this.multipleSelection);
      this.selectItemType = false;
    },
    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    reset() {
      this.params = {
        licenseName: "",
        custodyName: "",
        catalogueId: "",
        pageNum: 1,
        pageSize: 10,
        licenseStatus: "",
      };
      this.time = [];
      this.getList();
    },
    moveItemClick() {
      this.moveItemType = true;
    },
    dehandleNodeClick(data) {
      console.log(data);
      this.params.pertainDeptName = data.label;
      this.params.pertainDeptId = data.id;
      this.$refs.selectUpResId.blur();
    },
    handleNodeClick(data) {
      console.log(data);
      this.params.catalogueId = data.id;
      this.params.catalogueName = data.catalogueName;
      this.getList();
    },
    getTreeList() {
      getTreeList().then((res) => {
        this.leftTreeList = res.data;
      });
    },
    go() {
      this.$router.push({ path: "/certificate/pendingProcessing" });
    },
    getList() {
      return new Promise((resolve, reject) => {
        let params = {
          ...this.params,
        };
        if (this.time.length > 0) {
          params.issuingBeginTime = this.$format(this.time[0], "yyyy-MM-dd");
          params.issuingEndTime = this.$format(this.time[1], "yyyy-MM-dd");
        }
        licenseList({ ...params }).then((res) => {
          if (res.code == 200) {
            this.tableData = res.rows;
            this.tableData.forEach((item, index) => {
              item.acttype = false;
              item.xh = (this.params.pageNum - 1) * 10 + index + 1;
            });
            this.total = res.total;
            if (this.multipleSelection.length > 0) {
              console.log(this.tableData, 111);
              console.log(this.multipleSelection, 111222);
              if (this.multipleSelection.length > 0) {
                this.getArrEqual(this.tableData, this.multipleSelection);
                resolve();
              }
            }
          }
        });
      });
    },
    getArrEqual(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        for (let j = 0; j < arr1.length; j++) {
          if (arr1[j].id === arr2[i].id) {
            arr1[j].acttype = true;
          }
        }
      }
    },
    selectionChange(v, type) {
      var list = [...this.tableData];
      switch (type) {
        case "act":
          list.map((val, idx) => {
            if (val.id == v.id) {
              val.acttype = true;
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          this.multipleSelection.push(v);
          break;
        case "del":
          list.map((val, idx) => {
            if (val.id == v.id) {
              delete val.acttype;
            }
          });
          this.multipleSelection.map((val, idx) => {
            if (val.id == v.id) {
              this.multipleSelection.splice(idx, 1);
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "alldel":
          list.map((val, idx) => {
            delete val.acttype;
          });
          let arr = [...this.multipleSelection];
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < list.length; j++) {
              if (arr[i].id == list[j].id) {
                arr.splice(i, 1);
              }
            }
          }
          this.multipleSelection = [...arr];
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "allact":
          var flag = list.every((item) => {
            return item.acttype;
          });
          if (flag) {
            this.allType = true;
            list.map((val, idx) => {
              delete val.acttype;
            });
            let arr = [...this.multipleSelection];
            for (let i = 0; i < arr.length; i++) {
              for (let j = 0; j < list.length; j++) {
                if (arr[i].id == list[j].id) {
                  arr.splice(i, 1);
                }
              }
            }
            this.multipleSelection = [...arr];
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          } else {
            let datalist = [...list, ...this.multipleSelection];
            let obj = {};
            let peon = datalist.reduce((cur, next) => {
              obj[next.id] ? "" : (obj[next.id] = true && cur.push(next));
              return cur;
            }, []); //设置cur默认类型为数组，并且初始值为空的数组

            this.multipleSelection = [...peon];
            list.map((val, idx) => {
              val.acttype = true;
              // this.multipleSelection.push(val);
            });
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          }

          break;
      }
      console.log(this.multipleSelection, "---");
    },
    toDetail(v) {
      getDetail(v.id).then((res) => {});
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.catalogueName.indexOf(value) !== -1;
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  width: 100%;
  padding: 16px;
  .left {
    width: 280px;
    height: 650px;
    overflow-y: auto;
    border: 1px solid #ccc;
    flex-shrink: 0;
    padding: 16px;
    box-sizing: border-box;
  }
  .right {
    width: calc(100% - 280px);
    padding-left: 12px;
    .el-button {
      height: 32px;
    }
  }
}
.search {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    display: flex;
    align-items: center;
    margin-right: 16px;
    span {
      margin-right: 9px;
    }
  }
}
.el-button {
  height: 36px;
  margin-left: 4px;
  margin-right: 12px;
}
.solid {
  width: 100%;
  height: 1px;
  background: #f2f2f2;
  margin-top: 12px;
}
.selsct {
  width: 14px;
  cursor: pointer;
}
/deep/ .el-select .el-input__inner {
  height: 36px;
}
</style>
