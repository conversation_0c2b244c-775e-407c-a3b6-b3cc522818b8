<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="88px"
    >
      <el-form-item label="主题" prop="themes">
        <el-input
          v-model="queryParams.themes"
          placeholder="请输入主题"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>

      <el-form-item label="借用人" label-width="90px" prop="borrowPerson">
        <el-input
          v-model="queryParams.borrowPerson"
          placeholder="请输入借用人"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" label-width="90px" prop="signStatus" v-if="tabTable=='LY'">
        <el-select
          v-model="queryParams.signStatus"
          placeholder="请选择状态"
          clearable
          @clear="handleQuery"
          style="width: 200px"
        >
          <el-option
            v-for="dict in licenseSignStatus"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" label-width="90px" prop="signStatus" v-else>
        <el-select
          v-model="queryParams.signStatus"
          placeholder="请选择状态"
          clearable
          @clear="handleQuery"
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.license_time_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-tabs
      v-model="tabTable"
      type="card"
      @tab-click="handleClick"
      style="margin-top: 10px"
    >
      <el-tab-pane
        :label="item.label"
        :name="item.name"
        v-for="(item, index) in tabTableList"
        :key="index"
      ></el-tab-pane>
    </el-tabs>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table
      :key="tabTable"
      v-loading="loading"
      :data="configList"
      @selection-change="handleSelectionChange"
      row-key="id"
      ref="multipleTable"
    >
      <!-- <el-table-column
        type="selection"
        width="55"
        align="center"
        reserve-selection
        fixed="left"
      /> -->
     
      <el-table-column
        label="所属公司"
        align="center"
        prop="pertainCompanyName"
      />
      <el-table-column label="主题" align="center">
        <template #default="{ row }">
          <el-button size="mini" type="text" @click="seePro(row)">{{
            row.themes
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="借用人" align="center" prop="borrowPerson" />
      <el-table-column label="状态" align="center" prop="signStatusLabel" v-if="tabTable=='LY'" />
      <el-table-column label="收回状态" align="center" prop="signStatusLabel" v-if="tabTable=='SH'" />
      <el-table-column label="状态" align="center" prop="backTimeStatusLabel" v-if="tabTable=='SH'" >
       <template #default="{ row }">
          <div :style="{color:colorBackTimeStatusLabel[row.backTimeStatusLabel]}">{{ row.backTimeStatusLabel }}</div> 
        </template>
      </el-table-column>
      <el-table-column label="借用时间" align="center" min-width="150px">
        <template #default="{ row }">
          {{ row.borrowStartTime || "-" }}至{{ row.borrowEndTime || "-" }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        v-if="tabTable=='LY'" 
      >
        <template #default="{ row }">
          <el-button size="mini" type="text" v-if="row.signStatus == 1"   @click="handleUpdateProcess(row,'证照借用明细')"

            >证照签领</el-button
          >
          <el-button size="mini" type="text" v-if="['审核中','已签领'].includes(row.signStatusLabel)"  @click="signOffDetails(row)"
            >签领明细</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="seePro(row)"
            >查看流程</el-button
          >
          <el-button size="mini" type="text" v-if="row.signStatus == 4" @click="handleUpdateView(row)"
            >查看原因</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        v-if="tabTable=='SH'" 
      >
        <template #default="{ row }">
          <el-button size="mini" type="text" v-if="row.signStatus == 3"   @click="handleUpdateProcess(row,'证照收回明细')"

            >证照收回</el-button
          >
          <el-button
            size="mini"
            type="text"
        
            @click="seePro(row)"
            >查看流程</el-button
          >
          
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <DetailDialog v-model="open" :flowld="flowld" :tabTable="tabTable" :isView="isView"  :reason="reason" @success="getList" :title="title"/>
    <SignOffDetails v-model="openSignOffDetails" :flowld="flowld" />
  </div>
</template>
<script>
import config from "./components/config";
import DetailDialog from "./components/DetailDialog.vue";
import SignOffDetails from "./components/SignOffDetails.vue";
import { detailList } from "@/api/certificate/pendingProcessing";
export default {
  name: "PendingProcessing",
  components: { DetailDialog ,SignOffDetails},
  dicts: ["license_sign_status", "license_time_status"],
  data() {
    return {
      ...config,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      tabTable: "LY",
      // 参数表格数据
      configList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        themes: "",
        borrowPerson: "",
        signStatus: "",
      },
      multipleSelection: [],
      // 表单参数
      open: false,
      flowld: "",
      isView: false,
      reason: "",
      colorBackTimeStatusLabel:Object.freeze({
        已到期:"rgba(217, 0, 27, 0.8)",
        即将到期:"#F59A23",
        未到期:"#1890FF",
      }) ,
      title:"",
      openSignOffDetails:false
    };
  },
  created() {
    this.init();
  },
  computed: {
    licenseSignStatus() {
      return this.dict.type.license_sign_status.filter(
        (item) => item.value != 5
      );
    },
  },
  methods: {
    init() {
      this.getList();
    },
    columnIndex(index) {
      return (
        index + 1 + (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      );
    },
    handleClick() {
      this.getList();
    },
    /** 查询参数列表 */
    async getList() {
      try {
        this.loading = true;
        const { rows, total } = await detailList({
          ...this.queryParams,
          tableType: this.tabTable,
        });
        this.configList = rows;
        this.total = total;
        this.loading = false;
      } catch (error) {
        this.loading = false;
      }


    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
      this.ids = selection.map((item) => item.id);
    },
    handleUpdateView(value) {
      this.isView = true;
      this.reason = value.reason;
      this.handleUpdate(value,'证照借用明细');
    },
    handleUpdateProcess(row){
       this.$router.push({
        path: "/oaWork/approveProcessForm",
        query: {
          businessId: row.businessId,
          readFlag:"F",
        },
      });
    },
    /** 修改按钮操作 */
    handleUpdate(value,title) {
      this.title=title;
      this.flowld = value.businessId;
      this.open = true;
    },
    signOffDetails(value){
      this.flowld = value.businessId;
      this.openSignOffDetails=true;
    },
    seePro(v) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: v.businessId,
          businessId: v.businessId,
          myActiviteType: true,
        },
      });
    },
  },
};
</script>
