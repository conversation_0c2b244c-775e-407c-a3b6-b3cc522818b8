<template>
  <div>
    <el-dialog
      title="删除管控规则"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleClose"
    >
      <div>是否确认删除此管控规则？</div>
      <div style="color: red">
        本规则导入的数据已装入 [展示系统] ，删除后已导入数据也将被删除
      </div>
      <div style="margin-top: 20px">
        删除已装入的规则需由管理员进行审核，点击[下一步]将发起删除规则审核流程
      </div>
      <div>申请如果被通过，立即生效[下一步]将发起删除规则审核流程</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">下一步</el-button>
      </span>
    </el-dialog>
  </div>
</template>
  
  <script>
export default {
  props: {
    info: String,
  },
  data() {
    return {
      dialogVisible: true,
    };
  },
  mounted() {},
  methods: {
    handleClose() {
      this.$emit("close");
    },
    submit() {
      this.$emit("submit");
    },
  },
};
</script>
  
  <style lang="less" scoped>
</style>