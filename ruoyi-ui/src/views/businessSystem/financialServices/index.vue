<template>
  <div class="p-5">
    <div>
      设置金融局管控下的担保公司信息<el-button
        @click="imgFlowType = true"
        type="text"
        style="margin-left: 12px"
        >流程说明</el-button
      >
    </div>
    <div>最大担保限额 = 净资产 × 担保倍数</div>
    <div style="display: flex; align-items: center; margin-top: 16px">
      <span style="margin-right: 9px">担保公司</span>
      <el-select
        filterable=""
        v-model="params.companyId"
        placeholder="请输入"
        style="width: 200px; margin-right: 12px"
      >
        <el-option
          v-for="item in companyList"
          :key="item.companyId"
          :value="item.companyId"
          :label="item.companyName"
        ></el-option>
      </el-select>
      <el-button size="mini" type="primary" @click="search">搜索</el-button>
      <el-button size="mini" @click="reset">重置</el-button>
    </div>
    <el-table
      :data="tableData"
      style="width: 100%; margin-top: 16px; border: 1px solid #e6ebf5"
    >
      <el-table-column
        type="index"
        label="序号"
        width="50"
        :index="columnIndex"
      />
      <el-table-column prop="companyName" label="担保公司" />
      <el-table-column prop="netAssets" label="净资产">
        <template slot-scope="scope">
          <span v-if="scope.row.netAssets">{{ scope.row.netAssets }}亿</span>
          <span v-else style="color: red">未设置</span>
        </template>
      </el-table-column>
      <el-table-column prop="guaranteeMultiple" label="担保倍数">
        <template slot-scope="scope">
          <span v-if="scope.row.guaranteeMultiple"
            >{{ scope.row.guaranteeMultiple }}x</span
          >
          <span v-else style="color: red">未设置</span>
        </template>
      </el-table-column>
      <el-table-column prop="maxLimit" label="最大担保限额">
        <template slot-scope="scope">
          <span v-if="scope.row.maxLimit">{{ scope.row.maxLimit }}亿</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="setCompany(scope.row)" v-hasPermi="['financialServices:set']">设置</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="params.total > 0"
      :total="params.total"
      :page.sync="params.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="params.pageSize"
      @pagination="getList"
    />
    <SetCompany
      v-if="setCompanyType"
      @close="setCompanyType = false"
      @submit="submitCompany"
      :itemData="itemData"
    />
    <el-dialog
      title="提示"
      :visible.sync="imgFlowType"
      width="1200px"
      :before-close="handleCloseImg"
    >
      <img style="width: 100%" :src="img" alt="" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseImg">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";

import {
  queryCompanyInfo,
  queryGuaranteeCompanyDetail,
  updateGuaranteeCompanyDetail,
} from "@/api/businessSystem/financialServices";
import imgFlow from "@/assets/images/setCompanyFlow.png";
import SetCompany from "./components/SetCompany.vue";
export default {
  components: {
    SetCompany,
  },
  data() {
    return {
      itemData: null,
      companyList: [],
      img: "",
      imgFlowType: false,
      setCompanyType: false,
      tableData: [],
      params: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
        companyId: "",
        controlType: "1",
        companyTypeCodeList:null,
      },
    };
  },
  mounted() {
    this.img = imgFlow;
    this.queryCompanyInfo();
  },
  methods: {
    queryCompanyInfo() {
      getDicts("company_type").then((res) => {
        const guaranteeCodes = res.data
          .filter((item) => item.dictLabel.includes("担保公司"))
          .map((item) => item.dictCode);
        this.params.companyTypeCodeList = guaranteeCodes.join(',');
        queryCompanyInfo({ companyTypeCodeList: guaranteeCodes.join(','),companyType:1 }).then(
          (res) => {
            this.companyList = res.data;
          }
        );
        this.getList();
      });
    },
    columnIndex(index) {
      return index + 1 + (this.params.pageNum - 1) * this.params.pageSize;
    },
    submitCompany(v) {
      console.log(v);
      updateGuaranteeCompanyDetail({ ...v, controlType: "1" }).then((res) => {
        this.$message.success("担保公司信息设置成功");
        this.setCompanyType = false;
        this.getList();
      });
    },
    search() {
      this.params.pageSize = 10;
      this.getList();
    },
    reset() {
      this.params = {
        total: 0,
        pageNum: 1,
        pageSize: 10,
        companyId: "",
        controlType: "1",
      };
      this.getList();
    },
    setCompany(v) {
      this.itemData = { ...v };
      this.setCompanyType = true;
    },
    getList() {
      queryGuaranteeCompanyDetail({ ...this.params }).then((res) => {
        this.tableData = res.rows;
        this.params.total = res.total;
      });
    },
    handleCloseImg() {
      this.imgFlowType = false;
    },
  },
};
</script>

<style lang="less" scoped>
</style>
