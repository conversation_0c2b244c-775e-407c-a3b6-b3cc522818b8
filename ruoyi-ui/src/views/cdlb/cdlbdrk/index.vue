
<!--绿本待入库页-->

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <div style="height: 25px">
<span
  style="font-size: 13px; margin-left: 1px;  color:#cccccc;">项目经理可在此页面提交入库申请，查看申请进度 </span>
      <el-button type="text" @click="poenImage">出库操作说明</el-button>
      </div>
      <el-divider class="e-a"></el-divider>

    </el-form>
<div style="height: 8px"></div>
    <div style="font-size: 13px; margin-left: 1px;  color:#cccccc; height: 17px"> 共{{total}}条申请记录</div>
    <div style="height: 8px"></div>
    <el-table v-loading="loading" :data="applyList" style="line-height: 70px" @selection-change="handleSelectionChange">
      <!--       <el-table-column type="selection" width="55" align="left" />
            <el-table-column label="主键" align="left" prop="id" />
            <el-table-column label="车贷绿本管理表主键" align="left" prop="prId" />
          <el-table-column label="出入库状态  " align="left" prop="garageState" >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.garageState) }}</span>
              </template>
            </el-table-column>-->

      <el-table-column label="申请状态" align="left" prop="applyFlag" >
        <template slot-scope="scope">
          <span >{{changeStatus(scope.row.applyFlag)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属项目" align="left" prop="projectName" />
      <el-table-column label="担保公司" align="left" prop="custName" />

      <el-table-column label="申请时间" align="left" prop="user11Time" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.user11Time, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人（项目经理）" align="left" prop="user11Name" />
      <el-table-column label="入库人（综合管理部）" align="left" prop="user12Name" />
      <!--      <el-table-column label="出库申请人id" align="left" prop="user21Id" />
            <el-table-column label="出库审核人id" align="left" prop="user22Id" />
            <el-table-column label="出库登记人id" align="left" prop="user23Id" />-->
      <el-table-column label="绿本数量" align="left" prop="counts" />
      <el-table-column label="入库申请说明" align="left" prop="warehouseRemark"  :show-overflow-tooltip="true" width="180px"    />

      <!--      <el-table-column label="状态，0正常 1禁用" align="left" prop="status" />
              <el-table-column label="创建时间" align="left" prop="createTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="更新时间" align="left" prop="updateTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>-->

      <!--      <el-table-column label="入库登记时间" align="left" prop="user12Time" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.user12Time, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="出库申请时间" align="left" prop="user21Time" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.user21Time, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="出库审核人时间" align="left" prop="user22Time" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.user22Time, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="出库登记人时间" align="left" prop="user23Time" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.user23Time, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>-->

      <el-table-column label="操作" align="left" class-name="small-padding fixed-width" width="300px">
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="small"
            @click="rukuxqy(scope.row)"
          >办理入库登记
          </el-button>

        </template>
      </el-table-column>
    </el-table>

    <!--    <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />-->

    <!-- 添加或修改车贷绿本出入库申请对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="30%" append-to-body>
      <div style="font-size: 18px; font-weight:bold;   color:#131313;height: 46px; text-align: -webkit-center;">选择入库项目</div>
      <div style="font-size: 16px;   color:#131313;text-align: -webkit-center;">请选择绿本入库的所属项目</div>
      <div style="        font-size: 16px;    color:#131313;text-align: -webkit-center;">如果项目不存在，需要由运营管理部管理员创建项目</div>
      <div style="height: 100px" >
        <div style="height: 20px"></div>
        <el-select v-model="valuelb" placeholder="请选择"  class="input__zhong">
          <el-option
            v-for="item in optionszt"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>


        <div   v-if="valuelb!=null"    style="        font-size: 13px; font-weight:bold;   color:#131313;text-align: -webkit-center;">担保公司：{{this.custName}}</div>
      </div>
      <div style="height: 30px"></div>

      <div style="text-align:center;border-radius: 3px; height: 52px"  class="dialog-footer">
        <el-button  type="primary" @click="xiaYiBuDaoRu">下一步</el-button>
        <el-button  @click="cancel">取 消</el-button>
      </div>
      <div style="height: 100px;      font-size: 13px;  color:#e7e0e0;  text-align: -webkit-center;">申请入库的绿本必须属于同一项目，同一担保公司</div>
    </el-dialog>
    <el-dialog :visible.sync="openimage" append-to-body>
      <el-image style="margin: 0px auto; display: flex; justify-content: center;align-items: center"  :src="inmage"></el-image>
      <div slot="footer" class="dialog-footer">
        <el-button style="display:block;margin:0 auto" @click="cancelimage">关闭</el-button>
      </div>
    </el-dialog>


    <!-- 添加绿本的方式 -->
    <el-dialog :title="title" :visible.sync="openliRuLvBenr" width="800px" append-to-body>
      <div style=" margin-left: 1px;  height: 36px">
        <span style="font-size: 14px; font-weight:bold;  color: #363636">入库项目名称：{{ lvbenParams.projectName }}</span>
        <span style="font-size: 14px; font-weight:bold; margin-left: 150px; color: #363636">  担保公司：{{
            lvbenParams.custName
          }}</span></div>
      <span
        style="font-size: 13px; margin-left: 1px;  color:#cccccc;">申请入库的绿本，必须属于同一个项目，同一个担保公司</span>
      <el-divider class="e-b"></el-divider>
      <div style="font-size: 15px; font-weight:bold;  color:#131313;height: 46px;      text-align: -webkit-center;">
        请选择录入方式
      </div>
      <div>
        <div style="margin-left: 23%">
          <el-button @click="daoruwenjianqingdan()" style="font-size: 70px; border-radius: 18px;">
            <div
              style="font-size: 15px; font-weight:bold; margin-left: 1px;  color:#131313;height: 46px; height: 30px;width: 150px;"></div>
            <div
              style="font-size: 15px; font-weight:bold; margin-left: 1px;  color:#131313;height: 46px; height: 40px">
              导入文件清单
            </div>
            <div style="font-size: 13px; margin-left: 1px;  color:#cccccc;">导入一个含清单的文</div>
            <div style="font-size: 13px; margin-left: 1px;  color:#cccccc;  height: 40px">件excel，批量录入</div>
          </el-button>
          <el-button @click="luruxinxi()" style="font-size: 70px; margin-left: 5%; border-radius: 18px;">
            <div
              style="font-size: 15px; font-weight:bold; margin-left: 1px;  color:#131313;height: 46px; height: 30px;width: 150px;"></div>
            <div
              style="font-size: 15px; font-weight:bold; margin-left: 1px;  color:#131313;height: 46px; height: 40px">
              录入信息
            </div>
            <div style="font-size: 13px; margin-left: 1px;  color:#cccccc;">录入车抵贷客户信息</div>
            <div style="font-size: 13px; margin-left: 1px;  color:#cccccc;  height: 40px"></div>
          </el-button>
        </div>
      </div>

      <div slot="footer" style="text-align:center;border-radius: 3px;" class="dialog-footer">
        <el-button type="primary" style="border-radius: 3px;" @click="submitFormLvBenr"> 下一步</el-button>
        <el-button style="border-radius: 3px;" @click="cancelliRuLvBen">取 消</el-button>
      </div>
    </el-dialog>


    <!--      文件的方式添加列表-->
    <el-dialog :title="title" :visible.sync="opendaoruwenjian" width="800px" append-to-body>
      <!-- 上传组件的div-->
      <div v-show="showProgress" style="text-align: center">

        <div>
          <span style="">上传文件：</span>
          <el-upload
            ref="uploadExcel"
            :limit="1"
            accept=".xlsx"
            :headers="upload.headers"
            :action="upload.url+ '?updateId=' + upload.updateId"
            :before-remove="beforeRemove"
            :on-change="handleChange"
            :before-upload="beforeUpload"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :on-remove="handleRemove"
            :auto-upload="false"
            :file-list="upFileList"
            :data="uploadData"
            drag>


            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
              <span style="font-size: 13px;color: #cccccc">请选择《.xlsx》文件导入</span><br>
              <div
                style="font-size: 13px;color: #cccccc">请选择绿本清单文件，导入系统。文件的格式示例如下：
              </div>
              <br>
              <el-image :src="inmagedrwjlb"></el-image>
            </div>

          </el-upload>
        </div>
        <!--          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>-->
        <br/>
        <!-- 关闭按钮-->
        <!--      <div style="text-align: center">-->
        <!--        <el-button size="mini" type="primary" @click="submitFileForm">上传</el-button>-->
        <!--        <el-button size="mini" @click="goBackFirst()">取消</el-button>-->
        <!--      </div>-->
        <div v-if="buttonFlag === false " style="width: 100%;height:60px; line-height: 45px;">
          <el-button size="mini" type="info" disabled>上传</el-button>
          <el-button size="mini" @click="goBackFirst()">取消</el-button>
        </div>
        <div v-if="buttonFlag === true " style="width: 100%;height:60px; line-height: 45px;">
          <el-button size="mini" type="primary" @click="handleImport">上传</el-button>
          <el-button size="mini" @click="goBackFirst()">取消</el-button>
        </div>
      </div>
      <div v-show="showProgressSubsequent">
        <el-table
          v-loading="showProgressSubsequent"
          element-loading-text="上传中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgb(255, 255, 255)"
          style="width: 100%"/>

      </div>

    </el-dialog>

    <!--     <el-table-column label="合同编号" align="left" prop="contractCode"/>-->
    <!--      <el-table-column label="客户名称" align="left" prop="clientName"/>-->
    <!--      <el-table-column label="身份证号" align="left" prop="clientCardId"/>-->
    <!--      <el-table-column label="邮寄日期" align="left" prop="mailDate"/>-->

    <!--      添加行数据方式添加列表-->
    <el-dialog :title="title" :visible.sync="openjianjialiebiaowenjian" width="950px" append-to-body  style="line-height: 70px" >
      <el-form :model="configurationByParameterCodeVo" label-width="40px" :inline="true" size="small"
               label-position="center">
        <el-table :data="configurationByParameterCodeVo.tableData" :row-class-name="rowClassName">
          <el-table-column label="序号" align="left" width="50px">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.'+scope.$index+'.id'">
                <span>{{ scope.$index + 1 }}</span>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="合同编号(选填)" align="left" width="240px">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.'+scope.$index+'.contractCode'">
                <el-input type="text" v-model="scope.row.contractCode" style="width: 90%"></el-input>
              </el-form-item>

            </template>
          </el-table-column>


          <el-table-column label="客户名称" align="left" width="120px">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.'+scope.$index+'.clientName'">
                <el-input type="text" v-model="scope.row.clientName" style="width: 100%"></el-input>
              </el-form-item>

            </template>
          </el-table-column>

          <el-table-column label="身份证号码" align="left" width="240px">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.'+scope.$index+'.clientCardId'">
                <el-input type="text" v-model="scope.row.clientCardId" style="width: 100%"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="邮寄日期" align="left" width="160px">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.'+scope.$index+'.mailDate'">
                <el-date-picker type="date" placeholder="选择日期" v-model="scope.row.mailDate"
                                style="width: 100%;"></el-date-picker>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="" width="100px">
            <template slot-scope="scope">

              <el-button
                style="    margin-bottom: 17px;"
                v-if="(scope.$index>0)"
                size="mini"
                icon="el-icon-delete"
                type="primary"
                @click="rowDelete(scope.row)">删除
              </el-button>

            </template>
          </el-table-column>
        </el-table>


      </el-form>

      <div style="margin-top:10px;height: 60px">
        <el-button @click="onAdd()" type="primary" size="small">+ 增加绿本</el-button>
      </div>
      <div
        style="   font-size: 13px; font-weight:bold;   color:#131313;  width: 87px; height: 10px;text-align: center;float: left">
        入库申请说明
      </div>
      <el-input type="textarea" :rows="5" placeholder="请输入（选填）" v-model=rkremark
                style="width: 35%;padding-left: 1px"></el-input>


      <div style="height: 20px"></div>
      <div style="text-align: center">
        <el-button @click="submitFormlbzj()" type="primary">保存</el-button>
        <el-button size="medium" @click="gobiaodantianjia()">取消</el-button>
      </div>

    </el-dialog>

    <!--           返回成功提示-->
    <el-dialog :visible.sync="openchdnggong" width="500px" append-to-body>
        <span
          style="display:table;font-size: 15px; font-weight:bold;   color:#131313;    margin:0 auto"> 录入成功</span><br>
      <span
        style="display:table;  font-size: 14px;   color:#131313;   margin:0 auto"> 入库项目名称：{{ returnedValue.pmName }}</span><br>
      <span
        style="display:table; font-size: 14px;     color:#131313;   margin:0 auto"> 共{{ returnedValue.totals }}条记录</span><br>
      <div  class="dialog-footer" style="height: 60px">
        <el-button style="display:block;margin:0 auto; width: 160px"  @click="tiaozhuanyemian()" type="primary">查看</el-button>
      </div>
      <span
        style="font-size: 13px; display:table;  color:#cccccc; margin:0 auto">点击查看，确定绿本信息正确后提交入库申请 </span>
    </el-dialog>


    <!--           导入返回成功提示-->
    <el-dialog :visible.sync="openchdnggongdr" width="500px" append-to-body>
        <span
          style="display:table;font-size: 15px; font-weight:bold;   color:#131313;    margin:0 auto"> 导入成功</span><br>
      <span
        style="display:table; font-size: 14px;   color:#131313;   margin:0 auto"> 入库项目名称：{{
          returnedValue.pmName
        }}</span><br>
      <span
        style="display:table;  font-size: 14px;     color:#131313;   margin:0 auto"> 共{{
          returnedValue.oktotal
        }}条记录,成功识别{{ returnedValue.oktotal }}条</span><br>
      <div  class="dialog-footer"  style="height: 51px">
        <el-button style="display:block;margin:0 auto; width: 160px" @click="tiaozhuanyemian()" type="primary">查看</el-button>
      </div>
      <span
        style="font-size: 13px; display:table;  color:#cccccc; margin:0 auto">点击查看，确定绿本信息正确后提交入库申请 </span>

    </el-dialog>


    <!--    入库相关-->

    <!--    删除成功提示-->
    <el-dialog :title="title" :visible.sync="openshanchuok"  style="text-align:center;border-radius: 3px;" width="30%" append-to-body>
      <div   style=" height: 120px;  font-size: 18px; font-weight:bold;   color:#131313; "> 删除成功</div>

      <div slot="footer"   style="text-align:center;border-radius: 3px;"    class="dialog-footer">
        <el-button type="primary" @click="submitFormshanchuoks">确定</el-button>

      </div>
    </el-dialog>
    <el-dialog :title="title" :visible.sync="opendtjsc"  style="text-align:center;border-radius: 3px;" width="30%" append-to-body>
      <div   style=" height: 30px;  font-size: 18px; font-weight:bold;   color:#131313; "> 是否删除已录入绿本信息</div>
      <div   style=" height: 30px; font-size: 14px;    color:#666666; "> 绿本数量：{{cdcksq.counts}}</div>
      <div   style=" height: 30px; font-size: 14px;    color:#666666; "> 所属项目：{{cdcksq.projectName}}</div>
      <div   style=" height: 30px; font-size: 14px;    color:#666666; "> 担保公司：{{cdcksq.custName}}</div>
      <div> 删除后无法恢复，您需要重新提交！</div>
      <div slot="footer"   style="text-align:center;border-radius: 3px;"    class="dialog-footer">
        <el-button type="primary" @click="submitFormtjsq">提交</el-button>
        <el-button @click="cancelchukusq">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="title" :visible.sync="poenrkbhsq"  style="text-align:center;border-radius: 3px;" width="30%" append-to-body>
      <div   style=" height: 30px;  font-size: 18px; font-weight:bold;   color:#131313; "> 是否删除该入库申请</div>
      <div   style=" height: 30px; font-size: 14px;   color:#666666; "> 绿本数量：{{cdcksq.counts}}</div>
      <div   style=" height: 30px; font-size: 14px;   color:#666666; "> 所属项目：{{cdcksq.projectName}}</div>
      <div   style=" height: 30px; font-size: 14px;    color:#666666; "> 担保公司：{{cdcksq.custName}}</div>
      <div> 删除后无法恢复，您需要重新提交！</div>
      <div slot="footer"   style="text-align:center;border-radius: 3px;"    class="dialog-footer">
        <el-button type="primary" @click="submitFormtjsq">提交</el-button>
        <el-button @click="cancelchukusq">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listApply, getApply, delApply, addApply, updateApply, updateApplyBYlvben} from "@/api/cdlb/loansaply";
import rklc from '@/assets/images/rklc.png';
import {addProject, delProject, getProject, listProject, updateProject, xiangMulist} from "@/api/cdlb/project";
import {getToken} from "@/utils/auth";
import {addllvbentianjia, cshlist} from "@/api/cdlb/info";
import daoruwenjianlb from "@/assets/images/daoruwenjianlb.png";

/*import daoruwenjianlb from '@/assets/images/daoruwenjianlb.png';
import secondIndex from '@/views/cdlb/cdlbProject/xiangmuxiangqing';
import {getToken} from "@/utils/auth";
import {addllvbentianjia, cshlist} from "@/api/cdlb/info";
import ScrollPane from "@/layout/components/TagsView/ScrollPane";*/
export default {
  // name: "Cdlbdrk",
  data() {
    return {


      //录入动态表单
      configurationByParameterCodeVo: {
        tableData: [
          {
            "id": null,
            "contractCode": '',
            "clientName": '',
            "clientCardId": '',
            "mailDate": ''


          }
        ],
        remark: "",

      },
      cdcksq: {
        projectName: null,
        custName: null,
        counts:null,
      },

      rkremark: null,
      shenqingJiluId:null,

      // 用户导入参数
      upload: {
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/cdlb/info/importData",
        // 对应的id
        updateId: null,
      },

      toBParams: {paramsOne: '', paramsTwo: ''}, //A页面向B页面传递的参数
      //文件集合
      upFileList: [],
      radio: '',
      //第二个页面传参对象
      querySecondObj: {
        cdlbId: null
      },
      //第二个页面传参对象
      querySecondObj1: {
        cdlbId: null
      },

      //首次进入页面，显示的总览信息是否显示
      firstTime: true,

      // 车贷绿本管理主表格数据
      projectList: [],


      openliRuLvBenr: false,
      opendaoruwenjian: false,
      openjianjialiebiaowenjian: false,
      showProgress: true,
      showProgressSubsequent: false,
      //向上一个页面返回的结果-告诉第二个页面展示和隐藏
      CRes: false,
      //按钮是否置灰 false-灰色，true-蓝
      buttonFlag: false,
      fileNames: null,

      returnedValue: {
        pmName: '',
        totals: '',
        oktotal: '',
      },
      //弹窗相关
      dialogOpen: false,
      //上传携带的参数
      uploadData: {
        reconDate: null,
        productNo: null,
      },
      // 查询参数
      lvbenParams: {
        projectName: null,
        custName: null,

      },
      //删除相关
      openshanchuok:false,
      opendtjsc:false,
      poenrkbhsq:false,



      openchdnggong: false,
      openchdnggongdr: false,

      inmagedrwjlb: '',
      // 表单参数


      options: [],
      options1: [],
      options2: [],
      options3: [],
      value: [],
      value1: [],
      value2: [],
      value3: [],
      //第二页返回的值
      BAnswer: '',
      //第二页返回的值
      CAnswer: '',


      //第二页面详细信息是否显示
      detailShow: false,
      //第三页面详细信息是否显示
      detailShow1: false,


      rowapplyId : null,
      rowApplyFlag: null,



























      radio1: '申请中',
      custName: '',
      projectName: '',
      projectId:null,
      optionszt: [],
      valuelb:null,



      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      openimage: false,
      inmage: '',
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 车贷绿本出入库申请表格数据
      applyList: [],

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        /* pageNum: 1,
         pageSize: 10,*/
        projectId: null,
        garageState: null,
        user11Id: null,
        user12Id: null,
        user21Id: null,
        user22Id: null,
        user23Id: null,
        applyFlag: null,
        applyFlagxin: null,
        status: '0',
        createTime: null,
        updateTime: null,
        user11Time: null,
        user12Time: null,
        user21Time: null,
        user22Time: null,
        user23Time: null,
        counts: null
      },
      // 表单参数
      form: {},
      fanHuickremark:null,
      // 表单校验
      rules: {
        projectId: [
          { required: true, message: "车贷绿本管理表主键不能为空", trigger: "blur" }
        ],
        garageState: [
          { required: true, message: "出入库状态  不能为空", trigger: "blur" }
        ],
        applyFlag: [
          { required: true, message: "出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回 20出库申请21出库审核22出库登记23出库完成29出库驳回不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  watch : {
    radio1: function (val) {
      if (val == '申请中') {
        this.loading = true;
        // 出入库状态  （01出库，02入库）
        this.queryParams.garageState = '02';
        this.queryParams.applyFlag = "!= '12'"; //非入库的
        listApply(this.queryParams).then(response => {
          this.applyList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
      if (val == '已入库') {
        this.loading = true;
        // 出入库状态  （01出库，02入库）
        this.queryParams.garageState = '02';
        this.queryParams.applyFlag = "= '12'"; //入库
        listApply(this.queryParams).then(response => {
          this.applyList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    //担保公司
    valuelb:function (val) {
      if (val != null) {
        getProject(val).then(response => {
          this.custName = response.data.custName;
          this.projectName = response.data.projectName;
          this.projectId = response.data.id;
        });
      }
    },
  },


  created() {
    this.getList();
  /*  this.getXiangMuList();*/

  },
  methods: {
    /** 查询车贷绿本出入库申请列表 */
    getList() {
      this.loading = true;
      // 出入库状态  （01出库，02入库）
      this.queryParams.garageState = '02';
      this.queryParams.applyFlagxin = '11';
      listApply(this.queryParams).then(response => {
       // console.log("listApply",response);
        this.applyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    //获取项目集合
    getXiangMuList(){
      xiangMulist().then(response =>{
      //  console.log("xiangMulist",response)
        this.optionszt = response.rows;
      })
    },


    fanHuiremark(){
      this.fanHuickremark=  this.$route.query.ckremark;
    },


    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectId: null,
        garageState: null,
        user11Id: null,
        user12Id: null,
        user21Id: null,
        user22Id: null,
        user23Id: null,
        remark: null,
        applyFlag: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        user11Time: null,
        user12Time: null,
        user21Time: null,
        user22Time: null,
        user23Time: null,
        counts: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.custName= '',
        this.projectName= '',
        this.projectId=null,
        this.valuelb=null,
        this.open = true;
      // this.title = "";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getApply(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改车贷绿本出入库申请";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateApply(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addApply(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/apply/export', {
        ...this.queryParams
      }, `apply_${new Date().getTime()}.xlsx`)
    },
    /** shuoming */
    poenImage() {
      this.openimage = true;
      this.inmage = rklc;
    },
    // 取消按钮
    cancelimage() {
      this.openimage = false;

    },

    changeStatus(row){
      if (row==10){
        return '待提交';
      }
      if (row==11){
        return '待入库';
      }
      if (row==12){
        return '已入库';
      }
      if (row==19){
        return '入库驳回';
      }

      if (row==20){
        return '待审核';
      }
      if (row==21){
        return '待出库';
      }
      if (row==22){
        return '已出库';
      }
      if (row==28){
        return '审核驳回';
      }
      if (row==29){
        return '出库驳回';
      }

      // [{value:"10",label:"待提交"},{value:"11",label:"待入库"},{value:"12",label:"已入库"},{value:"20",label:"待审核"},{value:"21",label:"待出库"},{value:"22",label:"已出库"}],

    },
    /*this.custName = response.data.custName;
    this.projectName = response.data.projectName;
    this.projectId*/
    xiaYiBuDaoRu(){
      if (this.projectId!=null){
        /*  this.$router.push({path: '/cdlb/lurulvbenxinxi',query:{ custName : this.custName ,projectId:this.projectId ,projectName:this.projectName}});*/
        this.lvbenParams.custName = null;
        this.lvbenParams.projectName = null;
        this.lvbenParams.custName = this.custName;
        this.returnedValue.pmName = null;
        this.returnedValue.pmName = this.projectName;
        this.lvbenParams.projectName = this.projectName;
        this.radio = null;
        this.upload.updateId = null;
        this.upload.updateId = this.projectId;
        this.configurationByParameterCodeVo.tableData = [];
        this.rkremark = null;
        this.open =false;
        this.onAdd();
        this.openliRuLvBenr = true;

      }else {
        this.$modal.msgWarning("请选择项目");
      }

    },

    // 一下为添加绿本方法



    //提交表单，将修改的数据提交至接口
    submitFormlbzj() {
      //提交前进行校验
      // <!--     <el-table-column label="合同编号" align="left" prop="contractCode"/>-->
      // <!--      <el-table-column label="客户名称" align="left" prop="clientName"/>-->
      // <!--      <el-table-column label="身份证号" align="left" prop="clientCardId"/>-->
      // <!--      <el-table-column label="邮寄日期" align="left" prop="mailDate"/>-->
      let tableData = this.configurationByParameterCodeVo.tableData;
      let a = 0;
      let cln = 0;
      let ctc = 0;
      let mde = 0;
      tableData.forEach(tab => {
        a++;
        if (tab.clientName == "" ) {
          cln++;



        }
        if ( tab.clientCardId == "") {
          ctc++;


        }
        if (tab.mailDate == "") {
          mde++;


        }
        let number = cln+ctc+ mde;
        if (number>0){
          let string = "";
          if (cln>0){
            string+="客户名称，"
          }
          if (ctc>0){
            string+="身份证号码，"
          }
          if (mde>0){
            string+="邮寄日期，"
          }
          this.$message.error('序号为:' + tab.xh  + ' '+ string+'不能为空');
          throw  new Error();
        }

      })
      this.returnedValue.totals = a;
      let datas = {
        id: null,
        tableData: null,
        rkremark: null
      }
      datas.id = this.upload.updateId;
      datas.tableData = tableData;
      datas.rkremark = this.rkremark;
      addllvbentianjia(datas).then(response => {
        if (response.code === 200) {
          this.openjianjialiebiaowenjian = false;
          this.configurationByParameterCodeVo.tableData = [];
          this.rkremark = null;
          this.openchdnggong = true

        } else {
          this.$message.error('添加失败');
        }
      })
    },
    //跳转页面
    tiaozhuanyemian() {
      this.openchdnggong = false;
      this.openchdnggongdr = false;
      this.showProgressSubsequent = false;
      this.opendaoruwenjian = false;
      this.showProgress = false;
      this.getList();
      this.getXiangMuList();
      /*   opendaoruwenjian
         showProgress  */
      //转至我的入库申请-该申请的详情页 待完成 。。。  当去为入库申请页
    },
    submitFileForm(response) {

      this.$refs.uploadExcel.clearFiles();
      /* this.$modal.msgSuccess("上传成功"+response.data+"条数据！");*/
      this.CRes = false;
      this.$emit('emitToParent', this.CRes);

      this.showProgressSubsequent = false;
      this.opendaoruwenjian = false;
      this.submitFileFormError();
      this.buttonFlag = false;

    },
    gobiaodantianjia() {
      this.openjianjialiebiaowenjian = false;
      this.configurationByParameterCodeVo.tableData = [];
    },
    //录入信息费动态新增表单
    onAdd() {
      var item = {

        "id": null,
        "contractCode": '',
        "clientName": '',
        "clientCardId": '',
        "mailDate": ''
      }
      this.configurationByParameterCodeVo.tableData.push(item)
    },
    /* <el-table-column label="合同编号" align="left" prop="contractCode"/>
     <el-table-column label="客户名称" align="left" prop="clientName"/>
     <el-table-column label="身份证号" align="left" prop="clientCardId"/>
     <el-table-column label="邮寄日期" align="left" prop="mailDate"/>*/
    //录入信息费删除
    rowDelete(row) {
      // this.$confirm('此操作将永久删除, 是否继续?','提⽰',{
      this.$confirm('删除后数据暂不丢失，点击保存之后数据会永久消失。是否继续?', '提⽰', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.configurationByParameterCodeVo.tableData.splice(row.xh - 1, 1)
        this.$message.success('删除成功!')
        this.checkInputParameter();
      }).catch(() => {
      })
      // this.configurationByParameterCodeVo.tableData.splice(row.xh -1,1);
    },

    // 其中row是⾏对象，rowindex是⾏号，从0开始。所以这样就能实现了序号(xh属性)递增并且取值为⾏号加1。
    rowClassName({row, rowIndex}) {
      row.xh = rowIndex + 1
    },

    lbck(row){
      this.$router.push({path: '/cdlb/cdlbprojectlbck',query:{productId:row.id}});
    },
    //文件上传前处理
    // 文件上传 - 上传前
    beforeUpload(file, fileList) {
      //定义文件最大的限制，单位：MB
      var maxSize = 2048;
      //文件的大小
      var fileSize = file.size / 1024 / 1024;
      //进行文件的判断
      if (fileSize <= 0) {
        this.$message.error('上传文件大小不能为 0 MB');
        return false;
      } else if (fileSize < maxSize) {
        this.uploadData = {}; //上传携带的参数名
        let promise = new Promise((resolve) => {
          this.$nextTick(function () {
            resolve(true);
          });
        });
        return promise;
      } else {
        this.$message.error(`上传文件大小不能超过2G!`);
        return false;
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
    },
    // 文件上传成功处理
    handleFileSuccess(response) {
      if (response.code === 500) {
        this.$modal.msgError(response.msg);
        this.showProgress = true;
        this.showProgressSubsequent = false;
        this.submitFileFormError();
        this.buttonFlag = false;
      }
      if (response.code === 501) {
        this.dialogOpen = true;
        this.$refs.uploadExcel.clearFiles();
        this.showProgress = true;
        this.showProgressSubsequent = false;
        this.submitFileFormError();
        this.buttonFlag = false;
      }
      if (response.code === 200) {
        this.handleRemove()
        this.$refs.uploadExcel.clearFiles();
        this.submitFileForm(response);
        this.showProgress = true;
        this.showProgressSubsequent = false;
        this.showProgress = false;
        this.openchdnggongdr = true
        this.returnedValue.oktotal = response.total;
        this.buttonFlag = false;
      }
    },
    submitFileFormError() {
      this.$refs.uploadExcel.clearFiles();

      this.CRes = true;
      this.$emit('emitToParent', this.CRes);
      this.buttonFlag = false;
    },
    // 提交上传文件
    handleImport() {
      let file = this.$refs.uploadExcel.uploadFiles[0];
      if (file != undefined) {
        this.showProgress = false;
        this.showProgressSubsequent = true;
        this.$refs.uploadExcel.submit();
      } else {
        this.showProgress = true;
        this.showProgressSubsequent = false;
      }

    },
    beforeRemove(file, upFileList) {
      this.buttonFlag = false;
    },
    handleRemove(file,fileList){

      /*  remove(file,fileList){*/
      /*   this.$http.get(process.env.VUE_APP_BASE_API + "/cdlb/info/importData" ,//这里我后端写的接口是根据文件id删除
           { params: { fileId:file.response.fileId}})*/
    },

    /*    file=null;
        fileList = null;*/

    handleChange(file, fileList) {


      if (file !== null) {
        this.buttonFlag = true;
      }
    },
    /*    //上传携带的参数
        uploadData: {
          reconDate: null,
          productNo: null,
        },*/
    goBackFirst() {
      this.buttonFlag = false;
      this.opendaoruwenjian = false;
      this.$refs.uploadExcel.clearFiles();
      this.CRes = true;
      this.$emit('emitToParent', this.CRes);
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/iData/importTemplate', {}, `模板_${new Date().getTime()}.xlsx`)
    },

    daoruwenjianqingdan() {
      this.radio = 1;
      this.$modal.msgSuccess("已选中导入文件清单");
    },
    luruxinxi() {
      this.radio = 2;
      this.$modal.msgSuccess("已选中录入信息");
    },
    /** 第二页返回的值 */
    getBRes(data) {
      if (data === true) {
        this.getList();
        this.detailShow = false;
        this.firstTime = true;

      }
    },
    /** 第二页返回的值 */
    getCRes(data) {
      if (data === true) {
        this.getList();
        this.detailShow = false;
        this.firstTime = true;

      }

      /*  this.BAnswer = data;*/
    },


    /*   /!** 查询车贷绿本管理主列表 *!/
       getList() {

         this.detailShow1 = false;
         this.loading = true;
         listProject(this.queryParams).then(response => {
           this.projectList = response.rows;
           this.total = response.total;
           this.loading = false;
         });
       },
       // 取消按钮
       cancel() {
         this.open = false;
         this.reset();
       },
       // 取消按钮
       cancelimage() {
         this.openimage = false;

       },

      */ // 取消按钮
    cancelliRuLvBen() {
      this.radio = null;
      this.openliRuLvBenr = false;

    },



    /** 新增按钮操作 */
    handleAddlvBenTianJia(row) {
      this.lvbenParams.custName = null;
      this.lvbenParams.projectName = null;
      this.lvbenParams.custName = row.custName;
      this.returnedValue.pmName = null;
      this.returnedValue.pmName = row.projectName;
      this.lvbenParams.projectName = row.projectName;
      this.radio = null;
      this.upload.updateId = null;
      this.upload.updateId = row.id;
      this.configurationByParameterCodeVo.tableData = [];
      this.rkremark = null;
      this.onAdd();
      this.openliRuLvBenr = true;


    },

    /** 提交按钮 */
    submitFormLvBenr() {

      if (this.radio != null) {
        if (this.radio == 1) {
          this.openliRuLvBenr = false;
          this.inmagedrwjlb = daoruwenjianlb;
          this.opendaoruwenjian = true;
          this.showProgress = true;
          this.showProgressSubsequent = false;


        }
        if (this.radio == 2) {
          this.openliRuLvBenr = false;
          this.openjianjialiebiaowenjian = true;
        }
      }
    },

    /** 新增 */
    addData(row) {
      this.$modal.confirm('创建成功后，项目经理即可以对此项目发起绿本入库申请').then(function () {
      }).then(() => {
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != null) {
              updateProject(this.form).then(response => {
                this.$modal.msgSuccess("修改项目成功");
                this.open = false;
                this.getList();
              });
            } else {
              addProject(this.form).then(response => {
                this.$modal.msgSuccess("创建项目成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      }).catch(() => {
      });
    },

    openss(valid) {
      const h = this.$createElement;
      this.$msgbox({
        title: '是否创建新的项目？',
        message: h('p', null, [
          h('dev', null, '创建成功后，项目经理即可以对此项目发起绿本入库申请 '),

        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',

      }).then(action => {
        addProject(valid).then(response => {
          this.$modal.msgSuccess("创建项目成功");
          this.open = false;
          this.getList();
        });
      });
    },
    openssa(valid) {
      const h = this.$createElement;
      this.$msgbox({
        title: '',
        message: h('p', null, [
          h('dev', null, '是否保存修改？'),

        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',

      }).then(action => {
      //  console.log(valid)
        this.$modal.msgSuccess("修改项目成功");

        /* updateProject(this.form).then(response => {
           this.$modal.msgSuccess("修改项目成功");
           this.open = false;
           this.getList();
         });*/
      });
    },
    xiangmuxq(row) {
      this.firstTime = false;
      //给第二个页面传数据
      this.toBParams.paramsOne = row.id
      this.$refs.bPage.initData();
      this.detailShow = true;
    },
    xiangmuxqs() {
      //调用详情页面的接口
      this.firstTime = true;
      // this.detailShow = true;
      //给第二个页面传数据
      this.getList();
      this.detailShow = false;
    },


    //新增业务期次按钮方法
    addprojectDate() {

    },
//、以上为入库相关、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、、

    rukuxqy(row){
     /* this.fanHuiremark();*/
      this.$router.push({path: '/cdlb/rukudengji',query:{productId:row.id}});
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ;
      this.shenqingJiluId = ids;
      //待提交
      if (row.applyFlag=== '10'){
        this.cdcksq.custName = row.custName
        this.cdcksq.projectName = row.projectName
        this.cdcksq.counts=  row.counts
        this.opendtjsc = true
        //入库驳回
      }if (row.applyFlag==='19'){
        this.cdcksq.custName = row.custName
        this.cdcksq.projectName = row.projectName
        this.cdcksq.counts=  row.counts
        this.poenrkbhsq = true
      }



      /*   this.$modal.confirm('是否确认删除本条申请编号吗？').then(function() {
            return updateApply(ids);
          }).then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          }).catch(() => {});*/
    },



    submitFormtjsq(){
      this.opendtjsc = false
      this.poenrkbhsq = false
      let   cdlbInOutApply ={
        id:null,
        status:null,
      }
      //申请编号id
      cdlbInOutApply.id =   this.shenqingJiluId
      cdlbInOutApply.status = '1'
      updateApplyBYlvben(cdlbInOutApply).then(response =>{
        this.openshanchuok = true
        this.getList();
      })

    },


    cancelchukusq(){
      this.openshanchuok=false
      this.opendtjsc=false
      this.poenrkbhsq=false

    },
    submitFormshanchuoks(){
      this.openshanchuok=false
      this.opendtjsc=false
      this.poenrkbhsq=false
    },

  }
};
</script>
<style>
.input__zhong{
  width: 300px;
  height: 30px;
  /*line-height: 30px;*/
  padding: 0 5px;
  font-size: 18px;

  display: block;
  margin: 15px auto;
}
</style>

<style>
/* el-divider 修改高度&虚线效果 */
.e-a{
  margin: 16px 0;
}
.e-b{
  margin: 24px 0;
}
</style>
