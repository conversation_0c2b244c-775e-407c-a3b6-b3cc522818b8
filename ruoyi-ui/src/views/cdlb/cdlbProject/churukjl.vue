<template>
  <div >
    <div v-show="firstTime">
      <el-button icon="el-icon-arrow-left"  style="    margin-top: -4px;"  @click="xiangmuxq()" plain>返回上级</el-button>
     <div style="height: 25px"></div>
      <div  style="height: 40px;   margin-top: 0px; font-size: 14px; font-weight: bold; color: #333333">  绿本信息</div>
      <el-descriptions   class="margin-top" :column="3">
        <el-descriptions-item label="合同编号">{{ cddata.projectName }}</el-descriptions-item>
        <el-descriptions-item label="客户姓名">{{ cddata.custName }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ cddata.pm }}</el-descriptions-item>
        <el-descriptions-item label="邮寄日期">{{ cddata.rm }}</el-descriptions-item>
        <el-descriptions-item label="绿本状态">{{ cddata.dm }}</el-descriptions-item>
      </el-descriptions>
      <el-divider class="e-sa"></el-divider>
      <div style="height: 13px"></div>
      <div>
        <div style="    font-size: 14px; color: #333333;
    font-weight: bold; height: 40px;">入库记录</div>
        <Timeline :mapper="mapper" :activities="activities"/>
      </div>
      <el-divider></el-divider>
      <div>
        <div style="    font-size: 14px; color: #333333;
    font-weight: bold; height: 40px;">出库记录</div>
        <Timeline :mapper="mapper" :activities="activitiesB"/>
      </div>
    </div>


  </div>
</template>

<script>

import {addProject, delProject, getProject, listProject, updateProject} from "@/api/cdlb/project";
import {getcdInfo, getcdInfostate, listcdleftInfo} from "@/api/cdlb/info";
import {listloaninfo, getloaninfo} from "@/api/cdlb/loanInfo";
import Timeline from '@/views/cdlb/cdlbProject/Timeline/index';
import {listliuchengchuin, listliuchengin} from "@/api/cdlb/dynamiclb";

export default {
  components: { Timeline},
  name: "CdlbProject",

  // props，接收从A传递来的参数对象，optionsData和A页面中的保持一直
  props: {
    optionsData: {
      type: Object
    }
  },
  data() {
    return {

      mapper: {
        user: '用户',
        descInfo: '描述',
        state: '状态'
      },
      activities: [],
      activitiesB: [],


   /* {
      content: '<div>admin &nbsp;&nbsp;&nbsp;&nbsp; <span style="color: #000000">审核未通过</span></div><div>拒绝发布</div>',

    }*/
      toBParams: { paramsOne: '', paramsTwo: '' }, //A页面向B页面传递的参数

      paramsOne: '',
      paramsTwo: '',


      firstTime: true,
      //第二页面详细信息是否显示
      detailShow: false,
      //第三页面详细信息是否显示
      detailShow1: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 车贷绿本管理主表格数据
      projectList: [],
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openjjxx: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        custId: null,
        custName: null,
        inApplyCount: null,
        inOkCount: null,
        outApplyCount: null,
        outAuditCount: null,
        outOkCount: null,
        status: null,
        createTime: null,
        pm: null,
        rm: null,
        dm: null,
        updateTime: null,
      },
      // 表单参数
      form: {},
      /*  待提交       待入库        入库完成                       待审核        待出库     出库完成
      10入库录入   11入库申请    12入库登记    13入库完成         20出库申请    21出库审核    22出库登记     23出库完成*/
      optionszt: [{value: "", label: "全部"}, {value: "11", label: "待入库"}, {
        value: "12",
        label: "已入库"
      }, {value: "20", label: "待审核"}, {value: "21", label: "待出库"}],
      options: [],
      options1: [],
      options2: [],
      options3: [],
      value: [],
      value1: [],
      value2: [],
      value3: [],
      //车贷id
      cdlbId: '',
      cddata: {
        projectName: null,
        custName: null,
        pm: null,
        rm: null,
        dm: null,
      },

      chaxunjjxx: {
        /** 主键 */
        id: null,
        /** 是否绑定绿本信息 Y是 N否 */
        cdlbBinding: null,
        /**已绑定的绿本信息表ID,未绑定时默认为0 */
        cdlbId: null,
        /** 外部系统平台编码 */
        platformNo: null,
        /** 担保公司编码 */
        custNo: null,
        /** 合作方编码 */
        partnerNo: null,
        /** 资金方编码 */
        fundNo: null,
        /** 产品编号 */
        productNo: null,
        /** 借据申请编号 */
        loanNo: null,
        /** 客户姓名 */
        clientName: null,
        /** 身份证号码 */
        clientCardId: null,
        /** 身份证地址 */
        clientCardAddress: null,
        /** 借据状态 */
        loanStatus: null,
        /** 进件时间 */
        applyTime: null,
        /** 借款金额（元） */
        loanAmt: null,
        /** 在贷余额（元） */
        balanceAmt: null,
        /** 借款期限（期数） */
        totalTerm: null,
        /** 放款时间 */
        loanTime: null,
        /** 到期日期 */
        dueDate: null,
        /** 放款流水号 */
        loanReqNo: null,
        /** 还款方式 */
        repayWay: null,
        /** 借款用途 */
        loanUse: null,
        /** 车辆品牌 */
        carBrandName: null,
        /** 车牌号 */
        carNo: null,
        /** 车架号 */
        carVin: null,
        /** 状态，0正常 1禁用 */
        status: null,

        merageArr1: [],
      },
      merageArr1: [],
      meragePos1: 0,
      merageArr2: [],
      meragePos2: 0,

      valuelb: null,
      //向上一个页面返回的结果-告诉第二个页面展示和隐藏
      BRes: false,
      //按钮是否置灰 false-灰色，true-蓝
      buttonFlag: false,
      // 表单校验
      rules: {
        projectName: [
          {required: true, message: "项目名称不能为空", trigger: "blur"},
          {min: 1, max: 20, message: '项目名称长度必须介于 1 和 20 之间', trigger: 'blur'}
        ],
        /*custId: [
          { required: true, message: "担保公司id不能为空", trigger: "blur" }
        ],*/
      }
    };
  },
  watch: {},

  created() {
    /* this.getList();*/
      this.initData();
  },
  methods: {
// 初始化参数
    initData() {

      this.paramsOne = this.optionsData.paramsOne

      this.loading = true;

      if (this.paramsOne != '') {

        getcdInfo(this.paramsOne).then(response => {


          this.cddata.projectName = response.data.contractCode;
          this.cddata.custName = response.data.clientName;
          this.cddata.pm = response.data.clientCardId;
          this.cddata.rm = response.data.mailDate;
          let changeStatus1 = this.changeStatus(response.data.lbFlag);
          this.cddata.dm = changeStatus1;
        });


        let  cdlbInfoDynamic  ={
          /** 出入库状态  （01出库，02入库） */
          garageState:null,
          /** 车贷申请表id */
          infoId:null,

        }
        cdlbInfoDynamic.garageState = '02';
        cdlbInfoDynamic.infoId = this.paramsOne;
        listliuchengin(cdlbInfoDynamic).then(response => {
          this.activities = response.rows
        });

        cdlbInfoDynamic.garageState = '01';
        cdlbInfoDynamic.infoId = this.paramsOne;
        listliuchengchuin(cdlbInfoDynamic).then(response => {
          this.activitiesB = response.rows
        });
      }
    },
    changeStatus(row) {
      if (row==10){
        return '待提交';
      }
      if (row==11){
        return '待入库';
      }
      if (row==12){
        return '已入库';
      }
      if (row==19){
        return '入库驳回';
      }

      if (row==20){
        return '待审核';
      }
      if (row==21){
        return '待出库';
      }
      if (row==22){
        return '已出库';
      }
      if (row==28){
        return '审核驳回';
      }
      if (row==29){
        return '出库驳回';
      }


    },
    /** 查询车贷绿本管理主列表 */
   /* getList() {
      this.loading = true;
      listProject(this.queryParams).then(response => {
        this.projectList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },*/
    // 取消按钮
    cancel() {
      this.open = false;
      this.openjjxx = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        custId: null,
        custName: null,
        inApplyCount: null,
        inOkCount: null,
        outApplyCount: null,
        outAuditCount: null,
        outOkCount: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        rm: null,
        pm: null,
        dm: null,
      };
      this.resetForm("form");
    },


    xiangmuxq() {
      this.cddata.projectName=null
      this.cddata.custName=null
      this.cddata.pm=null
      this.cddata.rm=null
      this.cddata.dm=null
      this.BRes = true;
      this.$emit('emitToParent', this.BRes);
    },

  }
};
</script>
<style>

.el-icon-map {
  width: 10px;
  height: 10px;
  background: #ffffff;
  border-radius: 50%;
  border: 3px solid #ffffff;
}


</style>
<style>
/* el-divider 修改高度&虚线效果 */
.e-sa{
  margin: 11px 0;
}
</style>
