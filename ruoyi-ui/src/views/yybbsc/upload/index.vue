<template>
  <div class="app-container">
    <!-- 上传组件的div-->
    <div style="text-align: center">
      <div class="block">
        <span class="demonstration">所属业务日期：</span>
        <el-date-picker
          size="small"
          style="width: 160px"
          v-model="timeValue"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy/MM/dd"
          placeholder="选择日期">
        </el-date-picker>
      </div>

      <div v-if="this.queryFromFirst.productNo === '0'">
        <span style="">上传附件：</span>
        <el-upload
          ref="uploadExcel"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :before-remove="beforeRemove"
          :on-change="handleChange"
          :before-upload="beforeUpload"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          :file-list="upFileList"
          :data="uploadData"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            <span>请选择富邦业务的日放还款明细文件，例如：**********.xlsx</span><br>
            <span>导入成功后，即会将该日的放还款数据展示在每日运营报表中</span>
          </div>
        </el-upload>
      </div>
      <div v-if="this.queryFromFirst.productNo === '1'">
        <span style="">上传附件：</span>
        <el-upload
          ref="uploadExcel"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :before-remove="beforeRemove"
          :on-change="handleChange"
          :before-upload="beforeUpload"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          :file-list="upFileList"
          :data="uploadData"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            <span>请选择北部湾业务的日放还款明细文件，例如：BBW20221117.xlsx</span><br>
            <span>导入成功后，即会将该日的放还款数据展示在每日运营报表中</span>
          </div>
        </el-upload>
      </div>

      <div v-if="buttonFlag === false || timeValue === null" style="width: 100%;height:60px; line-height: 45px;">
        <el-button size="mini" type="info" disabled>开始解析</el-button>
        <el-button size="mini" @click="goBackFirst()">取消</el-button>
      </div>
      <div v-if="buttonFlag === true && timeValue !== null" style="width: 100%;height:60px; line-height: 45px;">
        <el-button size="mini" type="primary" @click="handleImport()">开始解析</el-button>
        <el-button size="mini" @click="goBackFirst()">取消</el-button>
      </div>
    </div>

    <el-dialog :visible.sync="dialogOpen" width="500px" :close-on-click-modal="false" @close="dialogCloseHandle" append-to-body>
      <div style="text-align: center">
        <span class="spancol">文件解析完成</span><br><br>
        <span>业务日期：{{parseTime(this.respObj.reconDate, '{y}/{m}/{d}')}}</span><br>
        <span>放款金额：{{this.respObj.loanAmt}}</span><br>
        <span>实还本金：{{this.respObj.actPrintAmt}}</span><br>
        <span>用户实还息费：{{this.respObj.actIntAmt}}</span><br>
        <span>借条分润：{{this.respObj.jtFrAmt}}</span><br>
        <span>中保分润：{{this.respObj.zbFrAmt}}</span><br>
        <span>代偿本金：{{this.respObj.compensatePrintAmt}}</span><br>
        <span>代偿利息：{{this.respObj.compensateIntAmt}}</span><br>
        <span>代偿罚息：{{this.respObj.compensateOintAmt}}</span><br>
        <span>代偿总计：{{this.respObj.compensateTotalAmt}}</span><br>
        <span>代偿后还款本金：{{this.respObj.compensateRepayPrintAmt}}</span><br>
        <span>代偿后还款总金：{{this.respObj.compensateRepayTotalAmt}}</span><br>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogCloseHandle">放弃</el-button>
        <el-button type="primary" @click="submitFileForm">写入每日运营报表</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { importData, addDay } from "@/api/yybbsc/day";
import { getToken } from '@/utils/auth'

export default {
  props: {
    queryFromFirst: {
      type: Object,
      default: function() {
        let obj = {
          productNo: null,
        };
        return obj;
      },
      required: false
    }
  },
  // name: "Day",
  data() {
    return {
      //向上一个页面返回的结果-告诉第二个页面展示和隐藏
      BRes: false,
      //按钮是否置灰 false-灰色，true-蓝
      buttonFlag: false,
      //文件集合
      upFileList: [],
      //请求接口的参数
      // queryParams: {
      //   productNo: null,
      // },
      //弹窗相关
      dialogOpen: false,
      //上传携带的参数
      uploadData: {
        reconDate: null,
        productNo: null,
      },
      // 上传完成之后接口响应的对象
      // respObj: null,
      respObj: {
        //页面展示的
        reconDate: null,
        loanAmt: null,
        actPrintAmt: null,
        actIntAmt: null,
        jtFrAmt: null,
        zbFrAmt: null,
        compensatePrintAmt: null,
        compensateIntAmt: null,
        compensateOintAmt: null,
        compensateTotalAmt: null,
        compensateRepayPrintAmt: null,
        compensateRepayTotalAmt: null,
        //页面不展示，也要往库里插的
        productNo: null,
        intAmt: null,
        ointAmt: null,
        flAmt: null,
        advDefineAmt: null,
        deductAmt: null,
        reduceAmt: null,
        fzAmt: null,
        fundBalanceAmt: null,
        userBalanceAmt: null,
        accumProfitAmt: null,
      },
      //日期对象
      timeValue: null,
      // 用户导入参数
      upload: {
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/yybbsc/day/importData"
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询每日运营统计列表 */
    getList() {
      this.loading = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    //文件上传前处理
    // 文件上传 - 上传前
    beforeUpload(file, fileList) {
      //定义文件最大的限制，单位：MB
      var maxSize = 2048;
      //文件的大小
      var fileSize = file.size / 1024 / 1024;
      //进行文件的判断
      if (fileSize <= 0){
        this.$message.error('上传文件大小不能为 0 MB');
        return false;
      } else if (fileSize < maxSize) {
        this.uploadData = { reconDate: this.timeValue, productNo: this.queryFromFirst.productNo }; //上传携带的参数名
        let promise = new Promise((resolve) => {
          this.$nextTick(function () {
            resolve(true);
          });
        });
        return promise;
      } else {
        this.$message.error(`上传文件大小不能超过2G!`);
        return false;
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if (response.code === 500) {
        this.$modal.msgError("文件类型错误，请删除文件后重新选择后上传！");
      }
      if (response.remark === '200'){
        this.respObj = response;
        this.dialogOpen = true;
        this.$refs.uploadExcel.clearFiles();
      }
    },
    // 提交上传文件
    handleImport() {
      this.$refs.uploadExcel.submit();
    },
    beforeRemove(file, upFileList) {
      this.buttonFlag = false;
    },
    //点击文件列表中已上传文件进行下载
    // handlePreview(file) {
    //   this.buttonFlag = true;
    // },
    handleChange(file, fileList) {
      if (file !== null) {
        this.buttonFlag = true;
      }
    },
    submitFileForm() {
      addDay(this.respObj).then(response => {
        this.$modal.msgSuccess("新增成功");
        this.dialogCloseHandle();
        this.timeValue = null;
        this.BRes = true;
        this.$emit('emitToParent', this.BRes);
      })
    },

    dialogCloseHandle() {
      this.dialogOpen = false;
      this.respObj.reconDate = null;
      this.respObj.loanAmt = null;
      this.respObj.actPrintAmt = null;
      this.respObj.actIntAmt = null;
      this.respObj.jtFrAmt = null;
      this.respObj.zbFrAmt = null;
      this.respObj.compensatePrintAmt = null;
      this.respObj.compensateIntAmt = null;
      this.respObj.compensateOintAmt = null;
      this.respObj.compensateTotalAmt = null;
      this.respObj.compensateRepayPrintAmt = null;
      this.respObj.compensateRepayTotalAmt = null;
      this.respObj.productNo = null;
      this.respObj.intAmt = null;
      this.respObj.ointAmt = null;
      this.respObj.flAmt = null;
      this.respObj.advDefineAmt = null;
      this.respObj.deductAmt = null;
      this.respObj.reduceAmt = null;
      this.respObj.fzAmt = null;
      this.respObj.fundBalanceAmt = null;
      this.respObj.userBalanceAmt = null;
      this.respObj.accumProfitAmt = null;
      this.buttonFlag = false;
    },
    goBackFirst() {
      this.timeValue = null;
      this.buttonFlag = false;
      this.$refs.uploadExcel.clearFiles();
      this.BRes = true;
      this.$emit('emitToParent', this.BRes);
    }


  }
};
</script>
<style>
.spancol{
  color:#333333;
  font-weight:bold;
  font-size:15px;
  display: inline-block;
  margin-left: 0;
  /* padding-top:10px; */
}
</style>
