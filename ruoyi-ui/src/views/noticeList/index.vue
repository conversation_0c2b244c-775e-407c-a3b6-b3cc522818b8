<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>

    <div class="flex mb-2 justify-between">
      <div class="flex">
        <!-- <el-button
          @click="handleExport"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          >导出列表</el-button
        > -->
      </div>
    </div>
    <MyTable
      ref="table"
      :showIndex="true"
      :columns="columns"
      :source="configList"
    >
      <template #noticeName="{ record }">
        <el-button type="text" @click="goView(record, 'view')">{{
          record.noticeName
        }}</el-button>
      </template>
      <template #operate="{ record }">
        <el-button type="text" @click="goView(record, 'view')"
          >查看详情</el-button
        >
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { getReadRelationList ,updateReadRelation,readDownloadHistory} from "@/api/notice/homePage";
import { systemDataManageList } from "@/api/notice/dataSet";

import config from "./components/config";
export default {
  name: "NoticeList",

  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeName: undefined,
      },
      total: 0,
      configList: [],
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },
    async getList() {
      let data = await systemDataManageList();
      const flatData = XEUtils.toTreeArray(data.rows, {
        children: "fPiattaformas", // 指定子节点字段名
        clear: true,
      });
      const { rows, total } = await getReadRelationList(this.queryParams);
      rows.forEach((item) => {
        flatData.forEach((item1) => {
          if (item1.id == item.noticeType) {
            item.noticeTypeName = item1.dataName;
          }
        });
      });
      this.configList = rows;
      this.total = total;
    },
    handleQuery(value) {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    async goView(row) {
      await updateReadRelation({noticeId:row.noticeId});
      await readDownloadHistory({noticeId:row.noticeId,rdType:1,version:row.version});
      this.$router.push({
        path: `/noticeListDetail/${row.noticeId}`,
        query: {
          title: "通知公告详情",
        },
      });
    },
    handleExport() {
      this.download(
        "noticeMain/notice/indexExport",
        {
          noticeName: this.queryParams.noticeName,
        },
        `通知公告列表.xlsx`
      );
    },
  },
};
</script>
