<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="查看会议通知回执"
      :visible.sync="innerValue"
      width="850px"
      @open="handleOpen"
      @close="handleClose"
    >
      <el-scrollbar>
        <div style="max-height: 60vh">
          <el-descriptions :column="2" class="ml-28">
            <el-descriptions-item
              :label="item.label"
              v-for="(item, index) in formColumnsDialog"
              :key="index"
              >{{ form[item.value] }}</el-descriptions-item
            >
          </el-descriptions>
          <MyTable :columns="columnsDialog" :source="configList">
            <template #receiptState="{ record }">
              <span
                :style="{
                  color: record.receiptState == '2' ? 'color' : '',
                }"
                >{{receiptStateObj[record.receiptState]}}</span
              >
            </template>
          </MyTable>
          <pagination
            v-show="total > 20"
            :total="total"
            :page.sync="queryParams.pageNum"
            :page-sizes="[20, 50, 100]"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import {  notifyList } from "@/api/meeting/notice";

export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    queryParamsPro: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      ...config,
      configList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      total: 0,
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
    handleOpen() {
      this.getList();
    },
    async getList() {
      const { rows, total } = await notifyList({
        ...this.queryParams,
        correlationId:this.queryParamsPro.activeStatus==1?this.form.id: this.form.meetingId,
        hyNotifyType:'1'
      });
      this.configList = rows;
      this.total = total;
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    handleClose() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
      };
    },
  },
};
</script>
