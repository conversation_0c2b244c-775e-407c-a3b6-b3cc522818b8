<template>

  <div class="app-container">
    <div style="font-size: 15px"> <div :title="tipsMsg"  style="color: red"  ><pre>{{tipsMsg}}</pre></div></div>

    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="125px">
      <el-row>
        <el-col :span="123">
          <el-form-item label="外部系统名称" prop="platformNo">
            <el-select v-model="platformNoParam" placeholder="请选择系统名称" filterable multiple size="small"
                       >
              <el-option
                v-for="dict in platformNoSelect"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          </el-col>
          <el-col :span="123">
            <el-form-item label="担保公司" prop="custNo">
              <el-select v-model="custNoParam" placeholder="请选择担保公司" filterable multiple size="small"
                         >
                <el-option
                  v-for="dict in custNoSelect"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="123">
            <el-form-item label="合作方" prop="partnerNo">
              <el-select v-model="partnerNoParam" placeholder="请选择合作方" filterable multiple size="small"
                         >
                <el-option
                  v-for="dict in partnerNoSelect"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="123">
            <el-form-item label="资金方" prop="fundNo">
              <el-select v-model="fundNoParam" placeholder="请选择资金方" filterable multiple size="small"
                         >
                <el-option
                  v-for="dict in fundNoSelect"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
       </el-row>
       <el-row>
         <el-col :span="121">
           <el-form-item label="产品编码" prop="productNo">
             <el-select v-model="productNoParam" placeholder="请选择产品" filterable multiple size="small"
                        >
               <el-option
                 v-for="dict in productNoSelect"
                 :key="dict.value"
                 :label="dict.label"
                 :value="dict.value"
               />
             </el-select>
           </el-form-item>
         </el-col>
         <el-col :span="121">
          <el-form-item label="放款月份" prop="loanMonth">
            <el-date-picker
              v-model="queryParams.loanMonth"
              size="small"
              style="width: 208px"
              value-format="yyyy-MM"
              type="month"
              range-separator="-"
              placeholder="请选择放款月份"
            ></el-date-picker>
          </el-form-item>
         </el-col>
         <el-col :span="121">
          <el-form-item label="统计月份" prop="reconMonth">
            <el-date-picker
              v-model="queryParams.reconMonth"
              size="small"
              style="width: 208px"
              value-format="yyyy-MM"
              type="month"
              range-separator="-"
              placeholder="请选择统计月份"
            ></el-date-picker>
          </el-form-item>
         </el-col>
          <el-col :span="121">
            <el-form-item label="是否映射成功" prop="isMapping">
              <el-select v-model="queryParams.isMapping" placeholder="请选择是否映射成功" clearable size="small">
                <el-option
                  v-for="dict in dict.type.is_mapping"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="121">
             <MoreSearch modelCode="DATAREPORT" :params="queryParams" v-show="showMoreSearch" byId="companyCode"></MoreSearch>
          </el-col>
          <el-col :span="121">
            <el-form-item>
              <el-button  type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
                >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
              >
            </el-form-item>
          </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="mappingData"
        >映射
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['data:distribution:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="distributionList" @selection-change="handleSelectionChange">
<!--      <el-table-column type="selection" width="55" align="center"/>-->
      <el-table-column label="序号" align="center" prop="id" width="100" v-if="this.columns[0].visible"
                                                            fixed="left"/>
      <el-table-column label="外部系统平台名称" align="center" prop="platformNo" :key="Math.random()" width="100" v-if="this.columns[1].visible"
                       fixed="left">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.platform_no" :value="scope.row.platformNo"/>
        </template>
      </el-table-column>
      <el-table-column label="担保公司名称" align="center" prop="custNo" :key="Math.random()" width="100" v-if="this.columns[2].visible"
                       fixed="left">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cust_no" :value="scope.row.custNo"/>
        </template>
      </el-table-column>
      <el-table-column label="合作方名称" align="center" prop="partnerNo" :key="Math.random()" width="100" v-if="this.columns[3].visible"
                       fixed="left">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.partner_no" :value="scope.row.partnerNo"/>
        </template>
      </el-table-column>
      <el-table-column label="资金方名称" align="center" prop="fundNo" :key="Math.random()" width="100" v-if="this.columns[4].visible"
                       fixed="left">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.fund_no" :value="scope.row.fundNo"/>
        </template>
      </el-table-column>
      <el-table-column label="产品名称" align="center" prop="productNo" :key="Math.random()" width="100" v-if="this.columns[5].visible"
                       fixed="left">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.product_no" :value="scope.row.productNo"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="外部系统平台编码" align="center" prop="platformNo" fixed="left" v-if="this.columns[6].visible"/>
      <el-table-column label="担保公司编码" align="center" prop="custNo" fixed="left" v-if="this.columns[7].visible"/>
      <el-table-column label="合作方编码" align="center" prop="partnerNo" fixed="left" v-if="this.columns[8].visible"/>
      <el-table-column label="资金方编码" align="center" prop="fundNo" fixed="left" v-if="this.columns[9].visible"/>
      <el-table-column label="产品编码" align="center" prop="productNo" fixed="left" v-if="this.columns[10].visible"/> -->

      <el-table-column label="放款月份" align="center" prop="loanMonth" :key="Math.random()" v-if="this.columns[6].visible"/>
      <el-table-column label="统计月份" align="center" prop="reconMonth" :key="Math.random()" v-if="this.columns[7].visible"/>
      <el-table-column label="在贷余额" align="center" prop="loanBalanceAmount" :key="Math.random()" v-if="this.columns[8].visible"/>
      <el-table-column label="在贷笔数" align="center" prop="loanRemainNumber" :key="Math.random()" v-if="this.columns[9].visible"/>
      <el-table-column label="类型" align="center" prop="balanceDistributionType" :key="Math.random()" v-if="this.columns[10].visible"/>
      <el-table-column label="Mn笔数" align="center" prop="mNumber" :key="Math.random()" v-if="this.columns[11].visible"/>
      <el-table-column label="Mn贷款余额" align="center" prop="mBalanceAmount" :key="Math.random()" v-if="this.columns[12].visible"/>
      <el-table-column label="是否映射成功" align="center" prop="isMapping" :key="Math.random()" v-if="this.columns[13].visible">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_mapping" :value="scope.row.isMapping"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :key="Math.random()" v-if="this.columns[14].visible"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改外部系统平台余额分布对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="外部系统平台编码" prop="platformNo">
          <el-select v-model="form.platformNo" placeholder="请选择外部系统平台编码">
            <el-option
              v-for="dict in dict.type.platform_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="担保公司编码" prop="custNo">
          <el-select v-model="form.custNo" placeholder="请选择担保公司编码">
            <el-option
              v-for="dict in dict.type.cust_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合作方编码" prop="partnerNo">
          <el-select v-model="form.partnerNo" placeholder="请选择合作方编码">
            <el-option
              v-for="dict in dict.type.partner_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资金方编码" prop="fundNo">
          <el-select v-model="form.fundNo" placeholder="请选择资金方编码">
            <el-option
              v-for="dict in dict.type.fund_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品编码" prop="productNo">
          <el-select v-model="form.productNo" placeholder="请选择产品编码">
            <el-option
              v-for="dict in dict.type.product_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="放款月份" prop="loanMonth">
          <el-input v-model="form.loanMonth" placeholder="请输入放款月份"/>
        </el-form-item>
        <el-form-item label="统计月份" prop="reconMonth">
          <el-input v-model="form.reconMonth" placeholder="请输入统计月份"/>
        </el-form-item>
        <el-form-item label="在贷余额" prop="loanBalanceAmount">
          <el-input v-model="form.loanBalanceAmount" placeholder="请输入在贷余额"/>
        </el-form-item>
        <el-form-item label="在贷笔数" prop="loanRemainNumber">
          <el-input v-model="form.loanRemainNumber" placeholder="请输入在贷笔数"/>
        </el-form-item>
        <el-form-item label="Mn笔数" prop="mNumber">
          <el-input v-model="form.mNumber" placeholder="请输入Mn笔数"/>
        </el-form-item>
        <el-form-item label="Mn贷款余额" prop="mBalanceAmount">
          <el-input v-model="form.mBalanceAmount" placeholder="请输入Mn贷款余额"/>
        </el-form-item>
        <el-form-item label="是否映射成功" prop="isMapping">
          <el-select v-model="form.isMapping" placeholder="请选择是否映射成功">
            <el-option
              v-for="dict in dict.type.is_mapping"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDistribution,
  getDistribution,
  delDistribution,
  addDistribution,
  updateDistribution,
  dataMapping
} from "@/api/data/distribution";
import {getSysDictRefList,getSelectSysDictRefList} from '@/api/ref/ref';
import {tipsMsg} from '../common';
import { getDicts} from "@/api/system/dict/data";
import { clone } from "xe-utils";


export default {
  name: "Distribution",
  dicts: ['is_mapping', 'platform_no', 'fund_no', 'product_no', 'cust_no', 'partner_no'],
  data() {
    return {
         //外部系统
      platforms:[],
      //担保公司
       custNos:[],
      //合作方
      partners:[],
      //资金方
      funds:[],
      //产品
      products:[],

      querydatatype:"cust_no",
      externalsystem:"platform_no",
      partnerscode:"partner_no",
      capitalcode:"fund_no",
      productcode:"product_no",

      platformNoParam: '',
      custNoParam: '',
      partnerNoParam: '',
      fundNoParam: '',
      productNoParam: '',


      sysDictRefParam: {
        dictType: '',
        dictValue: '',
        pDictType: '',
        pDictValue: '',
        selectDictDatas:''
      },
      platformNoSelect: [],
      custNoSelect: [],
      fundNoSelect: [],
      partnerNoSelect: [],
      productNoSelect: [],

      // 列信息
      columns: [
        {key: 0, label: `序号`, visible: false},
        {key: 1, label: `外部系统平台名称`, visible: true},
        {key: 2, label: `担保公司名称`, visible: true},
        {key: 3, label: `合作方名称`, visible: true},
        {key: 4, label: `资金方名称`, visible: true},
        {key: 5, label: `产品名称`, visible: true},
        {key: 6, label: `放款月份`, visible: true},
        {key: 7, label: `统计月份`, visible: true},
        {key: 8, label: `在贷余额`, visible: true},
        {key: 9, label: `在贷笔数`, visible: true},
        {key: 10, label: `类型`, visible: true},
        {key: 11, label: `Mn笔数`, visible: true},
        {key: 12, label: `Mn贷款余额`, visible: true},
        {key: 13, label: `是否映射成功`, visible: true},
         {key: 14, label: `备注`, visible: true},
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 外部系统平台余额分布表格数据
      distributionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        productNo: null,
        loanMonth: null,
        reconMonth: null,
        loanBalanceAmount: null,
        loanRemainNumber: null,
        balanceDistributionType: null,
        mNumber: null,
        mBalanceAmount: null,
        isMapping: null,
        params:{
           moduleTypeOfNewAuth: 'DATAREPORT'
        }
      },
      tipsMsg:null,

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        platformNo: [
          {required: true, message: "外部系统平台编码不能为空", trigger: "change"}
        ],
        loanMonth: [
          {required: true, message: "放款月份不能为空", trigger: "blur"}
        ],
        reconMonth: [
          {required: true, message: "统计月份不能为空", trigger: "blur"}
        ],
        loanBalanceAmount: [
          {required: true, message: "在贷余额不能为空", trigger: "blur"}
        ],
        loanRemainNumber: [
          {required: true, message: "在贷笔数不能为空", trigger: "blur"}
        ],
        balanceDistributionType: [
          {required: true, message: "类型不能为空", trigger: "change"}
        ],
        mNumber: [
          {required: true, message: "Mn笔数不能为空", trigger: "blur"}
        ],
        mBalanceAmount: [
          {required: true, message: "Mn贷款余额不能为空", trigger: "blur"}
        ],
        createTime: [
          {required: true, message: "创建时间不能为空", trigger: "blur"}
        ],
        updateTime: [
          {required: true, message: "更新时间不能为空", trigger: "blur"}
        ]
      },
      showMoreSearch:false
    };
  },
  created() {
    this.getList();
    this.tipsMsg= tipsMsg
    // this.initSelect()
    this.getexternalsystem();
    this.getcustnos();
    this.getpartner();
    this.getcapital();
    this.getproducts();
 this.initSelectData()
  },
  methods: {
    //获取外部系统平台编码
    getexternalsystem(){
        getDicts(this.externalsystem).then(response =>{
            this.platforms = response.data;
        } );
    },
    //担保公司
     getcustnos(){
        getDicts(this.querydatatype).then(response =>{
            this.custNos = response.data;
        } );
    },
    //获取合作方编码
    getpartner(){
        getDicts(this.partnerscode).then(response =>{
            this.partners = response.data;
        } );
    },
    //获取资金方编码
    getcapital(){
        getDicts(this.capitalcode).then(response =>{
            this.funds = response.data;
        } );
    },
     getproducts(){
        getDicts(this.productcode).then(response =>{
            this.products = response.data;
        } );
    },




    //wzy渲染下拉框
    initSelectData() {
      getSelectSysDictRefList({ unitType: 4, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.partnerNoSelect = response;
      });
    },
     getCustNoList(val) {
      const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
      this.queryParams.platformNo = this.platformNoParam.toString()
      if (val == null || val === '' || flag) {
        this.custNoSelect = null
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.custNo = null
        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.custNoParam = null
        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getPartnerNoList(val) {
      const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
      this.queryParams.custNo = this.custNoParam.toString()
      if (val == null || val === '' || flag) {
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getFundNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())
      this.queryParams.partnerNo = this.partnerNoParam.toString()
      if (val == null || val === ''|| flag) {
        this.fundNoSelect = null
        this.productNoSelect = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
         this.sysDictRefParam.selectDictDatas =""

      if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } 
      else {
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
        this.sysDictRefParam.selectDictDatas =""
         if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getProductNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

      this.queryParams.fundNo = this.fundNoParam.toString()
      if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo
this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo

this.sysDictRefParam.selectDictDatas =""
        if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            // this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
      getProductNoValue(val) {
      const flag = this.lateByte(this.queryParams.productNo) > this.lateByte(val.toString())

      this.queryParams.productNo = this.productNoParam.toString()
      
       if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        this.productNoParam = null
        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue =''
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue = this.queryParams.productNo

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }

    },


    //zdr渲染下拉框
    initSelect() {
      this.bindParamByPlatformNo("default")
      this.getPlatformNoList()

      this.sysDictRefParam.dictType = "INIT_DEFAULT"
      this.sysDictRefParam.dictValue = "INIT_DEFAULT"
      this.sysDictRefParam.pDictType = 'platform_no'
      this.sysDictRefParam.pDictValue = "INIT_DEFAULT"
      getSysDictRefList(this.sysDictRefParam).then(response => {
        this.custNoSelect = response.data
      })

      this.sysDictRefParam.dictType = "INIT_DEFAULT"
      this.sysDictRefParam.dictValue = "INIT_DEFAULT"
      this.sysDictRefParam.pDictType = 'cust_no'
      this.sysDictRefParam.pDictValue = "INIT_DEFAULT"
      getSysDictRefList(this.sysDictRefParam).then(response => {
        this.partnerNoSelect = response.data
      })
      this.sysDictRefParam.dictType = "INIT_DEFAULT"
      this.sysDictRefParam.dictValue = "INIT_DEFAULT"
      this.sysDictRefParam.pDictType = 'partner_no'
      this.sysDictRefParam.pDictValue = "INIT_DEFAULT"
      getSysDictRefList(this.sysDictRefParam).then(response => {
        this.fundNoSelect = response.data
      })
      this.sysDictRefParam.dictType = "INIT_DEFAULT"
      this.sysDictRefParam.dictValue = "INIT_DEFAULT"
      this.sysDictRefParam.pDictType = 'fund_no'
      this.sysDictRefParam.pDictValue = "INIT_DEFAULT"
      getSysDictRefList(this.sysDictRefParam).then(response => {
        this.productNoSelect = response.data
      })
    },
    // getProductNoValue() {
    //   this.queryParams.productNo = this.productNoParam.toString()
    // },
    /** 查询外部系统平台余额分布列表 */
    getList() {
      this.loading = true;
      this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      this.queryParams.fundNo = this.fundNoParam.toString();
      this.queryParams.productNo = this.productNoParam.toString();
      const params=clone(this.queryParams,true);
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
      listDistribution(params).then(response => {
        this.distributionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
      mappingData(){
      this.loading = true
      dataMapping().then(response=>{
        this.handleQuery()
        this.loading = false
      })
    },
    bindParamByPlatformNo(val) {
      this.sysDictRefParam.dictType = 'platform_no'
      this.sysDictRefParam.dictValue = val
      this.sysDictRefParam.pDictType = 'TOP'
      this.sysDictRefParam.pDictValue = "TOP"
    },
    getPlatformNoList() {
      getSysDictRefList(this.sysDictRefParam).then(response => {
        this.platformNoSelect = response.data
      })
    },
    // getCustNoList(val) {
    //   const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
    //   this.queryParams.platformNo = this.platformNoParam.toString()
    //   if (val == null || val === '' || flag) {
    //     this.custNoSelect = null
    //     this.partnerNoSelect = null
    //     this.fundNoSelect = null
    //     this.productNoSelect = null
    //     this.queryParams.custNo = null
    //     this.queryParams.partnerNo = null
    //     this.queryParams.fundNo = null
    //     this.queryParams.productNo = null
    //     this.custNoParam = null
    //     this.partnerNoParam = null
    //     this.fundNoParam = null
    //     this.productNoParam = null
    //     this.sysDictRefParam.dictType = 'cust_no'
    //     this.sysDictRefParam.pDictType = 'platform_no'
    //     this.sysDictRefParam.pDictValue = this.queryParams.platformNo
    //     getSysDictRefList(this.sysDictRefParam).then(response => {
    //       this.custNoSelect = response.data
    //     })
    //   } else {

    //     this.sysDictRefParam.dictType = 'cust_no'
    //     this.sysDictRefParam.pDictType = 'platform_no'
    //     this.sysDictRefParam.pDictValue = this.queryParams.platformNo
    //     getSysDictRefList(this.sysDictRefParam).then(response => {
    //       this.custNoSelect = response.data
    //     })
    //   }
    // },
    // getPartnerNoList(val) {
    //   const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
    //   this.queryParams.custNo = this.custNoParam.toString()
    //   if (val == null || val === '' || flag) {
    //     this.partnerNoSelect = null
    //     this.fundNoSelect = null
    //     this.productNoSelect = null
    //     this.queryParams.partnerNo = null
    //     this.queryParams.fundNo = null
    //     this.queryParams.productNo = null
    //     this.partnerNoParam = null
    //     this.fundNoParam = null
    //     this.productNoParam = null
    //     this.sysDictRefParam.dictType = 'partner_no'
    //     // this.sysDictRefParam.dictValue=val
    //     this.sysDictRefParam.pDictType = 'cust_no'
    //     this.sysDictRefParam.pDictValue = this.queryParams.custNo
    //     getSysDictRefList(this.sysDictRefParam).then(response => {
    //       this.partnerNoSelect = response.data
    //     })
    //   } else {

    //     this.sysDictRefParam.dictType = 'partner_no'
    //     // this.sysDictRefParam.dictValue=val
    //     this.sysDictRefParam.pDictType = 'cust_no'
    //     this.sysDictRefParam.pDictValue = this.queryParams.custNo
    //     getSysDictRefList(this.sysDictRefParam).then(response => {
    //       this.partnerNoSelect = response.data
    //     })
    //   }
    // },
    // getFundNoList(val) {
    //   const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

    //   this.queryParams.partnerNo = this.partnerNoParam.toString()
    //   if (val == null || val === ''||flag) {
    //     this.fundNoSelect = null
    //     this.productNoSelect = null
    //     this.queryParams.fundNo = null
    //     this.queryParams.productNo = null
    //     this.fundNoParam = null
    //     this.productNoParam = null
    //     this.sysDictRefParam.dictType = 'fund_no'
    //     this.sysDictRefParam.pDictType = 'partner_no'
    //     this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
    //     getSysDictRefList(this.sysDictRefParam).then(response => {
    //       this.fundNoSelect = response.data
    //     })
    //   } else {
    //     this.sysDictRefParam.dictType = 'fund_no'
    //     this.sysDictRefParam.pDictType = 'partner_no'
    //     this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
    //     getSysDictRefList(this.sysDictRefParam).then(response => {
    //       this.fundNoSelect = response.data
    //     })
    //   }
    // },
    // getProductNoList(val) {
    //   const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

    //   this.queryParams.fundNo = this.fundNoParam.toString()
    //   if (val == null || val === '' ||flag) {
    //     this.productNoSelect = null
    //     this.queryParams.productNo = null
    //     this.productNoParam = null
    //     this.sysDictRefParam.dictType = 'product_no'
    //     this.sysDictRefParam.pDictType = 'fund_no'
    //     this.sysDictRefParam.pDictValue = this.queryParams.fundNo
    //     getSysDictRefList(this.sysDictRefParam).then(response => {
    //       this.productNoSelect = response.data
    //     })
    //   } else {
    //     this.sysDictRefParam.dictType = 'product_no'
    //     this.sysDictRefParam.pDictType = 'fund_no'
    //     this.sysDictRefParam.pDictValue = this.queryParams.fundNo
    //     getSysDictRefList(this.sysDictRefParam).then(response => {
    //       this.productNoSelect = response.data
    //     })
    //   }
    // },
    lateByte(sTargetStr) {
      var sTmpStr, sTmpChar;
      var nOriginLen = 0;
      var nStrLength = 0;

      sTmpStr = new String(sTargetStr);
      nOriginLen = sTmpStr.length;

      for (var i = 0; i < nOriginLen; i++) {
        sTmpChar = sTmpStr.charAt(i);

        if (escape(sTmpChar).length > 4) {
          nStrLength += 2;
        } else if (sTmpChar != '/r') {
          nStrLength++;
        }
      }
      return nStrLength;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        productNo: null,
        loanMonth: null,
        reconMonth: null,
        loanBalanceAmount: null,
        loanRemainNumber: null,
        balanceDistributionType: null,
        mNumber: null,
        mBalanceAmount: null,
        isMapping: null,
        remark: null,
        createTime: null,
        updateTime: null
      };
      
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.platformNoParam = ''
      this.custNoParam = ''
      this.partnerNoParam = ''
      this.fundNoParam = ''
      this.productNoParam = ''
      this.queryParams.moreSearch=undefined;
      this.handleQuery();
      // this.initSelect()
      this.initSelectData();

    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加外部系统平台余额分布";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDistribution(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改外部系统平台余额分布";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDistribution(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDistribution(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除外部系统平台余额分布编号为"' + ids + '"的数据项？').then(function () {
        return delDistribution(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const params=clone(this.queryParams,true);
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
      this.download('data/distribution/export', {
        ...params
      }, `余额分布_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
