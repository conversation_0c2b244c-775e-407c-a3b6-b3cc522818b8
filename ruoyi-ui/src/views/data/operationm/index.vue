<template>
  <div class="app-container">

    <div style="color:red;margin-bottom:15px;font-size:12px;">数据统计口径：资方口径，即借据逾期后代偿成功才能计为已结清</div>

    <div v-hasPermi="['data:external:list3']">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="130px">
      <!--   Start 前端页面变成多选页面    -->
      <el-form-item label="外部系统名称" prop="platformNo">
        <el-select v-model="platformNoParam" placeholder="请选择系统名称" filterable multiple size="small"
                   >
          <el-option
            v-for="dict in platformNoSelect"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="担保公司" prop="custNo">
        <el-select v-model="custNoParam" placeholder="请选择担保公司"  filterable multiple size="small"
                   >
          <el-option
            v-for="dict in custNoSelect"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合作方" prop="partnerNo">
        <el-select v-model="partnerNoParam" placeholder="请选择合作方" filterable multiple size="small"
                   >
          <el-option
            v-for="dict in partnerNoSelect"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资金方" prop="fundNo">
        <el-select v-model="fundNoParam" placeholder="请选择资金方" filterable multiple size="small"
                   >
          <el-option
            v-for="dict in fundNoSelect"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!--   end 前端页面变成多选页面    -->
      <el-form-item label="数据统计时间范围">
        <el-date-picker
          v-model="queryParams.dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM"
          type="monthrange"
          range-separator="-"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <MoreSearch modelCode="DATAREPORT" :params="queryParams" v-show="showMoreSearch" byId="companyCode"></MoreSearch>
      <el-form-item>
        <el-button  type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
          >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
        >
      </el-form-item>
    </el-form>
    </div>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['data:external:export3']"
        >导出
        </el-button>
      </el-col>
    </el-row>

    <div v-hasPermi="['data:external:list3']">
    <el-table border v-loading="loading" :data="dataList">

      <el-table-column label="日期" align="center" prop="reconDate" width="180" fixed="left"/>
      <el-table-column label="贷款余额" align="center" prop="totalBalanceAmount" />
      <el-table-column label="贷款笔数" align="center" prop="totalCount" />
      <el-table-column label="新增贷款本金" align="center" prop="addAmount" />
      <el-table-column label="新增贷款笔数" align="center" prop="addCount" />
      <el-table-column label="累计贷款金额" align="center" prop="totalAmount" />
      <el-table-column label="累计解保笔数" align="center" prop="solutionStroke" />
      <el-table-column label="新增还款金额" align="center" prop="addRepayPrintAmount" />
      <el-table-column label="新增还款笔数" align="center" prop="addRepayCount" />
      <el-table-column label="累计还款本金" align="center" prop="totalRepayPrintAmount" />
      <el-table-column label="累计还款笔数" align="center" prop="totalRepayCount" />


    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    </div>


  </div>
</template>

<script>
  import {listData3} from '@/api/system/data';
  //  start 引用联级字典查询
  import {getSelectSysDictRefList} from '@/api/ref/ref'
  //  end 引用联级字典查询
  import { clone } from "xe-utils";
  export default {
    name: 'Operationm',
    data() {
      return {
        /** 限制日期选择框，不允许选择超过当前日期 */
        pickerOptions: {
          disabledDate(time) {
            var date=new Date();
            date.setDate(1);
            return time.getTime() > date - 1000 * 60 * 60 * 24 * 30;
          }
        },
          initSelect: '',
        // 日期范围
        //  start 新增参数
        platformNoParam: '',
        custNoParam: '',
        partnerNoParam: '',
        fundNoParam: '',
        productNoParam: '',


        sysDictRefParam: {
          dictType: '',
          dictValue: '',
          pDictType: '',
          pDictValue: '',
          selectDictDatas:''
        },
        platformNoSelect: [],
        custNoSelect: [],
        fundNoSelect: [],
        partnerNoSelect: [],
        productNoSelect: [],
        //  end 新增参数
        // 遮罩层
        loading: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 外部系统平台运营情况数据表格数据
        dataList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          dateRange: [],
          pageNum: 1,
          pageSize: 10,
          platformNo: null,
          custNo: null,
          partnerNo: null,
          fundNo: null,
          params:{
           moduleTypeOfNewAuth: 'DATAREPORT'
        }
        },
        showMoreSearch:false
      }
    },
    created() {
      this.getList()
      //  start 页面刷新时对数据的处理

      // this.initSelect()
      this.initSelectData()
      //  end 页面刷新时对数据的处理

    },
    methods: {
      //wzy渲染下拉框
      initSelectData() {
      getSelectSysDictRefList({ unitType: 4, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.partnerNoSelect = response;
      });
    },
      getCustNoList(val) {
        const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
        this.queryParams.platformNo = this.platformNoParam.toString()
        if (val == null || val === '' || flag) {
          this.custNoSelect = null
          this.partnerNoSelect = null
          this.fundNoSelect = null
          this.productNoSelect = null

          // this.queryParams.custNo = null
          // this.queryParams.partnerNo = null
          // this.queryParams.fundNo = null
          // this.queryParams.productNo = null

          // this.custNoParam = null
          // this.partnerNoParam = null
          // this.fundNoParam = null
          // this.productNoParam = null
          this.sysDictRefParam.dictType = 'cust_no'
          this.sysDictRefParam.pDictType = 'platform_no'
          this.sysDictRefParam.pDictValue = this.queryParams.platformNo
          this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
            this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
          }
          if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
          }
          if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
          }
          if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
          }
          getSelectSysDictRefList(this.sysDictRefParam).then(response => {

            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
          })
        } else {

          this.sysDictRefParam.dictType = 'cust_no'
          this.sysDictRefParam.pDictType = 'platform_no'
          this.sysDictRefParam.pDictValue = this.queryParams.platformNo
          this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
            this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
          }
          if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
          }
          if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
          }
          if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
          }
          getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
          })
        }
      },
      getPartnerNoList(val) {
        const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
        this.queryParams.custNo = this.custNoParam.toString()
        if (val == null || val === '' || flag) {
          this.partnerNoSelect = null
          this.fundNoSelect = null
          this.productNoSelect = null

          // this.queryParams.partnerNo = null
          // this.queryParams.fundNo = null
          // this.queryParams.productNo = null

          // this.partnerNoParam = null
          // this.fundNoParam = null
          // this.productNoParam = null
          this.sysDictRefParam.dictType = 'partner_no'
          // this.sysDictRefParam.dictValue=val
          this.sysDictRefParam.pDictType = 'cust_no'
          this.sysDictRefParam.pDictValue = this.queryParams.custNo
          this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
            this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
          }
          if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
          }
          if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
          }
          if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
          }
          getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
          })
        } else {
          this.sysDictRefParam.dictType = 'partner_no'
          // this.sysDictRefParam.dictValue=val
          this.sysDictRefParam.pDictType = 'cust_no'
          this.sysDictRefParam.pDictValue = this.queryParams.custNo
          this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
            this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
          }
          if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
          }
          if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
          }
          if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
          }
          getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
          })
        }
      },
      getFundNoList(val) {
        const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())
        this.queryParams.partnerNo = this.partnerNoParam.toString()
        if (val == null || val === ''|| flag) {
          this.fundNoSelect = null
          this.productNoSelect = null
          // this.queryParams.fundNo = null
          // this.queryParams.productNo = null
          // this.fundNoParam = null
          // this.productNoParam = null
          this.sysDictRefParam.dictType = 'fund_no'
          this.sysDictRefParam.pDictType = 'partner_no'
          this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
          this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
            this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
          }
          if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
          }
          if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
          }
          if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
          }
          getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
          })
        }
        else {
          this.sysDictRefParam.dictType = 'fund_no'
          this.sysDictRefParam.pDictType = 'partner_no'
          this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
          this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
            this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
          }
          if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
          }
          if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
          }
          if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
          }

          getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
          })
        }
      },
      getProductNoList(val) {
        const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

        this.queryParams.fundNo = this.fundNoParam.toString()
        if (val == null || val === '' ||flag) {
          this.productNoSelect = null
          // this.queryParams.productNo = null
          // this.productNoParam = null
          this.sysDictRefParam.dictType = 'product_no'
          this.sysDictRefParam.pDictType = 'fund_no'
          this.sysDictRefParam.pDictValue = this.queryParams.fundNo
          this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
            this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
          }
          if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
          }
          if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
          }
          if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
          }
          getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
          })
        } else {
          this.sysDictRefParam.dictType = 'product_no'
          this.sysDictRefParam.pDictType = 'fund_no'
          this.sysDictRefParam.pDictValue = this.queryParams.fundNo

          this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
            this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
          }
          if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
          }
          if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
          }
          if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
            this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
          }

          getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
          })
        }
      },

      /** 查询外部系统平台运营情况数据列表 */
      getList() {
        this.loading = true
        this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      this.queryParams.fundNo = this.fundNoParam.toString();
      this.queryParams.productNo = this.productNoParam.toString();
      const params=clone(this.queryParams,true);
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
        listData3(params).then(response => {
          this.dataList = response.rows
          this.total = response.total
          this.loading = false
        })
      },


      lateByte(sTargetStr) {
        var sTmpStr, sTmpChar;
        var nOriginLen = 0;
        var nStrLength = 0;

        sTmpStr = new String(sTargetStr);
        nOriginLen = sTmpStr.length;

        for (var i = 0; i < nOriginLen; i++) {
          sTmpChar = sTmpStr.charAt(i);

          if (escape(sTmpChar).length > 4) {
            nStrLength += 2;
          } else if (sTmpChar != '/r') {
            nStrLength++;
          }
        }
        return nStrLength;
      },

      //  end 新增方法

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        //  start 重置逻辑更新

        this.platformNoParam = '';
        this.custNoParam = '';
        this.partnerNoParam = '';
        this.fundNoParam = '';
        this.productNoParam = '';
        this.dateRange = [];

        /** 重置查询参数 start */
        this.queryParams.dateRange = [];
        this.queryParams.platformNo = '';
        this.queryParams.custNo = '';
        this.queryParams.partnerNo = '';
        this.queryParams.fundNo = '';
        /** 重置查询参数 end */
        this.queryParams.moreSearch=undefined;
        this.handleQuery()
        this.initSelectData();
        //  end 重置逻辑更新

      },

      /** 导出按钮操作 */
      handleExport() {
        const params=clone(this.queryParams,true);
        params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
        this.download('system/aData/export3', {
          ...params
        }, `运营情况月报表_${new Date().getTime()}.xlsx`)
      }
    }
  }
</script>


