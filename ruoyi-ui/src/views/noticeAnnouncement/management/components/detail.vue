<template>
  <div class="p-5 w-2/3 m-auto">
    <MyForm
      v-model="form"
      :columns="formColumnsDetail"
      formType="false"
      labelWidth="120px"
      ref="form"
      :rules="rules"
      ><template #publisher>
        <el-form-item
          class="selectList"
          label="发布人"
          prop="publisher"
          style="margin-bottom: 20px"
        >
          <i
            @click="openPublisherList"
            class="el-icon-search absolute z-10 cursor-pointer"
            style="right: 10px; top: 10px"
          ></i>
          <div
            v-show="
              !user.multipleSelectionUserDep ||
              !user.multipleSelectionUserDep.length
            "
            @click="openPublisherList"
            style="left: 0px; top: 0px; width: 240px; height: 36px"
            class="absolute z-10 cursor-pointer"
          ></div>
          <el-select
            v-model="form.publisher"
            multiple
            placeholder="请选择"
            style="width: 240px"
          >
            <el-option
              v-for="(item, index) in user.multipleSelectionUserDep"
              :key="index"
              :label="item.nickName"
              :value="item.userId"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </template>
      <template #content>
        <Editor ref="myEditor" minHeight="500" :value="form.content" :uploadUrl="uploadUrl"></Editor>
      </template>
    </MyForm>
    <InBody>
      <div
        class="text-center fixed bottom-0 bg-white z-10 pb-2"
        style="width: calc(100% - 260px); left: 260px"
      >
        <el-button
          @click="onSave"
          v-show="title.indexOf('查看') == -1"
          type="primary"
          >保存</el-button
        >

        <el-button @click="cancel">取消</el-button>
      </div>
    </InBody>
    <UserDepPostSelect
      title="user"
      rowKey="userId"
      :multipleSelectionProp="user.multipleSelectionUserDep"
      :multiple="user.multiple"
      v-model="user.open"
      @on-submit-success-user="userSuccess"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import config from "./config";
import {
  getHomePostCompany,
  addNoticeMainNotice,
  updateNoticeMainNotice,
  getNoticeMainNotice,
  getNoticeAuthCompany,
  getDataManageList
} from "@/api/notice/management";
import { systemDataManage } from "@/api/notice/dataSet";
// import { companyList } from "@/api/businessInformation/companyInformation";
import { renameField } from "@/utils";

export default {
  name: "noticeManagementDetail",

  data() {
    return {
      ...config,
      form: {
        noticeName: "",
        type: "",
        publisher: "",
        publishCompany: "",
        fileIds: "",
        content: "",
      },
      user: {
        multipleSelectionUserDep: [],
        open: false,
        multiple: false,
      },
      title: undefined,
      uploadUrl:process.env.VUE_APP_BASE_API + '/system/file/uploadNoticeFile'
    };
  },

  async created() {
    this.getTypes();
    await this.getCompanyAll();
    this.getInitForm();
  },
  methods: {
    async getTypes() {
      // let { rows } = await systemDataManage({ dataCode: null });
      // rows= rows.filter(item=>{
      //   return item.dataName&&item.dataName.indexOf('公告')!=-1;
      // })
      let { rows } = await getDataManageList({ firstDataCode: 'TZGGLB' });
      rows.forEach((rootNode) =>
        renameField(rootNode, "fPiattaformas", "children")
      );
      rows.forEach((rootNode) => renameField(rootNode, "dataName", "label"));
      this.formColumnsDetail[1].options = rows[0]?.children;
    },
    async getCompanyAll() {
      // const companyTypeCode=this.$store.state.data.OPTION_MAP.company_type.filter(item=>item.label=='担保公司')[0].code;
      const data  = await getNoticeAuthCompany({userId:sessionStorage.getItem('userId')});
      this.formColumnsDetail[3].options = data;
    },
    async getInitForm() {
      this.title = this.$route.query.title;
      if (this.$route.params.id == "add") {
        const userId = sessionStorage.getItem("userId");
        this.user.multipleSelectionUserDep = [
          {
            nickName: sessionStorage.getItem("userNickName"),
            userId,
          },
        ];
        this.form.publisher = [userId];
        const { data } = await getHomePostCompany({ userId });
        this.form.publishCompany =this.formColumnsDetail[3].options.length? Number(data.id):undefined;
      } else {
        const { data } = await getNoticeMainNotice(this.$route.params.id);
        this.form=data;
        this.user.multipleSelectionUserDep = [
          {
            nickName: data.publisherNickName,
            userId:data.publisher,
          },
        ];
        this.form.publisher = [data.publisher];
        this.form.files=this.form.noticesFileList;
      }
    },
    openPublisherList() {
      if (this.title == "通知公告详情") return;
      this.user.open = true;
    },
    async onSave() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const params = XEUtils.clone(this.form, true);
          params.publisher =Number(params.publisher[0]);
          params.noticesFileList = params.fileListAll.map((item) => {
            return {
              relationType: 1,
              fileName: item.name,
              filePath: item.response?.data.filePath||item.downLoadUrl,
              id:item.response?.data.id||item.id
            };
          });
          params.content = this.$refs.myEditor.currentValue;
          if (this.title == "新增通知公告") {
            await addNoticeMainNotice(params);
          } else {
            await updateNoticeMainNotice(params);
          }
          this.$modal.msgSuccess("操作成功");
          this.$router.go(-1);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    cancel() {
      this.$router.go(-1);
    },
    userSuccess(value) {
      this.user.multipleSelectionUserDep = XEUtils.clone(value, true);
      this.form.publisher = value.map((item) => item.userId);
    },
  },
};
</script>
<style lang="scss" scoped>
.selectList {
  ::v-deep .el-select__caret {
    display: none;
  }
}
</style>
