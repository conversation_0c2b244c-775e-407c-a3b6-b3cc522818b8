<template>
  <div class="app-container">
    <div style="width: 100%">
      <div style="margin-top: 4px">
        <span class="projectnamespan">{{ this.projectName }}</span
        ><br />
        <div style="margin-top: 8px; display: inline">
          <span class="projectnamespan1">法催业务</span>
        </div>
        <div style="width: 50%; margin-left: 59%; display: inline">
          <el-button
            v-if="
              hasRoleList.includes('9') ||
              hasRoleList.includes('2') ||
              hasRoleList.includes('1')
            "
            size="small"
            type="primary"
            @click="addprojectDate()"
            >新增业务期次</el-button
          >
          <el-button
            v-show="(hasRoleList.includes('9') || hasRoleList.includes('1')) && checkPermi(['sensitive:detailLawEditCompany'])"
            size="small"
            type="whitebg"
            @click="changeCompany()"
            >替换信息费公司</el-button
          >
          <el-button
            v-show="
              (hasRoleList.includes('9') ||
              hasRoleList.includes('2') ||
              hasRoleList.includes('1')) && checkPermi(['sensitive:detailLawEditProject'])
            "
            size="small"
            type="whitebg"
            @click="updateprojectData()"
            >修改项目信息</el-button
          >
          <el-button
            v-if="
              hasRoleList.includes('9') ||
              hasRoleList.includes('2') ||
              hasRoleList.includes('1')
            "
            size="small"
            type="whitebg"
            @click="abortproject()"
            >终止项目</el-button
          >
          <el-button
            v-show="this.isexportRole && checkPermi(['sensitive:export'])"
            size="small"
            type="warning"
            @click="handleExport()"
            plain
            icon="el-icon-download"
            >导出信息费明细</el-button
          >
        </div>
      </div>
    </div>
    <div
      style="
        width: 100%;
        height: 8px;
        background-color: #fbfbfb;
        margin-bottom: 24px;
        margin-top: 16px;
      "
    ></div>
    <div class="tabs_price">
      <div>
        <span>收入合计</span>
        <p>{{ sumData.incomeAmt }}</p>
      </div>
      <div v-hasPermi="['sensitive:detailLawStatistics']">
        <span>信息费合计</span>
        <p>{{ sumData.feeAmt }}</p>
      </div>
      <div v-hasPermi="['sensitive:detailLawStatistics']">
        <span>信息费已支付</span>
        <p>{{ sumData.feeAlreadyPay }}</p>
      </div>
      <div v-hasPermi="['sensitive:detailLawStatistics']">
        <span>信息费未结清</span>
        <p>{{ sumData.feeNoAlreadyPay }}</p>
      </div>
    </div>
    <div
      style="
        width: 100%;
        height: 8px;
        background-color: #fbfbfb;
        margin-bottom: 24px;
        margin-top: 16px;
      "
    ></div>
    <!--  <div style=" display: flex;width: 100%;height:580px; flex-direction: column;-->
    <!--    justify-content: flex-start;-->
    <!--    align-items: center;margin-bottom: 400px" v-show = "jindutiaoshow">-->
    <div style="width: 100%" v-show="jindutiaoshow">
      <div style="margin-left: 16px; margin-bottom: 10px">
        <span style="font-size: 14px; color: #666666">未完结期次：</span
        ><span style="font-size: 14px; color: #666666">{{
          this.phaseNoFinish
        }}</span>
      </div>
      <div style="width: 100%">
        <span
          style="
            font-size: 14px;
            margin-left: 16px;
            font-weight: bold;
            color: #666666;
          "
          >业务期次：
          <el-select
            v-model="yewuqici"
            placeholder="请选择业务期次"
            filterable
            clearable
            size="small"
          >
            <el-option
              v-for="dict in yewuqiciList"
              :key="dict.id"
              :label="dict.term_month"
              :value="dict.id"
              @click.native="updateqici(dict)"
            /> </el-select
        ></span>
        <el-button
          v-if="
            hasRoleList.includes('9') ||
            hasRoleList.includes('2') ||
            hasRoleList.includes('1')
          "
          size="small"
          type="whitebg"
          @click="updateprojectqc()"
          style="margin-left: 8px; font-size: 14px"
          v-show="jindutiaozhansh"
          >修改期次时间</el-button
        >
        <el-button
          v-if="
            hasRoleList.includes('9') ||
            hasRoleList.includes('2') ||
            hasRoleList.includes('1')
          "
          size="small"
          type="whitebg"
          @click="deleteincome()"
          style="margin-left: 8px; font-size: 14px"
          v-show="jindutiaozhansh"
          >删除期次</el-button
        >
      </div>
      <div v-if="jindutiaozhansh === false" style="margin-top: 24px"></div>
      <el-divider v-if="jindutiaozhansh === true"></el-divider>

      <el-dialog
        :title="title"
        :visible.sync="deleteincomedeilog"
        width="30%"
        append-to-body
      >
        <span class="deleteprojectSpan">删除业务期次</span>
        <div style="width: 100%; height: 30px"></div>
        <div style="width: 100%; height: 60px">
          <span style="font-size: 20px; margin-left: 35px"
            >是否确认删除本期次？</span
          ><br />
          <span style="font-size: 20px; margin-left: 35px"
            >删除后期次将从期次列表删除，无法找回</span
          ><br />
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="large" type="primary" @click="submitdeleteincome()"
            >确 定</el-button
          >
          <el-button size="large" @click="cencaldeleteincome()"
            >取 消</el-button
          >
        </div>
      </el-dialog>

      <div style="width: 90%" v-show="jindutiaozhansh">
        <el-steps :active="this.jindutiao" align-center>
          <el-step title="录入收入与信息费">
            <template slot="description">
              <span v-if="this.jindutiao >= 1">{{ this.lrsrdaibanren }}</span
              ><br />
              <span v-if="this.jindutiao >= 1">{{ this.lrsrdate }}</span>
              <span v-show="this.jindutiao == 0"
                >待办人:
                <span v-for="item in this.dbchuna">
                  {{ item }}
                </span>
              </span>
            </template>
          </el-step>
          <el-step title="确认金额">
            <template slot="description">
              <span v-if="this.jindutiao >= 2">{{ this.qrffdaibanren }}</span
              ><br />
              <span v-if="this.jindutiao >= 2">{{ this.qrffdate }}</span>

              <span v-show="this.jindutiao == 1"
                >待办人:

                <span v-for="item in this.dbkuaiji">
                  {{ item }}
                </span>
              </span>
            </template>
          </el-step>
          <!--            <el-step title="录入信息费">-->
          <!--              <template slot="description">-->
          <!--               <span v-if="this.jindutiao>=3">{{this.lrffdaibanren}}</span><br>-->
          <!--                <span v-if="this.jindutiao>=3">{{this.lrffdate}}</span>-->
          <!--                 <span v-show="this.jindutiao==2" >待办人:-->
          <!--                        <span v-for="item in this.dbkuaiji">-->
          <!--                  {{item}}-->
          <!--                </span>-->
          <!--                 </span>-->

          <!--              </template>-->
          <!--            </el-step>-->
          <!--            <el-step title="确认信息费">-->
          <!--              <template slot="description">-->
          <!--                <span v-if="this.jindutiao>=4">{{this.qrffdaibanren}}</span><br>-->
          <!--                <span v-if="this.jindutiao>=4">{{this.qrffdate}}</span>-->
          <!--                 <span v-show="this.jindutiao==3" >待办人:-->
          <!--                    <span v-for="item in this.dbyewu">-->
          <!--                    {{item}}-->
          <!--                  </span>-->
          <!--                </span>-->

          <!--              </template>-->
          <!--            </el-step>-->
          <el-step title="完成"> </el-step>
        </el-steps>
      </div>
      <div
        style="margin-top: 32px; margin-bottom: 8px; color: #666666"
        v-if="jindutiaozhansh === true"
      >
        <span style="font-size: 14px; margin-left: 6px; color: #666666"
          >出信息费公司：{{ this.feeCustNameProcess }}</span
        >
        <span style="font-size: 14px; margin-left: 10%; color: #666666"
          >信息费公司：{{ this.custNameProcess }}</span
        >
      </div>
      <!--        <div style="width: 100%;margin-bottom: 15px" v-show = jindutiaozhansh>-->
      <el-table
        v-show="jindutiaozhansh"
        :data="overDetailBodyDetailList1"
        :summary-method="getSummaries02"
        show-summary
        border
        style="line-height: 40px"
        :header-cell-style="{
          color: '#333333',
          fontSize: '14px',
          fontWeight: 'bold',
        }"
        :row-style="{ height: '60px' }"
      >
        <el-table-column label="服务商" prop="serviceProvider" min-width="11%">
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.serviceProviderFlag === '2' &&
                scope.row.serviceProvider.substring(0, 5) === '     '
              "
              style="margin-left: 30px"
              >{{ scope.row.serviceProvider }}</span
            >
            <span
              v-if="
                scope.row.serviceProviderFlag === '1' &&
                scope.row.serviceProvider.substring(0, 5) !== '     '
              "
              >{{ scope.row.serviceProvider }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="法催收入" prop="incomeAmt" min-width="11%">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.incomeAmt) }}</span>
          </template>
          <!--                <template slot-scope="scope">-->
          <!--                    <span v-for="(title, index) in overDetailBodyDetailList1[scope.$index].feeList" :key="index">-->
          <!--                    {{scope.row.feeList[index].incomeAmt}}<br/>-->
          <!--                    </span>-->
          <!--                </template>-->
        </el-table-column>
        <!--              <el-table-column label="本期信息费" prop="currentFee" min-width="11%"/>-->
        <!--              <el-table-column label="二级服务商" prop="serviceProviderSecond" min-width="11%"  >-->
        <!--                <template slot-scope="scope">-->
        <!--                  <span v-for="(title, index) in overDetailBodyDetailList1[scope.$index].feeList" :key="index">-->
        <!--                    {{scope.row.feeList[index].serviceProviderSecond}}<br/>-->
        <!--                    </span>-->
        <!--                </template>-->
        <!--              </el-table-column>-->
        <!--              <el-table-column label="二级服务商收入" prop="serviceProviderSecondIncome" min-width="11%"  >-->
        <!--                <template slot-scope="scope">-->
        <!--                  <span v-for="(title, index) in overDetailBodyDetailList1[scope.$index].feeList" :key="index">-->
        <!--                    {{scope.row.feeList[index].serviceProviderSecondIncome}}<br/>-->
        <!--                    </span>-->
        <!--                </template>-->
        <!--              </el-table-column>-->
        <!--              <el-table-column label="出信息费公司" prop="custName" min-width="10%"  >-->
        <!--                <template slot-scope="scope">-->
        <!--                  <span v-for="(title, index) in overDetailBodyDetailList1[scope.$index].feeList" :key="index">-->
        <!--                    {{scope.row.feeList[index].custName}}<br/>-->
        <!--                    </span>-->
        <!--                </template>-->
        <!--              </el-table-column>-->
        <!--              <el-table-column label="信息费公司" prop="feeCustName" min-width="10%"  >-->
        <!--                <template slot-scope="scope">-->
        <!--                  <span v-for="(title, index) in overDetailBodyDetailList1[scope.$index].feeList" :key="index">-->
        <!--                    {{scope.row.feeList[index].feeCustName}}<br/>-->
        <!--                    </span>-->
        <!--                </template>-->
        <!--              </el-table-column>-->
        <el-table-column v-if="checkPermi(['sensitive:detailLawWwj'])"  label="本期信息费" prop="currentFee" min-width="10%">
          <template slot-scope="scope">
            <span v-if="scope.row.currentFee < 0" style="color: red">{{
              formaterMoney(scope.row.currentFee)
            }}</span>
            <span v-if="scope.row.currentFee >= 0">{{
              formaterMoney(scope.row.currentFee)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="checkPermi(['sensitive:detailLawWwj'])" label="挂起金额" prop="currentFee" min-width="10%">
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.serviceProviderShowFlag !== '99' &&
                scope.row.currentFee < 0
              "
              >+{{ formaterMoney(Number(scope.row.currentFee) * -1) }}</span
            >
            <span
              v-if="
                scope.row.serviceProviderShowFlag !== '99' &&
                scope.row.currentFee > 0
              "
              >0.00</span
            >
          </template>
        </el-table-column>
        <el-table-column v-if="checkPermi(['sensitive:detailLawWwj'])" label="信息费" prop="feeAmt" min-width="10%">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.feeAmt) }}</span>
          </template>
          <!--                <template slot-scope="scope">-->
          <!--                  <span v-for="(title, index) in overDetailBodyDetailList1[scope.$index].feeList" :key="index">-->
          <!--                    {{scope.row.feeList[index].feeAmt}}<br/>-->
          <!--                    </span>-->
          <!--                </template>-->
        </el-table-column>
        <el-table-column v-if="checkPermi(['sensitive:detailLawWwj'])" label="信息费取整" prop="feeRound" min-width="10%">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.feeRound) }}</span>
          </template>
          <!--                <template slot-scope="scope">-->
          <!--                  <span v-for="(title, index) in overDetailBodyDetailList1[scope.$index].feeList" :key="index">-->
          <!--                    {{scope.row.feeList[index].feeRound}}<br/>-->
          <!--                    </span>-->
          <!--                </template>-->
        </el-table-column>
        <el-table-column v-if="checkPermi(['sensitive:detailLawWwj'])" label="借条分润" prop="jtfrAmt" min-width="10%">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.jtfrAmt) }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="checkPermi(['sensitive:detailLawWwj'])" label="法催利润" prop="lawProfit" min-width="10%">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.lawProfit) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <div style="width: 100%" v-show="jindutiaozhansh">
        <div
          v-if="hasRoleList.includes('9') || hasRoleList.includes('2')"
          v-show="this.jindutiao == 0"
          style="text-align: center; margin-bottom: 5px; margin-top: 24px"
        >
          <el-button
            v-if="hasRoleList.includes('9') || hasRoleList.includes('2')"
            v-show="this.jindutiao == 0"
            size="large"
            type="primary"
            @click="lurusr()"
            >录入收入与信息费</el-button
          ><br />
          <span
            v-if="hasRoleList.includes('9') || hasRoleList.includes('2')"
            v-show="this.jindutiao == 0"
            class="div3spankj"
            >请出纳人员录入收入与信息费信息</span
          >
        </div>

        <div
          v-if="
            hasRoleList.includes('9') ||
            hasRoleList.includes('2') ||
            hasRoleList.includes('1') ||
            hasRoleList.includes('10')
          "
          v-show="this.jindutiao == 1"
          style="text-align: center; margin-bottom: 5px; margin-top: 24px"
        >
          <el-button
            v-if="
              (hasRoleList.includes('9') || hasRoleList.includes('2')) &&
              jindutiao == 1
            "
            size="large"
            type="primary"
            @click="updateluru()"
            >修改金额</el-button
          >
          <el-button
            v-if="
              (hasRoleList.includes('9') || hasRoleList.includes('2')) &&
              jindutiao == 1
            "
            size="large"
            type="primary"
            @click="querenff()"
            >确认金额</el-button
          ><br />
          <span
            v-if="
              (hasRoleList.includes('9') || hasRoleList.includes('2')) &&
              jindutiao == 1
            "
            class="div3spankj"
            >请会计人员确认收入与信息费金额正确<br
          /></span>
        </div>
      </div>
    </div>
    <div v-show="dengjidakuantable" style="width: 100%">
      <span class="djdktablespan">登记打款信息</span>
      <br />
      <br />
      <div style="margin-bottom: 6px">
        <span style="font-size: 14px"
          >出信息费公司：{{ this.feeCustNameProcess }}</span
        >
        <span style="font-size: 14px; margin-left: 10%"
          >信息费公司：{{ this.custNameProcess }}</span
        >
      </div>
      <el-table v-loading="loading" :data="djdkList" border>
        <el-table-column label="服务商" prop="serviceProvider">
          <template slot-scope="scope">
            <span v-if="scope.row.serviceProviderFlag === '1'">{{
              scope.row.serviceProvider
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="二级服务商" prop="serviceProviderSecond" />
        <!--          <el-table-column label="出信息费公司"   prop="custName"   />-->
        <!--          <el-table-column label="信息费公司"  prop="feeCustName"   />-->
        <!--          <el-table-column label="信息费金额"    >-->
        <!--            <template slot-scope="scope">-->
        <!--              <span>{{scope.row.feeAmt}}</span>-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <el-table-column label="信息费取整（应打款）">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.feeRound) }}</span>
          </template>
          <!--            <template slot-scope="scope">-->
          <!--              <span>{{scope.row.feeRound}}</span>-->
          <!--            </template>-->
        </el-table-column>

        <el-table-column label="打款状态" prop="payStatus">
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.payStatus === '未开始' ||
                scope.row.payStatus === '部分打款'
              "
              style="color: #f19822"
            >
              <span class="el-icon-warning-outline"></span>
            </span>
            <span>{{ scope.row.payStatus }}</span>
          </template>
        </el-table-column>

        <el-table-column label="已完成打款合计" prop="payAlreadySum">
          <template slot-scope="scope">
            <span
              v-if="scope.row.hasOwnProperty('payAlreadySum') === false"
            ></span>
            <span
              v-if="
                scope.row.hasOwnProperty('payAlreadySum') === true &&
                scope.row.payAlreadySum !== null &&
                scope.row.payAlreadySum !== ''
              "
              >{{ formaterMoney(scope.row.payAlreadySum) }}</span
            >
          </template>
          <!--            <template slot-scope="scope">-->
          <!--              <span>{{scope.row.payAlreadySum}}</span>-->
          <!--            </template>-->
        </el-table-column>
        <el-table-column label="未完成打款合计" prop="payNoAlreadySum">
          <template slot-scope="scope">
            <span
              v-if="scope.row.hasOwnProperty('payNoAlreadySum') === false"
            ></span>
            <span
              v-if="
                scope.row.hasOwnProperty('payNoAlreadySum') === true &&
                scope.row.payNoAlreadySum !== null &&
                scope.row.payNoAlreadySum !== ''
              "
              >{{ formaterMoney(scope.row.payNoAlreadySum) }}</span
            >
          </template>
          <!--          <template slot-scope="scope">-->
          <!--            <span>{{scope.row.payNoAlreadySum}}</span>-->
          <!--          </template>-->
        </el-table-column>

        <el-table-column label="打款日期">
          <template slot-scope="scope">
            <span
              v-for="(title, index) in djdkList[scope.$index].payList"
              :key="index"
            >
              {{ scope.row.payList[index].payDate }}<br />
            </span>
          </template>
        </el-table-column>
        <el-table-column label="实际打款金额">
          <template slot-scope="scope">
            <span
              v-for="(title, index) in djdkList[scope.$index].payList"
              :key="index"
            >
              <span
                v-if="
                  scope.row.payList[index].hasOwnProperty('payAmt') === true &&
                  scope.row.payList[index].payAmt !== null &&
                  scope.row.payList[index].payAmt !== ''
                "
                >{{ formaterMoney(scope.row.payList[index].payAmt) }}<br
              /></span>
              <span
                v-if="
                  scope.row.payList[index].hasOwnProperty('payAmt') === false ||
                  scope.row.payList[index].payAmt === null ||
                  scope.row.payList[index].payAmt === ''
                "
                ><br
              /></span>
            </span>
          </template>
          <!--            <template slot-scope="scope">-->
          <!--              <span v-for="(title, index) in djdkList[scope.$index].payList" :key="index">-->
          <!--                {{scope.row.payList[index].payAmt}}<br/>-->
          <!--                </span>-->
          <!--            </template>-->
        </el-table-column>
        <el-table-column label="抹平差额">
          <template slot-scope="scope">
            <span
              v-for="(title, index) in djdkList[scope.$index].payList"
              :key="index"
            >
              <span
                v-if="
                  scope.row.payList[index].hasOwnProperty('differenceAmt') ===
                    true &&
                  scope.row.payList[index].differenceAmt !== null &&
                  scope.row.payList[index].differenceAmt !== ''
                "
                >{{ formaterMoney(scope.row.payList[index].differenceAmt) }}<br
              /></span>
              <span
                v-if="
                  scope.row.payList[index].hasOwnProperty('differenceAmt') ===
                    false ||
                  scope.row.payList[index].differenceAmt === null ||
                  scope.row.payList[index].differenceAmt === ''
                "
                ><br
              /></span>
            </span>
          </template>
          <!--            <template slot-scope="scope">-->
          <!--              <span v-for="(title, index) in djdkList[scope.$index].payList" :key="index">-->
          <!--                {{scope.row.payList[index].differenceAmt}}<br/>-->
          <!--                </span>-->
          <!--            </template>-->
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="xiugaidjdk(scope.row)"
              v-if="scope.row.changeButton == 'true'"
              >修改</el-button
            >
            <el-button
              type="primary"
              @click="dakuanbutton(scope.row)"
              v-if="scope.row.payButton == 'true'"
              >打款</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div
        style="
          width: 100%;
          text-align: center;
          margin-top: 25px;
          margin-bottom: 25px;
        "
      >
        <el-button
          size="large"
          type="primary"
          @click="dkywcButton()"
          v-show="dkywc"
          >打款已完成</el-button
        >
        <el-button size="large" @click="closedakuantable()">关闭</el-button>
        <span v-show="dkywc" class="qrdkspan"
          >点击打款已完成，结束本期次信息费工作流程</span
        >
      </div>
    </div>
    <div
      style="
        width: 100%;
        height: 8px;
        background-color: #fbfbfb;
        margin-bottom: 12px;
      "
      v-show="jindutiaoshow"
    ></div>
    <!--    <div style="margin-top: 50px">-->
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="信息费明细汇总表" name="first">
        <div style="width: 100%; height: 60px">
          <span class="tabsspan1">担保公司：{{ this.custName }}</span>
          <span class="tabsspan2">汇款公司：{{ this.incomeCustName }}</span>
        </div>
        <el-table
          :data="overDetailBodyDetailList_v2"
          :summary-method="getSummaries01"
          show-summary
          :span-method="overDetailBodyDetailList_v2panMethod"
          border
          v-horizontal-scroll="'always'"
        >
          <el-table-column
            label="业务期次"
            fixed
            prop="termMonth"
            width="180px"
          />
          <el-table-column
            label="收款时间"
            width="180px"
            prop="collectionTime"
          />
          <el-table-column
            label="服务商"
            prop="serviceProvider"
            width="300px"
          />
          <el-table-column label="法催收入" prop="incomeAmt" width="150px">
            <template slot-scope="scope">
              {{ formaterMoney(scope.row.incomeAmt) }}<br />
            </template>
          </el-table-column>
          <el-table-column
            label="二级服务商"
            prop="serviceProviderSecond"
            width="300px"
          />
          <el-table-column
            label="二级服务商收入"
            prop="serviceProviderSecondIncome"
            width="180px"
          >
            <template slot-scope="scope">
              {{ formaterMoney(scope.row.serviceProviderSecondIncome) }}<br />
            </template>
          </el-table-column>
          <el-table-column label="出信息费公司" width="180px" prop="custName" />
          <el-table-column
            label="信息费公司"
            width="180px"
            prop="feeCustName"
          />

          <el-table-column
            v-if="checkPermi(['sensitive:detailLawTable'])"
            
            label="真实回款金额"
            prop="trueComeAmt"
            width="180px"
          >
            <template slot-scope="scope">
              {{ formaterMoney(scope.row.trueComeAmt) }}<br />
            </template>
          </el-table-column>
          <el-table-column label="服务费" v-if="checkPermi(['sensitive:detailLawTable'])" prop="serviceFee" width="180px">
            <template slot-scope="scope">
              {{ formaterMoney(scope.row.serviceFee) }}<br />
            </template>
          </el-table-column>
          <el-table-column v-if="checkPermi(['sensitive:detailLawTable'])" label="本金" prop="principal" width="180px">
            <template slot-scope="scope">
              {{ formaterMoney(scope.row.principal) }}<br />
            </template>
          </el-table-column>
          <el-table-column v-if="checkPermi(['sensitive:detailLawTable'])" label="本期信息费" prop="currentFee" width="180px">
            <template slot-scope="scope">
              <!--                  {{formaterMoney(scope.row.currentFee)}}<br/>-->
              <span v-if="scope.row.currentFee >= 0"
                >{{ formaterMoney(scope.row.currentFee) }}<br
              /></span>
              <span v-if="scope.row.currentFee < 0" style="color: red"
                >{{ formaterMoney(scope.row.currentFee) }}<br
              /></span>
            </template>
          </el-table-column>
          <el-table-column v-if="checkPermi(['sensitive:detailLawTable'])" label="挂起金额" prop="suspendAmt" width="180px">
            <template slot-scope="scope">
              <span
                v-if="
                  scope.row.suspendAmtSum !== null &&
                  scope.row.suspendAmtSum !== '' &&
                  scope.row.currentFee < 0
                "
                >+{{ formaterMoney(Number(scope.row.currentFee) * -1) }}<br
              /></span>
              <span
                v-if="
                  scope.row.suspendClearAmt !== null &&
                  scope.row.suspendAmtSum !== null &&
                  scope.row.suspendAmtSum !== '' &&
                  scope.row.currentFee > 0
                "
                >{{ formaterMoney(scope.row.suspendClearAmt) }}<br
              /></span>
            </template>
          </el-table-column>
          <el-table-column
           v-if="checkPermi(['sensitive:detailLawTable'])"
            label="挂起金额累计"
            prop="suspendAmtSum"
            width="180px"
          >
            <template slot-scope="scope">
              {{ formaterMoney(scope.row.suspendAmtSum) }}<br />
            </template>
          </el-table-column>
          <el-table-column v-if="checkPermi(['sensitive:detailLawTable'])" label="信息费" width="100px" prop="feeAmt">
            <template slot-scope="scope">
              <!--                  <span v-if="scope.row.feeAmt > 0">{{formaterMoney(scope.row.feeAmt)}}<br/></span>-->
              <!--                  <span v-if="scope.row.feeAmt < 0" style="color: red">{{formaterMoney(scope.row.feeAmt)}}<br/></span>-->
              {{ formaterMoney(scope.row.feeAmt) }}<br />
            </template>
          </el-table-column>
          <el-table-column v-if="checkPermi(['sensitive:detailLawTable'])" label="信息费取整" prop="feeRound" width="180px">
            <template slot-scope="scope">
              {{ formaterMoney(scope.row.feeRound) }}<br />
            </template>
          </el-table-column>
          <el-table-column v-if="checkPermi(['sensitive:detailLawTable'])" label="借条分润" prop="jtfrAmt" width="180px">
            <template slot-scope="scope">
              {{ formaterMoney(scope.row.jtfrAmt) }}<br />
            </template>
          </el-table-column>
          <el-table-column v-if="checkPermi(['sensitive:detailLawTable'])" label="法催利润" prop="lawProfit" width="180px">
            <template slot-scope="scope">
              {{ formaterMoney(scope.row.lawProfit) }}<br />
            </template>
          </el-table-column>

          <el-table-column label="期次状态" prop="phaseStatus" width="100px" />
          <el-table-column label="备注" width="180">
            <template slot-scope="scope">
              <span
                v-if="scope.row.remark !== null && scope.row.remark.length > 15"
              >
                {{ scope.row.remark.substring(0, 10)
                }}<el-button
                  type="text"
                  size="mini"
                  @click="textDetail(scope.row.remark)"
                  >更多
                </el-button>
              </span>
              <span
                v-if="
                  scope.row.remark !== null && scope.row.remark.length <= 15
                "
              >
                {{ scope.row.remark }}
              </span>
              <span v-if="scope.row.remark === null"> </span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane v-if="checkPermi(['sensitive:detailLawPersonnelRate'])" label="参与人员与费率" name="second">
        <div style="width: 100%">
          <div class="tabs2div12">
            <div style="width: 100%; height: 60px">
              <span class="tabs2span1">信息费公司与费率</span>
            </div>
            <div style="width: 60%">
              <el-table
                v-loading="loading"
                :data="feecustNameAndfeilvList"
                border
              >
                <el-table-column
                  label="信息费公司名称"
                  align="center"
                  prop="custName"
                  min-width="30%"
                >
                  <template slot-scope="scope">
                    <span>{{ scope.row.custName }} </span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="费率"
                  align="center"
                  prop="rate"
                  min-width="15%"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.rate == '未设置'"
                      >{{ scope.row.rate }}
                    </span>
                    <span v-if="scope.row.rate != '未设置'"
                      >{{ scope.row.rate }}%</span
                    >
                    <!-- <span v-if="scope.row.rate!='未设置'" >{{scope.row.rate}}%</span> -->
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="
                    feecustNameAndfeilvList.some(
                      (item) => item.replaceFlag == 1
                    )
                  "
                  label="曾用公司(发生过替换)"
                  align="center"
                  min-width="15%"
                >
                  <template slot-scope="scope">
                    <span
                      v-if="
                        scope.row.oldCompanyName &&
                        scope.row.oldCompanyName.length > 0
                      "
                    >
                      <p v-for="item in scope.row.oldCompanyName" :key="item">
                        {{ item }}
                      </p>
                    </span>
                    <span v-else>--</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-else
                  label="曾用公司"
                  align="center"
                  min-width="15%"
                >
                  <template slot-scope="scope">
                    <span
                      v-if="
                        scope.row.oldCompanyName &&
                        scope.row.oldCompanyName.length > 0
                      "
                    >
                      <p v-for="item in scope.row.oldCompanyName" :key="item">
                        {{ item }}
                      </p>
                    </span>
                    <span v-else>--</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="tabs2div12">
            <div style="width: 100%; height: 60px">
              <span class="tabs2span1">替换信息费公司记录</span>
            </div>
            <el-table
              style="width: 80%"
              v-loading="loading"
              :data="replaceCompanyInfo"
              border
            >
              <el-table-column
                label="新信息费公司名称"
                align="center"
                prop="newOaTraderUserName"
              >
                <template slot-scope="scope">
                  {{ scope.row.newOaTraderUserName }}
                  <span v-if="scope.row.status == 1" style="color: #999"
                    >（该次替换已被撤销）</span
                  >
                </template>
              </el-table-column>
              <el-table-column
                label="原信息费公司名称"
                align="center"
                prop="oldOaTraderUserName"
              />
              <el-table-column
                label="替换时间"
                align="center"
                prop="replaceTime"
                width="240"
              />
              <el-table-column
                label="操作人"
                width="120"
                align="center"
                prop="replaceBy"
              />
              <el-table-column label="备注" align="center" prop="remark" />
              <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    v-if="scope.row.status == 0"
                    @click="withdraw(scope.row)"
                    >撤销替换</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="tabs2div1">
            <div style="width: 100%; height: 60px">
              <span class="tabs2span1">项目成员</span>
            </div>
            <div style="width: 60%">
              <el-table v-loading="loading" :data="userList" border>
                <el-table-column label="角色" align="center" prop="flag" />
                <el-table-column
                  label="用户姓名"
                  align="center"
                  prop="people_name"
                />
              </el-table>
            </div>
          </div>
        </div>

        <!--        <div style="display: flex;width: 100%;flex-wrap: wrap;flex-direction: row;">-->
        <!--          <div class="tabs2div">-->
        <!--            <div style="width: 100%;height:60px;">-->
        <!--            <span class="tabs2span1">信息费公司与费率</span>-->
        <!--            </div>-->
        <!--            <div style="width: 60%;float:left">-->
        <!--                <el-table v-loading="loading" :data="feecustNameAndfeilvList" border  >-->
        <!--                      <el-table-column label="信息费公司名称" align="center" prop="custName" />-->
        <!--                      <el-table-column label="费率" align="center" prop="rate" >-->
        <!--                        <template slot-scope="scope">-->
        <!--                          <span v-if="scope.row.rate=='未设置'">{{scope.row.rate}} </span>-->
        <!--                          <span v-if="scope.row.rate!='未设置'" >{{scope.row.rate}}%</span>-->
        <!--                          &lt;!&ndash; <span v-if="scope.row.rate!='未设置'" >{{scope.row.rate}}%</span> &ndash;&gt;-->
        <!--                        </template>-->
        <!--                      </el-table-column>-->
        <!--                      <el-table-column label="税率" align="center" prop="taxRate" >-->
        <!--                        <template slot-scope="scope">-->
        <!--                          <span v-if="scope.row.taxRate=='未设置'">{{scope.row.taxRate}} </span>-->
        <!--                          <span v-if="scope.row.taxRate!='未设置'" >{{scope.row.taxRate}}%</span>-->

        <!--                        </template>-->
        <!--                      </el-table-column>-->
        <!--                </el-table>-->
        <!--              </div>-->
        <!--          </div>-->
        <!--          <div class="tabs2div">-->
        <!--              <div style="width: 100%;height:60px;">-->
        <!--                <span class="tabs2span1">项目成员</span>-->
        <!--                </div>-->
        <!--                <div style="width: 60%;float:left">-->
        <!--                    <el-table v-loading="loading" :data="userList" border  >-->
        <!--                          <el-table-column label="角色" align="center" prop="flag" />-->
        <!--                          <el-table-column label="用户姓名" align="center" prop="people_name" />-->
        <!--                    </el-table>-->
        <!--                  </div>-->
        <!--          </div>-->
        <!--        </div>-->
      </el-tab-pane>
      <el-tab-pane label="记账凭证规则" name="three">
        <p style="font-weight: bold">
          是否生成记账凭证：{{
            projectData.generateCertificateFlag == 0 ? "否" : "是"
          }}
        </p>
        <div v-if="projectData.generateCertificateFlag == 1">
          <span stlye="color:#999"
            >项目将在每个期次完结后，自动生成2种记账凭证</span
          >
          <el-table
            border
            :data="jztableData"
            style="width: 100%; margin-top: 16px"
          >
            <el-table-column prop="zjsx" label="资金事项" />
            <el-table-column prop="zy" label="摘要" />
            <el-table-column prop="date" label="借方科目">
              <template slot-scope="scope">
                <div v-if="scope.row.jfkm == 1">
                  <p style="margin-bottom: 0">直接收款类型:</p>
                  <span style="color: #999">银行存款-担保费收款方简称</span>
                  <p style="margin-bottom: 0; margin-top: 12px">
                    混合平台收益类型:
                  </p>
                  <span style="color: #999">预收账款-项目名称</span>
                </div>
                <div v-else>主营业务成本-项目名称</div>
              </template>
            </el-table-column>
            <el-table-column prop="jfje" label="借方金额">
              <template slot-scope="scope">
                <div v-if="scope.row.jfje == 1">该期次的收入</div>
                <div v-else>该期次的信息费</div>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="贷方科目">
              <template slot-scope="scope">
                <div v-if="scope.row.dfkm == 1">
                  <p style="margin-bottom: 0">担保费收入-项目名称:</p>
                </div>
                <div v-else>
                  <p style="margin-bottom: 0">每个期次生成2类贷方科目:</p>
                  <span style="color: #999"
                    >应付账款-项目名称-信息费公司名称</span
                  ><br />
                  <span style="color: #999">营业外收入</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="贷方金额">
              <template slot-scope="scope">
                <div v-if="scope.row.dfje == 1">
                  <p style="margin-bottom: 0">收入</p>
                </div>
                <div v-else>
                  <p style="margin-bottom: 0">2类贷方科目对应金额:</p>
                  <span style="color: #999">信息费取整</span><br />
                  <span style="color: #999">应付信息费减信息费取整</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <p style="font-weight: bold">
            凭证所属账套：{{ projectData.companyName }}
          </p>
          <p style="font-weight: bold">
            担保费收入的类型：{{
              projectData.guaranteeIncomeType == 0 ? "直接收款" : "混合平台收益"
            }}
          </p>
          <p style="font-weight: bold">
            担保费收款方：{{ projectData.guarantyPayeeName
            }}<span style="margin-left: 20px"
              >简称：{{ projectData.abbreviation }}</span
            >
          </p>
          <span>收款方简称用于生成担保费收入记账凭证的借方二级科目</span>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="checkPermi(['sensitive:detailLawOaPay'])" label="OA流程支付信息费记录" name="four">
        <span style="color: #999"
          >在OA系统发起支付信息费流程成功后，在本系统增加的已支付信息费记录</span
        >
        <el-table
          border
          :data="alreadyPayList"
          style="width: 100%; margin-top: 16px"
        >
          <el-table-column prop="oaCompleteTime" label="发起时间" />
          <el-table-column prop="zjsx" label="流程主题">
            <template>财务项目管理</template>
          </el-table-column>
          <el-table-column prop="flowModelName" label="流程模板" />
          <el-table-column prop="initiator" label="发起人" />
          <el-table-column prop="termMonth" label="业务期次" />
          <el-table-column prop="amount" label="支付金额" />

          <el-table-column prop="zjsx" label="收款人是否为信息费公司">
            <template slot-scope="scope">
              <span v-if="scope.row.isRebateCom == 'Y'">是</span>
              <span v-else style="color: red">否</span>
            </template>
          </el-table-column>
          <el-table-column prop="payBefore" label="信息费未结清-打款前" />
          <el-table-column prop="payAfter" label="信息费未结清-打款后" />
          <el-table-column prop="zjsx" label="生成记账凭证">
            <template slot-scope="scope">
              <span v-if="scope.row.synStatus == 'Y'">成功</span>
              <span v-else style="color: red">失败</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <!--    </div>-->

    <el-dialog
      :title="title"
      :visible.sync="remarkDeliog"
      width="50%"
      append-to-body
      :close-on-click-modal="false"
    >
      <span class="remarkDetailSpan">备注</span>
      <div style="width: 100%; height: 30px"></div>
      <span>{{ this.remarkObj }}</span>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" @click="closeRemarkDetail">关 闭</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="title" :visible.sync="open" width="40%" append-to-body>
      <span class="addprojectSpan">新增业务期次</span>
      <el-divider></el-divider>
      <el-image style="width: 100%" :src="inmage"></el-image>
      <el-form
        ref="form"
        :model="addprojectform"
        :rules="rules"
        label-width="40%"
      >
        <el-form-item label="业务期次类型">
          <el-radio-group v-model="addprojectform.term">
            <el-radio label="0">整月</el-radio>
            <el-radio label="1">非整月</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="addprojectform.term == 0" label="期次所属月份">
          <el-date-picker
            v-model="addprojectform.termMonth"
            type="month"
            value-format="yyyy-MM"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item v-if="addprojectform.term == 1" label="期次所属时间范围">
          <el-date-picker
            v-model="selectDate"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <!--             <span class="formspan">设置期次所属时间，用于按时间范围做统计</span>-->
      </el-form>
      <br />
      <!--            <div slot="footer" class="dialog-footer">-->
      <div style="text-align: center">
        <el-button size="large" type="primary" @click="submitForm()"
          >新 增</el-button
        >
        <el-button size="large" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!--          <el-dialog :title="title" :visible.sync="updateproyewudel" width="50%" append-to-body>-->
    <!--            <span class="addprojectSpan">修改项目所属业务负责人</span>-->
    <!--            <div style="width: 100%;height:30px;"></div>-->
    <!--            <div style="width: 100%;height:60px;">-->
    <!--              <span style=" margin-left: 35px;" >业务：</span>-->
    <!--                <el-select v-model="updateyewu" placeholder="请选择业务人员" filterable multiple size="small">-->
    <!--                                  <el-option-->
    <!--                                    v-for="dict in this.yewuList"-->
    <!--                                    :key="dict.value"-->
    <!--                                    :label="dict.label"-->
    <!--                                    :value="dict.value"-->
    <!--                                  />-->
    <!--                                </el-select>-->

    <!--            </div>-->
    <!--            <span class="form2span3">业务角色负责确认每月会计录入的各项金额正确无误</span><br>-->
    <!--              <div slot="footer" class="dialog-footer">-->
    <!--              <el-button type="primary" @click="submitupdateyewu()">保存</el-button>-->
    <!--              <el-button @click="closeyewu()">取 消</el-button>-->
    <!--            </div>-->
    <!--          </el-dialog>-->

    <el-dialog
      :title="title"
      :visible.sync="lurudeilog"
      width="1080px"
      append-to-body
    >
      <span class="addprojectSpan">业务期次：{{ this.yewuqicideilog }}</span>
      <el-divider></el-divider>
      <span style="font-weight: bold">收款时间</span>
      <el-date-picker
        style="margin-left: 8px"
        v-model="collectionTime"
        type="month"
        format="yyyy年MM月"
        value-format="yyyy-MM"
        placeholder="选择日期"
      >
      </el-date-picker>
      <el-divider></el-divider>
      <div style="width: 100%; height: 60%">
        <el-form ref="shouruForm" :model="shouruForm" label-width="80px">
          <div>
            <el-form-item
              v-for="(domain, index) in shouruForm.fuwushang"
              :label="'服务商'"
              :key="domain.key"
            >
              <el-autocomplete
                class="inline-input"
                v-model="domain.yijifuwushangName"
                :fetch-suggestions="querySearchyijifuwushang"
                placeholder="请输入内容"
                style="width: 30%"
                :maxlength="30"
                show-word-limit
                clearable
              ></el-autocomplete>
              <!-- <el-input maxlength="30"
      show-word-limit clearable v-model="domain.custName" style="width: 30%;"></el-input> -->
              <span style="margin-left: 20px; margin-right: 5px"
                >是否有二级服务商</span
              >
              <el-select
                style="width: 70px"
                v-model="domain.valuetype"
                @change="
                  (value) => {
                    xuanzeerjifuwushang(value, domain, index);
                  }
                "
              >
                <el-option
                  v-for="item in domain.erjifuwushangSelect"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <span style="margin-left: 20px; margin-right: 5px">收入</span>
              <el-input
                v-if="domain.erjifuwushangShow === false"
                type="number"
                v-model="domain.yijifuwushangIncome"
                style="width: 17%"
                @change="changeIncome(index)"
              ></el-input>
              <el-input
                v-if="domain.erjifuwushangShow === true"
                type="number"
                disabled
                v-model="domain.yijifuwushangIncome"
                style="width: 17%"
              ></el-input>
              <!--                    <span v-if="domain.erjifuwushangShow === true"  v-model="domain.yijifuwushangIncome" style="width: 12%;"></span>-->
              <el-button
                size="large"
                type="primary"
                v-if="index > 0"
                style="margin-left: 20px"
                @click.prevent="removeDomainFuwushang(domain)"
                >-删除</el-button
              >
              <!--                    <span style="margin-left: 20px" v-if="index === 0">-->
              <!--                      <span>收入时间</span>-->
              <!--                      <el-date-picker-->
              <!--                        style="margin-left: 8px"-->
              <!--                        v-model="collectionTime"-->
              <!--                        type="month"-->
              <!--                        format="yyyy年MM月"-->
              <!--                        value-format="yyyy-MM"-->
              <!--                        placeholder="选择日期">-->
              <!--                      </el-date-picker>-->
              <!--                    </span>-->
              <div v-show="domain.erjifuwushangShow" style="width: 100%">
                <el-form-item
                  v-for="(erjifuwushangObj, index1) in domain.erjifuwushang"
                  :label="'二级服务商'"
                  :key="erjifuwushangObj.key"
                  label-width="90px"
                >
                  <el-autocomplete
                    class="inline-input"
                    v-model="erjifuwushangObj.erjifuwushangName"
                    :fetch-suggestions="querySearcherjifuwushang"
                    placeholder="请输入内容"
                    style="width: 30%"
                    :maxlength="30"
                    show-word-limit
                    clearable
                  ></el-autocomplete>
                  <span style="margin-left: 165px; margin-right: 5px"
                    >收入</span
                  >
                  <el-input
                    type="number"
                    v-model="erjifuwushangObj.erjifuwushangIncome"
                    style="width: 19%"
                    @change="erjifuwushangIncomeSum(index, index1)"
                  ></el-input>
                  <el-button
                    size="large"
                    type="primary"
                    v-if="index1 > 0"
                    style="margin-left: 20px; margin-top: 3px"
                    @click.prevent="
                      removeDomainErjiFuwushang(index, erjifuwushangObj)
                    "
                    >-删除</el-button
                  >
                </el-form-item>
                <el-form-item>
                  <el-button
                    size="large"
                    type="primary"
                    @click="addDomainErjiFuwushang(index)"
                    >+添加二级服务商</el-button
                  >
                </el-form-item>
                <el-divider></el-divider>
              </div>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button size="large" type="primary" @click="addDomainFuwushang()"
              >+添加服务商</el-button
            >
          </el-form-item>
        </el-form>
        <el-divider></el-divider>

        <div
          style="
            display: flex;
            width: 100%;
            height: 50px;
            text-align: center;
            flex-wrap: wrap;
            flex-direction: row;
          "
        >
          <div
            style="width: 23%; height: 50px; text-align: left; margin-left: 5%"
          >
            <span class="lrffdivspan123">合计</span>
          </div>
          <div style="width: 23%; height: 50px"></div>
          <div style="width: 23%; height: 50px">
            <span class="lrffdivspan1234">￥：{{ this.shouruheji }}</span
            ><br />
          </div>
          <!--                <div style=" width: 23%;height:50px;">-->
          <!--                  <span class="lrffdivspan1" >￥：{{this.tichengfanfeicount}}</span><br>-->
          <!--                </div>-->
        </div>
        <el-divider></el-divider>
        <br />
        <span style="font-weight: bold">备注信息</span><br />
        <el-input
          type="textarea"
          :rows="3"
          style="width: 600px; margin-top: 10px"
          placeholder="请输入内容"
          v-model="lrremark"
        >
        </el-input>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="nextToFeeCompany()"
          >下一步</el-button
        >
        <el-button size="large" @click="closeluru()">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="title"
      :visible.sync="querensrdeilog"
      width="40%"
      append-to-body
    >
      <span class="addprojectSpan">业务期次：{{ this.yewuqicideilog }}</span>
      <el-divider></el-divider>
      <el-form ref="feeCompanyForm" :model="feeCompanyForm" label-width="80px">
        <div style="width: 100%; height: 200px">
          <!--                <el-form-item v-for="(domain, index) in feeCompanyForm" :key="domain.key">-->
          <span style="margin-right: 10px; font-weight: bold"
            >出信息费公司</span
          >
          <el-autocomplete
            class="inline-input"
            v-model="feeCompanyForm.custName"
            :fetch-suggestions="querySearchCustNameList"
            placeholder="请输入内容"
            style="width: 30%"
            :maxlength="30"
            show-word-limit
            clearable
          ></el-autocomplete>
          <!--                <el-input type="text" v-model="feeCompanyForm.custName" placeholder="请输入内容" style="width: 28%;" />-->
          <span style="margin-left: 20px; margin-right: 10px; font-weight: bold"
            >信息费公司</span
          >
          <el-autocomplete
            class="inline-input"
            v-model="feeCompanyForm.feeCustName"
            :fetch-suggestions="querySearchFeeList"
            placeholder="请输入内容"
            style="width: 30%"
            :maxlength="30"
            show-word-limit
            clearable
            @blur="custIdHuoQu()"
            @change="custIdHuoQu()"
          ></el-autocomplete>
          <!--                  <el-button type="primary" v-if="(index>0)" style="margin-left:20px" @click.prevent="removeDomainFuwushang(domain)">-删除</el-button>-->
          <!--                </el-form-item>-->
        </div>
        <span style="size: 13px; color: red" v-if="nextToFeeInfoFlag === false"
          >请您输入或选择已有的信息费公司；若已填写但无法进行，点击信息费公司输入框离开即可</span
        >
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
        v-if="nextToFeeInfoFlag === true"
      >
        <el-button size="large" @click="backToShouru()">上一步</el-button>
        <el-button size="large" type="primary" @click="nextToFeeInfo()"
          >下一步</el-button
        >
        <el-button size="large" @click="quxiaoshouru()">取 消</el-button>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
        v-if="nextToFeeInfoFlag === false"
      >
        <el-button size="large" @click="backToShouru()">上一步</el-button>
        <el-button size="large" type="primary" disabled>下一步</el-button>
        <el-button size="large" @click="quxiaoshouru()">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="title"
      :visible.sync="lurufanfeideilog"
      width="80%"
      append-to-body
    >
      <span class="addprojectSpan">业务期次：{{ this.yewuqicideilog }}</span>
      <el-divider></el-divider>
      <span class="projectnamespan123"
        >信息费 = ( 真实回款金额 - 服务费 - ( 本金 × 3%)) × 费率</span
      ><br />
      <span class="projectnamespan123"
        >法催利润=真实回款金额 - 借条分润 - 服务费 - 信息费取整</span
      >
      <div style="width: 100%; height: 60%">
        <div style="width: 100%; height: 20px"></div>
        <div style="margin-left: 0.3%; margin-bottom: 12px">
          <span>出信息费公司：{{ this.feeCompanyForm.custName }}</span>
          <span style="margin-left: 10%"
            >信息费公司：{{ this.feeCompanyForm.feeCustName }}</span
          >
          <span style="margin-left: 10%"
            >费率：{{ this.feeCompanyForm.rate }}%</span
          >
        </div>
        <div>
          <el-form
            :model="incomeAndFeeForm"
            label-width="40px"
            :inline="true"
            size="small"
            label-position="center"
          >
            <el-table
              :data="incomeAndFeeForm.incomeAndFeeList"
              :row-class-name="rowClassName"
              :summary-method="getSummaries03"
              show-summary
            >
              <el-table-column label="服务商" min-width="50">
                <template slot-scope="scope">
                  <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.serviceProviderName'" >-->
                  <!--                    <el-input v-model="scope.row.serviceProviderName"></el-input>-->
                  <!--                  </el-form-item>-->
                  <span
                    v-if="
                      scope.row.yijifuwushangName !== null &&
                      scope.row.yijifuwushangName !== ''
                    "
                    >{{ scope.row.yijifuwushangName }}</span
                  >
                  <span
                    style="margin-left: 30px"
                    v-if="
                      (scope.row.yijifuwushangName === null ||
                        scope.row.yijifuwushangName !== '') &&
                      scope.row.serviceProviderSecondName !== null &&
                      scope.row.serviceProviderSecondName !== ''
                    "
                    >{{ scope.row.serviceProviderSecondName }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column label="法催收入" min-width="50">
                <template slot-scope="scope">
                  <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.serviceProviderIncome'" >-->
                  <!--                    <el-input v-model="scope.row.serviceProviderIncome"></el-input>-->
                  <!--                  </el-form-item>-->
                  <span
                    v-if="
                      scope.row.yijifuwushangIncome !== null &&
                      scope.row.yijifuwushangIncome !== ''
                    "
                    >{{ scope.row.yijifuwushangIncome }}</span
                  >
                  <span
                    v-if="
                      (scope.row.yijifuwushangIncome === null ||
                        scope.row.yijifuwushangIncome !== '') &&
                      scope.row.serviceProviderSecondIncome !== null &&
                      scope.row.serviceProviderSecondIncome !== ''
                    "
                    >{{ scope.row.serviceProviderSecondIncome }}</span
                  >
                </template>
              </el-table-column>
              <!--              <el-table-column label="二级服务商"  min-width="50">-->
              <!--                <template slot-scope="scope">-->
              <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.serviceProviderSecondName'" >-->
              <!--                    <el-input v-model="scope.row.serviceProviderSecondName"></el-input>-->
              <!--                  </el-form-item>-->
              <!--                  {{scope.row.serviceProviderSecondName}}-->
              <!--                </template>-->
              <!--              </el-table-column>-->
              <!--              <el-table-column label="二级服务商收入"  min-width="50">-->
              <!--                <template slot-scope="scope">-->
              <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.serviceProviderSecondIncome'" >-->
              <!--                    <el-input v-model="scope.row.serviceProviderSecondIncome"></el-input>-->
              <!--                  </el-form-item>-->
              <!--                  {{scope.row.serviceProviderSecondIncome}}-->
              <!--                </template>-->
              <!--              </el-table-column>-->
              <!--              <el-table-column label="出信息费公司"  min-width="50">-->
              <!--                <template slot-scope="scope">-->
              <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.custName'" >-->
              <!--                    <el-input v-model="scope.row.custName"></el-input>-->
              <!--                  </el-form-item>-->
              <!--                  {{scope.row.custName}}-->
              <!--                </template>-->
              <!--              </el-table-column>-->

              <!--              <el-table-column label="信息费公司"  min-width="50">-->
              <!--                <template slot-scope="scope">-->
              <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.feeCustName'" >-->
              <!--                    <el-input v-model="scope.row.feeCustName"></el-input>-->
              <!--                  </el-form-item>-->
              <!--                  {{scope.row.feeCustName}}-->
              <!--                </template>-->
              <!--              </el-table-column>-->

              <el-table-column label="真实回款金额" min-width="50">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.erjifuwushangShow === false"
                    :prop="'incomeAndFeeList.' + scope.$index + '.trueComeAmt'"
                  >
                    <el-input
                      type="number"
                      v-model="scope.row.trueComeAmt"
                      @change="changeTrueComeAmt(scope.$index)"
                      @blur="changeTrueComeAmt(scope.$index)"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="服务费" min-width="50">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.erjifuwushangShow === false"
                    :prop="'incomeAndFeeList.' + scope.$index + '.serviceFee'"
                  >
                    <el-input
                      type="number"
                      v-model="scope.row.serviceFee"
                      @change="changeTrueComeAmt(scope.$index)"
                      @blur="changeTrueComeAmt(scope.$index)"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="本金" min-width="50">
                <template slot-scope="scope">
                  <el-form-item
                    v-if="scope.row.erjifuwushangShow === false"
                    :prop="'incomeAndFeeList.' + scope.$index + '.principal'"
                  >
                    <el-input
                      type="number"
                      v-model="scope.row.principal"
                      @change="changeTrueComeAmt(scope.$index)"
                      @blur="changeTrueComeAmt(scope.$index)"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="本期信息费" min-width="50">
                <template slot-scope="scope">
                  <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.feeAmt'">-->
                  <!--                    <el-input  type="number" v-model="scope.row.feeAmt"  @blur="onblurfanfei(scope.$index)"></el-input>-->
                  <!--                  </el-form-item>-->
                  <!--                  <span v-if="scope.row.feeAmtSupendFlagIsOne >= 0">{{scope.row.feeAmt}}</span>-->
                  <span v-if="scope.row.currentFee >= 0">{{
                    scope.row.currentFee
                  }}</span>
                  <span v-if="scope.row.currentFee < 0" style="color: red">{{
                    scope.row.currentFee
                  }}</span>
                </template>
              </el-table-column>

              <el-table-column label="挂起金额" min-width="50">
                <template slot-scope="scope">
                  <span v-if="scope.row.currentFee < 0"
                    >+{{ Number(scope.row.currentFee) * -1 }}</span
                  >
                  <span
                    v-if="
                      scope.row.erjifuwushangShow === false &&
                      scope.row.serviceProviderFlag === 1 &&
                      scope.row.currentFee !== null &&
                      Number(scope.row.currentFee) > 0 &&
                      Number(scope.row.currentFee) >
                        Number(
                          scope.row.serviceProviderFeeAmtSuspendFlagIsOne
                        ) &&
                      Number(scope.row.serviceProviderFeeAmtSuspendFlagIsOne) <
                        0
                    "
                    >{{ scope.row.serviceProviderFeeAmtSuspendFlagIsOne }}</span
                  >
                  <span
                    v-if="
                      scope.row.erjifuwushangShow === false &&
                      scope.row.serviceProviderFlag === 2 &&
                      scope.row.currentFee !== null &&
                      Number(scope.row.currentFee) > 0 &&
                      Number(scope.row.currentFee) >
                        Number(
                          scope.row.serviceProviderSecondFeeAmtSuspendFlagIsOne
                        ) &&
                      Number(
                        scope.row.serviceProviderSecondFeeAmtSuspendFlagIsOne
                      ) < 0
                    "
                    >{{
                      scope.row.serviceProviderSecondFeeAmtSuspendFlagIsOne
                    }}</span
                  >
                </template>
              </el-table-column>

              <!--              <el-table-column label="累计挂起金额"  min-width="50">-->
              <!--                <template slot-scope="scope">-->
              <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.feeAmt'">-->
              <!--                    <el-input  type="number" v-model="scope.row.feeAmt"  @blur="onblurfanfei(scope.$index)"></el-input>-->
              <!--                  </el-form-item>-->
              <!--                  <span v-if="scope.row.feeAmtSupendFlagIsOne >= 0">{{scope.row.feeAmt}}</span>-->
              <!--                  <span v-if="scope.row.serviceProviderSecondFeeAmtSuspendFlagIsOne < 0" style="color: red">{{scope.row.serviceProviderSecondFeeAmtSuspendFlagIsOne}}</span>-->
              <!--                  <span v-if="scope.row.serviceProviderFeeAmtSuspendFlagIsOne < 0" style="color: red">{{scope.row.serviceProviderFeeAmtSuspendFlagIsOne}}</span>-->
              <!--                  <span v-if="scope.row.serviceProviderSecondFeeAmtSuspendFlagIsOne === null && scope.row.serviceProviderFeeAmtSuspendFlagIsOne === null">-</span>-->

              <!--                  <span v-if="scope.row.serviceProviderFeeAmtSuspendFlagIsOne < 0" style="color: red">{{scope.row.serviceProviderFeeAmtSuspendFlagIsOne}}</span>-->

              <!--                  <span v-if="scope.row.feeAmtSuspendFlagSum < 0">{{scope.row.feeAmtSuspendFlagSum}}</span>-->
              <!--                  <span v-if="scope.row.feeAmtSuspendFlagSum < 0" style="color: red">{{scope.row.feeAmtSuspendFlagSum}}</span>-->
              <!--                  <span v-if="scope.row.feeAmtSuspendFlagSum === null"></span>-->
              <!--                </template>-->
              <!--              </el-table-column>-->

              <el-table-column label="信息费" min-width="50">
                <template slot-scope="scope">
                  <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.feeAmt'">-->
                  <!--                    <el-input  type="number" v-model="scope.row.feeAmt"  @blur="onblurfanfei(scope.$index)"></el-input>-->
                  <!--                  </el-form-item>-->
                  {{ scope.row.feeAmt }}
                  <!--                  <span v-if="scope.row.feeAmt >= 0">{{scope.row.feeAmt}}</span>-->
                  <!--                  <span v-if="scope.row.feeAmt < 0" style="color: red">{{scope.row.feeAmt}}</span>-->
                </template>
              </el-table-column>

              <el-table-column label="信息费取整" min-width="50">
                <template slot-scope="scope">
                  <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.feeRound'" >-->
                  <!--                    <el-input  type="number" v-model="scope.row.feeRound" @blur="onblurtc(scope.$index)" ></el-input>-->
                  <!--                  </el-form-item>-->
                  {{ scope.row.feeRound }}
                </template>
              </el-table-column>

              <!--              <el-table-column label="提成信息费"  min-width="50">-->
              <!--                <template slot-scope="scope">-->
              <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.feeAmt2'" >-->
              <!--                    <el-input  type="number" v-model="scope.row.feeAmt2" @blur="onblurtc(scope.$index)" ></el-input>-->
              <!--                  </el-form-item>-->
              <!--                  {{scope.row.feeAmt2}}-->
              <!--                </template>-->
              <!--              </el-table-column>-->

              <el-table-column label="借条分润" min-width="50">
                <template slot-scope="scope">
                  <!--                                    <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.feeAmt2'" >-->
                  <!--                                      <el-input  type="number" v-model="scope.row.feeAmt2" @blur="onblurtc(scope.$index)" ></el-input>-->
                  <!--                                    </el-form-item>-->
                  <el-form-item
                    v-if="scope.row.erjifuwushangShow === false"
                    :prop="'incomeAndFeeList.' + scope.$index + '.jtfrAmt'"
                  >
                    <el-input
                      type="number"
                      v-model="scope.row.jtfrAmt"
                      @change="changeJtfrAmt(scope.$index)"
                      @blur="changeJtfrAmt(scope.$index)"
                    ></el-input>
                  </el-form-item>
                  <!--                  {{scope.row.jtfrAmt}}-->
                </template>
              </el-table-column>

              <el-table-column label="法催利润" min-width="50">
                <template slot-scope="scope">
                  <!--                  <el-form-item :prop="'incomeAndFeeList.'+scope.$index+'.feeAmt2'" >-->
                  <!--                    <el-input  type="number" v-model="scope.row.feeAmt2" @blur="onblurtc(scope.$index)" ></el-input>-->
                  <!--                  </el-form-item>-->
                  {{ scope.row.lawProfit }}
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </div>
        <!--        <el-divider></el-divider>-->

        <div
          v-if="feeAmtRedFlag === true || guaqiFeeAmtFlag === true"
          style="width: 100%; margin-top: 12px"
        >
          <span style="color: red" v-show="feeAmtRedFlag"
            >当服务商信息费出现负数时，将自动挂起，本期次不需打款，待下次该服务商存在正信息费时予以扣除</span
          ><br />
          <span style="color: red" v-show="guaqiFeeAmtFlag"
            >服务商往期负数的信息费金额，已在本期发生扣除</span
          >
          <!--          <div style=" width: 23%;height:50px;text-align: left;" ><span class="lrffdivspan1">合计</span></div>-->
          <!--          <div style=" width: 23%;height:50px;"></div>-->
          <!--          <div style=" width: 23%;height:50px;">-->
          <!--            <span class="lrffdivspan1" >￥：{{this.fanfeicount}}</span><br>-->
          <!--          </div>-->
          <!--          <div style=" width: 23%;height:50px;">-->
          <!--            <span class="lrffdivspan1" >￥：{{this.tichengfanfeicount}}</span><br>-->
          <!--          </div>-->
        </div>
        <!--        <div style="display: flex; width: 100%;height:60px; flex-wrap: wrap;flex-direction: row;" >-->
        <!--          <div style=" width: 25%;">-->
        <!--            <span style="font-size: 18px;">收款时间</span><span  style="margin-left: 13px;font-size: 18px;">{{this.collectionTimeString}}</span>-->
        <!--          </div>-->
        <!--          <div style=" width: 25%;height:100px;">-->
        <!--            <span class="lrffdivspan1" >毛利   ￥：{{this.maolijine}}</span><br>-->
        <!--            <span class="lrffdivspan2" >毛利= 收入 - 信息费取整</span>-->
        <!--          </div>-->
        <!--          <div style=" width: 25%;height:100px;">-->
        <!--            <span class="lrffdivspan1" >提成毛利   ￥：{{this.tichengmaolijine}}</span><br>-->
        <!--            <span class="lrffdivspan2" >提成毛利= 收入 - 提成信息费</span><br>-->
        <!--          </div>-->
        <!--          <div style=" width: 25%;height:100px;"></div>-->
        <!--          <div style=" width: 25%;height:100px;"></div>-->
        <!--        </div>-->
        <!--        <div style="width: 100%;height:50px; text-align: center;"></div>-->
        <div style="margin-top: 12px">
          <span style="margin-top: 12px" class="lurushouruspan2">备注信息</span
          ><br />
          <el-input
            type="textarea"
            :rows="3"
            style="width: 600px; margin-top: 10px"
            placeholder="请输入内容"
            v-model="lrremark"
          >
          </el-input>
        </div>
      </div>
      <div style="width: 100%; height: 50px; text-align: center"></div>
      <div style="width: 100%; height: 100px; text-align: center">
        <el-button size="large" @click="backToFeeCompany()">上一步</el-button>
        <el-button size="large" type="primary" @click="submitIncomeAndFee()"
          >提交会计确认</el-button
        >
        <el-button size="large" @click="closeluruff()">取 消</el-button><br />
        <span class="lrffdivspan233"
          >点击提交，等待会计人员确认金额是否正确</span
        >
      </div>
    </el-dialog>

    <!--
          <el-dialog :title="title" :visible.sync="updatelurudeilog" width="50%" append-to-body>
            <span class="addprojectSpan">业务期次：{{this.yewuqicideilog}}</span>
            <el-divider></el-divider>
            <div style="width: 100%;height:60%; ">

                <span class="lurushouruspan">收入   ￥：</span><el-input v-model="lrdeshouru" style="width:200px" placeholder="请输入内容"></el-input><br>
                <div style="width: 100%;height:20px;"></div>
                <span class="lurushouruspan2">备注信息</span><br>
                <br>
                <br>
                <el-input
                  type="textarea"
                  :rows="3"
                  style="width:600px"
                  placeholder="请输入内容"
                  v-model="lrremark">
                </el-input>

            </div>
              <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submitupdateluru()">保 存</el-button>
              <el-button @click="closeupdateluru()">取 消</el-button>
            </div>
          </el-dialog> -->

    <el-dialog
      :title="title"
      :visible.sync="abortprojectdeilog"
      width="30%"
      append-to-body
    >
      <span class="addprojectSpan">终止项目</span>
      <div style="width: 100%; height: 30px"></div>
      <div style="width: 100%; height: 60px">
        <span style="font-size: 16px; margin-left: 35px">是否终止该项目？</span
        ><br />
        <span style="font-size: 16px; margin-left: 35px"
          >项目被终止后将不能被编辑修改，并移至[完结项目归档]列表中</span
        ><br />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" @click="closezhongzhi()">取 消</el-button>
        <el-button size="large" type="primary" @click="quedingzhongzhi()"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      :title="title"
      :visible.sync="updateprojectqcdeilog"
      width="50%"
      append-to-body
    >
      <span class="addprojectSpan">修改业务期次</span>
      <el-divider></el-divider>
      <div>
        <el-form
          ref="form"
          :model="addprojectform"
          :rules="rules"
          label-width="40%"
        >
          <el-form-item label="业务期次类型">
            <el-radio-group v-model="addprojectform.term">
              <el-radio label="0">整月</el-radio>
              <el-radio label="1">非整月</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="addprojectform.term == 0" label="期次所属月份">
            <el-date-picker
              v-model="addprojectform.termMonth"
              type="month"
              value-format="yyyy-MM"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item
            v-if="addprojectform.term == 1"
            label="期次所属时间范围"
          >
            <el-date-picker
              v-model="selectDate"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div style="text-align: center">
          <el-button size="large" type="primary" @click="submitUpdateForm()"
            >确 定</el-button
          >
          <el-button size="large" @click="closeUpdate()">取 消</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      :title="title"
      :visible.sync="xiugaifanfeideilog"
      width="60%"
      append-to-body
    >
      <span class="addprojectSpan">业务期次：{{ this.yewuqicideilog }}</span>
      <el-divider></el-divider>
      <div style="width: 100%; height: 60%">
        <span class="lurushouruspan">收入 ￥：</span
        ><el-input
          :disabled="true"
          v-model="lrdeshouru"
          style="width: 200px"
        ></el-input
        ><br />
        <div style="width: 100%; height: 20px"></div>
        <div>
          <el-form
            :model="fanfei"
            label-width="40px"
            :inline="true"
            size="small"
            label-position="center"
          >
            <el-table :data="fanfei.tableData" :row-class-name="rowClassName">
              <el-table-column label="出信息费公司" min-width="50">
                <template slot-scope="scope">
                  <el-form-item
                    :prop="'tableData.' + scope.$index + '.custName'"
                  >
                    <el-input v-model="scope.row.custName"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="信息费公司" min-width="50">
                <template slot-scope="scope">
                  <el-form-item
                    :prop="'tableData.' + scope.$index + '.feeCustName'"
                  >
                    <el-select
                      :prop="'tableData.' + scope.$index + '.feeCustName'"
                      v-model="scope.row.feeCustName"
                      filterable
                      placeholder="请选择信息费公司"
                      collapse-tags
                      clearable
                    >
                      <el-option
                        v-for="(item1, index) in feecustNameAndfeilvList"
                        :key="item1.custName"
                        :value="item1.custName"
                        @click.native="indexSelect(item1, index, scope.$index)"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="计算方式" min-width="50">
                <template slot-scope="scope">
                  <el-form-item
                    :prop="'tableData.' + scope.$index + '.calculateType'"
                  >
                    <el-select
                      :prop="'tableData.' + scope.$index + '.calculateType'"
                      v-model="scope.row.calculateType"
                      @change="updafangshi(scope.$index)"
                      filterable
                      placeholder="请选择计算方式"
                      collapse-tags
                      clearable
                    >
                      <el-option
                        v-for="item1 in calculateTypes"
                        :key="item1.dictValue"
                        :label="item1.dictLabel"
                        :value="item1.dictValue"
                        :disabled="item1.disabled"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="信息费金额" min-width="50">
                <template slot-scope="scope">
                  <el-form-item :prop="'tableData.' + scope.$index + '.feeAmt'">
                    <el-input
                      type="number"
                      :disabled="scope.row.status"
                      v-model="scope.row.feeAmt"
                      @blur="onblurfanfei(scope.$index)"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column label="提成信息费金额" min-width="50">
                <template slot-scope="scope">
                  <el-form-item
                    :prop="'tableData.' + scope.$index + '.feeAmt2'"
                  >
                    <el-input
                      type="number"
                      :disabled="scope.row.status"
                      v-model="scope.row.feeAmt2"
                      @blur="onblurtc(scope.$index)"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column fixed="right" label="操作" width="100px">
                <template slot-scope="scope">
                  <el-button
                    size="large"
                    v-if="scope.$index > 0"
                    icon="el-icon-delete"
                    type="primary"
                    @click="rowDelete(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-form>
          <div style="margin-top: 10px">
            <el-button size="large" @click="onAdd()" type="primary"
              >+ 添加公司</el-button
            >
            <span style="font-size: 12px; color: #cccccc"
              >若找不到信息费公司，请在编辑界面进行添加</span
            >
          </div>
        </div>
        <el-divider></el-divider>

        <div
          style="
            display: flex;
            width: 100%;
            height: 50px;
            text-align: center;
            flex-wrap: wrap;
            flex-direction: row;
          "
        >
          <div style="width: 23%; height: 50px; text-align: left">
            <span class="lrffdivspan1">合计</span>
          </div>
          <div style="width: 23%; height: 50px"></div>
          <div style="width: 23%; height: 50px">
            <span class="lrffdivspan1">￥：{{ this.fanfeicount }}</span
            ><br />
          </div>
          <div style="width: 23%; height: 50px">
            <span class="lrffdivspan1">￥：{{ this.tichengfanfeicount }}</span
            ><br />
          </div>
        </div>
        <div>
          <el-divider></el-divider>
        </div>

        <div
          style="
            display: flex;
            width: 100%;
            height: 60px;
            flex-wrap: wrap;
            flex-direction: row;
          "
        >
          <div style="width: 25%; height: 100px">
            <span class="lrffdivspan1">毛利 ￥：{{ this.maolijine }}</span
            ><br />
            <span class="lrffdivspan2">毛利= 收入 - 信息费金额合计</span>
          </div>
          <div style="width: 25%; height: 100px">
            <span class="lrffdivspan1"
              >提成毛利 ￥：{{ this.tichengmaolijine }}</span
            ><br />
            <span class="lrffdivspan2">提成毛利= 收入 - 提成信息费金额合计</span
            ><br />
          </div>
          <div style="width: 25%; height: 100px"></div>
          <div style="width: 25%; height: 100px"></div>
        </div>
        <div style="width: 100%; height: 50px; text-align: center"></div>
        <span class="lurushouruspan2">备注信息</span><br />
        <br />
        <br />
        <el-input
          type="textarea"
          :rows="3"
          style="width: 600px"
          placeholder="请输入内容"
          v-model="lrremark"
        >
        </el-input>
      </div>
      <div style="width: 100%; height: 50px; text-align: center"></div>
      <div style="width: 100%; height: 100px; text-align: center">
        <el-button size="large" type="primary" @click="submitupdateff()"
          >保 存</el-button
        >
        <el-button size="large" @click="closeupdateff()">取 消</el-button><br />
        <span class="lrffdivspan2"
          >当业务人员完成信息费确认后，将无法修改金额</span
        >
      </div>
    </el-dialog>

    <el-dialog
      :title="title"
      :visible.sync="querenffdeilog"
      width="30%"
      append-to-body
    >
      <span class="addprojectSpan123">确认金额正确</span>
      <!--            <div style="width: 100%;height:30px;"></div>-->
      <div style="width: 100%; height: 60px; margin-top: 1%">
        <span style="font-size: 16px; margin-left: 6px"
          >如果金额正确，点击确定后本期次完结</span
        ><br />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" @click="quxiaofanfei()">取 消</el-button>
        <el-button size="large" type="primary" @click="querenfanfei()"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      :title="title"
      :visible.sync="dengjidakuandeilog"
      width="65%"
      append-to-body
    >
      <span class="lrffdivspan2">登记打款信息</span>
      <el-divider></el-divider>
      <div style="width: 100%; height: 60%">
        <span>出信息费公司：{{ dengjidakuanCustName }}</span>
        <span style="margin-left: 35px"
          >信息费公司：{{ dengjidakuanFeeCustName }}</span
        >
        <div style="width: 100%; height: 20px"></div>
        <div>
          <el-form
            ref="dengjidakuanform"
            :model="dengjidakuanform"
            label-width="100px"
          >
            <div v-for="(domain, index) in dengjidakuanform.tableData">
              <el-row>
                <el-col :span="7">
                  <el-form-item
                    :label="'打款日期：'"
                    :rules="[
                      {
                        required: true,
                        message: '请选择打款日期',
                        trigger: ['blur', 'change'],
                      },
                    ]"
                    :key="domain.key"
                    :prop="'tableData.' + index + '.payDate'"
                  >
                    <el-date-picker
                      v-model="domain.payDate"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <span style="margin-left: 20px">实际打款金额：</span
                  ><el-input
                    type="number"
                    @blur="onblurPayAmt(index)"
                    v-model="domain.payAmt"
                    style="width: 30%"
                  ></el-input
                  ><span>元</span>
                </el-col>
                <el-col :span="7">
                  <span style="margin-left: 20px">抹平差额：</span
                  ><el-input
                    type="number"
                    @blur="onblurPayAmt(index)"
                    v-model="domain.differenceAmt"
                    style="width: 30%"
                  >
                  </el-input
                  ><span>元</span>
                </el-col>
                <el-col :span="1">
                  <el-button
                    size="large"
                    v-if="index > 0"
                    style="margin-left: 20px"
                    type="primary"
                    @click.prevent="removeDomain(domain)"
                    >-删除</el-button
                  >
                </el-col>
              </el-row>
            </div>
            <el-form-item>
              <el-button size="large" type="primary" @click="addDomain()">
                + 添加打款记录</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div style="width: 100%; height: 50px; text-align: center"></div>
        <div
          style="
            display: flex;
            width: 100%;
            height: 60px;
            flex-wrap: wrap;
            flex-direction: row;
          "
        >
          <div style="width: 25%; height: 100px">
            <span class="lrffdivspan2"
              >信息费总金额：{{ formaterMoney(this.dakuanffzje) }} 元</span
            ><br />
            <span class="lrffdivspan2"
              >已完成打款：{{ formaterMoney(this.ywcdakuan) }} 元</span
            ><br />
            <span class="lrffdivspan2"
              >未完成打款：{{ formaterMoney(this.wwcdakuan) }} 元</span
            ><br />
          </div>
          <div style="width: 25%; height: 100px"></div>
          <div style="width: 25%; height: 100px"></div>
          <div style="width: 25%; height: 100px"></div>
        </div>
        <div style="width: 100%; height: 50px; text-align: center"></div>
        <span class="lurushouruspan2">备注信息</span><br />
        <br />
        <br />
        <el-input
          type="textarea"
          :rows="3"
          style="width: 600px"
          placeholder="请输入内容"
          v-model="lrremark"
        >
        </el-input>
      </div>
      <div style="width: 100%; height: 50px; text-align: center"></div>
      <div style="width: 100%; height: 100px; text-align: center">
        <el-button size="large" type="primary" @click="submitdjdk()"
          >提 交</el-button
        >
        <el-button size="large" @click="closedjdk()">取 消</el-button><br />
      </div>
    </el-dialog>
    <el-dialog
      :title="title"
      :visible.sync="dkywcdeilog"
      width="30%"
      append-to-body
    >
      <span class="addprojectSpan">打款已完成</span>
      <div style="width: 100%; height: 30px"></div>
      <div style="width: 100%; height: 60px">
        <span style="font-size: 16px; margin-left: 35px"
          >是否确认本期次的信息费打款工作均已完成？</span
        ><br />
        <span style="font-size: 16px; margin-left: 35px"
          >确定后本期次信息费工作流程将会结束</span
        ><br />
      </div>

      <div style="width: 100%; height: 50px; text-align: center">
        <el-button size="large" type="primary" @click="qrdkwcan()"
          >提 交</el-button
        >
        <el-button size="large" @click="quxiaoqrdk()">取 消</el-button><br />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { checkPermi } from "@/utils/permission"; // 权限判断函数

import {
  deleteincomeForLaw,
  getloginexport,
  getUserList,
  querendakuanForLaw,
  getDeatil,
  getyeuwList,
  submitLawdakuanData,
  getdakuanTableForLaw,
  querenIncomeAndFeeForLaw,
  updatefanfei,
  getfanfeidateil,
  lurufanfeiadd,
  updataYewuqici,
  getLawProjectqicideteils,
  getLawProjectdeteils,
  addyewuqiciForLaw,
  lrsrsubmit,
  submitshouru,
  updateyewufuze,
  getuser,
  closeProject,
  getyewuxiangqingLaw,
  getupdateyewuqiciByid,
  projectShowLawxiala,
  addIncomeForLaw,
  getyewuxiangqing,
  getProjectdeteils,
  findLawRecentlyCustFeeCompanyAndFeeCompany,
  findLawSuspendFlagIsOneSum,
  changePhase,
  getProjectqicideteils,
  getLawProjectSumByProjectId,
  getProjectCertificateFlagByProjectId,
  revocationReplaceFeeCompanyInfo,
  getFlowAlreadyPaylnfoFromOA,
  createVoucher,
  getUserRoleByProjectId,
} from "@/api/caiwu/project";
import fclct from "@/assets/images/cwxmglfcprocess.png";
import { get } from "http";
import IFrame from "@/components/iFrame";
export default {
  name: "ProjectDetailsLaw",
  inject: ["reload"],
  data() {
    return {
      lctableData: [],
      jztableData: [
        {
          zjsx: "担保费收入",
          zy: "项目名称+期次+担保费收入",
          jfkm: "1",
          jfje: "1",
          dfkm: "1",
          dfje: 1,
        },
        {
          zjsx: "信息费",
          zy: "项目名称+日期+应支付信息费",
          jfkm: "2",
          jfje: "2",
          dfkm: "2",
          dfje: 2,
        },
      ],
      //进度条下的出信息费公司
      feeCustNameProcess: null,
      //进度条下的信息费公司
      custNameProcess: null,
      //期次未完结
      phaseNoFinish: null,
      //收款时间
      collectionTime: null,
      collectionTimeString: null,
      //修改信息费标识 - 0 =》 新增 ， 1 =》 修改
      changeFlag: 0,
      nextToFeeInfoFlag: false,
      //挂起信息费清除后显示的红字
      guaqiFeeAmtFlag: false,
      //录入的时候显示的最小的红字
      feeAmtRedFlag: false,
      //期次id，用于本期次下的收入入库
      phaseId: null,
      //全局收入
      // incomeAmtQJ: 0.00,
      //全局二级收入
      incomeAmtSecondQJ: 0.0,
      //录入收入与信息费表单
      incomeAndFeeForm: {
        incomeAndFeeList: [],
      },
      alreadyPayList: [],
      //信息费公司表单参数
      feeCompanyForm: {
        //信息费公司表id
        custId: null,
        //出信息费公司
        custName: "",
        //信息费公司
        feeCustName: "",
        rate: "",
      },
      //收入合计
      shouruheji: 0.0,
      //真实回款金额合计
      zhenshihuankuanheji: 0.0,
      //服务商金额合计
      fuwushangheji: 0.0,
      //本金金额合计
      benjinheji: 0.0,
      //信息费金额合计
      fanfeiheji: 0.0,
      //本期信息费金额合计
      benqifanfeiheji: 0.0,
      //信息费取整金额合计
      fanfeiquzhengheji: 0.0,
      //提成信息费金额合计
      tichengfanfeiheji: 0.0,
      //todo 财务项目管理四期 借条分润合计
      jtfrheji: 0.0,
      //todo 财务项目管理四期 法催利润合计
      facuilirunheji: 0.0,
      yijifuwushangList: [],
      erjifuwushangList: [],
      // 录入收入表单参数
      shouruForm: {
        fuwushang: [
          {
            hebingId: 0,
            yijifuwushangName: "",
            yijifuwushangIncome: null,
            erjifuwushangShow: false,
            erjifuwushangSelect: [
              {
                value: "0",
                label: "无",
              },
              {
                value: "1",
                label: "有",
              },
            ],
            valuetype: "0",

            erjifuwushang: [
              {
                hebingId: 0,
                erjifuwushangName: null,
                erjifuwushangIncome: null,
              },
            ],
          },
        ],
      },
      //删除期次deilog
      deleteincomedeilog: false,
      //备注详情deliog
      remarkDeliog: false,
      //接收备注详情的对象
      remarkObj: null,
      //是否提交打款
      issubmit: false,
      //必填校验
      bitianjiaoyan: false,
      jinyong: true,
      //列表合计
      feeAmtSum: null,
      feeAmt2Sum: null,
      payAmtSum: null,
      differenceAmtSum: null,
      feeAlreadySum: null,
      feeNoAlreadySum: null,
      incomeSum: null,
      grossProfitAmtSum: null,
      grossProfitAmt2Sum: null,
      feeRoundSum: null,
      currentFeeSum: null,
      principalSum: null,
      serviceFeeSum: null,
      trueComeAmtSum: null,
      jtfrAmtSum: null,
      lawProfitSum: null,
      //小表格合计
      smaIncomeAmtSum: null,
      smafeeAmtSum: null,
      smafeeRoundSum: null,
      smafeeAmt2Sum: null,
      smaCurrentFeeSum: null,
      smaJtfrAmtSum: null,
      smaLawProfitSum: null,
      //毛利列表list
      maoliList: [],
      //收入List，
      shouruList: [],
      //进度条
      jindutiao: 0,
      //进度条展示
      jindutiaozhansh: false,
      //进度条div展示
      jindutiaoshow: true,
      //业务期次下拉框
      yewuqiciList: [],
      //项目id
      projectId: "",
      //信息费表id
      feeId: "",
      projectIncomeId: null,
      //信息费提交
      fanfeijiaoyan: true,
      //打款已完成按钮
      dkywc: true,
      //确认打款已完成deilog
      dkywcdeilog: false,

      //打款信息费总金额
      dakuanffzje: "",
      //已完成打款
      ywcdakuan: "",
      //未完成打款
      wwcdakuan: "",
      //打款form表单
      dengjidakuanform: {
        tableData: [
          {
            payDate: "",
            payAmt: "",
            differenceAmt: 0,
            key: "",
          },
        ],
      },
      //登记打款信息费公司
      dengjidakuanCustName: "测试登记打款饭费公司",
      //登记打款出饭费公司
      dengjidakuanFeeCustName: "测试登记打款出信息费公司",
      //登记打款列表数据
      djdkList: [],
      projectData: null,
      //登记打款列表
      dengjidakuantable: false,
      //登记打款deilog
      dengjidakuandeilog: false,
      //确认信息费deilog
      querenffdeilog: false,
      //录入信息费展示毛利
      maolijine: 0.0,
      tichengmaolijine: 0.0,
      //录入信息费计算方式
      calculateTypes: [
        { dictValue: "0", dictLabel: "自动", disabled: false },
        { dictValue: "1", dictLabel: "手动" },
      ],

      //录入信息费出信息费公司列表
      feeCustNames: [],
      replaceCompanyInfo: [],

      //录入信息费deilog
      lurufanfeideilog: false,
      //修改录入信息费deilog
      xiugaifanfeideilog: false,
      sumData: null,
      //录入信息费动态表单
      fanfei: {
        tableData: [
          {
            custId: "",
            custName: "",
            feeCustName: "",
            calculateType: "",
            feeAmt: "",
            feeAmt2: "",
            status: false,
          },
        ],
      },
      //修改业务期次
      updateprojectqcdeilog: false,
      //确认收入
      querensrdeilog: false,
      //收入
      lrdeshouru: "",
      //录入收入的备注
      lrremark: "",
      //传参null对象
      returnData: {},
      //录入展示业务期次
      yewuqicideilog: "",
      //录入deilog
      lurudeilog: false,
      //修改录入
      updatelurudeilog: false,
      //录入收入日期
      lrsrdate: "",
      //录入收入代办人
      lrsrdaibanren: "",
      //确认收入日期
      qrsrdate: "",
      //确认收入代办人
      qrsrdaibanren: "",
      //录入信息费日期
      lrffdate: "",
      //录入信息费代办人
      lrffdaibanren: "",
      //确认信息费日期
      qrffdate: "",
      //确认信息费代办人
      qrffdaibanren: "",
      //出纳打款日期
      cndkdate: "",
      //出纳打款代办人
      cndkdaibanren: "",

      //打款参数
      dakuancanshudata: [],
      //业务期次展示
      yewuqici: "",
      //终止项目deilog
      abortprojectdeilog: false,
      //修改业务人员
      updateyewu: "",
      //修改业务负责人deliog
      // updateproyewudel:false,
      //图片
      inmage: "",
      //选择日期范围数组
      selectDate: [],
      //incomeid
      returnincomeid: null,
      //新增业务期次form
      addprojectform: {
        id: null,
        projectId: null,
        term: "0",
        termMonth: "",
        termBegin: "",
        termEnd: "",
      },
      //tabs标签默认展示
      activeName: "first",
      //
      projectName: "",
      custName: "",
      incomeCustName: "",
      //固定数值展示
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 财务项目管理主表格数据
      overDetailBodyDetailList: [],
      overDetailBodyDetailList_v2: [],
      merageArr1: [],
      meragePos1: 0,
      merageArr2: [],
      meragePos2: 0,
      //进度条中表格
      overDetailBodyDetailList1: [],
      //步骤条数据
      buzhoutiaoList: [],
      //信息费公司与费率
      feecustNameAndfeilvList: [],
      //信息费合计
      fanfeicount: 0.0,
      //提成信息费合计
      tichengfanfeicount: 0.0,
      //用户列表
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //会计用户集合
      kuaijiList: [],
      //出纳用户集合
      chunaList: [],
      //业务用户集合
      yewuList: [],
      //incomeid
      incomeproId: "",
      // 查询参数
      queryParams: {
        dateRange: [],
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        custName: null,
        incomeCustName: null,
        projectFlag: null,
        status: null,
        createTime: null,
        updateTime: null,
      },
      //选中的信息费公司费率和税率
      selectRate: "",
      selectTax: "",
      //代办人列表
      dbchuna: [],
      dbyewu: [],
      dbkuaiji: [],
      dbcnandkj: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        custName: [
          { required: true, message: "担保公司不能为空", trigger: "blur" },
        ],
        incomeCustName: [
          { required: true, message: "汇款公司不能为空", trigger: "blur" },
        ],
        projectFlag: [
          {
            required: true,
            message: "项目状态：0正常 1终止不能为空",
            trigger: "blur",
          },
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" },
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" },
        ],
      },
      //登录人是否有导出权限
      isexportRole: "",
      //二级服务商选择器
      // erjifuwushangSelect: [
      //   {
      //     value: '0',
      //     label: '无'
      //   },
      //   {
      //     value: '1',
      //     label: '有'
      //   },
      // ],
      // valuetype: '0'
      hasRoleList: [],
    };
  },
  created() {
    if (this.$route.query.tabs == 2) {
      this.activeName = "second";
    }
    if (this.$route.query.incomeId) {
      this.phaseId = this.$route.query.incomeId;
    }
    this.getUserRoleByProjectId();
    this.getxiala();

    this.getprojectDetails();
    this.getdaibanUserList();
    this.checkExportRole();
  },
  computed: {
    leaveDataNew() {
      return JSON.parse(JSON.stringify(this.feeCompanyForm)); //watch监听要求
    },
  },
  watch: {
    yewuqici: function (val) {
      if (val == null || val == "" || val == undefined) {
        this.jindutiaozhansh = false;
        this.yewuqici = -999;
      }
    },
    leaveDataNew: {
      handler(newValue, oldValue) {
        this.custIdHuoQu();
        // for (let i = 0; i < newValue.length; i++) {
        //   if (oldValue[i].feeCustName != newValue[i].feeCustName) {
        //     this.custIdHuoQu()
        //     console.log('321321321321')
        //     break;
        //   }
        // }
      },
      immediate: false,
      deep: true,
    },
  },
  methods: {
    checkPermi,
    getUserRoleByProjectId() {
      getUserRoleByProjectId({ projectId: this.$route.query.productId }).then(
        (res) => {
          this.hasRoleList = res.hasRoleList;
          console.log(this.hasRoleList, " this.hasRoleList");
        }
      );
    },
    //确认删除期次
    async submitdeleteincome() {
      var project = {
        id: this.yewuqici,
      };
      const { isok } = await deleteincomeForLaw(project);
      if (isok) {
        this.reload();
      }
    },
    //取消删除期次
    cencaldeleteincome() {
      this.deleteincomedeilog = false;
    },
    //查询当前登录人是否具有导出权限

    deleteincome() {
      this.deleteincomedeilog = true;
    },

    async checkExportRole() {
      var project = {
        id: this.projectId,
      };
      await getloginexport(project).then((response) => {
        this.isexportRole = response.isOk;
      });
    },

    // 要合并的数组的方法
    merage(tableData) {
      this.merageInit();
      for (var i = 0; i < tableData.length; i++) {
        if (i === 0) {
          // 第一行正常显示 必须存在
          this.merageArr1.push(1);
          this.meragePos1 = 0;
          this.merageArr2.push(1);
          this.meragePos2 = 0;
        } else {
          // 判断当前元素与上一个元素是否相同 根据是否合并的id
          if (
            typeof tableData[i].phaseId !== "undefined" &&
            tableData[i].phaseId != null &&
            tableData[i].phaseId !== "" &&
            tableData[i].phaseId === tableData[i - 1].phaseId
          ) {
            this.merageArr1[this.meragePos1] += 1;
            this.merageArr1.push(0);
          } else {
            this.merageArr1.push(1);
            this.meragePos1 = i;
          }

          if (
            typeof tableData[i].projectFeeId !== "undefined" &&
            tableData[i].projectFeeId != null &&
            tableData[i].projectFeeId !== "" &&
            tableData[i].projectFeeId === tableData[i - 1].projectFeeId
          ) {
            this.merageArr2[this.meragePos2] += 1;
            this.merageArr2.push(0);
          } else {
            this.merageArr2.push(1);
            this.meragePos2 = i;
          }
        }
      }
    },
    merageInit() {
      this.merageArr1 = [];
      this.meragePos1 = 0;
      this.merageArr2 = [];
      this.meragePos2 = 0;
    },
    //合并单元格
    overDetailBodyDetailList_v2panMethod({
      row,
      column,
      rowIndex,
      columnIndex,
    }) {
      if (
        columnIndex === 0 ||
        columnIndex === 1 ||
        columnIndex === 6 ||
        columnIndex === 7 ||
        columnIndex === 18 ||
        columnIndex === 19 ||
        columnIndex === 20 ||
        columnIndex === 25
      ) {
        const _row = this.merageArr1[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }

      if (
        columnIndex === 2 ||
        columnIndex === 3 ||
        columnIndex === 4 ||
        columnIndex === 5 ||
        columnIndex === 6 ||
        columnIndex === 7 ||
        columnIndex === 8 ||
        columnIndex === 9 ||
        columnIndex === 10 ||
        columnIndex === 11 ||
        columnIndex === 12 ||
        columnIndex === 13 ||
        columnIndex === 14 ||
        columnIndex === 15 ||
        columnIndex === 16 ||
        columnIndex === 17
      ) {
        const _row = this.merageArr2[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    //合并毛利和提成毛利
    hebingmaoliAndtichengmaoli({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 7 || columnIndex === 8) {
        if (rowIndex === 0) {
          return {
            rowspan: 99999,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    //获取角色
    getdaibanUserList() {
      var getuser = {
        id: this.projectId,
      };
      getUserList(getuser).then((response) => {
        this.dbchuna = response.chunaList;
        this.dbkuaiji = response.kuaijiList;
        // this.dbyewu = response.yewuList
        this.dbcnandkj = response.cnandkjList;
      });
    },
    //获取业务
    getuserList() {
      getuser().then((response) => {
        // this.yewuList = response.yewu;
        this.kuaijiList = response.kuaiji;
        this.chunaList = response.chuna;
      });
    },
    onblurPayAmt(index) {
      if (this.dengjidakuanform.tableData[index].payAmt == "") {
        this.dengjidakuanform.tableData[index].payAmt = "0";
      }
      if (this.dengjidakuanform.tableData[index].differenceAmt == "") {
        this.dengjidakuanform.tableData[index].differenceAmt = "0";
      }
      this.onblurfeeAmt();
    },
    onblurfeeAmt() {
      //校验计数
      var a = 0;
      //已完成打款
      //打款总金额
      var allAmt = 0;

      for (var i = 0; i < this.dengjidakuanform.tableData.length; i++) {
        if (this.dengjidakuanform.tableData[i].differenceAmt == "") {
          this.dengjidakuanform.tableData[i].differenceAmt = 0;
          allAmt = (
            parseFloat(allAmt) +
            parseFloat(+this.dengjidakuanform.tableData[i].payAmt) +
            parseFloat("0")
          ).toFixed(2);
        } else {
          allAmt = (
            parseFloat(allAmt) +
            parseFloat(this.dengjidakuanform.tableData[i].payAmt) +
            parseFloat(this.dengjidakuanform.tableData[i].differenceAmt)
          ).toFixed(2);
        }
        if (
          this.dengjidakuanform.tableData[i].payDate == "" ||
          this.dengjidakuanform.tableData[i].payDate == undefined ||
          this.dengjidakuanform.tableData[i].payDate == null ||
          this.dengjidakuanform.tableData[i].payAmt == "" ||
          this.dengjidakuanform.tableData[i].payAmt == undefined ||
          this.dengjidakuanform.tableData[i].payAmt == null ||
          Number(this.dengjidakuanform.tableData[i].payAmt) <= 0
        ) {
          a++;
          // this.bitianjiaoyan = false
        }
        // else{
        //   this.bitianjiaoyan = true
        // }
      }
      if (a > 0) {
        this.bitianjiaoyan = false;
      } else {
        this.bitianjiaoyan = true;
      }
      // allAmt = parseFloat(allAmt)+parseFloat( this.ywcdakuan)

      if (allAmt > this.dakuanffzje) {
        alert("当前打款总计超过信息费总计！！！请检查打款信息");
        this.issubmit = false;
      } else {
        this.issubmit = true;
      }
    },
    //失焦事件提成信息费
    onblurtc(index) {
      if (this.fanfei.tableData[index].feeAmt == "") {
        this.fanfei.tableData[index].feeAmt = 0;
      }
      if (this.fanfei.tableData[index].feeAmt2 == "") {
        this.fanfei.tableData[index].feeAmt2 = 0;
      }
      //本行的信息费和提成信息费加到总计
      this.tichengfanfeicount = 0;
      this.tichengmaolijine = 0;
      for (var i = 0; i < this.fanfei.tableData.length; i++) {
        //提成信息费合计
        this.tichengfanfeicount = (
          parseFloat(this.tichengfanfeicount) +
          parseFloat(this.fanfei.tableData[i].feeAmt2)
        ).toFixed(2);
        //提成毛利
        this.tichengmaolijine = (
          parseFloat(this.lrdeshouru) - parseFloat(this.tichengfanfeicount)
        ).toFixed(2);
      }
    },
    //失焦事件信息费
    onblurfanfei(index) {
      if (this.fanfei.tableData[index].feeAmt == "") {
        this.fanfei.tableData[index].feeAmt = 0;
      }
      if (this.fanfei.tableData[index].feeAmt2 == "") {
        this.fanfei.tableData[index].feeAmt2 = 0;
      }
      this.fanfeicount = 0;
      this.maolijine = 0;
      for (var i = 0; i < this.fanfei.tableData.length; i++) {
        //本行的信息费和提成信息费加到总计
        this.fanfeicount = (
          parseFloat(this.fanfeicount) +
          parseFloat(this.fanfei.tableData[i].feeAmt)
        ).toFixed(2);

        //毛利
        this.maolijine = (
          parseFloat(this.lrdeshouru) - parseFloat(this.fanfeicount)
        ).toFixed(2);
      }
    },

    toFixedFun(data, len) {
      // debugger
      const number = Number(data);
      if (isNaN(number) || number >= Math.pow(10, 21)) {
        return number.toString();
      }
      if (typeof len === "undefined" || len === 0) {
        return Math.round(number).toString();
      }
      let result = number.toString();
      const numberArr = result.split(".");

      if (numberArr.length < 2) {
        // 整数的情况
        return padNum(result);
      }
      const intNum = numberArr[0]; // 整数部分
      const deciNum = numberArr[1]; // 小数部分
      const lastNum = deciNum.substr(len, 1); // 最后一个数字

      if (deciNum.length === len) {
        // 需要截取的长度等于当前长度
        return result;
      }
      if (deciNum.length < len) {
        // 需要截取的长度大于当前长度 1.3.toFixed(2)
        return padNum(result);
      }
      // 需要截取的长度小于当前长度，需要判断最后一位数字
      result = `${intNum}.${deciNum.substr(0, len)}`;
      if (parseInt(lastNum, 10) >= 5) {
        // 最后一位数字大于5，要进位
        const times = Math.pow(10, len); // 需要放大的倍数
        let changedInt = Number(result.replace(".", "")); // 截取后转为整数
        changedInt++; // 整数进位
        changedInt /= times; // 整数转为小数，注：有可能还是整数
        result = padNum(`${changedInt}`);
      }
      return result;
      // 对数字末尾加0
      function padNum(num) {
        const dotPos = num.indexOf(".");
        if (dotPos === -1) {
          // 整数的情况
          num += ".";
          for (let i = 0; i < len; i++) {
            num += "0";
          }
          return num;
        } else {
          // 小数的情况
          const need = len - (num.length - dotPos - 1);
          for (let j = 0; j < need; j++) {
            num += "0";
          }
          return num;
        }
      }
    },

    //改变输入方法
    updafangshi(index) {
      //信息费金额 = 收入 ×费率% ×（1-税率%）

      if (
        this.selectRate == "" &&
        this.fanfei.tableData[index].calculateType == "0"
      ) {
        this.$message({
          message: "请先选择信息费公司！",
          type: "warning",
        });
      } else {
        // 计算信息费金额和提成信息费金额
        if (this.fanfei.tableData[index].calculateType == "0") {
          this.fanfei.tableData[index].status = true;
          //信息费金额 = 收入 ×费率% ×（1-税率%）
          var a = 1 - parseFloat(this.selectTax) / 100;
          var amtCount =
            parseFloat(this.lrdeshouru) *
            (parseFloat(this.selectRate) / 100) *
            a;

          this.fanfei.tableData[index].feeAmt = this.toFixedFun(amtCount, 2);

          //提成信息费金额 = 收入 ×费率%
          var tcfanfeicount =
            parseFloat(this.lrdeshouru) * (parseFloat(this.selectRate) / 100);
          this.fanfei.tableData[index].feeAmt2 = this.toFixedFun(
            tcfanfeicount,
            2
          );
          this.onblurfanfei(index);
          this.onblurtc(index);
          //   this.fanfeicount = (parseFloat(this.fanfeicount)+parseFloat(amtCount)).toFixed(2);
          //       //提成信息费合计
          // this.tichengfanfeicount = (parseFloat(this.tichengfanfeicount)+parseFloat(tcfanfeicount)).toFixed(2);
          //     //毛利
          //     this.maolijine = (parseFloat(this.lrdeshouru)-parseFloat(this.fanfeicount)).toFixed(2);
          //     //提成毛利
          //     this.tichengmaolijine = (parseFloat(this.lrdeshouru)-parseFloat(this.tichengfanfeicount)).toFixed(2);
        } else {
          this.fanfei.tableData[index].status = false;
          this.fanfei.tableData[index].feeAmt = 0;
          this.fanfei.tableData[index].feeAmt2 = 0;
        }
      }
    },
    //得到选择的信息费公司的税率和费率

    indexSelect(e, eindex, index) {
      //置空当前行的计算方式 信息费金额  提成信息费金额

      // this.jinyong = false
      (this.selectRate = e.rate), (this.selectTax = e.taxRate);
      this.fanfei.tableData[index].custId = e.id;
      if (this.selectRate == "未设置") {
        this.calculateTypes[0].disabled = true;
      } else {
        this.calculateTypes[0].disabled = false;
        this.fanfei.tableData[index].calculateType = "0";
      }
      if (this.selectTax == "未设置") {
        this.selectTax = 0;
      }

      if (this.fanfei.tableData[index].calculateType == "0") {
        this.updafangshi(index);
      }
    },
    //计算数据
    // filters: {
    //   keepTwoNum(value){
    //     value = Number(value);
    //     return value.toFixed(2)
    //   }
    // },
    // //表格中的金额合计总价格
    // sumMoney(){
    // 		return this.tableData.map(
    // 			row=>row.number*row.price).reduce(
    // 			(acc, cur) => (parseFloat(cur) + acc), 0)
    //   },
    //   //所有费用合计
    // sums:function () {
    // 		return parseFloat(this.freight)+parseFloat(this.other) +this.sumMoney
    // 			},

    /** 格式化金额 */
    formaterMoney(data) {
      if (data === null || data === "") return "";
      if (data === "0.00") return data;
      if (data === 0) return "0.00";
      if (!data) return "-";
      if (data === "-") return "-";
      if (data === "您的业务流程有误，请再次核对！") return "数据错误！";
      // 将数据分割，保留两位小数
      data = data.toFixed(2);
      // 获取整数部分
      const intPart = Math.trunc(data);
      // 整数部分处理，增加,
      const intPartFormat = intPart
        .toString()
        .replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
      // 预定义小数部分
      let floatPart = ".00";
      // 将数据分割为小数部分和整数部分
      const newArr = data.toString().split(".");
      if (newArr.length === 2) {
        // 有小数部分
        floatPart = newArr[1].toString(); // 取得小数部分
        if (1 / intPart < 0 && intPart === 0) {
          return "-" + intPartFormat + "." + floatPart;
        }
        return intPartFormat + "." + floatPart;
      }
      if (1 / intPart < 0 && intPart === 0) {
        return "-" + intPartFormat + "." + floatPart;
      }
      return intPartFormat + floatPart;
    },
    getSummaries01(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }

        if (index === 3) {
          sums[index] = this.formaterMoney(Number(this.incomeSum));
        } else if (index === 8) {
          sums[index] = this.formaterMoney(Number(this.trueComeAmtSum));
        } else if (index === 9) {
          sums[index] = this.formaterMoney(Number(this.serviceFeeSum));
        } else if (index === 10) {
          sums[index] = this.formaterMoney(Number(this.principalSum));
        } else if (index === 11) {
          sums[index] = this.formaterMoney(Number(this.currentFeeSum));
        } else if (index === 14) {
          sums[index] = this.formaterMoney(Number(this.feeAmtSum));
        } else if (index === 15) {
          sums[index] = this.formaterMoney(Number(this.feeRoundSum));
        } else if (index === 16) {
          sums[index] = this.formaterMoney(Number(this.jtfrAmtSum));
        } else if (index === 17) {
          sums[index] = this.formaterMoney(Number(this.lawProfitSum));
        } else if (index === 18) {
          sums[index] = this.formaterMoney(Number(this.feeAlreadySum));
        } else if (index === 19) {
          sums[index] = this.formaterMoney(Number(this.feeNoAlreadySum));
        } else if (index === 23) {
          sums[index] = this.formaterMoney(Number(this.payAmtSum));
        } else if (index === 24) {
          sums[index] = this.formaterMoney(Number(this.differenceAmtSum));
        }
      });
      return sums;
    },

    getSummaries02(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "总计";
          return;
        }

        if (index === 1) {
          sums[index] = this.formaterMoney(Number(this.smaIncomeAmtSum));
        } else if (index === 2) {
          //todo 财务项目管理四期 本期信息费
          sums[index] = this.formaterMoney(Number(this.smaCurrentFeeSum));
        } else if (index === 4) {
          //todo 财务项目管理四期 信息费
          sums[index] = this.formaterMoney(Number(this.smafeeAmtSum));
        } else if (index === 5) {
          //todo 财务项目管理四期 信息费取整
          sums[index] = this.formaterMoney(Number(this.smafeeRoundSum));
        } else if (index === 6) {
          //todo 财务项目管理四期 借条分润
          sums[index] = this.formaterMoney(Number(this.smaJtfrAmtSum));
        } else if (index === 7) {
          //todo 财务项目管理四期 法催利润
          sums[index] = this.formaterMoney(Number(this.smaLawProfitSum));
        }
      });
      return sums;
    },

    //确认打款完成按钮
    qrdkwcan() {
      //提交数据修改状态
      var dakuanreturn = {
        id: this.returnincomeid,
        phaseStatus: 10,
      };
      querendakuanForLaw(dakuanreturn).then((response) => {
        this.$modal.msgSuccess("打款已完成，本期次信息费工作结束！");
      });

      var return3 = {
        id: this.projectId,
      };

      getLawProjectqicideteils(return3).then((response) => {
        if (response.length !== 0) {
          for (let i = 0; i < response.length; i++) {
            if (response[i].phase_status === "7") {
              response[i].phase_status = 0;
            } else if (response[i].phase_status === "8") {
              response[i].phase_status = 1;
            } else if (response[i].phase_status === "9") {
              response[i].phase_status = 2;
            } else if (response[i].phase_status === "10") {
              response[i].phase_status = 3;
            }
          }
        }

        this.yewuqiciList = response;
        if (this.yewuqiciList.length === 0) {
          this.jindutiaoshow = false;
        } else {
          for (let i = 0; i < this.yewuqiciList.length; i++) {
            if (this.yewuqiciList[i].id == this.returnincomeid) {
              this.yewuqicideilog = this.yewuqiciList[i].term_month;
            }
          }
        }
      });
      //关闭deilog
      this.dkywcdeilog = false;
      this.reload();
    },
    //取消打款完成按钮
    quxiaoqrdk() {
      this.dkywcdeilog = false;
    },
    //打款已完成按钮方法 打开deilog
    dkywcButton() {
      this.dkywcdeilog = true;
    },
    //隐藏打款表格
    closedakuantable() {
      this.dengjidakuantable = false;
    },
    //登记打款提交
    async submitdjdk() {
      this.onblurfeeAmt();
      if (this.issubmit == true && this.bitianjiaoyan == true) {
        //提交数据
        var returndatafee = {
          projectId: this.projectId,
          projectIncomeId: this.projectIncomeId,
          phaseId: this.returnincomeid,
          projectFeeId: this.feeId,
          payDataList: this.dengjidakuanform.tableData,
          remark: this.lrremark,
        };
        const { isok } = await submitLawdakuanData(returndatafee);
        this.returnData = {};
        this.anewget(this.returnincomeid);
        if (isok) {
          this.dengjidakuantable = false;
          //重新获取打款表数据
          this.dengjidakuan();
          var return2 = {
            id: this.projectId,
            sumFlag: 1,
          };
          // jindutiaoshow

          getLawProjectdeteils(return2).then((response) => {
            this.phaseNoFinish = response.phaseNoFinish;
            this.overDetailBodyDetailList = response.detail_list;
            this.overDetailBodyDetailList_v2 = response.detail_list_v2;
            this.replaceCompanyInfo = response.replaceCompanyInfo;
            this.merage(this.overDetailBodyDetailList_v2);
            this.feecustNameAndfeilvList = response.fee_list;
            //列表合计
            this.feeAmtSum = response.sum.sum_fee_amt;

            this.feeAmt2Sum = response.sum.sum_fee_amt_2;

            this.payAmtSum = response.sum.sum_pay_amt;

            this.differenceAmtSum = response.sum.sum_difference_amt;
            this.feeAlreadySum = response.sum.sum_fee_already;
            this.feeNoAlreadySum = response.sum.sum_fee_no_already;
            this.incomeSum = response.sum.sum_income_sum;
            this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
            this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
            this.feeRoundSum = response.sum.sum_fee_roud;
            this.currentFeeSum = response.sum.sum_current_fee;
            //todo 财务项目管理四期 新增合计
            this.trueComeAmtSum = response.sum.sum_true_come_amt;
            this.serviceFeeSum = response.sum.sum_service_fee;
            this.principalSum = response.sum.sum_principal;
            this.jtfrAmtSum = response.sum.sum_jtfr_amt;
            this.lawProfitSum = response.sum.sum_law_profit;
            this.userList = response.people_list;
            this.loading = false;
          });
        }
        //关闭deilog
        this.dengjidakuandeilog = false;
        this.$modal.msgSuccess("提交成功");
      } else {
        alert("打款信息有误！！请检查打款信息！");
      }
    },
    //登记打款取消
    closedjdk() {
      //关闭deilog
      this.dengjidakuandeilog = false;

      //   this. dengjidakuanform={
      //   tableData:[
      //       {
      //           payDate:'',
      //           payAmt:'',
      //           differenceAmt:0,
      //         }
      //   ]
      // }
      //重新获取登记打款信息表格
      this.dengjidakuantable = false;
      this.dengjidakuan();
    },
    //登记打款动态表单删除
    rowdakuanDelete(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提⽰", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.dengjidakuanform.tableData.splice(row.xh - 1, 1);
          this.$message.success("删除成功!");
        })
        .catch(() => {});
    },
    //录入打款信息动态新增表单
    removeDomain(item) {
      var index = this.dengjidakuanform.tableData.indexOf(item);
      if (index !== -1) {
        this.dengjidakuanform.tableData.splice(index, 1);
      }
      this.onblurfeeAmt();
    },
    addDomain() {
      let nowDate = new Date();

      // let date = {

      //   // 获取当前年份

      //   year: nowDate.getFullYear(),

      //   //获取当前月份

      //   month: (nowDate.getMonth() + 1 < 10 ? "0" + (nowDate.getMonth() + 1) : nowDate.getMonth()+1)-1 ,

      //   //获取当前日期

      //   date: nowDate.getDate(),
      // };

      //拼接
      this.dengjidakuanform.tableData.push({
        payDate: nowDate,
        payAmt: 0,
        differenceAmt: 0,
        key: Date.now(),
      });
    },

    //登记打款信息表格打款
    dakuanbutton(row) {
      this.dengjidakuandeilog = true;
      this.dengjidakuanCustName = row.custName;
      this.dengjidakuanFeeCustName = row.feeCustName;
      // this.dengjidakuanform.tableData = row.payList
      // this.dakuanffzje = row.feeAmt
      this.dakuanffzje = row.feeRound;
      this.ywcdakuan = row.payAlreadySum;
      this.wwcdakuan = row.payNoAlreadySum;
      this.feeId = row.id;
      this.projectIncomeId = row.projectIncomeId;
      if (row.payList.length === 1 && row.payList[0].payAmt == "") {
        let nowDate = new Date();
        var a = [
          {
            payDate: nowDate,
            payAmt: row.feeRound,
            differenceAmt: 0,
            key: Date.now(),
          },
        ];
        this.dengjidakuanform.tableData = a;
        //         this.dengjidakuanform.tableData[0].payDate = nowDate
        //                      this.dengjidakuanform.tableData[0].payAmt=row.feeRound
        //                      this.dengjidakuanform.tableData[0].differenceAmt=0
        // this.dengjidakuanform.tableData[0].key=Date.now()
      } else {
        this.dengjidakuanform.tableData = row.payList;
      }

      var rhd = {
        id: this.returnincomeid,
      };
      getyewuxiangqingLaw(rhd).then((response) => {
        this.collectionTime = response.detail.collectionTime;
        this.collectionTimeString = response.detail.collectionTimeString;
        this.lrremark = response.remark;
      });

      //
    },
    //登记打款信息修改
    xiugaidjdk(row) {
      if (row.payList.length === 1 && row.payList[0].payAmt == "") {
        let nowDate = new Date();
        var a = [
          {
            payDate: nowDate,
            payAmt: row.feeRound,
            differenceAmt: 0,
            key: Date.now(),
          },
        ];
        this.dengjidakuanform.tableData = a;
      } else {
        this.dengjidakuanform.tableData = row.payList;
      }
      //打开deilog 带入信息
      this.dengjidakuandeilog = true;
      this.dengjidakuanCustName = row.custName;
      this.dengjidakuanFeeCustName = row.feeCustName;

      // this.dakuanffzje = row.feeAmt
      this.dakuanffzje = row.feeRound;
      this.ywcdakuan = row.payAlreadySum;
      this.wwcdakuan = row.payNoAlreadySum;
      this.feeId = row.id;
      this.projectIncomeId = row.projectIncomeId;

      var rhd = {
        id: this.returnincomeid,
      };
      getyewuxiangqingLaw(rhd).then((response) => {
        this.collectionTime = response.detail.collectionTime;
        this.collectionTimeString = response.detail.collectionTimeString;
        this.lrremark = response.remark;
      });
    },
    //打开登记打款表格
    dengjidakuan() {
      if (this.dengjidakuantable === false) {
        this.dengjidakuantable = true;
        var rhd = {
          id: this.returnincomeid,
        };
        //获取打款参数
        getyewuxiangqingLaw(rhd).then((response) => {
          this.collectionTime = response.detail.collectionTime;
          this.collectionTimeString = response.detail.collectionTimeString;

          this.lrremark = response.remark;
          this.dakuancanshudata = response.detail.feeList;
          getdakuanTableForLaw(this.dakuancanshudata).then((response) => {
            this.djdkList = response.result;
            if (response.button == "true") {
              this.dkywc = true;
            } else {
              this.dkywc = false;
            }
          });
        });
      } else {
        this.$message.error("请勿重复点击本按钮，请完善下面打款信息！");
      }
      // this.dengjidakuantable = true;
      // var rhd = {
      //   id :this.returnincomeid
      // }
      // //获取打款参数
      // getyewuxiangqingLaw(rhd).then(response=>{
      //
      //     this.lrremark = response.remark
      //    this.dakuancanshudata = response.detail.feeList
      //   getdakuanTableForLaw(this.dakuancanshudata).then(response=>{
      //     this.djdkList = response.result
      //     if(response.button=='true'){
      //       this.dkywc = true;
      //     }else{
      //       this.dkywc = false;
      //     }
      //
      //   })
      //
      // })
    },
    //确认信息费
    async querenfanfei() {
      //调用后台提交数据接口
      // 掉后台接口修改状态
      var aaaa = {
        id: this.returnincomeid,
        phaseStatus: 9,
      };
      const { isok } = await querenIncomeAndFeeForLaw(aaaa);
      this.returnData = {};

      if (isok) {
        if (this.projectData.generateCertificateFlag == 1) {
          createVoucher({ id: this.returnincomeid, status: 0 });
          createVoucher({ id: this.returnincomeid, status: 1 });
        }
        var rhd = {
          id: this.returnincomeid,
        };

        getyewuxiangqingLaw(rhd).then((response) => {
          this.collectionTime = response.detail.collectionTime;
          this.collectionTimeString = response.detail.collectionTimeString;
          this.smaIncomeAmtSum = response.sum.incomeAmtSum;
          this.smafeeAmtSum = response.sum.feeAmtSum;
          this.smafeeRoundSum = response.sum.feeRoundSum;
          this.smafeeAmt2Sum = response.sum.feeAmt2Sum;
          //todo 财务项目管理四期 小表合计新增
          this.smaCurrentFeeSum = response.sum.currentFeeSum;
          this.smaJtfrAmtSum = response.sum.jtfrAmtSum;
          this.smaLawProfitSum = response.sum.lawProfitSum;
          if (response.phaseStatus === "7") {
            response.phaseStatus = 0;
          } else if (response.phaseStatus === "8") {
            response.phaseStatus = 1;
          } else if (response.phaseStatus === "9") {
            response.phaseStatus = 2;
          } else if (response.phaseStatus === "10") {
            response.phaseStatus = 3;
          }
          this.jindutiao = response.phaseStatus;
          this.overDetailBodyDetailList1 =
            // [
            response.detail.feeList;
          // ]

          //录入收入日期
          this.lrsrdate = response.status0.dynamic_time;
          //录入收入代办人
          this.lrsrdaibanren = response.status0.oper_name;
          // //确认收入日期
          // this.qrsrdate = response.status1.dynamic_time
          // //确认收入代办人
          // this.qrsrdaibanren = response.status1.oper_name
          // //录入信息费日期
          // this.lrffdate = response.status2.dynamic_time
          // //录入信息费代办人
          // this.lrffdaibanren = response.status2.oper_name
          //确认信息费日期
          this.qrffdate = response.status1.dynamic_time;
          //确认信息费代办人
          this.qrffdaibanren = response.status1.oper_name;
          if (response.phaseStatus === 3) {
          }
        });
        var return2 = {
          id: this.projectId,
          sumFlag: 1,
        };
        // jindutiaoshow

        getLawProjectdeteils(return2).then((response) => {
          this.phaseNoFinish = response.phaseNoFinish;
          this.overDetailBodyDetailList = response.detail_list;
          this.overDetailBodyDetailList_v2 = response.detail_list_v2;
          this.merage(this.overDetailBodyDetailList_v2);
          this.replaceCompanyInfo = response.replaceCompanyInfo;
          this.feecustNameAndfeilvList = response.fee_list;
          //列表合计
          this.feeAmtSum = response.sum.sum_fee_amt;

          this.feeAmt2Sum = response.sum.sum_fee_amt_2;

          this.payAmtSum = response.sum.sum_pay_amt;

          this.differenceAmtSum = response.sum.sum_difference_amt;
          this.feeAlreadySum = response.sum.sum_fee_already;
          this.feeNoAlreadySum = response.sum.sum_fee_no_already;
          this.incomeSum = response.sum.sum_income_sum;
          this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
          this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
          this.feeRoundSum = response.sum.sum_fee_roud;
          this.currentFeeSum = response.sum.sum_current_fee;
          //todo 财务项目管理四期 新增合计
          this.trueComeAmtSum = response.sum.sum_true_come_amt;
          this.serviceFeeSum = response.sum.sum_service_fee;
          this.principalSum = response.sum.sum_principal;
          this.jtfrAmtSum = response.sum.sum_jtfr_amt;
          this.lawProfitSum = response.sum.sum_law_profit;
          this.userList = response.people_list;
          this.loading = false;
        });
      }
      //关闭deilog
      this.querenffdeilog = false;
      this.$modal.msgSuccess("提交成功");
      this.reload();
    },
    //关闭确认信息费
    quxiaofanfei() {
      this.querenffdeilog = false;
    },
    //打开确认信息费
    querenff() {
      this.querenffdeilog = true;
    },
    //修改业务期次关闭
    closeUpdate() {
      this.updateprojectqcdeilog = false;
    },
    //修改信息费提交关闭
    closeupdateff() {
      this.xiugaifanfeideilog = false;
    },
    //修改信息费提交
    async submitupdateff() {
      this.checkfanfei();

      if (this.fanfeijiaoyan) {
        var addfanfeidata = {
          projectId: this.projectId,
          incomeId: this.returnincomeid,
          maoli: this.maolijine,
          tichengmaoli: this.tichengmaolijine,
          remark: this.lrremark,
          fee: this.fanfei.tableData,
        };
        const { isok } = await updatefanfei(addfanfeidata);
        this.returnData = {};
        //提交数据
        if (isok) {
          var rhd = {
            id: this.returnincomeid,
          };
          getyewuxiangqingLaw(rhd).then((response) => {
            this.collectionTime = response.detail.collectionTime;
            this.collectionTimeString = response.detail.collectionTimeString;
            this.smaIncomeAmtSum = response.sum.incomeAmtSum;
            this.smafeeAmtSum = response.sum.feeAmtSum;
            this.smafeeRoundSum = response.sum.feeRoundSum;
            this.smafeeAmt2Sum = response.sum.feeAmt2Sum;
            //todo 财务项目管理四期 小表合计新增
            this.smaCurrentFeeSum = response.sum.currentFeeSum;
            this.smaJtfrAmtSum = response.sum.jtfrAmtSum;
            this.smaLawProfitSum = response.sum.lawProfitSum;
            if (response.phaseStatus === "7") {
              response.phaseStatus = 0;
            } else if (response.phaseStatus === "8") {
              response.phaseStatus = 1;
            } else if (response.phaseStatus === "9") {
              response.phaseStatus = 2;
            } else if (response.phaseStatus === "10") {
              response.phaseStatus = 3;
            }
            this.jindutiao = response.phaseStatus;
            this.overDetailBodyDetailList1 =
              // [
              response.detail.feeList;
            // ]
            //录入收入日期
            this.lrsrdate = response.status0.dynamic_time;
            //录入收入代办人
            this.lrsrdaibanren = response.status0.oper_name;
            //确认收入日期
            this.qrsrdate = response.status1.dynamic_time;
            //确认收入代办人
            this.qrsrdaibanren = response.status1.oper_name;
            //录入信息费日期
            this.lrffdate = response.status2.dynamic_time;
            //录入信息费代办人
            this.lrffdaibanren = response.status2.oper_name;
          });
          //关闭deilog
          var return2 = {
            id: this.projectId,
            sumFlag: 1,
          };
          // jindutiaoshow

          getLawProjectdeteils(return2).then((response) => {
            this.phaseNoFinish = response.phaseNoFinish;
            this.overDetailBodyDetailList = response.detail_list;
            this.overDetailBodyDetailList_v2 = response.detail_list_v2;
            this.replaceCompanyInfo = response.replaceCompanyInfo;
            this.merage(this.overDetailBodyDetailList_v2);

            this.feecustNameAndfeilvList = response.fee_list;
            //列表合计
            this.feeAmtSum = response.sum.sum_fee_amt;

            this.feeAmt2Sum = response.sum.sum_fee_amt_2;

            this.payAmtSum = response.sum.sum_pay_amt;

            this.differenceAmtSum = response.sum.sum_difference_amt;
            this.feeAlreadySum = response.sum.sum_fee_already;
            this.feeNoAlreadySum = response.sum.sum_fee_no_already;
            this.incomeSum = response.sum.sum_income_sum;
            this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
            this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
            this.feeRoundSum = response.sum.sum_fee_roud;
            this.currentFeeSum = response.sum.sum_current_fee;
            //todo 财务项目管理四期 新增合计
            this.trueComeAmtSum = response.sum.sum_true_come_amt;
            this.serviceFeeSum = response.sum.sum_service_fee;
            this.principalSum = response.sum.sum_principal;
            this.jtfrAmtSum = response.sum.sum_jtfr_amt;
            this.lawProfitSum = response.sum.sum_law_profit;
            this.userList = response.people_list;
            this.loading = false;
          });
        }
        this.fanfei = {
          tableData: [
            {
              custName: "",
              feeCustName: "",
              calculateType: "",
              feeAmt: "",
              feeAmt2: "",
              status: false,
            },
          ],
        };
        this.$message.success("提交成功!");
        this.xiugaifanfeideilog = false;
      } else {
        alert("信息费添加有问题，请检查要提交的数据！！");
      }
    },
    //修改录入信息费
    updateluruff() {
      this.xiugaifanfeideilog = true;
      var sdasd = {
        id: this.returnincomeid,
      };
      //得到固定展示数值合计
      var rrraa = {
        id: this.returnincomeid,
      };
      getyewuxiangqingLaw(rrraa).then((response) => {
        this.collectionTime = response.detail.collectionTime;
        this.collectionTimeString = response.detail.collectionTimeString;
        this.lrdeshouru = response.detail.incomeAmt;
        this.fanfeicount = response.sum.feeAmtSum;
        this.tichengfanfeicount = response.sum.feeAmt2Sum;
        this.maolijine = response.detail.grossProfitAmt;
        this.tichengmaolijine = response.detail.grossProfitAmt2;
        this.lrremark = response.remark;
      });

      //得到列表
      getfanfeidateil(sdasd).then((response) => {
        this.fanfei.tableData = response;
      });

      var return2 = {
        id: this.projectId,
        sumFlag: 1,
      };
      // jindutiaoshow

      getLawProjectdeteils(return2).then((response) => {
        this.phaseNoFinish = response.phaseNoFinish;
        this.overDetailBodyDetailList = response.detail_list;
        this.overDetailBodyDetailList_v2 = response.detail_list_v2;
        this.merage(this.overDetailBodyDetailList_v2);
        this.feecustNameAndfeilvList = response.fee_list;
        this.replaceCompanyInfo = response.replaceCompanyInfo;
        //列表合计
        this.feeAmtSum = response.sum.sum_fee_amt;

        this.feeAmt2Sum = response.sum.sum_fee_amt_2;

        this.payAmtSum = response.sum.sum_pay_amt;

        this.differenceAmtSum = response.sum.sum_difference_amt;
        this.feeAlreadySum = response.sum.sum_fee_already;
        this.feeNoAlreadySum = response.sum.sum_fee_no_already;
        this.incomeSum = response.sum.sum_income_sum;
        this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
        this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
        this.feeRoundSum = response.sum.sum_fee_roud;
        this.currentFeeSum = response.sum.sum_current_fee;
        //todo 财务项目管理四期 新增合计
        this.trueComeAmtSum = response.sum.sum_true_come_amt;
        this.serviceFeeSum = response.sum.sum_service_fee;
        this.principalSum = response.sum.sum_principal;
        this.jtfrAmtSum = response.sum.sum_jtfr_amt;
        this.lawProfitSum = response.sum.sum_law_profit;
        this.userList = response.people_list;
        this.loading = false;
      });
    },
    //校验信息费内容
    checkfanfei() {
      for (let i = 0; i < this.fanfei.tableData.length; i++) {
        if (this.fanfei.tableData[i].feeCustName == "") {
          this.fanfeijiaoyan = false;
        } else {
          this.fanfeijiaoyan = true;
        }
        if (this.fanfei.tableData[i].calculateType == "") {
          this.fanfeijiaoyan = false;
        } else {
          this.fanfeijiaoyan = true;
        }
        if (this.fanfei.tableData[i].feeAmt == "") {
          this.fanfeijiaoyan = false;
        } else {
          this.fanfeijiaoyan = true;
        }
        if (this.fanfei.tableData[i].feeAmt2 == "") {
          this.fanfeijiaoyan = false;
        } else {
          this.fanfeijiaoyan = true;
        }

        if (parseInt(this.fanfei.tableData[i].feeAmt2) == 0) {
          this.fanfeijiaoyan = true;
        }
      }
    },
    //录入信息费取消
    closeluruff() {
      this.lurufanfeideilog = false;
      // this.shouruheji = 0.00;
      this.benqifanfeiheji = 0.0;
      this.fanfeiquzhengheji = 0.0;
      this.tichengfanfeiheji = 0.0;
      this.maolijine = 0.0;
      this.tichengmaolijine = 0.0;
      this.feeAmtRedFlag = false;
      this.guaqiFeeAmtFlag = false;
    },
    //录入信息费保存
    async submitluruff() {
      this.checkfanfei();
      if (this.fanfeijiaoyan) {
        var addfanfeidata = {
          projectId: this.projectId,
          incomeId: this.returnincomeid,
          phaseStatus: "3",
          maoli: this.maolijine,
          tichengmaoli: this.tichengmaolijine,
          remark: this.lrremark,
          fee: this.fanfei.tableData,
        };
        const { isok } = await lurufanfeiadd(addfanfeidata);
        this.returnData = {};
        if (isok) {
          var rhd = {
            id: this.returnincomeid,
          };

          getyewuxiangqingLaw(rhd).then((response) => {
            this.collectionTime = response.detail.collectionTime;
            this.collectionTimeString = response.detail.collectionTimeString;
            this.smaIncomeAmtSum = response.sum.incomeAmtSum;
            this.smafeeAmtSum = response.sum.feeAmtSum;
            this.smafeeRoundSum = response.sum.feeRoundSum;
            this.smafeeAmt2Sum = response.sum.feeAmt2Sum;
            //todo 财务项目管理四期 小表合计新增
            this.smaCurrentFeeSum = response.sum.currentFeeSum;
            this.smaJtfrAmtSum = response.sum.jtfrAmtSum;
            this.smaLawProfitSum = response.sum.lawProfitSum;
            if (response.phaseStatus === "7") {
              response.phaseStatus = 0;
            } else if (response.phaseStatus === "8") {
              response.phaseStatus = 1;
            } else if (response.phaseStatus === "9") {
              response.phaseStatus = 2;
            } else if (response.phaseStatus === "10") {
              response.phaseStatus = 3;
            }
            this.jindutiao = response.phaseStatus;
            this.overDetailBodyDetailList1 =
              // [
              response.detail.feeList;
            // ]
            //录入收入日期
            this.lrsrdate = response.status0.dynamic_time;
            //录入收入代办人
            this.lrsrdaibanren = response.status0.oper_name;
            //确认收入日期
            this.qrsrdate = response.status1.dynamic_time;
            //确认收入代办人
            this.qrsrdaibanren = response.status1.oper_name;
            //录入信息费日期
            this.lrffdate = response.status2.dynamic_time;
            //录入信息费代办人
            this.lrffdaibanren = response.status2.oper_name;
          });
          var return2 = {
            id: this.projectId,
            sumFlag: 1,
          };
          // jindutiaoshow

          getLawProjectdeteils(return2).then((response) => {
            this.phaseNoFinish = response.phaseNoFinish;
            this.overDetailBodyDetailList = response.detail_list;
            this.replaceCompanyInfo = response.replaceCompanyInfo;
            this.overDetailBodyDetailList_v2 = response.detail_list_v2;
            this.merage(this.overDetailBodyDetailList_v2);
            this.feecustNameAndfeilvList = response.fee_list;
            //列表合计
            this.feeAmtSum = response.sum.sum_fee_amt;

            this.feeAmt2Sum = response.sum.sum_fee_amt_2;

            this.payAmtSum = response.sum.sum_pay_amt;

            this.differenceAmtSum = response.sum.sum_difference_amt;
            this.feeAlreadySum = response.sum.sum_fee_already;
            this.feeNoAlreadySum = response.sum.sum_fee_no_already;
            this.incomeSum = response.sum.sum_income_sum;
            this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
            this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
            this.feeRoundSum = response.sum.sum_fee_roud;
            this.currentFeeSum = response.sum.sum_current_fee;
            //todo 财务项目管理四期 新增合计
            this.trueComeAmtSum = response.sum.sum_true_come_amt;
            this.serviceFeeSum = response.sum.sum_service_fee;
            this.principalSum = response.sum.sum_principal;
            this.jtfrAmtSum = response.sum.sum_jtfr_amt;
            this.lawProfitSum = response.sum.sum_law_profit;
            this.userList = response.people_list;
            this.loading = false;
          });
        }
        //关闭deilog

        this.lurufanfeideilog = false;
        this.$message.success("提交成功!");
      } else {
        alert("信息费添加有问题，请检查要提交的数据！！");
      }
    },
    //录入信息费删除
    rowDelete(row) {
      this.$confirm("此操作将永久删除, 是否继续?", "提⽰", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.fanfei.tableData.splice(row.xh - 1, 1);
          //重新计算
          this.fanfeicount = 0;
          this.maolijine = 0;
          //本行的信息费和提成信息费加到总计
          this.tichengfanfeicount = 0;
          this.tichengmaolijine = 0;
          for (var i = 0; i < this.fanfei.tableData.length; i++) {
            //提成信息费合计
            this.tichengfanfeicount = (
              parseFloat(this.tichengfanfeicount) +
              parseFloat(this.fanfei.tableData[i].feeAmt2)
            ).toFixed(2);
            //提成毛利
            this.tichengmaolijine = (
              parseFloat(this.lrdeshouru) - parseFloat(this.tichengfanfeicount)
            ).toFixed(2);
          }
          for (var i = 0; i < this.fanfei.tableData.length; i++) {
            //本行的信息费和提成信息费加到总计
            this.fanfeicount = (
              parseFloat(this.fanfeicount) +
              parseFloat(this.fanfei.tableData[i].feeAmt)
            ).toFixed(2);

            //毛利
            this.maolijine = (
              parseFloat(this.lrdeshouru) - parseFloat(this.fanfeicount)
            ).toFixed(2);
          }

          this.$message.success("删除成功!");
        })
        .catch(() => {});
    },
    // 其中row是⾏对象，rowindex是⾏号，从0开始。所以这样就能实现了序号(xh属性)递增并且取值为⾏号加1。
    rowClassName({ row, rowIndex }) {
      row.xh = rowIndex + 1;
    },
    //录入信息费动态新增表单
    onAdd() {
      var item = {
        custName: "",
        feeCustName: "",
        calculateType: "",
        feeAmt: "",
        feeAmt2: "",
        status: true,
      };
      this.fanfei.tableData.push(item);
    },
    //录入信息费
    lurufanfei() {
      var rrraa = {
        id: this.returnincomeid,
      };
      getyewuxiangqingLaw(rrraa).then((response) => {
        this.collectionTime = response.detail.collectionTime;
        this.collectionTimeString = response.detail.collectionTimeString;
        this.smaIncomeAmtSum = response.sum.incomeAmtSum;
        // this.overDetailBodyDetailList1 = response.detail
        this.smafeeAmtSum = response.sum.feeAmtSum;
        this.smafeeRoundSum = response.sum.feeRoundSum;
        this.smafeeAmt2Sum = response.sum.feeAmt2Sum;
        //todo 财务项目管理四期 小表合计新增
        this.smaCurrentFeeSum = response.sum.currentFeeSum;
        this.smaJtfrAmtSum = response.sum.jtfrAmtSum;
        this.smaLawProfitSum = response.sum.lawProfitSum;
        this.lrdeshouru = response.detail.incomeAmt;
        this.lrremark = response.remark;

        this.overDetailBodyDetailList1 =
          // [
          response.detail.feeList;
        // ]

        //录入收入日期
        this.lrsrdate = response.status0.dynamic_time;
        //录入收入代办人
        this.lrsrdaibanren = response.status0.oper_name;
        //确认收入日期
        this.qrsrdate = response.status1.dynamic_time;
        //确认收入代办人
        this.qrsrdaibanren = response.status1.oper_name;
        //录入信息费日期
        this.lrffdate = response.status2.dynamic_time;
        //录入信息费代办人
        this.lrffdaibanren = response.status2.oper_name;
      });
      this.fanfei = {
        tableData: [
          {
            custName: "",
            feeCustName: "",
            calculateType: "",
            feeAmt: "",
            feeAmt2: "",
            status: false,
          },
        ],
      };
      this.tichengfanfeicount = 0;
      this.tichengmaolijine = 0;
      this.fanfeicount = 0;
      this.maolijine = 0;
      this.lurufanfeideilog = true;
    },
    //修改业务期次
    updateprojectqc() {
      //todo 修改业务期次，必须选中了业务期次才能有提示框
      if (
        this.yewuqici != null &&
        this.yewuqici != "" &&
        this.yewuqici != undefined
      ) {
        this.updateprojectqcdeilog = true;
        var returndate = {
          id: this.yewuqici,
        };
        //赋值
        getupdateyewuqiciByid(returndate).then((response) => {
          if (response.term == 0) {
            this.addprojectform.term = response.term;
            this.addprojectform.termMonth = response.term_month;
            this.addprojectform.id = response.id;
          } else if (response.term == 1) {
            this.addprojectform.id = response.id;
            this.addprojectform.term = response.term;
            this.selectDate.push(response.term_begin);
            this.selectDate.push(response.term_end);
          }
        });
        var return2 = {
          id: this.projectId,
          sumFlag: 1,
        };
        // jindutiaoshow

        getLawProjectdeteils(return2).then((response) => {
          this.phaseNoFinish = response.phaseNoFinish;
          this.overDetailBodyDetailList = response.detail_list;
          this.replaceCompanyInfo = response.replaceCompanyInfo;
          this.overDetailBodyDetailList_v2 = response.detail_list_v2;
          this.merage(this.overDetailBodyDetailList_v2);
          this.feecustNameAndfeilvList = response.fee_list;
          //列表合计
          this.feeAmtSum = response.sum.sum_fee_amt;

          this.feeAmt2Sum = response.sum.sum_fee_amt_2;

          this.payAmtSum = response.sum.sum_pay_amt;

          this.differenceAmtSum = response.sum.sum_difference_amt;
          this.feeAlreadySum = response.sum.sum_fee_already;
          this.feeNoAlreadySum = response.sum.sum_fee_no_already;
          this.incomeSum = response.sum.sum_income_sum;
          this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
          this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
          this.feeRoundSum = response.sum.sum_fee_roud;
          this.currentFeeSum = response.sum.sum_current_fee;
          //todo 财务项目管理四期 新增合计
          this.trueComeAmtSum = response.sum.sum_true_come_amt;
          this.serviceFeeSum = response.sum.sum_service_fee;
          this.principalSum = response.sum.sum_principal;
          this.jtfrAmtSum = response.sum.sum_jtfr_amt;
          this.lawProfitSum = response.sum.sum_law_profit;
          this.userList = response.people_list;
          this.loading = false;
        });
        // this.addprojectform={
        //    id:null,
        //    projectId:null,
        //   term:'0',
        //   termMonth:'',
        //   termBegin:'',
        //   termEnd:''
        // }
        // this.reload();
      } else {
        this.$message.error("必须选择一个期次才能进行修改");
      }
    },
    //取消收入
    quxiaoshouru() {
      this.querensrdeilog = false;
    },
    //确认收入
    async querenshouru() {
      // 掉后台接口修改状态
      var aaaa = {
        id: this.returnincomeid,
        phaseStatus: 2,
      };

      const { isok } = await submitshouru(aaaa);
      this.returnData = {};
      // var isok2 = false
      // submitshouru(aaaa).then(response=>{

      //   isok2 = response.isok;
      // })
      if (isok) {
        var rhd = {
          id: this.returnincomeid,
        };

        getyewuxiangqingLaw(rhd).then((response) => {
          this.collectionTime = response.detail.collectionTime;
          this.collectionTimeString = response.detail.collectionTimeString;
          this.smaIncomeAmtSum = response.sum.incomeAmtSum;
          this.smafeeAmtSum = response.sum.feeAmtSum;
          this.smafeeRoundSum = response.sum.feeRoundSum;
          this.smafeeAmt2Sum = response.sum.feeAmt2Sum;
          //todo 财务项目管理四期 小表合计新增
          this.smaCurrentFeeSum = response.sum.currentFeeSum;
          this.smaJtfrAmtSum = response.sum.jtfrAmtSum;
          this.smaLawProfitSum = response.sum.lawProfitSum;
          if (response.phaseStatus === "7") {
            response.phaseStatus = 0;
          } else if (response.phaseStatus === "8") {
            response.phaseStatus = 1;
          } else if (response.phaseStatus === "9") {
            response.phaseStatus = 2;
          } else if (response.phaseStatus === "10") {
            response.phaseStatus = 3;
          }
          this.jindutiao = response.phaseStatus;
          this.overDetailBodyDetailList1 =
            // [
            response.detail.feeList;
          // ]
          //录入收入日期
          this.lrsrdate = response.status0.dynamic_time;
          //录入收入代办人
          this.lrsrdaibanren = response.status0.oper_name;
          //确认收入日期
          this.qrsrdate = response.status1.dynamic_time;
          //确认收入代办人
          this.qrsrdaibanren = response.status1.oper_name;
        });
        var return2 = {
          id: this.projectId,
          sumFlag: 1,
        };
        // jindutiaoshow

        getLawProjectdeteils(return2).then((response) => {
          this.phaseNoFinish = response.phaseNoFinish;
          this.replaceCompanyInfo = response.replaceCompanyInfo;
          this.overDetailBodyDetailList = response.detail_list;
          this.overDetailBodyDetailList_v2 = response.detail_list_v2;
          this.merage(this.overDetailBodyDetailList_v2);
          this.feecustNameAndfeilvList = response.fee_list;
          //列表合计
          this.feeAmtSum = response.sum.sum_fee_amt;

          this.feeAmt2Sum = response.sum.sum_fee_amt_2;

          this.payAmtSum = response.sum.sum_pay_amt;

          this.differenceAmtSum = response.sum.sum_difference_amt;
          this.feeAlreadySum = response.sum.sum_fee_already;
          this.feeNoAlreadySum = response.sum.sum_fee_no_already;
          this.incomeSum = response.sum.sum_income_sum;
          this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
          this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
          this.feeRoundSum = response.sum.sum_fee_roud;
          this.currentFeeSum = response.sum.sum_current_fee;
          //todo 财务项目管理四期 新增合计
          this.trueComeAmtSum = response.sum.sum_true_come_amt;
          this.serviceFeeSum = response.sum.sum_service_fee;
          this.principalSum = response.sum.sum_principal;
          this.jtfrAmtSum = response.sum.sum_jtfr_amt;
          this.lawProfitSum = response.sum.sum_law_profit;
          this.userList = response.people_list;
          this.loading = false;
        });
      }
      //关闭deilog
      this.querensrdeilog = false;
      this.$modal.msgSuccess("提交成功");
      getProjectSumByProjectId({ projectId: this.projectId }).then((res) => {
        if (res.code == 200) {
          this.sumData = res.data;
        }
      });
      // this.reload();
    },
    //打开确认收入
    querensr() {
      this.querensrdeilog = true;
    },
    //修改录入
    updateluru() {
      this.$modal
        .confirm("是否重新录入本期收入与信息费？")
        .then(function () {})
        .then(() => {
          this.lurudeilog = true;
          this.changeFlag = 1;
        })
        .catch(() => {});

      /* const userIds = row.userId || this.ids;*/
      /* this.getList();
            this.$modal.msgSuccess("删除成功");*/

      // var rrraa = {
      //        id :this.returnincomeid
      //      }
      // getyewuxiangqingLaw(rrraa).then(response=>{
      //   this.smaIncomeAmtSum = response.sum.incomeAmtSum;
      //         this.smafeeAmtSum = response.sum.feeAmtSum
      //   this.smafeeRoundSum = response.sum.feeRoundSum
      // this.smafeeAmt2Sum = response.sum.feeAmt2Sum
      //
      //           this.lrdeshouru =  response.detail.incomeAmt
      //           this.lrremark = response.remark
      //         this.overDetailBodyDetailList1 =
      //           // [
      //           response.detail.feeList
      //                 // ]
      //
      //       })
    },
    //取消录入
    closeluru() {
      this.lurudeilog = false;
    },
    // //关闭修改录入收入
    // //修改提交录入收入
    // submitupdateluru(){
    //   var returnData = {
    //     incomeAmt:this.lrdeshouru,
    //     remark:this.lrremark,
    //     id:this.returnincomeid,
    //     phaseStatus:1
    //   }
    //   lrsrsubmit(returnData).then(response=>{
    //     this.$modal.msgSuccess("录入成功！");
    //   })
    //   this.returnData = {};
    //   //关闭弹框
    //   this.updatelurudeilog = false

    //   var rhd = {
    //     id :this.returnincomeid
    //   }

    //   getyewuxiangqing(rhd).then(response=>{
    //     this.jindutiao = parseInt(response.phaseStatus);
    //           this.overDetailBodyDetailList1 = [
    //           response.detail
    //                   ]
    // },
    //提交录入
    async submitluru() {
      //收入
      // 提交后台
      // lrdeshouru  lrremark +当前业务期次id
      if (!this.lrdeshouru) {
        alert("录入的收入有误，请检查！");
      } else {
        var returnData = {
          incomeAmt: this.lrdeshouru,
          remark: this.lrremark,
          id: this.returnincomeid,
          phaseStatus: 1,
        };

        const { isok } = await lrsrsubmit(returnData);
        this.returnData = {};
        this.anewget(this.returnincomeid);
        //     this.lrdeshouru = "" // this.lrremark="" //关闭弹框
        if (isok) {
          var return2 = {
            id: this.projectId,
            sumFlag: 1,
          }; // jindutiaoshow
          getLawProjectdeteils(return2).then((response) => {
            this.phaseNoFinish = response.phaseNoFinish;
            this.overDetailBodyDetailList = response.detail_list;
            this.overDetailBodyDetailList_v2 = response.detail_list_v2;
            this.replaceCompanyInfo = response.replaceCompanyInfo;
            this.merage(this.overDetailBodyDetailList_v2);
            this.feecustNameAndfeilvList = response.fee_list; //列表合计
            this.feeAmtSum = response.sum.sum_fee_amt;

            this.feeAmt2Sum = response.sum.sum_fee_amt_2;

            this.payAmtSum = response.sum.sum_pay_amt;

            this.differenceAmtSum = response.sum.sum_difference_amt;
            this.feeAlreadySum = response.sum.sum_fee_already;
            this.feeNoAlreadySum = response.sum.sum_fee_no_already;
            this.incomeSum = response.sum.sum_income_sum;
            this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
            this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
            this.feeRoundSum = response.sum.sum_fee_roud;
            this.currentFeeSum = response.sum.sum_current_fee;
            //todo 财务项目管理四期 新增合计
            this.trueComeAmtSum = response.sum.sum_true_come_amt;
            this.serviceFeeSum = response.sum.sum_service_fee;
            this.principalSum = response.sum.sum_principal;
            this.jtfrAmtSum = response.sum.sum_jtfr_amt;
            this.lawProfitSum = response.sum.sum_law_profit;
            this.userList = response.people_list;
            this.loading = false;
          });
          this.$modal.msgSuccess("录入成功！");
        }
        this.lurudeilog = false;
      }
    },
    //重新获取
    anewget(incomeidget) {
      var rhd = {
        // id :this.returnincomeid
        id: incomeidget,
      };

      getyewuxiangqingLaw(rhd).then((response) => {
        this.collectionTime = response.detail.collectionTime;
        this.collectionTimeString = response.detail.collectionTimeString;
        this.smaIncomeAmtSum = response.sum.incomeAmtSum;
        this.smafeeAmtSum = response.sum.feeAmtSum;
        this.smafeeRoundSum = response.sum.feeRoundSum;
        this.smafeeAmt2Sum = response.sum.feeAmt2Sum;
        //todo 财务项目管理四期 小表合计新增
        this.smaCurrentFeeSum = response.sum.currentFeeSum;
        this.smaJtfrAmtSum = response.sum.jtfrAmtSum;
        this.smaLawProfitSum = response.sum.lawProfitSum;
        if (response.phaseStatus === "7") {
          response.phaseStatus = 0;
        } else if (response.phaseStatus === "8") {
          response.phaseStatus = 1;
        } else if (response.phaseStatus === "9") {
          response.phaseStatus = 2;
        } else if (response.phaseStatus === "10") {
          response.phaseStatus = 3;
        }
        this.jindutiao = response.phaseStatus;
        this.overDetailBodyDetailList1 =
          // [
          response.detail.feeList;
        // ]
        //录入收入日期
        this.lrsrdate = response.status0.dynamic_time;
        //录入收入代办人
        this.lrsrdaibanren = response.status0.oper_name;
      });
    },
    //取消录入
    //录入
    lurusr() {
      this.lurudeilog = true;
      this.lrdeshouru = "";
    },
    //关闭终止
    closezhongzhi() {
      this.abortprojectdeilog = false;
    },
    //确定终止
    quedingzhongzhi() {
      //返回后端状态
      closeProject(this.projectId).then((response) => {});
      //关闭deilog
      this.abortprojectdeilog = false;
      //跳转项目页面
      this.$router.push({ path: "/caiwu/returnAmount" });
    },
    //打开终止项目
    abortproject() {
      this.abortprojectdeilog = true;
    },
    //修改项目信息
    updateprojectData() {
      this.$router.push({
        path: "/caiwu/updateproject",
        query: { productId: this.projectId, projectPortfolioCode: "lawUrging" },
      });
    },

    //取消修改业务负责人
    //     closeyewu(){
    //       this.updateproyewudel = false;
    //        this.updateyewu = '';
    //     },
    //提交修改业务负责人
    // async submitupdateyewu(){
    //    this.returnData = {
    //      id : this.projectId,
    //      salesmans:this.updateyewu
    //    }
    //    const { isok } = await updateyewufuze(this.returnData);
    // if(isok){
    //  this.getprojectDetails();
    // }
    //    this.returnData = {
    //    }
    //
    //    this.updateproyewudel = false;
    //    this.$modal.msgSuccess("修改业务负责人成功！");
    //  },
    //打开deilog
    //  updateproyewu(){
    //    this.updateproyewudel = true;
    //
    //    //根据项目id获取当前项目业务负责人并赋值
    //    var getyewu = {
    //      id:this.projectId
    //    }
    //    getyeuwList(getyewu).then(response=>{
    //      this.updateyewu = response.idList
    //    })
    //
    //  },
    //新增业务期次按钮方法
    addprojectDate() {
      this.open = true;
      this.inmage = fclct;
    },
    //合并行
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % 2 === 0) {
          return {
            rowspan: 2,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    //传期次id获取详情
    getdeatilByincome(incomeid) {
      var dataparam = {
        id: this.projectId,
      };
      getDeatil(dataparam).then((response) => {
        this.projectName = response.projectName;
        this.custName = response.custName;
        this.incomeCustName = response.incomeCustName;
      });

      if (incomeid !== -999) {
        var rrraa = {
          id: incomeid,
        };
        getyewuxiangqingLaw(rrraa).then((response) => {
          this.collectionTime = response.detail.collectionTime;
          this.collectionTimeString = response.detail.collectionTimeString;
          this.smaIncomeAmtSum = response.sum.incomeAmtSum;
          this.smafeeAmtSum = response.sum.feeAmtSum;
          this.smafeeRoundSum = response.sum.feeRoundSum;
          this.smafeeAmt2Sum = response.sum.feeAmt2Sum;
          //todo 财务项目管理四期 小表合计新增
          this.smaCurrentFeeSum = response.sum.currentFeeSum;
          this.smaJtfrAmtSum = response.sum.jtfrAmtSum;
          this.smaLawProfitSum = response.sum.lawProfitSum;
          if (response.phaseStatus === "7") {
            response.phaseStatus = 0;
          } else if (response.phaseStatus === "8") {
            response.phaseStatus = 1;
          } else if (response.phaseStatus === "9") {
            response.phaseStatus = 2;
          } else if (response.phaseStatus === "10") {
            response.phaseStatus = 3;
          }
          this.jindutiao = response.phaseStatus;
          // this.overDetailBodyDetailList1 = response.detail
          if (response.phaseStatus == 1) {
            this.lrdeshouru = response.detail.incomeAmt;
            this.lrremark = response.remark;
          }

          this.overDetailBodyDetailList1 =
            // [
            response.detail.feeList;
          // ]

          //录入收入日期
          //todo 修复报错
          if (response.hasOwnProperty("status0")) {
            this.lrsrdate = response.status0.dynamic_time;
            //录入收入代办人
            this.lrsrdaibanren = response.status0.oper_name;
          }
          //   if(response.status1!=null){
          //   //确认收入日期
          //   this.qrsrdate = response.status1.dynamic_time
          //   //确认收入代办人
          //   this.qrsrdaibanren = response.status1.oper_name
          //   }
          //
          // if(response.status2!=null){
          //   //录入信息费日期
          //   this.lrffdate = response.status2.dynamic_time
          //   //录入信息费代办人
          //   this.lrffdaibanren = response.status2.oper_name
          // }
          if (response.status1 != null) {
            //确认信息费日期
            this.qrffdate = response.status1.dynamic_time;
            //确认信息费代办人
            this.qrffdaibanren = response.status1.oper_name;
          }
        });
      }
      var return2 = {
        id: this.projectId,
        sumFlag: 1,
      };
      // jindutiaoshow

      getLawProjectdeteils(return2).then((response) => {
        this.phaseNoFinish = response.phaseNoFinish;
        this.overDetailBodyDetailList = response.detail_list;
        this.replaceCompanyInfo = response.replaceCompanyInfo;
        this.overDetailBodyDetailList_v2 = response.detail_list_v2;
        this.merage(this.overDetailBodyDetailList_v2);
        this.feecustNameAndfeilvList = response.fee_list;
        //列表合计
        this.feeAmtSum = response.sum.sum_fee_amt;

        this.feeAmt2Sum = response.sum.sum_fee_amt_2;

        this.payAmtSum = response.sum.sum_pay_amt;

        this.differenceAmtSum = response.sum.sum_difference_amt;
        this.feeAlreadySum = response.sum.sum_fee_already;
        this.feeNoAlreadySum = response.sum.sum_fee_no_already;
        this.incomeSum = response.sum.sum_income_sum;
        this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
        this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
        this.feeRoundSum = response.sum.sum_fee_roud;
        this.currentFeeSum = response.sum.sum_current_fee;
        //todo 财务项目管理四期 新增合计
        this.trueComeAmtSum = response.sum.sum_true_come_amt;
        this.serviceFeeSum = response.sum.sum_service_fee;
        this.principalSum = response.sum.sum_principal;
        this.jtfrAmtSum = response.sum.sum_jtfr_amt;
        this.lawProfitSum = response.sum.sum_law_profit;
        this.userList = response.people_list;
        this.loading = false;
      });
      var return3 = {
        id: this.projectId,
      };

      getLawProjectqicideteils(return3).then((response) => {
        if (response.length !== 0) {
          for (let i = 0; i < response.length; i++) {
            if (response[i].phase_status === "7") {
              response[i].phase_status = 0;
            } else if (response[i].phase_status === "8") {
              response[i].phase_status = 1;
            } else if (response[i].phase_status === "9") {
              response[i].phase_status = 2;
            } else if (response[i].phase_status === "10") {
              response[i].phase_status = 3;
            }
          }
        }

        this.yewuqiciList = response;
        if (this.yewuqiciList.length === 0) {
          this.jindutiaoshow = false;
        } else {
          for (let i = 0; i < this.yewuqiciList.length; i++) {
            if (this.yewuqiciList[i].id == this.returnincomeid) {
              this.yewuqicideilog = this.yewuqiciList[i].term_month;
            }
          }
        }
      });

      this.yewuqici = this.returnincomeid - 0;

      if (incomeid !== -999) {
        //展示进度条
        this.jindutiaozhansh = true;
      } else {
        this.jindutiaozhansh = false;
      }
    },
    //业务期次下拉框改变
    updateqici(data) {
      //初始化进度条办理人
      //录入收入日期
      this.lrsrdate = "";
      //录入收入代办人
      this.lrsrdaibanren = "";
      //确认收入日期
      this.qrsrdate = "";
      //确认收入代办人
      this.qrsrdaibanren = "";
      //录入信息费日
      this.lrffdate = "";
      //录入信息费代办人
      this.lrffdaibanren = "";
      //确认信息费日期
      this.qrffdate = "";
      //确认信息费代办人
      this.qrffdaibanren = "";
      //出纳打款日期
      this.cndkdate = "";
      //出纳打款代办人
      this.cndkdaibanren = "";
      //查询当前期次状态
      if (data.id !== -999) {
        this.yewuqicideilog = data.term_month;
        this.returnincomeid = data.id;
        console.log(data.id, "===");
        this.phaseId = data.id;

        var rrraa = {
          id: this.returnincomeid,
        };
        getyewuxiangqingLaw(rrraa).then((response) => {
          this.feeCustNameProcess = response.feeCustName;
          this.custNameProcess = response.custName;
          this.collectionTime = response.detail.collectionTime;
          this.collectionTimeString = response.detail.collectionTimeString;
          this.smaIncomeAmtSum = response.sum.incomeAmtSum;
          this.smafeeAmtSum = response.sum.feeAmtSum;
          this.smafeeRoundSum = response.sum.feeRoundSum;
          this.smafeeAmt2Sum = response.sum.feeAmt2Sum;
          //todo 财务项目管理四期 小表合计新增
          this.smaCurrentFeeSum = response.sum.currentFeeSum;
          this.smaJtfrAmtSum = response.sum.jtfrAmtSum;
          this.smaLawProfitSum = response.sum.lawProfitSum;
          if (response.phaseStatus === "7") {
            response.phaseStatus = 0;
          } else if (response.phaseStatus === "8") {
            response.phaseStatus = 1;
          } else if (response.phaseStatus === "9") {
            response.phaseStatus = 2;
          } else if (response.phaseStatus === "10") {
            response.phaseStatus = 3;
          }
          this.jindutiao = response.phaseStatus;
          // this.overDetailBodyDetailList1 = response.detail
          if (response.phaseStatus == 1) {
            this.lrdeshouru = response.detail.incomeAmt;
            this.lrremark = response.remark;
          }

          this.overDetailBodyDetailList1 =
            // [
            response.detail.feeList;
          // ]
          // if(response.status1!=null){
          //         //确认收入日期
          //         this.qrsrdate = response.status1.dynamic_time
          //         //确认收入代办人
          //         this.qrsrdaibanren = response.status1.oper_name
          //         }
          //
          //       if(response.status2!=null){
          //         //录入信息费日期
          //         this.lrffdate = response.status2.dynamic_time
          //         //录入信息费代办人
          //         this.lrffdaibanren = response.status2.oper_name
          //       }
          if (response.status1 != null) {
            //确认信息费日期
            this.qrffdate = response.status1.dynamic_time;
            //确认信息费代办人
            this.qrffdaibanren = response.status1.oper_name;
          }
          if (response.status0) {
            //录入收入日期
            this.lrsrdate = response.status0.dynamic_time;
            //录入收入代办人
            this.lrsrdaibanren = response.status0.oper_name;
          }
        });
        var return2 = {
          id: this.projectId,
          sumFlag: 1,
        };
        // jindutiaoshow

        getLawProjectdeteils(return2).then((response) => {
          this.phaseNoFinish = response.phaseNoFinish;
          this.overDetailBodyDetailList = response.detail_list;
          this.replaceCompanyInfo = response.replaceCompanyInfo;
          this.overDetailBodyDetailList_v2 = response.detail_list_v2;
          this.merage(this.overDetailBodyDetailList_v2);
          this.feecustNameAndfeilvList = response.fee_list;
          //列表合计
          this.feeAmtSum = response.sum.sum_fee_amt;

          this.feeAmt2Sum = response.sum.sum_fee_amt_2;

          this.payAmtSum = response.sum.sum_pay_amt;

          this.differenceAmtSum = response.sum.sum_difference_amt;
          this.feeAlreadySum = response.sum.sum_fee_already;
          this.feeNoAlreadySum = response.sum.sum_fee_no_already;
          this.incomeSum = response.sum.sum_income_sum;
          this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
          this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
          this.feeRoundSum = response.sum.sum_fee_roud;
          this.currentFeeSum = response.sum.sum_current_fee;
          //todo 财务项目管理四期 新增合计
          this.trueComeAmtSum = response.sum.sum_true_come_amt;
          this.serviceFeeSum = response.sum.sum_service_fee;
          this.principalSum = response.sum.sum_principal;
          this.jtfrAmtSum = response.sum.sum_jtfr_amt;
          this.lawProfitSum = response.sum.sum_law_profit;
          this.userList = response.people_list;
          this.loading = false;
        });
        this.dengjidakuantable = false;
        //展示进度条
        this.jindutiaozhansh = true;
      } else {
        this.jindutiaozhansh = false;
      }
    },
    changeCompany() {
      this.$router.push({
        path: "/caiwu/changeCompany",
        query: {
          productId: this.$route.query.productId,
          law: true,
          projectPortfolioCode:this.$route.query.projectPortfolioCode,
        },
      });
    },
    //得到详情
    getprojectDetails() {
      this.projectId = this.$route.query.productId;
      getLawProjectSumByProjectId({ projectId: this.projectId }).then((res) => {
        if (res.code == 200) {
          this.sumData = res.data;
        }
      });
      getFlowAlreadyPaylnfoFromOA({ projectId: this.projectId }).then((res) => {
        if (res.code == 200) {
          this.alreadyPayList = res.rows;
        }
      });
      getProjectCertificateFlagByProjectId({ projectId: this.projectId }).then(
        (res) => {
          if (res.code == 200) {
            this.projectData = res.data;
          }
        }
      );
      this.returnincomeid = this.$route.query.incomeId;
      // this.incomeproId = 712;
      if (this.returnincomeid == undefined) {
        var dataparam = {
          id: this.projectId,
        };
        getDeatil(dataparam).then((response) => {
          this.projectName = response.projectName;
          this.custName = response.custName;
          this.incomeCustName = response.incomeCustName;
        });
        var return3 = {
          id: this.projectId,
        };
        getLawProjectqicideteils(return3).then((response) => {
          if (response.length !== 0) {
            for (let i = 0; i < response.length; i++) {
              if (response[i].phase_status === "7") {
                response[i].phase_status = 0;
              } else if (response[i].phase_status === "8") {
                response[i].phase_status = 1;
              } else if (response[i].phase_status === "9") {
                response[i].phase_status = 2;
              } else if (response[i].phase_status === "10") {
                response[i].phase_status = 3;
              }
            }
          }
          this.yewuqiciList = response;
          //todo 判断是否为1，为1的话，说明没有期次
          if (this.yewuqiciList.length === 1) {
            this.yewuqicideilog = this.yewuqiciList[0].term_month;
            this.jindutiaoshow = false;
          } else {
            for (let i = 0; i < this.yewuqiciList.length; i++) {
              if (this.yewuqiciList[i].id == this.returnincomeid) {
                this.yewuqicideilog = this.yewuqiciList[i].term_month;
              }
            }
          }
        });

        var return2 = {
          id: this.projectId,
          sumFlag: 1,
        };
        // jindutiaoshow

        getLawProjectdeteils(return2).then((response) => {
          this.phaseNoFinish = response.phaseNoFinish;
          this.overDetailBodyDetailList = response.detail_list;
          this.replaceCompanyInfo = response.replaceCompanyInfo;
          this.overDetailBodyDetailList_v2 = response.detail_list_v2;
          this.merage(this.overDetailBodyDetailList_v2);
          this.feecustNameAndfeilvList = response.fee_list;
          //列表合计
          this.feeAmtSum = response.sum.sum_fee_amt;

          this.feeAmt2Sum = response.sum.sum_fee_amt_2;

          this.payAmtSum = response.sum.sum_pay_amt;

          this.differenceAmtSum = response.sum.sum_difference_amt;
          this.feeAlreadySum = response.sum.sum_fee_already;
          this.feeNoAlreadySum = response.sum.sum_fee_no_already;
          this.incomeSum = response.sum.sum_income_sum;
          this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
          this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
          this.feeRoundSum = response.sum.sum_fee_roud;
          this.currentFeeSum = response.sum.sum_current_fee;
          //todo 财务项目管理四期 新增合计
          this.trueComeAmtSum = response.sum.sum_true_come_amt;
          this.serviceFeeSum = response.sum.sum_service_fee;
          this.principalSum = response.sum.sum_principal;
          this.jtfrAmtSum = response.sum.sum_jtfr_amt;
          this.lawProfitSum = response.sum.sum_law_profit;
          this.userList = response.people_list;
          this.loading = false;
        });
      } else {
        var a = 0;
        var returnincomeid1;
        var return3 = {
          id: this.projectId,
        };
        getLawProjectqicideteils(return3).then((response) => {
          this.yewuqiciList = response;
          //todo 判断业务期次集合长度是否为1
          if (this.yewuqiciList.length === 1) {
            this.yewuqicideilog = this.yewuqiciList[0].term_month;
            this.returnincomeid = -999;
          } else {
            //todo 修复从法催待办中跳过来后没有选中期次
            var filter = this.yewuqiciList.filter(
              (t) => t.id == this.returnincomeid
            );
            if (filter.length !== 0) {
              this.yewuqicideilog = filter[0].term_month;
            } else {
              this.yewuqicideilog = this.yewuqiciList[0].term_month;
              this.returnincomeid = -999;
            }
            // for (let i = 0; i < this.yewuqiciList.length; i++) {
            //   if (this.yewuqiciList[i].id == this.returnincomeid) {
            //     this.yewuqicideilog = this.yewuqiciList[i].term_month
            //   } else {
            //     this.yewuqicideilog = this.yewuqiciList[0].term_month;
            //     this.returnincomeid = -999;
            //   }
            // }
            this.getdeatilByincome(this.returnincomeid);
          }
          //todo 之前的逻辑
          // if (response.length !== 0) {
          //   returnincomeid1 = response[0].id;
          // }
          // for (let i = 0; i < response.length; i++) {
          //   if (this.returnincomeid == response[i].id) {
          //     a++;
          //   }
          // }
          // if (a === 0 && response.length !== 0) {
          //   this.returnincomeid = returnincomeid1;
          // }
          // this.getdeatilByincome(this.returnincomeid);
        });
      }
    },
    /** 查询财务项目管理主列表 */
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        custName: null,
        incomeCustName: null,

        projectFlag: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加财务项目管理主";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getProject(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改财务项目管理主";
      });
    },
    /** 提交按钮 */
    async submitForm() {
      if (this.addprojectform.term == 0) {
        if (
          this.addprojectform.termMonth === "" ||
          this.addprojectform.termMonth === null
        ) {
          this.$message.error("请选择时间");
        } else {
          this.addprojectform.projectId = this.projectId;
          const { incomeId, isok } = await addyewuqiciForLaw(
            this.addprojectform
          );
          //新增
          if (isok) {
            //添加成功后初始化时间组件信息
            this.addprojectform.termMonth = "";
            this.addprojectform.termBegin = "";
            this.addprojectform.termEnd = "";
            this.selectDate = [];

            this.getprojectDetails();
            this.open = false;

            this.addprojectform = {
              id: null,
              projectId: null,
              term: "0",
              termMonth: "",
              termBegin: "",
              termEnd: "",
            };

            // this.reload();
            this.$modal.msgSuccess("新增业务期次成功");
            var return3 = { id: this.projectId };
            getProjectqicideteils(return3).then((response) => {
              this.yewuqiciList = response;
              var dataList = this.yewuqiciList.filter((t) => t.id === incomeId);
              var dataObj = dataList[0];
              this.yewuqici = dataObj.id;
              this.updateqici(dataObj);
              this.addprojectform.termMonth = "";
              this.selectDate = [];
              if (this.jindutiaoshow === false) {
                this.jindutiaoshow = true;
              }
            });
          } else {
            this.$message.error(
              "已存在相同期次类型并且日期有重叠的期次信息，请检查！！"
            );
          }
        }
      }
      if (this.addprojectform.term == 1) {
        if (this.selectDate.length === 0) {
          this.$message.error("请选择时间");
        } else {
          this.addprojectform.termBegin = this.selectDate[0];
          this.addprojectform.termEnd = this.selectDate[1];
          this.addprojectform.projectId = this.projectId;
          const { incomeId, isok } = await addyewuqiciForLaw(
            this.addprojectform
          );
          //新增
          if (isok) {
            //添加成功后初始化时间组件信息
            this.addprojectform.termMonth = "";
            this.addprojectform.termBegin = "";
            this.addprojectform.termEnd = "";
            this.selectDate = [];

            this.getprojectDetails();
            this.open = false;

            this.addprojectform = {
              id: null,
              projectId: null,
              term: "0",
              termMonth: "",
              termBegin: "",
              termEnd: "",
            };

            // this.reload();
            this.$modal.msgSuccess("新增业务期次成功");
            var return3 = { id: this.projectId };
            getProjectqicideteils(return3).then((response) => {
              this.yewuqiciList = response;
              var dataList = this.yewuqiciList.filter((t) => t.id === incomeId);
              var dataObj = dataList[0];
              this.yewuqici = dataObj.id;
              this.updateqici(dataObj);
              this.addprojectform.termMonth = "";
              this.selectDate = [];
              if (this.jindutiaoshow === false) {
                this.jindutiaoshow = true;
              }
            });
          } else {
            this.$message.error(
              "已存在相同期次类型并且日期有重叠的期次信息，请检查！！"
            );
          }
        }
      }
      //   this.addprojectform.termBegin = this.selectDate[0]
      //   this.addprojectform.termEnd = this.selectDate[1]
      //   this.addprojectform.projectId =  this.projectId;
      //   const { isok } = await addyewuqiciForLaw(this.addprojectform);
      //   //新增
      //   if(isok){
      //     this.getprojectDetails();
      //     this.open = false;
      //
      //     this.addprojectform={
      //       id:null,
      //       projectId:null,
      //       term:'0',
      //       termMonth:'',
      //       termBegin:'',
      //       termEnd:''
      //     }
      //
      // this.reload();
      // this.$modal.msgSuccess("新增业务期次成功");
      // }else{
      //   this.$message.error('已存在相同期次类型并且日期有重叠的期次信息，请检查！！');
      // }
    },
    //提交修改
    async submitUpdateForm() {
      this.addprojectform.termBegin = this.selectDate[0];
      this.addprojectform.termEnd = this.selectDate[1];
      this.addprojectform.projectId = this.projectId;
      if (this.addprojectform.term == 1) {
        this.addprojectform.termMonth = "";
      }
      //修改
      const { isok } = await updataYewuqici(this.addprojectform);
      if (isok) {
        this.$modal.msgSuccess("修改业务期次成功");
        this.updateprojectqcdeilog = false;
        this.getprojectDetails();
        this.addprojectform = {
          id: null,
          projectId: null,
          term: "0",
          termMonth: "",
          termBegin: "",
          termEnd: "",
        };
        this.reload();
      } else {
        this.$message.error(
          "已存在相同期次类型并且日期有重叠的期次信息，请检查！！"
        );
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除财务项目管理主编号为"' + ids + '"的数据项？')
        .then(function () {
          return delProject(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 备注显示更多按钮 */
    textDetail(remark) {
      this.remarkDeliog = true;
      this.remarkObj = remark;
    },

    closeRemarkDetail() {
      this.remarkDeliog = false;
      this.remarkObj = null;
    },

    /** 导出按钮操作 */
    handleExport() {
      var exportParams = {
        projecId: this.projectId,
        tcustName: this.custName,
        incomeCustName: this.incomeCustName,
        projectName: this.projectName,
      };

      this.download(
        "cwxmgl/cust/over/detail/exportForLaw",
        {
          ...exportParams,
        },
        this.projectName + `_${new Date().getTime()}.xlsx`
      );
    },
    //是否有二级下拉框
    xuanzeerjifuwushang(val, domain, index) {
      if (val === "0") {
        //隐藏
        domain.erjifuwushangShow = false;
        this.shouruForm.fuwushang[index].erjifuwushang = [
          {
            hebingId: index,
            erjifuwushangName: null,
            erjifuwushangIncome: null,
          },
        ];
      }
      if (val === "1") {
        //显示
        domain.erjifuwushangShow = true;
      }
    },
    //一级服务商求和
    changeIncome(index) {
      if (this.shouruForm.fuwushang[index].yijifuwushangIncome === null) {
        this.shouruForm.fuwushang[index].yijifuwushangIncome = 0;
      }
      this.shouruheji = 0;
      for (let i = 0; i < this.shouruForm.fuwushang.length; i++) {
        this.shouruheji = (
          Number(this.shouruheji) +
          Number(this.shouruForm.fuwushang[i].yijifuwushangIncome)
        ).toFixed(2);
      }
    },
    //二级服务商求和
    erjifuwushangIncomeSum(index, index1) {
      if (
        this.shouruForm.fuwushang[index].erjifuwushang[index1]
          .erjifuwushangIncome === null
      ) {
        this.shouruForm.fuwushang[index].erjifuwushang[
          index1
        ].erjifuwushangIncome = 0;
      }
      this.shouruForm.fuwushang[index].yijifuwushangIncome = 0;
      for (
        let i = 0;
        i < this.shouruForm.fuwushang[index].erjifuwushang.length;
        i++
      ) {
        this.shouruForm.fuwushang[index].yijifuwushangIncome = (
          Number(this.shouruForm.fuwushang[index].yijifuwushangIncome) +
          Number(
            this.shouruForm.fuwushang[index].erjifuwushang[i]
              .erjifuwushangIncome
          )
        ).toFixed(2);
      }
      this.changeIncome(index);
    },
    //获取下拉框属性
    getxiala() {
      projectShowLawxiala().then((response) => {
        this.yijifuwushangList = response.yijifuwushangList;
        this.erjifuwushangList = response.erjifuwushangList;
        // this.feeCustNames = response.feeList;
        this.feeCustNames = response.custFeeList;
      });
    },
    //一级服务商下拉框属性
    querySearchyijifuwushang(queryString, cb) {
      var restaurants = this.yijifuwushangList;
      // if (restaurants.length !== 0) {
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
      // } else {
      //   cb([]);
      // }
    },
    //二级服务商下拉框属性
    querySearcherjifuwushang(queryString, cb) {
      var restaurants = this.erjifuwushangList;
      // if (restaurants.length !== 0) {
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
      // } else {
      //   cb([]);
      // }
    },
    //出信息费公司下拉框属性
    querySearchCustNameList(queryString, cb) {
      var restaurants = this.feeCustNames;
      // if (restaurants.length !== 0) {
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    //信息费公司下拉框属性
    querySearchFeeList(queryString, cb) {
      var restaurants = [];
      for (let i = 0; i < this.feecustNameAndfeilvList.length; i++) {
        var a = {
          label: this.feecustNameAndfeilvList[i].custName,
          value: this.feecustNameAndfeilvList[i].custName,
        };
        restaurants.push(a);
      }
      // var label1 = this.feecustNameAndfeilvList[0].custName;
      // this.feeCompanyForm.custId = this.feecustNameAndfeilvList[0].id;
      // var restaurants = [
      //   {
      //     label: label1,
      //     value: value1
      //   }
      // ]

      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    withdraw(v) {
      this.$confirm(
        "是否撤销本次替换操作？替换后，该次替换操作的信息费公司将不能再被查询统计到，该信息费公司期间发生的信息费记录将被替换前的公司继承",
        "撤销替换",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          revocationReplaceFeeCompanyInfo(v).then((res) => {
            if (res.code == 200) {
              this.$message.success("撤销替换成功");
              var return2 = {
                id: this.projectId,
                sumFlag: 1,
              };
              // jindutiaoshow

              getLawProjectdeteils(return2).then((response) => {
                this.phaseNoFinish = response.phaseNoFinish;
                this.overDetailBodyDetailList = response.detail_list;
                this.overDetailBodyDetailList_v2 = response.detail_list_v2;
                this.merage(this.overDetailBodyDetailList_v2);
                this.replaceCompanyInfo = response.replaceCompanyInfo;
                this.feecustNameAndfeilvList = response.fee_list;
                //列表合计
                this.feeAmtSum = response.sum.sum_fee_amt;

                this.feeAmt2Sum = response.sum.sum_fee_amt_2;

                this.payAmtSum = response.sum.sum_pay_amt;

                this.differenceAmtSum = response.sum.sum_difference_amt;
                this.feeAlreadySum = response.sum.sum_fee_already;
                this.feeNoAlreadySum = response.sum.sum_fee_no_already;
                this.incomeSum = response.sum.sum_income_sum;
                this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
                this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
                this.feeRoundSum = response.sum.sum_fee_roud;
                this.currentFeeSum = response.sum.sum_current_fee;
                //todo 财务项目管理四期 新增合计
                this.trueComeAmtSum = response.sum.sum_true_come_amt;
                this.serviceFeeSum = response.sum.sum_service_fee;
                this.principalSum = response.sum.sum_principal;
                this.jtfrAmtSum = response.sum.sum_jtfr_amt;
                this.lawProfitSum = response.sum.sum_law_profit;
                this.userList = response.people_list;
                this.loading = false;
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    custIdHuoQu() {
      for (let i = 0; i < this.feecustNameAndfeilvList.length; i++) {
        if (
          this.feeCompanyForm.feeCustName ===
          this.feecustNameAndfeilvList[i].custName
        ) {
          this.feeCompanyForm.custId = this.feecustNameAndfeilvList[i].id;
          this.nextToFeeInfoFlag = true;
          break;
        } else {
          this.nextToFeeInfoFlag = false;
        }
      }
    },
    createFilter(queryString) {
      return (companyList) => {
        return (
          companyList.value.toLowerCase().indexOf(queryString.toLowerCase()) >
          -1
        );
      };
    },
    //删除服务商
    removeDomainFuwushang(item) {
      var index = this.shouruForm.fuwushang.indexOf(item);
      if (index !== -1) {
        var i = this.shouruForm.fuwushang[index].yijifuwushangIncome;
        this.shouruheji = (this.shouruheji - i).toFixed(2);
        this.shouruForm.fuwushang.splice(index, 1);
      }
    },
    //添加服务商
    addDomainFuwushang() {
      const index = this.shouruForm.fuwushang.length - 1;
      this.shouruForm.fuwushang.push({
        hebingId: index + 1,
        yijifuwushangName: "",
        yijifuwushangIncome: null,
        erjifuwushangShow: false,
        erjifuwushangSelect: [
          {
            value: "0",
            label: "无",
          },
          {
            value: "1",
            label: "有",
          },
        ],
        valuetype: "0",

        erjifuwushang: [
          {
            hebingId: index + 1,
            erjifuwushangName: null,
            erjifuwushangIncome: null,
          },
        ],
      });
    },
    //删除二级服务商
    removeDomainErjiFuwushang(index, item) {
      var index1 = this.shouruForm.fuwushang[index].erjifuwushang.indexOf(item);
      if (index1 !== -1) {
        var i =
          this.shouruForm.fuwushang[index].erjifuwushang[index1]
            .erjifuwushangIncome;
        this.shouruForm.fuwushang[index].yijifuwushangIncome = (
          this.shouruForm.fuwushang[index].yijifuwushangIncome - i
        ).toFixed(2);
        this.shouruheji = (this.shouruheji - i).toFixed(2);
        this.shouruForm.fuwushang[index].erjifuwushang.splice(index1, 1);
      }
    },
    //添加二级服务商
    addDomainErjiFuwushang(index) {
      this.shouruForm.fuwushang[index].erjifuwushang.push({
        hebingId: index,
        erjifuwushangName: null,
        erjifuwushangIncome: null,
      });
    },
    //从第一个录入收入的页面跳往信息费公司录入页面
    nextToFeeCompany() {
      var nameCheck = 0;
      if (this.collectionTime !== null) {
        let strrr = this.collectionTime;
        this.collectionTimeString =
          strrr.substring(0, 4) + "年" + strrr.substring(5, 7) + "月";
      }
      var a = 0;
      for (let i = 0; i < this.shouruForm.fuwushang.length; i++) {
        //检测一级服务商是否重名
        if (i > 0) {
          if (
            this.shouruForm.fuwushang[i].yijifuwushangName ===
            this.shouruForm.fuwushang[i - 1].yijifuwushangName
          ) {
            nameCheck++;
          }
        }
        //检测输入款信息是否输入
        if (this.shouruForm.fuwushang[i].erjifuwushangShow === false) {
          //是一级服务商的
          if (
            this.shouruForm.fuwushang[i].yijifuwushangName === null ||
            this.shouruForm.fuwushang[i].yijifuwushangName === "" ||
            this.shouruForm.fuwushang[i].yijifuwushangIncome === null ||
            this.shouruForm.fuwushang[i].yijifuwushangIncome === ""
          ) {
            a++;
          }
        } else {
          //是二级服务商的
          for (
            let j = 0;
            j < this.shouruForm.fuwushang[i].erjifuwushang.length;
            j++
          ) {
            if (
              this.shouruForm.fuwushang[i].erjifuwushang[j]
                .erjifuwushangName === null ||
              this.shouruForm.fuwushang[i].erjifuwushang[j]
                .erjifuwushangName === "" ||
              this.shouruForm.fuwushang[i].erjifuwushang[j]
                .erjifuwushangIncome === null ||
              this.shouruForm.fuwushang[i].erjifuwushang[j]
                .erjifuwushangIncome === ""
            ) {
              a++;
            }
          }
        }
      }
      if (a > 0) {
        this.$message.error("请完善信息后再进行下一步！");
      } else {
        if (nameCheck > 0) {
          this.$message.error(
            "一级服务商名称不能重复，请检查填写的服务商名称！"
          );
        } else {
          //查找最近一个月的出信息费公司和信息费公司
          var termMonth = {
            //期次id
            projectId: this.projectId,
            termMonth: this.yewuqicideilog,
          };
          findLawRecentlyCustFeeCompanyAndFeeCompany(termMonth).then(
            (response) => {
              if (response !== null) {
                // this.feeCompanyForm.custId = response.custId;
                // this.feeCompanyForm.custName = response.custName;
                // this.feeCompanyForm.feeCustName = response.feeCustName;
              }
              this.custIdHuoQu();
            }
          );
          this.lurudeilog = false;
          this.querensrdeilog = true;
        }
      }
      // if (this.shouruForm.fuwushang[0].yijifuwushangIncome !== null && this.shouruForm.fuwushang[0].yijifuwushangName !== '') {
      //   this.lurudeilog = false;
      //   this.querensrdeilog = true;
      // } else {
      //   this.$modal.msgError("请完善信息后再进行下一步！");
      // }
    },
    //从第二个信息费公司录入页面返回第一个录入收入页面
    backToShouru() {
      this.querensrdeilog = false;
      this.lurudeilog = true;
      this.nextToFeeInfoFlag = false;
    },
    //从第二个信息费公司录入页面跳往信息费信息详情页面
    nextToFeeInfo() {
      var filter = this.feecustNameAndfeilvList.filter(
        (t) => t.custName === this.feeCompanyForm.feeCustName
      );
      if (filter.length !== 0) {
        this.feeCompanyForm.rate = filter[0].rate;
      }
      //找到输入的所有的一级服务商名称集合
      const yijifuwushang = this.shouruForm.fuwushang;
      const serviceProviderNameList = [];
      for (let i = 0; i < yijifuwushang.length; i++) {
        const serviceProviderName = yijifuwushang[i].yijifuwushangName;
        serviceProviderNameList.push(serviceProviderName);
      }
      var guaqijihe = [];
      //查询挂起的信息费合
      findLawSuspendFlagIsOneSum(serviceProviderNameList).then((response) => {
        if (response.length !== 0) {
          guaqijihe = response;
        }
        if (
          this.feeCompanyForm.custName !== "" &&
          this.feeCompanyForm.feeCustName !== "" &&
          typeof this.feeCompanyForm.custName !== "undefined" &&
          typeof this.feeCompanyForm.feeCustName !== "undefined"
        ) {
          //然后对服务商收入和对出信息费进行合并组装
          const list1 = this.shouruForm.fuwushang;
          const feeObj = this.feeCompanyForm;
          const objList = [];
          for (let i = 0; i < list1.length; i++) {
            //组装一个一级服务商
            if (list1[i].erjifuwushangShow === true) {
              //有二级服务商
              const objFirst = {
                hebingId: i,
                projectId: this.projectId,
                termMonth: this.yewuqicideilog,
                serviceProviderName: list1[i].yijifuwushangName,
                serviceProviderIncome: list1[i].yijifuwushangIncome,
                yijifuwushangName: list1[i].yijifuwushangName,
                yijifuwushangIncome: Number(
                  list1[i].yijifuwushangIncome
                ).toFixed(2),
                erjifuwushangShow: true,
                serviceProviderSecondName: null,
                serviceProviderSecondIncome: null,
                //出信息费公司
                custName: feeObj.custName,
                //信息费公司
                feeCustName: feeObj.feeCustName,
                //真实回款金额
                trueComeAmt: null,
                //服务费
                serviceFee: null,
                //本金
                principal: null,
                //信息费
                feeAmt: null,
                //信息费取整
                feeRound: null,
                //提成信息费
                feeAmt2: null,
                //毛利
                grossProfitAmt: null,
                //提成毛利
                grossProfitAmt2: null,
                // serviceProviderFlag: 1,
                // custId: feeObj.custId,
                // phaseId: this.phaseId,
                // serviceProviderFeeAmtSuspendFlagIsOne: null,
                // serviceProviderSecondFeeAmtSuspendFlagIsOne: null,
                // feeAmtSuspendFlagIsOneId: null,
                //服务商标识
                serviceProviderFlag: 1,
                //信息费公司id
                custId: feeObj.custId,
                //期次id
                phaseId: this.phaseId,
                //一级服务商挂起金额
                serviceProviderFeeAmtSuspendFlagIsOne: null,
                //二级服务商挂起金额
                serviceProviderSecondFeeAmtSuspendFlagIsOne: null,
                //挂起金额对应的信息费id
                feeAmtSuspendFlagIsOneId: null,
                //本期信息费
                currentFee: null,
                //todo 财务项目管理四期 借条分润
                jtfrAmt: null,
                //todo 财务项目管理四期 一级借条分润
                serviceProviderJtfrAmt: null,
                //todo 财务项目管理四期 法催利润
                lawProfit: null,
                //累计挂起金额
                feeAmtSuspendFlagSum: null,
                feeAmtSuspendFlagSum1: null,
                //挂起清除标志
                guaqiqingchuFlag: null,
                remark: this.lrremark,
                collectionTime: this.collectionTime,
              };
              for (let j = 0; j < guaqijihe.length; j++) {
                if (
                  guaqijihe[j].serviceProviderFlag === "1" &&
                  guaqijihe[j].serviceProvider ==
                    objFirst.serviceProviderName &&
                  guaqijihe[j].feeAmtSuspendFlagIsOne !== null
                ) {
                  objFirst.serviceProviderFeeAmtSuspendFlagIsOne =
                    guaqijihe[j].feeAmtSuspendFlagIsOne;
                  objFirst.feeAmtSuspendFlagSum = this.toFixedFun(
                    guaqijihe[j].feeAmtSuspendFlagIsOne,
                    2
                  );
                  objFirst.feeAmtSuspendFlagSum1 = this.toFixedFun(
                    guaqijihe[j].feeAmtSuspendFlagIsOne,
                    2
                  );
                  objFirst.feeAmtSuspendFlagIsOneId = guaqijihe[j].id;
                }
              }
              objList.push(objFirst);
              for (let j = 0; j < list1[i].erjifuwushang.length; j++) {
                // if (list1[i].erjifuwushang[0].erjifuwushangName !== ''){
                //   //说明这个是有服务商的
                // }
                const obj = {
                  hebingId: i,
                  projectId: this.projectId,
                  termMonth: this.yewuqicideilog,
                  serviceProviderName: list1[i].yijifuwushangName,
                  serviceProviderIncome: null,
                  yijifuwushangName: null,
                  yijifuwushangIncome: null,
                  erjifuwushangShow: false,
                  serviceProviderSecondName:
                    list1[i].erjifuwushang[j].erjifuwushangName,
                  serviceProviderSecondIncome:
                    list1[i].erjifuwushang[j].erjifuwushangIncome,
                  //出信息费公司
                  custName: feeObj.custName,
                  //信息费公司
                  feeCustName: feeObj.feeCustName,
                  //真实回款金额
                  trueComeAmt: null,
                  //服务费
                  serviceFee: null,
                  //本金
                  principal: null,
                  //信息费
                  feeAmt: null,
                  //信息费取整
                  feeRound: null,
                  //提成信息费
                  feeAmt2: null,
                  //毛利
                  grossProfitAmt: null,
                  //提成毛利
                  grossProfitAmt2: null,
                  // serviceProviderFlag: 2,
                  // custId: feeObj.custId,
                  // phaseId: this.phaseId,
                  // serviceProviderFeeAmtSuspendFlagIsOne: null,
                  // serviceProviderSecondFeeAmtSuspendFlagIsOne: null,
                  // feeAmtSuspendFlagIsOneId: null,
                  //服务商标识
                  serviceProviderFlag: 2,
                  //信息费公司id
                  custId: feeObj.custId,
                  //期次id
                  phaseId: this.phaseId,
                  //一级服务商挂起金额
                  serviceProviderFeeAmtSuspendFlagIsOne: null,
                  //二级服务商挂起金额
                  serviceProviderSecondFeeAmtSuspendFlagIsOne: null,
                  //挂起金额对应的信息费id
                  feeAmtSuspendFlagIsOneId: null,
                  //本期信息费
                  currentFee: null,
                  //todo 财务项目管理四期 借条分润
                  jtfrAmt: null,
                  //todo 财务项目管理四期 法催利润
                  lawProfit: null,
                  //累计挂起金额
                  feeAmtSuspendFlagSum: null,
                  feeAmtSuspendFlagSum1: null,
                  //挂起清除标志
                  guaqiqingchuFlag: null,
                  remark: this.lrremark,
                  collectionTime: this.collectionTime,
                };
                for (let j = 0; j < guaqijihe.length; j++) {
                  if (
                    guaqijihe[j].serviceProviderFlag === "2" &&
                    guaqijihe[j].serviceProviderSecond ==
                      obj.serviceProviderSecondName &&
                    guaqijihe[j].feeAmtSuspendFlagIsOne !== null
                  ) {
                    obj.serviceProviderSecondFeeAmtSuspendFlagIsOne =
                      guaqijihe[j].feeAmtSuspendFlagIsOne;
                    obj.feeAmtSuspendFlagSum = this.toFixedFun(
                      guaqijihe[j].feeAmtSuspendFlagIsOne,
                      2
                    );
                    obj.feeAmtSuspendFlagSum1 = this.toFixedFun(
                      guaqijihe[j].feeAmtSuspendFlagIsOne,
                      2
                    );
                    obj.feeAmtSuspendFlagIsOneId = guaqijihe[j].id;
                  }
                }
                objList.push(obj);
              }
            } else {
              //没有二级服务商
              const objFirst = {
                hebingId: i,
                projectId: this.projectId,
                termMonth: this.yewuqicideilog,
                serviceProviderName: list1[i].yijifuwushangName,
                serviceProviderIncome: list1[i].yijifuwushangIncome,
                yijifuwushangName: list1[i].yijifuwushangName,
                yijifuwushangIncome: Number(
                  list1[i].yijifuwushangIncome
                ).toFixed(2),
                erjifuwushangShow: false,
                serviceProviderSecondName: null,
                serviceProviderSecondIncome: null,
                //出信息费公司
                custName: feeObj.custName,
                //信息费公司
                feeCustName: feeObj.feeCustName,
                //真实回款金额
                trueComeAmt: null,
                //服务费
                serviceFee: null,
                //本金
                principal: null,
                //信息费
                feeAmt: null,
                //信息费取整
                feeRound: null,
                //提成信息费
                feeAmt2: null,
                //毛利
                grossProfitAmt: null,
                //提成毛利
                grossProfitAmt2: null,
                //服务商标识
                serviceProviderFlag: 1,
                //信息费公司id
                custId: feeObj.custId,
                //期次id
                phaseId: this.phaseId,
                //一级服务商挂起金额
                serviceProviderFeeAmtSuspendFlagIsOne: null,
                //二级服务商挂起金额
                serviceProviderSecondFeeAmtSuspendFlagIsOne: null,
                //挂起金额对应的信息费id
                feeAmtSuspendFlagIsOneId: null,
                //本期信息费
                currentFee: null,
                //todo 财务项目管理四期 借条分润
                jtfrAmt: null,
                //todo 财务项目管理四期 一级借条分润
                serviceProviderJtfrAmt: null,
                //todo 财务项目管理四期 法催利润
                lawProfit: null,
                //累计挂起金额
                feeAmtSuspendFlagSum: null,
                feeAmtSuspendFlagSum1: null,
                //挂起清除标志
                guaqiqingchuFlag: null,
                remark: this.lrremark,
                collectionTime: this.collectionTime,
              };
              for (let j = 0; j < guaqijihe.length; j++) {
                if (
                  guaqijihe[j].serviceProviderFlag === "1" &&
                  guaqijihe[j].serviceProvider ==
                    objFirst.serviceProviderName &&
                  guaqijihe[j].feeAmtSuspendFlagIsOne !== null
                ) {
                  objFirst.serviceProviderFeeAmtSuspendFlagIsOne =
                    guaqijihe[j].feeAmtSuspendFlagIsOne;
                  objFirst.feeAmtSuspendFlagSum = this.toFixedFun(
                    guaqijihe[j].feeAmtSuspendFlagIsOne,
                    2
                  );
                  objFirst.feeAmtSuspendFlagSum1 = this.toFixedFun(
                    guaqijihe[j].feeAmtSuspendFlagIsOne,
                    2
                  );
                  objFirst.feeAmtSuspendFlagIsOneId = guaqijihe[j].id;
                }
              }
              objList.push(objFirst);
            }
          }
          this.incomeAndFeeForm.incomeAndFeeList = objList;
          this.querensrdeilog = false;
          this.lurufanfeideilog = true;
        } else {
          this.$modal.msgError("请完善信息后再进行下一步！");
        }
      });

      // var guaqijihe = [];
      // //查询挂起的信息费合
      // findLawSuspendFlagIsOneSum(serviceProviderNameList).then(response => {
      //   console.log('dsaddddddd', response)
      //   if (response.length !== 0) {
      //     guaqijihe = response;
      //   }
      // })
      // if (this.feeCompanyForm.custName !=='' && this.feeCompanyForm.feeCustName !== ''){
      //   //然后对服务商收入和对出信息费进行合并组装
      //   const list1 = this.shouruForm.fuwushang;
      //   const feeObj = this.feeCompanyForm;
      //   const objList = [];
      //   for (let i = 0; i < list1.length; i++) {
      //
      //     //组装一个一级服务商
      //     if (list1[i].erjifuwushangShow === true) {
      //       //有二级服务商
      //       const objFirst = {
      //         hebingId: i,
      //         projectId: this.projectId,
      //         termMonth: this.yewuqicideilog,
      //         serviceProviderName: list1[i].yijifuwushangName,
      //         serviceProviderIncome: list1[i].yijifuwushangIncome,
      //         yijifuwushangName: list1[i].yijifuwushangName,
      //         yijifuwushangIncome: Number(list1[i].yijifuwushangIncome).toFixed(2),
      //         erjifuwushangShow: true,
      //         serviceProviderSecondName: null,
      //         serviceProviderSecondIncome: null,
      //         //出信息费公司
      //         custName: feeObj.custName,
      //         //信息费公司
      //         feeCustName: feeObj.feeCustName,
      //         //真实回款金额
      //         trueComeAmt: null,
      //         //服务费
      //         serviceFee: null,
      //         //本金
      //         principal: null,
      //         //信息费
      //         feeAmt: null,
      //         //信息费取整
      //         feeRound: null,
      //         //提成信息费
      //         feeAmt2: null,
      //         //毛利
      //         grossProfitAmt: null,
      //         //提成毛利
      //         grossProfitAmt2: null,
      //         serviceProviderFlag: 1,
      //         custId: feeObj.custId,
      //         phaseId: this.phaseId
      //       }
      //       console.log('0', guaqijihe)
      //       for (let j = 0; j < guaqijihe.length; j++) {
      //         console.log('1', guaqijihe[j])
      //         if (guaqijihe[j].serviceProvider == list1[i].yijifuwushangName && guaqijihe[j].feeAmtSuspendFlagIsOne !== null) {
      //           objFirst.feeAmt = guaqijihe[j].feeAmtSuspendFlagIsOne;
      //         }
      //       }
      //       console.log('objFirst1', objFirst)
      //       objList.push(objFirst);
      //       for (let j = 0; j < list1[i].erjifuwushang.length; j++) {
      //         // if (list1[i].erjifuwushang[0].erjifuwushangName !== ''){
      //         //   //说明这个是有服务商的
      //         // }
      //         const obj = {
      //           hebingId: i,
      //           projectId: this.projectId,
      //           termMonth: this.yewuqicideilog,
      //           serviceProviderName: list1[i].yijifuwushangName,
      //           serviceProviderIncome: null,
      //           yijifuwushangName:null,
      //           yijifuwushangIncome:null,
      //           erjifuwushangShow: false,
      //           serviceProviderSecondName: list1[i].erjifuwushang[j].erjifuwushangName,
      //           serviceProviderSecondIncome: list1[i].erjifuwushang[j].erjifuwushangIncome,
      //           //出信息费公司
      //           custName: feeObj.custName,
      //           //信息费公司
      //           feeCustName: feeObj.feeCustName,
      //           //真实回款金额
      //           trueComeAmt: null,
      //           //服务费
      //           serviceFee: null,
      //           //本金
      //           principal: null,
      //           //信息费
      //           feeAmt: null,
      //           //信息费取整
      //           feeRound: null,
      //           //提成信息费
      //           feeAmt2: null,
      //           //毛利
      //           grossProfitAmt: null,
      //           //提成毛利
      //           grossProfitAmt2: null,
      //           serviceProviderFlag: 2,
      //           custId: feeObj.custId,
      //           phaseId: this.phaseId
      //         }
      //         for (let j = 0; j < guaqijihe.length; j++) {
      //           console.log('2', guaqijihe[j])
      //           if (guaqijihe[j].serviceProvider == list1[i].yijifuwushangName && guaqijihe[j].feeAmtSuspendFlagIsOne !== null) {
      //             obj.feeAmt = guaqijihe[j].feeAmtSuspendFlagIsOne;
      //           }
      //         }
      //         console.log('obj', obj)
      //         objList.push(obj);
      //       }
      //     } else {
      //       //没有二级服务商
      //       const objFirst = {
      //         hebingId: i,
      //         projectId: this.projectId,
      //         termMonth: this.yewuqicideilog,
      //         serviceProviderName: list1[i].yijifuwushangName,
      //         serviceProviderIncome: list1[i].yijifuwushangIncome,
      //         yijifuwushangName: list1[i].yijifuwushangName,
      //         yijifuwushangIncome: Number(list1[i].yijifuwushangIncome).toFixed(2),
      //         erjifuwushangShow: false,
      //         serviceProviderSecondName: null,
      //         serviceProviderSecondIncome: null,
      //         //出信息费公司
      //         custName: feeObj.custName,
      //         //信息费公司
      //         feeCustName: feeObj.feeCustName,
      //         //真实回款金额
      //         trueComeAmt: null,
      //         //服务费
      //         serviceFee: null,
      //         //本金
      //         principal: null,
      //         //信息费
      //         feeAmt: null,
      //         //信息费取整
      //         feeRound: null,
      //         //提成信息费
      //         feeAmt2: null,
      //         //毛利
      //         grossProfitAmt: null,
      //         //提成毛利
      //         grossProfitAmt2: null,
      //         serviceProviderFlag: 1,
      //         custId: feeObj.custId,
      //         phaseId: this.phaseId
      //       }
      //       for (let j = 0; j < guaqijihe.length; j++) {
      //         console.log('3', guaqijihe[j])
      //         if (guaqijihe[j].serviceProvider == list1[i].yijifuwushangName && guaqijihe[j].feeAmtSuspendFlagIsOne !== null) {
      //           objFirst.feeAmt = guaqijihe[j].feeAmtSuspendFlagIsOne;
      //         }
      //       }
      //       console.log('objFirst2', objFirst)
      //       objList.push(objFirst);
      //     }
      //   }
      //   this.incomeAndFeeForm.incomeAndFeeList = objList;
      //   this.querensrdeilog = false;
      //   this.lurufanfeideilog = true;
      // } else {
      //   this.$modal.msgError("请完善信息后再进行下一步！");
      // }
    },
    backToFeeCompany() {
      this.lurufanfeideilog = false;
      this.querensrdeilog = true;
      this.feeAmtRedFlag = false;
      this.guaqiFeeAmtFlag = false;
      this.fanfeiheji = 0;
      this.benqifanfeiheji = 0;
      this.fanfeiquzhengheji = 0;
      this.tichengfanfeiheji = 0;
      this.maolijine = 0;
      this.tichengmaolijine = 0;
    },
    //todo 财务项目管理四期，借条分润的计算
    changeJtfrAmt(index) {
      //根据索引找到输入对应的对象
      var incomeAndFeeObj = this.incomeAndFeeForm.incomeAndFeeList[index];
      //输入的借条分润
      var jtfrAmt = incomeAndFeeObj.jtfrAmt;
      //真实回款金额
      var trueComeAmt = incomeAndFeeObj.trueComeAmt;
      //服务费
      var serviceFee = incomeAndFeeObj.serviceFee;
      //信息费取整
      var feeRound = incomeAndFeeObj.feeRound;
      if (isNaN(Number(feeRound))) {
        feeRound = 0;
      }
      //法催利润
      this.incomeAndFeeForm.incomeAndFeeList[index].lawProfit = Number(
        Number(trueComeAmt) -
          Number(jtfrAmt) -
          Number(serviceFee) -
          Number(feeRound)
      ).toFixed(2);

      let lawProfit = 0;
      let serviceProviderJtfrAmt = 0;
      for (let i = 0; i < this.incomeAndFeeForm.incomeAndFeeList.length; i++) {
        if (
          this.incomeAndFeeForm.incomeAndFeeList[i].hebingId ===
            this.incomeAndFeeForm.incomeAndFeeList[index].hebingId &&
          this.incomeAndFeeForm.incomeAndFeeList[i].yijifuwushangName === null
        ) {
          //找到这个二级对应的hebingId的所有二级
          lawProfit += Number(
            this.incomeAndFeeForm.incomeAndFeeList[i].lawProfit
          );
          serviceProviderJtfrAmt += Number(
            this.incomeAndFeeForm.incomeAndFeeList[i].jtfrAmt
          );
        }
      }
      for (let i = 0; i < index; i++) {
        if (
          this.incomeAndFeeForm.incomeAndFeeList[i].yijifuwushangName !==
            null &&
          this.incomeAndFeeForm.incomeAndFeeList[i].hebingId ===
            this.incomeAndFeeForm.incomeAndFeeList[index].hebingId
        ) {
          //找到了一级，给一级信息费总和
          this.incomeAndFeeForm.incomeAndFeeList[i].lawProfit =
            Number(lawProfit).toFixed(2);
          this.incomeAndFeeForm.incomeAndFeeList[i].serviceProviderJtfrAmt =
            Number(serviceProviderJtfrAmt).toFixed(2);
        }
      }

      this.jtfrheji = 0;
      this.facuilirunheji = 0;
      for (let i = 0; i < this.incomeAndFeeForm.incomeAndFeeList.length; i++) {
        if (
          this.incomeAndFeeForm.incomeAndFeeList[i].jtfrAmt !== null &&
          this.incomeAndFeeForm.incomeAndFeeList[i].jtfrAmt !== ""
        ) {
          this.jtfrheji = (
            Number(this.jtfrheji) +
            Number(this.incomeAndFeeForm.incomeAndFeeList[i].jtfrAmt)
          ).toFixed(2);
        }
        if (
          this.incomeAndFeeForm.incomeAndFeeList[i].yijifuwushangName !== null
        ) {
          this.facuilirunheji = (
            Number(this.facuilirunheji) +
            Number(this.incomeAndFeeForm.incomeAndFeeList[i].lawProfit)
          ).toFixed(2);
        }
      }
    },
    //真实回款金额，服务费，本金计算
    changeTrueComeAmt(index) {
      //控制红字的标识
      var feeAmtfushuCount = 0;
      // if (this.feeAmtRedFlag === true) {
      //   feeAmtfushuCount = 1;
      // }
      //挂起的信息费扣除标识 - 只要有一个被扣除了，就显示字
      var guaqiFeeAmtCount = 0;
      for (let i = 0; i < this.incomeAndFeeForm.incomeAndFeeList.length; i++) {
        if (
          Number(
            this.incomeAndFeeForm.incomeAndFeeList[i].feeAmtSuspendFlagSum1
          ) < 0
        ) {
          if (
            Number(
              this.incomeAndFeeForm.incomeAndFeeList[i].feeAmtSuspendFlagSum
            ) >= 0
          ) {
            guaqiFeeAmtCount++;
          }
        }
        if (this.incomeAndFeeForm.incomeAndFeeList[i].currentFee < 0) {
          feeAmtfushuCount++;
        }
      }
      // if (this.guaqiFeeAmtFlag === true) {
      //   guaqiFeeAmtCount = 1;
      // }
      //初始化金额
      if (this.incomeAndFeeForm.incomeAndFeeList[index].trueComeAmt === null) {
        this.incomeAndFeeForm.incomeAndFeeList[index].trueComeAmt = 0;
      }
      if (this.incomeAndFeeForm.incomeAndFeeList[index].serviceFee === null) {
        this.incomeAndFeeForm.incomeAndFeeList[index].serviceFee = 0;
      }
      if (this.incomeAndFeeForm.incomeAndFeeList[index].principal === null) {
        this.incomeAndFeeForm.incomeAndFeeList[index].principal = 0;
      }
      //税率
      var shuilv;
      //费率
      var feilv;
      for (let i = 0; i < this.feecustNameAndfeilvList.length; i++) {
        if (this.feeCompanyForm.custId === this.feecustNameAndfeilvList[i].id) {
          if (this.feecustNameAndfeilvList[i].taxRate !== "未设置") {
            shuilv = Number(this.feecustNameAndfeilvList[i].taxRate) / 100;
          } else {
            shuilv = 0;
          }
          if (this.feecustNameAndfeilvList[i].rate !== "未设置") {
            feilv = Number(this.feecustNameAndfeilvList[i].rate) / 100;
          } else {
            feilv = 0;
          }
        }
      }
      // if(this.feecustNameAndfeilvList[0].taxRate !== '未设置') {
      //   shuilv = (Number(this.feecustNameAndfeilvList[0].taxRate) / 100);
      // } else {
      //   shuilv = 0;
      // }
      // if (this.feecustNameAndfeilvList[0].rate !== '未设置') {
      //   feilv = (Number(this.feecustNameAndfeilvList[0].rate) / 100);
      // } else {
      //   feilv = 0
      // }
      //计算本期信息费，不管是不是一级服务商，都把信息费给计算出来
      var number = (
        (Number(this.incomeAndFeeForm.incomeAndFeeList[index].trueComeAmt) -
          Number(this.incomeAndFeeForm.incomeAndFeeList[index].serviceFee) -
          Number(this.incomeAndFeeForm.incomeAndFeeList[index].principal) *
            0.03) *
        feilv
      ).toFixed(3);
      number = this.toFixedFun(number, 2);
      //计算累计挂起金额
      var guaqiNumberSum;

      // if (Number(number) < 0) {
      if (
        this.incomeAndFeeForm.incomeAndFeeList[index].feeAmtSuspendFlagSum !==
        null
      ) {
        guaqiNumberSum = Number(
          (
            Number(number) +
            Number(
              this.incomeAndFeeForm.incomeAndFeeList[index]
                .feeAmtSuspendFlagSum1
            )
          ).toFixed(3)
        );
      } else {
        guaqiNumberSum = Number(number);
      }
      // guaqiNumberSum = this.toFixedFun(guaqiNumberSum, 2);
      // this.incomeAndFeeForm.incomeAndFeeList[index].feeAmtSuspendFlagSum = guaqiNumberSum;
      // } else if (Number(number) < 0) {
      //   if (this.incomeAndFeeForm.incomeAndFeeList[index].serviceProviderSecondFeeAmtSuspendFlagIsOne !== null) {
      //     guaqiNumberSum = (Number(number) + Number(this.incomeAndFeeForm.incomeAndFeeList[index].serviceProviderSecondFeeAmtSuspendFlagIsOne)).toFixed(3);
      //   } else {
      //     guaqiNumberSum = Number(number);
      //   }
      guaqiNumberSum = this.toFixedFun(guaqiNumberSum, 2);
      if (Number(guaqiNumberSum) >= 0 || Number(number) < 0) {
        this.incomeAndFeeForm.incomeAndFeeList[index].feeAmtSuspendFlagSum =
          guaqiNumberSum;
      } else {
        this.incomeAndFeeForm.incomeAndFeeList[index].feeAmtSuspendFlagSum =
          this.incomeAndFeeForm.incomeAndFeeList[index].feeAmtSuspendFlagSum1;
      }
      // }
      //计算信息费
      var feeAmt = 0;
      //如果本期信息费 != 累计挂起金额 ，那么说明有挂起而且已经清除
      if (
        Number(number) !==
        Number(
          this.incomeAndFeeForm.incomeAndFeeList[index].feeAmtSuspendFlagSum
        )
      ) {
        if (
          Number(
            this.incomeAndFeeForm.incomeAndFeeList[index].feeAmtSuspendFlagSum
          ) >= 0
        ) {
          //挂起消除大于或等于0，已消除
          feeAmt = Number(
            this.incomeAndFeeForm.incomeAndFeeList[index].feeAmtSuspendFlagSum
          ).toFixed(2);
          this.incomeAndFeeForm.incomeAndFeeList[index].guaqiqingchuFlag = 1;
          guaqiFeeAmtCount++;
        } else {
          //挂起消除小于0，未消除
          this.incomeAndFeeForm.incomeAndFeeList[index].guaqiqingchuFlag = 0;
          //那么与累计挂起金额无关，本期信息费就等于信息费
          feeAmt = number;
          feeAmt = 0.0;
        }
      } else {
        //本期信息费 = 累计挂起金额，说明没有要清除挂起的金额。
        if (
          Number(
            this.incomeAndFeeForm.incomeAndFeeList[index].feeAmtSuspendFlagSum
          ) > 0
        ) {
          feeAmt = Number(
            this.incomeAndFeeForm.incomeAndFeeList[index].feeAmtSuspendFlagSum
          ).toFixed(2);
        }
      }
      // else if (this.incomeAndFeeForm.incomeAndFeeList[index].serviceProviderSecondFeeAmtSuspendFlagIsOne !== null && Number(number) + this.incomeAndFeeForm.incomeAndFeeList[index].serviceProviderSecondFeeAmtSuspendFlagIsOne > 0) {
      //   feeAmt = Number(number) + this.incomeAndFeeForm.incomeAndFeeList[index].serviceProviderSecondFeeAmtSuspendFlagIsOne;
      //   this.incomeAndFeeForm.incomeAndFeeList[index].guaqiqingchuFlag = 1;
      //   guaqiFeeAmtCount++;
      // }
      //计算信息费取整，需要分两种情况。信息费可以以百取整的，信息费不可以以百取整的
      //todo 百位取整 start
      // var baiweiquzheng = number / 1000 | 0;
      //todo 百位取整 end
      // var shiweiquzheng = number / 100 | 0;
      var shiweiquzheng = (feeAmt / 100) | 0;
      //定义取整接收变量
      var feeRound;
      //定义提成信息费接收变量
      var feeAmt2;
      // if (number < 0) {
      if (number < 0) {
        feeAmtfushuCount++;
      }
      if (feeAmt < 0) {
        //信息费为负数，那就不取整
        feeRound = "-";
        feeAmt2 = "-";
      } else if (feeAmt === 0) {
        feeAmt = Number(feeAmt).toFixed(2);
        feeRound = "-";
        feeAmt2 = Number(0).toFixed(2);
      } else {
        //取整
        if (shiweiquzheng >= 1) {
          //说明可以十位向下取整
          feeRound = (shiweiquzheng * 100).toFixed(2);
          feeAmt2 = (feeRound * (1 - shuilv)).toFixed(3);
          feeAmt2 = this.toFixedFun(feeAmt2, 2);
        } else {
          //不可以十位向下取整
          // feeRound = number;
          // feeAmt2 = (number * (1 - shuilv)).toFixed(2);

          feeRound = feeAmt;
          feeAmt2 = (feeAmt * (1 - shuilv)).toFixed(3);
          feeAmt2 = this.toFixedFun(feeAmt2, 2);
        }

        //todo 百位取整逻辑 start
        // //取整
        // if (baiweiquzheng >= 1) {
        //   //说明可以百位向下取整
        //   feeRound = baiweiquzheng * 1000;
        //   feeAmt2 = (feeRound * (1 - shuilv)).toFixed(2);
        // } else {
        //   // if (baiweiquzheng > 0 && baiweiquzheng < 1) {
        //   //不可以百位向下取整
        //   var shiweiquzheng = number / 100 | 0;
        //   if (shiweiquzheng >= 1) {
        //     //说明可以十位取整
        //     feeRound = shiweiquzheng * 100;
        //     feeAmt2 = (feeRound * (1 - shuilv)).toFixed(2);
        //   } else {
        //     feeRound = number;
        //     feeAmt2 = (number * (1 - shuilv)).toFixed(2);
        //   }
        // }
        //todo 百位取整逻辑 end
      }
      if (
        this.incomeAndFeeForm.incomeAndFeeList[index].yijifuwushangName !== null
      ) {
        //说明是一级服务商的真实回款
        // this.incomeAndFeeForm.incomeAndFeeList[index].feeAmt = (Number(this.incomeAndFeeForm.incomeAndFeeList[index].trueComeAmt) - Number(this.incomeAndFeeForm.incomeAndFeeList[index].serviceFee) - (Number(this.incomeAndFeeForm.incomeAndFeeList[index].principal) * 0.03)) / 2
        //todo 改之前信息费
        // this.incomeAndFeeForm.incomeAndFeeList[index].feeAmt = number;
        //上面计算出来的number是本期信息费
        this.incomeAndFeeForm.incomeAndFeeList[index].currentFee = number;
        this.incomeAndFeeForm.incomeAndFeeList[index].feeAmt = feeAmt;

        this.incomeAndFeeForm.incomeAndFeeList[index].feeRound = feeRound;
        this.incomeAndFeeForm.incomeAndFeeList[index].feeAmt2 = feeAmt2;
      } else {
        //不是一级的，先把信息费给这个二级，然后找到属于他的一级
        //todo 改之前信息费
        // this.incomeAndFeeForm.incomeAndFeeList[index].feeAmt = number;
        //上面计算出来的number是本期信息费
        this.incomeAndFeeForm.incomeAndFeeList[index].currentFee = number;
        this.incomeAndFeeForm.incomeAndFeeList[index].feeAmt = feeAmt;

        this.incomeAndFeeForm.incomeAndFeeList[index].feeRound = feeRound;
        this.incomeAndFeeForm.incomeAndFeeList[index].feeAmt2 = feeAmt2;
        //一级初始化信息费金额
        let yijiAmt = 0;
        let benqiyijiAmt = 0;
        let yijiAmtRound = 0;
        let yijiAmt2 = 0;
        for (
          let i = 0;
          i < this.incomeAndFeeForm.incomeAndFeeList.length;
          i++
        ) {
          if (
            this.incomeAndFeeForm.incomeAndFeeList[i].hebingId ===
              this.incomeAndFeeForm.incomeAndFeeList[index].hebingId &&
            this.incomeAndFeeForm.incomeAndFeeList[i].yijifuwushangName === null
          ) {
            //找到这个二级对应的hebingId的所有二级
            yijiAmt += Number(this.incomeAndFeeForm.incomeAndFeeList[i].feeAmt);
            benqiyijiAmt += Number(
              this.incomeAndFeeForm.incomeAndFeeList[i].currentFee
            );
            if (this.incomeAndFeeForm.incomeAndFeeList[i].feeRound !== "-") {
              yijiAmtRound += Number(
                this.incomeAndFeeForm.incomeAndFeeList[i].feeRound
              );
              yijiAmt2 += Number(
                this.incomeAndFeeForm.incomeAndFeeList[i].feeAmt2
              );
            }
          }
        }
        for (let i = 0; i < index; i++) {
          if (
            this.incomeAndFeeForm.incomeAndFeeList[i].yijifuwushangName !==
              null &&
            this.incomeAndFeeForm.incomeAndFeeList[i].hebingId ===
              this.incomeAndFeeForm.incomeAndFeeList[index].hebingId
          ) {
            //找到了一级，给一级信息费总和
            this.incomeAndFeeForm.incomeAndFeeList[i].feeAmt =
              Number(yijiAmt).toFixed(2);
            this.incomeAndFeeForm.incomeAndFeeList[i].currentFee =
              Number(benqiyijiAmt).toFixed(2);
            this.incomeAndFeeForm.incomeAndFeeList[i].feeRound =
              Number(yijiAmtRound).toFixed(2);
            this.incomeAndFeeForm.incomeAndFeeList[i].feeAmt2 =
              Number(yijiAmt2).toFixed(2);
          }
        }
      }
      //然后进行累加合计计算
      this.zhenshihuankuanheji = 0;
      this.fuwushangheji = 0;
      this.benjinheji = 0;
      this.fanfeiheji = 0;
      this.benqifanfeiheji = 0;
      this.fanfeiquzhengheji = 0;
      this.tichengfanfeiheji = 0;
      for (let i = 0; i < this.incomeAndFeeForm.incomeAndFeeList.length; i++) {
        if (
          this.incomeAndFeeForm.incomeAndFeeList[i].yijifuwushangName !== null
        ) {
          this.zhenshihuankuanheji = (
            Number(this.zhenshihuankuanheji) +
            Number(this.incomeAndFeeForm.incomeAndFeeList[i].trueComeAmt)
          ).toFixed(2);
          this.fuwushangheji = (
            Number(this.fuwushangheji) +
            Number(this.incomeAndFeeForm.incomeAndFeeList[i].serviceFee)
          ).toFixed(2);
          this.benjinheji = (
            Number(this.benjinheji) +
            Number(this.incomeAndFeeForm.incomeAndFeeList[i].principal)
          ).toFixed(2);
          this.fanfeiheji = (
            Number(this.fanfeiheji) +
            Number(this.incomeAndFeeForm.incomeAndFeeList[i].feeAmt)
          ).toFixed(2);
          this.benqifanfeiheji = (
            Number(this.benqifanfeiheji) +
            Number(this.incomeAndFeeForm.incomeAndFeeList[i].currentFee)
          ).toFixed(2);
          if (this.incomeAndFeeForm.incomeAndFeeList[i].feeRound !== "-") {
            this.fanfeiquzhengheji = (
              Number(this.fanfeiquzhengheji) +
              Number(this.incomeAndFeeForm.incomeAndFeeList[i].feeRound)
            ).toFixed(2);
          }
          if (this.incomeAndFeeForm.incomeAndFeeList[i].feeAmt2 !== "-") {
            this.tichengfanfeiheji = (
              Number(this.tichengfanfeiheji) +
              Number(this.incomeAndFeeForm.incomeAndFeeList[i].feeAmt2)
            ).toFixed(2);
          }
        }
      }
      //毛利
      this.maolijine = (
        Number(this.shouruheji) - Number(this.fanfeiquzhengheji)
      ).toFixed(2);
      //提成毛利
      this.tichengmaolijine = (
        Number(this.shouruheji) - Number(this.tichengfanfeiheji)
      ).toFixed(2);
      this.incomeAndFeeForm.incomeAndFeeList[0].grossProfitAmt = this.maolijine;
      this.incomeAndFeeForm.incomeAndFeeList[0].grossProfitAmt2 =
        this.tichengmaolijine;
      if (feeAmtfushuCount > 0) {
        this.feeAmtRedFlag = true;
      } else if (feeAmtfushuCount === 0) {
        this.feeAmtRedFlag = false;
      }
      if (guaqiFeeAmtCount > 0) {
        this.guaqiFeeAmtFlag = true;
      } else if (guaqiFeeAmtCount === 0) {
        this.guaqiFeeAmtFlag = false;
      }
      this.changeJtfrAmt(index);
    },
    getSummaries03(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }

        if (index === 1) {
          sums[index] = this.formaterMoney(Number(this.shouruheji));
        } else if (index === 5) {
          sums[index] = this.formaterMoney(Number(this.benqifanfeiheji));
        } else if (index === 7) {
          sums[index] = this.formaterMoney(Number(this.fanfeiheji));
        } else if (index === 8) {
          sums[index] = this.formaterMoney(Number(this.fanfeiquzhengheji));
        } else if (index === 9) {
          sums[index] = this.formaterMoney(Number(this.jtfrheji));
        } else if (index === 10) {
          sums[index] = this.formaterMoney(Number(this.facuilirunheji));
        }
      });
      return sums;
    },
    // changeServiceFee(index) {
    //
    // }
    async submitIncomeAndFee() {
      this.incomeAndFeeForm.incomeAndFeeList[0].remark = this.lrremark;
      var a = 0;
      for (let i = 0; i < this.incomeAndFeeForm.incomeAndFeeList.length; i++) {
        if (
          this.incomeAndFeeForm.incomeAndFeeList[i].erjifuwushangShow ===
            false &&
          this.incomeAndFeeForm.incomeAndFeeList[i].serviceProviderFlag === 1
        ) {
          //他是一级服务商，下面没有二级
          if (
            this.incomeAndFeeForm.incomeAndFeeList[i]
              .serviceProviderSecondName === null ||
            this.incomeAndFeeForm.incomeAndFeeList[i]
              .serviceProviderSecondName === ""
          ) {
            //这个一级服务商收入不为空
            if (
              this.incomeAndFeeForm.incomeAndFeeList[i].trueComeAmt === null ||
              this.incomeAndFeeForm.incomeAndFeeList[i].trueComeAmt === "" ||
              this.incomeAndFeeForm.incomeAndFeeList[i].serviceFee === null ||
              this.incomeAndFeeForm.incomeAndFeeList[i].serviceFee === "" ||
              this.incomeAndFeeForm.incomeAndFeeList[i].principal === null ||
              this.incomeAndFeeForm.incomeAndFeeList[i].principal === "" ||
              this.incomeAndFeeForm.incomeAndFeeList[i].jtfrAmt === null ||
              this.incomeAndFeeForm.incomeAndFeeList[i].jtfrAmt === ""
            ) {
              a++;
            }
          }
        } else if (
          this.incomeAndFeeForm.incomeAndFeeList[i].erjifuwushangShow ===
            false &&
          this.incomeAndFeeForm.incomeAndFeeList[i].serviceProviderFlag === 2
        ) {
          //下面有二级
          if (
            this.incomeAndFeeForm.incomeAndFeeList[i]
              .serviceProviderSecondName !== null ||
            this.incomeAndFeeForm.incomeAndFeeList[i]
              .serviceProviderSecondName !== ""
          ) {
            if (
              this.incomeAndFeeForm.incomeAndFeeList[i].trueComeAmt === null ||
              this.incomeAndFeeForm.incomeAndFeeList[i].trueComeAmt === "" ||
              this.incomeAndFeeForm.incomeAndFeeList[i].serviceFee === null ||
              this.incomeAndFeeForm.incomeAndFeeList[i].serviceFee === "" ||
              this.incomeAndFeeForm.incomeAndFeeList[i].principal === null ||
              this.incomeAndFeeForm.incomeAndFeeList[i].principal === "" ||
              this.incomeAndFeeForm.incomeAndFeeList[i].jtfrAmt === null ||
              this.incomeAndFeeForm.incomeAndFeeList[i].jtfrAmt === ""
            ) {
              a++;
            }
          }
        }
      }
      if (a > 0) {
        this.$message.error("请完善未输入项！");
      } else {
        let a = null;
        if (this.changeFlag === 1) {
          a = await changePhase(this.incomeAndFeeForm.incomeAndFeeList);
          if (a) {
            this.changeFlag = 0;
          }
        }
        console.log(
          this.incomeAndFeeForm.incomeAndFeeList,
          "-----------------------------------"
        );
        const { isok } = await addIncomeForLaw(
          this.incomeAndFeeForm.incomeAndFeeList
        );
        this.incomeAndFeeForm.incomeAndFeeList = [];
        if (isok) {
          var rhd = {
            id: this.returnincomeid,
          };

          getyewuxiangqingLaw(rhd).then((response) => {
            //todo 2023.06.14修复出信息费公司和信息费公司没有的问题
            this.feeCustNameProcess = response.feeCustName;
            this.custNameProcess = response.custName;

            this.collectionTime = response.detail.collectionTime;
            this.collectionTimeString = response.detail.collectionTimeString;
            this.smaIncomeAmtSum = response.sum.incomeAmtSum;
            this.smafeeAmtSum = response.sum.feeAmtSum;
            this.smafeeRoundSum = response.sum.feeRoundSum;
            this.smafeeAmt2Sum = response.sum.feeAmt2Sum;
            //todo 财务项目管理四期 小表合计新增
            this.smaCurrentFeeSum = response.sum.currentFeeSum;
            this.smaJtfrAmtSum = response.sum.jtfrAmtSum;
            this.smaLawProfitSum = response.sum.lawProfitSum;
            if (response.phaseStatus === "7") {
              response.phaseStatus = 0;
            } else if (response.phaseStatus === "8") {
              response.phaseStatus = 1;
            } else if (response.phaseStatus === "9") {
              response.phaseStatus = 2;
            } else if (response.phaseStatus === "10") {
              response.phaseStatus = 3;
            }
            this.jindutiao = response.phaseStatus;
            this.overDetailBodyDetailList1 =
              // [
              response.detail.feeList;
            // ]
            //录入收入日期
            this.lrsrdate = response.status0.dynamic_time;
            //录入收入代办人
            this.lrsrdaibanren = response.status0.oper_name;
          });
          var return2 = {
            id: this.projectId,
            sumFlag: 1,
          };
          // jindutiaoshow

          getLawProjectdeteils(return2).then((response) => {
            this.phaseNoFinish = response.phaseNoFinish;
            this.overDetailBodyDetailList = response.detail_list;
            this.overDetailBodyDetailList_v2 = response.detail_list_v2;
            this.merage(this.overDetailBodyDetailList_v2);
            this.replaceCompanyInfo = response.replaceCompanyInfo;
            this.feecustNameAndfeilvList = response.fee_list;
            //列表合计
            this.feeAmtSum = response.sum.sum_fee_amt;

            this.feeAmt2Sum = response.sum.sum_fee_amt_2;

            this.payAmtSum = response.sum.sum_pay_amt;

            this.differenceAmtSum = response.sum.sum_difference_amt;
            this.feeAlreadySum = response.sum.sum_fee_already;
            this.feeNoAlreadySum = response.sum.sum_fee_no_already;
            this.incomeSum = response.sum.sum_income_sum;
            this.grossProfitAmtSum = response.sum.sum_gross_profit_amt;
            this.grossProfitAmt2Sum = response.sum.sum_gross_profit_amt2;
            this.feeRoundSum = response.sum.sum_fee_roud;
            this.currentFeeSum = response.sum.sum_current_fee;
            //todo 财务项目管理四期 新增合计
            this.trueComeAmtSum = response.sum.sum_true_come_amt;
            this.serviceFeeSum = response.sum.sum_service_fee;
            this.principalSum = response.sum.sum_principal;
            this.jtfrAmtSum = response.sum.sum_jtfr_amt;
            this.lawProfitSum = response.sum.sum_law_profit;
            this.userList = response.people_list;
            this.loading = false;
          });
        }
        //关闭deilog
        this.lurufanfeideilog = false;
        this.$message.success("提交成功!");
      }
    },
  },
};
</script>
<style lang="less" scoped>
.tabs_price {
  display: flex;
  div {
    border-right: 1px solid #e4e4e4;
    padding-right: 100px;
    padding-left: 24px;
    p {
      margin-top: 12px;
      font-weight: bold;
      font-size: 20px;
      margin-bottom: 0;
    }
  }
}
.qrdkspan {
  color: #cccccc;
  font-size: 13px;

  display: block;
}
.djdktablespan {
  color: #9d9d9d;
  font-size: 14px;
}
.lrffdivspan2 {
  color: #9d9d9d;
}
.lrffdivspan233 {
  color: #cccccc;
}
.lrffdivspan1 {
  font-size: 18px;

  display: block;
}
.lrffdivspan123 {
  font-size: 14px;
  font-weight: bold;

  display: block;
}
.lrffdivspan1234 {
  font-size: 14px;

  display: block;
}
.lurushouruspan2 {
  font-size: 18px;
  font-weight: bold;
}
.lurushouruspan {
  font-size: 16px;
  margin-left: 18px;
  font-weight: bold;
}
.div3spankj {
  font-size: 13px;
  color: #cccccc;
  text-align: center;
  /* font-weight:bold; */
}
.yewuqicispan {
  /* font-size:24px; */
  margin-left: 16px;
  font-weight: bold;
}
.formspan {
  color: #9d9d9d;
  /* font-weight:bold; */
  font-size: 14px;
  text-align: center;
  margin-left: 30%;
}
.addprojectSpan {
  font-size: 24px;
  margin-left: 5px;
  font-weight: bold;
}

.addprojectSpan123 {
  font-size: 17px;
  margin-left: 5px;
  font-weight: bold;
}
.el-row {
  margin-bottom: 20px;
}
.tabsspan1 {
  font-size: 12px;
  margin-left: 5px;

  line-height: 60px;
}
.tabs2span1 {
  font-size: 14px;
  margin-left: 5px;
  font-weight: bold;
  line-height: 60px;
}
.tabsspan2 {
  font-size: 12px;
  margin-left: 200px;

  line-height: 60px;
}
.projectNameDiv1 {
  width: 100%;
  height: 100%;
  /*line-height: 80px;*/
}
.projectnamespan {
  font-size: 26px;
  margin-left: 16px;
  font-weight: bold;
  /* line-height:40px; */
}
.projectnamespan1 {
  font-size: 14px;
  margin-left: 16px;
  /*font-weight:bold;*/
  color: #cccccc;
  /* line-height:40px; */
}
.projectnamespan123 {
  font-size: 14px;
  margin-left: 0.3%;
  /*font-weight:bold;*/
  color: #cccccc;
  /* line-height:40px; */
}
.grid-content {
  /* border-radius: 10px;
    height: 50px;
    line-height: 14px; */
  color: #9d9d9d;
  /* font-weight:bold; */
  font-size: 14px;
  text-align: center;
  margin-left: 24px;
}
.grid-contentfont {
  color: #333333;
  /* font-weight:bold; */
  font-size: 14px;
}
.grid-contentcol {
  height: 20px;
  line-height: 40px;
  left: 30px;
}
.grid-col1 {
  border-radius: 4px;
  height: 36px;
}
.bg-purple {
  background: #9d9d9d;
}
#col-line {
  float: left;
  width: 1px;
  height: 60px;
  background: #e6e6e6;
}

.form2span1 {
  /* color:#ff8000; */
  font-weight: bold;
  /* margin-left: 24px; */
  font-family: "Microsoft YaHei";
  font-size: 16px;
}
.form2span2 {
  color: #9d9d9d;
  /* font-weight:bold; */
  /* margin-left: 24px; */
  font-family: "Microsoft YaHei";
  font-size: 12px;
}
.form2span3 {
  color: #9d9d9d;
  /* font-weight:bold; */
  /* margin-top: 6px; */
  margin-left: 80px;
  font-family: "Microsoft YaHei";
  font-size: 14px;
}
.dakuanfontstyle {
  color: #cccccc;
  /* font-weight:bold; */
  /* margin-top: 6px; */
  margin-left: 35px;
  font-family: "Microsoft YaHei";
  font-size: 14px;
}
.spancol {
  color: #333333;
  font-weight: bold;
  font-size: 24px;
  display: inline-block;
  margin-left: 24px;
  /* padding-top:10px; */
}
.amounting {
  font-size: 14px;
  color: #9d9d9d;
  margin-left: 24px;
  margin-top: 10px;
  display: block;
}
.amounting2 {
  font-size: 14px;
  color: #9d9d9d;
  margin-left: 24px;
  /* margin-top:px; */
  display: block;
}
.spancol2 {
  font-size: 14px;
  color: #9d9d9d;
  margin-left: 24px;
  display: block;
}
.echartspan {
  color: #007fff;
  font-size: 12px;
  /* font-weight:bold; */
  /* margin-left: 60px; */
}
.balancediv {
  width: 100px;
  height: 35px;
}
.item {
  margin: 4px;
}
.divsecend {
  /* border-radius: px; */
  min-height: 100px;
  background-color: #ffffff;
}
.spanfont {
  color: #333333;
  font-size: 16px;
  font-weight: normal;
  font-family: "Microsoft YaHei";
  margin-left: 24px;
}

.inner {
  width: 49%;
  height: 12%;
  background: #ffffff;
  margin: 4px;
}
.tabs2div {
  width: 48%;
  height: 500px;
  margin-left: 24px;
  /* background:	#afadad */
}
.innerone1 {
  width: 18.3%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}
.innerone2 {
  width: 50.3%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}
.innerone3 {
  width: 29.3%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}
.inneraaa {
  width: 24.3%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}
.dialogspan {
  font-size: 10px;
  color: #afadad;
  /* font-weight:bold; */
}
.deleteprojectSpan {
  font-size: 24px;
  margin-left: 5px;
  font-weight: bold;
}
.el-button--rejection {
  color: #fff;
  background-color: #ff9900;
  border-color: #ff9900;
}

.el-button--whitebg.is-active,
.el-button--whitebg:active {
  background: #46a6ff;
  border-color: #46a6ff;
  color: #fff;
}

.el-button--whitebg:focus,
.el-button--whitebg:hover {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.el-button--whitebg {
  color: #409eff;
  background-color: #ffffff;
  border-color: #409eff;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>
