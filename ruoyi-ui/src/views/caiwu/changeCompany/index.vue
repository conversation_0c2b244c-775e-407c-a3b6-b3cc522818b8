<template>
  <div style="padding: 16px">
    <span style="font-weight: bold">信息费公司与费率</span>

    <div style="width: 60%; margin: 16px 0">
      <el-table
        v-loading="loading"
        :data="feecustNameAndfeilvList"
        border
        :span-method="feeCustTableMergeMethod"
      >
        <el-table-column
          label=""
          align="center"
          prop="schemeFlag"
          min-width="10%"
        />
        <el-table-column
          label="信息费公司名称"
          align="center"
          prop="custName"
          min-width="40%"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.custName }} </span>
          </template>
        </el-table-column>
        <el-table-column
          label="费率"
          align="center"
          prop="rate"
          min-width="15%"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.rate == '未设置'">{{ scope.row.rate }} </span>
            <span v-if="scope.row.rate != '未设置'">{{ scope.row.rate }}%</span>
            <!-- <span v-if="scope.row.rate!='未设置'" >{{scope.row.rate}}%</span> -->
          </template>
        </el-table-column>
        <el-table-column
          label="税率"
          align="center"
          prop="taxRate"
          min-width="15%"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.taxRate == '未设置'"
              >{{ scope.row.taxRate }}
            </span>
            <span v-if="scope.row.taxRate != '未设置'"
              >{{ scope.row.taxRate }}%</span
            >
          </template>
        </el-table-column>
        <!-- <el-table-column
          v-if="feecustNameAndfeilvList.some((item) => item.replaceFlag == 1)"
          label="曾用公司(发生过替换)"
          align="center"
          min-width="15%"
        >
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.oldCompanyName && scope.row.oldCompanyName.length > 0
              "
            >
              <p v-for="item in scope.row.oldCompanyName" :key="item">
                {{ item }}
              </p>
            </span>
            <span v-else>--</span>
          </template>
        </el-table-column> -->
        <el-table-column
          label="曾用公司(发生过替换)"
          align="center"
          min-width="25%"
        >
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.oldCompanyName && scope.row.oldCompanyName.length > 0
              "
            >
              <p v-for="item in scope.row.oldCompanyName" :key="item">
                {{ item }}
              </p>
            </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="!$route.query.law"
          label="方案已使用次数"
          align="center"
          prop="schemeFlagUseSituation"
          min-width="20%"
        />
      </el-table>
    </div>
    <div>
      <span style="font-weight: bold">替换信息费公司</span><br />
      <span style="color: #999"
        >选择要替换的信息费公司，然后选择是否要替换历史已发生期次的历史数据，进行替换<br />如果不选择历史已发生期次，则修改只作用于未来新增的期次<br />替换后，发起新期次录入信息费时，将使用新的公司。搜索或统计时，输入新旧公司搜索都将展示他们共有的全部数据<br />替换仅作用于本项目，不影响其他项目的信息费公司</span
      >
      <el-table
        style="width: 500px; margin-top: 16px"
        border
        :data="companyData"
      >
        <el-table-column
          label="可替换的信息费公司"
          align="center"
          prop="userName"
          width="400"
        />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="change(scope.row)">替换</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <changeCompanyDia
      :companyName="companyName"
      v-if="changeCompanyDiaType"
      :monthDataList="monthDataList"
      @close="changeCompanyDiaType = false"
      @submit="submit"
    />
  </div>
</template>

<script>
import changeCompanyDia from "./changeCompanyDia.vue";
import {
  getProjectdeteils,
  getLawProjectdeteils,
  getyewuxiangqingLaw,
  addNewReplaceFeeCompanyInfo,
  getProjectFeeCompanyInfoByProjectId,
} from "@/api/caiwu/project";
export default {
  components: {
    changeCompanyDia,
  },
  data() {
    return {
      merageArr88:[],
      submitData: {},
      companyName: "",
      changeCompanyDiaType: false,
      loading: true,
      feecustNameAndfeilvList: [],
      companyData: [],
      monthDataList: [],
    };
  },
  mounted() {
    this.getList();
    getProjectFeeCompanyInfoByProjectId({
      projectId: this.$route.query.productId,
    }).then((res) => {
      if (res.code == 200) {
        this.companyData = res.data.feeCompanyInfo;
      }
    });
  },
  methods: {
    submit(v) {
      this.submitData = Object.assign(this.submitData, v);
      addNewReplaceFeeCompanyInfo({ ...this.submitData }).then((res) => {
        if (res.code == 200) {
          this.$message.success("替换成功");
          if (this.$route.query.type) {
            let route = {
              fullPath: "/caiwu/projectDetails",
              name: "ProjectDetails",
              path: "/caiwu/projectDetails",
            };
            this.closeSelectedTag(route);
            setTimeout(() => {
              this.$router.push({
                path: "/caiwu/projectDetails",
                query: {
                  productId: this.$route.query.productId,
                  tabs: 2,
                  // projectType: this.$route.query.projectType,
                  projectPortfolioCode: this.$route.query.projectPortfolioCode,
                },
              });
            }, 500);
          } else {
            let route = {
              fullPath: "/caiwu/projectDetailsLaw",
              name: "ProjectDetailsLaw",
              path: "/caiwu/projectDetailsLaw",
            };
            this.closeSelectedTag(route);
            setTimeout(() => {
              this.$router.push({
                path: "/caiwu/projectDetailsLaw",
                query: {
                  productId: this.$route.query.productId,
                  tabs: 2,
                  // projectType: this.$route.query.projectType,
                  projectPortfolioCode: this.$route.query.projectPortfolioCode,
                },
              });
            }, 500);
          }
        }
      });
    },
    closeSelectedTag(view) {
      this.$tab.closePage(view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view);
        }
      });
    },
    change(v) {
      console.log(v);
      this.submitData = { ...v };
      this.companyName = v.userName;
      this.changeCompanyDiaType = true;
    },
    getList() {
      var return2 = {
        id: this.$route.query.productId,
        sumFlag: 1,
      };
      // jindutiaoshow
      if (this.$route.query.law) {
        getLawProjectdeteils(return2).then((res) => {
          this.feecustNameAndfeilvList = res.fee_list;
          this.monthDataList = res.detail_list_v2;
          this.feeCustTableMerge(this.feecustNameAndfeilvList);
          this.loading = false;
        });
      } else {
        getProjectdeteils(return2).then((response) => {
          this.feecustNameAndfeilvList = response.fee_list;
          this.monthDataList = response.detail_list_v2;
          this.feeCustTableMerge(this.feecustNameAndfeilvList);
          this.loading = false;
        });
      }
    },
    //信息费公司与费率合并单元格具体处理逻辑
    feeCustTableMergeMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 5) {
        const _row = this.merageArr88[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    //信息费公司与费率合并单元格初始化
    feeCustTablemerageInit() {
      this.merageArr88 = [];
      this.meragePos88 = 0;
    },
    //信息费公司与费率合并单元格方法
    feeCustTableMerge(tableData) {
      this.feeCustTablemerageInit();
      for (var i = 0; i < tableData.length; i++) {
        if (i === 0) {
          // 第一行正常显示 必须存在
          this.merageArr88.push(1);
          this.meragePos88 = 0;
        } else {
          // 判断当前元素与上一个元素是否相同 根据是否合并的id
          if (
            typeof tableData[i].schemeFlag !== "undefined" &&
            tableData[i].schemeFlag != null &&
            tableData[i].schemeFlag !== "" &&
            tableData[i].schemeFlag === tableData[i - 1].schemeFlag
          ) {
            this.merageArr88[this.meragePos88] += 1;
            this.merageArr88.push(0);
          } else {
            this.merageArr88.push(1);
            this.meragePos88 = i;
          }
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
</style>