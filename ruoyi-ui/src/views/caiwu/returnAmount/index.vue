<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="140px">
      <el-form-item label="项目名称">
        <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable size="small" />
      </el-form-item>
      <el-form-item label="担保公司">
        <el-select v-model="selectCustName" placeholder="全部" filterable clearable size="small">
          <el-option v-for="dict in custNameSelect" :key="dict.id" :label="dict.companyName"
            :value="dict.companyName" />
        </el-select>
      </el-form-item>
      <el-form-item label="汇款公司">
        <el-select v-model="selectIncomeCustName" placeholder="全部" filterable clearable size="small">
          <el-option v-for="dict in incomeCustNameselect" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目类型">
        <el-select v-model="selectProjectType" placeholder="全部" filterable multiple size="small">
          <el-option v-for="dict in projectTypeSelect" :key="dict.dataCode" :label="dict.dataName"
            :value="dict.dataCode" />
        </el-select>
      </el-form-item>
      <el-form-item label="按期次所属月份统计">
        <el-date-picker v-model="dateRangeList" size="small" style="width: 240px" value-format="yyyy-MM"
          type="monthrange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>

      <!-- <el-form-item label="担保公司" prop="custName">
        <el-input
          v-model="queryParams.custName"
          placeholder="请输入担保公司"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="汇款公司" prop="incomeCustName">
        <el-input
          v-model="queryParams.incomeCustName"
          placeholder="请输入汇款公司"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
<!--    <div>-->
    <div v-hasPermi="['projectDeploy:excelImport']">
      <p>上传信息费对冲金额excel用</p>
      <el-upload
        ref="uploadExcel"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :before-remove="beforeRemove"
        :on-change="handleChange"
        :before-upload="beforeUpload"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        :file-list="upFileList"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <el-button size="mini" type="primary" @click="handleImport()"
      >上传入库</el-button
      >
    </div>
    <div style="width: 100%; height: 8px; background-color: #fbfbfb"></div>

    <div style="
        display: flex;
        width: 100%;
        background-color: #fbfbfb;
        flex-wrap: wrap;
        flex-direction: row;
      ">
      <div class="innerone11">
        <div style="width: 40%; height: 90px; line-height: 40px; float: left">
          <span class="amountspan">收入总计 </span>
          <span class="spanamount333">{{ formaterMoney(this.incomeCount) }}
          </span>
        </div>
      </div>
      <div class="innerone21" v-hasPermi="['sensitive:statistics']">
        <!-- <div style="width: 40%;height:90px;float:left " > -->
        <div style="width: 100%; height: 15px"></div>
        <div style="
            width: 33%;
            height: 85px;
            height: 35px;
            line-height: 35px;
            float: left;
          ">
          <span class="amounting2">信息费总计 </span>
          <span class="spanamount333">{{ formaterMoney(this.returnfeecount) }}
          </span>
        </div>
        <div v-hasPermi="['sensitive:statistics']" style="
            width: 33%;
            height: 85px;
            height: 35px;
            line-height: 35px;
            float: left;
          ">
          <!--                    <span class="amounting2">提成信息费总计-->
          <!--                    </span>-->
          <!--                    <span class="spanamount333">{{formaterMoney(this.feeamt2Count)}} </span>-->
          <span class="amounting2">毛利总计 </span>
          <span class="spanamount333">{{ formaterMoney(this.grossProfitAmtcount) }}
          </span>
        </div>
        <div v-hasPermi="['sensitive:statistics']" style="
            width: 33%;
            height: 35px;
            height: 35px;
            line-height: 35px;
            float: left;
          ">
          <span class="grid-content">已结清: </span>
          <span class="gridsecond"> {{ formaterMoney(this.ysettle) }}</span>
        </div>
        <div v-hasPermi="['sensitive:statistics']" style="
            width: 34%;
            height: 35px;
            height: 35px;
            line-height: 30px;
            float: right;
          ">
          <span class="grid-content">未结清: </span>
          <span class="gridsecond"> {{ formaterMoney(this.wsettle) }}</span>
        </div>
        <!-- </div> -->
      </div>

      <div v-hasPermi="['sensitive:statistics']" class="innerone31">
        <div style="width: 100%; height: 15px"></div>
        <div style="width: 100%; height: 35px; line-height: 35px; float: left">
          <span class="grid-content">提成信息费总计</span>
          <span class="gridsecond">
            {{ formaterMoney(this.feeamt2Count) }}</span>
        </div>
        <div style="width: 100%; height: 35px; line-height: 30px; float: right">
          <span class="grid-content">提成毛利总计</span>
          <span class="gridsecond">
            {{ formaterMoney(this.grossProfitAmt2count) }}</span>
        </div>
        <!--              <div style=" width: 40%; height:90px;  line-height: 40px;float:left">-->
        <!--                      <span class="amountspan">毛利总计-->
        <!--                    </span>-->
        <!--                    <span class="spanamount333">{{formaterMoney(this.grossProfitAmtcount)}} </span>-->
        <!--              </div>-->
        <!--              <div style=" width: 40%; height:90px;  line-height: 40px;float:left">-->
        <!--                      <span class="amountspan">提成毛利总计-->
        <!--                    </span>-->
        <!--                    <span class="spanamount333">{{formaterMoney(this.grossProfitAmt2count)}} </span>-->
        <!--              </div>-->
      </div>
    </div>
    <div style="width: 100%; height: 6px; background-color: #fbfbfb"></div>
    <div style="width: 100%; height: 60px">
      <el-radio-group v-model="defaultTableSelect" size="mini" style="margin-right: 30px">
        <el-radio-button label="正常视图"></el-radio-button>
        <el-radio-button label="详细视图"></el-radio-button>
        <!--        <el-radio-button label="正常视图" @click="handleNormalView"></el-radio-button>-->
        <!--        <el-radio-button label="详细视图" @click="handleDetailView"></el-radio-button>-->
        <!--        <el-radio-button label="top" @click="handleNormalView">正常视图</el-radio-button>-->
        <!--        <el-radio-button label="bottom" @click="handleDetailView">详细视图</el-radio-button>-->
      </el-radio-group>
      <el-button v-if="
        hasRoleList.includes('9') ||
        hasRoleList.includes('3') ||
        hasRoleList.includes('1') ||
        hasRoleList.includes('10') ||
        hasRoleList.includes('2')
      " type="primary" size="mini" plain style="margin-top: 12px" @click="addprojectindex()">+新增项目</el-button>
      <el-button v-show="this.ischeckexport && checkPermi(['sensitive:export'])" type="warning" size="mini" plain
        style="margin-top: 12px" icon="el-icon-download" @click="beachExport()">批量导出</el-button>
    </div>

    <div v-show="normalView">
      <el-table v-loading="loading" :data="projectList" style="font-size: 14px">
        <el-table-column min-width="200px" style="color: #ffab18" label="项目名称" prop="project_name">
          <template slot-scope="scope">
            <a v-if="scope.row.controlFlag == 0" @click="goprojectDetails(scope.row)">
              <span style="color: #1e90ff">{{ scope.row.project_name }}</span>
            </a>
            <span v-if="scope.row.controlFlag == 1">{{
              scope.row.project_name
            }}</span>
            <!-- <el-button
              size="mini"
              type="text"
              :show-overflow-tooltip="true"
              v-if="scope.row.controlFlag==0"
              @click="goprojectDetails(scope.row)"
            >{{scope.row.project_name}}</el-button>
             -->
          </template>
        </el-table-column>
        <el-table-column label="项目类型" prop="projectType" min-width="130px" />
        <el-table-column label="担保公司" min-width="130px" prop="cust_name" />
        <el-table-column label="汇款公司" min-width="130px" prop="income_cust_name" />
        <el-table-column label="总期次" min-width="100px" prop="income_cust_name">
          <template slot-scope="scope">{{ scope.row.allPhase }} </template>
        </el-table-column>
        <el-table-column label="收入总计" min-width="120px" prop="income_amt">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.income_amt) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="信息费总计" min-width="120px" prop="fee_amt" v-if="checkPermi(['sensitive:table'])">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.fee_amt) }}</span>
          </template>
        </el-table-column>

        <!--        <el-table-column label="提成信息费"  prop="fee_amt2" >-->
        <!--          <template slot-scope = "scope" >-->
        <!--            <span>{{formaterMoney(scope.row.fee_amt2)}}</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->

        <!--        <el-table-column label="毛利"  prop="gross_profit_amt" >-->
        <!--          <template slot-scope = "scope" >-->
        <!--            <span>{{formaterMoney(scope.row.gross_profit_amt)}}</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->

        <!--        <el-table-column label="提成毛利"  prop="gross_profit_amt2" >-->
        <!--          <template slot-scope = "scope" >-->
        <!--            <span>{{formaterMoney(scope.row.gross_profit_amt2)}}</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="信息费未结清" min-width="120px" prop="fee_no_already" v-if="checkPermi(['sensitive:table'])">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.fee_no_already) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="会计" min-width="100px" prop="accountant_list">
          <template slot-scope="scope">
            <span v-if="scope.row.accountant_list.length !== 0" v-for="item in scope.row.accountant_list">
              {{ item }}<br />
            </span>
            <span v-if="scope.row.accountant_list.length === 0"> - </span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="出纳" prop="cashier_list" >-->
        <!--          <template slot-scope="scope">-->
        <!--              <span v-for="item in scope.row.cashier_list">-->
        <!--                {{item}}<br>-->
        <!--              </span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="业务" min-width="100px" prop="business_list">
          <template slot-scope="scope">
            <span v-if="scope.row.business_list.length !== 0" v-for="item in scope.row.business_list">
              {{ item }}<br />
            </span>
            <span v-if="scope.row.business_list.length === 0"> - </span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="查看权限"  prop="select_list" >-->
        <!--          <template slot-scope="scope">-->
        <!--              <span v-for="item in scope.row.select_list">-->
        <!--                {{item}}<br>-->
        <!--              </span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column label="导出权限"  prop="export_list" >-->
        <!--          <template slot-scope="scope">-->
        <!--              <span v-for="item in scope.row.export_list">-->
        <!--                {{item}}<br>-->
        <!--              </span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->

        <el-table-column label="操作" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" v-if="scope.row.controlFlag == 0"
              @click="goprojectDetails(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div v-show="detailView">
      <el-table v-loading="loading" :data="projectList" style="font-size: 14px">
        <el-table-column min-width="280px" style="color: #ffab18" label="项目名称" prop="project_name">
          <template slot-scope="scope">
            <a v-if="scope.row.controlFlag == 0" @click="goprojectDetails(scope.row)">
              <span style="color: #1e90ff">{{ scope.row.project_name }}</span>
            </a>
            <span v-if="scope.row.controlFlag == 1">{{
              scope.row.project_name
            }}</span>
            <!-- <el-button
              size="mini"
              type="text"
              :show-overflow-tooltip="true"
              v-if="scope.row.controlFlag==0"
              @click="goprojectDetails(scope.row)"
            >{{scope.row.project_name}}</el-button>
             -->
          </template>
        </el-table-column>

        <el-table-column label="项目类型" min-width="150px" prop="projectType" />
        <el-table-column label="担保公司" min-width="160px" prop="cust_name" />
        <el-table-column label="汇款公司" min-width="160px" prop="income_cust_name" />

        <el-table-column label="总期次" min-width="120px" prop="income_cust_name">
          <template slot-scope="scope">
            {{ scope.row.allPhase }}
          </template>
        </el-table-column>
        <el-table-column label="收入总计" min-width="120px" prop="income_amt">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.income_amt) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="信息费总计" min-width="120px" prop="fee_amt" v-if="checkPermi(['sensitive:table'])">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.fee_amt) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="提成信息费总计" min-width="120px" prop="fee_amt2" v-if="checkPermi(['sensitive:table'])">
          <template slot-scope="scope">
            <span v-if="
              scope.row.projectPortfolioCode === 'lawUrgingNo'
            ">{{ formaterMoney(scope.row.fee_amt2) }}</span>
            <span v-if="scope.row.projectPortfolioCode === 'lawUrging'">-</span>
          </template>
        </el-table-column>

        <el-table-column label="毛利总计" min-width="120px" prop="gross_profit_amt" v-if="checkPermi(['sensitive:table'])">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.gross_profit_amt) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="提成毛利总计" min-width="120px" prop="gross_profit_amt2" v-if="checkPermi(['sensitive:table'])">
          <template slot-scope="scope">
            <span v-if="
              scope.row.projectPortfolioCode === 'lawUrgingNo'
            ">{{ formaterMoney(scope.row.gross_profit_amt2) }}</span>
            <span v-if="scope.row.projectPortfolioCode === 'lawUrging'">-</span>
          </template>
        </el-table-column>
        <el-table-column label="信息费未结清" min-width="120px" prop="fee_no_already" v-if="checkPermi(['sensitive:table'])">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.fee_no_already) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="信息费已支付" min-width="120px" prop="fee_no_already" v-if="checkPermi(['sensitive:table'])">
          <template slot-scope="scope">
            <span>{{ formaterMoney(scope.row.feeAlreadyPay) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="生成记账凭证" min-width="120px" prop="fee_no_already">
          <template slot-scope="scope">
            {{ scope.row.generateCertificateflag == 1 ? "是" : "否" }}
          </template>
        </el-table-column>
        <el-table-column label="担保费收入类型" min-width="120px" prop="fee_no_already">
          <template slot-scope="scope">
            {{
              scope.row.guaranteeIncomeType == 0 ? "直接收款" : "混合平台收益"
            }}
          </template>
        </el-table-column>
        <el-table-column label="收款人简称" min-width="120px" prop="payeeAbbreviation">
        </el-table-column>
        <el-table-column label="担保费收入借方科目" min-width="160px" prop="fee_no_already">
          <template slot-scope="scope">
            <span v-if="scope.row.guaranteeIncomeType == 0">
              银行存款-{{ scope.row.payeeAbbreviation }}
            </span>
            <span v-else> 预收账款-{{ scope.row.project_name }} </span>
          </template>
        </el-table-column>
        <el-table-column label="会计" min-width="130px" prop="accountant_list">
          <template slot-scope="scope">
            <span v-if="scope.row.accountant_list.length !== 0" v-for="item in scope.row.accountant_list">
              {{ item }}<br />
            </span>
            <span v-if="scope.row.accountant_list.length === 0"> - </span>
          </template>
        </el-table-column>
        <el-table-column label="出纳" min-width="130px" prop="cashier_list">
          <template slot-scope="scope">
            <span v-if="scope.row.cashier_list.length !== 0" v-for="item in scope.row.cashier_list">
              {{ item }}<br />
            </span>
            <span v-if="scope.row.cashier_list.length === 0"> - </span>
          </template>
        </el-table-column>
        <el-table-column label="业务" min-width="130px" prop="business_list">
          <template slot-scope="scope">
            <span v-if="scope.row.business_list.length !== 0" v-for="item in scope.row.business_list">
              {{ item }}<br />
            </span>
            <span v-if="scope.row.business_list.length === 0"> - </span>
          </template>
        </el-table-column>
        <el-table-column label="查看权限" min-width="130px" prop="select_list">
          <template slot-scope="scope">
            <span v-if="scope.row.select_list.length !== 0" v-for="item in scope.row.select_list">
              {{ item }}<br />
            </span>
            <span v-if="scope.row.select_list.length === 0"> - </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" v-if="scope.row.controlFlag == 0"
              @click="goprojectDetails(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { checkPermi } from "@/utils/permission"; // 权限判断函数

import {
  getloginexportNum,
  beachExportFile,
  projectShowxiala,
  projectShow,
  getProject,
  delProject,
  updateProject,
  getUserRoleByProjectId,
} from "@/api/caiwu/project";
import { newCompanySelectList } from "@/api/businessInformation/companyInformation";
import { getToken } from '@/utils/auth'
import {
  splicingListByCode
} from "@/api/businessInformation/productInformation";

export default {
  name: "ReturnAmount",
  data() {
    return {
      //视图显示控制
      normalView: true,
      detailView: false,
      //数据列表默认选中
      defaultTableSelect: "正常视图",
      //项目类型下拉框
      projectTypeSelect: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "0",
          label: "通道业务",
        },
        {
          value: "2",
          label: "分润业务",
        },
        {
          value: "1",
          label: "法催业务",
        },
        {
          value: "3",
          label: "保函业务",
        },
      ],
      //预存收入下拉框
      prestoreIncomeSelect: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "1",
          label: "有",
        },
        {
          value: "0",
          label: "无",
        },
      ],
      //选中项目类型
      selectProjectType: [],
      //选中的预存收入类型
      //固定数值展示
      //收入总计
      incomeCount: "",
      //信息费总计
      returnfeecount: "",
      //提成信息费总计
      feeamt2Count: "",
      //已结清
      ysettle: "",
      //未结清
      wsettle: "",
      //毛利总计
      grossProfitAmtcount: "",
      //提成毛利总计
      grossProfitAmt2count: "",
      //选中的担保公司
      selectCustName: "",
      //选中的
      selectIncomeCustName: "",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 财务项目管理主表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      //日期集合
      dateRangeList: [],
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: "",
        custName: "",
        incomeCustName: "",
        projectTypes: "",
        prestoreIncome: "",
        startDate: null,
        endDate: null,
        sumFlag: 1,
      },

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        custName: [
          { required: true, message: "担保公司不能为空", trigger: "blur" },
        ],
        incomeCustName: [
          { required: true, message: "汇款公司不能为空", trigger: "blur" },
        ],
        projectFlag: [
          {
            required: true,
            message: "项目状态：0正常 1终止不能为空",
            trigger: "blur",
          },
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" },
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" },
        ],
      },
      //担保公司下拉框
      custNameSelect: [],
      //收入公司下拉框
      incomeCustNameselect: [],
      //是否展示批量导出按钮
      ischeckexport: "",
      hasRoleList: [],
      // 用户导入参数
      upload: {
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/cwxmgl/income/fileUpload",
      },
      //文件集合
      upFileList: [],
    };
  },
  created() {
    this.getUserRoleByProjectId();
    this.checkloginexportNum();
    this.getxiala();
    this.getList();
    this.getProjectTypesList();
  },
  watch: {
    defaultTableSelect: function (val) {
      if (val == "正常视图") {
        this.loading = true;
        this.normalView = true;
        this.detailView = false;
        this.loading = false;
      }
      if (val == "详细视图") {
        this.loading = true;
        this.normalView = false;
        this.detailView = true;
        this.loading = false;
      }
    },
  },
  methods: {
    checkPermi,
    async getProjectTypesList() {
      const { rows } = await splicingListByCode({ firstDataCode: 'project_type' });
      this.projectTypeSelect = rows;
    },
    getUserRoleByProjectId() {
      getUserRoleByProjectId({}).then((res) => {
        this.hasRoleList = res.hasRoleList;
        console.log(this.hasRoleList, " this.hasRoleList");
      });
    },
    //判断当前登录人具有导出权限的项目数量是否是0
    checkloginexportNum() {
      getloginexportNum().then((response) => {
        this.ischeckexport = response.isOk;
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      //  start 重置逻辑更新
      this.resetForm("queryForm");
      this.queryParams.projectName = "";
      this.selectCustName = "";
      this.selectIncomeCustName = "";
      this.selectProjectType = [];
      this.dateRangeList = [];
      this.handleQuery();
      //  end 重置逻辑更新
    },
    /** 格式化金额 */
    formaterMoney(data) {
      if (!data) return "0.00";
      if (data === "-") return "-";
      // 将数据分割，保留两位小数
      data = data.toFixed(2);
      // 获取整数部分
      const intPart = Math.trunc(data);
      // 整数部分处理，增加,
      const intPartFormat = intPart
        .toString()
        .replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
      // 预定义小数部分
      let floatPart = ".00";
      // 将数据分割为小数部分和整数部分
      const newArr = data.toString().split(".");
      if (newArr.length === 2) {
        // 有小数部分
        floatPart = newArr[1].toString(); // 取得小数部分
        if (1 / intPart < 0 && intPart === 0) {
          return "-" + intPartFormat + "." + floatPart;
        }
        return intPartFormat + "." + floatPart;
      }
      if (1 / intPart < 0 && intPart === 0) {
        return "-" + intPartFormat + "." + floatPart;
      }
      return intPartFormat + floatPart;
    },
    //获取下拉数据
    getxiala() {
      projectShowxiala().then((response) => {
        var a = {
          value: "99",
          label: "全部",
        };

        this.incomeCustNameselect = response.incomeCustRoleName;
        // this.incomeCustNameselect.push(a);
      });
      newCompanySelectList({
        selectCode: "cust",
        modelCode: "FINANCEPROJ",
      }).then((res) => {
        this.custNameSelect = res;
      });
    },
    //点击新增跳转页面
    addprojectindex() {
      this.$router.push({ path: "/caiwu/addproject" });
    },
    //点击详情
    goprojectDetails(row) {
      let type = row.projectPortfolioCode;
      if (type === "lawUrgingNo") {
        this.$router.push({
          path: "/caiwu/projectDetails",
          query: { productId: row.id, projectPortfolioCode: row.projectPortfolioCode },
        });
      }
      if (type === "lawUrging") {
        this.$router.push({
          path: "/caiwu/projectDetailsLaw",
          query: { productId: row.id, projectPortfolioCode: row.projectPortfolioCode },
        });
      }
    },
    /** 查询财务项目管理主列表 */
    getList() {
      if (this.dateRangeList == null) {
        this.queryParams.startDate = "";
        this.queryParams.endDate = "";
      } else {
        this.queryParams.startDate = this.dateRangeList[0];
        this.queryParams.endDate = this.dateRangeList[1];
      }

      this.queryParams.custName = this.selectCustName;
      this.queryParams.incomeCustName = this.selectIncomeCustName;
      this.queryParams.projectTypes = this.selectProjectType?.join();
      // this.loading = true;
      // listProject(this.queryParams).then(response => {
      projectShow(this.queryParams).then((response) => {
        this.projectList = response.rows;
        //收入总计
        (this.incomeCount = response.sum.incomeSum),
          //信息费总计
          (this.returnfeecount = response.sum.feeAmtSum),
          //提成信息费总计
          (this.feeamt2Count = response.sum.feeAmt2Sum),
          //已结清
          (this.ysettle = response.sum.feeAlreadySum),
          //未结清
          (this.wsettle = response.sum.feeNoAlreadySum),
          //毛利总计
          (this.grossProfitAmtcount = response.sum.grossProfitAmtSum),
          //提成毛利总计
          (this.grossProfitAmt2count = response.sum.grossProfitAmt2Sum),
          (this.loading = false);
        this.total = response.total;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        custName: null,
        incomeCustName: null,
        projectTypes: null,
        prestoreIncome: null,
        projectFlag: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加财务项目管理主";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getProject(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改财务项目管理主";
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除财务项目管理主编号为"' + ids + '"的数据项？')
        .then(function () {
          return delProject(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    async beachExport() {
      var fileName = "";
      await beachExportFile().then((response) => {
        fileName = response.msg;
      });
      if (fileName == "N") {
        this.$message.error("没有可导出的文件，请先确认是否具有导出权限！");
      } else {
        var exportParams = {
          projectName: fileName,
        };

        this.download(
          "system/projectShow/jtReportsts/download",
          {
            ...exportParams,
          },
          fileName
        );
      }
    },
    handleNormalView() {
      this.defaultTableSelect;
      if (this.defaultTableSelect === "正常视图") {
        console.log("触发1");
      }
      this.normalView = true;
      this.detailView = false;
      console.log("触发1");
    },
    handleDetailView() {
      if (this.defaultTableSelect === "详细视图") {
        console.log("触发2");
      }
      this.normalView = false;
      this.detailView = true;
      console.log("触发2");
    },
    /** 导入按钮操作  begin*/
    //文件上传前处理
    // 文件上传 - 上传前
    beforeUpload(file, fileList) {
      //定义文件最大的限制，单位：MB
      var maxSize = 2048;
      //文件的大小
      var fileSize = file.size / 1024 / 1024;
      //进行文件的判断
      if (fileSize <= 0) {
        this.$message.error("上传文件大小不能为 0 MB");
        return false;
      } else if (fileSize < maxSize) {
        let promise = new Promise((resolve) => {
          this.$nextTick(function () {
            resolve(true);
          });
        });
        return promise;
      } else {
        this.$message.error(`上传文件大小不能超过2G!`);
        return false;
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {},
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      // if (response.code === 500) {
      //   this.$modal.msgError("文件类型错误，请删除文件后重新选择后上传！");
      // }
      if (response.remark === "200") {
        // this.respObj = response;
        // this.dialogOpen = true;
        this.$refs.uploadExcel.clearFiles();
      }
    },
    // 提交上传文件
    handleImport() {
      this.$refs.uploadExcel.submit();
    },
    beforeRemove(file, upFileList) {
      // this.buttonFlag = false;
    },
    //点击文件列表中已上传文件进行下载
    // handlePreview(file) {
    //   this.buttonFlag = true;
    // },
    handleChange(file, fileList) {
      // if (file !== null) {
      //   this.buttonFlag = true;
      // }
    },
    /** 导入按钮操作  end*/
  },
};
</script>
<style>
.el-row {
  margin-bottom: 20px;
}

.grid-content {
  /* border-radius: 10px;
    height: 50px;
    line-height: 14px; */
  color: #9d9d9d;
  /* font-weight:bold; */
  font-size: 14px;
  text-align: center;
  margin-left: 24px;
}

.gridsecond {
  color: #333333;
  /* font-weight:bold; */
  font-size: 14px;
}

.grid-contentcol {
  height: 20px;
  line-height: 40px;
  left: 30px;
}

.grid-col1 {
  border-radius: 4px;
  height: 36px;
}

.bg-purple {
  background: #9d9d9d;
}

#col-line {
  float: left;
  width: 1px;
  height: 60px;
  background: #e6e6e6;
}

.spanamount333 {
  /* color:#ff8000; */
  /* font-weight:bold; */
  margin-left: 24px;
  font-size: 24px;
}

.spancol {
  color: #333333;
  font-weight: bold;
  font-size: 24px;
  display: inline-block;
  margin-left: 24px;
  /* padding-top:10px; */
}

.amountspan {
  font-size: 14px;
  color: #9d9d9d;
  margin-left: 24px;
  margin-top: 10px;
  display: block;
}

.amounting2 {
  font-size: 14px;
  color: #9d9d9d;
  margin-left: 24px;
  /* margin-top:px; */
  display: block;
}

.spancol2 {
  font-size: 14px;
  color: #9d9d9d;
  margin-left: 24px;
  display: block;
}

.echartspan {
  color: #007fff;
  font-size: 12px;
  /* font-weight:bold; */
  /* margin-left: 60px; */
}

.balancediv {
  width: 100px;
  height: 35px;
}

.item {
  margin: 4px;
}

.divsecend {
  /* border-radius: px; */
  min-height: 100px;
  background-color: #ffffff;
}

.spanfont {
  color: #333333;
  font-size: 16px;
  font-weight: normal;
  font-family: "Microsoft YaHei";
  margin-left: 24px;
}

.inner {
  width: 49%;
  height: 12%;
  background: #ffffff;
  margin: 4px;
}

.innerone11 {
  width: 19.7%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}

.innerone21 {
  width: 58.5%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}

.innerone31 {
  width: 19.7%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}

.inneraaa {
  width: 24.3%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}

.dialogspan {
  font-size: 10px;
  color: #afadad;
  /* font-weight:bold; */
}
</style>
