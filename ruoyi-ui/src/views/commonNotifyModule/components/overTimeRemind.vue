<template>
  <div style="font-size: 18px">
    <div>{{ contentData.title }}</div>
    <div
      v-if="
        contentData.workOvertimeSlaveList &&
        contentData.workOvertimeSlaveList.length == 1
      "
    >
      <div
        v-for="(item, index) in voidContentOverTimeList"
        :key="index"
        style="display: flex"
      >
        <div>{{ item.label }}:</div>
        <div class="text-black">
          【{{ contentData.workOvertimeSlaveList[0][item.value] }}】
        </div>
      </div>
    </div>
    <div v-else>
      <div
        v-for="(item, index) in contentData.workOvertimeSlaveList"
        :key="index"
        class="mb-2"
      >
        <div
          v-for="(item1, index1) in voidContentOverTimeListMultiple"
          :key="index1"
          style="display: flex"
        >
          <div>{{ item1.label }}{{ index + 1 }}:</div>
          <div class="text-black">【{{ item[item1.value] }}】</div>
        </div>
      </div>
      <div
        v-for="(item, index) in voidContentOverTimeListMultipleLast"
        :key="index + 'last'"
        style="display: flex"
      >
        <div>{{ item.label }}:</div>
        <div class="text-black">【{{ contentData[item.value] }}】</div>
      </div>
    </div>
    <div class="mt-2">
      <div v-show="data.notifyModule == '加班申请'" class="flex">
        <div>取消原因:</div>
        <div class="mr-1" v-show="contentData.voidReason">
          【 <span style="color: red">{{ contentData.voidReason }}</span> 】
        </div>
      </div>
      <div v-show="data.notifyModule == '取消加班申请'" class="flex">
        <div v-if="contentData.handleState == '2'">该申请已被拒绝，拒绝原因为：</div>
        <div v-else>该申请已被同意</div>
        <div class="mr-1" v-show="contentData.refuseReason">
          【 <span style="color: red">{{ contentData.refuseReason }}</span> 】
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { workHandleOvertime } from "@/api/checkWork/overTime";
export default {
  name: "MonthlyRemind",
  data() {
    return {
      voidContentOverTimeList: Object.freeze([
        { label: "加班时间", value: "time" },
        { label: "工作内容", value: "content" },
        { label: "加班时长", value: "times" },
      ]),
      voidContentOverTimeListMultiple: Object.freeze([
        { label: "加班时间", value: "time" },
        { label: "加班时长", value: "times" },
      ]),
      voidContentOverTimeListMultipleLast: Object.freeze([
        { label: "工作内容", value: "content" },
        { label: "加班时长合计", value: "totalTimes" },
      ]),

      data: JSON.parse(this.$route.query.row),
      contentData: {},
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      const { data } = await workHandleOvertime(this.data.correlationId);
      data.title = `【${data.nickName}】 于${
        this.data.createTime
      }取消了一条审核状态为 【${
        this.$store.state.data.KV_MAP.check_work_approve_status[data.state]
      }】 的加班申请:`;
      data.totalTimes =
        data.workOvertimeSlaveList
          .map((item) => item.times)
          .reduce(
            (accumulator, currentValue) => accumulator + currentValue,
            0
          ) + "天";
      data.workOvertimeSlaveList.forEach((item) => {
        item.time = `${item.startTime} ${item.startTimePeriod} 至 ${item.endTime} ${item.endTimePeriod}`;
        item.times = item.times + "天";
        item.content = data.content;
      });

      this.contentData = data;
    },
  },
};
</script>
