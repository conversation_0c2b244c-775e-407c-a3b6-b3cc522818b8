<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="批量增加考核配置"
      :visible.sync="innerValue"
      width="1000px"
      @close="handleClose"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div style="max-height: 70vh" class="pr-10">
          <div>
            <el-form label-position="top" label-width="80px" :model="myForm">
              <el-form-item label="选择公司:">
                <el-select
                  v-model="myForm.companyIds"
                  filterable
                  multiple
                  placeholder="请选择公司名称"
                  clearable
                  @change="getListCompany"
                  @clear="getListCompany"
                >
                  <el-option
                    v-for="item in formColumns[0].options"
                    :key="item.id"
                    :label="item.companyName"
                    :value="item.id"
                  >
                  </el-option
                ></el-select>
              </el-form-item>
              <el-form-item label="选择年度:">
                <el-date-picker
                  type="years"
                  v-model="myForm.years"
                  placeholder="请选择年度"
                  @change="getListCompany"
                  :value-format="'yyyy'"
                >
                </el-date-picker>
              </el-form-item>
            </el-form>
            <MyTable
              v-show="configListCompany.length"
              :columns="columnsCompany"
              :source="configListCompany"
            >
              <template #operate="{ record }">
                <el-button type="text" @click="handleDelete(record)"
                  >删除</el-button
                >
              </template>
            </MyTable>
            <pagination
              v-show="totalCompany > 0"
              :total="totalCompany"
              :page.sync="myForm.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :limit.sync="myForm.pageSize"
              @pagination="getListCompany"
            />
          </div>
          <el-divider class="my-10"></el-divider>
          <div>
            <div style="color: #606266; font-weight: 700">选择模板:</div>
            <MyTable
              :columns="columnsTemplate"
              :source="configListTemplate"
              :showCheckbox="true"
              @selection-change="handleSelectionChange"
              ref="multipleTable"
            >
              <template #operate="{ record }">
                <el-button type="text" @click="goView(record)"
                  >查看详情</el-button
                >
              </template>
            </MyTable>
            <pagination
              v-show="totalTemplate > 0"
              :total="totalTemplate"
              :page.sync="queryParamsTemplate.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :limit.sync="queryParamsTemplate.pageSize"
              @pagination="getListTemplate"
            />
          </div>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">取消</el-button>

          <el-button
            type="primary"
            @click="onSubmit"
            class="mr-3"
            :disabled="!Boolean(multipleSelection.length)"
            >确定</el-button
          >
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import vModelMixin from "@/mixin/v-model";
import { templateList } from "@/api/perAppraisal/templateList";
import { insertCheckConfigByTemplate } from "@/api/perAppraisal/assessmentConfiguration";
import { haveAuthorityCompanyList } from "@/api/businessInformation/companyInformation";

import config from "./config";
export default {
  mixins: [vModelMixin],
  props: {},
  data() {
    return {
      ...config,
      myForm: {
        pageNum: 1,
        pageSize: 10,
        companyIds: [],
        years: [],
      },
      totalCompany: 0,
      configListCompany: [],
      queryParamsTemplate: {
        pageNum: 1,
        pageSize: 10,
      },
      totalTemplate: 0,
      configListTemplate: [],
      multipleSelection: [],
    };
  },

  mounted() {},
  methods: {
    handleOpen() {
      this.myForm = {
        pageNum: 1,
        pageSize: 10,
        companyIds: [],
        years: [],
      };
      this.configListCompany = [];
      this.queryParamsTemplate = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getListTemplate();
    },
    getCompanyParams() {
      const params = XEUtils.clone(this.myForm, true);
      params.companyIdList = params.companyIds
        ?.map((item) => (item = Number(item)))
        .join();
      delete params.years;
      return params;
    },
    async getListCompany() {
      if (this.myForm.companyIds && this.myForm.companyIds.length) {
        const { rows, total } = await haveAuthorityCompanyList(
          this.getCompanyParams()
        );
        this.configListCompany = rows;
        this.totalCompany = total;
      } else {
        this.configListCompany = [];
        this.totalCompany = 0;
      }
    },
    async getListTemplate() {
      const { rows, total } = await templateList(this.queryParamsTemplate);
      this.configListTemplate = rows;
      this.totalTemplate = total;
    },
    handleDelete(row) {
      this.myForm.companyIds = this.myForm.companyIds.filter(
        (item) => item != row.id
      );
      this.getListCompany();
    },
    handleSelectionChange(selection) {
      if (selection.length > 1) {
        selection.shift();
        this.$refs.multipleTable.clearSelection();
        this.$refs.multipleTable.toggleRowSelection(selection[0]);
        this.multipleSelection = selection;
      } else {
        this.multipleSelection = selection;
      }
    },
    async onSubmit() {
      if (!(this.configListCompany && this.configListCompany.length)) {
        this.$message.error("请选择公司");
        return;
      }
      if (!(this.myForm.years && this.myForm.years.length)) {
        this.$message.error("请选择年度");
        return;
      }
      const params = {
        companyIds: this.configListCompany.map((item) => item.id),
        years: this.myForm.years,
        templateId: this.multipleSelection[0].id,
      };
      const { data } = await insertCheckConfigByTemplate(params);

      let content = ``;
      data.forEach((item) => {
        content += `<div>${item};</div>`;
      });
      content += `<div>已生成过考核配置。<span style="color:red">${data.length}</span>  条配置已批量增加成功</div>`;
      this.$confirm(content, "警告", {
        confirmButtonText: "确定",
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        type: "warning",
      }).then(() => {
        this.innerValue = false;
        this.$emit("on-submit-success");
      });
    },
    goView(row) {
      this.$router.push({
        path: `/perAppraisalOther/templateList/${row.id}`,
        query: {
          title: "查看配置模板详情",
        },
      });
    },
    handleClose() {
      localStorage.removeItem("openAssessBatch"); // 清除状态
    },
  },
};
</script>
<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 10px;
}
::v-deep .el-form-item__label {
  padding: 0px !important;
}
.el-select {
  width: 100%;
}
.el-date-editor {
  width: 100%;
}
::v-deep .el-table__header-wrapper .el-table__header .el-checkbox {
  display: none;
}
</style>
