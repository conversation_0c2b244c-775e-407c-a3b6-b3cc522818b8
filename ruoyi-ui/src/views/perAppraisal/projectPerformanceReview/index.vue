<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>
    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          @click="selectCompanyTypes"
          :disabled="!Boolean(multipleSelection.length)"
          type="primary"
          size="mini"
          plain
          icon="el-icon-check"
        >
          提交项目业绩审核</el-button
        >

        <el-button @click="openSelect = true" type="primary" size="mini"
          >已选择({{ multipleSelection.length }})条</el-button
        >
      </div>
    </div>

    <MyTable
      ref="table"
      :columns="columns"
      :source="configList"
      :showIndex="true"
      :showCheckbox="true"
      @selection-change="handleSelectionChange"
      :disabledState="true"
      :queryParams="queryParams"
    >
      <template #yewuList="{ record }">
        <el-tooltip
          class="item"
          effect="dark"
          :content="
            record.yewuList && record.yewuList.map((item) => item.name).join()
          "
          placement="top-start"
        >
          <div class="truncate ...">
            {{
              record.yewuList && record.yewuList.map((item) => item.name).join()
            }}
          </div>
        </el-tooltip>
      </template>
      <template #status="{ record }">
        <div>{{ statusObj[record.state] }}</div>
      </template>
      <template #operate="{ record }">
        <el-button type="text" @click="goView(record, 'view')"
          >查看详情</el-button
        >
      </template>
    </MyTable>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <TableSelect
      :columns="columnsTableSelect"
      :tableData="multipleSelection"
      v-model="openSelect"
      @on-submit-success-row="submitDelet"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import { haveAuthorityCompanyList } from "@/api/businessInformation/companyInformation";
import {
  listEnter,
  getAchievementEnterFlow,
  selectAchievementEnterListProcess,
  getSelectListOfAchievement,
} from "@/api/perAppraisal/projectPerformance";
import config from "./components/config";
import XEUtils from "xe-utils";

export default {
  name: "ProjectPerformanceReview",

  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        year: new Date().getFullYear() + "",
        quarter: "",
      },
      total: 0,
      configList: [],
      multipleSelection: [],
      openSelect: false,
      selectCompanyType: false,
    };
  },
  computed: {
    columnsTableSelect() {
      return this.columns.filter((item) => item.label != "操作");
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getProjectList();
      this.getCompanyList();
      this.getColumns();
      this.getList();
    },
    async getProjectList() {
      const { rows } = await getSelectListOfAchievement();
      this.formColumns[0].options = rows;
    },
    async getCompanyList() {
      const { rows } = await haveAuthorityCompanyList({ isInside: 1 });
      this.formColumns[1].options = rows;
    },
    async getColumns(value) {
      if (!value) {
        const month = new Date().getMonth();
        // 计算当前季度
        this.queryParams.quarter = String(Math.floor(month / 3) + 1);
      }
      const quarterColumns = {
        1: this.columnsAddQ1,
        2: this.columnsAddQ2,
        3: this.columnsAddQ3,
        4: this.columnsAddQ4,
      };
      this.columns = this.columnsInit.concat(
        quarterColumns[this.queryParams.quarter],
        this.columnsOperate
      );
    },
    getParams() {
      const params = XEUtils.clone(this.queryParams, true);
      params.years = params.year;
      params.companyIds = params.companyId
        ?.map((item) => (item = Number(item)))
        .join();
      params.projectIds = params.projectId
        ?.map((item) => (item = Number(item)))
        .join();

      delete params.year;
      delete params.companyId;
      delete params.projectId;
      return params;
    },
    async getList() {
      const { rows, total } = await listEnter(this.getParams());
      this.configList = rows;
      this.total = total;
    },

    handleQuery(value) {
      this.queryParams.pageNum = 1;
      this.getColumns(this.queryParams.quarter);
      this.getList();
      if (value == "year" || value == "quarter") {
        this.$refs.table.clearSelection();
        this.multipleSelection = [];
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    submitDelet(e) {
      e.forEach((row) => {
        this.$refs.table.toggleRowSelection(row, false);
      });
    },
    goView(row, type) {
      const title = {
        update: "修改项目业绩",
        view: "查看项目业绩详情",
      }[type];
      const form = {
        year: row.year,

        projectId: row.projectId,
      };
      this.$router.push({
        path: `/perAppraisalOther/projectPerformance/${row.id}`,
        query: {
          title,
          form: JSON.stringify(form),
        },
      });
    },
    selectCompanyTypes() {
      const years = new Set(this.multipleSelection.map((item) => item.year));
      const hasDifferentValues = years.size > 1;
      if(hasDifferentValues){
        this.$message.warning("请选择同一年度的数据");
        return;
      }
      this.selectCompanyType = true;
    },
    submitCompany(e) {
      getAchievementEnterFlow({ companyId: e }).then(async (res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          const { rows } = await selectAchievementEnterListProcess({
            ids: this.multipleSelection.map((item) => item.id).join(),
            quarter: this.queryParams.quarter,
          });

          sessionStorage.setItem(
            "oa-projectPerformanceReviewTable",
            JSON.stringify(rows)
          );
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              projectPerformanceReviewTable: true,
            },
          });
        }
      });
    },
    closeCompany() {
      this.selectCompanyType = false;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-table,.el-table__fixed {
  .el-table__header-wrapper,.el-table__fixed-header-wrapper {
    table {
      thead {
        th {
          height: 67px !important;
          font-weight: bold;
          color: #333;
          // 换行
          .cell {
            white-space: pre-wrap;
          }
        }
      }
    }
  }
}
</style>
