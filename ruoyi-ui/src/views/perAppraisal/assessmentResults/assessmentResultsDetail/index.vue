<template>
  <div
    class="p-5 px-10 page-component__scroll overflow-y-auto pb-10"
    style="height: calc(100vh - 100px)"
  >
    <div class="flex justify-between px-5 mb-3 pr-10">
      <div style="font-size: 28px" class="font-bold">{{ form.year }}年</div>
      <div style="font-size: 25px" class="font-bold text-center">
        <div>{{ form.companyName }}</div>
        <div>
          <span v-show="title.indexOf('部门') != -1">部门</span
          ><span v-show="title.indexOf('用户') != -1">用户</span>考核结果校准
        </div>
      </div>
      <div style="font-size: 25px; color: #02a7f0" class="font-bold">
        <div>第{{ quarterObj[form.quarter] }}季度</div>
        <div v-show="title.indexOf('部门') != -1">{{ form.deptName }}</div>
        <div v-show="title.indexOf('用户') != -1" class="flex">
          <div style="color: #555555">用户:</div>
          <div>{{ form.nickName }}</div>
        </div>
      </div>
    </div>

    <el-divider></el-divider>
    <div style="color: #aaaaaa">
      <div>
        说明：自拓项目和自拓银行，完成其中一项，视为本季度自拓指标完成。
      </div>
      <div>
        您填写的校准值不可大于本年度完成情况校准，填写后，系统会自动计算校准后年度剩余业绩偏差、薪资占比、完成状态。
      </div>
      <div>
        若本季度自拓项目业绩和授信银行指标同时完成，可勾选"以自拓银行完成情况为准"选项。勾选后，本季度自拓项目业绩可分配给其他季度。
      </div>
    </div>
    <MyTable
      class="mt-1"
      :columns="columns"
      :source="configList"
      border
      :cell-style="colunmStyle"
      :header-cell-style="headerStyle"
    >
      <template #distributionIndex="{ record }">
        <div v-if="record.title == '人事校准'">
          <el-input
            :disabled="proForm.isView"
            @input="changeInput(record, 'distributionIndex')"
            @blur="blurInput(record, 'distributionIndex')"
            v-model="record.distributionIndex"
            :class="[record.distributionIndex != 0 ? 'green' : '', 'w-1/2']"
          ></el-input>
          <div v-if="form.quarter == 4 && Boolean(formSpecial.length)">
            <div
              v-show="
                form.calibrationDistributionIndex >
                formSpecialYear.distributionIndexDeviationCalibrationYearend
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              v-show="
                record.distributionIndex > form.distributionIndexDeviationYear
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
        </div>
        <div v-else>
          <div
            :style="{
              color: record.distributionIndexColor,
            }"
          >
            {{
              record.distributionIndex !== null &&
              record.distributionIndex !== undefined &&
              record.distributionIndex !== ""
                ? record.distributionIndex
                : "-"
            }}
          </div>
        </div>
      </template>
      <template #extensionIndex="{ record }">
        <div v-if="record.title == '人事校准'">
          <el-input
            :disabled="proForm.isView"
            @input="changeInput(record, 'extensionIndex')"
            @blur="blurInput(record, 'extensionIndex')"
            v-model="record.extensionIndex"
            :class="[record.extensionIndex != 0 ? 'green' : '', 'w-1/2']"
          ></el-input>
          <div v-if="form.quarter == 4 && Boolean(formSpecial.length)">
            <div
              v-show="
                form.calibrationExtensionIndex >
                formSpecialYear.extensionIndexDeviationCalibrationYearend
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              v-show="record.extensionIndex > form.extensionIndexDeviationYear"
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
        </div>
        <div v-else>
          <div
            :style="{
              color: record.extensionIndexColor,
            }"
          >
            {{
              record.extensionIndex !== null &&
              record.extensionIndex !== undefined &&
              record.extensionIndex !== ""
                ? record.extensionIndex
                : "-"
            }}
          </div>
        </div>
      </template>
      <template #extensionBank="{ record }">
        <div v-if="record.title == '人事校准'">
          <el-input
            :disabled="proForm.isView"
            @input="changeInput(record, 'extensionBank')"
            @blur="blurInput(record, 'extensionBank')"
            v-model="record.extensionBank"
            :class="[record.extensionBank != 0 ? 'green' : '', 'w-1/2']"
          ></el-input>
          <div v-if="form.quarter == 4 && Boolean(formSpecial.length)">
            <div
              v-show="
                form.calibrationExtensionBank >
                formSpecialYear.extensionBankDeviationCalibrationYearend
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              v-show="record.extensionBank > form.extensionBankDeviationYear"
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
        </div>

        <div v-else>
          <div
            :style="{
              color: record.extensionBankColor,
            }"
          >
            <div>
              {{
                record.extensionBank !== null &&
                record.extensionBank !== undefined &&
                record.extensionBank !== ""
                  ? record.extensionBank
                  : "-"
              }}
            </div>
          </div>
          <div v-show="record.title == '薪资占比' && form.showCheckobx">
            <el-checkbox
              v-model="form.bankShallPrevail"
              @change="bankShallPrevailChange"
              >以自拓银行完成情况为准</el-checkbox
            >
          </div>
        </div>
      </template>
    </MyTable>
    <div v-show="form.quarter == 4">
      <el-divider></el-divider>
      <div style="color: #aaaaaa" class="mt-10">
        <div>说明：表格中展示的是本年前三个季度，季度校准后的数据。</div>
        <div>
          您可以在本季度继续补充前三个季度业绩，系统会自动累加到第四季度薪资占比中
        </div>
      </div>
      <MyTable
        class="mt-1"
        :columns="columnsSpecial"
        :source="configListSpecial"
        border
        :cell-style="colunmStyle"
        :header-cell-style="headerStyle"
      >
        <template #distributionIndexQ1="{ record }">
          <div v-if="record.title == '人事校准 - 年终'">
            <el-input
              :disabled="proForm.isView"
              @input="changeInputOther(record, 'distributionIndexQ1')"
              @blur="blurInput(record, 'distributionIndexQ1')"
              v-model="record.distributionIndexQ1"
              :class="[record.distributionIndexQ1 != 0 ? 'green' : '', 'w-1/2']"
            ></el-input>
            <div
              v-show="
                formSpecialYear.distributionIndexDeviationCalibrationYearend <
                  form.calibrationDistributionIndex ||
                formSpecialYear.distributionIndexDeviationCalibrationYearend < 0
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              :style="{
                color: record.distributionIndexQ1Color,
              }"
            >
              {{
                record.distributionIndexQ1 !== null &&
                record.distributionIndexQ1 !== undefined &&
                record.distributionIndexQ1 !== ""
                  ? record.distributionIndexQ1
                  : "-"
              }}
            </div>
          </div>
        </template>
        <template #extensionIndexQ1="{ record }">
          <div v-if="record.title == '人事校准 - 年终'">
            <el-input
              :disabled="proForm.isView"
              @input="changeInputOther(record, 'extensionIndexQ1')"
              @blur="blurInput(record, 'extensionIndexQ1')"
              v-model="record.extensionIndexQ1"
              :class="[record.extensionIndexQ1 != 0 ? 'green' : '', 'w-1/2']"
            ></el-input>
            <div
              v-show="
                formSpecialYear.extensionIndexDeviationCalibrationYearend <
                  form.calibrationExtensionIndex ||
                formSpecialYear.extensionIndexDeviationCalibrationYearend < 0
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              :style="{
                color: record.extensionIndexQ1Color,
              }"
            >
              {{
                record.extensionIndexQ1 !== null &&
                record.extensionIndexQ1 !== undefined &&
                record.extensionIndexQ1 !== ""
                  ? record.extensionIndexQ1
                  : "-"
              }}
            </div>
          </div>
        </template>
        <template #extensionBankQ1="{ record }">
          <div v-if="record.title == '人事校准 - 年终'">
            <el-input
              :disabled="proForm.isView"
              @input="changeInputOther(record, 'extensionBankQ1')"
              @blur="blurInput(record, 'extensionBankQ1')"
              v-model="record.extensionBankQ1"
              :class="[record.extensionBankQ1 != 0 ? 'green' : '', 'w-1/2']"
            ></el-input>
            <div
              v-show="
                formSpecialYear.extensionBankDeviationCalibrationYearend <
                  form.calibrationExtensionBank ||
                formSpecialYear.extensionBankDeviationCalibrationYearend < 0
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              :style="{
                color: record.extensionBankQ1Color,
              }"
            >
              {{
                record.extensionBankQ1 !== null &&
                record.extensionBankQ1 !== undefined &&
                record.extensionBankQ1 !== ""
                  ? record.extensionBankQ1
                  : "-"
              }}
            </div>
          </div>
        </template>

        <template #distributionIndexQ2="{ record }">
          <div v-if="record.title == '人事校准 - 年终'">
            <el-input
              :disabled="proForm.isView"
              @input="changeInputOther(record, 'distributionIndexQ2')"
              @blur="blurInput(record, 'distributionIndexQ2')"
              v-model="record.distributionIndexQ2"
              :class="[record.distributionIndexQ2 != 0 ? 'green' : '', 'w-1/2']"
            ></el-input>
            <div
              v-show="
                formSpecialYear.distributionIndexDeviationCalibrationYearend <
                  form.calibrationDistributionIndex ||
                formSpecialYear.distributionIndexDeviationCalibrationYearend < 0
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              :style="{
                color: record.distributionIndexQ2Color,
              }"
            >
              {{
                record.distributionIndexQ2 !== null &&
                record.distributionIndexQ2 !== undefined &&
                record.distributionIndexQ2 !== ""
                  ? record.distributionIndexQ2
                  : "-"
              }}
            </div>
          </div>
        </template>
        <template #extensionIndexQ2="{ record }">
          <div v-if="record.title == '人事校准 - 年终'">
            <el-input
              :disabled="proForm.isView"
              @input="changeInputOther(record, 'extensionIndexQ2')"
              @blur="blurInput(record, 'extensionIndexQ2')"
              v-model="record.extensionIndexQ2"
              :class="[record.extensionIndexQ2 != 0 ? 'green' : '', 'w-1/2']"
            ></el-input>
            <div
              v-show="
                formSpecialYear.extensionIndexDeviationCalibrationYearend <
                  form.calibrationExtensionIndex ||
                formSpecialYear.extensionIndexDeviationCalibrationYearend < 0
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              :style="{
                color: record.extensionIndexQ2Color,
              }"
            >
              {{
                record.extensionIndexQ2 !== null &&
                record.extensionIndexQ2 !== undefined &&
                record.extensionIndexQ2 !== ""
                  ? record.extensionIndexQ2
                  : "-"
              }}
            </div>
          </div>
        </template>
        <template #extensionBankQ2="{ record }">
          <div v-if="record.title == '人事校准 - 年终'">
            <el-input
              :disabled="proForm.isView"
              @input="changeInputOther(record, 'extensionBankQ2')"
              @blur="blurInput(record, 'extensionBankQ2')"
              v-model="record.extensionBankQ2"
              :class="[record.extensionBankQ2 != 0 ? 'green' : '', 'w-1/2']"
            ></el-input>
            <div
              v-show="
                formSpecialYear.extensionBankDeviationCalibrationYearend <
                  form.calibrationExtensionBank ||
                formSpecialYear.extensionBankDeviationCalibrationYearend < 0
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              :style="{
                color: record.extensionBankQ2Color,
              }"
            >
              {{
                record.extensionBankQ2 !== null &&
                record.extensionBankQ2 !== undefined &&
                record.extensionBankQ2 !== ""
                  ? record.extensionBankQ2
                  : "-"
              }}
            </div>
          </div>
        </template>

        <template #distributionIndexQ3="{ record }">
          <div v-if="record.title == '人事校准 - 年终'">
            <el-input
              :disabled="proForm.isView"
              @input="changeInputOther(record, 'distributionIndexQ3')"
              @blur="blurInput(record, 'distributionIndexQ3')"
              v-model="record.distributionIndexQ3"
              :class="[record.distributionIndexQ3 != 0 ? 'green' : '', 'w-1/2']"
            ></el-input>
            <div
              v-show="
                formSpecialYear.distributionIndexDeviationCalibrationYearend <
                  form.calibrationDistributionIndex ||
                formSpecialYear.distributionIndexDeviationCalibrationYearend < 0
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              :style="{
                color: record.distributionIndexQ3Color,
              }"
            >
              {{
                record.distributionIndexQ3 !== null &&
                record.distributionIndexQ3 !== undefined &&
                record.distributionIndexQ3 !== ""
                  ? record.distributionIndexQ3
                  : "-"
              }}
            </div>
          </div>
        </template>
        <template #extensionIndexQ3="{ record }">
          <div v-if="record.title == '人事校准 - 年终'">
            <el-input
              :disabled="proForm.isView"
              @input="changeInputOther(record, 'extensionIndexQ3')"
              @blur="blurInput(record, 'extensionIndexQ3')"
              v-model="record.extensionIndexQ3"
              :class="[record.extensionIndexQ3 != 0 ? 'green' : '', 'w-1/2']"
            ></el-input>
            <div
              v-show="
                formSpecialYear.extensionIndexDeviationCalibrationYearend <
                  form.calibrationExtensionIndex ||
                formSpecialYear.extensionIndexDeviationCalibrationYearend < 0
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              :style="{
                color: record.extensionIndexQ3Color,
              }"
            >
              {{
                record.extensionIndexQ3 !== null &&
                record.extensionIndexQ3 !== undefined &&
                record.extensionIndexQ3 !== ""
                  ? record.extensionIndexQ3
                  : "-"
              }}
            </div>
          </div>
        </template>
        <template #extensionBankQ3="{ record }">
          <div v-if="record.title == '人事校准 - 年终'">
            <el-input
              :disabled="proForm.isView"
              @input="changeInputOther(record, 'extensionBankQ3')"
              @blur="blurInput(record, 'extensionBankQ3')"
              v-model="record.extensionBankQ3"
              :class="[record.extensionBankQ3 != 0 ? 'green' : '', 'w-1/2']"
            ></el-input>
            <div
              v-show="
                formSpecialYear.extensionBankDeviationCalibrationYearend <
                  form.calibrationExtensionBank ||
                formSpecialYear.extensionBankDeviationCalibrationYearend < 0
              "
              style="font-size: 12px; color: red"
            >
              校准值不可大于本年度累计项目业绩偏差
            </div>
          </div>
          <div v-else>
            <div
              :style="{
                color: record.extensionBankQ3Color,
              }"
            >
              {{
                record.extensionBankQ3 !== null &&
                record.extensionBankQ3 !== undefined &&
                record.extensionBankQ3 !== ""
                  ? record.extensionBankQ3
                  : "-"
              }}
            </div>
          </div>
        </template>
      </MyTable>
      <div
        class="my-10 py-5 px-10 text-black font-bold"
        style="border: 1px solid black"
      >
        <div class="text-center text-xl mb-5">
          <div v-show="title.indexOf('公司') != -1">公司</div>
          <div v-show="title.indexOf('部门') != -1">部门</div>
          <div v-show="title.indexOf('人员') != -1">业务人员</div>
          <div>年度业绩汇总</div>
        </div>
        <div class="flex justify-between flex-wrap text-lg">
          <div
            v-for="(item, index) in perSummary"
            :key="index"
            class="flex w-1/2 justify-center my-5"
          >
            <div class="w-1/2 text-right">{{ item.label }}</div>
            <div class="w-1/2 pl-2">
              <span style="color: #02a7f0">{{ perSummaryObj[item.value] }}</span
              ><span v-show="perSummaryObj[item.value]">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <InBody>
      <div
        class="text-center fixed bottom-0 bg-white z-10 pb-2"
        style="width: calc(100% - 260px); left: 260px"
      >
        <el-backtop
          target=".page-component__scroll "
          :bottom="5"
          :right="220"
          :visibility-height="20"
        >
          <div
            style="
               {
                height: 100%;
                width: 100%;
                background-color: #fff;
                text-align: center;
                line-height: 40px;
                color: #1989fa;
                font-size: 14px;
              }
            "
          >
            返回顶部
          </div>
        </el-backtop>
        <el-button
          @click="onSave"
          type="primary"
          v-show="form.quarter != 4 && !proForm.isView"
          >保存</el-button
        >
        <el-button
          @click="onSaveSpecial"
          type="primary"
          v-show="form.quarter == 4 && !proForm.isView"
          >保存</el-button
        >
        <el-button @click="cancel">取消</el-button>
      </div>
    </InBody>
  </div>
</template>

<script>
import config from "./config";
import {
  floatAdd,
  decimal,
  floatDivide,
  floatMultiply,
  floatSub,
} from "@/utils";
import {
  updateResult,
  getCheckResult,
  selectCheckResultListofQ3,
  replaceCheckResult,
  getCheckResultTotal,
} from "@/api/perAppraisal/results";
import XEUtils from "xe-utils";

export default {
  name: "AssessmentResultsDetail",
  data() {
    return {
      ...config,
      id: this.$route.params.id,
      title: this.$route.query.title,
      proForm: JSON.parse(this.$route.query.proForm),
      form: {},
      prevObj: {},
      configList: [],
      columns: [],
      formSpecial: [],
      formSpecialYear: {
        distributionIndexDeviationCalibrationYearend: 0,
        extensionIndexDeviationCalibrationYearend: 0,
        extensionBankDeviationCalibrationYearend: 0,
      },
      configListSpecial: [],
      columnsSpecial: [],
      perSummary: [],
      perSummaryObj: {},
    };
  },
  computed: {},

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getForm();
    },
    async getForm() {
      const { data } = await getCheckResult(this.proForm);
      // const data = {
      //   quarter: "4",
      //   year: "2024",
      //   type: 2,
      //   companyName: "中保国信融资担保有限公司考核结果校准",
      //   deptName: "综合管理部",
      //   nickName: "张三",
      //   companyId: 5,
      //   deptId: 134,
      //   checkResult: {
      //     extensionIndex: 1,
      //     distributionIndex: 2,
      //     extensionBank: 3,

      //     distributionProportion: 4,
      //     extensionProportion: 5,

      //     completeDistributionIndex: 6,
      //     completeExtensionIndex: 7,
      //     completeExtensionBank: 8,

      //     projectSalaryDistributionProportion: 10,
      //     projectSalaryExtensionProportion: 11,
      //     bankSalaryExtensionProportion: 10,

      //     distributionIndexDeviation: 10,
      //     extensionIndexDeviation: -11,
      //     extensionBankDeviation: 10,

      //     distributionIndexDeviationYear: 101,
      //     extensionIndexDeviationYear: 1,
      //     extensionBankDeviationYear: 1012,

      //     calibrationDistributionIndex: 101,
      //     calibrationExtensionIndex: 111,
      //     calibrationExtensionBank: 1,

      //     distributionIndexDeviationCalibration: 101,
      //     extensionIndexDeviationCalibration: 111,
      //     extensionBankDeviationCalibration: 101,

      //     calibrationProjectSalaryDistributionProportion: 1,
      //     calibrationProjectSalaryExtensionProportion: 122,
      //     calibrationProjectSalaryBankProportion: 1,

      //     calibrationDistributionIndexState: 1,
      //     calibrationExtensionIndexProjectState: "已完成-项目",
      //     calibrationExtensionIndexBankState: "已完成-银行",

      //     distributionIndexDeviationProportionQ3: 111,
      //     extensionIndexDeviationProportionQ3: 111,
      //     extensionBankDeviationProportionQ3: 111,
      //   },
      // };

      const tempObj = { ...data, ...data.checkResult };
      delete tempObj.checkResult;
      this.form = XEUtils.clone(tempObj);
      if (
        this.form.calibrationDistributionIndexState == 1 &&
        this.form.calibrationExtensionIndexProjectState == "已完成-项目" &&
        this.form.calibrationExtensionIndexBankState == "已完成-银行"
      ) {
        this.$set(this.form, "showCheckobx", true);
      }
      this.getConfigList();
      this.getColumns();
      if (this.form.quarter == 4) {
        this.getSpecial();
        this.geterSummary();
      }
    },
    getItemFixed(
      obj,
      item,
      arr,
      tableItem = ["distributionIndex", "extensionIndex", "extensionBank"]
    ) {
      if (obj[arr[0]] != undefined && obj[arr[0]] != null)
        this.$set(item, tableItem[0], obj[arr[0]].toFixed(6));
      if (obj[arr[1]] != undefined && obj[arr[1]] != null)
        this.$set(item, tableItem[1], obj[arr[1]].toFixed(6));
      if (obj[arr[2]] != undefined && obj[arr[2]] != null)
        this.$set(item, tableItem[2], obj[arr[2]]);
    },
    getItemColor(
      obj,
      item,
      value1,
      value2,
      tableItem = ["distributionIndex", "extensionIndex", "extensionBank"]
    ) {
      let arr0, arr1, arr2;
      if (obj[value1[0]] > 0) {
        arr0 = `+${obj[value1[0]].toFixed(6)}`;
        this.$set(item, tableItem[0] + "Color", "rgb(245, 108, 108)");
      } else if (obj[value1[0]] < 0) {
        arr0 = `${obj[value1[0]].toFixed(6)}`;
        this.$set(
          item,
          tableItem[0] + "Color",
          "rgba(191, 191, 0, 0.***************)"
        );
      } else if (obj[value1[0]] == 0) {
        arr0 = 0;
      }
      this.$set(item, tableItem[0], arr0);
      if (obj[value1[1]] > 0) {
        arr1 = `+${obj[value1[1]].toFixed(6)}`;
        this.$set(item, tableItem[1] + "Color", "rgb(245, 108, 108)");
      } else if (obj[value1[1]] < 0) {
        arr1 = `${obj[value1[1]].toFixed(6)}`;
        this.$set(
          item,
          tableItem[1] + "Color",
          "rgba(191, 191, 0, 0.***************)"
        );
      } else if (obj[value1[1]] == 0) {
        arr1 = 0;
      }
      this.$set(item, tableItem[1], arr1);
      if (obj[value2[0]] > 0) {
        arr2 = `+${obj[value2[0]]}`;
        this.$set(item, tableItem[2] + "Color", "rgb(245, 108, 108)");
      } else if (obj[value2[0]] < 0) {
        arr2 = `${obj[value2[0]]}`;
        this.$set(
          item,
          tableItem[2] + "Color",
          "rgba(191, 191, 0, 0.***************)"
        );
      } else if (obj[value2[0]] == 0) {
        arr2 = 0;
      }
      this.$set(item, tableItem[2], arr2);
    },
    getItemUnit(
      obj,
      item,
      arr,
      tableItem = ["distributionIndex", "extensionIndex", "extensionBank"]
    ) {
      if (obj[arr[0]] != undefined && obj[arr[0]] != null)
        this.$set(item, tableItem[0], obj[arr[0]] + "%");
      if (obj[arr[1]] != undefined && obj[arr[1]] != null)
        this.$set(item, tableItem[1], obj[arr[1]] + "%");
      if (obj[arr[2]] != undefined && obj[arr[2]] != null)
        this.$set(item, tableItem[2], obj[arr[2]] + "%");
    },
    getConfigList() {
      const configListInit =
        this.form.quarter == 4
          ? [
              ...this.configListInit,
              { title: "前三季度补充业绩偏差值累计后薪资占比" },
            ]
          : this.configListInit;
      this.configList = XEUtils.clone(configListInit, true);
      this.configList.forEach((item) => {
        if (item.title == "年度计划") {
          this.getItemFixed(this.form, item, [
            "distributionIndex",
            "extensionIndex",
            "extensionBank",
          ]);
        } else if (item.title == "绩效占比") {
          this.getItemUnit(this.form, item, [
            "distributionProportion",
            "extensionProportion",
            "extensionProportion",
          ]);
        } else if (item.title == "完成情况") {
          this.getItemFixed(this.form, item, [
            "completeDistributionIndex",
            "completeExtensionIndex",
            "completeExtensionBank",
          ]);
        } else if (item.title == "薪资占比") {
          this.getItemUnit(this.form, item, [
            "projectSalaryDistributionProportion",
            "projectSalaryExtensionProportion",
            "bankSalaryExtensionProportion",
          ]);
        } else if (item.title == "本季度项目业绩偏差") {
          this.getItemColor(
            this.form,
            item,
            ["distributionIndexDeviation", "extensionIndexDeviation"],
            ["extensionBankDeviation"]
          );
        } else if (item.title == "本年度累计项目业绩偏差") {
          this.getItemColor(
            this.form,
            item,
            ["distributionIndexDeviationYear", "extensionIndexDeviationYear"],
            ["extensionBankDeviationYear"]
          );
        } else if (item.title == "人事校准") {
          this.getItemFixed(this.form, item, [
            "calibrationDistributionIndex",
            "calibrationExtensionIndex",
            "calibrationExtensionBank",
          ]);
        } else if (item.title == "校准后年度累计项目业绩偏差") {
          this.getItemColor(
            this.form,
            item,
            [
              "distributionIndexDeviationCalibration",
              "extensionIndexDeviationCalibration",
            ],
            ["extensionBankDeviationCalibration"]
          );
        } else if (item.title == "校准后薪资占比") {
          this.getItemUnit(this.form, item, [
            "calibrationProjectSalaryDistributionProportion",
            "calibrationProjectSalaryExtensionProportion",
            "calibrationProjectSalaryBankProportion",
          ]);
        } else if (item.title == "校准后完成状态") {
          this.$set(
            item,
            "distributionIndex",
            this.distributionStateObj[
              this.form.calibrationDistributionIndexState
            ]
          );
          this.$set(
            item,
            "extensionBank",
            this.form.calibrationExtensionIndexBankState
          );
          this.$set(
            item,
            "extensionIndex",
            this.form.calibrationExtensionIndexProjectState
          );
        } else if (item.title == "前三季度补充业绩偏差值累计后薪资占比") {
          this.getItemUnit(this.form, item, [
            "distributionIndexDeviationProportionQ3",
            "extensionIndexDeviationProportionQ3",
            "extensionBankDeviationProportionQ3",
          ]);
        }
      });
    },
    getColumns() {
      this.columns = XEUtils.clone(this.columnsCommon, true);
      this.columns.forEach((item, index) => {
        if (index) {
          item.label = this.quarterObj[this.form.quarter] + item.label;
        }
      });
    },
    async getSpecial() {
      const data = XEUtils.clone(this.proForm, true);
      delete data.quarter;
      const { rows } = await selectCheckResultListofQ3(data);
      // const rows = [
      //   {
      //     quarter: 1,
      //     year: "2024",
      //     type: 2,
      //     companyName: "中保国信融资担保有限公司考核结果校准",
      //     deptName: "综合管理部",
      //     nickName: "张三",
      //     companyId: 1,
      //     deptId: 134,
      //     checkResult: {
      //       extensionIndex: 10,
      //       distributionIndex: 20,
      //       extensionBank: 30,

      //       distributionProportion: 4,
      //       extensionProportion: 5,

      //       completeDistributionIndex: 6,
      //       completeExtensionIndex: 7,
      //       completeExtensionBank: 8,

      //       projectSalaryDistributionProportion: 10,
      //       projectSalaryExtensionProportion: 11,
      //       bankSalaryExtensionProportion: 10,

      //       distributionIndexDeviation: -10,
      //       extensionIndexDeviation: -11,
      //       extensionBankDeviation: -10,

      //       distributionIndexDeviationYear: 101,
      //       extensionIndexDeviationYear: 1,
      //       extensionBankDeviationYear: 1012,

      //       calibrationDistributionIndexYearend: 101,
      //       calibrationExtensionIndexYearend: 111,
      //       calibrationExtensionBankYearend: 1,

      //       distributionIndexDeviationCalibrationYearend: 101,
      //       extensionIndexDeviationCalibrationYearend: 111,
      //       extensionBankDeviationCalibrationYearend: 101,

      //       calibrationProjectSalaryDistributionProportionYearend: 1,
      //       calibrationProjectSalaryExtensionProportionYearend: 122,
      //       calibrationProjectSalaryBankProportionYearend: 1,

      //       calibrationDistributionIndexStateYearend: 1,
      //       calibrationExtensionIndexProjectStateYearend: "已完成-项目",
      //       calibrationExtensionIndexBankStateYearend: "已完成-银行",

      //       distributionIndexDeviationProportionQ4: 111,
      //       extensionIndexDeviationProportionQ4: 111,
      //       extensionBankDeviationProportionQ4: 111,
      //     },
      //   },
      //   {
      //     quarter: 2,
      //     year: "2024",
      //     type: 2,
      //     companyName: "中保国信融资担保有限公司考核结果校准",
      //     deptName: "综合管理部",
      //     nickName: "张三",
      //     companyId: 1,
      //     deptId: 134,
      //     checkResult: {
      //       extensionIndex: 10,
      //       distributionIndex: 2,
      //       extensionBank: 3,

      //       distributionProportion: 4,
      //       extensionProportion: 5,

      //       completeDistributionIndex: 6,
      //       completeExtensionIndex: 7,
      //       completeExtensionBank: 8,

      //       projectSalaryDistributionProportion: 10,
      //       projectSalaryExtensionProportion: 11,
      //       bankSalaryExtensionProportion: 10,

      //       distributionIndexDeviation: -10,
      //       extensionIndexDeviation: -11,
      //       extensionBankDeviation: -10,

      //       distributionIndexDeviationYear: 101,
      //       extensionIndexDeviationYear: 1,
      //       extensionBankDeviationYear: 1012,

      //       calibrationDistributionIndexYearend: 101,
      //       calibrationExtensionIndexYearend: 111,
      //       calibrationExtensionBankYearend: 1,

      //       distributionIndexDeviationCalibrationYearend: 101,
      //       extensionIndexDeviationCalibrationYearend: 111,
      //       extensionBankDeviationCalibrationYearend: 101,

      //       calibrationProjectSalaryDistributionProportionYearend: 1,
      //       calibrationProjectSalaryExtensionProportionYearend: 122,
      //       calibrationProjectSalaryBankProportionYearend: 1,

      //       calibrationDistributionIndexStateYearend: 1,
      //       calibrationExtensionIndexProjectStateYearend: "已完成-项目",
      //       calibrationExtensionIndexBankStateYearend: "已完成-银行",

      //       distributionIndexDeviationProportionQ4: 111,
      //       extensionIndexDeviationProportionQ4: 111,
      //       extensionBankDeviationProportionQ4: 111,
      //     },
      //   },
      // ];
      if (!rows.length) return;
      this.formSpecial = XEUtils.clone(rows, true);
      this.getDeviationYears();
      this.getColumnsSpecial();
      this.getConfigListSpecial();
    },
    getDeviationYears() {
      this.formSpecial.forEach((item) => {
        this.formSpecialYear.distributionIndexDeviationCalibrationYearend =
          item.checkResult.distributionIndexDeviationCalibrationYearend;
        this.formSpecialYear.extensionIndexDeviationCalibrationYearend =
          item.checkResult.extensionIndexDeviationCalibrationYearend;
        this.formSpecialYear.extensionBankDeviationCalibrationYearend =
          item.checkResult.extensionBankDeviationCalibrationYearend;
      });
    },
    getConfigListSpecial() {
      this.configListSpecial = XEUtils.clone(this.configListInitSpecial, true);
      this.configListSpecial.forEach((item) => {
        this.formSpecial.forEach((item1) => {
          if (item.title == "年度计划") {
            this.getItemFixed(
              item1.checkResult,
              item,
              ["distributionIndex", "extensionIndex", "extensionBank"],
              [
                `distributionIndexQ${item1.quarter}`,
                `extensionIndexQ${item1.quarter}`,
                `extensionBankQ${item1.quarter}`,
              ]
            );
          } else if (item.title == "绩效占比") {
            this.getItemUnit(
              item1.checkResult,
              item,
              [
                "distributionProportion",
                "extensionProportion",
                "extensionProportion",
              ],
              [
                `distributionIndexQ${item1.quarter}`,
                `extensionIndexQ${item1.quarter}`,
                `extensionBankQ${item1.quarter}`,
              ]
            );
          } else if (item.title == "完成情况") {
            this.getItemFixed(
              item1.checkResult,
              item,
              [
                "completeDistributionIndex",
                "completeExtensionIndex",
                "completeExtensionBank",
              ],
              [
                `distributionIndexQ${item1.quarter}`,
                `extensionIndexQ${item1.quarter}`,
                `extensionBankQ${item1.quarter}`,
              ]
            );
          } else if (item.title == "薪资占比") {
            this.getItemUnit(
              item1.checkResult,
              item,
              [
                "projectSalaryDistributionProportion",
                "projectSalaryExtensionProportion",
                "bankSalaryExtensionProportion",
              ],
              [
                `distributionIndexQ${item1.quarter}`,
                `extensionIndexQ${item1.quarter}`,
                `extensionBankQ${item1.quarter}`,
              ]
            );
          } else if (item.title == "本季度项目业绩偏差") {
            this.getItemColor(
              item1.checkResult,
              item,
              ["distributionIndexDeviation", "extensionIndexDeviation"],
              ["extensionBankDeviation"],
              [
                `distributionIndexQ${item1.quarter}`,
                `extensionIndexQ${item1.quarter}`,
                `extensionBankQ${item1.quarter}`,
              ]
            );
          } else if (item.title == "本年度累计项目业绩偏差") {
            this.getItemColor(
              item1.checkResult,
              item,
              ["distributionIndexDeviationYear", "extensionIndexDeviationYear"],
              ["extensionBankDeviationYear"],
              [
                `distributionIndexQ${item1.quarter}`,
                `extensionIndexQ${item1.quarter}`,
                `extensionBankQ${item1.quarter}`,
              ]
            );
          } else if (item.title == "人事校准 - 年终") {
            this.getItemFixed(
              item1.checkResult,
              item,
              [
                "calibrationDistributionIndexYearend",
                "calibrationExtensionIndexYearend",
                "calibrationExtensionBankYearend",
              ],
              [
                `distributionIndexQ${item1.quarter}`,
                `extensionIndexQ${item1.quarter}`,
                `extensionBankQ${item1.quarter}`,
              ]
            );
          } else if (item.title == "校准后年度累计项目业绩偏差 - 年终") {
            this.getItemColor(
              item1.checkResult,
              item,
              [
                "distributionIndexDeviationCalibrationYearend",
                "extensionIndexDeviationCalibrationYearend",
              ],
              ["extensionBankDeviationCalibrationYearend"],
              [
                `distributionIndexQ${item1.quarter}`,
                `extensionIndexQ${item1.quarter}`,
                `extensionBankQ${item1.quarter}`,
              ]
            );
          } else if (item.title == "校准后薪资占比 - 年终") {
            this.getItemUnit(
              item1.checkResult,
              item,
              [
                "calibrationProjectSalaryDistributionProportionYearend",
                "calibrationProjectSalaryExtensionProportionYearend",
                "calibrationProjectSalaryBankProportionYearend",
              ],
              [
                `distributionIndexQ${item1.quarter}`,
                `extensionIndexQ${item1.quarter}`,
                `extensionBankQ${item1.quarter}`,
              ]
            );
          } else if (item.title == "校准后完成状态 - 年终") {
            this.$set(
              item,
              `distributionIndexQ${item1.quarter}`,
              this.distributionStateObj[
                item1.checkResult.calibrationDistributionIndexStateYearend
              ]
            );
            this.$set(
              item,
              `extensionBankQ${item1.quarter}`,
              item1.checkResult.calibrationExtensionIndexBankStateYearend
            );
            this.$set(
              item,
              `extensionIndexQ${item1.quarter}`,
              item1.checkResult.calibrationExtensionIndexProjectStateYearend
            );
          } else if (item.title == "补充至第四季度薪资占比") {
            this.getItemUnit(
              item1.checkResult,
              item,
              [
                "distributionIndexDeviationProportionQ4",
                "extensionIndexDeviationProportionQ4",
                "extensionBankDeviationProportionQ4",
              ],
              [
                `distributionIndexQ${item1.quarter}`,
                `extensionIndexQ${item1.quarter}`,
                `extensionBankQ${item1.quarter}`,
              ]
            );
          }
        });
      });
    },
    getColumnsSpecial() {
      const title = [
        {
          label: "",
          prop: "title",
          align: "center",
          minWidth: "390px",
        },
      ];
      const columnsSpecialInit = this.columnsSpecialInit.filter((item) =>
        this.formSpecial.map((item) => item.quarter).includes(item.special)
      );
      this.columnsSpecial = XEUtils.clone(
        [...title, ...columnsSpecialInit],
        true
      );
    },
    async geterSummary() {
      const { data } = await getCheckResultTotal(this.proForm);
      this.perSummaryObj = {
        ...data,
        companyName: this.form.companyName,
        deptName: this.form.deptName,
        nickName: this.form.nickName,
      };
      const perSummary = {
        "考核结果校准-公司": this.PerSummaryCompany,
        "考核结果校准-部门": this.PerSummaryDept,
        "考核结果校准-用户": this.PerSummaryUser,
      }[this.title];
      this.perSummary = perSummary.concat(this.PerSummaryCommon);
    },
    changeInput(value, itemValue) {
      value[itemValue] = decimal(value[itemValue], 6);
      this.getChange(value, itemValue, this.form);
    },
    bankShallPrevailChange(value) {
      if (value) {
        this.prevObj = {
          extensionIndexDeviation: this.form.extensionIndexDeviation,
          extensionIndexDeviationCalibration:
            this.form.extensionIndexDeviationCalibration,
          calibrationExtensionIndexProjectState:
            this.form.calibrationExtensionIndexProjectState,
        };
        this.form.extensionIndexDeviation = this.form.completeExtensionIndex;
        this.form.extensionIndexDeviationCalibration = floatAdd(
          this.form.extensionIndexDeviation,
          this.form.extensionIndexDeviationYear
        );
        this.form.calibrationExtensionIndexProjectState = "";
        this.getConfigList();
      } else {
        this.form.extensionIndexDeviation =
          this.prevObj.extensionIndexDeviation;
        this.form.extensionIndexDeviationCalibration =
          this.prevObj.extensionIndexDeviationCalibration;
        this.form.calibrationExtensionIndexProjectState =
          this.prevObj.calibrationExtensionIndexProjectState;
        this.getConfigList();
      }
    },

    getChange(value, itemValue, form) {
      if (itemValue == "distributionIndex") {
        form.calibrationDistributionIndex = Number(value[itemValue]);
        form.distributionIndexDeviation = form.distributionIndexDeviation || 0;
        if (form.distributionIndexDeviation >= 0) {
          form.distributionIndexDeviationCalibration = Number(
            floatSub(
              floatAdd(
                form.distributionIndexDeviationYear || 0,
                form.distributionIndexDeviation
              ),
              value[itemValue]
            )
          );
        } else {
          form.distributionIndexDeviationCalibration = Number(
            floatSub(form.distributionIndexDeviationYear, value[itemValue])
          );
        }

        if (form.completeDistributionIndex > form.extensionIndex) {
          form.calibrationProjectSalaryDistributionProportion =
            form.distributionProportion;
        } else {
          form.calibrationProjectSalaryDistributionProportion = floatMultiply(
            form.distributionProportion,
            floatDivide(
              floatAdd(value[itemValue], form.completeDistributionIndex),
              form.distributionIndex
            )
          ).toFixed(2);
          if (
            floatMultiply(
              form.calibrationProjectSalaryDistributionProportion,
              100
            ) > form.distributionProportion
          ) {
            form.calibrationProjectSalaryDistributionProportion =
              form.distributionProportion;
          }
        }
        form.calibrationDistributionIndexState =
          floatAdd(value[itemValue], form.completeDistributionIndex) >=
          form.extensionIndex
            ? "3"
            : "4";
        if (value[itemValue] == 0) {
          if (form.calibrationDistributionIndexState == 3)
            form.calibrationDistributionIndexState = 1;
          if (form.calibrationDistributionIndexState == 4)
            form.calibrationDistributionIndexState = 2;
        }
      } else if (itemValue == "extensionIndex") {
        form.calibrationExtensionIndex = Number(value[itemValue]);
        form.extensionIndexDeviation = form.extensionIndexDeviation || 0;
        if (form.extensionIndexDeviation >= 0) {
          form.extensionIndexDeviationCalibration = Number(
            floatSub(
              floatAdd(
                form.extensionIndexDeviationYear || 0,
                form.extensionIndexDeviation
              ),
              value[itemValue]
            )
          );
        } else {
          form.extensionIndexDeviationCalibration = Number(
            floatSub(form.extensionIndexDeviationYear, value[itemValue])
          );
        }

        if (form.completeExtensionIndex > form.distributionIndex) {
          form.calibrationProjectSalaryExtensionProportion =
            form.extensionProportion;
        } else {
          form.calibrationProjectSalaryExtensionProportion = floatMultiply(
            form.extensionProportion,
            floatDivide(
              floatAdd(value[itemValue], form.completeExtensionIndex),
              form.extensionIndex
            )
          ).toFixed(2);
          if (
            floatMultiply(
              form.calibrationProjectSalaryExtensionProportion,
              100
            ) > form.extensionProportion
          ) {
            form.calibrationProjectSalaryExtensionProportion =
              form.extensionProportion;
          }
        }
        this.getSelfStatus(value, itemValue, "extensionIndex", form);
      } else if (itemValue == "extensionBank") {
        form.calibrationExtensionBank = Number(value[itemValue]);
        form.extensionBankDeviation = form.extensionBankDeviation || 0;
        if (form.extensionBankDeviation >= 0) {
          form.extensionBankDeviationCalibration = Number(
            floatSub(
              floatAdd(
                form.extensionBankDeviationYear || 0,
                form.extensionBankDeviation
              ),
              value[itemValue]
            )
          );
        } else {
          form.extensionBankDeviationCalibration = Number(
            floatSub(form.extensionBankDeviationYear, value[itemValue])
          );
        }
        if (form.completeExtensionBank > form.extensionBank) {
          form.calibrationProjectSalaryBankProportion =
            form.extensionProportion;
        } else {
          form.calibrationProjectSalaryBankProportion = floatMultiply(
            form.extensionProportion,
            floatDivide(
              floatAdd(value[itemValue], form.completeExtensionBank),
              form.extensionBank
            )
          ).toFixed(2);
          if (
            floatMultiply(form.calibrationProjectSalaryBankProportion, 100) >
            form.extensionProportion
          ) {
            form.calibrationProjectSalaryBankProportion =
              form.extensionProportion;
          }
        }
        this.getSelfStatus(value, itemValue, "extensionBank", form);
      }
    },
    changeInputOther(value, itemValue) {
      const form = this.formSpecial.filter(
        (item) => item.quarter == itemValue.split("Q")[1]
      )[0].checkResult;
      if (itemValue.indexOf("distributionIndex") != -1) {
        form.calibrationDistributionIndexYearend = Number(value[itemValue]);
        form.distributionIndexDeviation = form.distributionIndexDeviation || 0;

        if (form.completeDistributionIndex > form.extensionIndex) {
          form.calibrationProjectSalaryDistributionProportionYearend =
            form.distributionProportion;
        } else {
          form.calibrationProjectSalaryDistributionProportionYearend =
            floatMultiply(
              form.distributionProportion,
              floatDivide(
                floatAdd(value[itemValue], form.completeDistributionIndex),
                form.distributionIndex
              )
            ).toFixed(2);
          if (
            floatMultiply(
              form.calibrationProjectSalaryDistributionProportionYearend,
              100
            ) > form.distributionProportion
          ) {
            form.calibrationProjectSalaryDistributionProportionYearend =
              form.distributionProportion;
          }
        }
        form.calibrationDistributionIndexStateYearend =
          floatAdd(value[itemValue], form.completeDistributionIndex) >
          form.extensionIndex
            ? 3
            : 4;
        form.distributionIndexDeviationProportionQ4 = floatSub(
          form.calibrationProjectSalaryDistributionProportionYearend,
          form.projectSalaryDistributionProportion
        );
      } else if (itemValue.indexOf("extensionIndex") != -1) {
        form.calibrationExtensionIndexYearend = Number(value[itemValue]);
        form.extensionIndexDeviation = form.extensionIndexDeviation || 0;

        if (form.completeExtensionIndex > form.distributionIndex) {
          form.calibrationProjectSalaryExtensionProportionYearend =
            form.extensionProportion;
        } else {
          form.calibrationProjectSalaryExtensionProportionYearend =
            floatMultiply(
              form.extensionProportion,
              floatDivide(
                floatAdd(value[itemValue], form.completeExtensionIndex),
                form.extensionIndex
              )
            ).toFixed(2);
          if (
            floatMultiply(
              form.calibrationProjectSalaryExtensionProportionYearend,
              100
            ) > form.extensionProportion
          ) {
            form.calibrationProjectSalaryExtensionProportionYearend =
              form.extensionProportion;
          }
        }
        this.getSelfStatusOther(value, itemValue, "extensionIndex", form);
        form.extensionIndexDeviationProportionQ4 = floatSub(
          form.calibrationProjectSalaryExtensionProportionYearend,
          form.projectSalaryExtensionProportion
        );
      } else if (itemValue.indexOf("extensionBank") != -1) {
        form.calibrationExtensionBankYearend = Number(value[itemValue]);
        form.extensionBankDeviation = form.extensionBankDeviation || 0;

        if (form.completeExtensionBank > form.extensionBank) {
          form.calibrationProjectSalaryBankProportionYearend =
            form.extensionProportion;
        } else {
          form.calibrationProjectSalaryBankProportionYearend = floatMultiply(
            form.extensionProportion,
            floatDivide(
              floatAdd(value[itemValue], form.completeExtensionBank),
              form.extensionBank
            )
          ).toFixed(2);
          if (
            floatMultiply(
              form.calibrationProjectSalaryBankProportionYearend,
              100
            ) > form.extensionProportion
          ) {
            form.calibrationProjectSalaryBankProportionYearend =
              form.extensionProportion;
          }
        }
        this.getSelfStatusOther(value, itemValue, "extensionBank", form);
        form.extensionBankDeviationProportionQ4 = floatSub(
          form.calibrationProjectSalaryBankProportionYearend,
          form.bankSalaryExtensionProportion
        );
      }

      let distributionIndex = 0;
      let extensionIndex = 0;
      let extensionBank = 0;
      this.formSpecial.forEach((item) => {
        distributionIndex = floatAdd(
          distributionIndex,
          item.checkResult.calibrationDistributionIndexYearend
        );
        extensionIndex = floatAdd(
          extensionIndex,
          item.checkResult.calibrationExtensionIndexYearend
        );
        extensionBank = floatAdd(
          extensionBank,
          item.checkResult.calibrationExtensionBankYearend
        );
      });
      this.formSpecial.forEach((item) => {
        item.checkResult.distributionIndexDeviationCalibrationYearend = Number(
          floatSub(
            item.checkResult.distributionIndexDeviationYear,
            distributionIndex
          )
        );
        item.checkResult.extensionIndexDeviationCalibrationYearend = Number(
          floatSub(item.checkResult.extensionIndexDeviationYear, extensionIndex)
        );
        item.checkResult.extensionBankDeviationCalibrationYearend = Number(
          floatSub(item.checkResult.extensionBankDeviationYear, extensionBank)
        );
      });

      this.getQ4();
    },
    getQ4() {
      let distributionIndex = 0;
      let extensionIndex = 0;
      let extensionBank = 0;
      this.formSpecial.forEach((item) => {
        distributionIndex = floatAdd(
          distributionIndex,
          item.checkResult.distributionIndexDeviationProportionQ4
        );
        extensionIndex = floatAdd(
          extensionIndex,
          item.checkResult.extensionIndexDeviationProportionQ4
        );
        extensionBank = floatAdd(
          extensionBank,
          item.checkResult.extensionBankDeviationProportionQ4
        );
      });
      this.form.distributionIndexDeviationProportionQ3 = floatAdd(
        this.form.calibrationProjectSalaryDistributionProportion,
        distributionIndex
      );
      this.form.extensionIndexDeviationProportionQ3 = floatAdd(
        this.form.calibrationProjectSalaryExtensionProportion,
        extensionIndex
      );
      this.form.extensionBankDeviationProportionQ3 = floatAdd(
        this.form.calibrationProjectSalaryBankProportion,
        extensionBank
      );
      this.getConfigList();
    },
    getSelfStatusOther(value, itemValue, type, form) {
      if (type == "extensionIndex") {
        if (
          floatAdd(form.completeExtensionIndex || 0, value[itemValue]) >=
          form.distributionIndex
        ) {
          form.calibrationExtensionIndexProjectStateYearend = "已完成-项目校准";
        } else {
          form.calibrationExtensionIndexProjectStateYearend = "未完成-项目校准";
        }
        if (value[itemValue] == 0) {
          form.calibrationExtensionIndexProjectStateYearend =
            form.calibrationExtensionIndexProjectStateYearend.slice(0, 3);
        }
      } else {
        if (
          floatAdd(form.completeExtensionBank || 0, value[itemValue]) >=
          form.extensionBank
        ) {
          form.calibrationExtensionIndexBankStateYearend = "已完成-银行校准";
        } else {
          form.calibrationExtensionIndexBankStateYearend = "未完成-银行校准";
        }
        if (value[itemValue] == 0) {
          form.calibrationExtensionIndexBankStateYearend =
            form.calibrationExtensionIndexBankStateYearend.slice(0, 3);
        }
      }
    },
    getSelfStatus(value, itemValue, type, form) {
      if (type == "extensionIndex") {
        if (
          floatAdd(form.completeExtensionIndex || 0, value[itemValue]) >=
          form.distributionIndex
        ) {
          form.calibrationExtensionIndexProjectState = "已完成-项目校准";
        } else {
          form.calibrationExtensionIndexProjectState = "未完成-项目校准";
        }
        if (value[itemValue] == 0) {
          form.calibrationExtensionIndexProjectState =
            form.calibrationExtensionIndexProjectState.slice(0, 3);
        }
      } else {
        if (
          floatAdd(form.completeExtensionBank || 0, value[itemValue]) >=
          form.extensionBank
        ) {
          form.calibrationExtensionIndexBankState = "已完成-银行校准";
        } else {
          form.calibrationExtensionIndexBankState = "未完成-银行校准";
        }
        if (value[itemValue] == 0) {
          form.calibrationExtensionIndexBankState =
            form.calibrationExtensionIndexBankState.slice(0, 3);
        }
      }
    },
    blurInput(value, itemValue) {
      if (itemValue != "extensionBank") {
        value[itemValue] =
          value[itemValue] && Number(value[itemValue]) > 0
            ? Number(value[itemValue]).toFixed(6)
            : 0;
      }
      this.getConfigList();
      if (this.form.quarter == 4) {
        this.getDeviationYears();
        this.getConfigListSpecial();
      }
    },
    async onSaveSpecial() {
      if (this.formSpecial.length) {
        if (
          //输入了四季度校准且其他三个季度有值时
          this.form.calibrationDistributionIndex >
            this.formSpecialYear.distributionIndexDeviationCalibrationYearend ||
          this.form.calibrationExtensionIndex >
            this.formSpecialYear.extensionIndexDeviationCalibrationYearend ||
          this.form.calibrationExtensionBank >
            this.formSpecialYear.extensionBankDeviationCalibrationYearend
        )
          return;
        //不输入四季度校准时
        if (
          this.formSpecialYear.distributionIndexDeviationCalibrationYearend <
            0 ||
          this.formSpecialYear.extensionIndexDeviationCalibrationYearend < 0 ||
          this.formSpecialYear.extensionBankDeviationCalibrationYearend < 0
        )
          return;
      } else {
        if (
          this.form.calibrationDistributionIndex >
            this.form.distributionIndexDeviationYear ||
          this.form.calibrationExtensionIndex >
            this.form.extensionIndexDeviationYear ||
          this.form.calibrationExtensionBank >
            this.form.extensionBankDeviationYear
        )
          return;
      }

      if (this.formSpecial.length) await replaceCheckResult(this.formSpecial);
      this.onSaveCommon();
    },
    async onSave() {
      if (
        this.form.calibrationDistributionIndex >
          this.form.distributionIndexDeviationYear ||
        this.form.calibrationExtensionIndex >
          this.form.extensionIndexDeviationYear ||
        this.form.calibrationExtensionBank >
          this.form.extensionBankDeviationYear
      )
        return;
      this.onSaveCommon();
    },
    async onSaveCommon() {
      const paramsTemp = XEUtils.clone({ checkResult: { ...this.form } }, true);
      const {
        companyId,
        deptId,
        name,
        params,
        quarter,
        year,
        userId,
        type,
        ...rest
      } = paramsTemp.checkResult;
      paramsTemp.companyId = companyId;
      paramsTemp.deptId = deptId;
      paramsTemp.name = name;
      paramsTemp.params = params;
      paramsTemp.quarter = quarter;
      paramsTemp.year = year;
      paramsTemp.userId = userId;
      paramsTemp.type = type;
      paramsTemp.checkResult = rest;
      await updateResult(paramsTemp);
      this.$message.success("保存成功");
      this.cancel();
    },
    cancel() {
      const obj = { path: "/perAppraisal/assessmentResults" };
      this.$tab.closeOpenPage(obj);
    },
    colunmStyle({ row, column, rowIndex, columnIndex }) {
      //第一列背景色
      if (columnIndex == 0) {
        return "background:#f8f8f9;font-weight:700;color: rgb(51, 51, 51);fontSize:18px;height:60px";
      }
    },
    headerStyle() {
      return {
        fontWeight: 700,
        fontSize: "18px",
        color: "rgb(51, 51, 51)",
        height: "60px",
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.green {
  ::v-deep .el-input__inner {
    color: rgba(191, 191, 0, 0.8);
  }
}
.el-backtop {
  width: 100px;
}
::v-deep .el-table__body {
  .cell {
    font-size: 16px;
  }
  .el-input__inner {
    font-size: 16px;
  }
}
</style>
