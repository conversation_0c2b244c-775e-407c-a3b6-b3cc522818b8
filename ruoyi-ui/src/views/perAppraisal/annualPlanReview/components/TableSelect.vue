<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      width="1050px"
      @open="handlerOpen"
    >
      <div class="pr-5 flex justify-between">
        <div class="mb-2 font-medium text-base">
          已选中{{ tableData.length - dataListCancel.length }}条
        </div>
        <div>
          <el-checkbox v-model="tableShow.fullName" @change="changeDetail"
            >展示公司全称</el-checkbox
          >
          <el-checkbox v-model="tableShow.tenThousand" @change="changeDetail"
            >以万为单位展示金额</el-checkbox
          >
          <el-checkbox v-model="tableShow.detail" @change="changeDetail"
            >展示指标详情</el-checkbox
          >
        </div>
      </div>
      <MyTable
        :columns="dataColumns"
        :source="dataList"
        :tree-props="{ children: 'children' }"
      >
        <template #operate="{ record }">
          <el-button type="text" @click="handleDelete(record)">删除</el-button>
        </template>
      </MyTable>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <span slot="footer">
        <div class="flex justify-end">
          <el-button type="primary" @click="onSubmit">确定</el-button>
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import tablePlan from "@/mixin/perAppraisal/tablePlan";
import vModelMixin from "@/mixin/v-model";
import XEUtils from "xe-utils";
export default {
  mixins: [vModelMixin, tablePlan],
  name: "TableSelect",
  props: {
    tableData: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
  },

  data() {
    return {
      dataListInit: [],
      dataList: [],
      dataListCancel: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
    };
  },
  computed: {
    dataColumns() {
      return this.columns.filter((item) => {
        if (item.key == "operate") {
          item.minWidth = "100px";
        }
        return item.key !== "status";
      });
    },
  },
  mounted() {},
  methods: {
    getList() {
      const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
      const end = start + this.queryParams.pageSize;
      this.dataList = this.dataListInit.slice(start, end);
      this.total = this.dataListInit.length;
    },
    handlerOpen() {
      this.columns = this.columnsInitY;
      this.dataListInit = XEUtils.clone(this.tableData, true);
      this.dataListInit = this.buildTree(this.dataListInit);
      this.queryParams.pageNum = 1;
      this.getList();
      this.deleteId = [];
      this.dataListCancel = [];
    },
    handleDelete(row) {
      const tableRowCancel = this.tableData.filter((item) => item.id == row.id);
      this.dataListCancel = this.dataListCancel.concat(tableRowCancel);
      const flatData = XEUtils.toTreeArray(this.dataListInit, {
        children: "children", // 指定子节点字段名
        clear:true
      });
     
      const tableRow = flatData.filter((item) => item.id != row.id);
      this.dataListInit = this.buildTree(tableRow);
      this.queryParams.pageNum = 1;
      this.getList();
    },
    onSubmit() {
      this.innerValue = false;
      this.$emit("on-submit-success-row", this.dataListCancel);
    },
    //扁平转树
    buildTree(arr) {
      // 首先找到 type 为 1 的数据作为根节点
      const roots = arr.filter((item) => item.type == 1);
      // 处理 type 为 2 的数据
      arr
        .filter((item) => item.type == 2)
        .forEach((item) => {
          // 找到对应的 type 为 1 的节点
          const parent = roots.find((root) => root.companyId == item.companyId&&root.year==item.year);
          if (parent) {
            // 找到对应的 parentDeptId 的节点
            const deptParent = arr.find(
              (node) => node.deptId == item.parentDeptId && node.type == 2&&node.year==item.year
            );
            if (deptParent) {
              if (!deptParent.children) {
                deptParent.children = [];
              }
              deptParent.children.push(item);
            } else {
              if (!parent.children) {
                parent.children = [];
              }
              parent.children.push(item);
            }
          } else {
            roots.push(item); // 如果找不到对应的 type 为 1 的节点，则放在第一层
          }
        });

      // 处理 type 为 3 的数据
      arr
        .filter((item) => item.type == 3)
        .forEach((item) => {
          // 尝试根据 deptId 找到对应的 type 为 2 的节点
          const type2Parent = arr.find(
            (node) => node.deptId == item.deptId && node.type == 2&&node.year==item.year
          );
          if (type2Parent) {
            if (!type2Parent.children) {
              type2Parent.children = [];
            }
            type2Parent.children.push(item);
          } else {
            // 如果找不到则根据 companyId 找到对应的 type 为 1 的节点
            const companyParent = roots.find(
              (root) => root.companyId == item.companyId&& root.type == 1&&root.year==item.year
            );
            if (companyParent) {
              if (!companyParent.children) {
                companyParent.children = [];
              }
              companyParent.children.push(item);
            } else {
              roots.push(item); // 如果都找不到对应关系，则放在第一层
            }
          }
        });

      return roots;
    },
  },
};
</script>
<style lang="less" scoped>
</style>