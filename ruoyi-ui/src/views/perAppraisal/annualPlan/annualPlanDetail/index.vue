<template>
  <div
    class="p-5 page-component__scroll overflow-y-auto"
    style="height: calc(100vh - 100px)"
  >
    <div class="flex justify-between px-5 mb-3 pr-10">
      <div style="font-size: 28px" class="font-bold">{{ proForm.year }}年</div>
      <div style="font-size: 25px" class="font-bold text-center">
        <div>{{ proForm.companyName }}</div>
        <div>{{ title.substring(2, 4) }}计划</div>
      </div>
      <div style="font-size: 25px" class="font-bold">
        {{
          ["部门", "用户"].includes(title.substring(2, 4))
            ? title.substring(2, 4)
            : ""
        }}<span v-show="proForm.nickName || proForm.deptName">: </span>
        <span style="color: #02a7f0">{{
          proForm.nickName || proForm.deptName
        }}</span>
      </div>
    </div>
    <div class="flex justify-between">
      <div
        v-for="(item, index) in headForm"
        :key="index"
        class="flex flex-1 mx-5 leading-9"
      >
        <div class="font-bold">{{ item.title }}</div>
        <el-input
          v-model="yearsHeader[item.value]"
          disabled
          class="mx-1"
          style="width: 200px"
        ></el-input>
        <div>{{ item.unit }}</div>
      </div>
    </div>
    <el-divider></el-divider>
    <div class="px-5">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <div v-for="(item, index) in quarterList" :key="index">
          <div class="flex">
            <div @click="item.show = !item.show" class="flex cursor-pointer">
              <div class="font-bold text-base">{{ item.title }}</div>
              <i
                class="el-icon-caret-bottom relative top-1 mr-10"
                v-if="item.show"
              ></i>
              <i class="el-icon-caret-top relative top-1 mr-10" v-else></i>
            </div>
            <div style="color: #02a7f0">{{ form.shortName }}</div>
            <div class="mx-1">年度累计</div>
            <div
              v-for="(item1, index1) in item.content"
              :key="index1"
              class="flex"
            >
              <div class="mr-1">
                {{ item1.label }}值:
                <span style="color: #02a7f0">{{
                  yearsItemHeader[item1.value] || "0.00"
                }}</span>
                {{ item1.unit }} <span v-show="index1 != 3">,</span>
              </div>
            </div>
          </div>
          <div
            :style="{
              height: item.show ? '224px' : '0px',
              transition: 'height 0.5s ',
            }"
            class="overflow-hidden pt-2"
          >
            <div
              v-for="(item1, index1) in item.content"
              :key="index1"
              class="flex leading-9"
            >
              <el-form-item
                :prop="item1.value"
                :label="item1.label"
                class="flex"
              >
                <el-input
                  :disabled="index1 == 0 || title.indexOf('查看') != -1"
                  v-model.trim="form[item1.value]"
                  @input="changeInput($event, item1.value)"
                  @blur="blurInput(item1.value)"
                  class="mr-2"
                  style="width: 200px"
                ></el-input>
              </el-form-item>
              <div>{{ item1.unit }}</div>
            </div>
          </div>
          <el-divider></el-divider>
        </div>
      </el-form>
    </div>
    <InBody>
      <div
        class="text-center fixed bottom-0  bg-white z-10 pb-2"
        style="width: calc(100% - 260px);left:260px"
      >
        <el-backtop
          target=".page-component__scroll "
          :bottom="5"
          :right="220"
          :visibility-height="20"
        >
          <div
            style="
               {
                height: 100%;
                width: 100%;
                background-color: #fff;
                text-align: center;
                line-height: 40px;
                color: #1989fa;
                font-size: 14px;
              }
            "
          >
            返回顶部
          </div>
        </el-backtop>
        <el-button
          @click="onSave"
          type="primary"
          v-show="title.indexOf('查看') == -1"
          >保存</el-button
        >
        <el-button @click="cancel" v-show="title.indexOf('查看') == -1"
          >取消</el-button
        >
        <el-button @click="cancel" v-show="title.indexOf('查看') != -1"
          >关闭</el-button
        >
      </div>
    </InBody>
  </div>
</template>

<script>
import config from "./config";
import { floatAdd, decimal } from "@/utils";
import {
  getAnnualPlanVo,
  annualPlan,
  annualPlanUpdate,
} from "@/api/perAppraisal/annualPlan";
import XEUtils from "xe-utils";

export default {
  name: "AnnualPlanDetail",
  data() {
    return {
      ...config,
      id: this.$route.params.id,
      title: this.$route.query.title,
      proForm: JSON.parse(this.$route.query.form),
      headFormValue: {},
      form: {
        q1DistributionIndex: "",
        q1ExtensionBank: "",
        q1ExtensionIndex: "",
        q1TotalIndex: "",
        q2DistributionIndex: "",
        q2ExtensionBank: "",
        q2ExtensionIndex: "",
        q2TotalIndex: "",
        q3DistributionIndex: "",
        q3ExtensionBank: "",
        q3ExtensionIndex: "",
        q3TotalIndex: "",
        q4DistributionIndex: "",
        q4ExtensionBank: "",
        q4ExtensionIndex: "",
        q4TotalIndex: "",
      },
    };
  },
  computed: {
    yearsHeader() {
      return {
        yearsTotal: floatAdd(
          this.form.q1TotalIndex,
          this.form.q2TotalIndex,
          this.form.q3TotalIndex,
          this.form.q4TotalIndex
        ),
        yearsDisTotal: floatAdd(
          this.form.q1DistributionIndex,
          this.form.q2DistributionIndex,
          this.form.q3DistributionIndex,
          this.form.q4DistributionIndex
        ),
        yearsSelfTotal: floatAdd(
          this.form.q1ExtensionIndex,
          this.form.q2ExtensionIndex,
          this.form.q3ExtensionIndex,
          this.form.q4ExtensionIndex
        ),
        yearsSelfBankTotal: floatAdd(
          this.form.q1ExtensionBank,
          this.form.q2ExtensionBank,
          this.form.q3ExtensionBank,
          this.form.q4ExtensionBank
        ),
      };
    },
    yearsItemHeader() {
      return {
        q1TotalIndex:
          this.form.q1TotalIndex && Number(this.form.q1TotalIndex).toFixed(2),
        q1DistributionIndex:
          this.form.q1DistributionIndex &&
          Number(this.form.q1DistributionIndex).toFixed(2),
        q1ExtensionIndex:
          this.form.q1ExtensionIndex &&
          Number(this.form.q1ExtensionIndex).toFixed(2),
        q1ExtensionBank: this.form.q1ExtensionBank,
        q2TotalIndex: floatAdd(
          this.form.q1TotalIndex,
          this.form.q2TotalIndex
        ).toFixed(2),
        q2DistributionIndex: floatAdd(
          this.form.q1DistributionIndex,
          this.form.q2DistributionIndex
        ).toFixed(2),
        q2ExtensionIndex: floatAdd(
          this.form.q1ExtensionIndex,
          this.form.q2ExtensionIndex
        ).toFixed(2),
        q2ExtensionBank: floatAdd(
          this.form.q1ExtensionBank,
          this.form.q2ExtensionBank
        ),
        q3TotalIndex: floatAdd(
          this.form.q1TotalIndex,
          this.form.q2TotalIndex,
          this.form.q3TotalIndex
        ).toFixed(2),
        q3DistributionIndex: floatAdd(
          this.form.q1DistributionIndex,
          this.form.q2DistributionIndex,
          this.form.q3DistributionIndex
        ).toFixed(2),
        q3ExtensionIndex: floatAdd(
          this.form.q1ExtensionIndex,
          this.form.q2ExtensionIndex,
          this.form.q3ExtensionIndex
        ).toFixed(2),
        q3ExtensionBank: floatAdd(
          this.form.q1ExtensionBank,
          this.form.q2ExtensionBank,
          this.form.q3ExtensionBank
        ),
        q4TotalIndex: floatAdd(
          this.form.q1TotalIndex,
          this.form.q2TotalIndex,
          this.form.q3TotalIndex,
          this.form.q4TotalIndex
        ).toFixed(2),
        q4DistributionIndex: floatAdd(
          this.form.q1DistributionIndex,
          this.form.q2DistributionIndex,
          this.form.q3DistributionIndex,
          this.form.q4DistributionIndex
        ).toFixed(2),
        q4ExtensionIndex: floatAdd(
          this.form.q1ExtensionIndex,
          this.form.q2ExtensionIndex,
          this.form.q3ExtensionIndex,
          this.form.q4ExtensionIndex
        ).toFixed(2),
        q4ExtensionBank: floatAdd(
          this.form.q1ExtensionBank,
          this.form.q2ExtensionBank,
          this.form.q3ExtensionBank,
          this.form.q4ExtensionBank
        ),
      };
    },
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.quarterList = XEUtils.clone(this.quarterListInit, true);
      this.getForm();
    },
    async getForm() {
      if (this.title.indexOf("新增") == -1) {
        const { data } = await getAnnualPlanVo(this.proForm);
        this.form = data;
      }
    },
    changeInput(value, itemValue) {
      this.limitValue(value, itemValue);
      this.getItemTotal(itemValue);
    },
    blurInput(itemValue) {
      if (
        ![
          "q1ExtensionBank",
          "q2ExtensionBank",
          "q3ExtensionBank",
          "q4ExtensionBank",
        ].includes(itemValue)
      ) {
        this.form[itemValue] =
          this.form[itemValue] && Number(this.form[itemValue]) > 0
            ? Number(this.form[itemValue]).toFixed(2)
            : "";
      }
    },
    limitValue(value, itemValue) {
      if (itemValue.indexOf("ExtensionBank") == -1) {
        this.form[itemValue] = decimal(value, 2);
      } else {
        this.form[itemValue] = value.replace(/\D/g, "");
      }
    },
    getItemTotal(itemValue) {
      if (["q1DistributionIndex", "q1ExtensionIndex"].includes(itemValue)) {
        this.form.q1TotalIndex = floatAdd(
          this.form.q1DistributionIndex,
          this.form.q1ExtensionIndex
        ).toFixed(2);
      } else if (
        ["q2DistributionIndex", "q2ExtensionIndex"].includes(itemValue)
      ) {
        this.form.q2TotalIndex = floatAdd(
          this.form.q2DistributionIndex,
          this.form.q2ExtensionIndex
        ).toFixed(2);
      } else if (
        ["q3DistributionIndex", "q3ExtensionIndex"].includes(itemValue)
      ) {
        this.form.q3TotalIndex = floatAdd(
          this.form.q3DistributionIndex,
          this.form.q3ExtensionIndex
        ).toFixed(2);
      } else if (
        ["q4DistributionIndex", "q4ExtensionIndex"].includes(itemValue)
      ) {
        this.form.q4TotalIndex = floatAdd(
          this.form.q4DistributionIndex,
          this.form.q4ExtensionIndex
        ).toFixed(2);
      }
    },
    onSave() {
      this.$refs.form.validate(async (valid, object) => {
        Object.keys(object).forEach((item) => {
          const index = Number(item.substring(1, 2)) - 1;
          this.quarterList[index].show = true;
        });
        if (valid) {
          if (this.form.id) {
            await annualPlanUpdate({ ...this.proForm, ...this.form });
          } else {
            await annualPlan({ ...this.proForm, ...this.form });
          }
          this.$message.success("保存成功");
          this.cancel();
        }
      });
    },
    cancel() {
      const obj = { path: "/perAppraisal/annualPlan" };
      this.$tab.closeOpenPage(obj);
    },
  },
};
</script>
<style lang="scss" scoped>
.el-backtop {
  width: 100px;
}
::v-deep .el-form-item__content {
  margin-left: 0px !important;
}
</style>
