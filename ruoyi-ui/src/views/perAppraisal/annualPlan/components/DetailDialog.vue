<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="typeObj[type]"
      :visible.sync="innerValue"
      width="550px"
      @close="handleClose"
      @open="handleOpen"
    >
      <div>
        <MyForm
          ref="form"
          v-model="myForm"
          :columns="formColumnsDialog"
          formType="form"
          :rules="rules"
        >
          <template #userId>
            <el-form-item label="用户姓名" prop="nickName">
              <el-input
                @click.native="openUserDepPosts"
                placeholder="请选择用户姓名"
                v-model="myForm.nickName"
                suffix-icon="el-icon-search"
              >
              </el-input>
            </el-form-item>
          </template>
        </MyForm>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button type="primary" @click="onSubmit" class="mr-3"
            >下一步</el-button
          >
          <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        </div>
      </span>
      <UserDepPostSelect
        rowKey="userId"
        title="user"
        :deptIdPro="myForm.deptId"
        :companyIdPro="myForm.companyId"
        :multiple="false"
        v-model="openUserDepPost"
        @on-submit-success-user="userSuccess"
      />
    </el-dialog>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import { getAnnualPlanVo } from "@/api/perAppraisal/annualPlan";
import { haveAuthorityCompanyList } from "@/api/businessInformation/companyInformation";
import { treeselect } from "@/api/system/dept";
import { findNodeById } from "@/utils";

export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    type: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      ...config,
      myForm: {},
      openUserDepPost: false,
    };
  },
  watch: {},
  mounted() {},
  methods: {
    handleOpen() {
      this.formColumnsDialog = {
        1: this.formColumnsDialogCompany,
        2: this.formColumnsDialogDept,
        3: this.formColumnsDialogUser,
      }[this.type];
      this.rules = {
        1: this.rulesCompany,
        2: this.rulesDept,
        3: this.rulesUser,
      }[this.type];
      this.myForm = XEUtils.clone(this.form, true);
      if (this.type == 3 && this.myForm.mainDeptId) {
        this.myForm.deptId = this.myForm.mainDeptId;
      }
      this.$nextTick(() => this.$refs["form"].clearValidate());
      this.getDeptList(this.form.companyId);
      this.getCompanyList();
    },
    async getCompanyList() {
      const { rows } = await haveAuthorityCompanyList({ isInside: 1 });
      this.formColumnsDialog[1].options = rows;
    },
    async getDeptList(value) {
      const { data } = await treeselect({ unitId: value });
      const newData = data[0]?.children || [];
      if (this.formColumnsDialog[2])
        this.formColumnsDialog[2].options =
          this.type == 3 && this.myForm.mainDeptId ? data : [...newData];
    },
    openUserDepPosts() {
      if (this.type == 3 && this.form.mainDeptId) {
        this.$set(this.myForm, "nickName", "");
        this.$set(this.myForm, "deptId", this.form.mainDeptId);
      }
      this.openUserDepPost = true;
    },
    userSuccess(value) {
      this.$set(this.myForm, "nickName", value[0].nickName);
      this.$set(this.myForm, "deptId", value[0].deptId);
      this.myForm.userId = value[0].userId;
    },
    onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const { data } = await getAnnualPlanVo({
            year: this.myForm.year,
            companyId: this.myForm.companyId,
            deptId: this.myForm.deptId,
            userId: this.myForm.userId,
            type: this.type,
          });
          const obj = {
            1: this.submitCompany,
            2: this.submitDept,
            3: this.submitUser,
          };
          await obj[this.type](data);
          this.$emit("on-submit-success", this.myForm);
        }
      });
    },

    getProForm() {
      const companyName = this.formColumnsDialog[1].options.filter(
        (item) => item.id == this.myForm.companyId
      )[0].companyName;
      const deptName = ["2", "3"].includes(this.type)
        ? findNodeById(this.formColumnsDialog[2].options, this.myForm.deptId)
            .label
        : "";

      return {
        year: this.myForm.year,
        companyName,
        companyId: this.myForm.companyId,
        nickName: this.myForm.nickName,
        userId: this.myForm.userId,
        deptId: this.myForm.deptId,
        deptName,
        type: this.type,
      };
    },
    async submitCompany(value) {
      if (["1", "4"].includes(value?.state)) {
        const content = `【${value.companyShortName}】公司【${value.year}】年度计划已存在，您后续的操作将转换为修改操作，确定请点击【下一步】`;
        this.$confirm(content, "提示", {
          confirmButtonText: "下一步",
          confirmButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.innerValue = false;
            this.$router.push({
              path: `/perAppraisalOther/annualPlan/${value.companyId}`,
              query: {
                title: "修改年度计划",
                form: JSON.stringify(this.getProForm()),
              },
            });
          })
          .catch(() => {});
      } else if (["2", "3"].includes(value?.state)) {
        const content = `【${value.companyShortName}】公司【${value.year}】年度计划已在审核中或已审核通过，请勿重复新增`;
        this.$confirm(content, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.innerValue = false;
          })
          .catch(() => {});
      } else {
        this.$router.push({
          path: `/perAppraisalOther/annualPlan/addCompany`,
          query: {
            title: "新增年度计划",
            form: JSON.stringify(this.getProForm()),
          },
        });
      }
    },
    async submitDept(value) {
      if (["1", "4"].includes(value?.state)) {
        const content = `【${value.companyShortName}】公司【${value.year}】年度 【${value.deptName}】的计划已存在，您后续的操作将转换为修改操作，确定请点击【下一步】`;
        this.$confirm(content, "提示", {
          confirmButtonText: "下一步",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.innerValue = false;

            this.$router.push({
              path: `/perAppraisalOther/annualPlan/${value.deptId}`,
              query: {
                title: "修改部门计划",
                form: JSON.stringify(this.getProForm()),
              },
            });
          })
          .catch(() => {});
      } else if (["2", "3"].includes(value?.state)) {
        const content = `【${value.companyShortName}】公司【${value.year}】年度 【${value.deptName}】的计划已在审核中或已审核通过，请勿重复新增`;
        this.$confirm(content, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.innerValue = false;
          })
          .catch(() => {});
      } else {
        this.$router.push({
          path: `/perAppraisalOther/annualPlan/addDept`,
          query: {
            title: "新增部门计划",
            form: JSON.stringify(this.getProForm()),
          },
        });
      }
    },
    async submitUser(value) {
      if (["1", "4"].includes(value?.state)) {
        const content = `【${value.companyShortName}】公司【${value.year}】年度 【${value.deptName}】【${value.nickName}】的年度计划已存在，您后续的操作将转换为修改操作，确定请点击【下一步】`;
        this.$confirm(content, "提示", {
          confirmButtonText: "下一步",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.innerValue = false;
            this.$router.push({
              path: `/perAppraisalOther/annualPlan/${value.userId}`,
              query: {
                title: "修改用户计划",
                form: JSON.stringify(this.getProForm()),
              },
            });
          })
          .catch(() => {});
      } else if (["2", "3"].includes(value?.state)) {
        const content = `用户【${value.nickName}】【${value.year}】年度的计划已在审核中或已审核通过，请勿重复新增`;
        this.$confirm(content, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.innerValue = false;
          })
          .catch(() => {});
      } else {
        this.$router.push({
          path: `/perAppraisalOther/annualPlan/addUser`,
          query: {
            title: "新增用户计划",
            form: JSON.stringify(this.getProForm()),
          },
        });
      }
    },
    handleClose() {},
  },
};
</script>
