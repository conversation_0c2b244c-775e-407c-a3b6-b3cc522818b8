<template>
  <div>
    <el-dialog
      title="查看薪资调整记录"
      :visible.sync="dialogVisible"
      width="850px"
      :before-close="handleClose"
    >
      <div class="search">
        <div class="item">
          <span>姓名</span>
          <p>{{ data.personName }}</p>
        </div>
        <div class="item">
          <span>所属部门</span>
          <p>{{ data.archivesDeptName }}</p>
        </div>
      </div>
      <div class="search">
        <div class="item">
          <span>直接上级</span>
          <p>{{ data.leaderName }}</p>
        </div>
        <div class="item">
          <span style="margin-left: 28px;">岗位</span>
          <p>{{ data.archivesPostName }}</p>
        </div>
      </div>
      <div>
        <el-table
          v-if="data && data.bodyData && data.bodyData.length > 0"
          :data="data.bodyData"
          ref="table"
          style="width: 100%; margin-top: 16px; margin-left: 4px"
        >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="oldRecordMoney"
            label="调整前薪资(元)"
            width="220"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="newRecordMoney"
            width="220"
            label="调整后薪资(元)"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="kind"
            label="调薪种类"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="effectiveDate"
            width="220"
            label="调整生效日期"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="createByName"
            label="发起人"
          />
          <el-table-column
            align="center"
            width="220"
            show-overflow-tooltip=""
            prop="createTime"
            label="发起时间"
          />
          <el-table-column
            align="center"
            width="220"

            show-overflow-tooltip=""
            prop="auditTime"
            label="审批通过时间"
          />
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { payrollFileTx } from "@/api/oa/processConfig";
export default {
  props: {
    id: String || Number,
  },
  data() {
    return {
      dialogVisible: true,
      tableData: [],
      data: null,
    };
  },
  mounted() {
    payrollFileTx(this.id).then((res) => {
      this.data = res.data;
    });
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    display: flex;
    align-items: center;
    margin-right: 16px;
    span {
      margin-right: 9px;
      display: inline-block;
      width: 100px;
      text-align: right;
    }
    p{
      font-weight: bold;
    }
  }
}
.el-button {
  height: 36px;
  margin-left: 4px;
  margin-right: 12px;
}
.solid {
  width: 100%;
  height: 1px;
  background: #f2f2f2;
  margin-top: 12px;
}
p {
  margin-bottom: 0;
}
</style>