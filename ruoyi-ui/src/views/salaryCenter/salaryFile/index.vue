<template>
  <div style="height: 100%">
    <div class="search">
      <div class="item">
        <span>员工姓名</span>
        <el-input
          v-model="params.personName"
          clearable=""
          placeholder="请输入员工姓名"
          style="width: 200px"
        ></el-input>
      </div>

      <div class="item">
        <span>所属部门</span>
        <el-select
          placeholder="请选择所属部门"
          clearable=""
          @clear="delml"
          style="width: 200px"
          v-model="params.archivesDept"
          ref="selectUpResId"
        >
          <el-option
            hidden
            :value="params.archivesDept"
            :label="params.pertainDeptName"
          >
          </el-option>
          <el-tree
            :data="deTreeList"
            :props="defaultProps2"
            :expand-on-click-node="false"
            :check-on-click-node="true"
            @node-click="dehandleNodeClick"
          >
          </el-tree>
        </el-select>
      </div>
      <div class="item">
        <span>身份证号码</span>
        <el-input
          placeholder="请输入身份证号码"
          v-model="params.idCard"
          clearable=""
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>岗位</span>
        <el-select
          placeholder="请选择岗位"
          v-model="params.archivesPost"
          filterable=""
          clearable=""
          style="width: 200px"
        >
          <el-option
            v-for="item in postList"
            :key="item.postId"
            :value="item.postId"
            :label="item.postName"
          ></el-option>
        </el-select>
      </div>
      <div class="item">
        <span>薪资区间</span>
        <el-input
          placeholder="请输入两位小数"
          v-model="params.beginMoney"
          clearable=""
          style="width: 150px"
        ></el-input>
        <span style="margin: 0 10px">-</span>
        <el-input
          placeholder="请输入两位小数"
          v-model="params.endMoney"
          clearable=""
          style="width: 150px"
        ></el-input>
      </div>
      <div class="item">
        <span>系统登陆名</span>
        <el-input
          placeholder="请输入系统登录名"
          v-model="params.sysName"
          clearable=""
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>人员状态</span>
        <el-select
          placeholder="请选择人员状态"
          v-model="params.personnelState"
          clearable=""
          style="width: 200px"
        >
          <el-option
             v-for="dict in dict.type.personnel_status"
             :key="dict.value"
             :label="dict.label"
             :value="dict.value"
          />
        </el-select>
      </div>
      <div>
        <el-button type="primary" icon="el-icon-search" @click="search"
          >搜 索</el-button
        >
        <el-button icon="el-icon-refresh" @click="reset">重 置</el-button>
      </div>
    </div>
    <div class="solid"></div>
    <div style="padding: 16px; padding-bottom: 0">
      <el-button
        style="border-color: #ffc26c; background: #fff8e6; color: #ffc10d"
        icon="el-icon-download"
        type="primary"
        size="mini"
        @click="dc"
        >导出列表</el-button
      >
      <el-button
        style="border-color: #ffb3c3; background: #ffeded; color: #ff9f9f"
        icon="el-icon-delete"
        type="primary"
        size="mini"
        @click="del"
        >删除</el-button
      >
      <el-button
        style="border-color: #ffc26c; background: #fff8e6; color: #ffc10d"
        icon="el-icon-upload2"
        type="primary"
        size="mini"
        @click="uploadType = true"
        >批量导入</el-button
      >
      <el-button
        type="primary"
        size="mini"
        :disabled="selectList.length > 1"
        @click="seeHistory"
        >查看薪资调整记录</el-button
      >
    </div>
    <div style="padding: 16px; padding-top: 0px">
      <el-table
        :data="tableData"
        row-key="id"
        ref="table"
        @selection-change="handleSelectionChange"
        style="width: 100%; margin-top: 16px; margin-left: 4px"
      >
        <el-table-column
          :reserve-selection="true"
          type="selection"
          width="55"
        ></el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="payrollfileCode"
          label="薪资档案编号"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="personName"
          label="员工姓名"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="detail(scope.row)">{{
              scope.row.personName
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="archivesPostName"
          label="岗位"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="archivesDeptName"
          label="部门"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="idCard"
          label="身份证号码"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="billSourceName"
          label="单据来源"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          fixed="right"
          width="160"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button @click="send(scope.row)" type="text" size="small"
              >发起薪资调整流程</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="params.pageNum"
        :limit.sync="params.pageSize"
        @pagination="getList"
      />
    </div>
    <AddItem
      :itemDetail="itemDetail"
      v-if="addItemType"
      @close="addItemType = false"
    />
    <el-dialog
      title="导入"
      :visible.sync="uploadType"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        :file-list="upload.fileList"
        :on-change="changeFileList"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span style="color: red"
            >提示：仅允许导入一个xls或xlsx格式文件。</span
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="importTemplate"
          style="background: #13ce66; color: #fff; border-color: #13ce66"
          >下载模板</el-button
        >
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="uploadType = false">取 消</el-button>
      </div>
    </el-dialog>
    <History :id="selectId" v-if="historyType" @close="historyType = false" />
    <personData
      :id="selectId"
      v-if="personDataType"
      @close="personDataType = false"
    />
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import History from "./History.vue";
import AddItem from "./AddItem.vue";
import { treeselect } from "@/api/system/dept";
import { getPostAuthorizationList } from "@/api/directoryMation/directoryMation";
import personData from "./personData.vue";
import {
  payrollFileList,
  payrollFileDetail,
  payrollFileDel,
  getDraftId,
} from "@/api/oa/processConfig";
export default {
  components: {
    AddItem,
    History,
    personData,
  },
  dicts: ["personnel_status"],
  data() {
    return {
      personDataType: false,
      selectId: "",
      historyType: false,
      selectCompanyType: false,
      uploadType: false,
      upload: {
        // 是否禁用上传
        fileList: [],
        isUploading: false,

        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API + "/payrollFile/payrollFile/importData",
      },
      itemDetail: null,
      addItemType: false,
      deTreeList: [],
      defaultProps2: {
        children: "children",
        label: "label",
      },
      tableData: [],
      total: 0,
      params: {
        pageNum: 1,
        pageSize: 10,
        personName: "",
        pertainDeptName: "",
        idCard: "",
        archivesDept: "",
        archivesPost: "",
        beginMoney: "",
        endMoney: "",
        sysName: "",
        personnelState: "",
      },
      selectList: [],
      postList: [],
      upList: [],
    };
  },
  mounted() {
    getPostAuthorizationList().then((res) => {
      this.postList = res.rows;
    });
    treeselect().then((res) => {
      this.deTreeList = res.data;
      this.deTreeList.forEach((item) => {
        item.type = true;
      });
    });
    //this.getList();
  },
  methods: {
    ss() {
      return;
      console.log("123");
    },
    detail(v) {
      this.selectId = v.id;
      this.personDataType = true;
    },
    seeHistory() {
      if (this.selectList.length > 1 || this.selectList.length == 0) {
        this.$message.warning("请选择一条数据");
        return;
      }
      this.selectId = this.selectList[0].id;
      this.historyType = true;
    },
    del() {
      if (this.selectList.length == 0) {
        this.$message.warning("请选择数据");
        return;
      }
      this.$confirm(
        `是否确认删除${this.selectList.map(
          (item) => item.personName
        )}的薪资档案`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          payrollFileDel(this.selectList.map((item) => item.id)).then((res) => {
            if (res.code == 200) {
              this.$message.success(res.msg);
              this.$refs.table.clearSelection();
              this.selectList = [];
              this.getList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    submitFileForm() {
      this.$refs.upload.submit();
    },

    importTemplate() {
      this.download(
        "/payrollFile/payrollFile/singleOwn",
        {},
        `导入薪资档案模板.xlsx`
      );
    },
    dc() {
      this.download("/payrollFile/payrollFile/export", {}, `薪资档案列表.xlsx`);
    },
    changeFileList(file, fileList) {
      console.log(fileList);
      if (fileList.length > 0) {
        this.upload.fileList = [fileList[fileList.length - 1]];
      }
    },
    handleFileUploadProgress(response, file, fileList) {
      console.log(response, file, fileList);
    },
    handleFileSuccess(response, file, fileList) {
      console.log(response, file, fileList);
      if (response.code == 200) {
        this.$message.success(response.msg);
        this.uploadType = false;
        this.$refs.upload.clearFiles();
        this.getList();
      } else {
        this.$message.warning(response.msg);
      }
    },
    handleSelectionChange(e) {
      this.selectList = e;
    },
    delml() {
      this.params.archivesDept = "";
      this.params.pertainDeptName = "";
    },
    dehandleNodeClick(data) {
      console.log(data);
      if (data.type) {
        return;
      }
      this.params.archivesDept = data.id;
      this.params.pertainDeptName = data.label;
      this.$refs.selectUpResId.blur();
    },
    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    reset() {
      this.params = {
        pageNum: 1,
        pageSize: 10,
        personName: "",
        idCard: "",
        archivesDept: "",
        archivesPost: "",
        beginMoney: "",
        endMoney: "",
        sysName: "",
        personnelState: "",
      };
      //this.getList();
      this.tableData = [];
      this.total = 0;
      this.$refs.table.clearSelection();
      this.params.pageNum = 1;
      this.loading = false;
    },
    getList() {
      payrollFileList(this.params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.total = res.total;
        }
      });
    },
    newData() {},
    send(v) {
      getDraftId(v.id).then((res) => {
        this.itemDetail = res.data;
        this.addItemType = true;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    margin-right: 16px;
    span {
      margin-right: 9px;
    }
  }
}
.el-button {
  height: 36px;
  margin-left: 4px;
  margin-right: 12px;
}
.solid {
  width: 100%;
  height: 1px;
  background: #f2f2f2;
  margin-top: 12px;
}
</style>
