<template>
  <div>
    <div class="search">
      <div class="item">
        <span>所属账套</span>
        <el-select
          placeholder="请选择账套"
          style="width: 250px"
          v-model="queryParams.accountSetId"
        >
          <el-option
            v-for="item in accList"
            :key="item.id"
            :label="item.companyName"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>
      <div class="item">
        <span>凭证编号</span>
        <el-select
          style="width: 80px; margin-right: 3px"
          placeholder=" "
          v-model="queryParams.codeName"
        >
          <el-option value="记" label="记"></el-option>
          <el-option value="收" label="收"></el-option>
          <el-option value="付" label="付"></el-option>
          <el-option value="转" label="转"></el-option>
        </el-select>
        <el-input
          v-model="queryParams.voucharCode"
          placeholder="请输入凭证编号"
          style="width: 180px"
        ></el-input>
      </div>
      <div class="item">
        <span>制单人</span>
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入制单人姓名"
          style="width: 250px"
        ></el-input>
      </div>
      <div class="item">
        <span>借方科目</span>
        <el-input
          v-model="queryParams.borrowSubject"
          placeholder="请输入借方科目"
          style="width: 250px"
        ></el-input>
      </div>
      <div class="item">
        <span>贷方科目</span>
        <el-input
          v-model="queryParams.loanSubject"
          placeholder="请输入贷方科目"
          style="width: 250px"
        ></el-input>
      </div>
      <el-button type="primary" @click="search">搜 索</el-button>
      <el-button @click="reset">重 置</el-button>
    </div>
    <div class="solid"></div>
    <div class="btn">
      <el-button
        @click="handleExport"
        style="border-color: #ffc26c; background: #fff8e6; color: #ffc30d"
        icon="el-icon-download"
        size="mini"
        >导出列表</el-button
      >
      <el-button
        icon="el-icon-refresh"
        style="margin-right: 16px"
        @click="getList"
        >刷新</el-button
      >
    </div>
    <div style="padding: 16px; padding-top: 0">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column
          show-overflow-tooltip=""
          prop="accountSetName"
          label="所属账套"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="voucharCode"
          label="凭证编号"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="voucharDate"
          label="凭证日期"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="borrowSubject"
          label="借方科目"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="borrowAmount"
          label="借方金额(元)"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="loanSubject"
          label="贷方科目"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="loanAmount"
          label="贷方金额(元)"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="voucharAbstract"
          label="摘要"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="userName"
          label="制单人"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="amountSum"
          label="合计金额(元)"
        />

        <el-table-column label="操作" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" v-if="scope.row.generateStatus==0" @click="toTheme(scope.row)"
              >跳转凭证页</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="queryParams.total > 0"
        :total="queryParams.total"
        :page.sync="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

  <script>
import { voucharList, getAllAccountSetsList } from "@/api/oa/financeProcess";

export default {
  data() {
    return {
      tableData: [],
      accList: [],
      queryParams: {
        voucharId: "",
        loanSubject: "",
        borrowSubject: "",
        voucharCode: "",
        userName: "",
        codeName: "",
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
    };
  },
  mounted() {
    getAllAccountSetsList().then((res) => {
      this.accList = res;
    });
    this.getList();
  },
  methods: {
    toTheme(v) {
      if (process.env.NODE_ENV == "production") {
        window.open(`http://oa.jhrs.top:11009/voucher/redirect/${v.accountSetId}/${v.voucharId}`);
      } else if (process.env.NODE_ENV == "testbeta") {
        window.open(`http://*************:21009/voucher/redirect/${v.accountSetId}/${v.voucharId}`);
      } else if (process.env.NODE_ENV == "testbeta2") {
        window.open(`http://*************:31009/voucher/redirect/${v.accountSetId}/${v.voucharId}`);
      } else if (process.env.NODE_ENV == "uat"){
        window.open(`http://oatest.jhrs.top:21009/voucher/redirect/${v.accountSetId}/${v.voucharId}`);
      } else if (process.env.NODE_ENV == "uat2"){
        window.open(`http://oatest.jhrs.top:31009/voucher/redirect/${v.accountSetId}/${v.voucharId}`);
      } else if (process.env.NODE_ENV == "uat3"){
        window.open(`http://oatest.jhrs.top:41009/voucher/redirect/${v.accountSetId}/${v.voucharId}`);
      }
    },
    handleExport() {
      this.download(
        "/oasystem/caiwuDataquery/voucharExport",
        {
          ...this.queryParams,
        },
        `凭证详情导出.xlsx`
      );
    },

    search() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      voucharList({
        ...this.queryParams,
        voucharCode: this.queryParams.codeName + this.queryParams.voucharCode,

        pfdId: this.$route.query.pfdId,
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.queryParams.total = res.total;
        }
      });
    },
    reset() {
      this.queryParams = {
        voucharId: "",
        loanSubject: "",
        borrowSubject: "",
        voucharCode: "",
        userName: "",
        codeName: "",
        pageSize: 10,
        pageNum: 1,
      };

      this.getList();
    },
  },
};
</script>

  <style lang="less" scoped>
.btn {
  padding: 16px 0;
  display: flex;
  justify-content: space-between;
}
.search {
  padding: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    margin-right: 16px;
    display: flex;
    align-items: center;
  }
  span {
    margin-right: 9px;
  }
}

.el-button {
  height: 36px;
  margin-left: 16px;
}
.solid {
  width: 100%;
  height: 10px;
  background: #f8f8f9;
}
/deep/ .el-select .el-input__inner {
  height: 36px !important;
}
</style>
