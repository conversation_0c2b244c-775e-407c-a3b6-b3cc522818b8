<template>
  <div style="font-size: 14px !important" class="app-container" id="myActivite">
    <div>
      <el-form
        :model="queryParams"
        @submit.native.prevent
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="关键字">
          <el-input
            v-model="queryParams.theme"
            placeholder="请输入内容"
            @keyup.enter.native="handleQuery"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
          <el-button @click="showType = !showType" type="text"
            >{{ showType ? "收起" : "更多搜索"
            }}<i v-if="!showType" class="el-icon-arrow-down"></i
            ><i v-else class="el-icon-arrow-up"></i
          ></el-button>
        </el-form-item>
      </el-form>
      <el-divider v-if="showType"></el-divider>
      <el-form
        v-if="showType && showSearch"
        :model="queryParams"
        @submit.native.prevent
        ref="queryForm"
        :inline="true"
      >
        <el-form-item label="发起人">
          <el-input
            v-model="queryParams.initiatorNickName"
            placeholder="请输入"
            @keyup.enter.native="handleQuery"
          ></el-input>
        </el-form-item>
        <el-form-item label="发起时间">
          <el-date-picker
            @keyup.enter.native="handleQuery"
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            @keyup.enter.native="handleQuery"
            v-model="queryParams.type"
            clearable=""
          >
            <el-option label="全部" value=""></el-option
            ><el-option label="财务类流程" value="1"></el-option
            ><el-option label="普通流程" value="0"></el-option
          ></el-select>
        </el-form-item>
      </el-form>
    </div>
    <el-button
      class="send_btn"
      type="primary"
      @click="$router.push('/oaWork/initiatingProcess')"
      >发起流程</el-button
    >
    <div style="width: 100%; height: 30px"></div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="我的流程" name="first">
        <template slot="label">
          <el-badge v-if="myflow > 0" :value="myflow" type="warning" :max="99">
            <span style="width: 100px">我的流程</span>
          </el-badge>
        </template>

        <el-tabs v-model="activeName1" @tab-click="handleClickLi1">
          <el-tab-pane label="审批中" name="first">
            <template slot="label">
              <el-badge v-if="sping > 0" :value="sping" type="info" :max="99">
                <span>审批中</span>
              </el-badge>
            </template>

            <el-button
              style="float: right"
              size="mini"
              @click="getList"
              icon="el-icon-refresh"
              >刷新</el-button
            >
            <el-table
              :data="tableData11"
              @row-click="goProcessForm"
              style="width: 100%"
            >
              <el-table-column prop="theme" label="主题" width="520">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    style="white-space: initial; text-align: left"
                    @click="goProcessForm(scope.row)"
                    >{{ scope.row.theme }}</el-button
                  >
                  <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" width="80" label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="shortName"
                label="公司"
                width="110"
              ></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column
                prop="nodeName"
                label="当前节点"
              ></el-table-column>
              <el-table-column label="当前处理人">
                <template slot-scope="scope">
                  <span style="color: #cccccc">{{
                    scope.row.userStatus1
                  }}</span>
                  <span v-if="scope.row.processor != '没有找到处理人'">{{
                    scope.row.processor
                  }}</span>
                  <span
                    v-if="scope.row.processor == '没有找到处理人'"
                    style="color: red"
                    >{{ scope.row.processor }}</span
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="已驳回" name="second">
            <template slot="label">
              <el-badge v-if="ybhing > 0" :value="ybhing" type="info" :max="99">
                <span>已驳回</span>
              </el-badge>
            </template>

            <el-table
              :data="tableData12"
              style="width: 100%"
              @row-click="goProcessForm"
            >
              <el-table-column prop="theme" label="主题" width="520">
                <template slot-scope="scope">
                  <span v-show="scope.row.readFlag == 'T'">
                    <el-button
                      type="text"
                      style="
                        white-space: initial;
                        text-align: left;
                        color: #5e87b9;
                      "
                      @click="goProcessForm(scope.row)"
                      >{{ scope.row.theme }}</el-button
                    >
                  </span>

                  <span v-show="scope.row.readFlag == 'F'">
                    <el-button
                      type="text"
                      style="white-space: initial; text-align: left"
                      @click="goProcessForm(scope.row)"
                      >{{ scope.row.theme }}</el-button
                    >
                  </span>
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" width="100" label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                  <span v-if="scope.row.status == '10'">{{
                    "审核不通过"
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="shortName"
                label="公司"
                width="110"
              ></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column
                prop="nodeName"
                label="当前节点"
              ></el-table-column>
              <el-table-column label="当前处理人">
                <template slot-scope="scope">
                  {{ scope.row.processor }}
                  <span style="color: #cccccc">{{
                    scope.row.userStatus1
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    v-show="false"
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click.stop="handleDelete(scope.row, '4')"
                    >删除</el-button
                  >
                  <el-button
                    v-show="scope.row.status==10"
                    size="mini"
                    type="text"
                    @click.stop="knownInformed(scope.row)"
                    >已知悉</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="草稿" name="third">
            <template slot="label">
              <el-badge v-if="caogao > 0" :value="caogao" type="info" :max="99">
                <span>草稿</span>
              </el-badge>
            </template>

            <el-table
              :data="tableData13"
              style="width: 100%"
              @row-click="goProcessForm"
            >
              <el-table-column prop="theme" label="主题" width="520">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    style="white-space: initial; text-align: left"
                    @click="goProcessForm(scope.row)"
                    >{{ scope.row.theme }}</el-button
                  >
                  <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="shortName"
                label="公司"
                width="110"
              ></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column prop="nodeName" label="当前节点"
                >-</el-table-column
              >
              <el-table-column prop="processor" label="当前处理人"
                >-</el-table-column
              >
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click.stop="handleDelete(scope.row, '5')"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="已结束" name="fourth">
            <el-table
              :data="tableData14"
              style="width: 100%"
              @row-click="goProcessForm"
            >
              <el-table-column prop="theme" label="主题" width="520">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    style="white-space: initial; text-align: left"
                    @click="goProcessForm(scope.row)"
                    >{{ scope.row.theme }}</el-button
                  >
                  <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                  <span v-if="scope.row.status == '11'">{{ "结束" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="shortName"
                label="公司"
                width="110"
              ></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column prop="updateTime" label="结束时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template></el-table-column
              >
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="已废弃" name="fieth">
            <el-table
              :data="tableData15"
              style="width: 100%"
              @row-click="goProcessForm"
            >
              <el-table-column prop="theme" label="主题" width="520">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    style="white-space: initial; text-align: left"
                    @click="goProcessForm(scope.row)"
                    >{{ scope.row.theme }}</el-button
                  >
                  <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="shortName"
                label="公司"
                width="110"
              ></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column prop="updateTime" label="终止时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template></el-table-column
              >
              <!-- <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click.stop="handleDelete(scope.row, '3')"
                    >删除</el-button
                  >
                </template>
              </el-table-column> -->
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>

      <el-tab-pane label="待我审批" name="second">
        <template slot="label">
          <el-badge
            v-if="myApproval > 0"
            :value="myApproval"
            type="warning"
            :max="99"
          >
            <span>待我审批</span>
          </el-badge>
        </template>

        <el-tabs v-model="activeName2" @tab-click="handleClickLi2">
          <el-tab-pane label="待我审批" name="first">
            <template slot="label">
              <el-badge
                v-if="daiwoshenpi > 0"
                :value="daiwoshenpi"
                type="info"
                :max="99"
              >
                <span>待我审批</span>
              </el-badge>
            </template>
            <el-row>
              <div style="width: 100%; height: 30px; line-height: 30px">
                <el-button
                  style="float: right"
                  @click="getList"
                  icon="el-icon-refresh"
                  >刷新</el-button
                >
              </div>
            </el-row>
            <el-table
              :data="tableData21"
              style="width: 100%"
              @row-click="goshenpiProcessForm"
            >
              <el-table-column prop="theme" label="主题" width="520">
                <template slot-scope="scope">
                  <span v-show="scope.row.readFlag == 'T'">
                    <el-button
                      type="text"
                      style="
                        white-space: initial;
                        text-align: left;
                        color: #5e87b9;
                      "
                      @click="goshenpiProcessForm(scope.row)"
                      >{{ scope.row.theme }}</el-button
                    >
                  </span>
                  <span v-show="scope.row.readFlag == 'F'">
                    <el-button
                      type="text"
                      style="white-space: initial; text-align: left"
                      @click="goshenpiProcessForm(scope.row)"
                      >{{ scope.row.theme }}</el-button
                    >
                  </span>
                  <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column label="申请人">
                <template slot-scope="scope">
                  <el-button
                    slot="reference"
                    type="text"
                    @click.stop="getuser(scope.row.createBy)"
                  >
                    {{ scope.row.nickName }}
                    <span style="color: #cccccc">{{
                      scope.row.userStatus
                    }}</span>
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="acreateTime" label="待审时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.createdDate, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="lastPass" label="状态" width="80">
                <template slot-scope="scope">
                  <span v-if="scope.row.lastPass == '1'">{{ "审批中" }}</span>
                  <span v-if="scope.row.lastPass == '6'">{{ "加签" }}</span>
                  <span v-if="scope.row.lastPass == '2'">{{ "审批中" }}</span>
                  <span v-if="scope.row.lastPass == '3'">{{ "废弃" }}</span>
                  <span v-if="scope.row.lastPass == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.lastPass == '5'">{{ "转签" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="shortName"
                label="公司"
                width="110"
              ></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column prop="name" label="当前节点"></el-table-column>
              <el-table-column prop="loginUser" label="当前处理人">
                <template slot-scope="scope">
                  {{ scope.row.loginUser }}
                  <span style="color: #cccccc">{{
                    scope.row.loginUserStatus
                  }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="我已审批" name="second">
            <el-row>
              <div style="width: 100%; height: 30px; line-height: 30px">
                <el-button
                  style="float: right"
                  @click="getList"
                  icon="el-icon-refresh"
                  >刷新</el-button
                >
              </div>
            </el-row>
            <el-table
              :data="tableData22"
              style="width: 100%"
              @row-click="goProcessForm"
            >
              <el-table-column prop="theme" label="主题" width="520">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    style="white-space: initial; text-align: left"
                    @click="goProcessForm(scope.row)"
                    >{{ scope.row.theme }}</el-button
                  >
                  <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column label="申请人">
                <template slot-scope="scope">
                  <el-button
                    slot="reference"
                    type="text"
                    @click.stop="getuser(scope.row.createBy)"
                  >
                    {{ scope.row.nickName }}
                    <span style="color: #cccccc">{{
                      scope.row.userStatus
                    }}</span>
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="acreateTime" label="审批时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.create_time, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="shortName"
                label="公司"
                width="110"
              ></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column prop="NAME_" label="当前节点"></el-table-column>
              <el-table-column prop="processor" label="当前处理人">
                <template slot-scope="scope">
                  {{ scope.row.processor }}
                  <span style="color: #cccccc">{{
                    scope.row.userStatus1
                  }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="我已驳回" name="third">
            <el-row>
              <div style="width: 100%; height: 30px; line-height: 30px">
                <el-button
                  style="float: right"
                  @click="getList"
                  icon="el-icon-refresh"
                  >刷新</el-button
                >
              </div>
            </el-row>
            <el-table
              :data="tableData23"
              style="width: 100%"
              @row-click="goProcessForm"
            >
              <el-table-column prop="theme" label="主题" width="520">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    style="white-space: initial; text-align: left"
                    @click="goProcessForm(scope.row)"
                    >{{ scope.row.theme }}</el-button
                  >
                  <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column label="申请人">
                <template slot-scope="scope">
                  <el-button
                    slot="reference"
                    type="text"
                    @click.stop="getuser(scope.row.createBy)"
                  >
                    {{ scope.row.nickName }}
                    <span style="color: #cccccc">{{
                      scope.row.userStatus
                    }}</span>
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="审批时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.create_time, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="shortName"
                label="公司"
                width="110"
              ></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column prop="NAME_" label="当前节点"></el-table-column>
              <el-table-column prop="processor" label="当前处理人">
                <template slot-scope="scope">
                  {{ scope.row.processor }}
                  <span style="color: #cccccc">{{
                    scope.row.userStatus1
                  }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="抄送给我" name="fourth">
            <template slot="label">
              <el-badge
                v-if="copyToMe > 0"
                :value="copyToMe"
                type="info"
                :max="99"
              >
                <span>抄送给我</span>
              </el-badge>
            </template>
            <el-row>
              <div style="width: 100%; height: 30px; line-height: 30px">
                <el-button
                  style="float: right"
                  @click="getList"
                  icon="el-icon-refresh"
                  >刷新</el-button
                >
                <el-button
                  style="white-space: initial"
                  type="primary"
                  icon="el-icon-edit"
                  :disabled="multiple"
                  @click="batchUpdateCopyReadFlag"
                  >批量已读</el-button
                >
                <el-button
                  style="white-space: initial"
                  type="primary"
                  icon="el-icon-edit"
                  :disabled="copyToMe === 0"
                  @click="updateCopyReadFlag"
                  >全部已读</el-button
                >
              </div>
            </el-row>
            <el-table
              :data="tableData24"
              style="width: 100%"
              @row-click="goCopyProcessForm"
              @selection-change="copySelectionChange"
            >
              <el-table-column
                v-if="copyToMe > 0"
                type="selection"
                width="55"
                align="center"
              />
              <el-table-column prop="theme" label="主题" width="300">
                <template slot-scope="scope">
                  <span v-show="scope.row.readFlag == '1'">
                    <el-button
                      type="text"
                      style="
                        white-space: initial;
                        text-align: left;
                        color: #5e87b9;
                      "
                      @click="goCopyProcessForm(scope.row)"
                      >{{ scope.row.theme }}</el-button
                    >
                  </span>
                  <span v-show="scope.row.readFlag == '0'">
                    <el-button
                      type="text"
                      style="white-space: initial; text-align: left"
                      @click="goCopyProcessForm(scope.row)"
                      >{{ scope.row.theme }}</el-button
                    >
                  </span>
                  <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column label="申请人">
                <template slot-scope="scope">
                  <el-button
                    slot="reference"
                    type="text"
                    @click.stop="getuser(scope.row.createBy)"
                  >
                    {{ scope.row.nickName }}
                    <span style="color: #cccccc">{{
                      scope.row.userStatus
                    }}</span>
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="shortName"
                label="公司"
                width="110"
              ></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column
                prop="nodeName"
                label="当前节点"
              ></el-table-column>
              <el-table-column prop="processor" label="当前处理人">
                <template slot-scope="scope">
                  {{ scope.row.processor }}
                  <span style="color: #cccccc">{{
                    scope.row.userStatus1
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="readFlag" label="是否已读">
                <template slot-scope="scope">
                  <span v-if="scope.row.readFlag == '0'">{{ "未读" }}</span>
                  <span v-if="scope.row.readFlag == '1'">{{ "已读" }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>

      <el-tab-pane label="待我阅览" name="third">
        <template slot="label">
          <el-badge v-if="myRed > 0" :value="myRed" type="warning" :max="99">
            <span>待我阅览</span>
          </el-badge>
        </template>

        <el-tabs v-model="activeName3" @tab-click="handleClickLi3">
          <el-tab-pane label="审批中" name="first">
            <template slot="label">
              <el-badge
                v-if="shenpizhong > 0"
                :value="shenpizhong"
                type="info"
                :max="99"
              >
                <span>审批中</span>
              </el-badge>
            </template>
            <el-row>
              <div style="width: 100%; height: 30px; line-height: 30px">
                <el-button
                  style="float: right"
                  @click="getList"
                  icon="el-icon-refresh"
                  >刷新</el-button
                >
                <el-button
                  style="white-space: initial"
                  type="primary"
                  icon="el-icon-edit"
                  :disabled="multiple"
                  @click="batchUpdateReadFlagFormData"
                  >批量已读</el-button
                >
                <el-button
                  style="white-space: initial"
                  type="primary"
                  icon="el-icon-edit"
                  :disabled="shenpizhong === 0"
                  @click="updateReadFlagFormData"
                  >全部已读</el-button
                >
              </div>
            </el-row>
            <el-table
              :data="tableData31"
              @row-click="onlyReadGoProcessForm"
              @selection-change="handleSelectionChange"
              style="width: 100%"
            >
              <el-table-column
                v-if="shenpizhong > 0"
                type="selection"
                width="55"
                align="center"
              />
              <el-table-column prop="theme" label="主题">
                <template slot-scope="scope">
                  <span v-show="scope.row.readFlag == 'T'">
                    <el-button
                      type="text"
                      style="
                        white-space: initial;
                        text-align: left;
                        color: #5e87b9;
                      "
                      @click="onlyReadGoProcessForm(scope.row)"
                      >{{ scope.row.theme }}
                    </el-button>
                  </span>
                  <span v-show="scope.row.readFlag == 'F'">
                    <el-button
                      type="text"
                      style="white-space: initial; text-align: left"
                      @click="onlyReadGoProcessForm(scope.row)"
                      >{{ scope.row.theme }}
                    </el-button>
                  </span>
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column prop="createName" label="申请人">
                <template slot-scope="scope">
                  <el-popover placement="right" width="200" trigger="click">
                    <div>
                      <span style="font-size: 20px; font-weight: bold">{{
                        nickName
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">公司</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        companyName
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">部门</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        bumen
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">岗位</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        gangwei
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">手机</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        phone
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">邮箱</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        email
                      }}</span>
                      <br />
                    </div>
                    <el-button
                      slot="reference"
                      type="text"
                      @click="getuser(scope.row.createBy)"
                      >{{ scope.row.createName }}</el-button
                    >
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "终止" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="shortName" label="公司"></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column
                prop="nodeName"
                label="当前节点"
              ></el-table-column>
              <el-table-column
                prop="handlers"
                label="当前处理人"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="已驳回" name="second">
            <template slot="label">
              <el-badge
                v-if="yibohui > 0"
                :value="yibohui"
                type="info"
                :max="99"
              >
                <span>已驳回</span>
              </el-badge>
            </template>
            <el-row>
              <div style="width: 100%; height: 30px; line-height: 30px">
                <el-button
                  style="float: right"
                  @click="getList"
                  icon="el-icon-refresh"
                  >刷新</el-button
                >
                <el-button
                  style="white-space: initial"
                  type="primary"
                  icon="el-icon-edit"
                  :disabled="multiple"
                  @click="batchUpdateReadFlagFormData"
                  >批量已读</el-button
                >
                <el-button
                  style="white-space: initial"
                  type="primary"
                  icon="el-icon-edit"
                  :disabled="yibohui === 0"
                  @click="updateReadFlagFormData"
                  >全部已读</el-button
                >
              </div>
            </el-row>
            <el-table
              :data="tableData32"
              @row-click="onlyReadGoProcessForm"
              @selection-change="handleSelectionChange"
              style="width: 100%"
            >
              <el-table-column
                v-if="yibohui > 0"
                type="selection"
                width="55"
                align="center"
              />
              <el-table-column prop="theme" label="主题">
                <template slot-scope="scope">
                  <span v-show="scope.row.readFlag == 'T'">
                    <el-button
                      type="text"
                      style="
                        white-space: initial;
                        text-align: left;
                        color: #5e87b9;
                      "
                      @click="onlyReadGoProcessForm(scope.row)"
                      >{{ scope.row.theme }}
                    </el-button>
                  </span>
                  <span v-show="scope.row.readFlag == 'F'">
                    <el-button
                      type="text"
                      style="white-space: initial; text-align: left"
                      @click="onlyReadGoProcessForm(scope.row)"
                      >{{ scope.row.theme }}
                    </el-button>
                  </span>
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column prop="createName" label="申请人">
                <template slot-scope="scope">
                  <el-popover placement="right" width="200" trigger="click">
                    <div>
                      <span style="font-size: 20px; font-weight: bold">{{
                        nickName
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">公司</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        companyName
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">部门</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        bumen
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">岗位</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        gangwei
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">手机</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        phone
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">邮箱</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        email
                      }}</span>
                      <br />
                    </div>
                    <el-button
                      slot="reference"
                      type="text"
                      @click="getuser(scope.row.createBy)"
                      >{{ scope.row.createName }}</el-button
                    >
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "终止" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="shortName" label="公司"></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column
                prop="nodeName"
                label="当前节点"
              ></el-table-column>
              <el-table-column
                prop="handlers"
                label="当前处理人"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="已结束" name="third">
            <template slot="label">
              <el-badge
                v-if="yijieshu > 0"
                :value="yijieshu"
                type="info"
                :max="99"
              >
                <span>已结束</span>
              </el-badge>
            </template>
            <el-row>
              <div style="width: 100%; height: 30px; line-height: 30px">
                <el-button
                  style="float: right"
                  @click="getList"
                  icon="el-icon-refresh"
                  >刷新</el-button
                >
                <el-button
                  style="white-space: initial"
                  type="primary"
                  icon="el-icon-edit"
                  :disabled="multiple"
                  @click="batchUpdateReadFlagFormData"
                  >批量已读</el-button
                >
                <el-button
                  style="white-space: initial"
                  type="primary"
                  icon="el-icon-edit"
                  :disabled="yijieshu === 0"
                  @click="updateReadFlagFormData"
                  >全部已读</el-button
                >
              </div>
            </el-row>
            <el-table
              :data="tableData33"
              @row-click="onlyReadGoProcessForm"
              @selection-change="handleSelectionChange"
              style="width: 100%"
            >
              <el-table-column
                v-if="yijieshu > 0"
                type="selection"
                width="55"
                align="center"
              />
              <el-table-column prop="theme" label="主题">
                <template slot-scope="scope">
                  <span v-show="scope.row.readFlag == 'T'">
                    <el-button
                      type="text"
                      style="
                        white-space: initial;
                        text-align: left;
                        color: #5e87b9;
                      "
                      @click="onlyReadGoProcessForm(scope.row)"
                      >{{ scope.row.theme }}
                    </el-button>
                  </span>
                  <span v-show="scope.row.readFlag == 'F'">
                    <el-button
                      type="text"
                      style="white-space: initial; text-align: left"
                      @click="onlyReadGoProcessForm(scope.row)"
                      >{{ scope.row.theme }}
                    </el-button>
                  </span>
                  <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                    >紧急</span
                  >
                  <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                    >较急</span
                  >
                </template>
              </el-table-column>
              <el-table-column prop="createName" label="申请人">
                <template slot-scope="scope">
                  <el-popover placement="right" width="200" trigger="click">
                    <div>
                      <span style="font-size: 20px; font-weight: bold">{{
                        nickName
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">公司</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        companyName
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">部门</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        bumen
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">岗位</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        gangwei
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">手机</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        phone
                      }}</span>
                      <br />
                      <span style="font-size: 14px; color: #afadad">邮箱</span>
                      <span style="font-size: 14px; margin-left: 20px">{{
                        email
                      }}</span>
                      <br />
                    </div>
                    <el-button
                      slot="reference"
                      type="text"
                      @click="getuser(scope.row.createBy)"
                      >{{ scope.row.createName }}
                    </el-button>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
                  <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
                  <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
                  <span v-if="scope.row.status == '3'">{{ "终止" }}</span>
                  <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
                  <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="shortName" label="公司"></el-table-column>
              <el-table-column prop="opcName" label="分类"></el-table-column>
              <el-table-column
                prop="templateName"
                label="模板"
              ></el-table-column>
              <el-table-column
                prop="nodeName"
                label="当前节点"
              ></el-table-column>
              <el-table-column
                prop="handlers"
                label="当前处理人"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
    </el-tabs>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[15, 30, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <UserDetail
      v-model="userDetailType"
      @close="userDetailType = false"
      :id="userId"
    />
  </div>
</template>

<script>
import { listTask, formDataShow, formDataSave } from "@/api/flow/task";
import { getUserProfile } from "@/api/system/user";
import { deleteRejected } from "@/api/certificate/allLicenses";
import {
  started,
  history,
  getByCheck,
  delForm,
  getUserData,
  getTabsSums,
  batchUpdateReadFlagFormData,
  updateReadFlagFormData,
  updateReadFlag,
  batchUpdateCopyReadFlag,
  knownFlowByBusinessId,
} from "@/api/flow/flow";
export default {
  name: "MyActivite",
  data() {
    return {
      userId: "",
      userDetailType: false,
      time: [],
      showType: false,
      vis: false,
      nickName: "",
      companyName: "",
      bumen: "",
      gangwei: "",
      phone: "",
      email: "",
      woyibohui: 0,
      woyishenpi: 0,
      daiwoshenpi: 0,
      zhongzhi: 0,
      overing: 0,
      caogao: 0,
      ybhing: 0,
      sping: 0,
      myApproval: 0,
      readOnlyTotal: 0,
      myflow: 0,
      myRed: 0,
      shenpizhong: 0,
      yibohui: 0,
      yijieshu: 0,
      copyToMe: 0,
      tableData11: [],
      tableData12: [],
      tableData13: [],
      tableData14: [],
      tableData15: [],
      tableData21: [],
      tableData22: [],
      tableData23: [],
      tableData24: [],
      tableData31: [],
      tableData32: [],
      tableData33: [],
      activeName: "first",
      activeName1: "first",
      activeName2: "first",
      activeName3: "first",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      businessIds: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 15,
        theme: "",
        input: "",
        initiatorNickName: "",
        activeNameFirst: "",
        activeNameSecend: "",
      },
      // 表单参数
      form: {},
      otherTab1: "true",
      otherTab2: "true",
    };
  },
  created() {
    this.getList();
  },
  watch: {
    $route: {
      handler(newval, oldval) {
        console.log(newval);
        this.getList();
      },
      deep: true,
    },
  },
  methods: {
    //查看
    goProcessForm(item) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: item.oid,
          businessId: item.businessId,
          myActiviteType: true,
          readFlag: item.readFlag,
          currentStatus: item.status,
        },
      });
    },
    //待我阅览查看
    onlyReadGoProcessForm(item) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: item.oid,
          businessId: item.businessId,
          myActiviteType: true,
          readFlag: item.readFlag,
          currentStatus: item.status,
          ifOnlyRead: true,
        },
      });
    },
    //抄送处理页面
    goCopyProcessForm(item) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: item.oid,
          businessId: item.businessId,
          myActiviteType: true,
          copyTaskFlag: true,
          copyTaskId: item.id,
          readFlag: item.readFlag,
          currentStatus: item.status,
        },
      });
    },
    goshenpiProcessForm(item) {
      this.$router.push({
        path: "/oaWork/approveProcessForm",
        query: {
          oid: item.oid,
          businessId: item.businessKey,
          readFlag: item.readFlag,
          taskId: item.id,
        },
      });
    },
    //审批
    goshenpiProcessForm23(item) {
      this.$router.push({
        path: "/oaWork/approveProcessForm",
        query: { oid: item.oid, businessId: item.businessId },
      });
    },
    getTabsDataNum() {
      getTabsSums(this.queryParams).then((response) => {
        this.myflow = response.t1;
        this.myApproval = response.t2;
        this.myRed = response.readOnlyTotal;
        this.sping = response.t11;
        this.ybhing = response.t12;
        this.caogao = response.t13;
        // this.overing = response.t14;
        // this.zhongzhi = response.t15;
        this.daiwoshenpi = response.t21;
        // this.woyishenpi = response.t22;
        this.woyibohui = response.t23;
        this.copyToMe = response.t24;
        this.shenpizhong = response.readOnlyApproveNum;
        this.yibohui = response.readOnlyRejectNum;
        this.yijieshu = response.readOnlyEndNum;
      });
    },
    getuser(userId) {
      getUserData(userId).then((response) => {
        this.userId = response.data.userId;
        this.userDetailType = true;
      });
    },
    handleClick(tab, event) {
      this.$router.replace({});
      this.queryParams.activeNameFirst = this.activeName;
      if (this.activeName == "first") {
        this.queryParams.activeNameSecend = this.activeName1;
      } else if (this.activeName == "second") {
        this.queryParams.activeNameSecend = this.activeName2;
      } else if (this.activeName == "third") {
        this.queryParams.activeNameSecend = this.activeName3;
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleClickLi1(tab, event) {
      this.$router.replace({});
      this.queryParams.activeNameFirst = this.activeName;
      this.queryParams.activeNameSecend = this.activeName1;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleClickLi2(tab, event) {
      this.$router.replace({});
      this.queryParams.activeNameFirst = this.activeName;
      this.queryParams.activeNameSecend = this.activeName2;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleClickLi3(tab, event) {
      this.queryParams.activeNameFirst = this.activeName;
      this.queryParams.activeNameSecend = this.activeName3;
      this.getList();
    },

    /** 查询【请填写功能名称】列表 */
    async getList() {
      this.loading = true;
      this.otherTab1 = this.$route.query.tab1;
      this.otherTab2 = this.$route.query.tab2;
      if (this.otherTab1 != undefined && this.otherTab2 != undefined) {
        this.activeName = this.otherTab1;
        if (this.otherTab1 == "first") {
          this.activeName1 = this.otherTab2;
        } else if (this.otherTab1 == "second") {
          this.activeName2 = this.otherTab2;
        }
        if (this.otherTab1 == "third") {
          this.activeName3 = this.otherTab2;
        }
      }
      if (this.activeName == "first" && this.activeName1 == "first") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName1;

        getByCheck(this.queryParams).then((response) => {
          this.tableData11 = response.rows;
          this.sping = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "first" && this.activeName1 == "second") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName1;

        getByCheck(this.queryParams).then((response) => {
          this.tableData12 = response.rows;
          this.ybhing = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "first" && this.activeName1 == "third") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName1;
        getByCheck(this.queryParams).then((response) => {
          this.tableData13 = response.rows;
          this.caogao = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "first" && this.activeName1 == "fourth") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName1;
        getByCheck(this.queryParams).then((response) => {
          this.tableData14 = response.rows;
          this.overing = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "first" && this.activeName1 == "fieth") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName1;
        getByCheck(this.queryParams).then((response) => {
          this.tableData15 = response.rows;
          this.zhongzhi = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "second" && this.activeName2 == "first") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName2;
        getByCheck(this.queryParams).then((response) => {
          this.tableData21 = response.rows;
          this.daiwoshenpi = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "second" && this.activeName2 == "second") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName2;
        getByCheck(this.queryParams).then((response) => {
          this.tableData22 = response.rows;
          this.woyishenpi = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "second" && this.activeName2 == "third") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName2;
        getByCheck(this.queryParams).then((response) => {
          this.tableData23 = response.rows;
          this.woyibohui = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "second" && this.activeName2 == "fourth") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName2;
        getByCheck(this.queryParams).then((response) => {
          this.tableData24 = response.rows;
          this.copyToMe = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "third" && this.activeName3 == "first") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName3;

        getByCheck(this.queryParams).then((response) => {
          this.tableData31 = response.rows;
          this.shenpizhong = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "third" && this.activeName3 == "second") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName3;

        getByCheck(this.queryParams).then((response) => {
          this.tableData32 = response.rows;
          this.yibohui = response.total;
          this.total = response.total;
        });
      } else if (this.activeName == "third" && this.activeName3 == "third") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName3;

        getByCheck(this.queryParams).then((response) => {
          this.tableData33 = response.rows;
          this.yijieshu = response.total;
          this.total = response.total;
        });
      }

      this.getTabsDataNum();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        businessType: null,
        fundShortName: null,
        fundFullName: null,
        productShortName: null,
        productFullName: null,
        custShortName: null,
        custFullName: null,
        userId: null,
        projectDate: null,
        projectStatus: "0",
        scheduleStatus: "0",
        lockStatus: "0",
        overTime: null,
        status: "0",
        createTime: null,
        createBr: null,
        updateTime: null,
        updateBr: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.startTime = this.$format(this.time[0], "yyyy-MM-dd");
        this.queryParams.endTime = this.$format(this.time[1], "yyyy-MM-dd");
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      if (this.queryParams.type == 2) {
        this.queryParams.flowType = null;
      } else {
        this.queryParams.flowType = this.queryParams.type;
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.theme = "";
      this.time = [];
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.queryParams.type = "";

      this.queryParams.initiatorNickName = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.businessIds = selection.map((item) => item.businessId);
      console.log("this.businessIds为", this.businessIds);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 多选框选中数据
    copySelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      console.log("this.ids为", this.ids);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      // this.open = true;
      // this.title = "添加【请填写功能名称】";
      //跳转
      this.$router.push({ path: "/xmgl/addProject" });
    },
    /**认领项目 */
    claimproject(row) {
      this.form = row;
      this.claimprojectdeilog = true;
    },
    async submitclaim() {
      claimproupdateuser(this.form).then((response) => {
        this.$modal.msgSuccess("认领成功！");
        this.claimprojectdeilog = false;
        this.$router.push({
          path: "/xmgl/updateProject",
          query: { projectId: this.form.id, isrl: 0 },
        });
      });
    },
    cancelclaim() {
      this.claimprojectdeilog = false;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$router.push({
        path: "/xmgl/updateProject",
        query: { projectId: row.id, isrl: 1 },
      });
    },
    /**跳转详情 */
    checkDetails(row) {
      this.$router.push({
        path: "/xmgl/projectDetails",
        query: { projectId: row.id },
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateProject(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProject(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    //抄送全量已读
    updateCopyReadFlag() {
      updateReadFlag(null).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.open = false;
        this.getList();
      });
    },
    batchUpdateCopyReadFlag() {
      batchUpdateCopyReadFlag(this.ids).then((response) => {
        this.$modal.msgSuccess("全部置为已读");
        this.open = false;
        this.getList();
      });
    },
    updateReadFlagFormData() {
      updateReadFlagFormData({ businessStatus: "3" }).then((res) => {
        this.$modal.msgSuccess("全部置为已读");
        this.open = false;
        this.getList();
      });
    },
    batchUpdateReadFlagFormData() {
      batchUpdateReadFlagFormData(this.businessIds).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.open = false;
        this.getList();
      });
    },
    async knownInformed(value) {
      this.$alert(
        "<div>当前流程审核不通过</div><div>点击[已知悉]，流程将移至已结束列表</div><div>审核不通过的流程不允许复制后重新发起，必须通过触发系统功能自动发起</div>",
        "审核不通过",
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "已知悉",
          callback: async (action) => {
            if (action == "confirm") {
              await knownFlowByBusinessId(value.businessId);
              this.getList();
              this.$modal.msgSuccess("流程已移至已结束");
            }
          },
        }
      );
    },
    /** 删除按钮操作 */
    handleDelete(row, i) {
      let msg = "";
      if (i == "4") {
        msg = "已驳回";
      } else if (i == "5") {
        msg = "草稿";
      } else if (i == "3") {
        msg = "已废弃";
      }
      const ids = row.oid || this.ids;
      this.$modal
        .confirm("是否确认删此" + msg + "流程？")
        .then(async function () {
          await deleteRejected(row.businessId);
          return delForm({ oid: ids, type: i });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      let nowDate = new Date();

      let date = {
        // 获取当前年份
        year: nowDate.getFullYear(),
        //获取当前月份
        month:
          nowDate.getMonth() + 1 < 10
            ? "0" + (nowDate.getMonth() + 1)
            : nowDate.getMonth() + 1,
        //获取当前日期
        date: nowDate.getDate(),
      };
      //拼接
      var datetime = date.year + "-" + date.month + "-" + date.date;

      this.download(
        "xmgl/project/export",
        {
          ...this.queryParams,
        },
        `项目管理_${datetime}.xlsx`
      );
    },
  },
};
</script>
<style lang="less" scoped>
.el-divider--horizontal {
  margin-top: 0 !important;
}
.status-point {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
.send_btn {
  position: absolute;
  right: 20px;
}
/deep/ .el-badge__content.is-fixed {
  top: 7px !important;
  right: 2px !important;
}
/deep/ .el-tabs__item {
  padding: 0 35px !important;
}
</style>
