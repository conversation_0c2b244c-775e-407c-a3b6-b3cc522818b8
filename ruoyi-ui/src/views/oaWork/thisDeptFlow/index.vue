<template>
  <div class="app-container">
    <div style="width: 100%; height: 80px">
      <span style="font-size: 20px; font-weight: bold">{{ userDept }}</span>
      <br />
      <span style="font-size: 14px; font-weight: bold; color: #afadad"
        >说明：</span
      >
      <span style="font-size: 14px; color: #afadad"
        >部门负责人可在此查看本部门所有成员发起的流程</span
      >
    </div>
    <el-form
      :model="queryParams"
      @submit.native.prevent
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label>
        <el-input
          v-model="queryParams.theme"
          placeholder="请输入内容"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div style="width: 100%; height: 10px"></div>
    <el-divider></el-divider>
    <el-tabs v-model="activeName1" @tab-click="handleClickLi1">
      <el-tab-pane label="审批中" name="first">
        <el-row>
          <div style="width: 100%; height: 30px; line-height: 30px">
            <el-button
              style="float: right"
              @click="getList"
              icon="el-icon-refresh"
              >刷新</el-button
            >
          </div>
        </el-row>
        <el-table
          :data="tableData11"
          style="width: 100%"
          @row-click="goProcessForm"
        >
          <el-table-column prop="theme" label="主题" width="550">
            <template slot-scope="scope">
              <el-button
                type="text"
                style="text-align: left; white-space: initial"
                @click="goProcessForm(scope.row)"
                >{{ scope.row.theme }}</el-button
              >
              <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
              <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                >紧急</span
              >
              <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                >较急</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="申请人">
            <template slot-scope="scope">
              <el-popover placement="right" width="200" trigger="hover">
                <div>
                  <span style="font-size: 20px; font-weight: bold">{{
                    nickName
                  }}</span
                  ><span
                    style="font-size: 20px; font-weight: bold; color: #cccccc"
                    >{{ scope.row.userStatus }}</span
                  >
                  <br />
                  <span style="font-size: 14px; color: #afadad">公司</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    companyName
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">部门</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    bumen
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">岗位</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    gangwei
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">手机</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    phone
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">邮箱</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    email
                  }}</span>
                  <br />
                </div>
                <el-button
                  slot="reference"
                  type="text"
                  @click.stop=""
                  @mouseenter.native="getuser(scope.row.createBy)"
                  >{{ scope.row.nickName
                  }}<span style="color: #cccccc">{{
                    scope.row.userStatus
                  }}</span></el-button
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间">
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{m}:{s}")
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" align="left" label="状态" width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
              <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
              <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
              <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
              <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
              <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="shortName"
            label="公司"
            width="110"
          ></el-table-column>
          <el-table-column prop="opcName" label="分类"></el-table-column>
          <el-table-column prop="templateName" label="模板"></el-table-column>
          <el-table-column prop="nodeName" label="当前节点"></el-table-column>
          <el-table-column prop="processor" label="当前处理人">
            <template slot-scope="scope">
              <span style="color: #cccccc">{{ scope.row.userStatus1 }}</span>
              <span v-if="scope.row.processor != '没有找到处理人'">{{
                scope.row.processor
              }}</span>
              <span
                v-if="scope.row.processor == '没有找到处理人'"
                style="color: red"
                >{{ scope.row.processor }}</span
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="已驳回" name="second">
        <el-row>
          <div style="width: 100%; height: 30px; line-height: 30px">
            <el-button
              style="float: right"
              @click="getList"
              icon="el-icon-refresh"
              >刷新</el-button
            >
          </div>
        </el-row>
        <el-table
          :data="tableData12"
          style="width: 100%"
          @row-click="goProcessForm"
        >
          <el-table-column
      
            prop="theme"
            label="主题"
            width="550"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                style="text-align: left; white-space: initial"
                @click="goProcessForm(scope.row)"
                >{{ scope.row.theme }}</el-button
              >
              <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
              <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                >紧急</span
              >
              <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                >较急</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="申请人">
            <template slot-scope="scope">
              <el-popover placement="right" width="200" trigger="hover">
                <div>
                  <span style="font-size: 20px; font-weight: bold">{{
                    nickName
                  }}</span
                  ><span
                    style="font-size: 20px; font-weight: bold; color: #cccccc"
                    >{{ scope.row.userStatus }}</span
                  >
                  <br />
                  <span style="font-size: 14px; color: #afadad">公司</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    companyName
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">部门</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    bumen
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">岗位</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    gangwei
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">手机</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    phone
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">邮箱</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    email
                  }}</span>
                  <br />
                </div>
                <el-button
                  slot="reference"
                  type="text"
                  @click.stop=""
                  @mouseenter.native="getuser(scope.row.createBy)"
                  >{{ scope.row.nickName
                  }}<span style="color: #cccccc">{{
                    scope.row.userStatus
                  }}</span></el-button
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间">
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{m}:{s}")
              }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
              <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
              <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
              <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
              <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
              <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="shortName"
            label="公司"
            width="110"
          ></el-table-column>
          <el-table-column prop="opcName" label="分类"></el-table-column>
          <el-table-column prop="templateName" label="模板"></el-table-column>
          <el-table-column prop="nodeName" label="当前节点"></el-table-column>
          <el-table-column prop="processor" label="当前处理人">
            <template slot-scope="scope">
              <span style="color: #cccccc">{{ scope.row.userStatus1 }}</span>
              <span v-if="scope.row.processor != '没有找到处理人'">{{
                scope.row.processor
              }}</span>
              <span
                v-if="scope.row.processor == '没有找到处理人'"
                style="color: red"
                >{{ scope.row.processor }}</span
              >
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click.stop="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </el-tab-pane>
      <!-- <el-tab-pane label="草稿" name="third">
        <el-row>
          <div style="width:100%;height:30px;line-height:30px">
            <el-button style="float:right" @click="getList" icon="el-icon-refresh">刷新</el-button>
          </div>
        </el-row>
        <el-table :data="tableData13" style="width: 100%" @row-click="goProcessForm">
          <el-table-column prop="formName" label="主题">
            <template slot-scope="scope">
              <el-button type="text" @click="goProcessForm(scope.row)">{{scope.row.theme}}</el-button>
              <span v-show="scope.row.urgency==3" style="color:#ff0000">紧急</span>
              <span v-show="scope.row.urgency==2" style="color:#ff9900">较急</span>
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="申请人">
            <template slot-scope="scope">
              <el-popover placement="right" width="200" trigger="hover">
                <div>
                  <span style="font-size:20px;font-weight:bold;">{{nickName}}</span>
                  <br />
                  <span style="font-size:14px;color: 	#afadad;">公司</span>
                  <span style="font-size:14px;margin-left:20px">{{companyName}}</span>
                  <br />
                  <span style="font-size:14px;color: 	#afadad;">部门</span>
                  <span style="font-size:14px;margin-left:20px">{{bumen}}</span>
                  <br />
                  <span style="font-size:14px;color: 	#afadad;">岗位</span>
                  <span style="font-size:14px;margin-left:20px">{{gangwei}}</span>
                  <br />
                  <span style="font-size:14px;color: 	#afadad;">手机</span>
                  <span style="font-size:14px;margin-left:20px">{{phone}}</span>
                  <br />
                  <span style="font-size:14px;color: 	#afadad;">邮箱</span>
                  <span style="font-size:14px;margin-left:20px">{{email}}</span>
                  <br />
                </div>
                <el-button
                  slot="reference"
                  type="text"
                  @click.stop=""
                      @mouseenter.native="getuser(scope.row.createBy)"
                >{{scope.row.nickName}}<span style="color: #cccccc">{{scope.row.userStatus}}</span></el-button>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.status == '0'" style="margin-left: 10px">{{ "审批中" }}</span>
              <span v-if="scope.row.status == '1'" style="margin-left: 10px">{{ "结束" }}</span>
              <span v-if="scope.row.status == '2'" style="margin-left: 10px">{{ "审批不通过" }}</span>
              <span v-if="scope.row.status == '3'" style="margin-left: 10px">{{ "终止" }}</span>
              <span v-if="scope.row.status == '4'" style="margin-left: 10px">{{ "驳回" }}</span>
              <span v-if="scope.row.status == '5'" style="margin-left: 10px">{{ "草稿" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="shortName" label="公司" width="110"></el-table-column>
          <el-table-column prop="opcName" label="分类"></el-table-column>
          <el-table-column prop="templateName" label="模板"></el-table-column>
          <el-table-column prop="nodeName" label="当前节点"></el-table-column>
          <el-table-column prop="processor" label="当前处理人">
            <template slot-scope="scope">
              {{scope.row.processor}}<span style="color: #cccccc">{{scope.row.userStatus1}}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          </el-table-column>
        </el-table>
      </el-tab-pane> -->
      <el-tab-pane label="已结束" name="fourth">
        <el-row>
          <div style="width: 100%; height: 30px; line-height: 30px">
            <el-button
              style="float: right"
              @click="getList"
              icon="el-icon-refresh"
              >刷新</el-button
            >
          </div>
        </el-row>
        <el-table
          :data="tableData14"
          style="width: 100%"
          @row-click="goProcessForm"
        >
          <el-table-column
         
            prop="theme"
            label="主题"
            width="520"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                style="text-align: left; white-space: initial"
                @click="goProcessForm(scope.row)"
                >{{ scope.row.theme }}</el-button
              >
              <!-- <el-button type="text">{{scope.row.theme}}</el-button> -->
              <span v-show="scope.row.urgency == 3" style="color: #ff0000"
                >紧急</span
              >
              <span v-show="scope.row.urgency == 2" style="color: #ff9900"
                >较急</span
              >
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="申请人">
            <template slot-scope="scope">
              <el-popover placement="right" width="200" trigger="hover">
                <div>
                  <span style="font-size: 20px; font-weight: bold">{{
                    nickName
                  }}</span
                  ><span
                    style="font-size: 20px; font-weight: bold; color: #cccccc"
                    >{{ scope.row.userStatus }}</span
                  >
                  <br />
                  <span style="font-size: 14px; color: #afadad">公司</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    companyName
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">部门</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    bumen
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">岗位</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    gangwei
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">手机</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    phone
                  }}</span>
                  <br />
                  <span style="font-size: 14px; color: #afadad">邮箱</span>
                  <span style="font-size: 14px; margin-left: 20px">{{
                    email
                  }}</span>
                  <br />
                </div>
                <el-button
                  slot="reference"
                  type="text"
                  @click.stop=""
                  @mouseenter.native="getuser(scope.row.createBy)"
                  >{{ scope.row.nickName
                  }}<span style="color: #cccccc">{{
                    scope.row.userStatus
                  }}</span></el-button
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间">
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{m}:{s}")
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.status == '0'">{{ "审批中" }}</span>
              <span v-if="scope.row.status == '1'">{{ "结束" }}</span>
              <span v-if="scope.row.status == '2'">{{ "审批不通过" }}</span>
              <span v-if="scope.row.status == '3'">{{ "废弃" }}</span>
              <span v-if="scope.row.status == '4'">{{ "驳回" }}</span>
              <span v-if="scope.row.status == '5'">{{ "草稿" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="shortName"
            label="公司"
            width="110"
          ></el-table-column>
          <el-table-column prop="opcName" label="分类"></el-table-column>
          <el-table-column prop="templateName" label="模板"></el-table-column>
          <el-table-column prop="updateTime" label="结束时间">
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{m}:{s}")
              }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <!-- <el-tab-pane label="已终止" name="fieth">
        <el-row>
          <div style="width:100%;height:30px;line-height:30px">
            <el-button style="float:right" @click="getList" icon="el-icon-refresh">刷新</el-button>
          </div>
        </el-row>
        <el-table :data="tableData15" style="width: 100%" @row-click="goProcessForm">
          <el-table-column prop="formName" label="主题">
            <template slot-scope="scope">
              <el-button type="text" @click="goProcessForm(scope.row)">{{scope.row.theme}}</el-button>
              <span v-show="scope.row.urgency==3" style="color:#ff0000">紧急</span>
              <span v-show="scope.row.urgency==2" style="color:#ff9900">较急</span>
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="申请人">
            <template slot-scope="scope">
              <el-popover placement="right" width="200" trigger="hover">
                <div>
                  <span style="font-size:20px;font-weight:bold;">{{nickName}}</span>
                  <br />
                  <span style="font-size:14px;color: 	#afadad;">公司</span>
                  <span style="font-size:14px;margin-left:20px">{{companyName}}</span>
                  <br />
                  <span style="font-size:14px;color: 	#afadad;">部门</span>
                  <span style="font-size:14px;margin-left:20px">{{bumen}}</span>
                  <br />
                  <span style="font-size:14px;color: 	#afadad;">岗位</span>
                  <span style="font-size:14px;margin-left:20px">{{gangwei}}</span>
                  <br />
                  <span style="font-size:14px;color: 	#afadad;">手机</span>
                  <span style="font-size:14px;margin-left:20px">{{phone}}</span>
                  <br />
                  <span style="font-size:14px;color: 	#afadad;">邮箱</span>
                  <span style="font-size:14px;margin-left:20px">{{email}}</span>
                  <br />
                </div>
                <el-button
                  slot="reference"
                  type="text"
                  @click.stop=""
                      @mouseenter.native="getuser(scope.row.createBy)"
                >{{scope.row.nickName}}<span style="color: #cccccc">{{scope.row.userStatus}}</span></el-button>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <span v-if="scope.row.status == '0'" style="margin-left: 10px">{{ "审批中" }}</span>
              <span v-if="scope.row.status == '1'" style="margin-left: 10px">{{ "结束" }}</span>
              <span v-if="scope.row.status == '2'" style="margin-left: 10px">{{ "审批不通过" }}</span>
              <span v-if="scope.row.status == '3'" style="margin-left: 10px">{{ "终止" }}</span>
              <span v-if="scope.row.status == '4'" style="margin-left: 10px">{{ "驳回" }}</span>
              <span v-if="scope.row.status == '5'" style="margin-left: 10px">{{ "草稿" }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="shortName" label="公司" width="110"></el-table-column>
          <el-table-column prop="opcName" label="分类"></el-table-column>
          <el-table-column prop="templateName" label="模板"></el-table-column>
          <el-table-column prop="updateTime" label="终止时间">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          </el-table-column>
        </el-table>
      </el-tab-pane> -->
    </el-tabs>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listTask, formDataShow, formDataSave } from "@/api/flow/task";
import { getUserProfile } from "@/api/system/user";
import {
  started,
  history,
  getByCheck,
  delForm,
  getUserData,
  getTabsSums,
  getThisDeptSums,
} from "@/api/flow/flow";
export default {
  name: "ThisDeptFlow",
  data() {
    return {
      userDept: "",

      nickName: "",
      companyName: "",
      bumen: "",
      gangwei: "",
      phone: "",
      email: "",

      tableData11: [],
      tableData12: [],
      tableData13: [],
      tableData14: [],
      tableData15: [],
      activeName: "first",
      activeName1: "first",
      activeName2: "first",
      activeName3: "first",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        input: "",
        activeNameFirst: "",
        activeNameSecend: "",
        theme: "",
      },
      // 表单参数
      form: {},
    };
  },
  created() {
    this.getList();
    this.getUserDa();
  },
  methods: {
    //查看
    goProcessForm(item) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: { oid: item.oid, businessId: item.businessId },
      });
    },
    getuser(userId) {
      getUserData(userId).then((response) => {
        this.nickName = response.data.nickName;
        if (response.data.unit && response.data.unit.unitName) {
          this.companyName = response.data.unit.unitName;
        }

        this.bumen = response.data.dept.deptName;
        this.gangwei = response.mainPost;
        this.phone = response.data.phonenumber;
        this.email = response.data.email;
      });

      console.log(userId);
    },

    getUserDa() {
      getUserProfile().then((response) => {
        this.userDept = response.data.dept.deptName;
      });
    },
    handleClickLi1(tab, event) {
      console.log();
      this.queryParams.activeNameFirst = this.activeName;
      this.queryParams.activeNameSecend = this.activeName1;

      this.getList();
    },

    /** 查询【请填写功能名称】列表 */
    async getList() {
      this.loading = true;

      if (this.activeName == "first" && this.activeName1 == "first") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName1;

        getThisDeptSums(this.queryParams).then((response) => {
          this.tableData11 = response.rows;

          this.total = response.total;
        });
      } else if (this.activeName == "first" && this.activeName1 == "second") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName1;

        getThisDeptSums(this.queryParams).then((response) => {
          this.tableData12 = response.rows;

          this.total = response.total;
        });
      } else if (this.activeName == "first" && this.activeName1 == "third") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName1;
        getThisDeptSums(this.queryParams).then((response) => {
          this.tableData13 = response.rows;

          this.total = response.total;
        });
      } else if (this.activeName == "first" && this.activeName1 == "fourth") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName1;
        getThisDeptSums(this.queryParams).then((response) => {
          this.tableData14 = response.rows;

          this.total = response.total;
        });
      } else if (this.activeName == "first" && this.activeName1 == "fieth") {
        this.queryParams.activeNameFirst = this.activeName;
        this.queryParams.activeNameSecend = this.activeName1;
        getThisDeptSums(this.queryParams).then((response) => {
          this.tableData15 = response.rows;

          this.total = response.total;
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        businessType: null,
        fundShortName: null,
        fundFullName: null,
        productShortName: null,
        productFullName: null,
        custShortName: null,
        custFullName: null,
        userId: null,
        projectDate: null,
        projectStatus: "0",
        scheduleStatus: "0",
        lockStatus: "0",
        overTime: null,
        status: "0",
        createTime: null,
        createBr: null,
        updateTime: null,
        updateBr: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.theme = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      // this.open = true;
      // this.title = "添加【请填写功能名称】";
      //跳转
      this.$router.push({ path: "/xmgl/addProject" });
    },
    /**认领项目 */
    claimproject(row) {
      this.form = row;
      this.claimprojectdeilog = true;
    },
    async submitclaim() {
      claimproupdateuser(this.form).then((response) => {
        this.$modal.msgSuccess("认领成功！");
        this.claimprojectdeilog = false;
        this.$router.push({
          path: "/xmgl/updateProject",
          query: { projectId: this.form.id, isrl: 0 },
        });
      });
    },
    cancelclaim() {
      this.claimprojectdeilog = false;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$router.push({
        path: "/xmgl/updateProject",
        query: { projectId: row.id, isrl: 1 },
      });
    },
    /**跳转详情 */
    checkDetails(row) {
      this.$router.push({
        path: "/xmgl/projectDetails",
        query: { projectId: row.id },
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateProject(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProject(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.oid || this.ids;
      this.$modal
        .confirm('是否确认删除编号为"' + ids + '"的数据项？')
        .then(function () {
          return delForm(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      let nowDate = new Date();

      let date = {
        // 获取当前年份
        year: nowDate.getFullYear(),
        //获取当前月份
        month:
          nowDate.getMonth() + 1 < 10
            ? "0" + (nowDate.getMonth() + 1)
            : nowDate.getMonth() + 1,
        //获取当前日期
        date: nowDate.getDate(),
      };
      //拼接
      var datetime = date.year + "-" + date.month + "-" + date.date;

      this.download(
        "xmgl/project/export",
        {
          ...this.queryParams,
        },
        `项目管理_${datetime}.xlsx`
      );
    },
  },
};
</script>
<style>
.status-point {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
</style>
