<template>
  <div class="app-container">
    <div style="width: 100%; margin-bottom: 20px">
      <span style="color: #666666; font-weight: bold; font-size: 20px">请选择流程模板</span>
    </div>

    <div style="position: relative">
      <el-tabs v-model="editableTabsValue" type="card" style="width: 100%">
        <el-tab-pane
          :key="item.name"
          v-for="item in editableTabs"
          :label="item.title"
          :name="item.name"
        >
          <el-button icon="el-icon-s-tools" size="small" style="float: right" @click="set">配置</el-button>

          <div v-loading="loading" style="margin-top: 25px">
            <div style="height: 40px">
              <span style="position: relative">我的常用流程</span>
            </div>
            <div>
              <el-row>
                <!--无数据时，显示添加数据-->
                <el-col
                  :span="5.5"
                  v-if="
                    templateObj1.length == 0 &&
                    templateObj2.length == 0 &&
                    templateObj3.length == 0 &&
                    templateObj4.length == 0
                  "
                >
                  <el-table
                    class="customer-table"
                    v-loading="loading"
                    :data="templateObjNoData"
                    :show-header="false"
                    width="1000px"
                  >
                    <el-table-column label align="center" prop="templateObj1" width="250px">
                      <template slot-scope="scope">
                        <div>
                          <div
                            style="
                              background: #e4e4e4;
                              font-size: 16px;
                              border: 1px solid #999999;
                            "
                          >
                            <i class="el-icon-document"></i>
                            {{ scope.row.templateObj.classificationName }}
                            <i
                              v-if="
                                scope.row.templateObj.classificationName !=
                                '添加'
                              "
                              class="el-icon-delete"
                              @click="delItem(scope.row)"
                            ></i>
                          </div>
                          <div style="border: 1px solid #999999">
                            <el-button
                              type="text"
                              style="
                                font-size: 16px;
                                font-weight: 600;
                                color: #666666;
                              "
                              @click="openDispositionDeilog = true"
                            >
                              {{
                              scope.row.templateObj.templateName
                              }}
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>

                <!--有数据时，正常显示-->
                <el-col :span="5.5" v-if="templateObj1.length != 0">
                  <el-table
                    class="customer-table"
                    v-loading="loading"
                    :data="templateObj1"
                    :show-header="false"
                    width="1000px"
                  >
                    <el-table-column label prop="templateObj1" width="250px">
                      <template slot-scope="scope">
                        <div>
                          <div
                            style="
                              background: #e4e4e4;
                              font-size: 16px;
                              border: 1px solid #999999;
                            "
                          >
                            <i class="el-icon-document"></i>
                            {{ scope.row.templateObj.classificationName }}
                            <i
                              v-if="
                                scope.row.templateObj.classificationName !=
                                '添加'
                              "
                              class="el-icon-delete"
                              @click="delItem(scope.row)"
                            ></i>
                          </div>
                          <div style="border: 1px solid #999999">
                            <el-button
                              type="text"
                              style="
                                font-size: 16px;
                                font-weight: 600;
                                color: #666666;
                              "
                              @click="goUpdateProcessForm(scope.row)"
                            >
                              {{
                              scope.row.templateObj.templateName
                              }}
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>

                <el-col :span="5.5" v-if="templateObj2.length != 0">
                  <el-table
                    class="customer-table"
                    v-loading="loading"
                    :data="templateObj2"
                    :show-header="false"
                    width="1000px"
                  >
                    <el-table-column label prop="templateObj1" width="250px">
                      <template slot-scope="scope">
                        <div>
                          <div
                            style="
                              background: #e4e4e4;
                              font-size: 16px;
                              border: 1px solid #999999;
                            "
                          >
                            <i class="el-icon-document"></i>
                            {{ scope.row.templateObj.classificationName }}
                            <i
                              v-if="
                                scope.row.templateObj.classificationName !=
                                '添加'
                              "
                              class="el-icon-delete"
                              @click="delItem(scope.row)"
                            ></i>
                          </div>
                          <div style="border: 1px solid #999999">
                            <el-button
                              type="text"
                              style="
                                font-size: 16px;
                                font-weight: 600;
                                color: #666666;
                              "
                              @click="goUpdateProcessForm(scope.row)"
                            >
                              {{
                              scope.row.templateObj.templateName
                              }}
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>

                <el-col :span="5.5" v-if="templateObj3.length != 0">
                  <el-table
                    class="customer-table"
                    v-loading="loading"
                    :data="templateObj3"
                    :show-header="false"
                    width="1000px"
                  >
                    <el-table-column label prop="templateObj1" width="250px">
                      <template slot-scope="scope">
                        <div>
                          <div
                            style="
                              background: #e4e4e4;
                              font-size: 16px;
                              border: 1px solid #999999;
                            "
                          >
                            <i class="el-icon-document"></i>
                            {{ scope.row.templateObj.classificationName }}
                            <i
                              v-if="
                                scope.row.templateObj.classificationName !=
                                '添加'
                              "
                              class="el-icon-delete"
                              @click="delItem(scope.row)"
                            ></i>
                          </div>
                          <div style="border: 1px solid #999999">
                            <el-button
                              type="text"
                              style="
                                font-size: 16px;
                                font-weight: 600;
                                color: #666666;
                              "
                              @click="goUpdateProcessForm(scope.row)"
                            >
                              {{
                              scope.row.templateObj.templateName
                              }}
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>

                <el-col :span="5.5" v-if="templateObj4.length != 0">
                  <el-table
                    class="customer-table"
                    v-loading="loading"
                    :data="templateObj4"
                    :show-header="false"
                    width="1000px"
                  >
                    <el-table-column label prop="templateObj1" width="250px">
                      <template slot-scope="scope">
                        <div>
                          <div
                            style="
                              background: #e4e4e4;
                              font-size: 16px;
                              border: 1px solid #999999;
                            "
                          >
                            <i class="el-icon-document"></i>
                            {{ scope.row.templateObj.classificationName }}
                            <i
                              v-if="
                                scope.row.templateObj.classificationName !=
                                '添加'
                              "
                              class="el-icon-delete"
                              @click="delItem(scope.row)"
                            ></i>
                          </div>
                          <div style="border: 1px solid #999999">
                            <el-button
                              type="text"
                              style="
                                font-size: 16px;
                                font-weight: 600;
                                color: #666666;
                              "
                              @click="goUpdateProcessForm(scope.row)"
                            >
                              {{
                              scope.row.templateObj.templateName
                              }}
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>

              <!--              <el-table class="customer-table" v-loading="loading" :data="dataList" :show-header="false" width="1000px">-->
              <!--                <el-table-column label="" prop="templateObj1" width="250px">-->
              <!--                  <template slot-scope="scope">-->
              <!--                    <div>-->
              <!--                      <div style="background: #e4e4e4;font-size: 16px;border: 1px solid #999999"><i class="el-icon-document"></i>{{scope.row.templateObj1.classificationName}}</div>-->
              <!--                      <div style="font-size: 16px;border: 1px solid #999999">{{scope.row.templateObj1.templateName}}</div>-->
              <!--                    </div>-->
              <!--                  </template>-->
              <!--                </el-table-column>-->
              <!--                <el-table-column label="" prop="templateObj2" width="250px">-->
              <!--                  <template slot-scope="scope">-->
              <!--                    <div>-->
              <!--                      <div style="background: #e4e4e4;font-size: 16px;border: 1px solid #999999"><i class="el-icon-document"></i>{{scope.row.templateObj2.classificationName}}</div>-->
              <!--                      <div style="font-size: 16px;border: 1px solid #999999">{{scope.row.templateObj2.templateName}}</div>-->
              <!--                    </div>-->
              <!--                  </template>-->
              <!--                </el-table-column>-->
              <!--                <el-table-column label="" prop="templateObj3" width="250px">-->
              <!--                  <template slot-scope="scope">-->
              <!--                    <div>-->
              <!--                      <div style="background: #e4e4e4;font-size: 16px;border: 1px solid #999999"><i class="el-icon-document"></i>{{scope.row.templateObj3.classificationName}}</div>-->
              <!--                      <div style="font-size: 16px;border: 1px solid #999999">{{scope.row.templateObj3.templateName}}</div>-->
              <!--                    </div>-->
              <!--                  </template>-->
              <!--                </el-table-column>-->
              <!--                <el-table-column label="" prop="templateObj4" width="250px">-->
              <!--                  <template slot-scope="scope">-->
              <!--                    <div>-->
              <!--                      <div style="background: #e4e4e4;font-size: 16px;border: 1px solid #999999"><i class="el-icon-document"></i>{{scope.row.templateObj4.classificationName}}</div>-->
              <!--                      <div style="font-size: 16px;border: 1px solid #999999">{{scope.row.templateObj4.templateName}}</div>-->
              <!--                    </div>-->
              <!--                  </template>-->
              <!--                </el-table-column>-->
              <!--              </el-table>-->
            </div>

            <el-divider></el-divider>
            <div>
              <span>全部流程</span>
              <el-form @submit.native.prevent style="margin: 8px 0;">
                <el-form-item>
                  <el-input v-model="processKey" placeholder="输入流程关键字" style="width: 200px"></el-input>
                  <el-button
                    icon="el-icon-search"
                    type="primary"
                    style="margin-left: 10px"
                    size="small"
                    @click="getByProcessKey()"
                  >搜索</el-button>
                </el-form-item>
              </el-form>
              <div>
                <div style="width: 40%;height: 410px;float: left;overflow: auto;">
                  <!--      todo 加入xxx表-树形结构-->
                  <div style="height: 100%; float: left">
                    <el-tree
                      :data="deptOptions"
                      :props="defaultProps"
                      :load="loadNode"
                      lazy
                      :filter-node-method="filterNode"
                      ref="tree"
                      :default-expand-all="false"
                      @node-click="handleNodeClick"
                    >
                      <span class="custom-tree-node" slot-scope="{ node }">
                        <span>
                          <!-- <i class="el-icon-house"></i> -->
                          <i class="el-icon-folder-opened"></i>
                        </span>
                        <span>{{ node.label }}</span>
                      </span>
                    </el-tree>
                  </div>
                </div>
                <div style="width: 60%; float: left">
                  <el-table
                    v-loading="loading"
                    :data="roleTemplList"
                    @selection-change="handleSelectionChange"
                  >
                    <el-table-column label="模板名称" align="center" prop="templateName">
                      <template slot-scope="scope">
                        <a @click="goTemplateDetail(scope.row)">
                          <span style="color: #1e90ff">
                            {{
                            scope.row.templateName
                            }}
                          </span>
                        </a>
                      </template>
                    </el-table-column>
                    <el-table-column label="备注说明" align="center" prop="remark" />
                    <!--                    <el-table-column-->
                    <!--                      label="启用状态"-->
                    <!--                      align="center"-->
                    <!--                      prop="isEnable"-->
                    <!--                    >-->
                    <!--                      <template slot-scope="scope">-->
                    <!--                        <el-switch-->
                    <!--                          v-model="scope.row.isEnable"-->
                    <!--                          active-color="#13ce66"-->
                    <!--                          inactive-color="#ff4949"-->
                    <!--                          active-value="Y"-->
                    <!--                          inactive-value="N"-->
                    <!--                          @change="updateinEnable(scope.row)"-->
                    <!--                        ></el-switch>-->
                    <!--                      </template>-->
                    <!--                    </el-table-column>-->
                    <!--                    <el-table-column-->
                    <!--                      label="最后修改时间"-->
                    <!--                      align="center"-->
                    <!--                      prop="endUpdateTime"-->
                    <!--                      width="180"-->
                    <!--                    >-->
                    <!--                      <template slot-scope="scope">-->
                    <!--                        <span>{{-->
                    <!--                          parseTime(scope.row.endUpdateTime, "{y}-{m}-{d}")-->
                    <!--                        }}</span>-->
                    <!--                      </template>-->
                    <!--                    </el-table-column>-->
                  </el-table>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!--tabs页最右侧输入框和按钮-->
      <el-form @submit.native.prevent>
        <el-form-item style="position: absolute; right: 6%; top: 79px">
          <el-autocomplete
            class="inline-input"
            v-model="companyKey"
            :fetch-suggestions="queryCompanySearch"
            placeholder="输入公司名称"
            :trigger-on-focus="false"
            @select="handleCompanySelect"
          >
            <template #suffix>
              <el-button class="limianButton" icon="el-icon-search" circle size="mini"></el-button>
            </template>
          </el-autocomplete>

          <!--          <el-input v-model="processKey" placeholder="输入公司名称" style="width: 200px">-->
          <!--            <template #suffix>-->
          <!--              <el-button class="limianButton" icon="el-icon-search" circle size="mini"></el-button>-->
          <!--            </template>-->
          <!--          </el-input>-->
        </el-form-item>
      </el-form>
    </div>

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <!--      <el-table :data="oaProcessTemplateUpdateInfoList" border>-->
      <!--        <el-table-column label="修改日期"   prop="updateTime"   />-->

      <!--        <el-table-column label="用户"   prop="nickName"   />-->

      <!--        <el-table-column label="说明"   prop="information"   />-->
      <!--      </el-table>-->
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <Disposition
      :companyId="companyId"
      v-if="openDispositionDeilog"
      @close="openDispositionDeilog = false"
      @success="successDis"
    />
  </div>
</template>

<script>
import {
  listMyUsualProcess,
  delTemplate,
  addTemplate,
  updateTemplate,
  changeenableStatus,
  getOaProcessTemplateUpdateInfoList,
  companyList,
  roleClassSelectList,
  getDataByRoleAndClassId,
  tabsCompanyList,
  addMyUseulTempl,
  getCompanyId,
  getTemplate,
  deleteMyUsualProcessById
} from "@/api/oa/processTemplate";
import Disposition from "./Disposition";
import { getFlowInfoByFlowFullId } from "@/api/flow/flow";
export default {
  components: {
    Disposition
  },
  name: "InitiatingProcess",
  data() {
    return {
      //根据分类id获取到的所属公司名
      companyIdByClass: "",
      //入库我的常用流程
      addMyUsualList: [],
      openDispositionDeilog: false,
      roleTemplList: [],
      deilogRoleTemplList: [],
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 部门树选项
      deptOptions: undefined,
      //公司搜索用数组
      restaurants: [],
      //向接口发送的公司id
      companyId: null,
      //无数据时列表
      templateObjNoData: [],
      //列表4
      templateObj4: [],
      //列表3
      templateObj3: [],
      //列表2
      templateObj2: [],
      //列表1
      templateObj1: [],
      editableTabsValue: null,
      editableTabs: [],
      // editableTabs: [{
      //   title: '中保国信1',
      //   name: '1',
      //   content: 'Tab 1 content',
      //   id: 1,
      // }, {
      //   title: '中保国信2',
      //   name: '2',
      //   content: 'Tab 2 content',
      //   id: 2,
      // }, {
      //   title: '中保国信3',
      //   name: '3',
      //   content: 'Tab 3 content',
      //   id: 3,
      // }, {
      //   title: '中保国信4',
      //   name: '4',
      //   content: 'Tab 4 content',
      //   id: 4,
      // }
      // ],
      // tabIndex: 233,

      //公司关键字
      companyKey: null,
      //流程关键字
      processKey: null,

      //修改记录
      oaProcessTemplateUpdateInfoList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      dataList: [],
      // 弹出层标题
      title: "配置常用模板",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        dictName: null,
        dictType: null,
        isEnable: null,
        endUpdateTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    //获取公司列表
    this.getCompanyList();
    // this.getList();
  },
  watch: {
    editableTabsValue(newValue, oldValue) {
      console.log("1" + newValue);

      if (this.editableTabs.length != 0) {
        var filter = this.editableTabs.filter(
          t => t.name == this.editableTabsValue
        );
        if (filter.length != 0) {
          this.companyId = filter[0].id;
          this.loading = true;
          this.roleTemplList = [];
          this.getList();
          this.getTreeselect(this.companyId);
          this.loading = false;
        }
      }
      // if (newValue == null || newValue == '') {
      //
      // }
    }
  },
  methods: {
    delItem(v) {
      console.log(v);
      this.$confirm("此操作将永久删除该配置, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          deleteMyUsualProcessById(v.id).then(res => {
            if (res.code == 200) {
              this.$message.success("删除成功");
              this.getList();
            } else {
              this.$message.warning(res.msg);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    successDis() {
      this.openDispositionDeilog = false;
      this.getCompanyList();
      // this.getList();
    },
    set() {
      this.openDispositionDeilog = true;
      console.log(this.deptOptions);
    },
    loadNode(node, resolve) {
      console.log(node);
      if (node.level === 0) {
        return resolve([{ name: "region" }]);
      }
      if (!node.data.children) return resolve([]);

      setTimeout(() => {
        const data = node.data.children || [];

        resolve(data);
      }, 500);
    },

    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 节点单击事件
    handleNodeClick(data) {
      this.processKey = "";
      getCompanyId(data.id).then(response => {
        this.companyIdByClass = response.data;
      });
      this.getByClassData(data.id);
    },
    //根据分类id获取模板列表接口
    getByClassData(id) {
      var data = {
        classificationId: id,
        templateName: this.processKey,
        companyId: this.companyId
      };
      getDataByRoleAndClassId(data).then(response => {
        this.roleTemplList = response.rows;
      });
    },

    getByProcessKey() {
      var data = {
        companyId: this.companyId,
        templateName: this.processKey
      };
      getDataByRoleAndClassId(data).then(response => {
        this.roleTemplList = response.rows;
      });
    },

    /** 查询部门下拉树结构 */
    getTreeselect(id) {
      roleClassSelectList({ companyId: id }).then(response => {
        this.$nextTick(() => {
          setTimeout(() => {
            this.deptOptions = response.data;
          }, 300);
        });
      });
    },

    //点击跳转编辑表单
    closeSelectedTag(view) {
      console.log(view);
      this.$tab.closePage(view).then(({ visitedViews }) => {});
    },
    goUpdateProcessForm(row) {
      getTemplate(row.templateId).then(response => {
        getFlowInfoByFlowFullId({
          flowFullId: response.data.oaProcessTemplate.flowFullId
        }).then(resp => {
          if (resp.msg == "该流程图没有两个以上处理节点") {
            this.$modal.msgError("该流程模板错误，请先保证模板的正确性");
          } else {
            sessionStorage.setItem(
              "templateName",
              row.templateObj.templateName
            );
            let route = {
              fullPath: "/oaWork/updateProcessForm",
              name: "UpdateProcessForm",
              path: "/oaWork/updateProcessForm"
            };
            this.closeSelectedTag(route);
            setTimeout(() => {
              this.$router.push({
                path: "/oaWork/updateProcessForm",
                query: {
                  templateId: row.templateId,
                  classificationId: row.classificationId,
                  companyId: row.companyId
                }
              });
            }, 150);
          }
        });
      });
    },
    //公司搜索，选中以后
    handleCompanySelect(item) {
      var filter = this.editableTabs.filter(t => t == item);
      this.editableTabsValue = filter[0].name;
    },
    //公司搜索用
    queryCompanySearch(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString
        ? restaurants.filter(this.createCompanyFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createCompanyFilter(queryString) {
      return restaurant => {
        return (
          (restaurant.value||restaurant.VALUE)?.toLowerCase()?.indexOf(queryString?.toLowerCase()) ===
          0
        );
      };
    },
    //获取公司列表
    getCompanyList() {
      tabsCompanyList().then(res => {
        this.editableTabs = res;
        this.restaurants = res;
        // this.getList();
        //必须要用name，否则页面报错！！！！
        this.editableTabsValue = res[0].name;
        if (res.length != 0) {
          this.companyId = res[0].id;
          this.getTreeselect(this.companyId);
        }
        this.getList();
      });
    },

    // //修改启用状态
    // updateinEnable(row) {
    //   let text = row.isEnable === "Y" ? "启用" : "停用";
    //   this.$modal
    //     .confirm("确认要" + text + '"' + row.templateName + '"模板吗？')
    //     .then(function () {
    //       return changeenableStatus(row.id, row.isEnable);
    //     })
    //     .then(() => {
    //       this.$modal.msgSuccess(text + "成功");
    //     })
    //     .catch(function () {
    //       row.isEnable = row.isEnable === "Y" ? "N" : "Y";
    //     });
    // },
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      var queryObj = {
        companyId: this.companyId
      };
      listMyUsualProcess(queryObj).then(response => {
        // this.dataList = response.rows;
        this.templateObj1 = response.rows[0].templateObj1;
        this.templateObj2 = response.rows[0].templateObj2;
        this.templateObj3 = response.rows[0].templateObj3;
        this.templateObj4 = response.rows[0].templateObj4;
        if (
          response.rows[0].templateObj1.length == 0 &&
          response.rows[0].templateObj2.length == 0 &&
          response.rows[0].templateObj3.length == 0 &&
          response.rows[0].templateObj4.length == 0
        ) {
          var templateObjNull = [
            {
              classificationId: null,
              //todo 死值，后续悔改
              companyId: this.companyId,
              id: null,
              templateId: null,
              templateObj: {
                classificationName: "添加",
                templateName: "+"
              },
              userId: null
            }
          ];
          this.templateObjNoData = templateObjNull;
        }

        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        dictName: null,
        dictType: null,
        isEnable: null,
        endUpdateTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【请填写功能名称】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$router.push({
        path: "/oa/processTemplateUpdate",
        query: { templateId: row.id }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTemplate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTemplate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      var templateName = row.templateName;
      this.$modal
        .confirm('是否确认删除"' + templateName + '"的模板？删除之后不可找回')
        .then(function() {
          return delTemplate(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    //点击详情
    goTemplateDetail(row) {
      getFlowInfoByFlowFullId({ flowFullId: row.flowFullId }).then(resp => {
        if (resp.msg == "该流程图没有两个以上处理节点") {
          this.$modal.msgError("该流程模板错误，请先保证模板的正确性");
        } else {
          sessionStorage.setItem("templateName", row.templateName);
          let route = {
            fullPath: "/oaWork/updateProcessForm",
            name: "UpdateProcessForm",
            path: "/oaWork/updateProcessForm"
          };
          this.closeSelectedTag(route);
          setTimeout(() => {
            this.$router.push({
              path: "/oaWork/updateProcessForm",
              query: {
                templateId: row.id,
                classificationId: row.classificationId,
                companyId: row.companyId
              }
            });
          }, 150);
        }
      });
    },
    //点击跳转创建模板
    handleCreate() {
      this.$router.push("/oa/processTemplateCreate");
    },
    //点击跳转全局参数设置
    handleGlobalSetting() {
      this.$router.push("/oa/processTemplateGlobalSetting");
    },
    handleShow(row) {
      var obj = {
        id: row.id
      };
      getOaProcessTemplateUpdateInfoList(obj).then(response => {
        if (response.rows.length !== 0) {
          this.oaProcessTemplateUpdateInfoList = response.rows;
          this.open = true;
        } else {
          this.$modal.msgError("该流程模板没有修改记录");
        }
        // this.
      });
    }
  }
};
</script>
<style scoped="scoped"  lang="scss">
.el-icon-delete {
  float: right;
  margin-top: 3px;
  cursor: pointer;
  margin-right: 4px;
}
/*去掉按钮的边框*/
.el-button.limianButton.el-button--default.el-button--mini.is-circle {
  border: none;
}

/* 去掉表格单元格边框*/
.customer-table th {
  border: none;
}
.customer-table td,
.customer-table th.is-leaf {
  border: none;
}
/* 表格最外边框*/
.el-table--border,
.el-table--group {
  border: none;
}
/* 头部边框*/
.customer-table thead tr th.is-leaf {
  border: 1px solid #ebeef5;
  border-right: none;
}
.customer-table thead tr th:nth-last-of-type(2) {
  border-right: 1px solid #ebeef5;
}
/* 表格最外层边框-底部边框*/
.el-table--border::after,
.el-table--group::after {
  width: 0;
}
.customer-table::before {
  width: 0;
}
.customer-table .el-table__fixed-right::before,
.el-table__fixed::before {
  width: 0;
}
/*表格有滚动时表格头边框*/
.el-table--border th.gutter:last-of-type {
  border: 1px solid #ebeef5;
  border-left: none;
}

/*el-table去掉最最下面的边框*/
.el-table th.el-table__cell.is-leaf,
.el-table td.el-table__cell {
  border-bottom: 0px solid #dfe6ec;
}
</style>
