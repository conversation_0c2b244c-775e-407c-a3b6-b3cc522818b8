<template>
  <div class="content">
    <el-form ref="form" :model="form" label-width="130px">
      <div class="flex">
        <el-form-item label="员工姓名" class="form-item">
          <el-input v-model="form.name||form.showName" disabled></el-input>
        </el-form-item>
        <el-form-item label="离职申请编号" class="form-item">
          <el-input v-model="form.resignationCode" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="交接人" class="form-item">
          <el-input v-model="form.handoverName" disabled></el-input>
        </el-form-item>
        <el-form-item label="部门" class="form-item">
          <el-input v-model="form.deptName" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="岗位" class="form-item">
          <el-input v-model="form.postName" disabled></el-input>
        </el-form-item>

        <el-form-item label="手机号" class="form-item">
          <el-input v-model="form.phoneNum" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="身份证号码" class="form-item">
          <el-input v-model="form.idCard" disabled></el-input>
        </el-form-item>

        <el-form-item label="直接上级" class="form-item">
          <el-input v-model="form.directSuperior" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="转正日期" class="form-item">
          <el-input v-model="form.formalTime" disabled></el-input>
        </el-form-item>

        <el-form-item label="系统登录名" class="form-item">
          <el-input v-model="form.sysName" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="人员类别" class="form-item">
          <el-select
            v-model="form.personnelType"
            placeholder="请选择人员类别"
            disabled
          >
            <el-option
              v-for="dict in dict.type.personnel_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          label="批准离职日期"
          class="form-item"
          prop="applicationTime"
          :disabled="disabled"
        >
          <el-date-picker
            v-model="form.ratifyTime"
            align="right"
            type="date"
            placeholder="审批过程中选择"
            :picker-options="pickerOptions"
            :disabled="disabled"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="申请离职日期" class="form-item">
          <el-input v-model="form.applicationTime" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex-one">
        <el-form-item label="备注">
          <el-input
            type="textarea"
            v-model="form.remark"
            maxlength="600"
            disabled
          ></el-input>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({
        file: [],
      }),
    },
    disabled: {
      type: Boolean,
      default: true,
    },
  },
  dicts: ["personnel_category"],
  data() {
    return {
      pickerOptions: Object.freeze({
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      }),
    };
  },
  watch: {
    
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      if(this.form.handoverList){
        console.log(this.form.handoverList.map(item=>item.nickName),22222222)
        this.form.handoverName=this.form.handoverList.map(item=>item.nickName);
        this.form.handoverName=this.form.handoverName.jion()
      }
      
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  padding-right: 20px;
  .flex {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .form-item {
      width: 48%;
    }
  }
  .flex-one {
    width: 70%;
    display: flex;
    margin-bottom: 20px;
    ::v-deep .el-form-item {
      width: 100%;
    }
  }
}
</style>