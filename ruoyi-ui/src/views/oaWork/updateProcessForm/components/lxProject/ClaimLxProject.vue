<template>
  <div>
    <div class="data_content" v-if="myForm">
      <div style="display: flex">
        <p class="title2" style="flex-shrink: 0; margin-top: 0">
          认领项目信息:
        </p>
        <div style="width: 600px">
          <div class="row" style="margin-top: 0">
            <div>
              <span>项目名称：</span
              >{{ myForm.oldData.projectForm.projectName }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>原项目负责人：</span
              >{{
                myForm.oldData.projectForm.projectPrincipalList
                  .map((item) => item.nickName)
                  .join(",")
              }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>立项时间：</span>
              {{
                $format(myForm.oldData.projectForm.projectDate, "yyyy-MM-dd")
              }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>原渠道方：</span>
              {{
                myForm.oldData.projectForm.channelType == 1
                  ? myForm.oldData.projectForm.projectChannelList
                      .map((item) => item.nickName)
                      .join(",")
                  : myForm.oldData.projectForm.channelSide
              }}
            </div>
            <div class="row">
              <div>
                <span>渠道方类型：</span
                >{{
                  myForm.oldData.projectForm.channelType == 1 ? "内部" : "外部"
                }}
              </div>
            </div>
          </div>
          <div class="row">
            <div>
              <span>产品分类</span
              >{{
                myForm.oldData.projectForm.xmglBusinessTypeList.length > 0
                  ? myForm.oldData.projectForm.xmglBusinessTypeList
                      .map((item) => item.typeName)
                      .join(",")
                  : ""
              }}
            </div>
            <div>
              <span>项目类型：</span
              >{{
                myForm.oldData.projectForm.xmglProjectTypeList.length > 0
                  ? myForm.oldData.projectForm.xmglProjectTypeList
                      .map((item) => item.typeName)
                      .join(",")
                  : ""
              }}
            </div>
          </div>

          <div class="row">
            <div>
              <span>项目描述：</span>
              {{ myForm.oldData.projectForm.projectDescribe }}
            </div>
          </div>
          <el-divider></el-divider>
          <div class="row">
            <div>
              <span>资金方联系人姓名：</span>{{ myForm.userform.fundName }}
            </div>
            <div>
              <span>资产方联系人姓名：</span>{{ myForm.userform.productName }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>资金方联系人电话：</span>{{ myForm.userform.fundTel }}
            </div>
            <div>
              <span>资产方联系人电话：</span>{{ myForm.userform.productTel }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>资金方联系人微信：</span>{{ myForm.userform.fundWx }}
            </div>
            <div>
              <span>资产方联系人微信：</span>{{ myForm.userform.productWx }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>资金方联系人所属部门：</span>{{ myForm.userform.fundDept }}
            </div>
            <div>
              <span>资产方联系人所属部门：</span
              >{{ myForm.userform.productDept }}
            </div>
          </div>
          <el-divider></el-divider>
          <div class="row" style="margin-top: 0">
            <div>
              <span>新的项目负责人：</span
              >{{ myForm.projectForm.projectUserName.join(",") }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>新的渠道方：</span
              >{{
                myForm.projectForm.channelType == 1
                  ? myForm.projectForm.channelFormName.join(",")
                  : myForm.projectForm.channelSide
              }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>渠道方类型：</span
              >{{ myForm.projectForm.channelType == 1 ? "内部" : "外部" }}
            </div>
          </div>
        </div>
      </div>
      <el-divider></el-divider>

      <div style="display: flex; margin-top: 16px">
        <p class="title2" style="flex-shrink: 0; margin-top: 0">
          <i style="color: red; margin-right: 5px">*</i>认领项目说明：
        </p>
        <el-input
          type="textarea"
          :rows="4"
          :disabled="disabled"
          @input="inputChange"
          show-word-limit
          maxlength="200"
          style="width: 500px"
          placeholder="请输入内容"
          v-model="textarea"
        >
        </el-input>
      </div>
    </div>
  </div>
</template>
        
        <script>
export default {
  props: {
    data: Object,
    disabled: Boolean,
    info: String,
  },

  data() {
    return {
      myForm: null,

      textarea: "",
    };
  },

  mounted() {
    this.textarea = this.info;
    this.myForm = JSON.parse(JSON.stringify(this.data));

    console.log(this.myForm, "1");
  },
  methods: {
    inputChange(e) {
      console.log(e);
      this.$emit("getText", e);
    },
  },
};
</script>
        
  <style lang="less" scoped>
.data_content {
  margin: 0 auto;
  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 16px;
    width: 140px;
    text-align: right;
  }
}
.row {
  display: flex;
  margin-top: 20px;
  justify-content: space-between;
  div {
    span {
      display: inline-block;
      font-weight: bold;
      width: 175px;
      text-align: right;
    }
  }
}
.left,
.right {
  width: 50%;
  div {
    margin-top: 16px;
    span {
      font-weight: bold;
      display: inline-block;
      width: 210px;
      text-align: right;
    }
  }
}
</style>