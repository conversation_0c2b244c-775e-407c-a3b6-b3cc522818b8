<template>
  <div>
    <div class="data_content" v-if="myForm">
      <div style="display: flex">
        <p class="title2" style="flex-shrink: 0; margin-top: 0">
          延期项目信息:
        </p>
        <div style="width: 600px">
          <div class="row" style="margin-top: 0">
            <div>
              <span>项目名称：</span>{{ myForm.projectForm.projectName }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>立项时间：</span>
              {{ $format(myForm.projectForm.projectDate, "yyyy-MM-dd") }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>项目负责人：</span
              >{{
                myForm.projectForm.projectPrincipalList
                  .map((item) => item.nickName)
                  .join(",")
              }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>渠道方：</span>
              {{
                myForm.projectForm.channelType == 1
                  ? myForm.projectForm.projectChannelList
                      .map((item) => item.nickName)
                      .join(",")
                  : myForm.projectForm.channelSide
              }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>渠道方类型：</span
              >{{ myForm.projectForm.channelType == 1 ? "内部" : "外部" }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>产品分类</span
              >{{
                myForm.projectForm.xmglBusinessTypeList.length > 0
                  ? myForm.projectForm.xmglBusinessTypeList
                      .map((item) => item.typeName)
                      .join(",")
                  : ""
              }}
            </div>
            <div>
              <span>项目类型：</span
              >{{
                myForm.projectForm.xmglProjectTypeList.length > 0
                  ? myForm.projectForm.xmglProjectTypeList
                      .map((item) => item.typeName)
                      .join(",")
                  : ""
              }}
            </div>
          </div>

          <div class="row">
            <div>
              <span>项目描述：</span>
              {{ myForm.projectForm.projectDescribe }}
            </div>
          </div>
          <el-divider></el-divider>
          <div class="row">
            <div>
              <span>申请延期天数：</span>
              {{ myForm.day }}天
            </div>
            <div>
              <span>延期后锁定期将延长至：</span>
              {{ myForm.newDate }}
            </div>
          </div>
        </div>
      </div>
      <el-divider></el-divider>

      <div style="display: flex; margin-top: 16px">
        <p class="title2" style="flex-shrink: 0; margin-top: 0">
          <i style="color: red; margin-right: 5px">*</i>延期原因说明:
        </p>
        <el-input
          type="textarea"
          :rows="4"
          :disabled="disabled"
          @input="inputChange"
          show-word-limit
          maxlength="200"
          style="width: 500px"
          placeholder="请输入内容"
          v-model="textarea"
        >
        </el-input>
      </div>
    </div>
  </div>
</template>
          
          <script>
export default {
  props: {
    data: Object,
    disabled: Boolean,
    info: String,
  },

  data() {
    return {
      myForm: null,

      textarea: "",
    };
  },

  mounted() {
    this.textarea = this.info;
    this.myForm = JSON.parse(JSON.stringify(this.data));

    console.log(this.myForm, "1");
  },
  methods: {
    inputChange(e) {
      console.log(e);
      this.$emit("getText", e);
    },
  },
};
</script>
          
    <style lang="less" scoped>
.data_content {
  margin: 0 auto;
  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 16px;
    width: 140px;
    text-align: right;
  }
}
.row {
  display: flex;
  margin-top: 20px;
  justify-content: space-between;
  div {
    span {
      display: inline-block;
      font-weight: bold;
      width: 175px;
      text-align: right;
    }
  }
}
.left,
.right {
  width: 50%;
  div {
    margin-top: 16px;
    span {
      font-weight: bold;
      display: inline-block;
      width: 210px;
      text-align: right;
    }
  }
}
</style>