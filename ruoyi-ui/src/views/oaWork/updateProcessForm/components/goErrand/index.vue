<template>
  <div>
    <el-image
      ref="previewImg"
      v-show="false"
      :src="photoUrl"
      :preview-src-list="imagePreviewUrls"
    />
    <div>
      <el-descriptions :column="2" border>
        <el-descriptions-item
          ><template slot="label"> 出差申请人: </template>
          {{ tableData.applicantName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 出差申请单号: </template>
          {{ tableData.businessTripCode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 合计出差时长: </template>
          {{ tableData.businessTripTimes }}天
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 关联项目: </template>
          {{ tableData.projectName }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="1" border>
        <el-descriptions-item>
          <template slot="label"> 出差同行人: </template>
          {{
            tableData.companionsList &&
            tableData.companionsList.map((item) => item.nickName).join("、")
          }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 出差事由: </template>
          {{ tableData.businessTripReason }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 附件上传: </template>
          <div v-for="(item, index) in tableData.files" :key="index">
            {{ item.fileName }}
            <el-button type="text" @click="handleDownload(item)" class="ml-2"
              >下载</el-button
            >
            <el-button
              v-show="
                item.fileName.endsWith('.pdf') ||
                item.fileName.endsWith('.jpg') ||
                item.fileName.endsWith('.png') ||
                item.fileName.endsWith('.gif') ||
                item.fileName.endsWith('.jpeg')
              "
              type="text"
              @click="handlePreview(item)"
              >查看</el-button
            >
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 备注: </template>
          {{ tableData.remark }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <MyTable
      class="my-2"
      ref="table"
      :columns="columns"
      :source="tableData.businessTripSlaveList"
    >
      <template #startTimes="{ record }">
        <div>{{ record.startTime }} {{ record.startTimePeriod }}</div>
      </template>
      <template #endTimes="{ record }">
        <div>{{ record.endTime }} {{ record.endTimePeriod }}</div>
      </template>
      <template #times="{ record }">
        <div>{{ record.times }}</div>
        <div>(合计{{ tableData.businessTripTimes }}天)</div>
      </template>
    </MyTable>
  </div>
</template>
<script>
import privew from "@/mixin/privew";
export default {
  mixins: [privew],
  name: "GoErrand",
  props: {
    tableData: {
      type: Object,
    },
  },

  data() {
    return {
      columns: Object.freeze([
        {
          label: "出发地点",
          prop: "setOut",
        },
        {
          label: "到达地点",
          prop: "reach",
        },
        {
          label: "出差起始时间",
          key: "startTimes",
        },
        {
          label: "出差结束时间",
          key: "endTimes",
        },
        {
          label: "出差时长(天)",
          key: "times",
        },
        {
          label: "交通工具",
          prop: "vehicle",
        },
      ]),
    };
  },
  created() {},
  methods: {},
};
</script>
<style lang="scss" scoped>
::v-deep .el-descriptions-item__label {
  width: 200px;
}
</style>