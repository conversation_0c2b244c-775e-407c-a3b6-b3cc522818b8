<template>
  <div>
    <div class="data_content">
      <div>
        <p class="title2">修改内容：</p>
        <ContrastDetailNewTable
          :data="datas"
          ref="ContrastDetailNewTable"
        ></ContrastDetailNewTable>
      </div>
      <p class="title2">
        <i style="color: red; margin-right: 5px">*</i>修改原因说明：
      </p>
      <el-input
        type="textarea"
        :rows="4"
        :disabled="disabled"
        @input="inputChange"
        show-word-limit
        maxlength="200"
        placeholder="请输入内容"
        v-model="textarea"
      >
      </el-input>
    </div>
  </div>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";
import XEUtils from "xe-utils";
export default {
  provide() {
    return {
      contrast: this.contrast,
      arraryToStringItem: this.arraryToStringItem,
      dictionary:{
        isEnable:{Y:'开启',N:'关闭'}
      }
    };
  },
  props: {
    data: Object,
    disabled: Boolean,
    info: String,
  },
  data() {
    return {
      textarea: "",
      companytypeList: [],
      datas: {},
      contrast: {}, //对比表格映射的字段
      arraryToStringItem: {},
    };
  },
  mounted() {
    this.getDicts();
    this.textarea = this.info;
    this.handleForm();

    this.getColoumn();
  },
  methods: {
    inputChange(e) {
      console.log(e);
      this.$emit("getText", e);
    },
    getDicts() {
      return new Promise((resolve) => {
        const tasks = [];
        const params = ["project_type", "company_type"];
        params.forEach((item) => {
          tasks.push(getDicts(item));
        });
        Promise.all(
          tasks.map((p) => {
            //.then返回需要的东西 .catch返回一些错误信息
            return p
              .then((e) => {
                return p;
              })
              .catch((err) => {
                return "错误了";
              });
          })
        )
          .then((res) => {
            this.companytypeList = res[1].data;
            resolve();
          })
          .catch((reason) => {
            console.log(reason);
          });
      });
    },
    getColoumn() {
      let newObj = {};
      let oldObj = {};
      // 遍历 arr 数组
      this.$store.state.data.OPTION_MAP.select_name.forEach((item) => {
        let key = item.value + "List"; // 根据 value 构建匹配的键
        if (this.datas.newData[key]) {
          newObj[key] = item.label;
        }
        if (this.datas.oldData[key]) {
          oldObj[key] = item.label;
        }
      });
      let longerObj =
        Object.keys(newObj).length >= Object.keys(oldObj).length
          ? newObj
          : oldObj;
      console.log(longerObj, 123);
      this.contrast = {
        projectName: "项目名称",
        ...longerObj,
        otherUnitList: "其他公司",
        projectTypeList: "项目类型",
        businessTypeList: "产品分类",
        isEnable: "启用状态",
      };
      const arraryToStringItems = XEUtils.clone(longerObj, true);
      Object.keys(arraryToStringItems).forEach((item) => {
        arraryToStringItems[item] = "unitShortName";
      });
      this.arraryToStringItem = {
        ...arraryToStringItems,
        otherUnitList:'unitShortName',
        projectTypeList: "typeName",
        businessTypeList: "typeName",
      };
      this.$refs.ContrastDetailNewTable.getRecordList(
        this.datas.newData,
        this.datas.oldData,
        this.contrast,
        this.arraryToStringItem
      );
    },
    handleForm() {
      this.datas = XEUtils.clone(this.data, true);
      this.datas.oldData = {
        ...this.datas.oldData,
        ...this.datas.oldData.tableList,
      };
      this.datas.newData = {
        ...this.datas.newData,
        ...this.datas.newData.tableList,
      };
      this.datas.oldInfo = JSON.stringify(this.datas.oldData);
      this.datas.newInfo = JSON.stringify(this.datas.newData);
    },
  },
};
</script>

<style lang="less" scoped>
</style>