<template>
  <div>
    <div class="data_content">
      <div style="display: flex">
        <p class="title2">收付款人详情：</p>
        <div>
          <div>
            申请人申请在 [业务信息配置-收付款人] 中{{
              data.editType == 0 ? "新增" : "删除"
            }}以下收付款信息，请审核
          </div>
          <el-descriptions class="margin-top" :column="1" border>
            <el-descriptions-item>
              <template slot="label"> 类型 </template>
              {{
                data.traderType == 1
                  ? "收款人"
                  : data.traderType == 0
                  ? "付款人"
                  : data.traderType == 9
                  ? "收/付款人"
                  : ""
              }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 主体类型 </template>
              {{ data.type == 0 ? "公司" : data.type == 1 ? "个人" : "" }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 账户名称 </template>
              {{ data.userName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 开户行 </template>
              {{ data.bankOfDeposit }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 账号 </template>
              {{ data.accountNumber }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 简称 </template>
              {{ data.abbreviation }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 所属公司 </template>
              {{ company(data.companyNo) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 所属账套 </template>
              {{ ass(data.accountId) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 启用状态 </template>
              {{ data.isEnable == "Y" ? "启用" : "关闭" }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <div style="display: flex; margin-top: 16px">
        <p class="title2">
          <i style="color: red; margin-right: 5px">*</i
          >{{ data.editType == 0 ? "新增" : "删除" }}原因说明：
        </p>
        <el-input
          type="textarea"
          :rows="4"
          :disabled="disabled"
          @input="inputChange"
          show-word-limit
          maxlength="200"
          placeholder="请输入内容"
          v-model="textarea"
        >
        </el-input>
      </div>
    </div>
  </div>
</template>
    
    <script>
import { selectAccountInfo, selectCompanyInfo } from "@/api/oa/voucharRules";

export default {
  props: {
    data: Object,
    disabled: Boolean,
    info: String,
  },
  data() {
    return {
      textarea: "",
      accountSetList: [],
      projects: [],
    };
  },
  mounted() {
    console.log(this.data);

    this.textarea = this.info;
    selectCompanyInfo({queryAllFlag:'1'}).then((response) => {
      this.projects = response.data;
    });
    selectAccountInfo({queryAllFlag:'1'}).then((response) => {
      this.accountSetList = response.data;
    });
  },
  methods: {
    inputChange(e) {
      console.log(e);
      this.$emit("getText", e);
    },
    company(v) {
      let data = this.projects.find((item) => {
        return item.companyId == v;
      });
      if(data){
        return data.name;
      }
      
    },
    ass(v) {
      let data = this.accountSetList.find((item) => {
        return item.id == v;
      });
      if(data){
        return data.companyName;
      }
      
    },
  },
};
</script>
    
    <style lang="less" scoped>
.data_content {
  margin: 0 auto;
  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    width: 132px;
    display: inline-block;
    text-align: right;
    flex-shrink: 0;
  }
}
</style>