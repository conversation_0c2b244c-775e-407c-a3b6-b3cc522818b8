<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="员工姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入员工姓名"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="岗位" prop="onboardingPost">
        <el-select
          v-model="queryParams.onboardingPost"
          placeholder="岗位"
          clearable
          size="small"
          @clear="handleQuery"
        >
          <el-option
            v-for="dict in onboardingPostList"
            :key="dict.postId"
            :label="dict.postName"
            :value="dict.postId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属部门" prop="onboardingDept">
        <treeselect
          style="width: 200px"
          v-model="queryParams.onboardingDept"
          :options="deptOptions"
          :normalizer="normalizer"
          :show-count="true"
          placeholder="选择入所属部门"
        />
      </el-form-item>
      <!-- <el-form-item label="身份证号码" label-width="90px" prop="idCard">
        <el-input
          v-model="queryParams.idCard"
          placeholder="请输入身份证号码"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item> -->

      <el-form-item label="入职日期">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="实际转正日期">
        <el-date-picker
          v-model="actualDateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="单据状态" prop="formalState" v-show="tabTable == 3">
        <el-select
          v-model="queryParams.formalState"
          placeholder="请选择单据状态"
          clearable
          size="small"
          @clear="handleQuery"
        >
          <el-option
            v-for="(item, index) in documentStateList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="系统登录名" prop="sysName">
        <el-input
          v-model="queryParams.sysName"
          placeholder="请输入系统登录名"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-tabs
      v-model="tabTable"
      type="card"
      @tab-click="handleClick"
      style="margin-top: 10px"
    >
      <el-tab-pane
        :label="item.label"
        :name="item.name"
        v-for="(item, index) in tabTableList"
        :key="index"
      ></el-tab-pane>
    </el-tabs>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdateOpear"
          v-if="['3'].includes(tabTable)"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-if="['3'].includes(tabTable)"
          >删除</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          :disabled="single"
          @click="application()"
          v-if="['3'].includes(tabTable)"
          >发起转正申请</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table
      :key="tabTable"
      v-loading="loading"
      :data="configList"
      @selection-change="handleSelectionChange"
      row-key="id"
      ref="multipleTable"
    >
      <el-table-column
        v-if="tabTable == '3'"
        type="selection"
        width="55"
        align="center"
        reserve-selection
        fixed="left"
      />
      <el-table-column
        v-if="tabTable == '3'"
        type="index"
        label="序号"
        width="50"
        :index="columnIndex"
      />

      <el-table-column label="员工姓名" align="center" width="120">
        <template #default="{ row }">
          <el-button size="mini" type="text" @click="handleUpdate(row, true)">{{
            row.name
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="系统登录名"
        align="center"
        prop="sysName"
        width="180"
      />
      <el-table-column
        label="岗位"
        align="center"
        prop="onboardingPostName"
        width="180"
      />
      <el-table-column
        label="所属部门"
        align="center"
        prop="onboardingDeptName"
        min-width="200"
      />

      <el-table-column
        label="考核评估分数"
        align="center"
        prop="assessmentScore"
        :show-overflow-tooltip="true"
        width="180"
      />
      <el-table-column
        label="公司PPT考核分数"
        align="center"
        prop="pptAssessmentScore"
        :show-overflow-tooltip="true"
        width="180"
      />
      <el-table-column
        label="专业知识考试分数"
        align="center"
        prop="professionalKnowledgeScore"
        :show-overflow-tooltip="true"
        width="180"
      />

      <el-table-column
        label="入职日期"
        align="center"
        prop="onboardingTime"
        width="180"
      >
        <template #default="{ row }">
          <span>{{ row.onboardingTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="实际转正日期"
        align="center"
        prop="realFormalTime"
        width="180"
      >
        <template #default="{ row }">
          <span>{{ row.realFormalTime }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="单据状态"
        align="center"
        prop="formalState"
        width="180"
      >
        <template #default="{ row }">
          <span>{{ documentStateObj[row.formalState] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建人"
        align="center"
        prop="createBy"
        width="180"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="{ row }">
          <span>{{ row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="审核通过时间"
        align="center"
        prop="completeTime"
        width="180"
        v-if="tabTable == '2'"
      >
        <template #default="{ row }">
          <span>{{ row.completeTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        min-width="160"
      >
        <template #default="{ row }">
          <div v-if="tabTable == '3'">
            <div v-if="row.formalState == 1 || row.formalState == 3">
              <el-button size="mini" type="text" @click="handleUpdate(row)"
                >修改</el-button
              >
              <el-button size="mini" type="text" @click="submitUpdata(row)"
                >提交</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(row, true)"
                >查看详情</el-button
              >
            </div>
            <div v-else>
              <el-button @click="application(row)" size="mini" type="text"
                >发起转正申请</el-button
              >
            </div>
          </div>
          <el-button v-else size="mini" type="text" @click="seePro(row)"
            >查看流程详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <!-- <DetailDialog
      :form="form"
      :deptOptions="deptOptions"
      :onboardingPostList="onboardingPostList"
      :title="formTitle"
      v-model="open"
      @on-save-success="getList"
      @on-submit-success="submitUpdata"
      :disabled="disabledForm"
    /> -->
    <Detail
      :formProp="form"
      :title="formTitle"
      :type="formType"
      v-model="open"
      @on-save-success="getList"
      @on-submit-success="submitUpdata"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompanyType"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import {
  onboardingSelectFormalList,
  onboardingGetFormalList,
  updateFormal,
  checkFormalByIds,
  getPersonnelOnboardingFlow,
  delFormal,
} from "@/api/personnel/becomeWorker";
import config from "./components/config";
import { getPostAuthorizationList } from "@/api/directoryMation/directoryMation";
import { treeselect } from "@/api/system/dept";
import DetailDialog from "./components/DetailDialog.vue";
import Detail from "./components/detail.vue";
import Treeselect from "@riophae/vue-treeselect";
import SelectCompany from "@/components/SelectCompany/index.vue";
import { clone } from "xe-utils";

export default {
  name: "BecomeWorker",
  components: { DetailDialog, Treeselect, SelectCompany ,Detail},
  data() {
    return {
      ...config,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      //详情信息
      form: {},
      formTitle: "",
      disabledForm: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      onboardingPostList: [],
      deptOptions: [],
      // 总条数
      total: 0,
      tabTable: "3",
      // 参数表格数据
      configList: [],
      // 日期范围
      dateRange: [],
      actualDateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        idCard: undefined,
        formalState: undefined,
        onboardingPost: undefined,
        onboardingDept: undefined,
        sysName: undefined,
      },
      multipleSelection: [],
      names: [],
      documentStateList: Object.freeze([
        { label: "未提交", value: "3" },
        { label: "未生成", value: "5" },
        { label: "审核不通过", value: "4" },
      ]),
      documentStateObj: Object.freeze({
        3: "未提交",
        5: "未生成",
        4: "审核不通过",
        1: "审核中",
        2: "审核通过",
      }),
      // 表单参数
      open: false,
      formType:"add",
      selectCompanyType: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
      this.getPostAuthorizationList();
      this.getTreeselect();
    },
    async getPostAuthorizationList() {
      const { rows } = await getPostAuthorizationList();
      this.onboardingPostList = [...rows];
    },
    async getTreeselect() {
      const { data } = await treeselect();
      this.deptOptions = [...data];
    },
    columnIndex(index) {
      return (
        index + 1 + (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      );
    },
    handleClick() {
      this.getList();
    },
    /** 查询参数列表 */
    async getList() {
      this.loading = true;
      const apiList = {
        3: onboardingSelectFormalList,
        1: onboardingGetFormalList,
        2: onboardingGetFormalList,
      };
      let paramsCommon = this.addDateRangeCustom(
        this.queryParams,
        this.dateRange,
        ["onboardingTimeStart", "onboardingTimeEnd"]
      );
      paramsCommon = this.addDateRangeCustom(
        paramsCommon,
        this.actualDateRange,
        ["realFormalTimeStart", "realFormalTimeEnd"]
      );
      const specialParams =
        this.tabTable == 3 ? {} : { formalState: this.tabTable };
      const params = clone(
        {
          ...paramsCommon,
          ...specialParams,
        },
        true
      );
      const { rows, total } = await apiList[this.tabTable](params);
      this.configList = rows;
      this.total = total;
      this.loading = false;
      this.clearCheck();
    },
    clearCheck() {
      this.$nextTick(() => {
        this.$refs.multipleTable.clearSelection();
        this.multipleSelection = [];
        this.single = true;
        this.multiple = true;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.actualDateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    application(row) {
      // this.submitUpdata(this.multipleSelection[0]);
      const form = row || this.multipleSelection[0];
      this.form={...form}
      this.formTitle="新增转正申请";
      this.formType="add";
      this.open=true;
      // this.$router.push({
      //   path: "/personnelOther/belcomeWorker/" + form.onboardingId,
      //   query: {
      //     title: "新增转正申请",
      //     form: JSON.stringify(form),
      //     type: "add",
      //   },
      // });
    },
    async submitUpdata(value) {
      this.form = { ...value};
      await checkFormalByIds(this.form.onboardingId);
      if (value.processId) {
        this.$router.push({
          path: "/oaWork/processFormView",
          query: {
            oid: value.processId,
            myActiviteType: true,
          },
        });
        this.open = false;
        return;
      }
      this.selectCompanyType = true;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
      this.ids = selection.map((item) => item.id);
      this.names = selection.map((item) => item.name);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    handleUpdateOpear() {
      this.handleUpdate(this.multipleSelection[0]);
    },
    /** 修改按钮操作 */
    handleUpdate(row, isView = false) {
      this.form = {...row};
      this.formTitle=isView?"查看转正申请详情" : "修改转正申请";
      this.formType=isView ? "view" : "add";
      this.open=true;
      // this.$router.push({
      //   path: "/personnelOther/belcomeWorker/" + row.onboardingId,
      //   query: {
      //     title: isView ? "查看转正申请详情" : "修改转正申请",
      //     form: JSON.stringify(row),
      //     type: isView ? "view" : "add",
      //   },
      // });
    },
    closeCompanyType() {
      this.selectCompanyType = false;
      this.getList();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const idNames = row.name || this.names;
      this.$modal
        .confirm('是否确认删除员工姓名为"' + idNames + '"的数据项？')
        .then(function () {
          return delFormal(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const exportUrl = {
        1: {
          url: "/personnel/formal/export",
          params: { ...this.queryParams,formalState:1,ids: JSON.stringify(this.onboardingId)},
          name: "转正中.xlsx",
        },
        2: {
          url: "personnel/formal/export",
          params: { ...this.queryParams,formalState:2,ids: JSON.stringify(this.onboardingId)},
          name: "已转正.xlsx",
        },
        3: {
          url: "personnel/onboarding/getFormalBeforeList/export",
          params: { ...this.queryParams, ids: JSON.stringify(this.onboardingId) },
          name: "未转正.xlsx",
        },
      };
      this.download(
        exportUrl[this.tabTable].url,
        exportUrl[this.tabTable].params,
        exportUrl[this.tabTable].name
      );
    },
    seePro(v) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: v.processId,
          businessId: v.processId,
          myActiviteType: true,
        },
      });
    },
    async submitCompany(e) {
      const { data } = await updateFormal({
        ...this.form,
      });
      const { classificationId, companyId, templateId } =
        await getPersonnelOnboardingFlow({ companyId: e });
      this.selectCompanyType = false;
      sessionStorage.setItem("oa-becomeForm", JSON.stringify(data));
      this.$router.push({
        path: "/oaWork/updateProcessForm",
        query: {
          templateId,
          classificationId,
          companyId,
          perBecome: true,
        },
      });
    },
  },
};
</script>
