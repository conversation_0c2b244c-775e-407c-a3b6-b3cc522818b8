<template>
  <div class="p-5 pb-20">
    <div>借据号</div>
    <div>{{$route.query.promissory}}</div>
    <el-tabs v-model="currentTab" type="card" class="mt-4">
      <el-tab-pane
        :key="index"
        v-for="(item, index) in tabsList"
        :label="item.label"
        :name="item.name"
      >
      </el-tab-pane>
    </el-tabs>
    <component
      :is="currentTab"
    ></component>
    <InBody>
      <div
        class="flex justify-center fixed bottom-0 bg-white z-10 w-full pb-2"
        style="left: 130px"
      >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </InBody>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import BorrowerInformation from "./components/BorrowerInformation.vue";
import CaseInformation from "./components/CaseInformation.vue";
import DetailsOfRepaymentAfter from "./components/DetailsOfRepaymentAfter.vue";
import HistoricalDivisionCase from "./components/HistoricalDivisionCase.vue";
import DetailsOfRepaymentBefore from "./components/DetailsOfRepaymentBefore.vue";
export default {
  name: "AssetManagementDetail",
  components: {
    BorrowerInformation,
    CaseInformation,
    DetailsOfRepaymentAfter,
    HistoricalDivisionCase,
    DetailsOfRepaymentBefore,
  },
  data() {
    return {
      currentTab: "BorrowerInformation",
      tabsList:Object.freeze( [
        { label: "借款人信息", name: "BorrowerInformation" },
        { label: "案件信息", name: "CaseInformation" },
        { label: "本次委后还款明细", name: "DetailsOfRepaymentAfter" },
        { label: "历史分案", name: "HistoricalDivisionCase" },
        { label: "还款明细", name: "DetailsOfRepaymentBefore" },
      ]),
    };
  },

  mounted() {
    this.init();
  },
  methods: {
    init() {},
    cancel() {
      this.$router.go(-1);
    },
  },
};
</script>
