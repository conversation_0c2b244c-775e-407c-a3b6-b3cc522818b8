<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
      label-width="165px"
    />
    <el-divider></el-divider>

    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          v-hasPermi="['badSystem:financialProductConfiguration:add']"
          @click="handleAdd"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          >新增产品</el-button
        >
      </div>
    </div>
    <MyTable
      :columns="columns"
      :showIndex="true"
      :source="configList"
      :queryParams="queryParams"
    >
      <template #assetCompanyName="{ record }">
        <el-button v-if="checkPermi(['badSystem:financialProductConfiguration:view'])" type="text" @click="handleView(record)">{{
          record.assetCompanyName || "-"
        }}</el-button>
        <div v-else>{{ record.assetCompanyName || "-" }}</div>
      </template>
      <template #operate="{ record }">
        <el-button v-hasPermi="['badSystem:financialProductConfiguration:view']" type="text" @click="handleView(record)">查看</el-button>
        <el-button v-hasPermi="['badSystem:financialProductConfiguration:update']" type="text" @click="handleUpdate(record)">修改</el-button>
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <DetailDialog
      v-model="open"
      :form="dialogForm"
      :dialogType="dialogType"
      @on-submit-success="getList"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { newCompanySelectList } from "@/api/businessInformation/companyInformation";
import { getCompanyProductList } from "@/api/badSystem/financialProductConfiguration";
import DetailDialog from "./components/DetailDialog.vue";
import { checkPermi } from "@/utils/permission"; // 权限判断函数

import config from "./components/config";
export default {
  name: "FinancialProductConfiguration",
  components: { DetailDialog },

  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName:undefined
      },
      total: 0,
      configList: [],
      open: false,
      dialogForm: {},
      dialogType: "add",
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    checkPermi,
    init() {
      this.getCompanyList();
      this.getList();
    },

    async getCompanyList() {
      const response = await newCompanySelectList({
        selectCode: "partner",
        modelCode: "BADSYSTEM",
      });
      this.formColumns[1].options = response;
      this.formColumnsDialog[0].options = response;
    },
    async getList() {
      const { rows, total } = await getCompanyProductList(this.queryParams);
      this.configList = this.handleConfigList(rows);
      this.total = total;
    },
    handleConfigList(row) {
      row.forEach((item) => {
        item.interestMethodLabel = this.interestMethodObj[item.interestMethod];
        item.enableStatusLabel = this.enableStatusObj[item.enableStatus];
        item.companyProductInterestListLength =
          item.companyProductInterestList.length;
      });
      return row;
    },
    handleAdd() {
      this.dialogType = "add";
      this.dialogForm = {};
      this.open = true;
    },
    handleUpdate(record) {
      this.dialogType = "update";
      this.dialogForm = { ...record };
      this.open = true;
    },
    handleView(row) {
      this.dialogType = "view";
      this.dialogForm = { ...row };
      this.open = true;
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleImport() {
      this.openImport = true;
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
