<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
      label-width="110px"
    />
    <el-divider></el-divider>

    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          v-hasPermi="['bad:financialSettlement:add']"
          @click="handleAdd()"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          >创建结算单</el-button
        >
      </div>
    </div>
    <MyTable
      :columns="columns"
      :showIndex="true"
      :source="configList"
      :queryParams="queryParams"
    >
      <template #settlementDocumentCode="{ record }">
        <el-button v-if="checkPermi(['badSystem:financialSettlement:view'])" type="text" @click="handleView(record)">{{
          record.settlementDocumentCode || "-"
        }}</el-button>
        <div v-else>{{ record.settlementDocumentCode || "-" }}</div>
      </template>
      <template #financialSettlementReconciliationList="{ record }">
        <div
          v-for="(item, index) in record.financialSettlementReconciliationList"
          :key="index"
        >
          {{ item.reconciliationCode }}
        </div>
      </template>

      <template #operate="{ record }">
        <el-button v-hasPermi="['bad:financialSettlement:view']" type="text" @click="handleView(record)">查看</el-button>
        <el-button v-hasPermi="['bad:financialSettlement:update']" type="text" @click="handleAdd(record)">修改</el-button>
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { getSettlementList } from "@/api/badSystem/financialSettlement";
import { getMechanismList } from "@/api/badSystem/organizationalManagement";
import { checkPermi } from "@/utils/permission"; // 权限判断函数

import config from "./components/config";
export default {
  name: "FinancialSettlement",

  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      configList: [],
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    checkPermi,
    init() {
      this.getOrganizational();
      this.getList();
    },
    async getOrganizational() {
      const { rows } = await getMechanismList();
      this.formColumns[0].options = rows;
    },
    async getList() {
      const { rows, total } = await getSettlementList(this.queryParams);
      this.configList = this.handleConfigList(rows);
      this.total = total;
    },
    handleConfigList(row) {
      const newRow = XEUtils.clone(row, true);
      const addYuan = [
        "settlementAmount",
        "financialReceiptsAmount",
        "unsettledAmount",
      ];
      newRow.forEach((item) => {
        addYuan.forEach((item1) => {
          item[item1] = item[item1]?item[item1].toLocaleString("zh-CN") + "元":"";
        });
        item.statusLabel = this.shenpiObj[item.status];
        item.settlementStatusLabel =
          this.settlementStatusObj[item.settlementStatus];
      });
      return newRow;
    },
    handleAdd(record) {
      this.$router.push({
        path: `/badSystemOther/financialSettlementDetail/${
          (record && record.id) || "add"
        }`,
        query: {
          title: `${record && record.id ? "修改财务结算单" : "创建财务结算单"}`,
        },
      });
    },

    handleUpdate(row) {},
    handleView(row) {
      this.$router.push({
        path: `/badSystemOther/financialSettlementDetailView/${row.id}`,
        query: {
          title: `财务结算单详情${row.settlementDocumentCode}`,
        },
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
