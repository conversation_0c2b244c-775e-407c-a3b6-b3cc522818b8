<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      width="1050px"
      title="选择业务对账单"
      @close="handleClose"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div class="content">
          <MyForm
            v-model="queryParams"
            :columns="formColumnsDialog"
            @onSearchList="handleQuery"
          >
          </MyForm>
          <el-button @click="openSelect = true" type="primary" class="mb-2">
            查看选择{{ multipleSelection.length }}
          </el-button>
          <MyTable
            ref="multipleTable"
            :columns="columnsDialog"
            :source="configList"
            :showCheckbox="true"
            @orderChange="orderChange"
            @selection-change="handleSelectionChange"
          >
          </MyTable>
          <TableSelect
            :columns="columnsDialogSelect"
            :tableData="multipleSelection"
            v-model="openSelect"
            @on-submit-success-row="submitDelet"
          />
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button type="primary" @click="onSave">确定</el-button>
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import config from "./config";
import { getReconciliationList } from "@/api/badSystem/financialSettlement";

export default {
  name: "AccountDialog",
  mixins: [vModelMixin],
  props: {
    settlementInstitutionId: {
      required: false,
    },
  },
  watch: {},
  data() {
    return {
      ...config,
      total: 0,
      configList: [],
      queryParams: {
        settlementDocumentCode: "",
        pageNum: 1,
        pageSize: 10,
      },
      multipleSelection: [],
      openSelect: false,
    };
  },
  mounted() {},
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    onSave() {
      if (!this.multipleSelection.length) {
        this.$modal.msgError("请至少选择一项");
        return;
      }

      this.innerValue = false;
      this.$emit("on-save-success", this.multipleSelection);
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    async handleOpen() {
      this.queryParams.pageNum = 1;
      this.multipleSelection = [];
      this.getList();
      this.$nextTick(() => {
        this.$refs.multipleTable.clearSelection();
      });
    },
    async getList() {
      const { rows, total } = await getReconciliationList({...this.queryParams,settlementInstitutionId:this.settlementInstitutionId});
      this.configList = rows;
      this.configList.forEach((item) => {
        item.totalAmountCollectedLabel = item.totalAmountCollected
          ? item.totalAmountCollected.toLocaleString("zh-CN") + "元"
          : "";
        item.totalRepaymentAmountLabel = item.totalRepaymentAmount
          ? item.totalRepaymentAmount.toLocaleString("zh-CN") + "元"
          : "";
      });
      this.total = total;
    },
    orderChange(value) {
      const result = value.reduce((acc, { order, prop }) => {
        console.log(prop, 22);
        if (prop.indexOf("Label") != -1) {
          acc[prop.replace("Label", "")] = order;
        } else {
          acc[prop] = order;
        }

        return acc;
      }, {});
      this.queryParams.sortMap = result;

      this.getList();
    },
    submitDelet(e) {
      console.log(e);
      e.forEach((row) => {
        this.$nextTick(() => {
          this.$refs.multipleTable.toggleRowSelection(row, false);
        });
      });
    },
    handleClose() {},
  },
};
</script>
<style lang="less" scoped>
.content {
  max-height: 70vh;
  padding-right: 20px;
}
</style>