<template>
  <div class="p-5">
    <el-button
      type="primary"
      v-hasPermi="['meituanCash:add']"
      size="mini"
      @click="addRecord"
      >+新增提现记录</el-button
    >
    <el-table
      :data="tableData"
      show-summary
      :summary-method="getSummaries"
      style="width: 100%; margin-top: 16px"
    >
      <el-table-column label="序号" prop="xh" width="60" />
      <el-table-column width="320" label="消费期间" prop="xh2">
        <template slot-scope="scope">
          {{ scope.row.beginDate }} - {{ scope.row.endDate }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            v-hasPermi="['meituanCash:edit']"
            v-if="scope.row.isChange == 0"
            @click="edit(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            v-if="scope.row.isChange == 0"
            @click="gen(scope.row)"
            v-hasPermi="['meituanCash:addGen']"
            >生成凭证</el-button
          >
          <el-button type="text" v-else style="color: #999"
            >已生成凭证</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="消费额" prop="consumeAmt">
        <template slot-scope="scope">
          {{ formatAmount(scope.row.consumeAmt) }}
        </template>
      </el-table-column>
      <el-table-column label="到账额" prop="intoAccount">
        <template slot-scope="scope">
          {{ formatAmount(scope.row.intoAccount) }}
        </template>
      </el-table-column>
      <el-table-column label="手续费" prop="serviceAmt">
        <template slot-scope="scope">
          {{ formatAmount(scope.row.serviceAmt) }}
        </template>
      </el-table-column>
      <el-table-column label="提现日期" prop="withdrawDate"></el-table-column>
      <el-table-column label="美团记账" prop="meituanTally">
        <template slot-scope="scope">
          {{ formatAmount(scope.row.meituanTally) }}
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        prop="remark"
        show-overflow-tooltip=""
      ></el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <AddRecord
      :detail="detail"
      v-if="addType"
      @close="addType = false"
      @submit="getList()"
    />
    <GenDetail
      v-if="genDetailType"
      @close="genDetailType = false"
      @submit="getList"
      :detail="detail"
    />
  </div>
</template>

<script>
import {
  meituanWithdraw,
  createMTVouchar,
  getAmtTotal,
} from "@/api/lyxManage/dailyLedger";
import AddRecord from "./AddRecord.vue";
import GenDetail from "./GenDetail.vue";
export default {
  components: {
    AddRecord,
    GenDetail,
  },
  data() {
    return {
      genDetailType: false,
      queryParams: {
        pageNum: 1,
        pageSize: 50,
      },
      total: 0,
      addType: false,
      tableData: [],
      detail: null,
      allData: {},
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 2) {
          sums[index] = "总计";
          return;
        }
        if (index === 3) {
          sums[index] = this.formatAmount(this.allData.consumeAmt);
          return;
        }
        if (index === 4) {
          sums[index] = this.formatAmount(this.allData.intoAccount);
          return;
        }
        if (index === 5) {
          sums[index] = this.formatAmount(this.allData.serviceAmt);
          return;
        }
        if (index === 7) {
          sums[index] = this.formatAmount(this.allData.meituanTally);
          return;
        }
        return;
        if (
          column.property !== "xh" &&
          column.property !== "xh2" &&
          column.property !== "withdrawDate" &&
          column.property !== "remark"
        ) {
          const values = data.map((item) => item[column.property]);

          // const total = values.reduce((sum, value) => sum + value, 0);
          const total = values.reduce((sum, value) => {
            if (!value) {
              value = 0;
            }
            console.log(sum, value);
            return sum + value;
          }, 0);
          console.log(total);

          sums[index] = this.formatAmount(total); // 使用会计计数法格式化金额
        } else {
          // 其他列合计逻辑...
          sums[index] = ""; // 或者进行其他处理
        }
      });
      console.log(sums);
      return sums;
    },
    gen(v) {
      this.detail = v;
      this.genDetailType = true;
    },
    edit(v) {
      this.detail = v;
      this.addType = true;
    },
    addRecord() {
      this.detail = null;
      this.addType = true;
    },
    getList() {
      this.addType = false;
      this.genDetailType = false;
      meituanWithdraw({ ...this.queryParams }).then((res) => {
        this.tableData = res.rows;
        this.tableData.forEach((item, index) => {
          item.xh = (this.queryParams.pageNum - 1) * 50 + index + 1;
        });
        this.total = res.total;
      });
      getAmtTotal().then((res) => {
        this.allData = res;
      });
    },
    formatAmount(a, b) {
      if (a == "" || a == "undefined" || a == null) {
        return "0.00";
      }
      var c = "number" == typeof b && b > 0 && 20 >= b ? b : 2;
      a = parseFloat((a + "").replace(/[^\d.-]/g, "")).toFixed(c) + "";
      for (
        var d = a.split(".")[0].split("").reverse(),
          e = a.split(".")[1],
          f = "",
          g = 0;
        g < d.length;
        g++
      )
        f += d[g] + ((g + 1) % 3 == 0 && g + 1 != d.length ? "," : "");
      var re = f.split("").reverse().join("") + "." + e;
      if (re.substr(0, 2) == "-,") {
        return re.replace(",", "");
      } else {
        return re;
      }
    },
  },
};
</script>

<style>
</style>