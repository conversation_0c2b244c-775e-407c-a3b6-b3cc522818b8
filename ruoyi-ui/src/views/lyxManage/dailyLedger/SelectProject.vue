<template>
  <div>
    <el-dialog
      title="选择动态收入项"
      :visible.sync="dialogVisible"
      width="580px"
      :before-close="handleClose"
    >
      <el-select
        v-model="formData.value"
        ref="template"
        @change="change"
        @visible-change="(v) => visibleChange(v, 'template')"
        :style="{ width: '300px' }"
        filterable
        clearable
      >
        <el-option
          v-for="item in options"
          :key="item.id"
          :disabled="selectIds.includes(item.id)"
          :label="item.revenueItem"
          :value="item.id"
        >
          <span style="float: left" class="span-style">{{
            item.revenueItem
          }}</span>
          <span
            style="float: right"
            class="span-style-delete"
            @click.stop="deleteItem(item)"
            ><i class="el-icon-delete"
          /></span>
        </el-option>
      </el-select>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit" :disabled="!formData.value"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <AddProject
      v-if="addProjectType"
      @close="addProjectType = false"
      @submit="getQueryLyxDictData"
    />
  </div>
</template>

<script>
import AddProject from "./AddProject.vue";
import { queryLyxDictData, removeItemDict } from "@/api/lyxManage/dailyLedger";

export default {
  props: {
    incomeList: Array,
  },
  components: {
    AddProject,
  },
  data() {
    return {
      addProjectType: false,
      dialogVisible: true,
      options: [],
      formData: {
        value: "",
      },
      selectData: null,
      selectIds: [],
    };
  },
  mounted() {
    if (this.incomeList.length > 0) {
      console.log(this.incomeList);
      this.selectIds = this.incomeList.map((item) => item.dynamicIncomeId?item.dynamicIncomeId:item.id);
    }
    this.getQueryLyxDictData();
  },
  methods: {
    change(v) {
      console.log(v);
      this.selectData = this.options.find((item) => {
        return item.id == v;
      });
    },
    getQueryLyxDictData() {
      this.addProjectType = false;
      queryLyxDictData().then((res) => {
        this.options = res;
      });
    },
    handleClose() {
      this.$emit("close");
    },
    submit() {
      this.$emit("submit", this.selectData);
    },
    visibleChange(visible, refName) {
      if (visible) {
        const ref = this.$refs[refName];
        let product = ref.$refs.popper;
        if (product.$el) product = product.$el;
        if (
          !Array.from(product.children).some(
            (v) => v.className === "el-template-menu__list"
          )
        ) {
          const el = document.createElement("ul");
          el.className = "el-template-menu__list";
          el.style =
            "border-top:2px solid rgb(219 225 241); padding:0; color:rgb(64 158 255);font-size: 13px";
          el.innerHTML = `<li class="el-cascader-node text-center" style="height:37px;line-height: 50px;margin-left:10px;">
            <span class="el-cascader-node__label"><i class="font-blue el-icon-plus"></i> 创建一个新项目</span>
            </li>`;
          product.appendChild(el);
          // 新增按钮点击事件
          el.onclick = () => {
            this.addItem(null);
          };
        }
      }
    },
    editItem(item) {
      // 本地编辑
      this.addItem(item.value);
    },
    deleteItem(item) {
      console.log(item);
      this.$confirm(
        "是否确认删除该动态收入项？删除后将不能再选择该动态收入项，不会影响已经录入的收款记录",
        "删除",
        {
          type: "warning",
        }
      )
        .then(() => {
          removeItemDict(item.id).then((res) => {
            if (res.code == 200) {
              this.$message.success("删除成功");
              this.getQueryLyxDictData();
              if (item.id == this.formData.value) {
                this.formData.value = "";
                this.selectData = null;
              }
            }
          });
        })
        .catch(() => {});
    },
    // 添加产品
    addItem(val) {
      console.log(123);
      this.addProjectType = true;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-select .el-input__inner {
  height: 36px !important;
}
</style>