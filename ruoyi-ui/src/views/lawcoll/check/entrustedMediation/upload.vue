<template>
  <div class="app-container" style="text-align: center">
    <div v-if="showUpload">
      <div style="margin: 100px 0px 10px 0px; font-weight: bold">
        上传《委托调解业务对账单》Excel文件
      </div>
      <!-- 添加或修改法催对账历史对话框 -->
      <el-upload
        v-loading="loading"
        element-loading-text="查询数据中，请稍后..."
        ref="upload"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-change="fileChange"
        :on-remove="fileChange"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip" >
          <div style="text-align:left; display:inline-block">
            <ul>
              <li>上传前需先删除非数据工作表（例如汇总表），可支持包含多个数据工作表</li>
              <li>工作表中不得包含与表头无关的非数据内容，例如标题、数据求和、备注说明等</li>
              <li>表头需包含：借据号、资金方、回款金额、回款本金、回款日期、逾期天数、服务费费率、服务费</li>
            </ul>
          </div>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer" style="margin-top: 20px">
        <el-button type="primary" :disabled="disabled" @click="submitFileForm">上传</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>
    <div v-if="showResult">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            icon="el-icon-arrow-left"
            size="mini"
            @click="goBack"
          >返回上级</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            size="mini"
            @click="saveResult"
            v-hasPermi="['lawcoll:check:edit']"
          >保存结果</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            size="mini"
            @click="exportResult"
            v-hasPermi="['lawcoll:check:export']"
          >导出文件</el-button>
        </el-col>
      </el-row>
      <div style="text-align: left; margin-bottom: 10px">
        对账单共{{total}}条数据<br>
        {{total-differentTotal}}条完全一致，{{differentTotal}}条存在差异<br>
        <div v-if="missApplyNos != ''">
          另外，以下借据号在系统中的回款日期，在对账单中无对应数据：{{missApplyNos}}
        </div>
      </div>
      <el-tabs v-model="sheetSelected" type="card" @tab-click="tabClick">
        <el-tab-pane
          :key="sheet.sheetIndex"
          v-for="(sheet, index) in sheetList"
          :label="sheet.sheetName"
          :name="sheet.sheetIndex"
        >
          <el-table :data="sheet.dataList" :cell-class-name="setBackground">
            <el-table-column type="index" label="序号" align="center"/>
            <el-table-column label="借据号" align="center" prop="cApplyNo" width="190"/>
            <el-table-column label="回款金额" align="center">
              <el-table-column label="对账单" align="center" prop="cRepayTotalAmt" width="100"/>
              <el-table-column label="系统数据" align="center" prop="sRepayTotalAmt" width="100"/>
            </el-table-column>
            <el-table-column label="回款本金" align="center">
              <el-table-column label="对账单" align="center" prop="cRepayPrinAmt" width="100"/>
              <el-table-column label="系统数据" align="center" prop="sRepayPrinAmt" width="100"/>
            </el-table-column>
            <el-table-column label="回款日期" align="center">
              <el-table-column label="对账单" align="center" prop="cRepayDate" width="100"/>
              <el-table-column label="系统数据" align="center" prop="sRepayDate" width="100"/>
            </el-table-column>
            <el-table-column label="逾期天数" align="center">
              <el-table-column label="对账单" align="center" prop="cOvdDays" />
              <el-table-column label="系统数据" align="center" prop="sOvdDays" />
            </el-table-column>
            <el-table-column label="服务费费率" align="center">
              <el-table-column label="对账单" align="center" prop="cServiceRate" />
              <el-table-column label="系统数据" align="center" prop="sServiceRate" />
            </el-table-column>
            <el-table-column label="服务费" align="center">
              <el-table-column label="对账单" align="center" prop="cServiceAmt" width="100"/>
              <el-table-column label="系统数据" align="center" prop="sServiceAmt" width="100"/>
            </el-table-column>
            <el-table-column label="资金方" align="center">
              <el-table-column label="对账单" align="center" prop="cFundCode" >
                <template slot-scope="scope">
                  <span v-if="scope.row.cFundCode">{{scope.row.cFundCode == "FBBK"? "富邦银行":"北部湾银行"}}</span>
                </template>
              </el-table-column>
              <el-table-column label="系统数据" align="center" prop="sFundCode" >
                <template slot-scope="scope">
                  <span v-if="scope.row.sFundCode">{{scope.row.sFundCode == "FBBK"? "富邦银行":"北部湾银行"}}</span>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="代偿日期" align="center">
              <el-table-column label="对账单" align="center" prop="cCompensatoryDate" width="100"/>
              <el-table-column label="系统数据" align="center" prop="sCompensatoryDate" width="100"/>
            </el-table-column>

            <el-table-column label="分润金额" align="center" prop="fee3Amt" />
            <el-table-column label="姓名" align="center" prop="name" />
            <el-table-column label="结清状态" align="center" prop="status">
              <template slot-scope="scope">
                <span v-if="scope.row.status">{{scope.row.status == "FP"? "结清":"未结清"}}</span>
              </template>
            </el-table-column>
            <el-table-column label="回款途径" align="center" prop="repaySrc"></el-table-column>
            <el-table-column label="放款日期" align="center" prop="loanTime" width="100"/>
            <el-table-column label="代偿总金额" align="center" prop="compensatoryTotalAmt" />
            <el-table-column label="代偿本金" align="center" prop="compensatoryPrinAmt" />
            <el-table-column label="批次" align="center" prop="cBatchNo" width="110"/>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 保存核对结果 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body center>
      <span>
        保存此次核对结果，之后可以再次查看
      </span>
      <el-form ref="form" :model="form">
        <el-input v-model="form.importIdentify" hidden />
        <el-form-item prop="remark">
          <el-input type="textarea" rows="5" v-model="form.remark" maxlength="500" placeholder="输入备注说明（限500字）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancelSubmit">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {listCheck, getCheck, delCheck, addCheck, updateCheck, saveResult} from "@/api/lawcoll/check";
import {getToken} from "@/utils/auth";
import moment from "moment";
export default {
  name: "UploadLawCollData",
  data() {
    return {
      title: "保存核对结果",
      //遮罩层
      loading: false,
      open: false,
      showUpload: true,
      showResult: false,
      // 遮罩层
      disabled: true,
      // 用户导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/lawcoll/check/importEm"
      },
      // 比对结果表格数据
      sheetSelected: "sheet1",
      sheetList: [],
      missApplyNos: "",
      total: 0,
      differentTotal: 0,
      differentMap: new Map(),
      form: {}
    };
  },
  created() {
  },
  methods: {
    // 取消按钮
    cancel() {
      const obj = { name: "entrustedMediation" };
      this.$tab.closeOpenPage(obj);
    },
    fileChange(file, fileList){
      if(fileList.length > 0){
        if(fileList.length>1){
          fileList.splice(0,1);
        }
        this.disabled = false;
      }else{
        this.disabled = true;
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.disabled = true;
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.disabled = true;
      this.$refs.upload.clearFiles();
      if(response.msg=="success"){
        this.loading = true;
        this.sheetList = response.data;
        this.form.importIdentify = this.sheetList[0].importIdentify;
        this.tabClick();
        this.showResult = true;
        this.showUpload = false;
      }else{
        try {
          var res = JSON.parse(response.msg);
          if(res.errDesc == 'headError'){
            this.loading = false;
            this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + res.errMsg + "</div>", "对账单文件格式错误", {
              confirmButtonText: "关闭",
              dangerouslyUseHTMLString: true
            }).then(() => {
              this.cancel();
            }).catch(() => {});
          }else if(res.errDesc == 'applyNoRepeat'){
            this.loading = false;
            this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + res.errMsg + "</div>", "对账单数据存在重复项", {
              confirmButtonText: "关闭",
              dangerouslyUseHTMLString: true
            }).then(() => {
              this.cancel();
            }).catch(() => {});
          }
        }catch (e){
          this.loading = false;
          let errMsg = "读取委托业务对账单异常，导致读取失败的原因可能有：1、文件格式不正确 2、存在借据为空，请检查导入文件";
          this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+errMsg+"</div>", "提示", {
            confirmButtonText: "关闭",dangerouslyUseHTMLString: true
          }).then(() => {
            this.cancel();
          }).catch(() => {});
        }
      }
    },
    // 提交上传文件
    submitFileForm() {
      this.loading = true;
      this.$refs.upload.submit();
    },
    tabClick(){
      this.sheetList.filter((item, index, selfList)=>{
        if(this.sheetSelected == item.sheetIndex){
          this.missApplyNos = item.missApplyNos;
          this.total = item.dataList.length;
          this.differentTotal = item.differentTotal;
        }
      })
    },
    goBack(){
      this.cancel();
    },
    saveResult(){
       this.open = true;
    },
    cancelSubmit(){
      this.open = false;
      this.form.remark = "";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.importIdentify != null) {
            this.form.isSave = 0;
            saveResult(this.form).then(response => {
              this.$modal.msgSuccess("保存成功");
              this.open = false;
              this.cancel();
            });
          }
        }
      });
    },
    exportResult(){
      /** 导出按钮操作 */
      let queryParams = {
        importIdentify: this.form.importIdentify,
      }
      this.download('lawcoll/check/exportEm', {
        ...queryParams
      }, `委托调解业务_${moment(new Date()).format("YYYYMMDDHHmmss")}.xlsx`)
    },
    setBackground({ row, column, rowIndex, columnIndex }) {
      let index = [3,5,7,9,11,13,15,17,18]
      //回款金额比对
      if(columnIndex ==index[0] && !row.repayTotalAmtResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[0]){
        return "setSuccess";
      }
      //回款本金比对
      if(columnIndex ==index[1] && !row.repayPrinAmtResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[1]){
        return "setSuccess";
      }
      //回款日期比对
      if(columnIndex ==index[2] && !row.repayDateResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[2]){
        return "setSuccess";
      }
      //逾期天数比对
      if(columnIndex ==index[3] && !row.ovdDaysResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[3]){
        return "setSuccess";
      }
      //服务费费率比对
      if(columnIndex ==index[4] && !row.serviceRateResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[4]){
        return "setSuccess";
      }
      //服务费比对
      if(columnIndex ==index[5] && !row.serviceAmtResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[5]){
        return "setSuccess";
      }
      //资金方比对
      if(columnIndex ==index[6] && !row.fundCodeResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[6]){
        return "setSuccess";
      }
      //代偿日期比对
      if(columnIndex ==index[7] && !row.compensatoryDateResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[7]){
        return "setSuccess";
      }
      //分润比对
      if(columnIndex ==index[8] && !row.fee3AmtResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[8]){
        return "setNormal";
      }
    }
  }
};
</script>

<style lang='scss'>
.setNormal {
  background: none !important;
}
.setSuccess {
  background: #F3FEEF !important;
}
.setError {
  background: #FCCDCD !important;
}
</style>
