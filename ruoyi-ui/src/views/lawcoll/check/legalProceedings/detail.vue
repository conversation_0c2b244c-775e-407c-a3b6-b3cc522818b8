<template>
  <div class="app-container" style="text-align: center">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="el-icon-arrow-left"
          size="mini"
          @click="goBack"
        >返回上级</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          size="mini"
          @click="saveResult"
          v-hasPermi="['lawcoll:check:edit']"
        >保存结果</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          size="mini"
          @click="exportResult"
          v-hasPermi="['lawcoll:check:export']"
        >导出文件</el-button>
      </el-col>
    </el-row>
    <div style="text-align: left; margin-bottom: 10px">
      对账单共{{total}}条数据<br>
      {{total-differentTotal}}条完全一致，{{differentTotal}}条存在差异<br>
    </div>
    <el-tabs v-model="sheetSelected" type="card" @tab-click="tabClick">
      <el-tab-pane
        :key="sheet.sheetIndex"
        v-for="(sheet, index) in sheetList"
        :label="sheet.sheetName"
        :name="sheet.sheetIndex"
      >
        <el-table :data="sheet.dataList" :cell-class-name="setBackground">
          <el-table-column type="index" label="序号" align="center"/>
          <el-table-column label="借据编号" align="center" prop="cApplyNo" width="190"/>
          <el-table-column label="回款途径" align="center" prop="repaySrc" width="100"/>
          <el-table-column label="批次" align="center">
            <el-table-column label="对账单" align="center" prop="cBatchNo" width="150"/>
            <el-table-column label="系统" align="center" prop="sBatchNo" width="140"/>
          </el-table-column>
          <el-table-column label="委托逾期天数" align="center">
            <el-table-column label="对账单" align="center" prop="cOvdDays" />
            <el-table-column label="系统" align="center" prop="sOvdDays" />
          </el-table-column>
          <el-table-column label="回款日期" align="center">
            <el-table-column label="对账单" align="center" prop="cRepayDate" width="100"/>
            <el-table-column label="系统/回款数据" align="center" prop="sRepayDate" width="120"/>
          </el-table-column>
          <el-table-column label="真实回款金额（元）" align="center">
            <el-table-column label="对账单" align="center" prop="cActRepayTotalAmt" width="100"/>
            <el-table-column label="系统/回款数据" align="center" prop="sActRepayTotalAmt" width="120"/>
          </el-table-column>
          <el-table-column label="服务费比例" align="center">
            <el-table-column label="对账单" align="center" prop="cServiceRate"/>
            <el-table-column label="系统" align="center" prop="sServiceRate"/>
          </el-table-column>
          <el-table-column label="服务费金额" align="center">
            <el-table-column label="对账单" align="center" prop="cServiceAmt" width="100"/>
            <el-table-column label="系统" align="center" prop="sServiceAmt" width="100"/>
          </el-table-column>
          <el-table-column label="诉讼费" align="center">
            <el-table-column label="对账单" align="center" prop="cCostsAmt" width="100"/>
            <el-table-column label="回款数据" align="center" prop="sCostsAmt" width="100"/>
          </el-table-column>
          <el-table-column label="代偿日期" align="center">
            <el-table-column label="对账单" align="center" prop="cCompensatoryDate" width="100"/>
            <el-table-column label="系统" align="center" prop="sCompensatoryDate" width="100"/>
          </el-table-column>
          <el-table-column label="律师费" align="center" v-if="sheet.preFee!=='0'">
            <el-table-column label="对账单" align="center" prop="cLawyerFee" width="100"/>
            <el-table-column label="系统" align="center" prop="sLawyerFee" width="100"/>
          </el-table-column>

          <el-table-column label="姓名" align="center" prop="name" />
          <el-table-column label="放款日期" align="center" prop="loanTime" width="100"/>
          <el-table-column label="结清状态" align="center" prop="status">
            <template slot-scope="scope">
              <span v-if="scope.row.status">{{scope.row.status == "FP"? "结清":"未结清"}}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 保存核对结果 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body center>
      <span>
        保存此次核对结果，之后可以再次查看
      </span>
      <el-form ref="form" :model="form">
        <el-input v-model="form.importIdentify" hidden />
        <el-form-item prop="remark">
          <el-input type="textarea" rows="5" v-model="form.remark" maxlength="500" placeholder="输入备注说明（限500字）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancelSubmit">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {listCheck, getCheckLp, delCheck, addCheck, updateCheck, saveResult} from "@/api/lawcoll/check";
import moment from "moment";
export default {
  name: "legalProceedingsDetail",
  data() {
    return {
      title: "保存核对结果",
      open: false,
      // 遮罩层
      disabled: true,
      // 比对结果表格数据
      sheetSelected: "sheet1",
      sheetList: [],
      total: 0,
      differentTotal: 0,
      differentMap: new Map(),
      form: {
        importIdentify: "",
        remark: ""
      },
      preFee: 0
    };
  },
  created() {
    this.preFee = this.$route.params.preFee;
    this.form.importIdentify = this.$route.params.importIdentify;
    this.form.remark = this.$route.params.remark;
    this.getDetail();
  },
  methods: {
    tabClick(){
      this.sheetList.filter((item, index, selfList)=>{
        if(this.sheetSelected == item.sheetIndex){
          this.total = item.dataList.length;
          this.differentTotal = item.differentTotal;
        }
      })
    },
    /** 查询法催对账历史列表 */
    getDetail() {
      this.loading = true;
      getCheckLp(this.form.importIdentify).then(response => {
        this.sheetList = response.data;
        this.tabClick();
      });
    },
    goBack(){
      const obj = { name: "legalProceedings" };
      this.$tab.closeOpenPage(obj);
    },
    saveResult(){
       this.open = true;
    },
    cancelSubmit(){
      this.open = false;
      this.form.remark = "";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.importIdentify != null) {
            this.form.isSave = 0;
            saveResult(this.form).then(response => {
              this.$modal.msgSuccess("保存成功");
              this.open = false;
              const obj = { path: "/lawcoll/check/legalProceedings" };
              this.$tab.closeOpenPage(obj);
            });
          }
        }
      });
    },
    exportResult(){
      /** 导出按钮操作 */
      let queryParams = {
        importIdentify: this.form.importIdentify,
      }
      this.download('lawcoll/check/exportLp', {
        ...queryParams
      }, `法律诉讼业务_非安徽_${moment(new Date()).format("YYYYMMDDHHmmss")}.xlsx`)
    },
    setBackground({ row, column, rowIndex, columnIndex }) {
      let index = [4,6,8,10,12,14,16,18,19,20]
      //批次号比对
      if(columnIndex ==index[0] && !row.batchNoResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[0]){
        return "setSuccess";
      }
      //委托逾期天数比对
      if(columnIndex ==index[1] && !row.ovdDaysResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[1]){
        return "setSuccess";
      }
      //回款日期比对
      if(columnIndex ==index[2] && !row.repayDateResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[2]){
        return "setSuccess";
      }
      //逾期天数比对
      if(columnIndex ==index[3] && !row.actRepayTotalAmtResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[3]){
        return "setSuccess";
      }
      //回款金额比对
      if(columnIndex ==index[4] && !row.serviceRateResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[4]){
        return "setSuccess";
      }
      //服务费比对
      if(columnIndex ==index[5] && !row.serviceAmtResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[5]){
        return "setSuccess";
      }
      //诉讼费比对
      if(columnIndex ==index[6] && !row.costsAmtResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[6]){
        return "setSuccess";
      }
      //代偿日期比对
      if(columnIndex ==index[7] && !row.compensatoryDateResult){
        this.differentMap.set(rowIndex, 1)
        return "setError";
      }else if(columnIndex ==index[7]){
        return "setSuccess";
      }
      if(this.preFee==1){
        //诉讼费比对
        if(columnIndex ==index[8] && !row.cLawyerFeeResult){
          this.differentMap.set(rowIndex, 1)
          return "setError";
        }else if(columnIndex ==index[8]){
          return "setSuccess";
        }
        if(columnIndex ==index[9] && !row.sLawyerFeeResult){
          this.differentMap.set(rowIndex, 1)
          return "setError";
        }else if(columnIndex ==index[9]){
          return "setSuccess";
        }
      }
    }
  }
};
</script>

<style lang='scss'>
.setSuccess {
  background: #F3FEEF !important;
}
.setError {
  background: #FCCDCD !important;
}
</style>
