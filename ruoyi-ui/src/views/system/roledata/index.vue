<template>
  <div class="app-container" id="roleData">
    <el-form :model="queryParams" ref="queryForm" v-show="showSearch" :inline="true">
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="请输入角色名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="权限字符" prop="roleKey">
        <el-input
          v-model="queryParams.roleKey"
          placeholder="请输入权限字符"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="角色状态"
          clearable
          size="small"
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户昵称或名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户昵称或名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:roledata:add']"
        >新增</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:roledata:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roleList" >
      <el-table-column label="序号" type="index" align="center" width="120" />
      <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="权限字符" prop="roleKey" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <!-- <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="数据权限范围" align="center" prop="dataScope" :show-overflow-tooltip="true"  width="150">
        <template slot-scope="scope">
          <span v-for="dict in dataScopeOptions" :key="dict.value" >
            <span v-if="scope.row.dataScope == dict.value">{{dict.label}}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column   align="center" class-name="small-padding fixed-width" v-for="itemE in externalsystems" :key="itemE.dictValue" :label="itemE.dictLabel"     >
        <template slot-scope="scope">
          <span v-if="scope.row.dataScope==1">全部</span>
          <span v-else-if="scope.row.dataScope==2
          && scope.row.tableData.filter(item => (item!==null && item.platformNo===itemE.dictValue)).length==1
          && scope.row.tableData.filter(item => (item!==null && item.platformNo===itemE.dictValue))[0].custNo=='all'
          && scope.row.tableData.filter(item => (item!==null && item.platformNo===itemE.dictValue))[0].partnerNo=='all'
          && scope.row.tableData.filter(item => (item!==null && item.platformNo===itemE.dictValue))[0].fundNo=='all'">全部</span>
          <span v-else>{{scope.row.tableData.filter(item => (item!==null && item.platformNo===itemE.dictValue)).length}}</span>



        </template>
      </el-table-column>
      <el-table-column label="用户数量"  align="center" class-name="small-padding fixed-width" >
        <template slot-scope="scope">
          {{scope.row.userList.length}}
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" class-name="small-padding fixed-width" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['system:roledata:view']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:roledata:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:roledata:remove']"
          >删除</el-button>



        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1300px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row >
          <el-col :span="12">
            <el-form-item label="角色名称" prop="roleName">
              <el-input v-model="form.roleName" placeholder="请输入角色名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="roleKey">
              <span slot="label">
                <el-tooltip content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                权限字符
              </span>
              <el-input v-model="form.roleKey" placeholder="请输入权限字符" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <hr/>
        <el-row >
          <el-col :span="24">
            <span>已授权用户</span>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <el-table v-loading="loading" :data="form.userList" :row-class-name="rowClassName" >
              <el-table-column label="用户名称" prop="userName"  />
              <el-table-column label="用户昵称" prop="nickName"  />>
              <el-table-column label="状态" align="center" prop="status">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
                </template>
              </el-table-column>
              <el-table-column  label="公司">
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.unitShortName}} </br>
                  </template>
                </template>
              </el-table-column>

              <el-table-column label="部门"  >
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.deptName}} </br>
                  </template>
                </template>
              </el-table-column>
              <el-table-column label="岗位"  >
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.postName}} </br>
                  </template>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-circle-close"
                    @click="deleteRow(scope.row)"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <el-button type="primary" icon="el-icon-plus" @click="addPostUserInitBtn()">添加用户</el-button>
          </el-col>
        </el-row>
        <hr/>

        <el-form-item label="权限范围">
          <el-select v-model="form.dataScope">
            <el-option
              v-for="item in dataScopeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <div  v-show="form.dataScope == 2" style="height:10px;">
          <hr/>
        </div>

        <el-form v-show="form.dataScope == 2" ref="formRef" :model="form" label-width="120px" :inline="true" :rules="formRules" size="small" label-position="center">
          <el-tabs type="card" v-model="activeName">
              <el-tab-pane v-for="itemE in externalsystems" :key="itemE.dictValue"  :label="itemE.dictLabel+'('+(form.tableData || []).filter(itemT => itemT.platformNo===itemE.dictValue).length+')'" :name="itemE.dictValue">
                <div v-show="form.dataScope == 2" style="margin-top:10px;">
                    <el-button @click="onAdd(itemE.dictValue)" type="primary" plain>+ 新增</el-button>
                </div>

                <!-- (form.tableData || []).filter(itemT => itemT.platformNo===itemE.dictValue) -->
                  <el-table  v-loading="tableLoading" :data="form.tableData"  :row-class-name="rowClassNameData" border >



                    <el-table-column label="系统" :render-header="addRedStar" min-width="260" >
                        <!-- `tableForm.tableData.${scope.$index}.platformNo` -->
                      <template slot-scope="scope" v-if="scope.row.platformNo === itemE.dictValue">
                        <el-form-item :prop="'tableData.'+scope.$index+'.platformNo'" :rules="formRules.platformNo" >
                          <el-select v-model="scope.row.platformNo" filterable placeholder="请选择" collapse-tags clearable>
                          <el-option v-for="item in externalsystems" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"/>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>

                    <el-table-column label="担保公司" :render-header="addRedStar" min-width="260" >
                      <template slot-scope="scope">
                        <el-form-item :prop="'tableData.'+scope.$index+'.custNo'" :rules="formRules.custNo" >
                          <el-select v-model="scope.row.custNo" filterable placeholder="请选择" collapse-tags clearable>
                          <el-option v-for="item in dbcompany" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"/>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>

                    <el-table-column label="合作方" :render-header="addRedStar" min-width="260" >
                      <template slot-scope="scope">
                        <el-form-item :prop="'tableData.'+scope.$index+'.partnerNo'" :rules="formRules.partnerNo">
                          <el-select v-model="scope.row.partnerNo" filterable placeholder="请选择" collapse-tags clearable>
                          <el-option v-for="item in partnerdata" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"/>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>

                    <el-table-column label="资金方" :render-header="addRedStar" min-width="260" >
                      <template slot-scope="scope">
                        <el-form-item :prop="'tableData.'+scope.$index+'.fundNo'" :rules="formRules.fundNo">
                          <el-select v-model="scope.row.fundNo" filterable placeholder="请选择" collapse-tags clearable>
                          <el-option v-for="item in capitaldata" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"/>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>

                    <el-table-column fixed="right" label="操作" width="120px" >
                      <template slot-scope="scope">
                        <el-button  size="mini" icon="el-icon-delete" type="danger" @click="rowDelete(scope.row)">删除</el-button>
                      </template>
                    </el-table-column>

                  </el-table>

              </el-tab-pane>
          </el-tabs>
        </el-form>





      </el-form>

      <el-dialog
        width="30%"
        title="角色数据添加用户"
        :visible.sync="innerVisible"
        append-to-body>
        <el-row type="flex" justify="center">
          <el-col :span="24" >
            <el-select clearable v-model="innerFormUserIds" placeholder="请选择添加的用户" filterable multiple  >
              <el-option
                v-for="item in userListEnablePostEdit"
                :key="item.userId"
                :label="item.nickName +'（账号：'+item.userName+'）'"
                :value="item.userId">
              </el-option>
              <!-- +'；状态：'+(item.status==='0'?'正常':'禁用') -->
            </el-select>
          </el-col>
          </el-row>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="addPostUser()">确 定</el-button>
            <el-button @click="innerCancel">取 消</el-button>
          </div>
      </el-dialog>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>









    <!-- 查看角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row >
          <el-col :span="12">
            <el-form-item label="角色名称" prop="roleName">{{ form.roleName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限字符" prop="roleKey" >{{ form.roleKey }}</el-form-item>
          </el-col>
        </el-row>

        <el-row >
          <el-col :span="12">
            <el-form-item label="状态">
              <dict-tag :options="dict.type.sys_normal_disable" :value="form.status"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">{{ form.remark }}</el-form-item>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <span>已授权用户</span>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <el-table v-loading="loading" :data="form.userList" :row-class-name="rowClassName" >
              <el-table-column label="用户名称" prop="userName"  />
              <el-table-column label="用户昵称" prop="nickName"  />>
              <el-table-column label="状态" align="center" prop="status">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
                </template>
              </el-table-column>
              <el-table-column  label="公司">
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.unitShortName}} </br>
                  </template>
                </template>
              </el-table-column>

              <el-table-column label="部门"  >
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.deptName}} </br>
                  </template>
                </template>
              </el-table-column>
              <el-table-column label="岗位"  >
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.postName}} </br>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <span>数据权限范围（<span v-for="dict in dataScopeOptions" :key="dict.value" >
            <span v-if="form.dataScope == dict.value">{{dict.label}}</span>
          </span>）</span>
          </el-col>
        </el-row>
        <div  v-show="form.dataScope == 2" >
          <el-row   v-for="itemE in externalsystems" :key="itemE.dictValue"  >
            <!-- <el-col :span="4">
              <el-descriptions class="margin-top"    border>
                <el-descriptions-item :label="itemE.dictLabel+'('+(form.tableData || []).filter(itemT => itemT.platformNo===itemE.dictValue).length+')'" />

              </el-descriptions>
            </el-col> -->
            <el-col :span="24">

              <el-tabs type="card" >
                <el-tab-pane   :label="itemE.dictLabel+'('+(form.tableData || []).filter(itemT => itemT.platformNo===itemE.dictValue).length+')'" >

                  <el-table  :default-sort = "{prop: 'custNo', order: 'descending'}" :data="(form.tableData || []).filter(itemT => itemT.platformNo===itemE.dictValue)"  :row-class-name="rowClassName" border >
                      <el-table-column label="担保公司" prop="custNo"  sortable>
                        <template slot-scope="scope">
                          <dict-tag :options="dict.type.cust_no" :value="scope.row.custNo"/>
                          <span v-if="scope.row.custNo=='all'">全部</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="合作方" prop="partnerNo" sortable>
                        <template slot-scope="scope">
                          <dict-tag :options="dict.type.partner_no" :value="scope.row.partnerNo"/>
                          <span v-if="scope.row.partnerNo=='all'">全部</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="资金方" prop="fundNo" sortable>
                        <template slot-scope="scope">
                          <dict-tag :options="dict.type.fund_no" :value="scope.row.fundNo"/>
                          <span v-if="scope.row.fundNo=='all'">全部</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-tab-pane>
                </el-tabs>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<style>
  .el-table .data-display-row {
    display: none;
  }
</style>
<script>
import { listRole, getRole, delRole, addRole, updateRole, changeRoleStatus } from "@/api/system/roledata";
import { getDicts} from "@/api/system/dict/data";
import { getUserListAll,getUserListEnable } from "@/api/system/user";
import { optionselect,userPostSetList } from "@/api/system/post";

export default {
  name: "RoleData",
  dicts: ['sys_normal_disable','fund_no', 'partner_no', 'cust_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      viewOpen: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: "1",
          label: "全部数据权限"
        },
        {
          value: "2",
          label: "自定义数据权限"
        }
      ],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      //
      tableLoading:false,
       //外部系统
      externalsystems:[],
      //担保公司编码
      dbcompany:[],
      //合作方
      partnerdata:[],
      //资金方
      capitaldata:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
        roleKey: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      //添加用户临时参数
      innerFormUserIds: [],
      //添加用户嵌套对话框是否展示
      innerVisible:false,
      // 人员选项 所有（含删除禁用）
      userListAll: [],
      // 人员选项 所有正常用户
      userListEnable: [],
      // 人员选项 本岗位未选择用户
      userListEnablePostEdit: [],
      //查询用户岗位公司部门集合
      userPostListAll:[],
      activeName: 'XJ',


      //动态表单校验
      formRules:{

            platformNo:[{ required:true, message:'请选择系统', trigger:'change'}],
            custNo:[{ required:true, message:'请选择担保公司', trigger:'change'}],
            partnerNo:[{ required:true, message:'请选择合作方', trigger:'change'}],
            fundNo:[{ required:true, message:'请选择资金方', trigger:'change'}],

      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" }
        ],
        roleKey: [
          { required: true, message: "权限字符不能为空", trigger: "blur" }
        ],
        roleSort: [
          { required: true, message: "角色顺序不能为空", trigger: "blur" }
        ]
      },
      querydatatype:"cust_no",
      externalsystem:"platform_no",
      partnerscode:"partner_no",
      capitalcode:"fund_no",
    };
  },
  mounted() {
    this.getList();

     this.getexternalsystem();
    this.getdbcompany();
    this.getpartner();
    this.getcapital();
    this.getUserListEnableF();
    this.getUserListAllF();
    this.getPostListAllF();
    this.getUserPostSetListF();
  },
  methods: {

    /**查询所有岗位 */
    getPostListAllF() {
      optionselect().then(response => {
        this.postListAll = response.data;
      });
    },
    /**查询用户岗位公司部门集合 */
    getUserPostSetListF() {
      userPostSetList().then(response => {
        this.userPostListAll = response.data;
      });
    },
    /**查询所有正常用户 */
    getUserListEnableF() {
      getUserListEnable().then(response => {
        this.userListEnable = response.data;
      });
    },
    /** 查询所有用户 */
    getUserListAllF() {
      getUserListAll().then(response => {
        this.userListAll = response.data;
      });
    },
     //获取外部系统平台编码
    getexternalsystem(){
        getDicts(this.externalsystem).then(response =>{
            this.externalsystems = response.data;
        } );
    },
    //获取担保公司编码
    getdbcompany(){
        getDicts(this.querydatatype).then(response =>{
            this.dbcompany = response.data;
            this.dbcompany.unshift({dictValue:'all',dictLabel:'全部'})
        } );
    },
    //获取合作方编码
    getpartner(){
        getDicts(this.partnerscode).then(response =>{
            this.partnerdata = response.data;

            this.partnerdata.unshift({dictValue:'all',dictLabel:'全部'})
        } );
    },
    //获取资金方编码
    getcapital(){
        getDicts(this.capitalcode).then(response =>{
            this.capitaldata = response.data;
            this.capitaldata.unshift({dictValue:'all',dictLabel:'全部'})
        } );
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.roleList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },

    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.roleName + '"角色吗？').then(function() {
        return changeRoleStatus(row.roleId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.viewOpen = false;
      this.innerVisible=false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      // this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        roleId: undefined,
        roleName: undefined,
        roleKey: undefined,
        roleSort: 0,
        status: "0",
        menuIds: [],
        deptIds: [],
        menuCheckStrictly: true,
        deptCheckStrictly: true,
        remark: undefined,
        dataScope: "2",
        tableData:[
          {
            "platformNo":'XJ',
            "custNo":'',
            "partnerNo":'',
            "fundNo":'',
          }
          ],
          userList: []
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加角色";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids
      getRole(roleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改角色";

        this.activeName= 'XJ';

        this.userListEnablePostEdit=this.userListEnable;
        for(var itemUserE of this.form.userList){
          this.userListEnablePostEdit=this.userListEnablePostEdit.filter(item => item.userId !== itemUserE.userId);
        }
      });
    },
    //动态表单添加
    onAdd(platformNo){
      var item = {

            "platformNo":platformNo,
            "custNo":'',
            "partnerNo":'',
            "fundNo":''

      }
    this.form.tableData.push(item)
    },
    // 给表头加必选标识
    addRedStar(h,{ column }){
        return[h('span',{ style:'color: red'},'*'),h('span',' '+ column.label)]
    },
    rowDelete(row){
        this.$confirm('此操作将永久删除, 是否继续?','提⽰',{
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      }).then(()=>{
        this.form.tableData.splice(row.xh -1,1)
        this.$message.success('删除成功!')
        }).catch(()=>{
      })
    },
    // 其中row是⾏对象，rowindex是⾏号，从0开始。所以这样就能实现了序号(xh属性)递增并且取值为⾏号加1。
    rowClassNameData({ row, rowIndex}){
      row.xh = rowIndex +1;
      if(row.platformNo!==this.activeName){
        return 'data-display-row';
      }
    },

    /** 提交按钮 */
    submitForm: function() {
      if(this.form.dataScope == 1){
        this.form.tableData=[];
        this.$refs["form"].validate(valid => {
            if (valid) {
              if (this.form.roleId != undefined) {
                //this.form.menuIds = this.getMenuAllCheckedKeys();
                updateRole(this.form).then(response => {
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
                });
              } else {
                //this.form.menuIds = this.getMenuAllCheckedKeys();
                addRole(this.form).then(response => {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
                });
              }
            }
          });
      }else{
        this.$refs['formRef'].validate((validRdf)=>{
          if (validRdf) {
            this.$refs["form"].validate(valid => {
              if (valid) {
                if (this.form.roleId != undefined) {
                  //this.form.menuIds = this.getMenuAllCheckedKeys();
                  updateRole(this.form).then(response => {
                    this.$modal.msgSuccess("修改成功");
                    this.open = false;
                    this.getList();
                  });
                } else {
                  //this.form.menuIds = this.getMenuAllCheckedKeys();
                  addRole(this.form).then(response => {
                    this.$modal.msgSuccess("新增成功");
                    this.open = false;
                    this.getList();
                  });
                }
              }
            });
          }else{
            this.$message.error('有权限设置未填写完成')
          }
        });
      }
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId || this.ids;
      this.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？').then(function() {
        return delRole(roleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/roledata/export', {
        ...this.queryParams
      }, `role_${new Date().getTime()}.xlsx`)
    },
    // 其中row是⾏对象，rowindex是⾏号，从0开始。所以这样就能实现了序号(xh属性)递增并且取值为⾏号加1。
    rowClassName({ row, rowIndex }){
      row.xh = rowIndex +1
    },
    /** 删除行数据 */
    deleteRow(row) {
      this.form.userList.splice(row.xh -1,1);
      this.$modal.msgSuccess("点击确定后保存修改");
    },
    /** 新增行数据 */
    addPostUserInitBtn(){
      this.innerVisible = true;
      this.userListEnablePostEdit=this.userListEnable;
      for(var itemUserE of this.form.userList){
        this.userListEnablePostEdit=this.userListEnablePostEdit.filter(item => item.userId !== itemUserE.userId);
      }
    },
    addPostUser() {

      var item = {
              "userId":'',
              "userName":'',
              "nickName":'',
              "status":''
              };
      for(var itemId of this.innerFormUserIds){
        for(var itemUser of this.userListEnable){
          if(itemId===itemUser.userId){
            item = {
              "userId":itemUser.userId,
              "userName":itemUser.userName,
              "nickName":itemUser.nickName,
              "status":itemUser.status
              };
            this.form.userList.push(item);
          }
        }
      }
      this.innerVisible=false;
      this.innerFormUserIds=[];
      this.$modal.msgSuccess("点击确定后保存修改");
    },
    //取消按钮 选择用户
    innerCancel() {
      this.innerVisible=false;
      this.innerFormUserIds=[];
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const roleId = row.roleId || this.ids
      getRole(roleId).then(response => {
        this.form = response.data;
        this.title = "查看角色";
        this.viewOpen = true;

        this.activeName= 'XJ';

      });
    },
  }
};
</script>
<style>
.dialogDiv{
  height: 500px;
  overflow: auto;
}

</style>
