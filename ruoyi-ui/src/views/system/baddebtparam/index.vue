<template>
  <div class="app-container">
     <div style="font-size: 16px">
          <div   style="color: #999999"  >
            <span style="font-size: 13px;font-weight: bold">说明：</span><span style="font-size: 13px">本页面用于设置在【利润测算】功能中，计算坏账金额的参数：</span><br>
                <span style="font-size: 13px"> • 当选择自动计算，设置是否有追偿数据，若有，月坏账 = 月代偿 - 月追回</span><br>
                <span style="font-size: 13px"> • 当选择自动计算，若无追偿数据，需设置坏账率公式中的A值</span><br>
                <span style="font-size: 13px"> • 当选择手工计算，直接设置坏账率，月坏账 = 年化坏账率 ÷ 12 × 月初本金在贷余额</span><br>
          </div>
      </div>
      <div style="width: 100%;height:35px; ">

      </div>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="110px">
      <el-form-item label="系统名称" prop="platformNo">
                                <el-select v-model="platformNoParam" placeholder="请选择" filterable multiple size="small"
                                          >
                                  <el-option
                                    v-for="dict in platformNoSelect"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                      </el-form-item>
                      <el-form-item label="担保公司" prop="custNo">
                                <el-select v-model="custNoParam" placeholder="请选择" filterable multiple size="small"
                                          >
                                  <el-option
                                    v-for="dict in custNoSelect"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                      </el-form-item>
                      <el-form-item label="合作方" prop="partnerNo">
                              <el-select v-model="partnerNoParam" placeholder="请选择" filterable multiple size="small"
                                        >
                                <el-option
                                  v-for="dict in partnerNoSelect"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                      </el-form-item>
                    <el-form-item label="资金方" prop="fundNo">
                            <el-select v-model="fundNoParam" placeholder="请选择" filterable multiple size="small"
                                      >
                              <el-option
                                v-for="dict in fundNoSelect"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                              />
                            </el-select>
                    </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" style="margin-bottom: 8px">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:badParam:add']"
          >新增</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          v-hasPermi="['system:badParam:update']"-->
<!--          @click="handleUpdate"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="paramsList" @selection-change="handleSelectionChange" style="margin-top: 0">

      <el-table-column label="系统名称"  prop="" min-width="100px" >
        <template slot-scope="scope">
          <span v-for="dict in externalsystems" :key="dict.dictValue" >
            <span v-if="scope.row.platformNo == dict.dictValue">{{dict.dictLabel}}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="担保公司"  prop=""  min-width="100px" >
         <template slot-scope="scope">
          <span v-for="dict in dbcompany" :key="dict.dictValue" >
            <span v-if="scope.row.custNo == dict.dictValue">{{dict.dictLabel}}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="合作方"  prop=""  min-width="100px" >
         <template slot-scope="scope">
          <span v-for="dict in partnerdata" :key="dict.dictValue" >
            <span v-if="scope.row.partnerNo == dict.dictValue">{{dict.dictLabel}}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="资金方"  prop=""  min-width="230px" >
         <template slot-scope="scope">
          <span v-for="dict in capitaldata" :key="dict.dictValue" >
            <span v-if="scope.row.fundNo == dict.dictValue">{{dict.dictLabel}}</span>
          </span>
           <span v-if="scope.row.fundNo === ''">未定义</span>
        </template>
      </el-table-column>
      <el-table-column label="产品编码"  prop=""  min-width="230px" >
        <template slot-scope="scope">
          <span v-for="dict in productdata" :key="dict.dictValue" >
            <span v-if="scope.row.productNo == dict.dictValue">{{dict.dictLabel}}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="是否有追偿数据" align="center" prop="isRepay8Data" min-width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.isRepay8Data == 'Y'">是</span>
          <span v-if="scope.row.isRepay8Data== 'N' ">否</span>
          <span v-if="scope.row.isRepay8Data==null">未设置</span>

<!--          <div v-if="scope.row.badDebtList.length !== 0">-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == 'Y'">是</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data== 'N' ">否</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data==null">未设置</span>-->
<!--          </div>-->
        </template>
      </el-table-column>
      <el-table-column label="代偿规则"  prop="reduce7Rule" min-width="150px">
        <template slot-scope="scope">
            <span v-if="scope.row.isRepay8Data == 'Y'">-</span>
             <span v-if="scope.row.isRepay8Data == 'N' && scope.row.reduce7Rule != null">{{scope.row.reduce7Rule}}</span>
          <span v-if="scope.row.isRepay8Data == 'N' && scope.row.reduce7Rule == null">未设置</span>
          <span v-if="scope.row.isRepay8Data == null">未设置</span>

<!--          <div v-if="scope.row.badDebtList.length !== 0">-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == 'Y'">-</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == 'N' && scope.row.badDebtList[0].reduce7Rule != null">{{scope.row.badDebtList[0].reduce7Rule}}</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == 'N' && scope.row.badDebtList[0].reduce7Rule == null">未设置</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == null">未设置</span>-->
<!--          </div>-->
        </template>
      </el-table-column>
      <el-table-column label="A值参数" align="center" prop="aValueParam" min-width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.isRepay8Data == 'Y'">-</span>
          <span v-if="scope.row.isRepay8Data == 'N' && scope.row.aValueParam != null">{{scope.row.aValueParam}}</span>
          <span v-if="scope.row.isRepay8Data == 'N' && scope.row.aValueParam == null">-</span>
          <span v-if="scope.row.isRepay8Data == null">未设置</span>

<!--          <div v-if="scope.row.badDebtList.length !== 0">-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == 'Y'">-</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == 'N' && scope.row.badDebtList[0].aValueParam != null">{{scope.row.badDebtList[0].aValueParam}}</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == 'N' && scope.row.badDebtList[0].aValueParam == null">-</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == null">未设置</span>-->
<!--          </div>-->
        </template>
      </el-table-column>
      <el-table-column label="年化坏账率" align="center" prop="badDebtRate" min-width="150px">
        <template slot-scope="scope">
          <span v-if="scope.row.isRepay8Data == 'Y'">-</span>
           <span v-if="scope.row.isRepay8Data == 'N' && scope.row.badDebtRate != null">{{scope.row.badDebtRate}}</span>
          <span v-if="scope.row.isRepay8Data == 'N' && scope.row.badDebtRate == null">未设置</span>
          <span v-if="scope.row.isRepay8Data == null">未设置</span>

<!--          <div v-if="scope.row.badDebtList.length !== 0">-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == 'Y'">-</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == 'N' && scope.row.badDebtList[0].badDebtRate != null">{{scope.row.badDebtList[0].badDebtRate}}</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == 'N' && scope.row.badDebtList[0].badDebtRate == null">未设置</span>-->
<!--            <span v-if="scope.row.badDebtList[0].isRepay8Data == null">未设置</span>-->
<!--          </div>-->
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="100px">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-hasPermi="['system:badParam:update']"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            v-hasPermi="['system:badParam:delete']"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1017px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
       <el-row style="margin: 0 0 0 0">

          <el-col :span="12">
              <el-form-item label="系统名称" prop="platformNo">
                <el-select size="small" clearable  filterable v-model="form.platformNo" placeholder="请选择外部系统平台编码" >
                  <el-option
                    v-for="item in externalsystems"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue">
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
          <el-col :span="12">
          </el-col>
        </el-row>
        <el-row style="margin: 0 0 0 0">
          <el-col :span="12">
            <el-form-item label="担保公司" prop="custNo">
              <el-select size="small" clearable filterable v-model="form.custNo" placeholder="请选择外部系统平台编码">
                <el-option
                  v-for="item in dbcompany"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="合作方" prop="partnerNo">
                <el-select size="small" clearable filterable v-model="form.partnerNo" placeholder="请选择合作方编码">
                  <el-option
                    v-for="item in partnerdata"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue">
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin: 0 0 -22px 0">
          <el-col :span="12">
            <el-form-item label="资金方" prop="fundNo">
                <el-select size="small" clearable filterable v-model="form.fundNo" placeholder="请选择资金方编码">
                  <el-option
                    v-for="item in capitaldata"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue">
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品编码" prop="productNo">
                <el-select size="small" clearable filterable v-model="form.productNo" placeholder="请选择产品编码">
                  <el-option
                    v-for="item in productdata"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue">
                  </el-option>
                </el-select>
              </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <span style="font-size:14px;font-weight:bold;margin-left: 18px">坏账参数：</span><br>
        <span></span><br>
        <span class="spanfont">1.自动计算坏账规则</span><br>
        <span class="spanfont">若有追偿数据：月坏账 = 月代偿 - 月追回</span><br>
        <span></span><br>
        <span class="spanfont">若无追偿数据：月坏账 = 月坏账率 × 月初本金在贷余额（资方口径）</span><br>
        <span class="spanfont">月坏账率 = 月代偿率 × A</span><br>
        <span class="spanfont">月代偿率 = 每月代偿金额 ÷ 月初本金在贷余额</span><br>
        <span class="spanfont">若该业务3天内代偿（含3天），A = 25%</span><br>
        <span class="spanfont">若该业务15天内代偿（含15天），A = 40%</span><br>
        <span class="spanfont">若该业务30天内代偿（含30天），A = 55%</span><br>
        <span class="spanfont">若该业务45天内代偿（含45天），A = 70%</span><br>
        <span class="spanfont">若该业务60天内代偿（含60天），A = 80%</span><br>
        <span class="spanfont">若该业务60天以上代偿，A = 95%</span><br>
        <span></span><br>
        <span class="spanfont">2.手动计算坏账规则</span><br>
        <span class="spanfont">直接输入坏账率，月坏账 = 坏账率 × 月初本金在贷余额（资方口径）</span><br>
        <span></span><br>
        <span class="spanfont">使用自动计算还是手动计算，需在[数据报表-利润测算参数设置]中设置</span><br>
        <span></span><br>


        <div style="background: #fafafa;border: 1px solid #f2f2f2">
          <div v-for="(domain, index) in badDebtList">
            <div>
              <div style="display: inline-block;border: 1px solid #cccccc;width: 260px;height: 50px;text-align: center;font-weight: bold"><div style="margin-top: 14px">时间范围</div></div>
              <div style="display: inline-block;border: 1px solid #cccccc;width: 320px;height: 50px;text-align: center;font-weight: bold"><div style="margin-top: 14px">自动计算规则</div></div>
              <div style="display: inline-block;border: 1px solid #cccccc;width: 320px;height: 50px;text-align: center;font-weight: bold"><div style="margin-top: 14px">手动计算规则</div></div>
            </div>
            <div>

              <div style="display: inline-block;border: 1px solid #cccccc;width: 260px;height: 150px">
                <div style="margin-top: 16px">
                  <span style="color: red;margin-left: 16px">*</span>&nbsp;<span>生效时间</span>
                  <el-date-picker
                    v-model="domain.effectiveTime"
                    value-format="yyyy-MM-dd"
                    type="date"
                    size="mini"
                    style="width: 150px;margin-left: 8px;"
                    placeholder="请选择">
                  </el-date-picker>
                </div>
                <div style="margin-top: 20px">
                  <span style="color: red;margin-left: 16px">*</span>&nbsp;<span>失效时间</span>
                  <el-date-picker
                    v-model="domain.failureTime"
                    value-format="yyyy-MM-dd"
                    type="date"
                    size="mini"
                    style="width: 150px;margin-left: 8px"
                    placeholder="请选择">
                  </el-date-picker>
                </div>
                <br/>
              </div>

              <div style="display: inline-block;border: 1px solid #cccccc;width: 320px;height: 150px">
                <div v-if="domain.isRepay8Data == 'N'">
                  <div style="margin-top: 10px">
                    <span style="color: red;margin-left: 20px">*</span>&nbsp;<span>是否有追偿数据</span>
                    <el-select size="small" v-model="domain.isRepay8Data" placeholder="请选择" style="width: 170px;margin-left: 8px">
                      <el-option
                        v-for="item in isRepay8DataList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                  <div style="margin-top: 18px">
                    <span style="color: red;margin-left: 62px">*</span>&nbsp;<span>代偿规则</span>
                    <el-select size="small" v-model="domain.aValueParam" placeholder="请选择" style="width: 170px;margin-left: 8px">
                      <el-option
                        v-for="item in reduce7RuleList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                  <div v-if="domain.aValueParam == null || domain.aValueParam == ''" style="color: #fafafa;margin-left: 138px;height: 7px">请选择</div>
                  <div v-if="domain.aValueParam == 25" style="color: #999999;margin-left: 138px">A  = 25%</div>
                  <div v-if="domain.aValueParam == 40" style="color: #999999;margin-left: 138px">A  = 40%</div>
                  <div v-if="domain.aValueParam == 55" style="color: #999999;margin-left: 138px">A  = 55%</div>
                  <div v-if="domain.aValueParam == 70" style="color: #999999;margin-left: 138px">A  = 70%</div>
                  <div v-if="domain.aValueParam == 80" style="color: #999999;margin-left: 138px">A  = 80%</div>
                  <div v-if="domain.aValueParam == 95" style="color: #999999;margin-left: 138px">A  = 95%</div>
                </div>

                <div v-if="domain.isRepay8Data == 'Y' || domain.isRepay8Data == null || domain.isRepay8Data == ''">
                  <div style="margin-top: 10px">
                    <span style="color: red;margin-left: 20px">*</span>&nbsp;<span>是否有追偿数据</span>
                    <el-select size="small" clearable filterable v-model="domain.isRepay8Data" placeholder="请选择" style="width: 150px;margin-left: 8px">
                      <el-option
                        v-for="item in isRepay8DataList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                  <div style="margin-top: 31px">
                    <br/>
                  </div>
                  <br/>
                </div>

              </div>



              <div style="display: inline-block;border: 1px solid #cccccc;width: 320px;height: 150px">
                <div style="display: inline-block;margin-top: 11px">
<!--                  <span style="color: red;margin-left: 42px">*</span>&nbsp;-->
                  <span style="margin-left: 42px">年化坏账率</span>
                  <el-input v-model="domain.badDebtRate" size="small" clearable type="number" style="width:150px;margin-left: 8px" placeholder="请输入" />       %
                </div>
                <div style="margin-top: 6px;margin-left: 9px"><span style="font-size: 14px;color: #cccccc">月坏账 = 年化坏账率 ÷ 12 × 月初本金在贷余额</span></div>
                <div style="margin-top: 5px;margin-left: 9px"></div>
                <br/>
                <br/>
              </div>

              <el-button type="primary" size="mini" v-if="(index>0)" style="margin-left:8px" @click.prevent="removeDomain(domain)">- 删除</el-button>
            </div>

            <div style="height: 8px"></div>
          </div>

          <el-button type="primary" size="mini" @click="addDomain">+ 添加时间范围</el-button>
        </div>


<!--        <el-form-item label="是否有追偿数据：" prop="isRepay8Data" label-width="140px">-->
<!--          <el-select size="small" clearable filterable v-model="form.isRepay8Data" placeholder="请选择">-->
<!--                  <el-option-->
<!--                    v-for="item in isRepay8DataList"-->
<!--                    :key="item.value"-->
<!--                    :label="item.label"-->
<!--                    :value="item.value">-->
<!--                  </el-option>-->
<!--                </el-select>-->

<!--        </el-form-item>-->
<!--        <div style="display: inline-block;margin-right: 0" v-if="form.isRepay8Data=='N'"><span style="color: red">*</span></div><el-form-item style="display: inline-block" v-if="form.isRepay8Data=='N'" label="代偿规则：" prop="reduce7Rule" label-width="140px">-->
<!--          <el-select size="small" @change="seletChange" clearable v-model="form.aValueParam" placeholder="请选择">-->
<!--                  <el-option-->
<!--                    v-for="item in reduce7RuleList"-->
<!--                    :key="item.value"-->
<!--                    :label="item.label"-->
<!--                    :value="item.value">-->
<!--                  </el-option>-->
<!--                </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item v-if="form.isRepay8Data=='N'" label="A = " prop="aValueParam" label-width="130px">-->
<!--          <span v-if="form.aValueParam !=null">{{form.aValueParam}}%</span>-->
<!--          <span v-if="form.aValueParam ==null">-</span>-->
<!--        </el-form-item>-->

<!--        <el-divider></el-divider>-->
<!--        <span class="spanfont">为该业务手工设置坏账率。在【利润测算】功能中，如果选择手工计算，直接采用此坏账率</span><br>-->
<!--        <span></span><br>-->
<!--        <el-form-item label="年化坏账率" prop="badDebtRate" label-width="140px">-->
<!--          <el-input v-model="form.badDebtRate" size="small" clearable type="number" style="width:20%" placeholder="请输入坏账率" />       %<br/>-->
<!--          <span style="font-size: 14px;color: #cccccc">月坏账 = 年化坏账率 ÷ 12 × 月初本金在贷余额</span><br>-->
<!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listParams, getParams, delParams, addParams, updateParams, checkParams } from "@/api/system/baddebtparams";
//  start 引用联级字典查询
import {getSysDictRefList,getSelectSysDictRefList} from '@/api/ref/ref'
//  end 引用联级字典查询
import { getDicts} from "@/api/system/dict/data";
export default {
  name: "Baddebtparam",
  data() {
    return {
      badDebtList: [
        {
          //id   修改用
          id: null,
          badDebtId: null,
          effectiveTime: null,
          failureTime: null,
          //是否有追偿
          isRepay8Data: null,
          // isRepay8DataList:[
          //   {label:"是",value:'Y'},
          //   {label:"否",value:'N'}
          // ],
          //代偿规则
          aValueParam: null,
          // reduce7RuleList:[
          //   {
          //     label:'3天内代偿（含3天）',
          //     value:'25'
          //   },
          //   {
          //     label:'15天内代偿（含15天）',
          //     value:'40'
          //   },
          //   {
          //     label:'30天内代偿（含30天）',
          //     value:'55'
          //   },
          //   {
          //     label:'45天内代偿（含45天）',
          //     value:'70'
          //   },
          //   {
          //     label:'60天内代偿（含60天）',
          //     value:'80'
          //   },
          //   {
          //     label:'60天以上代偿',
          //     value:'95'
          //   }
          // ],
          //手动计算规则，年化坏账率
          badDebtRate: null,
        }
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      paramsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        productNo: null,
        isRepay8Data: null,
        reduce7Rule: null,
        aValueParam: null,
        badDebtRate: null,
        params:{
           moduleTypeOfNewAuth: 'DATAREPORT'
        }
      },
          //  start 新增参数
      platformNoParam: '',
      custNoParam: '',
      partnerNoParam: '',
      fundNoParam: '',
      productNoParam: '',


      sysDictRefParam: {
        dictType: '',
        dictValue: '',
        pDictType: '',
        pDictValue: '',
        selectDictDatas:''
      },
      platformNoSelect: [],
      custNoSelect: [],
      fundNoSelect: [],
      partnerNoSelect: [],
      productNoSelect: [],
      // end 新增参数

      // 表单参数
      form: {},
      // 表单校验
      rules: {

        platformNo: [
          { required: true, message: "系统编码不能为空", trigger: ['blur','change'] }
        ],
        custNo: [
          { required: true, message: "担保公司不能为空", trigger: ['blur','change'] }
        ],
        partnerNo: [
          { required: true, message: "合作方不能为空", trigger: ['blur','change'] }
        ],
        fundNo: [
          { required: true, message: "资金方不能为空", trigger: ['blur','change'] }
        ],
        productNo: [
          { required: true, message: "产品不能为空", trigger: ['blur','change'] }
        ],
        isRepay8Data: [
          { required: true, message: "是否有追偿不能为空", trigger: ['blur','change'] }
        ],
      },
       //外部系统
      externalsystems:[],
      //担保公司编码
      dbcompany:[],
      //合作方
      partnerdata:[],
      //资金方
      capitaldata:[],
      //产品方
      productdata:[],

          querydatatype:"cust_no",
      externalsystem:"platform_no",
      partnerscode:"partner_no",
      capitalcode:"fund_no",
      productcode:"product_no",
      //是否有追偿信息
      isRepay8DataList:[
        {label:"是",value:'Y'},
         {label:"否",value:'N'}
        ],
        //代偿规则
        reduce7RuleList:[
          {
            label:'3天内代偿（含3天）',
            value:'25'
          },
           {
            label:'15天内代偿（含15天）',
            value:'40'
          },
           {
            label:'30天内代偿（含30天）',
            value:'55'
          },
           {
            label:'45天内代偿（含45天）',
            value:'70'
          },
           {
            label:'60天内代偿（含60天）',
            value:'80'
          },
          {
            label:'60天以上代偿',
            value:'95'
          }
        ]
    };
  },
  created() {
    this.getexternalsystem();
    this.getdbcompany();
    this.getpartner();
    this.getcapital();
    this.getproduct();
    this.initSelectData();
    this.getList();

  },
  methods: {


     //wzy渲染下拉框
     initSelectData() {
      getSelectSysDictRefList({ unitType: 4, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.partnerNoSelect = response;
      });
    },
     getCustNoList(val) {
      const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
      this.queryParams.platformNo = this.platformNoParam.toString()
      if (val == null || val === '' || flag) {
        this.custNoSelect = null
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.custNo = null
        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.custNoParam = null
        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {

            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getPartnerNoList(val) {
      const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
      this.queryParams.custNo = this.custNoParam.toString()
      if (val == null || val === '' || flag) {
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;

            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getFundNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())
      this.queryParams.partnerNo = this.partnerNoParam.toString()
      if (val == null || val === ''|| flag) {
        this.fundNoSelect = null
        this.productNoSelect = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
         this.sysDictRefParam.selectDictDatas =""

      if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
      else {
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
        this.sysDictRefParam.selectDictDatas =""
         if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getProductNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

      this.queryParams.fundNo = this.fundNoParam.toString()
      if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo
this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo

this.sysDictRefParam.selectDictDatas =""
        if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
      getProductNoValue(val) {
      const flag = this.lateByte(this.queryParams.productNo) > this.lateByte(val.toString())

      this.queryParams.productNo = this.productNoParam.toString()

       if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        this.productNoParam = null
        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue =''
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue = this.queryParams.productNo

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }

    },
     lateByte(sTargetStr) {
      var sTmpStr, sTmpChar;
      var nOriginLen = 0;
      var nStrLength = 0;

      sTmpStr = new String(sTargetStr);
      nOriginLen = sTmpStr.length;

      for (var i = 0; i < nOriginLen; i++) {
        sTmpChar = sTmpStr.charAt(i);

        if (escape(sTmpChar).length > 4) {
          nStrLength += 2;
        } else if (sTmpChar != '/r') {
          nStrLength++;
        }
      }
      return nStrLength;
    },
    //end



    seletChange(val) {
     //选中的数据和options进行匹配
      var obj={}
      obj = this.reduce7RuleList.find(function(i){
        return i.value ===val
      });
      this.form.reduce7Rule = obj.label
    },

    //获取外部系统平台编码
    getexternalsystem(){
        getDicts(this.externalsystem).then(response =>{
            this.externalsystems = response.data;
        } );
    },
    //获取担保公司编码
    getdbcompany(){
        getDicts(this.querydatatype).then(response =>{
            this.dbcompany = response.data;
        } );
    },
    //获取合作方编码
    getpartner(){
        getDicts(this.partnerscode).then(response =>{
            this.partnerdata = response.data;
        } );
    },
    //获取资金方编码
    getcapital(){
        getDicts(this.capitalcode).then(response =>{
          for (let i = 0; i < response.data.length; i++) {
            if (response.data[i].dictLabel === '未定义' && response.data[i].dictValue === '') {
              response.data[i].dictValue = 'ZIJINFANGNOTDEFINITION'
              break;
            }
          }
            this.capitaldata = response.data;
        } );
    },
    //获取产品编码
    getproduct(){
        getDicts(this.productcode).then(response =>{
            this.productdata = response.data;
        } );
    },
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      this.queryParams.fundNo = this.fundNoParam.toString();
      this.queryParams.productNo = this.productNoParam.toString();
      listParams(this.queryParams).then(response => {
        this.paramsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        productNo: null,
        isRepay8Data: null,
        reduce7Rule: null,
        aValueParam: null,
        badDebtRate: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.badDebtList = [
        {
          //id   修改用
          id: null,
          badDebtId: null,
          effectiveTime: null,
          failureTime: null,
          //是否有追偿
          isRepay8Data: null,
          //代偿规则
          aValueParam: null,
          //手动计算规则，年化坏账率
          badDebtRate: null,
        }
      ];
      this.open = true;
      this.title = "添加利润测算参数设置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getParams(id).then(response => {
        this.form = response.data;
        this.badDebtList = response.data.badDebtList;
        this.open = true;
        this.title = "修改利润测算坏账参数";
      });
    },
    /** 提交按钮 */
    submitForm() {
      var effectiveTimeFlag = 0;
      var failureTimeFlag = 0;
      var timeError = 0;
      var isRepay8DataFlag = 0;
      var aValueParamFlag = 0;
      /* var badDebtRateFlag = 0; */
      /* var badDebtRateErrorFlag = 0; */
      this.$refs["form"].validate(valid => {
        if (valid) {
          for (let i = 0; i < this.badDebtList.length; i++) {
            //判断时间是否正确
            if (this.badDebtList[i].effectiveTime == null || this.badDebtList[i].effectiveTime == '') {
              effectiveTimeFlag++;
            } else if (this.badDebtList[i].failureTime == null || this.badDebtList[i].failureTime=='') {
              failureTimeFlag++;
            } else if (this.badDebtList[i].effectiveTime != null  && this.badDebtList[i].effectiveTime != '' && this.badDebtList[i].failureTime != null && this.badDebtList[i].failureTime != '') {
              // var effectiveTimeDate = Date.parse(this.badDebtList[i].effectiveTime.replace(/-/g,"/"));
              // var failureTimeDate = Date.parse(this.badDebtList[i].failureTime.replace(/-/g,"/"));
              // console.log('aa', effectiveTimeDate);
              // console.log('bb', failureTimeDate);
              if (this.badDebtList[i].effectiveTime > this.badDebtList[i].failureTime) {
                timeError++;
              }
            }
            //判断是否追偿是否必选
            if (this.badDebtList[i].isRepay8Data == null || this.badDebtList[i].isRepay8Data == '') {
              isRepay8DataFlag++;
            }
            if (this.badDebtList[i].isRepay8Data == 'N') {
              //判断代偿规则是否必选
              if (this.badDebtList[i].aValueParam == null || this.badDebtList[i].aValueParam == '') {
                aValueParamFlag++;
              }
            }
            /* //判断年化坏账率是否必填
            if (this.badDebtList[i].badDebtRate == null || this.badDebtList[i].badDebtRate == '') {
              badDebtRateFlag++;
            } */
            /* if (Number(this.badDebtList[i].badDebtRate) > 100 || Number(this.badDebtList[i].badDebtRate) <= 0) {
              badDebtRateErrorFlag++;
            } */
          }


          if (effectiveTimeFlag != 0) {
            this.$modal.msgError("请检查生效时间，生效时间不能为空");
          } else if (failureTimeFlag != 0) {
            this.$modal.msgError("请检查失效时间，失效时间不能为空");
          } else if (timeError != 0) {
            this.$modal.msgError("生效时间不能大于失效时间");
          } else if (isRepay8DataFlag != 0) {
            this.$modal.msgError("请检查是否有追偿数据，该选项不能为空");
          } else if (aValueParamFlag != 0) {
            this.$modal.msgError("请检查代偿规则，代偿规则不能为空");
          }
          /* else if (badDebtRateFlag != 0) {
            this.$modal.msgError("请检查年化坏账率，年化坏账率不能为空");
          } else if (badDebtRateErrorFlag != 0) {
            this.$modal.msgError("请检查年化坏账率填写范围，范围在0~100之间");
          }  */
          else {
            //组装坏账集合
            this.form.badDebtList = this.badDebtList;
            console.log(this.form);
            if (this.form.id != null) {
              updateParams(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              checkParams(this.form).then(checkFlag => {
                if (checkFlag) {
                  addParams(this.form).then(response => {
                    this.$modal.msgSuccess("新增成功");
                    this.open = false;
                    this.getList();
                  });
                } else {
                  this.$modal.msgError("输入新增的数据已存在，不允许重复添加");
                }
              })
            }

          }

        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除【请填写功能名称】编号为"' + ids + '"的数据项？').then(function() {
        return delParams(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/params/export', {
        ...this.queryParams
      }, `params_${new Date().getTime()}.xlsx`)
    },
    addDomain() {
      this.badDebtList.push(
        {
          effectiveTime: null,
          failureTime: null,
          //是否有追偿
          isRepay8Data: null,
          isRepay8DataList:[
            {label:"是",value:'Y'},
            {label:"否",value:'N'}
          ],
          //代偿规则
          aValueParam: null,
          reduce7RuleList:[
            {
              label:'3天内代偿（含3天）',
              value:'25'
            },
            {
              label:'15天内代偿（含15天）',
              value:'40'
            },
            {
              label:'30天内代偿（含30天）',
              value:'55'
            },
            {
              label:'45天内代偿（含45天）',
              value:'70'
            },
            {
              label:'60天内代偿（含60天）',
              value:'80'
            },
            {
              label:'60天以上代偿',
              value:'95'
            }
          ],
          //手动计算规则
          badDebtRate: null,
        }
      );
    },
    removeDomain(item) {
      var index = this.badDebtList.indexOf(item)
      if (index !== -1) {
        this.badDebtList.splice(index, 1);
      }
      // this.checkRepeat(index);
    },
  }
};
</script>
<style>
.spanfont{
      font-size:14px;
    color:#cccccc;
    margin-left: 18px;
}
</style>
