<template>
  <div>
    <el-dialog
      :title="
        itemDetail && !seeType
          ? '修改自定义档案'
          : itemDetail && seeType
          ? '查看自定义档案'
          : '新增自定义档案'
      "
      :visible.sync="dialogVisible"
      width="750px"
      :before-close="handleClose"
    >
      <div class="flex">
        <div class="item">
          <span>档案编号</span>
          <el-input
            type="text"
            disabled
            v-model="params.recordCode"
            placeholder="保存后自动生成"
            style="width: 220px"
          ></el-input>
        </div>
        <div class="item">
          <span>档案助记码</span>
          <el-input
            type="text"
            :disabled="seeType"
            v-model="params.mnemonicCode"
            placeholder="请输入档案助记码"
            style="width: 220px"
          ></el-input>
        </div>
      </div>
      <div class="flex">
        <div class="item">
          <span><i>*</i>档案名称</span>
          <el-input
            type="text"
            :disabled="seeType"
            v-model="params.recordName"
            placeholder="请输入档案名称"
            style="width: 220px"
          ></el-input>
        </div>
        <div class="item" style="margin-right: 92px">
          <span>状态</span>
          <el-radio :disabled="seeType" v-model="params.state" label="0"
            >正常</el-radio
          >
          <el-radio :disabled="seeType" v-model="params.state" label="1"
            >停用</el-radio
          >
        </div>
      </div>
      <div class="flex">
        <div class="item">
          <span><i>*</i>所属公司</span>
          <el-select
            placeholder="请选择所属公司"
            clearable=""
            style="width: 200px"
            :disabled="seeType"
            v-model="params.unitId"
          >
            <el-option
              v-for="item in projects"
              :key="item.unitId"
              :label="item.unitShortName"
              :value="item.unitId"
            >
            </el-option>
          </el-select>
        </div>
        <div class="item">
          <span><i>*</i>所属部门</span>
          <el-select
            :disabled="seeType"
            v-model="params.deptId"
            clearable
            style="width: 220px"
            placeholder="请选择所属部门"
            ref="selectUpResId"
          >
            <el-option hidden :value="params.deptId" :label="params.deptName">
            </el-option>

            <el-tree
              :data="deTreeList"
              :props="defaultProps2"
              :expand-on-click-node="false"
              :check-on-click-node="true"
              @node-click="dehandleNodeClick"
            >
            </el-tree>
          </el-select>
        </div>
      </div>
      <div class="flex">
        <div class="item" style="display: flex">
          <span style="flex-shrink: 0"><i>*</i>档案值</span>
          <el-input
            type="textarea"
            :disabled="seeType"
            :rows="2"
            style="width: 590px"
            placeholder="请输入档案值"
            v-model="params.recordContent"
          >
          </el-input>
        </div>
      </div>
      <div class="flex">
        <div class="item" style="display: flex">
          <span style="flex-shrink: 0">备注</span>
          <el-input
            type="textarea"
            :disabled="seeType"
            :rows="2"
            style="width: 590px"
            placeholder="请输入内容"
            v-model="params.remark"
          >
          </el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button  @click="handleClose">取 消</el-button>
        <el-button v-if="!seeType" type="primary" @click="addItem"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
  
  <script>
import { treeselect } from "@/api/system/dept";
import { allCompanyList } from "@/api/oa/processTemplate";
import { selectCompanyInfo } from "@/api/directoryArchives/directoryArchives";
import { recordAdd, recordEdit,getUnit } from "@/api/oa/processConfig";

export default {
  props: {
    seeType: Boolean,
    itemDetail: Object,
  },
  data() {
    return {
      defaultProps: {
        children: "fpiattaformas",
        label: "catalogueName",
      },
      defaultProps2: {
        children: "children",
        label: "label",
      },
      params: {
        recordCode: "",
        recordName: "",
        state: "0",
        mnemonicCode: "",
        unitId: "",
        deptId: "",
        deptName: "",
        recordContent: "",
        remark: "",
      },
      textarea: "",
      dialogVisible: true,
      projects: [],
      deTreeList: [],
    };
  },
  watch: {},
  created() {
    this.getTree();
  },
  mounted() {
    if (this.itemDetail) {
      this.params = Object.assign(this.params, this.itemDetail);
    }
  },
  methods: {
    getTree() {
      getUnit().then((response) => {
        this.projects = response.data;
      });
      treeselect().then((res) => {
        this.deTreeList = res.data;
        this.deTreeList.forEach(item=>{
          item.isOne = true
        })
      });
    },

    addItem() {
      if (!this.params.recordName) {
        this.$message.warning("请输入档案名称");
        return;
      }
      if (!this.params.unitId) {
        this.$message.warning("请选择公司");
        return;
      }
      if (!this.params.deptId) {
        this.$message.warning("请选择所属部门");
        return;
      }
      if (!this.params.recordContent) {
        this.$message.warning("请输入档案值");
        return;
      }
      if (this.itemDetail) {
        recordEdit(this.params).then((res) => {
          if (res.code == 200) {
            this.$message.success("操作成功");
            this.$emit("success");
          }
        });
      } else {
        recordAdd(this.params).then((res) => {
          if (res.code == 200) {
            this.$message.success("操作成功");
            this.$emit("success");
          }
        });
      }
    },
    handleNodeClick(data) {
      console.log(data);
     
      this.params.parentCatalogueName = data.catalogueName;
      this.params.parentId = data.id;
      this.$refs.selectUpResId1.blur();
    },
    dehandleNodeClick(data) {
      console.log(data);
      if(data.isOne){
        return
      }
      this.params.deptName = data.label;
      this.params.deptId = data.id;
      selectCompanyInfo(this.params.deptId).then((res) => {
        this.params.unitId = res.data.unitId;
      });
      this.$refs.selectUpResId.blur();
    },
    // 选择器配置可以清空选项，用户点击清空按钮时触发
    handleClear() {
      // 将选择器的值置空
      this.upResName = "";
      this.saveForm.upResId = "";
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>
  
  <style lang="less" scoped>
.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  span {
    margin-right: 9px;
    display: inline-block;
    width: 100px;
    text-align: right;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
</style>