<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item label="外部系统平台编码" prop="platformNo">
        <el-select v-model="queryParams.platformNo" filterable placeholder="请选择系统名称" clearable size="small">
          <el-option
            v-for="dict in dict.type.platform_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

       <el-form-item label="字典类型" prop="dictType">
        <el-select v-model="queryParams.dictType" filterable placeholder="请选择字典类型" clearable size="small" @change="getSelectDictValue()" >
          <el-option
            v-for="item in dictTypes"
              :key="item.dictType"
              :label="item.dictName"
              :value="item.dictType"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="字典键值" prop="dictValue">
         <el-select v-model="queryParams.dictValue" filterable placeholder="请选择字典键值" clearable size="small">
            <el-option
              v-for="item in dictKeyValues"
              :key="item.dictddmId"
              :label="item.dictLabel"
              :value="item.dictValue">
            </el-option>
          </el-select>
      </el-form-item>
      <el-form-item label="字典键值映射" prop="dictValueMapping">
        <el-input
          v-model="queryParams.dictValueMapping"
          placeholder="请输入字典键值映射"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="数据状态" clearable size="small">
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
       <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:mapping:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:mapping:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:mapping:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
            <el-button
              type="info"
              plain
              icon="el-icon-upload2"
              size="mini"
              @click="handleImport"
              v-hasPermi="['system:mapping:import']"
            >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:mapping:export']"
        >导出</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleRefreshCache"
          v-hasPermi="['system:mapping:remove']"
        >刷新缓存</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"  :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="mappingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="外部系统平台编码" align="center" prop=""  v-if="columns[0].visible">
         <template slot-scope="scope">
          <span v-for="dict in externalsystems" :key="dict.dictValue" >
            <span v-if="scope.row.platformNo == dict.dictValue">{{dict.dictLabel}}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="字典类型" align="center" prop="" v-if="columns[1].visible" >
         <template slot-scope="scope">
          <span v-for="dict in dictTypes" :key="dict.dictType" >
            <span v-if="scope.row.dictType == dict.dictType">{{dict.dictName}}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="字典键值" align="center" prop="" v-if="columns[2].visible" >
         <template slot-scope="scope">
          <span v-for="dict in alldictdatas" :key=" dict.dictddmId" >
            <span v-if="scope.row.dictValue === dict.dictValue && scope.row.dictType === dict.dictType">{{dict.dictLabel}}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="字典键值映射" align="center" prop="dictValueMapping" v-if="columns[3].visible"/>
      <el-table-column label="备注" align="center" prop="remark" v-if="columns[4].visible"/>
      <el-table-column label="创建人" align="center" prop="createBr" v-if="columns[5].visible"/>
       <el-table-column label="状态" align="center" prop="status" v-if="columns[6].visible">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[7].visible" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:mapping:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:mapping:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改字典数据映射对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="外部系统平台编码" prop="platformNo">
          <el-select v-model="form.platformNo" placeholder="请选择外部系统平台编码">
            <el-option
              v-for="item in externalsystems"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue">
            </el-option>
          </el-select>
          <!-- <el-input v-model="form.platformNo" placeholder="请输入外部系统平台编码" /> -->
        </el-form-item>

        <el-form-item label="字典类型" prop="dictType">
          <el-select  v-model="form.dictType" placeholder="请选择字典类型" @change="getdictvalue()">
            <el-option
              v-for="item in dictTypes"
              :key="item.dictType"
              :label="item.dictName"
              :value="item.dictType">
            </el-option>
          </el-select>
          <!-- <el-input v-model="form.dictType" placeholder="请输入" /> -->
        </el-form-item>
        <el-form-item label="字典键值" prop="dictValue">
          <el-select v-model="form.dictValue" placeholder="请选择字典键值">
            <el-option
              v-for="item in dictKeyValues"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue">
            </el-option>
          </el-select>
          <!-- <el-input v-model="form.dictValue" placeholder="请输入字典键值" /> -->
        </el-form-item>

        <el-form-item label="字典键值映射" prop="dictValueMapping">
          <el-input v-model="form.dictValueMapping" placeholder="请输入字典键值映射" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>



    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的字典映射数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMapping, getMapping, delMapping, addMapping, updateMapping ,refreshCache,listnopageMapping} from "@/api/system/mapping";
import { optionselect} from "@/api/system/dict/type";
import { getDicts,getAllDicts} from "@/api/system/dict/data";
import { getToken } from "@/utils/auth";

export default {
  name: "Compy",
  dicts: ['sys_normal_disable','platform_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 字典数据映射表格数据
      mappingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //字典类型下拉框
      dictTypes:[],
      //字典键值下拉框
      dictKeyValues:[],
      // //所有的字典类型数据
      alldictdatas:[],
      //外部系统
      externalsystems:[],
      columns: [
        { key: 0, label: `外部系统平台编码`, visible: true },
        { key: 1, label: `字典类型`, visible: true },
        { key: 2, label: `字典键值`, visible: true },
        { key: 3, label: `字典键值映射`, visible: true },
        { key: 4, label: `备注`, visible: true },
        { key: 5, label: `创建人`, visible: true },
        { key: 6, label: `状态`, visible: true },
        { key: 7, label: `创建时间`, visible: true },
        { key: 8, label: `数据状态`, visible: true }
      ],

       // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/mapping/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platformNo: null,
        dictType: null,
        dictValue: null,
        dictValueMapping: null,
        status: null,

      },
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        platformNo: [
          { required: true, message: "外部系统平台编码不能为空", trigger: "blur" }
        ],
        dictType: [
          { required: true, message: "字典类型不能为空", trigger: "change" }
        ],
        dictValue: [
          { required: true, message: "字典键值不能为空", trigger: "blur" }
        ],
        dictValueMapping: [
          { required: true, message: "字典键值映射不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ]
      },
      externalsystem:"platform_no",

    };
  },
  created() {
    this.getList();
    this.getdictType();
    // this.getdictvalue();
    this.getexternalsystem();
    this.getalldictvalue();

  },
  methods: {
    //获取字典键值
    getdictvalue(){
        getDicts(this.form.dictType).then(response =>{
            this.dictKeyValues = response.data;
        } );
    },
     //获取字典键值
    getSelectDictValue(){
      this.queryParams.dictValue = null;
        listnopageMapping(this.queryParams).then(response =>{
            this.dictKeyValues = response.data;
        } );
    },
    //获取所有字典键值数据
    getalldictvalue(){
        getAllDicts().then(response=>{
          this.alldictdatas =response.data;
          this.dictKeyValues=response.data;
        });
    },
    //获取外部系统平台编码
    getexternalsystem(){
        getDicts(this.externalsystem).then(response =>{
            this.externalsystems = response.data;
        } );
    },
    //获取字典类型
    getdictType(){
       optionselect().then(response => {
         this.dictTypes= response.data;
        }
      );
    },
    /** 查询字典数据映射列表 */
    getList() {
      this.loading = true;
      listMapping(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.mappingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        dictddmId: null,
        platformNo: null,
        dictType: null,
        dictValue: null,
        dictValueMapping: null,
        remark: null,
        status: "0",
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.dictddmId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {

      this.reset();
      this.open = true;
      this.getdictType();
      // this.getdictvalue();
      this.getexternalsystem();
      this.title = "添加字典数据映射";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
          this.getdictType();
       getDicts(row.dictType).then(response =>{
            this.dictKeyValues = response.data;
        } );
      this.getexternalsystem();
      const dictddmId = row.dictddmId || this.ids
      getMapping(dictddmId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改字典数据映射";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.dictddmId != null) {
            updateMapping(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMapping(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const dictddmIds = row.dictddmId || this.ids;
      this.$modal.confirm('是否确认删除这条字典数据映射的数据项？').then(function() {
        return delMapping(dictddmIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/mapping/export', {
        ...this.queryParams
      }, `mapping_${new Date().getTime()}.xlsx`)
    },

     /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/mapping/importTemplate', {
      }, `SysDictDataMapping_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
     /** 刷新缓存按钮操作 */
    handleRefreshCache() {
      refreshCache().then(() => {
        this.$modal.msgSuccess("刷新成功");
      });
    }
  }
};
</script>
