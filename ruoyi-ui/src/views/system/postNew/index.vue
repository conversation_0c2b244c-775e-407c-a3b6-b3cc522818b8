<template>
  <div class="app-container">
    <div v-show="!detailType">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="岗位名称" prop="postName">
          <el-input
            v-model.trim="queryParams.postName"
            placeholder="请输入岗位名称"
            clearable
            size="small"
            @clear="handleQuery"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属部门" prop="deptName">
          <el-input
            v-model.trim="queryParams.deptName"
            placeholder="请输入所属部门"
            clearable
            size="small"
            @clear="handleQuery"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="岗位类型" prop="postType">
          <el-select
            v-model="queryParams.postType"
            placeholder="岗位类型"
            clearable
            @change="handleQuery"
            size="small"
          >
            <el-option
              v-for="item in sysPostTypeList"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="岗位状态"
            clearable
            @change="handleQuery"
            size="small"
          >
            <el-option
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
          <el-form-item label="用户昵称或名称" prop="userName" label-width="128px">
          <el-input
            v-model.trim="queryParams.userName"
            placeholder="请输入用户昵称或名称"
            clearable
            size="small"
            @clear="handleQuery"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:postNew:add']"
            >新增</el-button
          >
        </el-col>
        <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:post:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:post:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:post:export']"
        >导出</el-button>
      </el-col> -->
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="postList">
        <el-table-column
          type="index"
          label="序号"
          width="55"
          align="left"
          :index="indexMethod"
        />
        <el-table-column label="岗位名称" align="left" width="180">
          <template #default="{ row }">
            <el-button size="mini" type="text" @click="handleView(row)">{{
              row.postName
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="所属部门"
          align="left"
          prop="deptName"
          min-width="180"
        />
        <el-table-column label="岗位类型" align="left" width="120">
          <template #default="{ row }">
            <div>{{ dict.label.sys_post_type[row.postType] }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="人员"
          align="left"
          prop="postSort"
          min-width="280"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.userList.length !== 0">
              <template v-for="(item, index) in scope.row.userList">
                {{ item.nickName }}
                <span v-bind:style="{ color: '#F56C6C' }">{{
                  item.status === "0" ? "" : "(停用)"
                }}</span
                >{{ index + 1 === scope.row.userList.length ? "" : "," }}
              </template>
            </span>
            <span v-if="scope.row.userList.length === 0"> - </span>
          </template>
        </el-table-column>
        <el-table-column
          label="功能菜单权限"
          align="left"
          prop="menuCounts"
          min-width="120"
        />
        <el-table-column label="状态" align="left" prop="status" width="100">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_normal_disable"
              :value="scope.row.status"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="left"
          class-name="small-padding fixed-width"
          width="280"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['system:postNew:view']"
              >查看</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:postNew:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="handleAddUser(scope.row)"
              v-hasPermi="['system:postNew:addUser']"
              >添加用户</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:postNew:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <UserDepPostSelect
        rowKey="userId"
        title="user"
        :showSelect="true"
        :isClose="false"
        v-model="openUserDepPost"
        @on-submit-success-user="userSuccess"
      />
    </div>
    <PostDetail :id="detailId" :type="detailType" @addUpdateCallBack="addUpdateCallBack" @goEdit="goEdit" v-if="detailType" />
  </div>
</template>

<script>
import PostDetail from "./components/postDetail";
import { getDicts } from "@/api/system/dict/data";
import { accreditUsers } from "@/api/system/user";
import {uniqueArrObj} from "@/utils"
import { listPost, delPost ,batchUserPost} from "@/api/system/post";
export default {
  name: "PostNew",
  components: { PostDetail },
  dicts: ["sys_normal_disable", "sys_post_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptName: undefined,
        postType: undefined,
        postName: undefined,
        userName: undefined,
        status: undefined,
      },
      openUserDepPost: false,
      detailType: "",
      detailId: "",
      sysPostTypeList:[],
      postIdAdd:'',
      multipleSelectionUser:[]
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
      this.getDicts();
    },
    /** 获取序号 */
    indexMethod(index) {
       if (this.queryParams && this.queryParams.pageNum) {
        return (
          (this.queryParams.pageNum - 1) * this.queryParams.pageSize +
          (index + 1)
        );
      } else {
        return index + 1;
      }
    },
    /** 查询岗位列表 */
    getList() {
      this.loading = true;
      listPost(this.queryParams).then((response) => {
        this.postList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    async getDicts(){
      const {data}=await getDicts('sys_post_type');
      this.sysPostTypeList=data;
      this.sysPostTypeList.unshift({'dictLabel':'全部','dictValue':undefined});
    },
    handleView(value) {
      this.detailType = "view";
      this.goDetail(value.postId);
    },
    handleUpdate(value) {
      this.detailType = "update";
      this.goDetail(value.postId);
    },
    handleAdd() {
      this.detailType = "add";
      this.goDetail();
    },
    goDetail(value) {
        this.detailId=value||'';
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    async handleAddUser(value) {
      const {rows}= await accreditUsers({postId:value.postId});
      this.multipleSelectionUser=rows;
      this.postIdAdd=value.postId;
      this.openUserDepPost = true;
    },
    async userSuccess(value) {
      const hasDuplicateUser = value.some((newUser) =>
        this.multipleSelectionUser.some((existingUser) => existingUser.userId === newUser.userId)
      );
      if (hasDuplicateUser) {
        this.$modal.msgWarning("该用户已存在");
        return;
      }

      const userListAll=value.concat(this.multipleSelectionUser);
      let userList=userListAll.map(item=>{
        return {
          userId:item.userId,
          nickName:item.nickName,
        }
      })
      userList=uniqueArrObj(userList,'userId');
      const params={
        postId:this.postIdAdd,
        userList
      }
      await batchUserPost(params);
      this.$modal.msgSuccess("添加用户成功！");
      this.openUserDepPost = false;
      this.getList();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      this.$modal
        .confirm('是否确认删除岗位编号为"' + postIds + '"的数据项？')
        .then(function () {
          return delPost(postIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
    addUpdateCallBack(){
      this.detailType=false;
      this.getList();
    },
    goEdit(){
      this.detailType="edit";
    }
  },
};
</script>
