<template>
  <div class="app-container" id="roleOa">
    <el-form :model="queryParams" ref="queryForm" v-show="showSearch" :inline="true">
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="请输入角色名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="权限字符" prop="roleKey">
        <el-input
          v-model="queryParams.roleKey"
          placeholder="请输入权限字符"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="角色状态"
          clearable
          size="small"
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户昵称或名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户昵称或名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:roleoa:add']"
        >新增</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:roleoa:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roleList" >

      <el-table-column label="序号" type="index" align="center" width="120" />
      <el-table-column label="角色名称" class-name="small-padding fixed-width" >
          <template slot-scope = "scope">
            <a  v-if="checkPermi(['system:roleoa:view'])"
                @click="handleView(scope.row)">

              <span style="color:#1E90FF">{{scope.row.roleName}}</span>
            </a>
            <span v-if="!checkPermi(['system:roleoa:view'])">{{scope.row.roleName}}</span>
          </template>
      </el-table-column>
      <el-table-column label="权限字符" prop="roleKey" class-name="small-padding fixed-width" />

      <el-table-column label="状态" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="流程数量"  align="center" class-name="small-padding fixed-width" >
        <template slot-scope="scope">
          {{scope.row.roleOaList.filter(item => (item!==null && item.oaType==='opt')).length}}
        </template>
      </el-table-column>
      <el-table-column label="用户数量"  align="center" class-name="small-padding fixed-width" >
        <template slot-scope="scope">
          {{scope.row.userList.length}}
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" class-name="small-padding fixed-width" />

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['system:roleoa:view']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:roleoa:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:roleoa:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row >
          <el-col :span="12">
            <el-form-item label="角色名称" prop="roleName">
              <el-input v-model="form.roleName" placeholder="请输入角色名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="roleKey">
              <span slot="label">
                <el-tooltip content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                权限字符
              </span>
              <el-input v-model="form.roleKey" placeholder="请输入权限字符" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row >
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <span>已授权用户</span>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <el-table v-loading="loading" :data="form.userList" :row-class-name="rowClassName" >
              <el-table-column label="用户名称" prop="userName"  />
              <el-table-column label="用户昵称" prop="nickName"  />>
              <el-table-column label="状态" align="center" prop="status">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
                </template>
              </el-table-column>
              <el-table-column  label="公司">
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.unitShortName}} <br>
                  </template>
                </template>
              </el-table-column>

              <el-table-column label="部门"  >
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.deptName}} <br>
                  </template>
                </template>
              </el-table-column>
              <el-table-column label="岗位"  >
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.postName}} <br>
                  </template>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-circle-close"
                    @click="deleteRow(scope.row)"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <el-button type="primary" icon="el-icon-plus" @click="addPostUserInitBtn()">添加用户</el-button>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <span>OA流程权限</span>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <el-form-item >
              <el-checkbox v-model="oaflowExpand" @change="handleCheckedTreeExpand($event, 'oaflow')">展开/折叠</el-checkbox>
              <el-checkbox v-model="oaflowNodeAll" @change="handleCheckedTreeNodeAll($event, 'oaflow')">全选/全不选</el-checkbox>
              <el-checkbox v-model="form.oaCheckStrictly" @change="handleCheckedTreeConnect($event, 'oaflow')">父子联动</el-checkbox>
              <el-tree
                class="tree-border"
                :data="oaflowOptions"
                show-checkbox
                ref="oaflow"
                node-key="id"
                :check-strictly="!form.oaCheckStrictly"
                empty-text="加载中，请稍候"
                :props="defaultProps"
              >
              <span class="custom-tree-node" slot-scope="{ node,data }">
                <span>
                  <i class="el-icon-house" v-if="data.oaRoot" ></i>
                  <i class="el-icon-folder-opened" v-if="data.oaRoot===false && data.oaType==='opc'" ></i>
                  <i class="el-icon-document" v-if="data.oaRoot===false && data.oaType==='opt'"></i>
                </span>
                <span>{{ node.label }}</span>
              </span>
            </el-tree>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-dialog
        width="30%"
        title="角色OA添加用户"
        :visible.sync="innerVisible"
        append-to-body>
        <el-row type="flex" justify="center">
          <el-col :span="24" >
            <el-select clearable v-model="innerFormUserIds" placeholder="请选择添加的用户" filterable multiple  >
              <el-option
                v-for="item in userListEnablePostEdit"
                :key="item.userId"
                :label="item.nickName +'（账号：'+item.userName+'）'"
                :value="item.userId">
              </el-option>
              <!-- +'；状态：'+(item.status==='0'?'正常':'禁用') -->
            </el-select>
          </el-col>
          </el-row>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="addPostUser()">确 定</el-button>
            <el-button @click="innerCancel">取 消</el-button>
          </div>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 查看角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row >
          <el-col :span="12">
            <el-form-item label="角色名称" prop="roleName">{{ form.roleName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限字符" prop="roleKey" >{{ form.roleKey }}</el-form-item>
          </el-col>
        </el-row>

        <el-row >
          <el-col :span="12">
            <el-form-item label="状态">
              <dict-tag :options="dict.type.sys_normal_disable" :value="form.status"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">{{ form.remark }}</el-form-item>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <span>已授权用户</span>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <el-table v-loading="loading" :data="form.userList" :row-class-name="rowClassName" >
              <el-table-column label="用户名称" prop="userName"  />
              <el-table-column label="用户昵称" prop="nickName"  />>
              <el-table-column label="状态" align="center" prop="status">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
                </template>
              </el-table-column>
              <el-table-column  label="公司">
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.unitShortName}} <br>
                  </template>
                </template>
              </el-table-column>

              <el-table-column label="部门"  >
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.deptName}} <br>
                  </template>
                </template>
              </el-table-column>
              <el-table-column label="岗位"  >
                <template slot-scope="scope">
                  <template v-for="(item) in userPostListAll.filter(item => item.userId===scope.row.userId)">
                    {{item.postName}} <br>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <span>OA流程权限</span>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <el-form-item >
              <el-tree
                class="tree-border"
                :data="oaflowOptions"
                show-checkbox
                ref="oaflowView"
                node-key="id"
                :check-strictly="!form.oaCheckStrictly"
                empty-text="加载中，请稍候"
                :props="defaultProps"
              >

              <span class="custom-tree-node" slot-scope="{ node,data }">
                <span>
                  <i class="el-icon-house" v-if="data.oaRoot" ></i>
                  <i class="el-icon-folder-opened" v-if="data.oaRoot===false && data.oaType==='opc'" ></i>
                  <i class="el-icon-document" v-if="data.oaRoot===false && data.oaType==='opt'"></i>
                </span>
                <span>{{ node.label }}</span>
              </span>
            </el-tree>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listRole, getRole, delRole, addRole, updateRole, changeRoleStatus, treeselect as oaTreeselect, roleOaTreeselect } from "@/api/system/roleoa";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
import { getUserListAll,getUserListEnable } from "@/api/system/user";
import { optionselect,userPostSetList } from "@/api/system/post";
export default {
  name: "RoleOa",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      viewOpen: false,
      // 是否显示弹出层（数据权限）
      oaflowExpand: false,
      oaflowNodeAll: false,
      // 日期范围
      dateRange: [],
      // 菜单列表
      oaflowOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
        roleKey: undefined,
        status: undefined,
        userName: undefined
      },
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      //添加用户临时参数
      innerFormUserIds: [],
      //添加用户嵌套对话框是否展示
      innerVisible:false,
      // 人员选项 所有（含删除禁用）
      userListAll: [],
      // 人员选项 所有正常用户
      userListEnable: [],
      // 人员选项 本岗位未选择用户
      userListEnablePostEdit: [],
      //岗位选项 所有
      postListAll: [],
      //查询用户岗位公司部门集合
      userPostListAll:[],
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" }
        ],
        roleKey: [
          { required: true, message: "权限字符不能为空", trigger: "blur" }
        ]
      },



    };
  },
  mounted() {
    this.getList();
    this.getUserListEnableF();
    this.getUserListAllF();
    this.getPostListAllF();
    this.getUserPostSetListF();
  },
  methods: {
    checkPermi,
    //合并行
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      return [1, 2];
      },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.roleList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /**查询用户岗位公司部门集合 */
    getUserPostSetListF() {
      userPostSetList().then(response => {
        this.userPostListAll = response.data;
      });
    },
    /**查询所有岗位 */
    getPostListAllF() {
      optionselect().then(response => {
        this.postListAll = response.data;
      });
    },
    /**查询所有正常用户 */
    getUserListEnableF() {
      getUserListEnable().then(response => {
        this.userListEnable = response.data;
      });
    },
    /** 查询所有用户 */
    getUserListAllF() {
      getUserListAll().then(response => {
        this.userListAll = response.data;
      });
    },

    /** 查询菜单树结构 */
    getOaTreeselect() {
      oaTreeselect().then(response => {
        this.oaflowOptions = response.data;
      });
    },
    // 所有菜单节点数据
    getOaAllCheckedKeys() {
      // 目前被选中的菜单节点
      let checkedKeys = this.$refs.oaflow.getCheckedKeys();
      // 半选中的菜单节点
      let halfCheckedKeys = this.$refs.oaflow.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    /** 根据角色ID查询菜单树结构 */
    getRoleOaTreeselect(roleId) {
      return roleOaTreeselect(roleId).then(response => {
        this.oaflowOptions = response.oaflows;
        return response;
      });
    },
    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.roleName + '"角色吗？').then(function() {
        return changeRoleStatus(row.roleId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    // 取消按钮
    cancel() {
      this.innerVisible=false;
      this.open = false;
      this.viewOpen = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.oaflow != undefined) {
        this.$refs.oaflow.setCheckedKeys([]);
      }
      if (this.$refs.oaflowView != undefined) {
        this.$refs.oaflowView.setCheckedKeys([]);
      }
      this.oaflowExpand = false,
      this.oaflowNodeAll = false,
      this.form = {
        roleId: undefined,
        roleName: undefined,
        roleKey: undefined,
        roleSort: 0,
        status: "0",
        oaflowIds: [],
        oaCheckStrictly: true,
        remark: undefined,
        roleType: '0',
        userList: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type == 'oaflow') {
        let treeList = this.oaflowOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.oaflow.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type == 'oaflow') {
        this.$refs.oaflow.setCheckedNodes(value ? this.oaflowOptions: []);
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type == 'oaflow') {
        this.form.oaCheckStrictly = value ? true: false;
      }
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getOaTreeselect();
      this.open = true;
      this.title = "添加角色-OA";
      this.userListEnablePostEdit=this.userListEnable;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids
      const roleOa = this.getRoleOaTreeselect(roleId);
      getRole(roleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleOa.then(res => {
            let checkedKeys = res.checkedKeys
            checkedKeys.forEach((v) => {
                this.$nextTick(()=>{
                    this.$refs.oaflow.setChecked(v, true ,false);
                })
            })
          });
        });
        this.title = "修改角色-OA";

        this.userListEnablePostEdit=this.userListEnable;
        for(var itemUserE of this.form.userList){
          this.userListEnablePostEdit=this.userListEnablePostEdit.filter(item => item.userId !== itemUserE.userId);
        }
      });
    },

    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.roleId != undefined) {
            this.form.oaflowIds = this.getOaAllCheckedKeys();
            updateRole(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.oaflowIds = this.getOaAllCheckedKeys();
            addRole(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const roleId = row.roleId || this.ids
      const roleOa = this.getRoleOaTreeselect(roleId);
      getRole(roleId).then(response => {
        this.form = response.data;
        this.viewOpen = true;
        this.$nextTick(() => {
          roleOa.then(res => {
            let checkedKeys = res.checkedKeys
            checkedKeys.forEach((v) => {
                this.$nextTick(()=>{
                    this.$refs.oaflowView.setChecked(v, true ,false);
                })
            })
          });
        });
        this.title = "查看角色-OA";


      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除角色编号为"' + row.roleId + '"，角色名称为"' + row.roleName + '"的数据项？').then(function() {
        return delRole(row.roleId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/roleoa/export', {
        ...this.queryParams
      }, `role_${new Date().getTime()}.xlsx`)
    },
    // 其中row是⾏对象，rowindex是⾏号，从0开始。所以这样就能实现了序号(xh属性)递增并且取值为⾏号加1。
    rowClassName({ row, rowIndex }){
      row.xh = rowIndex +1
    },
    /** 删除行数据 */
    deleteRow(row) {
      this.form.userList.splice(row.xh -1,1);
      this.$modal.msgSuccess("点击确定后保存修改");
    },
    /** 新增行数据 */
    addPostUserInitBtn(){
      this.innerVisible = true;
      this.userListEnablePostEdit=this.userListEnable;
      for(var itemUserE of this.form.userList){
        this.userListEnablePostEdit=this.userListEnablePostEdit.filter(item => item.userId !== itemUserE.userId);
      }
    },
    addPostUser() {

      var item = {
              "userId":'',
              "userName":'',
              "nickName":'',
              "status":''
              };
      for(var itemId of this.innerFormUserIds){
        for(var itemUser of this.userListEnable){
          if(itemId===itemUser.userId){
            item = {
              "userId":itemUser.userId,
              "userName":itemUser.userName,
              "nickName":itemUser.nickName,
              "status":itemUser.status
              };
            this.form.userList.push(item);
          }
        }
      }
      this.innerVisible=false;
      this.innerFormUserIds=[];
      this.$modal.msgSuccess("点击确定后保存修改");
    },
    //取消按钮 选择用户
    innerCancel() {
      this.innerVisible=false;
      this.innerFormUserIds=[];
    },
  }
};
</script>
