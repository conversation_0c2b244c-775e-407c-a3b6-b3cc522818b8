<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="公司名称" prop="guaranteeCompanyName">
         <el-select
          v-model="queryParams.guaranteeCompanyId"
          clearable
          size="small"
          placeholder="请选择"
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="item in custList"
            :key="item.guaranteeCompanyId"
            :label="item.guaranteeCompanyName"
            :value="item.guaranteeCompanyId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="报表类型" prop="reportType">
        <el-select
          v-model="queryParams.reportType"
          clearable
          size="small"
          placeholder="请选择"
        >
          <el-option
            v-for="item in dict.type.report_date_type"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select 
          v-model="queryParams.status"
          clearable
          size="small"
          placeholder="请选择"
        >
          <el-option
            v-for="item in dict.type.sys_normal_disable"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="报表名称" prop="reportFormsName">
        <el-input
          v-model="queryParams.reportFormsName"
          placeholder="请输入报表名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['core:cwbbjy:add']"
        >新增策略</el-button>
      </el-col>
      
    </el-row>

    <el-table v-loading="loading" :data="cwbbjyList">
      <el-table-column label="公司名称" align="center" prop="guaranteeCompanyName" />
      <el-table-column label="报表类型" align="center" prop="reportType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.report_date_type" :value="scope.row.reportType"/>
        </template>
      </el-table-column>
      <el-table-column label="报表名称" align="center" prop="reportFormsName" />
      <el-table-column label="状态 " align="center" prop="status" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"

            @click="handleUpdatexq(scope.row)"
            v-hasPermi="['core:cwbbjy:view']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"

            @click="handleUpdate(scope.row)"
            v-hasPermi="['core:cwbbjy:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"

            @click="handleUpdateCopy(scope.row)"
            v-hasPermi="['core:cwbbjy:edit']"
          >复制</el-button>
          <el-button
            size="mini"
            type="text"

            @click="handleDelete(scope.row)"
            v-hasPermi="['core:cwbbjy:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改财务报校验对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row >
          <el-col :span="12">
            <el-form-item label="公司："  prop="guaranteeCompanyId" label-width="30%">
              <el-select
                v-model="form.guaranteeCompanyId"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in custList"
                  :key="item.guaranteeCompanyId"
                  :label="item.guaranteeCompanyName"
                  :value="item.guaranteeCompanyId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报表类型："  prop="reportType" label-width="30%">
              <el-select
                v-model="form.reportType"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in dict.type.report_date_type"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="12">
            <el-form-item label="报表名称：" prop="reportFormsName" label-width="30%">
              <el-input v-model="form.reportFormsName"  @blur="form.reportFormsName=$event.target.value.trim()"  maxlength="200" show-word-limit placeholder="请输入报表名称"  style="width: 206px"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态：" prop="status"   required label-width="30%">
              <el-switch
                v-model="form.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(form.status)"
              ></el-switch>  <span>{{this.statusName}}</span>
            </el-form-item>
        
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <el-form-item label="备注：" prop="remark" label-width="15%">
              <el-input v-model="form.remark" type="textarea"   maxlength="500" show-word-limit  placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>


        <el-row >
          <el-col :span="24">
            <el-button type="primary" icon="el-icon-plus" @click="onAdd()">添加规则</el-button>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <p style="color:red;margin-bottom:15px;font-size:12px;">
              规则说明：<br/>
                1、取值说明：取值字段前后需使用$符号标识出来，取值字段由三个部分组成：sheet页名称、关键字、以关键字为基准右侧单元格偏移量。<br/>
                    示例：“$Sheet1.资产总计【2】$”，表示获取sheet页名称为“Sheet1”中，“资产总计”单元格右侧第二个单元格的数据。<br/>
                2、公式说明：规则中支持常用计算表达式，允许执行基本的算数运算（“加：+、减：-、乘：*、除：/、求余：%”）、逻辑运算（或：||、且：&&）、和比较运算（相等：==、大于：>、小于：<、大于等于：>=、小于等于：<=、不等于：!=），符号需使用英文符号。<br/>
                    示例1：“$Sheet1.资产总计【2】$==$Sheet1.负债及所有者权益总计【2】$”；<br/>
                    示例2：“$Sheet1.未分配利润【3】$+$Sheet1.盈余公积【3】$-($Sheet1.未分配利润【2】$+$Sheet1.盈余公积【2】$)-$Sheet1.累计净利润【3】$==0”；<br/>
                    示例3：“$Sheet1.资产总计【2】$==$Sheet1.负债及所有者权益总计【2】 && $Sheet1.资产总计【3】$==$Sheet1.负债及所有者权益总计【3】$”。<br/>
            </p>
          </el-col>
        </el-row>

        <el-row >
          <el-col :span="24">
            <el-form  ref="formRef" :model="form"  :inline="true" :inline-message="true" :rules="formRules" size="medium" label-position="center">
              <el-table v-loading="loading" :data="form.ruleList" :row-class-name="rowClassName" >
                <el-table-column label="规则" prop="ruleValue"  >
                  <template slot-scope="scope" >
                        <el-form-item :prop="'ruleList.'+scope.$index+'.ruleValue'" :rules="formRules.ruleValue" >
                          <el-input v-model="scope.row.ruleValue" type="textarea" rows="3"  maxlength="15000" show-word-limit  placeholder="请输入规则" style="width: 730px" />
                        </el-form-item>
                  </template>                
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="120px" >
                  <template slot-scope="scope">
                    <el-button  size="mini" icon="el-icon-delete" type="danger" @click="rowDelete(scope.row)">删除</el-button>
                  </template>
                </el-table-column>              
              </el-table>
            </el-form>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 查看校验策略对话框 -->
    <el-dialog :title="title" :visible.sync="openxq" width="900px" append-to-body>
      <el-form ref="form" :model="form"  label-width="80px">
        <el-row >
          <el-col :span="12">
            <el-form-item label="公司："  prop="guaranteeCompanyId" label-width="30%">
              {{form.guaranteeCompanyName}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报表类型："  prop="reportType" label-width="30%">
              <dict-tag :options="dict.type.report_date_type" :value="form.reportType"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="12">
            <el-form-item label="报表名称：" prop="reportFormsName" label-width="30%">
              {{form.reportFormsName}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态：" prop="status"  label-width="30%">
              <dict-tag :options="dict.type.sys_normal_disable" :value="form.status"/>
            </el-form-item>
        
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            <el-form-item label="备注：" prop="remark" label-width="15%">
              {{form.remark}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row >
          <el-col :span="24">
            
              <el-table v-loading="loading" :data="form.ruleList" :row-class-name="rowClassName" >
                <el-table-column label="规则" prop="ruleValue"  >
                  <template slot-scope="scope" >
                    {{scope.row.ruleValue}}
                  </template>                
                </el-table-column>
              </el-table>
            
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" style="text-align:center;border-radius: 3px; height: 52px" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCwbbjy,
  getCwbbjy,
  delCwbbjy,
  addCwbbjy,
  updateCwbbjy,
  custList,reportTypeList
} from "@/api/cwbbjy/cwbbjy";

export default {

  dicts: ['sys_normal_disable','report_date_type','cust_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 财务报校验表格数据
      cwbbjyList: [],
      // 弹出层标题
      title: "",
      statusName :null,

      // 是否显示弹出层
      open: false,
      openxq: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        guaranteeCompanyId: null,
        guaranteeCompanyName: null,
        reportFormsName: null,
        status: null,
        link: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        guaranteeCompanyId: [{ required: true, message: "公司不能为空", trigger: "blur" },],
        reportFormsName: [{ required: true, message: "报表名称不能为空", trigger: "blur" },
            { min: 3, max: 100, message: '长度在 3 到 100 个字', trigger: 'blur' }],
        reportType: [{ required: true, message: "报表类型不能为空", trigger: "blur" }],
      },
      //动态表单校验
      formRules:{
        ruleValue:[{ required:true, message:'规则必填', trigger:'blur'}],
      },
      custList: [],
      reportTypeList: [],
    };
  },
  created() {
    this.getList();
    this.getcustList();
    this.getreportTypeList();
  },
  methods: {
    getcustList() {
      custList().then(response => {
        this.custList = response.data;
      });
    },
    getreportTypeList() {
      reportTypeList(this.fromData).then(response => {
        var tempList=response.data;
        for(var itemE of tempList){
          this.reportTypeList.push(itemE.reportType);
        }
      });
    },
    /** 查询财务报校验列表 */
    getList() {
      this.loading = true;
      listCwbbjy(this.queryParams).then(response => {
        this.cwbbjyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

  
    // 取消按钮
    cancel() {
      this.open = false;
      this.openxq = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        guaranteeCompanyId: null,
        guaranteeCompanyName: null,
        reportFormsName: null,
        status: "0",
        link: null,
        remark: null,
        reportType: null,
        ruleList:[{ruleValue:""}]
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.guaranteeCompanyId = null
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();


      this.handleStatusChange(  0)
      this.open = true;
      this.title = "添加校验策略";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCwbbjy(id).then(response => {
        this.handleStatusChange(  response.data.status)
        this.form = response.data;
        this.open = true;
        this.title = "修改校验策略";
      });
    },
    
    /** 复制按钮操作 */
    handleUpdateCopy(row) {
      this.reset();
      const id = row.id || this.ids
      getCwbbjy(id).then(response => {
        this.handleStatusChange(  response.data.status)
        this.form = response.data;
        this.form.id=null;
        this.open = true;
        this.title = "复制校验策略";
      });
    },
    /** 修改按钮操作 */
    handleUpdatexq(row) {
      this.reset();
      const id = row.id || this.ids
      getCwbbjy(id).then(response => {
        this.handleStatusChange(  response.data.status)
        this.form = response.data;
        this.openxq = true;
        this.title = "校验策略详情";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.$refs['formRef'].validate((validRdf)=>{
            if (validRdf) {
              if (this.form.guaranteeCompanyId==null||this.form.guaranteeCompanyId==''){
                this.$modal.msgError("公司不能为空");
              }else if (this.form.reportType==null||this.form.reportType==''){
                this.$modal.msgError("报表类型不能为空");
              }else {
                if (this.form.id != null) {
                  updateCwbbjy(this.form).then(response => {
                    if(response.code===200){
                      this.$modal.msgSuccess("修改成功");
                    }
                    this.open = false;
                    this.getList();
                  });
                } else {
                  addCwbbjy(this.form).then(response => {
                    if(response.code===200){
                      this.$modal.msgSuccess("新增成功");
                    }
                    this.open = false;
                    this.getList();
                  });
                }
              }
            }
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否删除此策略配置？').then(function() {
        return delCwbbjy(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('core/cwbbjy/export', {
        ...this.queryParams
      }, `cwbbjy_${new Date().getTime()}.xlsx`)
    },


    // 用户状态修改
    handleStatusChange(row) {
      if (row==1){
        this.statusName = "  停用"
      }
      if (row==0){
        this.statusName = "  启用"
      }
    },




    
    //动态表单添加
    onAdd(){
      var item = {
            "ruleValue":''
      }
    this.form.ruleList.push(item)
    },
    // 给表头加必选标识
    addRedStar(h,{ column }){
        return[h('span',{ style:'color: red'},'*'),h('span',' '+ column.label)]
    },
    rowDelete(row){
        this.$confirm('此操作将永久删除, 是否继续?','提⽰',{
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      }).then(()=>{
        this.form.ruleList.splice(row.xh -1,1)
        this.$message.success('删除成功!')
        }).catch(()=>{
      })
    },
    // 其中row是⾏对象，rowindex是⾏号，从0开始。所以这样就能实现了序号(xh属性)递增并且取值为⾏号加1。
    rowClassNameData({ row, rowIndex}){
      row.xh = rowIndex +1;
    },

    // 其中row是⾏对象，rowindex是⾏号，从0开始。所以这样就能实现了序号(xh属性)递增并且取值为⾏号加1。
    rowClassName({ row, rowIndex }){
      row.xh = rowIndex +1
    },

  }
};
</script>
