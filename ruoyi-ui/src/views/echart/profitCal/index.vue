<template>
  <div style="width: 1500px;height:1950px;">
     <div style="font-size: 13px;margin-left: 20px">
<!--       <div :title="tipsMsg"  style="color: #9D9D9D"  >-->
<!--         <pre>{{tipsMsg}}</pre>-->
<!--       </div>-->
       <div style="color:#999999;margin-bottom:15px;font-size:13px;margin-left: 15px;margin-top: 15px">
         <span style="font-weight: bold">说明：</span>每月利润 = 月收入 - 月坏账 - 月通道费 - 月流量费 - 月运营成本。需要先在【数据报表-利润测算参数设置】中配置参数，才能统计结果
       </div>
     </div>
    <el-form style="margin-left: 20px" :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-row style="margin-bottom: 0">
        <el-col :span="6">
         <el-form-item label="系统名称" prop="platformNo">
          <el-select size="medium"  clearable v-model="platformNoParam" placeholder="请选择" filterable multiple
                    >
            <el-option
              v-for="dict in platformNoSelect"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
      </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合作方" prop="partnerNo">
            <el-select size="medium"  clearable v-model="partnerNoParam" placeholder="请选择" filterable multiple
                      >
              <el-option
                v-for="dict in partnerNoSelect"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
            <el-form-item label="资金方" prop="fundNo">
              <el-select size="medium"  clearable v-model="fundNoParam" placeholder="请选择" filterable multiple
                        >
                <el-option
                  v-for="dict in fundNoSelect"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="担保公司" prop="custNo">
            <el-select v-model="custNoParam" placeholder="请选择" filterable multiple size="medium"
                       >
              <el-option
                v-for="dict in custNoSelect"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="开始时间">
            <el-date-picker
              style="width: 205px"
              v-model="queryParams.reconYear"
              clearable:false
              type="month"
              value-format="yyyy-MM"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结束时间">
            <el-date-picker
              style="width: 205px"
              v-model="queryParams.remark"
              type="month"
              value-format="yyyy-MM"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <MoreSearch modelCode="ECHARTS" :params="queryParams" v-show="showMoreSearch" byId="companyCode"></MoreSearch>
        <el-col :span="6">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
              >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div v-loading="loading" id="profitEChartDiv" style="width: 1200px;height:550px;margin-left: 100px">利润</div>
    <el-divider></el-divider>
    <div v-loading="loading" id="incomeCostEChartDiv" style="width: 1200px;height:550px;margin-left: 100px">成本</div>
    <el-divider></el-divider>
    <div v-loading="loading" id="costConstituteEChartDiv" style="width: 1200px;height:550px;margin-left: 100px">成本构成</div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { profixEchart} from '@/api/system/echarts'
import {getSysDictRefList,getSelectSysDictRefList} from '@/api/ref/ref'
import { getDicts} from "@/api/system/dict/data";
import { clone } from "xe-utils";

export default {
  name: "ProfitCal",
  dicts: ['sys_normal_disable'],
  data() {
    return {


 //  start 新增参数
      platformNoParam: '',
      custNoParam: '',
      partnerNoParam: '',
      fundNoParam: '',
      productNoParam: '',


      sysDictRefParam: {
        dictType: '',
        dictValue: '',
        pDictType: '',
        pDictValue: '',
        selectDictDatas:''
      },
      platformNoSelect: [],
      custNoSelect: [],
      fundNoSelect: [],
      partnerNoSelect: [],
      productNoSelect: [],
      //  end 新增参数


      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      dataList: [],
      //标题
      tipsMsg:"说明：每月利润（毛利） = 月收入 - 月坏账 - 月通道费 - 月流量费 - 月运营成本)。需要先在【数据报表-利润测算参数设置】中配置参数，才能展示结果",
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
       //外部系统
      externalsystems:[],
      //担保公司编码
      dbcompany:[],
      //合作方
      partnerdata:[],
      //资金方
      capitaldata:[],
      // 查询参数
      queryParams: {
        platformNo: null,
        partnerNo: null,
        fundNo: null,
        reconYear: null,
        remark: null,
        params: {
         moduleTypeOfNewAuth: 'ECHARTS',
        }
      },
      //X轴数据
      xAxisData:[],
      //每月利润
      profitEChartData:[],
      //收入与成本
      incomeCostData:[],
      //每月成本构成
      costConstituteData:[],
      // 表单参数
      form: {},
      querydatatype:"cust_no",
      externalsystem:"platform_no",
      partnerscode:"partner_no",
      capitalcode:"fund_no",
      productcode:"product_no",
      showMoreSearch:false
    };
  },
  mounted() {
    //默认时间计算
    this.nowtime();
    this.getList();
    //系统资金方担保公司资产方等
    this.getexternalsystem();
    this.getdbcompany();
    this.getpartner();
    this.getcapital();
        //  start 页面刷新时对数据的处理
     this.initSelectData()
    //  end 页面刷新时对数据的处理



  },
  methods: {


     //wzy渲染下拉框
     initSelectData() {
      getSelectSysDictRefList({ unitType: 4, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.partnerNoSelect = response;
      });
    },
     getCustNoList(val) {
      const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
      this.queryParams.platformNo = this.platformNoParam.toString()
      if (val == null || val === '' || flag) {
        this.custNoSelect = null
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.custNo = null
        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.custNoParam = null
        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {

            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getPartnerNoList(val) {
      const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
      this.queryParams.custNo = this.custNoParam.toString()
      if (val == null || val === '' || flag) {
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getFundNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())
      this.queryParams.partnerNo = this.partnerNoParam.toString()
      if (val == null || val === ''|| flag) {
        this.fundNoSelect = null
        this.productNoSelect = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
         this.sysDictRefParam.selectDictDatas =""

      if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
      else {
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
        this.sysDictRefParam.selectDictDatas =""
         if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getProductNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

      this.queryParams.fundNo = this.fundNoParam.toString()
      if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo
this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo

this.sysDictRefParam.selectDictDatas =""
        if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            // this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
      getProductNoValue(val) {
      const flag = this.lateByte(this.queryParams.productNo) > this.lateByte(val.toString())

      this.queryParams.productNo = this.productNoParam.toString()

       if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        this.productNoParam = null
        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue =''
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue = this.queryParams.productNo

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }

    },
     lateByte(sTargetStr) {
      var sTmpStr, sTmpChar;
      var nOriginLen = 0;
      var nStrLength = 0;

      sTmpStr = new String(sTargetStr);
      nOriginLen = sTmpStr.length;

      for (var i = 0; i < nOriginLen; i++) {
        sTmpChar = sTmpStr.charAt(i);

        if (escape(sTmpChar).length > 4) {
          nStrLength += 2;
        } else if (sTmpChar != '/r') {
          nStrLength++;
        }
      }
      return nStrLength;
    },
    //end


    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      this.queryParams.fundNo = this.fundNoParam.toString();
      this.queryParams.productNo = this.productNoParam.toString();
      //echart数据
      const params=clone(this.queryParams,true);
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
      profixEchart(params).then(response => {

        //每月利润
      this.profitEChartData = response.profitData;
      //收入与成本
      this.incomeCostData=response.incomeAndCostData;
        for (let i = 0; i < response.costConstituteData.length; i++) {
          this.incomeCostData.push(response.costConstituteData[i]);
        }
      // this.incomeCostData.push(response.profitData);
      //每月成本构成
      this.costConstituteData=response.costConstituteData;
      //X轴
      this.xAxisData = response.xAxis,
       this.profitStat();
       this.incomeAndCostStat();
       this.costConstituteEchart();
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    //获取外部系统平台编码
    getexternalsystem(){
        getDicts(this.externalsystem).then(response =>{
            this.externalsystems = response.data;
        } );
    },
     //获取担保公司编码
    getdbcompany(){
        getDicts(this.querydatatype).then(response =>{
            this.dbcompany = response.data;
        } );
    },
    //获取合作方编码
    getpartner(){
        getDicts(this.partnerscode).then(response =>{
            this.partnerdata = response.data;
        } );
    },
    //获取资金方编码
    getcapital(){
        getDicts(this.capitalcode).then(response =>{
            this.capitaldata = response.data;
        } );
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        statisticalIndex: null,
        reconYear: null,
        dataJanuary: null,
        dataFebruary: null,
        dataMarch: null,
        dataApril: null,
        dataMay: null,
        dataJune: null,
        dataJuly: null,
        dataAugust: null,
        dataSeptember: null,
        dataOctober: null,
        dataNovember: null,
        dataDecember: null,
        yearTotal: null,
        status: "0",
        remark: null,
        createTime: null,
        updateTime: null
      };
      
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryParams");
      this.queryParams.moreSearch=undefined;
      this.handleQuery();
    },
  //默认时间计算
    nowtime() {

      let nowDate = new Date();

      let date = {

        // 获取当前年份

        year: nowDate.getFullYear(),

        //获取当前月份
        lastyear : nowDate.getFullYear()-1,

        month: (nowDate.getMonth() + 1 < 10 ? "0" + (nowDate.getMonth() + 1) : nowDate.getMonth()+1)-1 ,

        //获取当前日期

        date1: nowDate.getDate(),
      };

        //拼接

      this.queryParams.remark = date.year + "-" + date.month + "-" + date.date1;
      this.queryParams.reconYear = date.lastyear + "-" + date.month + "-" + date.date1;

    },
    //每月利润数据
     profitStat(){
            var myChart = this.$echarts.init(document.getElementById('profitEChartDiv'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          //  orient: 'vertical',
                          type:'scroll',
                          left: 'center',
                          top: 50
                       },
                       color: ['#3AA1FF'],
                       grid: {
                              y: 50,
                              x2: 100,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        title: {
                            left: 'center',
                            text: '每月利润（毛利）',
                            x: 'center',
                            textStyle: {
                              fontSize: 16,
                              color: "rgba(85, 85, 85, 1)"
                            },

                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxisData
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},

                            },
                             right: "100px"
                        },
                        yAxis: {
                           name: "单位（万元）",
                          show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        },
                        series: this.profitEChartData
             };
              myChart.clear()
            myChart.setOption(option);

    },

    //每月收入与成本
    incomeAndCostStat(){
      var myChart = this.$echarts.init(document.getElementById('incomeCostEChartDiv'));
      var option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        color: ['#3AA1FF','#F15A75','#F5DD67','#A67BD5','#6676CE'],
       legend: {
        //  orient: 'vertical',
        type:'scroll',
        left: 'center',
        bottom: "5%",

     },
        // legend: {
        //   data: ['收入', '月坏账', '月通道费', '月流量费', '月运营成本']
        // },

       grid: {

              y: 50,
              x2: 100,
              x1: 10,
              y2: 100,
              borderWidth: 1,
            },
        title: {
          left: 'center',
          text: '每月收入与成本',
          x: 'center',
          textStyle: {
            fontSize: 16,
            color: "rgba(85, 85, 85, 1)"
          },

        },
        // grid: {
        //   left: '3%',
        //   right: '4%',
        //   bottom: '3%',
        //   containLabel: true
        // },
         xAxis: {
          type: 'category',
          boundaryGap: true,
          data: this.xAxisData,
          axisLabel:{
                rotate : 30
            }
      },
       toolbox: {
          show: true,
          feature: {
              dataZoom: {
                      yAxisIndex: 'none'
                        },
              dataView: { readOnly: false },
              // 柱状图或者折线图展示
              magicType: { type: ['line', 'bar'] },

              restore: {},
              saveAsImage: {},

          },
          right: "100px"
      },
        yAxis: [{
          name: "单位（万元）",
          show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
            type: 'value',
            boundaryGap: ['0%', '100%']
        }],
        // series: this.incomeCostData
        series: this.incomeCostData
      };
      myChart.clear()
      myChart.setOption(option);
    },
    // //每月收入与成本
    //  incomeAndCostStat(){
    //         var myChart = this.$echarts.init(document.getElementById('incomeCostEChartDiv'));
    //         var option={
    //               tooltip: {
    //                         trigger: 'axis',
    //                     },
    //                      legend: {
    //                       //  orient: 'vertical',
    //                       type:'scroll',
    //                       left: 'center',
    //                       bottom: "5%",
    //
    //                    },
    //                     color: ['#3AA1FF','#F49B49'],
    //
    //                    grid: {
    //
    //                           y: 50,
    //                           x2: 100,
    //                           x1: 10,
    //                           y2: 100,
    //                           borderWidth: 1,
    //                         },
    //                     title: {
    //                         left: 'center',
    //                         text: '每月收入与成本',
    //                         x: 'center',
    //                          textStyle: {
    //                           fontSize: 16,
    //                           color: "rgba(85, 85, 85, 1)"
    //                         },
    //                     },
    //                      xAxis: {
    //                         type: 'category',
    //                         boundaryGap: true,
    //                         data: this.xAxisData,
    //                         axisLabel:{
    //                               rotate : 30
    //                           }
    //                     },
    //                      toolbox: {
    //                         show: true,
    //                         feature: {
    //                             dataZoom: {
    //                                     yAxisIndex: 'none'
    //                                       },
    //                             dataView: { readOnly: false },
    //                             // 柱状图或者折线图展示
    //                             magicType: { type: ['line', 'bar'] },
    //
    //                             restore: {},
    //                             saveAsImage: {},
    //
    //                         },
    //                         right: "100px"
    //                     },
    //                     yAxis: {
    //                       name: "单位（万元）",
    //                       show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
    //                         type: 'value',
    //                         boundaryGap: ['0%', '100%']
    //                     },
    //                     series: this.incomeCostData
    //          };
    //           myChart.clear()
    //         myChart.setOption(option);
    //     },
        //每月成本构成
         costConstituteEchart(){
            var myChart = this.$echarts.init(document.getElementById('costConstituteEChartDiv'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          //  orient: 'vertical',
                          type:'scroll',
                          left: 'center',

                           bottom: "0%",
                       },
                       color: ['#F15A75','#F5DD67','#A67BD5','#6676CE'],
                       grid: {

                              y: 50,
                              x2: 100,
                              x1: 10,
                              y2: 100,
                              borderWidth: 1,
                            },
                        title: {
                            left: 'center',
                            text: '每月成本构成',
                            x: 'center',
                             textStyle: {
                              fontSize: 16,
                              color: "rgba(85, 85, 85, 1)"
                            },
                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxisData,
                            axisLabel:{
                                  rotate : 30
                              }
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},

                            },
                            right: "100px"
                        },
                        yAxis: {
                          name: "单位（万元）",
                          show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        },
                        series: this.costConstituteData
             };
              myChart.clear()
            myChart.setOption(option);
        },
  }
};
</script>
