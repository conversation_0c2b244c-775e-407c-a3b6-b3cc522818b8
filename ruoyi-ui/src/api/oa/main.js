import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listMain(query) {
  return request({
    url: '/oasystem/projectFlowMain/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getMain(id) {
  return request({
    url: '/oasystem/projectFlowMain/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addMain(data) {
  return request({
    url: '/oasystem/projectFlowMain',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateMain(data) {
  return request({
    url: '/oasystem/projectFlowMain',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delMain(id) {
  return request({
    url: '/oasystem/projectFlowMain/' + id,
    method: 'delete'
  })
}

// 项目与流程关联删除
export function deleteMain(id) {
  return request({
    url: '/oasystem/projectFlowMain/delete/' + id,
    method: 'delete'
  })
}


// 新增【请填写功能名称】
export function addproMain(data) {
  return request({
    url: '/oasystem/projectFlowMain/addProFlowMain',
    method: 'post',
    data: data
  })
}
// 查询【请填写功能名称】详细
export function listMaindetil(id) {
  return request({
    url: '/oasystem/projectFlowMain/datadetils/' + id,
    method: 'get'
  })
}
export function getupdataData(id) {
  return request({
    url: '/oasystem/projectFlowMain/getUpdatedatad/' + id,
    method: 'get'
  })
}




