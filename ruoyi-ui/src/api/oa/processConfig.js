import request from '@/utils/request'
export function getFlow(query) {
    return request({
        url: '/noa/authority/getFlow',
        method: 'get',
        params: query
    })
}
export function getNodeToFlow(query) {
    return request({
        url: '/noa/authority/getNodeToFlow',
        method: 'get',
        params: query
    })
}
export function getUnit() {
    return request({
        url: '/records/record/getUnit',
        method: 'get',
    })
}
export function getOASettingList(query) {
    return request({
        url: '/noa/OASettingUp/list',
        method: 'get',
        params: query
    })
}
export function getOASettingDetail(query) {
    return request({
        url: '/noa/OASettingUp',
        method: 'get',
        params: query
    })
}
export function OASettingUp(data) {
    return request({
        url: '/noa/OASettingUp',
        method: 'post',
        data: data
    })
}
export function editOASettingUp(data) {
    return request({
        url: '/noa/OASettingUp',
        method: 'put',
        data: data
    })
}
export function delOASettingUp(id) {
    return request({
        url: '/noa/OASettingUp/' + id,
        method: 'delete',
    })
}
export function roleScope(data) {
    return request({
        url: '/system/roleScope',
        method: 'post',
        data: data
    })
}
export function editroleScope(data) {
    return request({
        url: '/system/roleScope',
        method: 'put',
        data: data
    })
}
export function changeStatus(data) {
    return request({
        url: '/system/roleScope/changeStatus',
        method: 'put',
        data: data
    })
}
export function roleScopeList(query) {
    return request({
        url: '/system/roleScope/list',
        method: 'get',
        params: query
    })
}
export function roleScopeDetail(id) {
    return request({
        url: '/system/roleScope/' + id,
        method: 'get'
    })
}
export function delroleScope(id) {
    return request({
        url: '/system/roleScope/' + id,
        method: 'delete'
    })
}
export function recordAdd(data) {
    return request({
        url: '/records/record',
        method: 'post',
        data: data
    })
}
export function recordEdit(data) {
    return request({
        url: '/records/record',
        method: 'put',
        data: data
    })
}
export function recordDel(id) {
    return request({
        url: '/records/record/' + id,
        method: 'delete'
    })
}
export function recordList(query) {
    return request({
        url: '/records/record/list',
        method: 'get',
        params: query
    })
}
export function payrollFileAdd(data) {
    return request({
        url: '/payrollFile/payrollFile',
        method: 'post',
        data: data
    })
}
export function payrollFileD(query) {
    return request({
        url: '/payrollFile/payrollFile',
        method: 'get',
        params: query
    })
}
export function payrollFileDel(ids) {
    return request({
        url: '/payrollFile/payrollFile/' + ids,
        method: 'delete',

    })
}
export function payrollFileList(query) {
    return request({
        url: '/payrollFile/payrollFile/list',
        method: 'get',
        params: query
    })
}
export function payrollFileDetail(id) {
    return request({
        url: '/payrollFile/payrollFile/' + id,
        method: 'get',
    })
}
export function payrollFileTx(id) {
    return request({
        url: '/payrollFile/payrollFile/tx/' + id,
        method: 'get',

    })
}
export function setRemindState(query) {
    return request({
        url: '/noa/OASettingUp/setRemindState',
        method: 'get',
        params: query
    })
}
export function getDraftId(id) {
    return request({
        url: '/payrollFileRecord/payrollFileRecord/getDraftId/' + id,
        method: 'get',

    })
}
export function payrollFileRecordAdd(data) {
    return request({
        url: '/payrollFileRecord/payrollFileRecord',
        method: 'post',
        data: data
    })
}