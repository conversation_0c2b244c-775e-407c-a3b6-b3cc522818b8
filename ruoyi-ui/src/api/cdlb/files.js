import request from '@/utils/request'

// 查询绿本文件关联列表
export function listFiles(query) {
  return request({
    url: '/cdlbFiles/files/list',
    method: 'get',
    params: query
  })
}

// 查询绿本文件关联详细
export function getFiles(id) {
  return request({
    url: '/cdlbFiles/files/' + id,
    method: 'get'
  })
}
// 查询绿本文件关联详细
export function getFilesPathMapping() {
  return request({
    url: '/cdlbFiles/files/pathMapping',
    method: 'get'
  })
}

// 新增绿本文件关联
export function addFiles(data) {
  return request({
    url: '/cdlbFiles/files',
    method: 'post',
    data: data
  })
}

// 修改绿本文件关联
export function updateFiles(data) {
  return request({
    url: '/cdlbFiles/files',
    method: 'put',
    data: data
  })
}

// 删除绿本文件关联
export function delFiles(id) {
  return request({
    url: '/cdlbFiles/files/' + id,
    method: 'delete'
  })
}
