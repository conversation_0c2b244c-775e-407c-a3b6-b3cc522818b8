import request from "@/utils/request";

export function addPromissoryNote(data) {
  return request({
    url: "/promissory/note",
    method: "post",
    data,
  });
}
export function updatePromissoryNote(data) {
  return request({
    url: "/promissory/note",
    method: "put",
    data,
  });
}
export function getPromissoryNoteList(params) {
  return request({
    url: "/promissory/note/list",
    method: "get",
    params
  });
}
export function getPromissoryNoteById(id) {
  return request({
    url: "/promissory/note/"+id,
    method: "get",
  });
}
export function delPromissoryNote(id) {
  return request({
    url: '/promissory/note/' + id,
    method: 'delete'
  })
}