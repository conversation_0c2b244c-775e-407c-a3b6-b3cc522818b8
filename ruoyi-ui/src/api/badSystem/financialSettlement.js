import request from "@/utils/request";

export function addFinancialSettlement(data) {
  return request({
    url: "/financial/settlement",
    method: "post",
    data,
  });
}
export function updateFinancialSettlement(data) {
  return request({
    url: "/financial/settlement",
    method: "put",
    data,
  });
}
export function getReconciliationList(data) {
  return request({
    url: "/channel/business/reconciliation/list",
    method: "post",
    data,
  });
}
export function getSettlementList(params) {
  return request({
    url: "/financial/settlement/list",
    method: "get",
    params
  });
}
export function getFinancialSettlementById(id) {
  return request({
    url: "/financial/settlement/"+id,
    method: "get",
  });
}
export function delFinancialSettlement(id) {
  return request({
    url: '/financial/settlement/' + id,
    method: 'delete'
  }) 
}
export function getBlFinancialSettlementFlowController(params) {
  return request({
    url: "/informationFlowController/getBlFinancialSettlement",
    method: "get",
    params
  });
}
export function updateProcessSettlementFinancial(data) {
  return request({
    url: "/financial/settlement/processFinancialSettlementVo",
    method: "put",
    data,
  });
}
export function getByProcessIdFinancialSettlement(id) {
  return request({
    url: '/financial/settlement/getByProcessId/' + id,
    method: 'get'
  })
} 
export function settlementPaidAlready(data) {
  return request({
    url: '/financial/settlement/paidAlready',
    method: 'post',
    data,
  })
} 
export function updateBlNotify(data) {
  return request({
    url: "/bl/notify",
    method: "put",
    data,
  });
}