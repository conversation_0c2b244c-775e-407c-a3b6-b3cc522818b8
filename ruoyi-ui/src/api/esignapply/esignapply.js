import request from '@/utils/request'

// 查询富邦《担保明细清单》签章申请列表
export function listEsignapply(query) {
  return request({
    url: '/core/esignapply/list',
    method: 'get',
    params: query
  })
}

// 查询富邦《担保明细清单》签章申请详细
export function getEsignapply(id) {
  return request({
    url: '/core/esignapply/' + id,
    method: 'get'
  })
}

// 新增富邦《担保明细清单》签章申请
export function addEsignapply(data) {
  return request({
    url: '/core/esignapply',
    method: 'post',
    data: data
  })
}

// 业务总监审核
export function updateEsignapply2(data) {
  return request({
    url: '/core/esignapply/edit2',
    method: 'put',
    data: data
  })
}
// 风险总监审核
export function updateEsignapply3(data) {
  return request({
    url: '/core/esignapply/edit3',
    method: 'put',
    data: data
  })
}
// 管理部总监审核
export function updateEsignapply4(data) {
  return request({
    url: '/core/esignapply/edit4',
    method: 'put',
    data: data
  })
}
//  档案管理员归档
export function updateEsignapply6(data) {
  return request({
    url: '/core/esignapply/edit6',
    method: 'put',
    data: data
  })
}


//  申请终止
export function updateEsignapply9(data) {
  return request({
    url: '/core/esignapply/edit9',
    method: 'put',
    data: data
  })
}

