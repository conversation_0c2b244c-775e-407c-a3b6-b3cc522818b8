import request from "@/utils/request";
export function qrReport(data) {
    return request({
        url: "/qrAuditReport/qrReport",
        method: "post",
        data

    });
}
export function qrReportEdit(data) {
    return request({
        url: "/qrAuditReport/qrReport",
        method: "put",
        data

    });
}
export function qrReportList(data) {
    return request({
        url: "/qrAuditReport/qrReport/list",
        method: "post",
        data

    });
}
export function getInfo(params) {
    return request({
        url: "qrAuditReport/qrReport/getInfo",
        method: "get",
        params
    });
}
export function historyList(params) {
    return request({
        url: "qrAuditReport/history/list",
        method: "get",
        params
    });
}
export function historyItem(params) {
    return request({
        url: "qrAuditReport/history",
        method: "get",
        params
    });
}
export function getVersion(params) {
    return request({
        url: "/versionMaintain/maintain/getVersion",
        method: "get",
        params
    });
}
export function maintain(data) {
    return request({
        url: "/versionMaintain/maintain",
        method: "put",
        data

    });
}
export function qrReportDel(params) {
    return request({
        url: "qrAuditReport/qrReport",
        method: "delete",
        params
    });
}