import request from '@/utils/request'

// 查询财务项目管理-信息费列表
export function listFee(query) {
  return request({
    url: '/cwxmgl/fee/list',
    method: 'get',
    params: query
  })
}

// 查询财务项目管理-信息费详细
export function getFee(id) {
  return request({
    url: '/cwxmgl/fee/' + id,
    method: 'get'
  })
}

// 新增财务项目管理-信息费
export function addFee(data) {
  return request({
    url: '/cwxmgl/fee',
    method: 'post',
    data: data
  })
}

// 修改财务项目管理-信息费
export function updateFee(data) {
  return request({
    url: '/cwxmgl/fee',
    method: 'put',
    data: data
  })
}

// 删除财务项目管理-信息费
export function delFee(id) {
  return request({
    url: '/cwxmgl/fee/' + id,
    method: 'delete'
  })
}

// 查询财务项目管理-信息费列表-待录入收入
export function listFlagZero(query) {
  return request({
    url: '/cwxmgl/fee/list/flagzero',
    method: 'get',
    params: query
  })
}

// 查询财务项目管理-信息费列表-待确认收入
export function listFlagOne(query) {
  return request({
    url: '/cwxmgl/fee/list/flagone',
    method: 'get',
    params: query
  })
}
