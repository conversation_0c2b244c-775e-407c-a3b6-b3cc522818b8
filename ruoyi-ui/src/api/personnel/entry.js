import request from "@/utils/request";

export function onboardingList(params) {
  return request({
    url: "/personnel/onboarding/list",
    method: "get",
    params,
  });
}
export function getPersonnelOnboardingFlow(params) {
  return request({
    url: "/personnelflow/getPersonnelOnboardingFlow",
    method: "get",
    params,
  });
}
export function addOnboarding(data) {
  return request({
    url: "/personnel/onboarding",
    method: "post",
    data,
  });
}
export function insertCommitOnboarding(data) {
  return request({
    url: "/personnel/onboarding/insertCommitOnboarding",
    method: "post",
    data,
  });
}
export function batchPersonnelArchives(data) {
  return request({
    url: "/personnel/onboarding/batchPersonnelArchives",
    method: "post",
    data,
  });
}
export function updateOnboarding(data) {
  return request({
    url: "/personnel/onboarding",
    method: "put",
    data,
  });
}
export function delOnboarding(configId) {
  return request({
    url: '/personnel/onboarding/' + configId,
    method: 'delete'
  })
}
export function personnelProcessId(configId) {
  return request({
    url: '/personnel/process/processId/' + configId,
    method: 'get'
  })
}

export function commitOnboardings(data) {
  return request({
    url: '/personnel/onboarding/commitOnboardings',
    method: "post",
    data,
  })
}
export function personnelProcess(data) {
  return request({
    url: '/personnel/process',
    method: "post",
    data,
  })
}

export function updateByApprovaling(data) {
  return request({
    url: "/personnel/onboarding/updateByApprovaling",
    method: "put",
    data,
  });
}
export function completeProcessByProcessId(data) {
  return request({
    url: "/personnel/process/completeProcessByProcessId",
    method: "put",
    data,
  });
}
export function passOnboardings(data) {
  return request({
    url: "/personnel/onboarding/passOnboardings",
    method: "post",
    data,
  });
}
export function unpassOnboardings(data) {
  return request({
    url: "/personnel/onboarding/unpassOnboardings",
    method: "post",
    data,
  });
}
export function payrollrileInsert(data) {
  return request({
    url: "/payrollFile/payrollFile/insert",
    method: "post",
    data,
  });
}
export function personnelOnboarding(configId) {
  return request({
    url: "/personnel/onboarding/"+configId,
    method: "get",
  });
}
export function systemUserList(params) {
  return request({
    url: "/system/user/listForPersonnel",
    method: "get",
    params,
  });
}