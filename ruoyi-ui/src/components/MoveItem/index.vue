<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleClose"
    >
      <el-tree
        class="filter-tree"
        :data="treeData"
        :props="defaultProps"
        @node-click="handleNodeClick"
        ref="tree"
      >
      </el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="move()">移动到该目录</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name:"MoveItem",
  props: {
    multipleSelection: Array,
    treeData: Array,
    defaultProps:Object
  },
  data() {
    return {
      dialogVisible: true,
      moveId: null,
      
    };
  },
  methods: {
    handleNodeClick(data) {
      console.log(data);
      this.moveId = data.id;
    },
    move() {
      if (!this.moveId) {
        this.$message.warning("请选择目录");
        return;
      }
      this.$emit("success",this.moveId);
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>


</style>