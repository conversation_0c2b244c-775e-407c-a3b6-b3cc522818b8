<template>
  <div>
    <el-dialog
      title="查看催审记录"
      :visible.sync="dialogVisible"
      width="1400px"
      :before-close="handleClose"
    >
      <div v-if="type" class="search">
        <div class="item">
          <span>流程分类</span>
          <el-select
            clearable
            style="width: 200px"
            v-model="queryParams.processType"
          >
            <el-option
              v-for="dict in dict.type.yspTemplate_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </div>
        <div class="item">
          <span>流程主题</span>
          <el-input
            v-model="queryParams.theme"
            placeholder="请输入流程主题"
            style="width: 200px"
          ></el-input>
        </div>

        <div class="item">
          <span>通知审核人</span>
          <el-input
            v-model="queryParams.noticeProcessUserName"
            placeholder="请输入姓名"
            style="width: 200px"
          ></el-input>
        </div>
        <div class="item">
          <span>催审人</span>
          <el-input
            v-model="queryParams.urgentReviewUser"
            placeholder="请输入姓名"
            style="width: 200px"
          ></el-input>
        </div>
        <div class="item">
          <span>催审时间</span>
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </div>
        <el-button type="primary" @click="search">搜 索</el-button>
        <el-button @click="reset">重 置</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column v-if="type" label="流程主题">
          <template slot-scope="scope">
            <el-button
              style="margin-left: 0"
              type="text"
              @click="toTheme(scope.row)"
              >{{ scope.row.theme }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="noticeNodesNane"
          label="通知审核节点"
          width="180"
        />
        <el-table-column
          prop="noticeProcessUserName"
          label="通知审核人"
          width="100"
        />
        <el-table-column
          prop="noticeCheckStatus"
          label="通知查看状态"
          width="100"
        >
          <template slot-scope="scope">{{
            scope.row.noticeCheckStatus == 0 ? "未查看" : "已查看"
          }}</template>
        </el-table-column>
        <el-table-column
          prop="templateName"
          label="催办节点审核状态"
          width="150"
        >
          <template slot-scope="scope">
            {{
              scope.row.nodesCheckStatus === "0"
                ? "未审核"
                : scope.row.nodesCheckStatus === "1"
                ? "同意"
                : scope.row.nodesCheckStatus === "2"
                ? "驳回"
                : scope.row.nodesCheckStatus === "3"
                ? "不通过"
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column prop="urgentReviewRemarks" label="催审留言" />
        <el-table-column prop="urgentReviewUser" label="催审人" width="100" />
        <el-table-column prop="urgentReviewTime" label="催审时间" width="200" />
      </el-table>
      <pagination
        v-show="queryParams.total > 0"
        :total="queryParams.total"
        :page.sync="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { csList } from "@/api/oa/financeProcess";

export default {
  name: "ReminderRecord",
  props: {
    businessId: String,
    type: Boolean,
  },
  dicts: ["yspTemplate_type"],
  data() {
    return {
      tableData: [],
      dialogVisible: true,
      time: [],
      queryParams: {
        businessId: "",
        flowType: "",
        theme: "",
        noticeProcessUserName: "",
        urgentReviewUser: "",
        createTime: "",
        updateTime: "",
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
    };
  },
  mounted() {
    this.queryParams.businessId = this.businessId;
    this.getList();
  },
  methods: {
    toTheme(v) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: v.businessId,
          businessId: v.businessId,
          financeProcess: "true",
        },
      });
    },
    getList() {
      if (this.time && this.time.length > 0) {
        this.queryParams.createTime = this.$format(
          this.time[0],
          "yyyy-MM-dd HH:mm:ss"
        );
        this.queryParams.updateTime = this.$format(
          this.time[1],
          "yyyy-MM-dd HH:mm:ss"
        );
      } else {
        this.queryParams.createTime = "";
        this.queryParams.updateTime = "";
      }
      csList({ ...this.queryParams }).then((res) => {
        this.tableData = res.rows;
        this.queryParams.total = res.total;
      });
    },
    search() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    reset() {
      this.queryParams = {
        flowType: "",
        theme: "",
        noticeProcessUserName: "",
        urgentReviewUser: "",
        createTime: "",
        updateTime: "",
        pageSize: 10,
        pageNum: 1,
        total: 0,
      };
      this.queryParams.businessId = this.businessId;
      this.time = [];
      this.getList();
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  padding-bottom: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    margin-right: 16px;
    display: flex;
    align-items: center;
  }
  span {
    margin-right: 9px;
    display: inline-block;
    width: 90px;
    text-align: right;
  }
}

.el-button {
  height: 36px;
  margin-left: 16px;
}
</style>