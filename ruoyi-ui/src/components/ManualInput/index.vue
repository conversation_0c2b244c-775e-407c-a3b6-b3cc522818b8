<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      @open="handelOpen"
      width="1000px"
    >
      <el-scrollbar>
        <div style="max-height: 75vh">
          <el-form
            v-if="title == '手工录入代偿款数据'"
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            label-width="70px"
            style="margin-bottom: 10px"
          >
            <el-form-item label="项目名称" class="form-item" prop="projectName">
              <el-select
                filterable
                v-model="queryParams.projectName"
                placeholder="请选择项目名称"
                @change="handleChange"
              >
                <el-option
                  v-for="(item, index) in projectListTable"
                  :key="index"
                  :label="item.projectName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-form>
          <div v-else class="mb-2">项目名称: {{ dataInfo.projectName }}</div>
          <MyTable
            :columns="columns"
            :source="tableList"
            :row-class-name="rowClassName"
          >
            <template #dataDay="{ record }">
              <el-date-picker
                v-model="record.dataDay"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                style="width:150px"
              >
              </el-date-picker>
            </template>
            <template #debitAmount="{ record }">
              <el-input
                v-model="record.debitAmount"
                @input="handleInput(record)"
                 style="width:180px"
              >
                <template slot="append">元</template></el-input
              >
            </template>
            <template #remark="{ record }">
              <el-input
                v-model="record.remark"
                placeholder="限500字"
                maxlength="500"
                show-word-limit
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
              ></el-input>
            </template>
            <template #operate="{ record }">
              <el-button type="text" @click="deletTable(record)"
                >删除</el-button
              >
            </template>
          </MyTable>
          <el-button @click="addTable" type="primary" class="mt-1"
            >添加</el-button
          >
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button
            @click="submit"
            type="primary"
            class="ml-3"
            :disabled="tableAll"
            >确定</el-button
          >
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import XEUtils from "xe-utils";
import { decimal, toMillennials } from "@/utils";
export default {
  name: "ManualInput",
  mixins: [vModelMixin],
  props: {
    title: {
      type: String,
      default: "手工录入代偿款数据",
    },
    projectListTable: {
      type: Array,
      default: () => [],
      required: true,
    },
    dataInfo: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  computed: {
    //是否全部填写完成
    tableAll() {
      let temp = false;
      // if (!this.queryParams.projectName) {
      //   return true;
      // }
      if (!this.tableList.length) {
        return true;
      }
      this.tableList.some((item) => {
        if (!item.dataDay || !item.debitAmount) {
          temp = true;
          return false;
        }
      });
      return temp;
    },
  },
  data() {
    return {
      queryParams: {
        projectName: "",
      },
      columns: Object.freeze([
        { label: "记账日期", key: "dataDay", width: 150 },
        { label: "实际代偿款", key: "debitAmount", width: 180 },
        { label: "说明", key: "remark",width: 300 },
        { label: "", key: "operate", width: 100 },
      ]),
      tableList: [],
      formatterValue: "",
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
    handelOpen() {
      const { tableList = [] } = XEUtils.clone(this.dataInfo, true);
      this.tableList = tableList;
      toMillennials(this.tableList, ["debitAmount"]);
    },
    handleChange() {},
    rowClassName({ row, rowIndex }) {
      row.myId = rowIndex + 1;
    },
    addTable() {
      this.tableList.push({
        dataDay: "",
        debitAmount: "",
      });
    },
    deletTable(value) {
      this.tableList.splice(value.myId - 1, 1);
    },
    handleInput(val) {
      this.$nextTick(() => {
        const data = decimal(val.debitAmount, 2);
        val.debitAmount = String(data).replace(
          /\B(?=(\d{3})+(?!\d))/g,
          ","
        );
      });
    },
    async submit() {
      if (this.tableList.some((item) => item.debitAmount == 0)) {
        this.$message({
          message: "代偿款不能为0",
          type: "warning",
        });
        return;
      }
      this.innerValue=false;
      this.$emit("sure",this.tableList);
    },
  },
};
</script>
