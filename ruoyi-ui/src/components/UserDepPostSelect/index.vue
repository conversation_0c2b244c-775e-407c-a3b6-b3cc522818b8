<template>
  <div>
    <el-dialog
      append-to-body
      :title="titleShow[title]"
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      @opened="handlerOpen"
      @close="handlerClose"
      width="1240px"
    >
      <div class="pr-5">
        <el-tabs type="card">
          <el-tab-pane :label="tabListAll[title]"></el-tab-pane>
        </el-tabs>
        <div class="flex items-center">
          <el-input
            class="w-48 mr-5"
            v-model="queryParams.name"
            :placeholder="placeholderAll[title]"
            clearable
            @keyup.enter.native="handleQuery"
            @clear="handleQuery"
          ></el-input>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
          <el-checkbox v-model="isPartTime" v-if="title == 'user'" class="ml-8"
            >显示兼岗</el-checkbox
          >
          <div v-if="showSelect" class="ml-4">
            已选中: {{ multipleSelection.length }}人
          </div>
        </div>
        <div class="flex">
          <div class="w-1/4">
            <el-tree
              :key="treeKey"
              class="overflow-y-auto"
              style="max-height: 50vh"
              :data="deptOptions"
              :props="defaultProps"
              @node-click="handleNodeClick"
            />
          </div>
          <div class="w-3/4" :style="cssVars">
            <MyTable
              :key="tableKey"
              :rowKey="rowKey"
              :columns="columns"
              selectableType
              :source="configList"
              showCheckbox
              ref="multipleTable"
              @selection-change="handleSelectionChange"
            >
              <template #unitName="{ record }" v-if="title == 'user'">
                <div
                  v-for="item in userPostListAll.filter((item) => {
                    if (isPartTime) {
                      return item.userId === record.userId;
                    } else {
                      return (
                        item.userId === record.userId && item.homePost == '0'
                      );
                    }
                  })"
                >
                  {{ item.companyShortName }}
                </div>
              </template>
              <template #status="{ record }" v-if="title == 'user'">
                <div>
                  {{ record.status === "0" ? "正常" : "停用" }}
                </div>
              </template>
              <template #deptName="{ record }" v-if="title == 'user'">
                <div
                  v-for="item in userPostListAll.filter((item) => {
                    if (isPartTime) {
                      return item.userId === record.userId;
                    } else {
                      return (
                        item.userId === record.userId && item.homePost == '0'
                      );
                    }
                  })"
                >
                  {{ item.deptName }}
                </div>
              </template>
              <template #postName="{ record }" v-if="title == 'user'">
                <div
                  v-for="item in userPostListAll.filter((item) => {
                    if (isPartTime) {
                      return item.userId === record.userId;
                    } else {
                      return (
                        item.userId === record.userId && item.homePost == '0'
                      );
                    }
                  })"
                >
                  <div>
                    {{ item.postName }}
                    <span v-show="item.homePost == '0'">(主要)</span>
                  </div>
                </div>
              </template>
              <template #userList="{ record }" v-if="title == 'post'">
                <span v-if="record.userList && record.userList.length !== 0">
                  <template v-for="(item, index) in record.userList">
                    {{ item.nickName }}
                    <span v-bind:style="{ color: '#F56C6C' }">{{
                      item.status === "0" ? "" : "(停用)"
                    }}</span
                    >{{ index + 1 === record.userList.length ? "" : "," }}
                  </template>
                </span>
                <span v-if="record.userList && record.userList.length === 0">
                  -
                </span>
              </template>
            </MyTable>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getConfigList"
            />
          </div>
        </div>
      </div>

      <span slot="footer">
        <div class="flex justify-end">
          <el-button
            type="primary"
            @click="onSubmit"
            :disabled="!multipleSelection.length"
            >确定</el-button
          >
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import XEUtils from "xe-utils";
import { treeselect } from "@/api/system/dept";
import { queryUserInfoList } from "@/api/system/user";
import { listUser } from "@/api/system/user";
import { listDept,noPermissionDeptList } from "@/api/system/dept";
import { userPostSetList, listPost,noPermissionPostList } from "@/api/system/post";
import { groupUserInfoList } from "@/api/checkWork/groupingSet";
import { punishmentQueryUserInfoList } from "@/api/checkWork/bonusPenalty";

export default {
  name: "UserDepPostSelect",

  mixins: [vModelMixin],
  props: {
    showSelect: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    deptIdPro: {
      type: [String, Number],
      default: "",
    },
    companyIdPro: {
      type: [String, Number],
      default: "",
    },
    title: {
      type: String,
      required: true,
      default: "",
    },
    rowKey: {
      type: String,
      required: false,
      default: "id",
    },
    multipleSelectionProp: {
      type: Array,
      default: () => [],
    },
    //是否是分组设置时选择分组人员
    isGrounpSearch: {
      type: Boolean,
      default: false,
    },
    //是否是奖惩时选择分组人员
    isBonusPenalty: {
      type: Boolean,
      default: false,
    },
    //是否点完确定后关闭弹窗
    isClose: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    cssVars() {
      return {
        "--multiple": this.multiple || "none",
      };
    },
  },
  data() {
    return {
      leftTree: [],
      userPostListAll: [],
      configList: [],
      columns: [],
      columnsUser: Object.freeze([
        { label: "姓名", prop: "nickName", minWidth: "180" },
        { label: "状态", key: "status", minWidth: "120" },
        { label: "公司", key: "unitName", minWidth: "180" },
        { label: "部门", key: "deptName", minWidth: "180" },
        { label: "岗位", key: "postName", minWidth: "220" },
      ]),
      columnsDep: Object.freeze([
        { label: "部门名称", prop: "deptName", minWidth: "180" },
        { label: "岗位数量", prop: "postCounts", minWidth: "180" },
      ]),
      columnsPost: Object.freeze([
        { label: "岗位编码", prop: "postCode", minWidth: "80" },
        { label: "岗位名称", prop: "postName", minWidth: "100" },
        { label: "人员", key: "userList", minWidth: "220" },
      ]),
      tabListAll: Object.freeze({
        user: "人员组织架构",
        dep: "部门组织架构",
        post: "岗位组织架构",
      }),
      titleShow: Object.freeze({
        user: "选择人员",
        dep: "选择部门",
        post: "选择岗位",
      }),
      placeholderAll: Object.freeze({
        user: "请输入人员姓名",
        dep: "请输入部门名称",
        post: "请输入岗位名称",
      }),
      queryParams: {
        name: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      treeId: undefined,
      isPartTime: false,
      defaultProps: Object.freeze({
        children: "children",
        label: "label",
      }),
      deptOptions: [],
      multipleSelection: [],
      treeKey: Math.random(),
      tableKey: Math.random(),
    };
  },

  watch: {
    rowKey() {
      this.tableKey = Math.random();
    },
  },
  mounted() {},
  methods: {
    init() {
      this.getTreeselect();
    },
    async getTreeselect() {
      let params = {};
      params.deptId = this.deptIdPro || undefined;
      params.unitId = this.companyIdPro || undefined;
      const { data } = await treeselect(params);
      this.deptOptions = data;
    },
    async handlerOpen() {
      this.init();
      await this.getUserPostSetListF();
      this.getColumns();
      this.setCheck();
      await this.getConfigList();
    },
    /**查询用户岗位公司部门集合 */
    async getUserPostSetListF() {
      if (this.title == "user") {
        const { data } = await userPostSetList();
        this.userPostListAll = data;
      }
    },
    reset() {
      this.queryParams = {
        name: undefined,
        pageNum: 1,
        pageSize: 10,
      };
      this.multipleSelection = [];
      this.treeId = undefined;
      this.treeKey = Math.random();
      this.$nextTick(() => {
        this.$refs.multipleTable.clearSelection();
      });
    },
    getColumns() {
      const obj = {
        user: this.columnsUser,
        dep: this.columnsDep,
        post: this.columnsPost,
      };
      this.columns = obj[this.title];
    },
    async getConfigListUser() {
      const params = {
        ...this.queryParams,
        deptId: this.deptIdPro || this.treeId,
        nickName: this.queryParams.name,
        status: 0,
        homePost: this.deptIdPro || this.companyIdPro ? 0 : undefined,
      };
      if (this.isGrounpSearch) {
        const { rows, total } = await groupUserInfoList(params);
        return { rows, total };
      }else if(this.isBonusPenalty){
        const { rows, total } = await punishmentQueryUserInfoList(params);
        return { rows, total };
      } else {
        //没进行权限校验
        const { rows, total } = await queryUserInfoList(params);
        //进行权限校验
        // const { rows, total } = await listUser(params);
        return { rows, total };
      }
    },
    async getConfigListDep() {
      const params = {
        ...this.queryParams,
        parentId: this.treeId,
        deptName: this.queryParams.name,
      };
      // const { rows, total } = await listDept(params);

      const { rows, total } = await noPermissionDeptList(params);
      return { rows, total };
    },
    async getConfigListPost() {
      const params = {
        ...this.queryParams,
        deptId: this.treeId,
        postName: this.queryParams.name,
      };
      //不需要权限校验
      // const { rows, total } = await listPost(params);
      const { rows, total } = await noPermissionPostList(params);
      
      return { rows, total };
    },
    async getConfigList() {
      return new Promise(async (resolve) => {
        const obj = {
          user: this.getConfigListUser,
          dep: this.getConfigListDep,
          post: this.getConfigListPost,
        };
        const { rows, total } = await obj[this.title]();
        this.configList = rows;
        this.total = total;
        resolve();
      });
    },
    setCheck() {
      this.multipleSelection = XEUtils.clone(this.multipleSelectionProp, true);
      this.$nextTick(() => {
        this.multipleSelection.forEach((item) => {
          this.$refs.multipleTable.toggleRowSelection(item, true);
        });
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getConfigList();
    },
    resetQuery() {
      this.queryParams.name = undefined;
      this.treeId = undefined;
      this.handleQuery();
    },
    handleNodeClick(value) {
      this.treeId = value.id;
      this.getConfigList();
    },
    handleSelectionChange(selection) {
      if (!this.multiple) {
        if (selection.length > 1) {
          selection.shift();
          this.$refs.multipleTable.clearSelection();
          this.$refs.multipleTable.toggleRowSelection(selection[0]);
          this.multipleSelection = selection;
        } else {
          this.multipleSelection = selection;
        }
      } else {
        this.multipleSelection = selection;
      }
    },
    onSubmit() {
      const obj = {
        user: () => {
          this.$emit("on-submit-success-user", this.multipleSelection);
          if (this.isClose) {
            this.innerValue = false; 
          }
        },
        dep: () => {
          this.$emit("on-submit-success-dep", this.multipleSelection);
          if (this.isClose) {
            this.innerValue = false; 
          }
        },
        post: () => {
          this.$emit("on-submit-success-post", this.multipleSelection);
          if (this.isClose) {
            this.innerValue = false; 
          }
        },
      };
      obj[this.title]();
    },
    handlerClose() {
      this.reset();
    },
  },
};
</script>
<style lang="scss" scoped>
/**找到表头那一行，然后把里面的复选框隐藏掉**/
::v-deep .el-table__header-wrapper .el-table__header .el-checkbox {
  display: var(--multiple);
}
</style>
