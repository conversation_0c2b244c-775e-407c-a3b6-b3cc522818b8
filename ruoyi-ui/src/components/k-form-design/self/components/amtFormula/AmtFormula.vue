<template>
  <a-config-provider :locale="locale">
    <a-form>
      <a-form-item label="计算方式">
        <Select :options="calculateTypes" v-model="options.calculateType"/>
      </a-form-item>
      <a-form-item v-if="options.calculateType==='1'" label="显示类型">
        <Radio :options="numberTypes" v-model="options.selectType" />
      </a-form-item>
      <a-form-item v-if="options.calculateType !='3'" label="保留位数">
        <Input v-model="options.fixedNum"/>
      </a-form-item>
      <a-form-item v-if="options.calculateType==='1'" label="表达式">
        <Input v-model="options.formula"/>
        <Button v-text="'编辑'" type="primary" @click="openFormula('0')"></Button>
      </a-form-item>
      <a-form-item v-if="options.calculateType==='2' || options.calculateType==='3'" label="关联元素">
        <Select
          v-model="options.associatedElement"
          :options="options.associatedElements"
          :allowClear="options.clearable"
        />
        <span v-if="options.calculateType==='2'" style="color: red; font-size: 12px">温馨提示：必须是动态表格中的元素</span>
        <span v-if="options.calculateType==='3'" style="color: red; font-size: 12px">温馨提示：必须是日期元素</span>
      </a-form-item>
      <a-modal v-model="showFormula" title="编辑表达式" width="80%" :body-style="modalStyle">
        <Calculator :formula="options.formula" :resetFormula="resetFa" :datas="options.associatedElements" @input="getFormula"></Calculator>
        <template #footer>
          <Button type="primary" @click="openFormula('1')">确定</Button>
          <Button @click="resetFormula">重置</Button>
          <Button @click="closeFormula()">取消</Button>
        </template>
      </a-modal>
    </a-form>
  </a-config-provider>
</template>

<script>

import zhCN from "ant-design-vue/lib/locale-provider/zh_CN";
import Calculator from "./Calculator";
import {pluginManager} from "@/components/k-form-design/packages";

const Select = pluginManager.getComponent("select").component;
const Input = pluginManager.getComponent("input").component;
const Radio = pluginManager.getComponent("radio").component;
const Button = pluginManager.getComponent("aButton").component;
const modalStyle = {
  height: "500px",
  overflow: 'hidden',
  overflowY: 'scroll',
};

export default {
  name: "AmtFormula",
  props: ["options"],
  components: { Select, Input, Radio, Button, Calculator },
  data() {
    return {
      locale: zhCN,
      showFormula: false,
      resetFa: false,
      modalStyle,
      calculateTypes: [
        {
          value: "1",
          label: "公式"
        },
        {
          value: "2",
          label: "求和"
        }
      ],
      numberTypes: [
        {
          value: "1",
          label: "金额"
        },
        {
          value: "2",
          label: "数字"
        }
      ],
    };
  },
  methods: {
    /** 公式组件相关方法 */
    openFormula(type){
      if(type==0){
        this.resetFa = false;
        this.showFormula = true
      }else{
        this.options.formula = this.editFormula;
        this.showFormula = false;
      }
    },
    closeFormula(){
      this.showFormula = false;
      this.resetFa = !this.resetFa;
    },
    resetFormula(){
      this.resetFa = true;
    },
    getFormula(formula){
      this.editFormula = formula;
    }
  }
};
</script>
