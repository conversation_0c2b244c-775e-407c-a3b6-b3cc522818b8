<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1609652" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">拆分</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">撤销</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">重做</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">组件</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">编辑2</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8cd;</span>
                <div class="name">tabs</div>
                <div class="code-name">&amp;#xe8cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">LC_icon_edit_line_1</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72a;</span>
                <div class="name">edit</div>
                <div class="code-name">&amp;#xe72a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe83b;</span>
                <div class="name">edit-2</div>
                <div class="code-name">&amp;#xe83b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a5;</span>
                <div class="name">树型</div>
                <div class="code-name">&amp;#xe7a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">模型树</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">关联</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">edit-编辑</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">write-写</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">gallery-画廊</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">代码</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe693;</span>
                <div class="name">html5</div>
                <div class="code-name">&amp;#xe693;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">提示</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe633;</span>
                <div class="name">time-时间</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">number</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">menu-菜单</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">download-下载</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">upload-上传</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">folder-文件夹</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">image-图像</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">calendar-日历</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe676;</span>
                <div class="name">单选-粗线条</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">字号</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">button-add-按钮添加</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">button-remove-按钮删除</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66d;</span>
                <div class="name">多选</div>
                <div class="code-name">&amp;#xe66d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e6;</span>
                <div class="name">单行文本</div>
                <div class="code-name">&amp;#xe6e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e7;</span>
                <div class="name">多行文本</div>
                <div class="code-name">&amp;#xe6e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">单选 选中</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6da;</span>
                <div class="name">开关3</div>
                <div class="code-name">&amp;#xe6da;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">表格</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe674;</span>
                <div class="name">卡片</div>
                <div class="code-name">&amp;#xe674;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">栅格</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe640;</span>
                <div class="name">下拉2</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec7f;</span>
                <div class="name">分割线</div>
                <div class="code-name">&amp;#xec7f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe877;</span>
                <div class="name">评分_默认</div>
                <div class="code-name">&amp;#xe877;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1622099828259') format('woff2'),
       url('iconfont.woff?t=1622099828259') format('woff'),
       url('iconfont.ttf?t=1622099828259') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-chaifen"></span>
            <div class="name">
              拆分
            </div>
            <div class="code-name">.icon-chaifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chexiao"></span>
            <div class="name">
              撤销
            </div>
            <div class="code-name">.icon-chexiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanping"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.icon-quanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongzuo"></span>
            <div class="name">
              重做
            </div>
            <div class="code-name">.icon-zhongzuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zujian"></span>
            <div class="name">
              组件
            </div>
            <div class="code-name">.icon-zujian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji2"></span>
            <div class="name">
              编辑2
            </div>
            <div class="code-name">.icon-bianji2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tabs"></span>
            <div class="name">
              tabs
            </div>
            <div class="code-name">.icon-tabs
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-LC_icon_edit_line_1"></span>
            <div class="name">
              LC_icon_edit_line_1
            </div>
            <div class="code-name">.icon-LC_icon_edit_line_1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-edit1"></span>
            <div class="name">
              edit
            </div>
            <div class="code-name">.icon-edit1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-edit-"></span>
            <div class="name">
              edit-2
            </div>
            <div class="code-name">.icon-edit-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-juxingkaobei"></span>
            <div class="name">
              树型
            </div>
            <div class="code-name">.icon-juxingkaobei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tree"></span>
            <div class="name">
              模型树
            </div>
            <div class="code-name">.icon-tree
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanlian"></span>
            <div class="name">
              关联
            </div>
            <div class="code-name">.icon-guanlian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-edit"></span>
            <div class="name">
              edit-编辑
            </div>
            <div class="code-name">.icon-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-write"></span>
            <div class="name">
              write-写
            </div>
            <div class="code-name">.icon-write
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gallery"></span>
            <div class="name">
              gallery-画廊
            </div>
            <div class="code-name">.icon-gallery
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ai-code"></span>
            <div class="name">
              代码
            </div>
            <div class="code-name">.icon-ai-code
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-html"></span>
            <div class="name">
              html5
            </div>
            <div class="code-name">.icon-html
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zu"></span>
            <div class="name">
              提示
            </div>
            <div class="code-name">.icon-zu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-time"></span>
            <div class="name">
              time-时间
            </div>
            <div class="code-name">.icon-time
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-number"></span>
            <div class="name">
              number
            </div>
            <div class="code-name">.icon-number
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-menu"></span>
            <div class="name">
              menu-菜单
            </div>
            <div class="code-name">.icon-menu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-download"></span>
            <div class="name">
              download-下载
            </div>
            <div class="code-name">.icon-download
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-upload"></span>
            <div class="name">
              upload-上传
            </div>
            <div class="code-name">.icon-upload
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-folder"></span>
            <div class="name">
              folder-文件夹
            </div>
            <div class="code-name">.icon-folder
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-image"></span>
            <div class="name">
              image-图像
            </div>
            <div class="code-name">.icon-image
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-calendar"></span>
            <div class="name">
              calendar-日历
            </div>
            <div class="code-name">.icon-calendar
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-danxuan-cuxiantiao"></span>
            <div class="name">
              单选-粗线条
            </div>
            <div class="code-name">.icon-danxuan-cuxiantiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zihao"></span>
            <div class="name">
              字号
            </div>
            <div class="code-name">.icon-zihao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-button-add"></span>
            <div class="name">
              button-add-按钮添加
            </div>
            <div class="code-name">.icon-button-add
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-button-remove"></span>
            <div class="name">
              button-remove-按钮删除
            </div>
            <div class="code-name">.icon-button-remove
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duoxuan1"></span>
            <div class="name">
              多选
            </div>
            <div class="code-name">.icon-duoxuan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-danhangwenben"></span>
            <div class="name">
              单行文本
            </div>
            <div class="code-name">.icon-danhangwenben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duohangwenben"></span>
            <div class="name">
              多行文本
            </div>
            <div class="code-name">.icon-duohangwenben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-danxuanxuanzhong"></span>
            <div class="name">
              单选 选中
            </div>
            <div class="code-name">.icon-danxuanxuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kaiguan3"></span>
            <div class="name">
              开关3
            </div>
            <div class="code-name">.icon-kaiguan3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biaoge"></span>
            <div class="name">
              表格
            </div>
            <div class="code-name">.icon-biaoge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiapian"></span>
            <div class="name">
              卡片
            </div>
            <div class="code-name">.icon-qiapian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhage"></span>
            <div class="name">
              栅格
            </div>
            <div class="code-name">.icon-zhage
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiala"></span>
            <div class="name">
              下拉2
            </div>
            <div class="code-name">.icon-xiala
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fengexian"></span>
            <div class="name">
              分割线
            </div>
            <div class="code-name">.icon-fengexian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pingfen_moren"></span>
            <div class="name">
              评分_默认
            </div>
            <div class="code-name">.icon-pingfen_moren
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chaifen"></use>
                </svg>
                <div class="name">拆分</div>
                <div class="code-name">#icon-chaifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chexiao"></use>
                </svg>
                <div class="name">撤销</div>
                <div class="code-name">#icon-chexiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanping"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#icon-quanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongzuo"></use>
                </svg>
                <div class="name">重做</div>
                <div class="code-name">#icon-zhongzuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zujian"></use>
                </svg>
                <div class="name">组件</div>
                <div class="code-name">#icon-zujian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji2"></use>
                </svg>
                <div class="name">编辑2</div>
                <div class="code-name">#icon-bianji2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tabs"></use>
                </svg>
                <div class="name">tabs</div>
                <div class="code-name">#icon-tabs</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-LC_icon_edit_line_1"></use>
                </svg>
                <div class="name">LC_icon_edit_line_1</div>
                <div class="code-name">#icon-LC_icon_edit_line_1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-edit1"></use>
                </svg>
                <div class="name">edit</div>
                <div class="code-name">#icon-edit1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-edit-"></use>
                </svg>
                <div class="name">edit-2</div>
                <div class="code-name">#icon-edit-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-juxingkaobei"></use>
                </svg>
                <div class="name">树型</div>
                <div class="code-name">#icon-juxingkaobei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tree"></use>
                </svg>
                <div class="name">模型树</div>
                <div class="code-name">#icon-tree</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanlian"></use>
                </svg>
                <div class="name">关联</div>
                <div class="code-name">#icon-guanlian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-edit"></use>
                </svg>
                <div class="name">edit-编辑</div>
                <div class="code-name">#icon-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-write"></use>
                </svg>
                <div class="name">write-写</div>
                <div class="code-name">#icon-write</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gallery"></use>
                </svg>
                <div class="name">gallery-画廊</div>
                <div class="code-name">#icon-gallery</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ai-code"></use>
                </svg>
                <div class="name">代码</div>
                <div class="code-name">#icon-ai-code</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-html"></use>
                </svg>
                <div class="name">html5</div>
                <div class="code-name">#icon-html</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zu"></use>
                </svg>
                <div class="name">提示</div>
                <div class="code-name">#icon-zu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-time"></use>
                </svg>
                <div class="name">time-时间</div>
                <div class="code-name">#icon-time</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-number"></use>
                </svg>
                <div class="name">number</div>
                <div class="code-name">#icon-number</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-menu"></use>
                </svg>
                <div class="name">menu-菜单</div>
                <div class="code-name">#icon-menu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-download"></use>
                </svg>
                <div class="name">download-下载</div>
                <div class="code-name">#icon-download</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-upload"></use>
                </svg>
                <div class="name">upload-上传</div>
                <div class="code-name">#icon-upload</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-folder"></use>
                </svg>
                <div class="name">folder-文件夹</div>
                <div class="code-name">#icon-folder</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-image"></use>
                </svg>
                <div class="name">image-图像</div>
                <div class="code-name">#icon-image</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-calendar"></use>
                </svg>
                <div class="name">calendar-日历</div>
                <div class="code-name">#icon-calendar</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-danxuan-cuxiantiao"></use>
                </svg>
                <div class="name">单选-粗线条</div>
                <div class="code-name">#icon-danxuan-cuxiantiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zihao"></use>
                </svg>
                <div class="name">字号</div>
                <div class="code-name">#icon-zihao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-button-add"></use>
                </svg>
                <div class="name">button-add-按钮添加</div>
                <div class="code-name">#icon-button-add</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-button-remove"></use>
                </svg>
                <div class="name">button-remove-按钮删除</div>
                <div class="code-name">#icon-button-remove</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duoxuan1"></use>
                </svg>
                <div class="name">多选</div>
                <div class="code-name">#icon-duoxuan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-danhangwenben"></use>
                </svg>
                <div class="name">单行文本</div>
                <div class="code-name">#icon-danhangwenben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duohangwenben"></use>
                </svg>
                <div class="name">多行文本</div>
                <div class="code-name">#icon-duohangwenben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-danxuanxuanzhong"></use>
                </svg>
                <div class="name">单选 选中</div>
                <div class="code-name">#icon-danxuanxuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaiguan3"></use>
                </svg>
                <div class="name">开关3</div>
                <div class="code-name">#icon-kaiguan3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaoge"></use>
                </svg>
                <div class="name">表格</div>
                <div class="code-name">#icon-biaoge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiapian"></use>
                </svg>
                <div class="name">卡片</div>
                <div class="code-name">#icon-qiapian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhage"></use>
                </svg>
                <div class="name">栅格</div>
                <div class="code-name">#icon-zhage</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiala"></use>
                </svg>
                <div class="name">下拉2</div>
                <div class="code-name">#icon-xiala</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fengexian"></use>
                </svg>
                <div class="name">分割线</div>
                <div class="code-name">#icon-fengexian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pingfen_moren"></use>
                </svg>
                <div class="name">评分_默认</div>
                <div class="code-name">#icon-pingfen_moren</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
