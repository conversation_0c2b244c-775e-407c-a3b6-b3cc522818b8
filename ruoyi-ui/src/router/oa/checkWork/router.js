import Layout from "@/layout";

export default [
  {
    path: "/checkWorkOther",
    name: "CheckWorkOther",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "groupingSet",
        component: () =>
          import("@/views/checkWork/logSearch/groupingSet/index"),
        name: "GroupingSet",
        meta: {
          title: "快捷分组设置",
          activeMenu: "/checkWork/logSearch",
          noCache: true,
        },
      },
      {
        path: "bonusPenaltyAdd/:id",
        component: () =>
          import("@/views/checkWork/bonusPenalty/components/Add"),
        name: "BonusPenaltyAdd",
        meta: {
          title: "新增奖惩申请",
          activeMenu: "/checkWork/bonusPenalty",
          noCache: true,
        },
      },
      {
        path: "goErrandAdd/:id",
        component: () => import("@/views/checkWork/goErrand/components/Add"),
        name: "EoErrandAdd",
        meta: {
          title: "新增出差申请",
          activeMenu: "/checkWork/goErrand",
          noCache: true,
        },
      },
      {
        path: "goErrandView/:id",
        component: () => import("@/views/checkWork/goErrand/components/View"),
        name: "EoErrandView",
        meta: {
          title: "出差申请详情",
          activeMenu: "/checkWork/goErrand",
          noCache: true,
        },
      },
    ],
  },
];
