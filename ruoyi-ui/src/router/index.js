import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/** 引入子路由 */
import {importAllFn} from '@/utils/routerImportAll'
const routeList = importAllFn(require.context('../router', true, /router\.js$/))
const oaRouteList = importAllFn(require.context('../router/oa', true, /router\.js$/))
const debtConversionRouteList = importAllFn(require.context('../router/debtConversion', true, /router\.js$/))

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/cdlbprojectlbckb',
    name: 'Cdlbprojectlbckb',
    component: () => import('@/views/cdlb/cdlbprojectlbckb'),
  },

  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/addProject',
    component: () => import('@/views/system/project/addProject'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/oaWork/homepage/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      },
      {
        path: '/organizationalAll',
        name: 'OrganizationalAll',
        component: () => import('@/views/organizationalAll'),
        meta: { title: '全公司组织架构'},
        hidden: true
      },
      {
        path: '/organizational',
        name: 'Organizational',
        component: () => import('@/views/organizational'),
        meta: { title: '组织框架图' },
        hidden: true
      },
      {
        path: '/commonNotifyModule/:id',
        name: 'CommonNotifyModule',
        component: () => import('@/views/commonNotifyModule'),
        meta: { title: '通知详情',noCache: true},
        hidden: true
      },
      {
        path: '/noticeList',
        name: 'NoticeList',
        component: () => import('@/views/noticeList'),
        meta: { title: '通知公告列表',noCache: true,activeMenu: "/index"},
        hidden: true
      },
      {
        path: '/noticeListDetail/:id',
        name: 'NoticeListDetail',
        component: () => import('@/views/noticeList/components/detail'),
        meta: { title: '通知公告详情',noCache: true,activeMenu: "/index"},
        hidden: true
      },
      {
        path: '/xmgl/addProject',
        name: 'AddProject',
        component: () => import('@/views/xmgl/addProject/index'),
        meta: { title: '新增立项项目',noCache: true},
        hidden: true
      },
      {
        path: '/fileManagement/contractFileDetail',
        name: 'ContracFileDetail',
        component: () => import('@/views/fileManagement/contractFile/detail'),
        meta: { title: '合同档案详情', activeMenu: "/fileManagement/contractFile",noCache: true},
        hidden: true
      },
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      },
      {
        path: 'userDetail',
        component: () => import('@/views/system/userDetail/index'),
        name: 'UserDetail',
        meta: { title: '用户详情', icon: 'user' }
      }
    ]
  },
  ...oaRouteList,
  ...debtConversionRouteList
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  },

  ...routeList
]

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
