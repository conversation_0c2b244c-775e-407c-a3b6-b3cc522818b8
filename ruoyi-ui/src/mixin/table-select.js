export default {
  data() {
    return {
      tableDataCancel: [],
      configListAll: [],
      tableData: [],
    };
  },
  methods: {
    addTableAll(rows) {
      this.configListAll = [...this.configListAll, ...rows];
      this.configListAll = this.configListAll.reduce((accumulator, current) => {
        const duplicate = accumulator.find((item) => item.id === current.id);
        if (!duplicate) {
          return accumulator.concat([current]);
        }
        return accumulator;
      }, []);
    },
    cancelTableSelect(value) {
      this.multipleSelection = this.multipleSelection.filter(
        (item) => !value.includes(item.id)
      );
      const tableData = this.configList.filter((item) =>
        value.includes(item.id)
      );
      this.$nextTick(() => {
        tableData.forEach((item) => {
          this.$refs.multipleTable.toggleRowSelection(item, false);
        });
      });
    },
    selectTable() {
      this.tableDataCancel = [];
      this.tableData = this.configListAll.filter((item) =>
        this.ids.includes(item.id)
      );
    },
  },
};
