import BonusPenalty from "@/views/oaWork/updateProcessForm/components/bonusPenalty";
import { getProcessIdPunishment, unpassRewardPunishment,punishmentList } from "@/api/checkWork/bonusPenalty";

export default {
  components: {
    BonusPenalty,
  },
  data() {
    return {};
  },
  methods: {
    async initCheckWorkBonusPenalty() {
      const id = this.$route.query.businessId || this.$route.query.oid;
      const { data } = await getProcessIdPunishment({ processId: id });
      if(data)this.checkWork.checkWorkBonusPenalty=data;
    },
    async initCheckWorkBonusPenaltyList() {
      const id = this.$route.query.businessId || this.$route.query.oid;
      const { rows } = await punishmentList({ processId: id });
      if(rows)this.checkWork.checkWorkBonusPenaltyList=rows;
    },
    async unpassChekcWorkBonusPenalty(){
      if (Object.keys(this.checkWork.checkWorkBonusPenalty).length) {
       await unpassRewardPunishment({ids:[this.checkWork.checkWorkBonusPenalty.id]});
     }
   },
    async unpassChekcWorkBonusPenaltyList(){
      if (Object.keys(this.checkWork.checkWorkBonusPenaltyList).length) {
       await unpassRewardPunishment({ids:this.checkWork.checkWorkBonusPenaltyList.map(item=>item.id)});
     }
   },
  },
};
