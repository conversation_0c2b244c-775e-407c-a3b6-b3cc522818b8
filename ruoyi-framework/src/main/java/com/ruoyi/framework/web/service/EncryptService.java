package com.ruoyi.framework.web.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.SecretStatus;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.ruoyi.core.constant.DataConstants;
import org.ruoyi.core.domain.DSecretKey;
import org.ruoyi.core.tool.AESUtils;
import org.ruoyi.core.tool.RSAUtils;
import org.ruoyi.core.tool.Sm2CreateKeyUtils;
import org.ruoyi.core.tool.Sm2Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 加密解密方法
 *
 * <AUTHOR>
 * 2022-03-09
 */
@Component
public class EncryptService {
    // 日志工厂
    public static final Logger LOGGER = LoggerFactory.getLogger(EncryptService.class);
    @Resource
    private RedisTemplate<String, Integer> redisTemplate;

    // 初始化的 Key 大小
    public static final Integer INIT_KEY_SIZE = 10001;

    // 非对称加密 AES 秘钥长度
    public static final int AES_INIT_SIZE = 128;
    public static final String AES_WORK_TYPE = "AES/ECB/PKCS5Padding";

    /**
     * 通用加密算法
     *
     * @param keyCode  查询redis中的代码code
     * @param pushCode 请求代码
     * @param jsonData json数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String, Object> commonToEncrypt(String keyCode, String pushCode, String jsonData) {
        LOGGER.info("EncryptService类 commonToEncrypt方法请求参数 keyCode:{},pushCode:{},jsonData:{}", keyCode, pushCode, jsonData);
        if (jsonData.isEmpty()) {
            return setMapParam(SecretStatus.STATUS001001.getCode(), SecretStatus.STATUS001001.getInfo(), "");
        }

        DSecretKey dSecretKey = getRedisData(keyCode);
        if (dSecretKey == null) {
            return setMapParam(SecretStatus.STATUS001004.getCode(), SecretStatus.STATUS001004.getInfo(), "");
        }

        String privateKey = null;
        String publicKey = null;
        String secretKey = null;


        // Redis 存储的私钥
        if (!Strings.isNullOrEmpty(dSecretKey.getPrivateKey())) {
            privateKey = dSecretKey.getPrivateKey();
        }

        // Redis 存储的公钥
        if (!Strings.isNullOrEmpty(dSecretKey.getPublicKey())) {
            publicKey = dSecretKey.getPublicKey();
        }
        // Redis 中存储的秘钥
        if (!Strings.isNullOrEmpty(dSecretKey.getSecretKey())) {
            secretKey = dSecretKey.getSecretKey();
        }
        // 秘钥算法
        String keyAlgorithm = dSecretKey.getKeyAlgorithm();
        // 字符编码
        String charSet = dSecretKey.getKeyCharset();
        // 工作方式
        String workType = dSecretKey.getKeySignatureAlgorithm();
        // 对称加密或者非对称加密
        String keyType = dSecretKey.getKeyType();

        Map<String, Object> result = requestCommon(secretKey, publicKey, privateKey, pushCode, null, jsonData, null, null, null, keyType, keyAlgorithm, charSet, workType, DataConstants.ENCRYPTION_METHOD);//检查接收数据是否为空
        if (result != null) {
            return result;
        }
        // 加密后的数据
        String encryptData = null;
        // 加密的秘钥
        String encryptAESKey = null;
        // 签名
        String sign1 = null;
        String sign2 = null;
        String sign3 = null;
        try {
            if (DataConstants.NO_SYMMETRIC_ENCRYPTION.equals(keyType)) {
                // 生成初始化的 AES 秘钥
                String aesKey = org.ruoyi.core.tool.AESUtils.initSecretKey(AES_INIT_SIZE);
                secretKey = aesKey;
                // 非对称机密进行使用默认 AES 加密方式进行加密
                encryptData = org.ruoyi.core.tool.AESUtils.encrypt(jsonData, org.ruoyi.core.tool.AESUtils.toKey(secretKey), AES_WORK_TYPE, charSet);
            } else {
                // 对称加密使用AES 自定义的方式进行加密
                encryptData = org.ruoyi.core.tool.AESUtils.encrypt(jsonData, org.ruoyi.core.tool.AESUtils.toKey(secretKey), workType, charSet);
            }

        } catch (Exception e) {
            return setMapParam(SecretStatus.STATUS001002.getCode(), SecretStatus.STATUS001002.getInfo(), "");
        }
        if (Strings.isNullOrEmpty(encryptData)) {
            return setMapParam(SecretStatus.STATUS001019.getCode(), SecretStatus.STATUS001019.getInfo(), "");
        }
        // 非对称加密
        if (DataConstants.NO_SYMMETRIC_ENCRYPTION.equals(keyType)) {
            try {
                // 使用非对称加密进行加密 AES 秘钥
                encryptAESKey = secretEncrypt(keyAlgorithm, secretKey, privateKey, charSet, publicKey);
                if (Strings.isNullOrEmpty(encryptAESKey)) {
                    return setMapParam(SecretStatus.STATUS001006.getCode(), SecretStatus.STATUS001006.getInfo(), "");
                }
            } catch (NoSuchAlgorithmException e) {
                return setMapParam(SecretStatus.STATUS001003.getCode(), SecretStatus.STATUS001003.getInfo(), "");
            } catch (InvalidKeySpecException e) {
                return setMapParam(SecretStatus.STATUS001004.getCode(), SecretStatus.STATUS001004.getInfo(), "");
            } catch (UnsupportedEncodingException e) {
                return setMapParam(SecretStatus.STATUS001010.getCode(), SecretStatus.STATUS001010.getInfo(), "");
            } catch (Exception e) {
                return setMapParam(SecretStatus.STATUS002015.getCode(), SecretStatus.STATUS002015.getInfo(), "");

            }

            try {
                // 数据的整体签名
                sign1 = encryptedDataSignature(keyAlgorithm, jsonData, privateKey, workType, charSet);//私钥数字签名（对密文签名）
                if (Strings.isNullOrEmpty(sign1)) {
                    return setMapParam(SecretStatus.STATUS001007.getCode(), SecretStatus.STATUS001007.getInfo(), "");
                }
                sign2 = encryptedDataSignature(keyAlgorithm, encryptData, privateKey, workType, charSet);//私钥数字签名（对密文签名）
                if (Strings.isNullOrEmpty(sign2)) {
                    return setMapParam(SecretStatus.STATUS001008.getCode(), SecretStatus.STATUS001008.getInfo(), "");
                }
                sign3 = encryptedDataSignature(keyAlgorithm, pushCode, privateKey, workType, charSet);//私钥数字签名（对请求代码明文签名）
                if (Strings.isNullOrEmpty(sign2)) {
                    return setMapParam(SecretStatus.STATUS001009.getCode(), SecretStatus.STATUS001009.getInfo(), "");
                }
            } catch (UnsupportedEncodingException e) {
                return setMapParam(SecretStatus.STATUS001010.getCode(), SecretStatus.STATUS001010.getInfo(), "");
            }
            // 创建返回体
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("pushCode", pushCode);//请求代码
            dataMap.put("encryptAESKey", encryptAESKey);//加密秘钥
            dataMap.put("encryptData", encryptData);//加密数据
            dataMap.put("sign1", sign1);//签名1
            dataMap.put("sign2", sign2);//签名2
            dataMap.put("sign3", sign3);//签名3
            LOGGER.info("EncryptService类 commonToEncrypt方法响应参数 dataMap:{}", JSONObject.toJSONString(dataMap));
            return setMapParam(SecretStatus.STATUS000000.getCode(), SecretStatus.STATUS000000.getInfo(), dataMap);
            // 对称加密
        } else if (DataConstants.SYMMETRIC_ENCRYPTION.equals(keyType)) {
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("pushCode", pushCode);//请求代码
            dataMap.put("encryptAESKey", secretKey);//无需加密的 AES 秘钥
            dataMap.put("encryptData", encryptData);//加密数据
            LOGGER.info("EncryptService类 commonToEncrypt方法响应参数 dataMap:{}", JSONObject.toJSONString(dataMap));
            return setMapParam(SecretStatus.STATUS000000.getCode(), SecretStatus.STATUS000000.getInfo(), dataMap);
        }
        return setMapParam(SecretStatus.STATUS001005.getCode(), SecretStatus.STATUS001005.getInfo(), "");
    }


    /**
     * 公共的解密方式
     *
     * @param keyCode       查询redis中的代码code
     * @param pushCode      请求代码
     * @param encryptAESKey 非对称加密好的 AES私钥串 若对称加密则对称加密秘钥
     * @param encryptData   请求回来的加密数据
     * @param sign1         sign1
     * @param sign2         sign2
     * @param sign3         sign3
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String, Object> commonToDecrypt(String keyCode, String pushCode, String encryptData, String sign1, String sign2, String sign3, String encryptAESKey) {
        LOGGER.info("EncryptService类 commonToDecrypt方法请求参数 keyCode:{},pushCode:{},encryptData:{},sign1:{},sign2:{},sign3:{},encryptAESKey:{}", keyCode, pushCode, encryptData, sign1, sign2, sign3, encryptAESKey);

        DSecretKey dSecretKey = getRedisData(keyCode);
        if (dSecretKey == null) {
            return setMapParam(SecretStatus.STATUS001004.getCode(), SecretStatus.STATUS001004.getInfo(), "");
        }
        // 秘钥算法
        String keyAlgorithm = dSecretKey.getKeyAlgorithm();
        // 字符编码
        String charSet = dSecretKey.getKeyCharset();
        // 对称加密或者非对称加密
        String keyType = dSecretKey.getKeyType();
        // 工作模式
        String workType = dSecretKey.getKeySignatureAlgorithm();

        String privateKey = null;
        String publicKey = null;
        String secretKey = null;


        // Redis 存储的私钥
        if (!Strings.isNullOrEmpty(dSecretKey.getPrivateKey())) {
            privateKey = dSecretKey.getPrivateKey();
        }

        // Redis 存储的公钥
        if (!Strings.isNullOrEmpty(dSecretKey.getPublicKey())) {
            publicKey = dSecretKey.getPublicKey();
        }
        // Redis 中存储的秘钥
        if (!Strings.isNullOrEmpty(dSecretKey.getSecretKey())) {
            secretKey = dSecretKey.getSecretKey();
        }
        Map<String, Object> result = requestCommon(secretKey, publicKey, privateKey, pushCode, encryptAESKey, encryptData, sign1, sign2, sign3, keyType, keyAlgorithm, charSet, workType, DataConstants.DECRYPT_METHOD);//检查接收数据是否为空
        if (result != null) {
            return result;
        }

        //校验数据签名（密文）
        boolean flagForData = false;
        //校验数据签名（请求代码明文）
        boolean flagForPushCode = false;

        // 非对称解密
        // 非对称解密
        if (DataConstants.NO_SYMMETRIC_ENCRYPTION.equals(keyType)) {
            try {
                flagForData = verifySign(keyAlgorithm, encryptData, publicKey, sign2, workType, charSet);
                flagForPushCode = verifySign(keyAlgorithm, pushCode, publicKey, sign3, workType, charSet);
            } catch (UnsupportedEncodingException e) {
                return setMapParam(SecretStatus.STATUS002014.getCode(), SecretStatus.STATUS002014.getInfo(), "");
            }
            if (flagForData) {//校验数据签名（密文）
                if (flagForPushCode) {//校验数据签名（请求代码明文）
                    String aesKey = null;
                    try {
                        //动态使用方法进行解析 AES 的秘钥串
                        aesKey = getForAES(keyAlgorithm, publicKey, encryptAESKey, privateKey, charSet);
                        if (Strings.isNullOrEmpty(aesKey)) {
                            return setMapParam(SecretStatus.STATUS002017.getCode(), SecretStatus.STATUS002017.getInfo(), "");
                        }
                    } catch (Exception e) {
                        return setMapParam(SecretStatus.STATUS002002.getCode(), SecretStatus.STATUS002002.getInfo(), "");//解密时公钥对象转换失败！无效的公钥描述
                    }
                    String json_data = null;
                    boolean jsonFlag = false;
                    try {
                        // 使用 AES 解析
                        json_data = getForJsonData(keyAlgorithm, encryptData, aesKey, charSet, AES_WORK_TYPE);//AES解密数据
                        if (Strings.isNullOrEmpty(json_data)) {
                            return setMapParam(SecretStatus.STATUS002018.getCode(), SecretStatus.STATUS002018.getInfo(), "");//解密数据体为空！
                        }
                        jsonFlag = verifySign(keyAlgorithm, json_data, publicKey, sign1, workType, charSet);
                    } catch (Exception e) {
                        return setMapParam(SecretStatus.STATUS002003.getCode(), SecretStatus.STATUS002003.getInfo(), "");//解密时数据解密失败！
                    }
                    LOGGER.info("EncryptService类 commonToDecrypt方法响应参数 keyCode:{},pushCode:{},encryptData:{},sign1:{},sign2:{},sign3:{},encryptAESKey:{},dSecretKey:{},flagForData:{},flagForPushCode:{},aesKey:{},json_data:{},jsonFlag:{}", keyCode, pushCode, encryptData, sign1, sign2, sign3, encryptAESKey, JSONObject.toJSONString(dSecretKey), flagForData, flagForPushCode, aesKey, json_data, jsonFlag);
                    if (jsonFlag) {//校验数据签名（明文）
                        return setMapParam(SecretStatus.STATUS000000.getCode(), SecretStatus.STATUS000000.getInfo(), pushCode, json_data);
                    } else {
                        return setMapParam(SecretStatus.STATUS002004.getCode(), SecretStatus.STATUS002004.getInfo(), "");//明文校验失败
                    }
                } else {
                    return setMapParam(SecretStatus.STATUS002005.getCode(), SecretStatus.STATUS002005.getInfo(), "");//请求代码校验失败
                }
            } else {
                return setMapParam(SecretStatus.STATUS002006.getCode(), SecretStatus.STATUS002006.getInfo(), "");//密文校验失败
            }
        } else if (DataConstants.SYMMETRIC_ENCRYPTION.equals(keyType)) {
            try {
                // 对称加密算法解密
                String decryptedData = getForSymmetricalSecret(keyAlgorithm, charSet, secretKey, encryptData, workType);
                LOGGER.info("EncryptService类 commonToDecrypt方法响应参数 keyCode:{},pushCode:{},encryptData:{},sign1:{},sign2:{},sign3:{},encryptAESKey:{},dSecretKey:{},flagForData:{},flagForPushCode:{},decryptedData:{}", keyCode, pushCode, encryptData, sign1, sign2, sign3, encryptAESKey, JSONObject.toJSONString(dSecretKey), flagForData, flagForPushCode, decryptedData);

                return setMapParam(SecretStatus.STATUS000000.getCode(), SecretStatus.STATUS000000.getInfo(), decryptedData);
            } catch (Exception e) {
                return setMapParam(SecretStatus.STATUS002001.getCode(), SecretStatus.STATUS002001.getInfo(), "");//解密时没有找到算法
            }
        }
        return setMapParam(SecretStatus.STATUS002019.getCode(), SecretStatus.STATUS002019.getInfo(), "");

    }


    /**
     * 获取分布式Id
     *
     * @param key 对应系统编码请求
     * @return 自增后的 ID
     */
    public Long getPushCodeByRedis(String key) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        Object cacheObject = redisCache.getCacheObject(key);
        if ( null == cacheObject ) {
            synchronized (EncryptService.class) {
                if ( null == cacheObject) {
                    redisTemplate.opsForValue().set(key,INIT_KEY_SIZE);
                }
            }
        }
        return redisTemplate.opsForValue().increment(key,1);
    }

    /**
     * 获取 Redis 数据
     *
     * @param keyCode 关键代码
     * @return {@link DSecretKey}
     */
    private DSecretKey getRedisData(String keyCode) {
        // 获取缓存中的秘钥数据
        Object cacheObject = SpringUtils.getBean(RedisCache.class).getCacheObject(Constants.SECRET_CODE_KEY + keyCode);
        DSecretKey dSecretKey = StringUtils.cast(cacheObject);
        return dSecretKey;
    }

    /**
     * 加密数据签名
     *
     * @param keyAlgorithm 算法
     * @param data         数据
     * @param privateKey   私钥
     * @param workType     工作模式
     * @param charSet      字符编码
     * @return {@link String}
     * @throws UnsupportedEncodingException 不支持编码异常
     */
    private String encryptedDataSignature(String keyAlgorithm, String data, String privateKey, String workType, String charSet) throws UnsupportedEncodingException {
        switch (keyAlgorithm) {
            case "RSA":
                return RSAUtils.sign(data, privateKey, workType, charSet);//私钥数字签名（对明文签名）
            case "SM2":
                return Base64.getUrlEncoder().encodeToString(Sm2Utils.signByPrivateKey(data.getBytes(charSet), Sm2CreateKeyUtils.createPrivateKey(privateKey), workType));
            default:
                return null;
        }
    }

    /**
     * 加密 AES 秘钥
     *
     * @param keyAlgorithm  秘钥算法
     * @param asymmetricKey 默认 用于加密非对称机密 AES 秘钥
     * @param privateKey    使用的钥匙
     * @param charSet       字符编码
     * @param publicKey     公钥
     * @return 加密好的 AES 秘钥串
     */
    private String secretEncrypt(String keyAlgorithm, String asymmetricKey, String privateKey, String charSet, String publicKey) throws Exception {
        switch (keyAlgorithm) {
            case "RSA":
                return RSAUtils.privateEncrypt(asymmetricKey, RSAUtils.getPrivateKey(privateKey), charSet);
            case "SM2":
                return Base64.getUrlEncoder().encodeToString(Sm2Utils.encrypt(asymmetricKey.getBytes(charSet), Sm2CreateKeyUtils.createPublicKey(publicKey)));
            default:
                return null;
        }

    }

    /**
     * 验证签名
     *
     * @param keyAlgorithm 算法
     * @param data         加密好数据解密
     * @param publicKey    公钥
     * @param sign2        sign2
     * @param workType     工作模式
     * @param charSet      字符集
     * @return boolean
     * @throws UnsupportedEncodingException 不支持编码异常
     */
    private boolean verifySign(String keyAlgorithm, String data, String publicKey, String sign2, String workType, String charSet) throws UnsupportedEncodingException {
        switch (keyAlgorithm) {
            case "RSA":
                return RSAUtils.verify(data, publicKey, sign2, workType, charSet);
            case "SM2":
                return Sm2Utils.verifyByPublicKey(data.getBytes(charSet), Sm2CreateKeyUtils.createPublicKey(publicKey), Base64.getUrlDecoder().decode(sign2.getBytes(charSet)), workType);
            default:
                return false;
        }
    }

    /**
     * 解析 AES 加密的数据
     *
     * @param keyAlgorithm 算法
     * @param publicKey    公钥
     * @param key          加密好的AES的钥匙
     * @param privateKey   私钥
     * @param charset      字符集
     * @return {@link String}
     * @throws Exception 异常
     */
    private String getForAES(String keyAlgorithm, String publicKey, String key, String privateKey, String charset) throws Exception {
        switch (keyAlgorithm) {
            case "RSA":
                return RSAUtils.publicDecrypt(key, RSAUtils.getPublicKey(publicKey), charset);
            case "SM2":
                return new String(Sm2Utils.decrypt(Base64.getUrlDecoder().decode(key.getBytes(charset)), Sm2CreateKeyUtils.createPrivateKey(new String(privateKey))));//SM2解密AES秘钥
            default:
                return "";
        }
    }

    /**
     * 使用 AES 算法进行解析数据
     *
     * @param keyAlgorithm 算法
     * @param data         数据
     * @param aesKey       解密算法解析出来的秘钥串
     * @param charSet      字符编码
     * @return {@link String}
     * @throws Exception 异常
     */
    private String getForJsonData(String keyAlgorithm, String data, String aesKey, String charSet, String workType) throws Exception {
        switch (keyAlgorithm) {
            case "RSA":
            case "SM2":
                return AESUtils.decrypt(data, AESUtils.toKey(aesKey), workType, charSet);//AES解密数据
            default:
                return "";
        }
    }


    /**
     * 对称机密 解析
     *
     * @param keyAlgorithm 算法
     * @param charSet      字符编码
     * @param secretKey    秘密密钥
     * @param key          加密好的 AES私钥串
     * @return {@link String}
     * @throws Exception 异常
     */
    private String getForSymmetricalSecret(String keyAlgorithm, String charSet, String secretKey, String key, String workType) throws Exception {
        switch (keyAlgorithm) {
            case "AES":
                return AESUtils.decrypt(key, AESUtils.toKey(secretKey), workType, charSet);
            default:
                return "";
        }
    }

    /**
     * 检查接收数据是否为空
     *
     * @param secretKey     秘钥
     * @param publicKey     公钥
     * @param privateKey    私钥
     * @param push_code     请求代码
     * @param encryptAESKey 非对称加密完成的AES秘钥,对称加密为null
     * @param encryptData   用于加密的话是原始数据，解密的情况是用AES加密好的秘钥串
     * @param sign1         签名
     * @param sign2         签名
     * @param sign3         签名
     * @param encryptType   加密类型(对称和非对称)
     * @param keyAlgorithm  加密算法（AES、RSA、SM2）
     * @param charSet       字符编码
     * @param workType      工作模式
     * @param purpose       用途（用于加密 或者 用于解密 区分值）
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    private Map<String, Object> requestCommon(String secretKey, String publicKey, String privateKey, String push_code, String encryptAESKey, String encryptData, String sign1, String sign2, String sign3, String encryptType, String keyAlgorithm, String charSet, String workType, String purpose) {

        if (Strings.isNullOrEmpty(encryptType)) {
            return setMapParam(SecretStatus.STATUS003004.getCode(), SecretStatus.STATUS003004.getInfo(), "");
        }

        if (Strings.isNullOrEmpty(purpose)) {
            return setMapParam(SecretStatus.STATUS003005.getCode(), SecretStatus.STATUS003005.getInfo(), "");
        }

        if (DataConstants.SYMMETRIC_ENCRYPTION.equals(encryptType)) {
            if (DataConstants.DECRYPT_METHOD.equals(purpose)) {
                if (Strings.isNullOrEmpty(push_code)) {
                    return setMapParam(SecretStatus.STATUS002008.getCode(), SecretStatus.STATUS002008.getInfo(), "");
                }//请求代码未设置
                if (Strings.isNullOrEmpty(secretKey)) {
                    return setMapParam(SecretStatus.STATUS002009.getCode(), SecretStatus.STATUS002009.getInfo(), "");
                }//AES秘钥未设置
                if (Strings.isNullOrEmpty(encryptData)) {
                    return setMapParam(SecretStatus.STATUS002010.getCode(), SecretStatus.STATUS002010.getInfo(), "");
                }//数据未设置
            }
            if (DataConstants.ENCRYPTION_METHOD.equals(purpose)) {
                if (Strings.isNullOrEmpty(push_code)) {
                    return setMapParam(SecretStatus.STATUS001011.getCode(), SecretStatus.STATUS001011.getInfo(), "");
                }//请求代码未设置
                if (Strings.isNullOrEmpty(secretKey)) {
                    return setMapParam(SecretStatus.STATUS001012.getCode(), SecretStatus.STATUS001012.getInfo(), "");
                }//AES秘钥未设置
                if (Strings.isNullOrEmpty(encryptData)) {
                    return setMapParam(SecretStatus.STATUS002010.getCode(), SecretStatus.STATUS002010.getInfo(), "");
                }//数据未设置
            }
        } else if (DataConstants.NO_SYMMETRIC_ENCRYPTION.equals(encryptType)) {
            if (DataConstants.DECRYPT_METHOD.equals(purpose)) {
                if (Strings.isNullOrEmpty(publicKey)) {
                    return setMapParam(SecretStatus.STATUS002007.getCode(), SecretStatus.STATUS002007.getInfo(), "");
                }//瑞信公钥未设置
                if (Strings.isNullOrEmpty(privateKey)) {
                    return setMapParam(SecretStatus.STATUS002016.getCode(), SecretStatus.STATUS002016.getInfo(), "");
                }//私钥未设置
                if (Strings.isNullOrEmpty(push_code)) {
                    return setMapParam(SecretStatus.STATUS002008.getCode(), SecretStatus.STATUS002008.getInfo(), "");
                }//请求代码未设置
                if (Strings.isNullOrEmpty(encryptAESKey)) {
                    return setMapParam(SecretStatus.STATUS002009.getCode(), SecretStatus.STATUS002009.getInfo(), "");
                }//AES秘钥未设置
                if (Strings.isNullOrEmpty(encryptData)) {
                    return setMapParam(SecretStatus.STATUS002010.getCode(), SecretStatus.STATUS002010.getInfo(), "");
                }//数据未设置
                if (Strings.isNullOrEmpty(sign1)) {
                    return setMapParam(SecretStatus.STATUS002011.getCode(), SecretStatus.STATUS002011.getInfo(), "");
                }//签名1未设置
                if (Strings.isNullOrEmpty(sign2)) {
                    return setMapParam(SecretStatus.STATUS002012.getCode(), SecretStatus.STATUS002012.getInfo(), "");
                }//签名2未设置
                if (Strings.isNullOrEmpty(sign3)) {
                    return setMapParam(SecretStatus.STATUS002013.getCode(), SecretStatus.STATUS002013.getInfo(), "");
                }//签名3未设置
            }
            if (DataConstants.ENCRYPTION_METHOD.equals(purpose)) {
                if (Strings.isNullOrEmpty(publicKey)) {
                    return setMapParam(SecretStatus.STATUS001015.getCode(), SecretStatus.STATUS001015.getInfo(), "");
                }//瑞信公钥未设置
                if (Strings.isNullOrEmpty(privateKey)) {
                    return setMapParam(SecretStatus.STATUS001016.getCode(), SecretStatus.STATUS001016.getInfo(), "");
                }//私钥未设置
                if (Strings.isNullOrEmpty(push_code)) {
                    return setMapParam(SecretStatus.STATUS001017.getCode(), SecretStatus.STATUS001017.getInfo(), "");
                }//请求代码未设置
                if (Strings.isNullOrEmpty(encryptData)) {
                    return setMapParam(SecretStatus.STATUS001018.getCode(), SecretStatus.STATUS001018.getInfo(), "");
                }//数据未设置
            }
        }
        if (Strings.isNullOrEmpty(keyAlgorithm)) {
            return setMapParam(SecretStatus.STATUS003001.getCode(), SecretStatus.STATUS003001.getInfo(), "");
        }

        if (Strings.isNullOrEmpty(charSet)) {
            return setMapParam(SecretStatus.STATUS003002.getCode(), SecretStatus.STATUS003002.getInfo(), "");
        }

        if (Strings.isNullOrEmpty(workType)) {
            return setMapParam(SecretStatus.STATUS003003.getCode(), SecretStatus.STATUS003003.getInfo(), "");
        }
        return null;
    }

    /**
     * 设置返回参数
     *
     * @param code 代码
     * @param msg  消息体积
     * @param data 数据
     * @return 返回数据
     */
    private static Map<String, Object> setMapParam(String code, String msg, Object data) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("code", code);
        resultMap.put("msg", msg);
        resultMap.put("data", data);
        return resultMap;
    }

    /**
     * 设置返回参数
     *
     * @param code     代码
     * @param msg      消息体积
     * @param data     数据
     * @param pushCode 请求代码
     * @return 返回数据
     */
    private static Map<String, Object> setMapParam(String code, String msg, String pushCode, Object data) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("code", code);
        resultMap.put("msg", msg);
        resultMap.put("pushCode", pushCode);
        resultMap.put("data", data);
        return resultMap;
    }


}