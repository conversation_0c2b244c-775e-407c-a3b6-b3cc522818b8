<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.financial.mapper.FinancialVoucherDetailsAuxiliaryMapper">
    <resultMap id="BaseResultMap" type="com.ruoyi.financial.domain.FinancialVoucherDetailsAuxiliary">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="voucher_details_id" jdbcType="INTEGER" property="voucherDetailsId"/>
        <result column="accounting_category_id" jdbcType="INTEGER" property="accountingCategoryId"/>
        <result column="accounting_category_details_id" jdbcType="INTEGER" property="accountingCategoryDetailsId"/>
        <association property="accountingCategoryDetails" javaType="com.ruoyi.financial.domain.FinancialAccountingCategoryDetails" column="accounting_category_details_id" select="com.ruoyi.financial.mapper.FinancialAccountingCategoryDetailsMapper.selectById"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, voucher_details_id, accounting_category_id, accounting_category_details_id
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into financial_voucher_details_auxiliary
        (voucher_details_id, accounting_category_id, accounting_category_details_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.voucherDetailsId,jdbcType=INTEGER}, #{item.accountingCategoryId,jdbcType=INTEGER},
            #{item.accountingCategoryDetailsId,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="selectByDetailsId" resultMap="BaseResultMap">
        select * from financial_voucher_details_auxiliary where voucher_details_id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByAccountBlock" resultMap="com.ruoyi.financial.mapper.FinancialAccountingCategoryDetailsMapper.BaseResultMap">
        select * from financial_accounting_category_details ffacd where ffacd.id in (
        select distinct ffvda.accounting_category_details_id
        from financial_voucher_details_auxiliary ffvda
        left join financial_voucher_details ffvd on ffvda.voucher_details_id = ffvd.id
        left join financial_voucher ffv on ffvd.voucher_id = ffv.id
        where ffv.account_sets_id=#{accountSetsId,jdbcType=INTEGER} and ffvda.accounting_category_id = #{auxiliaryId,jdbcType=INTEGER})
    </select>

    <select id="selectAccountBookDetails" resultType="com.ruoyi.financial.vo.VoucherDetailVo">
        select ffv.voucher_date,
        ffv.word,
        ffv.code,
        ffvd.voucher_id,
        ffvd.summary,
        ffvd.debit_amount,
        ffvd.credit_amount,
        ffs.balance_direction
        from financial_voucher_details ffvd
        left join financial_voucher ffv on ffvd.voucher_id = ffv.id
        left join financial_subject ffs on ffvd.subject_id = ffs.id
        left join financial_voucher_details_auxiliary ffvda on ffvd.id = ffvda.voucher_details_id
        where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
        and ffvda.accounting_category_id = #{auxiliaryId,jdbcType=INTEGER}
        and ffvda.accounting_category_details_id = #{auxiliaryItemId,jdbcType=INTEGER,jdbcType=INTEGER}
        and ffv.voucher_date between date_format(#{startDate,jdbcType=DATE},'%Y-%m-%d') and date_format(#{endDate,jdbcType=DATE},'%Y-%m-%d')
        order by ffvd.voucher_id
    </select>

    <select id="selectAccountBookStatistical" resultType="com.ruoyi.financial.vo.VoucherDetailVo">
        select
        <if test="auxiliaryItemId == null">
            ffvda.accounting_category_details_id detailsId,
        </if>
        sum(ffvd.debit_amount) debit_amount,
        sum(ffvd.credit_amount) credit_amount,
        ffs.balance_direction
        from financial_voucher_details ffvd
        left join financial_voucher ffv on ffvd.voucher_id = ffv.id
        left join financial_subject ffs on ffvd.subject_id = ffs.id
        left join financial_voucher_details_auxiliary ffvda on ffvd.id = ffvda.voucher_details_id
        where ffv.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffvd.voucher_id is not null
        and ffvda.accounting_category_id = #{auxiliaryId,jdbcType=INTEGER}
        <if test="auxiliaryItemId != null">
            and ffvda.accounting_category_details_id = #{auxiliaryItemId,jdbcType=INTEGER,jdbcType=INTEGER}
        </if>
        <if test="startDate != null and endDate != null">
            and ffv.voucher_date between date_format(#{startDate,jdbcType=DATE},'%Y-%m-%d') and date_format(#{endDate,jdbcType=DATE},'%Y-%m-%d')
        </if>
        <if test="startDate != null and endDate == null">
            and ffv.voucher_date >= date_format(#{startDate,jdbcType=DATE},'%Y-%m-%d')
        </if>
        <if test="startDate == null and endDate != null">
            and ffv.voucher_date <![CDATA[< ]]> date_format(#{endDate,jdbcType=DATE},'%Y-%m-%d')
        </if>
        <if test="auxiliaryItemId == null">
            group by ffvda.accounting_category_details_id
        </if>
    </select>

</mapper>