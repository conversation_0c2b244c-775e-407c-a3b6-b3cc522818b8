<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.financial.mapper.FinancialReportTemplateMapper">
    <resultMap id="BaseResultMap" type="com.ruoyi.financial.domain.FinancialReportTemplate">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId"/>
        <result column="template_key" jdbcType="VARCHAR" property="templateKey"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, account_sets_id, template_key, `type`
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into financial_report_template
        (`name`, account_sets_id, template_key, `type`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.name,jdbcType=VARCHAR}, #{item.accountSetsId,jdbcType=INTEGER}, #{item.templateKey,jdbcType=VARCHAR},
            #{item.type,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="selectReportItemInfo" resultType="com.ruoyi.financial.po.ReportTemplatePo">
        select frt.account_sets_id accountSetsId, fas.company_name accountSetsName, frt.id templateId, frti.id templateItemsId, frti.title from financial_report_template frt
        inner join financial_report_template_items frti on frt.id = frti.template_id
        inner join financial_account_sets fas on frt.account_sets_id = fas.id
        where frt.template_key = #{templateKey} and frti.title=#{title}
    </select>
</mapper>