<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.financial.mapper.FinancialUserMapper">
    <resultMap id="BaseResultMap" type="com.ruoyi.financial.domain.FinancialUser">
        <!--@mbg.generated-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId"/>
        <result column="account_sets_num" jdbcType="INTEGER" property="accountSetsNum"/>
        <result column="accounting_num" jdbcType="INTEGER" property="accountingNum"/>
        <result column="expiration_date" jdbcType="DATE" property="expirationDate"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, account_sets_id, account_sets_num, accounting_num, expiration_date
    </sql>

    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from financial_user
        where user_id = #{userId}
    </select>

    <update id="updateByUserId">
        update financial_user set account_sets_id = #{accountSetsId}
        where user_id = #{userId}
    </update>

    <select id="selectByAccountSetId" resultType="com.ruoyi.financial.vo.FinancialUserVo">
        select ffu.*,ffuas.role_type as role
        from financial_user ffu
        left join financial_user_account_sets ffuas on ffu.id = ffuas.user_id
        where ffuas.account_sets_id = #{accountSetsId,jdbcType=INTEGER}
    </select>
</mapper>