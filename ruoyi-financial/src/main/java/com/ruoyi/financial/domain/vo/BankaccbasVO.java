package com.ruoyi.financial.domain.vo;

import lombok.Data;

import java.util.List;

@Data
public class BankaccbasVO {

    //账号
    private String account;

    //账户名称
    private String accountname;

    //主键
    private String pk_bankaccbas;

    //银行账户编码
    private String accountcode;

    //收付属性
    private String arapprop;

    //集团账户
    private String groupaccount;

    //总分属性
    private String genebranprop;

    //开户日期
    private String accopendate;

    //账户类型
    private String acctype;

    //开户公司
    private String ownercorp;

    //银行类别
    private String pk_banktype;

    //公司
    private String pk_corp;

    //币种
    private String pk_currtype;

    //银行类别
    private String banktypecode;

    private List<BankaccbasVO> bankaccbasvo;
}
