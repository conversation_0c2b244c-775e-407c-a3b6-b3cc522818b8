package com.ruoyi.financial.domain;


import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 智慧财务系统关联用友U8C银行账户对象 financial_u8c_bankaccount
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
@Data
public class FinancialU8CBankaccount extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 智慧财务系统账套主键 */
    private Long accountId;

    //用友U8C账套主键
    private String pkGlorgbook;

    /** 智慧财务系统银行账户主键 */
    @Excel(name = "智慧财务系统银行账户主键")
    private Long traderId;

    /** 智慧财务系统银行账号 */
    @Excel(name = "智慧财务系统银行账号")
    private String accountNumber;

    //智慧财务系统银行账户
    private String bankOfDeposit;

    /** 用友U8C银行账户主键 */
    @Excel(name = "用友U8C银行账户主键")
    private String pkBankaccbas;

    /** 用友U8C银行账户 */
    @Excel(name = "用友U8C银行账户")
    private String u8cAccountName;

    /** 用友U8C银行账户编号 */
    @Excel(name = "用友U8C银行账户编号")
    private String u8cAccountCode;

    /** 用友U8C银行账号 */
    @Excel(name = "用友U8C银行账号")
    private String u8cAccount;

    /** 前一版本用友U8C银行账户编号 */
    @Excel(name = "前一版本用友U8C银行账户编号")
    private String u8cAccountCodeBefor;

    /** 前一版本用友U8C银行账户主键 */
    @Excel(name = "前一版本用友U8C银行账户主键")
    private String pkBankaccbasBefor;

    /** 前一版本用友U8C银行账号 */
    @Excel(name = "前一版本用友U8C银行账号")
    private String u8cAccountBefor;

    /** 前一版本用友U8C银行账户 */
    @Excel(name = "前一版本用友U8C银行账户")
    private String u8cAccountNameBefor;

    /** 状态（0正常 1删除） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=删除")
    private String state;

    /** 更新版本 */
    @Excel(name = "更新版本")
    private Integer version;

    /** 修改原因 */
    @Excel(name = "修改原因")
    private String updateReason;

    /** 关联方式（0系统自动关联，1人工关联） */
    @Excel(name = "关联方式", readConverterExp = "0=系统自动关联，1人工关联")
    private String relevanceType;
}
