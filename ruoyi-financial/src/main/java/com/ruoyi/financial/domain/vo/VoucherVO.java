package com.ruoyi.financial.domain.vo;

import lombok.Data;

import java.util.List;

@Data
public class VoucherVO {

    private String pk_corp;

    private String pk_glorgbook;

    private String pk_prepared;

    private String pk_vouchertype;

    /**
     * U8C凭证ID
     */
    private String pk_voucher;

    private Integer voucherId;

    private String pushStatus;

    /**
     * 凭证类型
     */
    private String vouchertype_code;

    private String u8cReturnMessage;

    /**
     * 制单日期
     */
    private String prepareddate;

    /**
     * 凭证号
     */
    private String no;

    /**
     * 凭证字+凭证号
     */
    private String wordCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    private Integer ruleId;

    private List<VoucherDetailVO> details;

    private List<VoucherVO> voucher;
}
