package com.ruoyi.financial.domain.vo;

import lombok.Data;

import java.util.List;

@Data
public class CubasdocVO {
    //客商名称
    private String custname;

    //客商简称
    private String custshortname;

    //客商主键
    private String pk_cubasdoc;

    //客商编码
    private String custcode;

    //客商类型(0 - 外部单位1 - 内部核算单位2 - 内部法人单位3 - 内部渠道成员)
    private String custprop;

    //所属账套(公司主键)
    private String pk_corp;

    //地区分类
    private String pk_areacl;

    //当前页码
    private Integer page_now;

    private String pk_corp1;

    //每页显示条数
    private Integer page_size;

    private CubasdocVO parentvo;

    private List<CubasdocVO> cbdocvo;

    private CubasdocVO custbasvo;
}
