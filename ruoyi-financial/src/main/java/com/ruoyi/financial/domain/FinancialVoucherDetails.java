package com.ruoyi.financial.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.kernel.model.entity</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年10月21日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Data
@TableName(value = "financial_voucher_details")
public class FinancialVoucherDetails implements Serializable{
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "voucher_id")
    private Integer voucherId;

    /**
     * 摘要
     */
    @TableField(value = "summary")
    private String summary;

    @TableField(value = "subject_id")
    private Integer subjectId;

    @TableField(value = "subject_name")
    private String subjectName;

    @TableField(value = "subject_code")
    private String subjectCode;


    @TableField(exist = false)
    private String balanceDirection;

    /**
     * 借方金额
     */
    @TableField(value = "debit_amount")
    private Double debitAmount;

    /**
     * 贷方金额
     */
    @TableField(value = "credit_amount")
    private Double creditAmount;

    /**
     * 辅助名称
     */
    @TableField(value = "auxiliary_title")
    private String auxiliaryTitle;

    /**
     * 数量
     */
    @TableField(value = "num")
    private Double num;

    /**
     * 单价
     */
    @TableField(value = "price")
    private Double price;

    @TableField(value = "account_sets_id")
    private Integer accountSetsId;

    /**
     * 期初累计借方
     */
    @TableField(value = "cumulative_debit")
    private Double cumulativeDebit;

    /**
     * 期初累计贷方
     */
    @TableField(value = "cumulative_credit")
    private Double cumulativeCredit;

    @TableField(value = "cumulative_debit_num")
    private Double cumulativeDebitNum;

    @TableField(value = "cumulative_credit_num")
    private Double cumulativeCreditNum;

    /**
     * 结转损益
     */
    @TableField(value = "carry_forward")
    private Boolean carryForward;

    /**
     * 推送U8C的新科目
     */
    @TableField(exist = false)
    private String newSubject;

    /**
     * 推送U8C的新科目--辅助核算
     */
    @TableField(exist = false)
    private String newSubjectToHS;

    /**
     * 银行简称
     */
    @TableField(exist = false)
    private String abbreviation;

    /**
     * 推送U8C的新科目编码
     */
    @TableField(exist = false)
    private String newSubjectCode;

    /**
     * 推送至用友U8C科目展示名称
     */
    @TableField(exist = false)
    private String newSubjectName;

    /**
     * 科目类型
     */
    @TableField(exist = false)
    private String type;

    /** 创建者 */
    @TableField(exist = false)
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    @TableField(exist = false)
    private String updateBy;

    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String projectName;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date updateTime;

    /**
     * 是否关联 1 关联银行账户  2 关联客商档案-外部单位(项目)  3 关联客商档案-外部单位(客商类型2，用于存放外部公司、用户)  4 关联客商档案-内部法人单位(用于存放内部公司)   0 不关联
     */
    @TableField(exist = false)
    private Integer isGL;

    //关联的银行账户
    @TableField(exist = false)
    private String accountNumber;

    @TableField(exist = false)
    private String accountName;

    @TableField(exist = false)
    private Integer projectId;

    @TableField(exist = false)
    private List<FinancialAccountingCategoryDetails> auxiliary = new ArrayList<>(0);

    @TableField(exist = false)
    private FinancialSubject subject;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_VOUCHER_ID = "voucher_id";

    public static final String COL_SUMMARY = "summary";

    public static final String COL_SUBJECT_ID = "subject_id";

    public static final String COL_SUBJECT_NAME = "subject_name";

    public static final String COL_SUBJECT_CODE = "subject_code";

    public static final String COL_DEBIT_AMOUNT = "debit_amount";

    public static final String COL_CREDIT_AMOUNT = "credit_amount";

    public static final String COL_AUXILIARY_TITLE = "auxiliary_title";

    public static final String COL_NUM = "num";

    public static final String COL_PRICE = "price";

    public static final String COL_ACCOUNT_SETS_ID = "account_sets_id";

    public static final String COL_CUMULATIVE_DEBIT = "cumulative_debit";

    public static final String COL_CUMULATIVE_CREDIT = "cumulative_credit";

    public static final String COL_CUMULATIVE_DEBIT_NUM = "cumulative_debit_num";

    public static final String COL_CUMULATIVE_CREDIT_NUM = "cumulative_credit_num";

    public static final String COL_CARRY_FORWARD = "carry_forward";
}
