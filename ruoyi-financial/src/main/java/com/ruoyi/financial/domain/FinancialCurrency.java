package com.ruoyi.financial.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "financial_currency")
public class FinancialCurrency implements Serializable{
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 编码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 汇率
     */
    @TableField(value = "exchange_rate")
    private Double exchangeRate;

    /**
     * 是否本位币
     */
    @TableField(value = "local_currency")
    private Boolean localCurrency;

    /**
     * 所属账套
     */
    @TableField(value = "account_sets_id")
    private Integer accountSetsId;

    public static final String COL_CODE = "code";

    public static final String COL_NAME = "name";

    public static final String COL_EXCHANGE_RATE = "exchange_rate";

    public static final String COL_LOCAL_CURRENCY = "local_currency";

    public static final String COL_ACCOUNT_SETS_ID = "account_sets_id";
}