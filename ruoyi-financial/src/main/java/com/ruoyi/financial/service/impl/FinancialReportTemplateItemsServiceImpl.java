package com.ruoyi.financial.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.financial.domain.FinancialReportTemplateItems;
import com.ruoyi.financial.domain.FinancialReportTemplateItemsFormula;
import com.ruoyi.financial.mapper.FinancialReportTemplateItemsFormulaMapper;
import com.ruoyi.financial.mapper.FinancialReportTemplateItemsMapper;
import com.ruoyi.financial.service.IFinancialReportTemplateItemsService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.kernel.service.impl</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年09月05日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Service
@AllArgsConstructor
public class FinancialReportTemplateItemsServiceImpl extends ServiceImpl<FinancialReportTemplateItemsMapper, FinancialReportTemplateItems> implements IFinancialReportTemplateItemsService {

    private FinancialReportTemplateItemsFormulaMapper formulaMapper;

    @Override
    public int batchInsert(List<FinancialReportTemplateItems> list) {
        return baseMapper.batchInsert(list);
    }

    @Override
    public List<FinancialReportTemplateItems> list(Wrapper<FinancialReportTemplateItems> queryWrapper) {
        QueryWrapper<FinancialReportTemplateItems> qw = (QueryWrapper) queryWrapper;
        qw.orderByAsc("pos");
        return super.list(qw);
    }

    @Override
    public boolean save(FinancialReportTemplateItems entity) {
        if (entity.getIsClassified() != null && entity.getIsClassified()) {
            entity.setLineNum(null);
            entity.setSources(null);
            entity.setType(null);
        }
        return super.save(entity);
    }

    /**
     * 保存公式信息
     *
     * @param formulas
     * @param accountSetsId
     */
    @Override
    @Transactional
    public void saveFormula(Integer templateItemsId, List<FinancialReportTemplateItemsFormula> formulas, Integer accountSetsId) {
        LambdaQueryWrapper<FinancialReportTemplateItemsFormula> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialReportTemplateItemsFormula::getAccountSetsId, accountSetsId);
        qw.eq(FinancialReportTemplateItemsFormula::getTemplateItemsId, templateItemsId);
        formulaMapper.delete(qw);

        if (formulas != null && formulas.size() > 0) {
            formulas.forEach(f -> f.setAccountSetsId(accountSetsId));
            formulaMapper.batchInsert(formulas);
        }
    }

    @Override
    public boolean update(FinancialReportTemplateItems entity, Wrapper<FinancialReportTemplateItems> updateWrapper) {
        if (entity.getIsClassified() != null && entity.getIsClassified()) {
            entity.setLineNum(null);
            entity.setSources(null);
            entity.setType(null);
        }

        FinancialReportTemplateItems templateItems = baseMapper.selectOne(updateWrapper);
        if (!templateItems.getSources().equals(entity.getSources())) {
            LambdaQueryWrapper<FinancialReportTemplateItemsFormula> qw = Wrappers.lambdaQuery();
            qw.eq(FinancialReportTemplateItemsFormula::getTemplateItemsId, entity.getId());
            formulaMapper.delete(qw);
        }
        return super.update(entity, updateWrapper);
    }
}




