package com.ruoyi.financial.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.financial.domain.FinancialVoucherWord;
import com.ruoyi.financial.mapper.FinancialVoucherWordMapper;
import com.ruoyi.financial.service.IFinancialVoucherWordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Service
public class FinancialVoucherWordServiceImpl extends ServiceImpl<FinancialVoucherWordMapper, FinancialVoucherWord> implements IFinancialVoucherWordService {

    @Override
    public int batchInsert(List<FinancialVoucherWord> list) {
        return baseMapper.batchInsert(list);
    }

    @Override
    @Transactional
    public boolean save(FinancialVoucherWord entity) {
        //判断是否重复
        LambdaQueryWrapper<FinancialVoucherWord> query = Wrappers.lambdaQuery();
        query.eq(FinancialVoucherWord::getWord, entity.getWord().trim());
        query.eq(FinancialVoucherWord::getAccountSetsId, entity.getAccountSetsId());

        if (this.count(query) > 0) {
            throw new ServiceException("亲，保存失败啦！凭证字【%s】已经存在！", entity.getWord());
        }
        boolean rs = super.save(entity);
        if (rs) {
            this.updateDefault(entity);
        }
        return rs;
    }

    @Override
    @Transactional
    public boolean update(FinancialVoucherWord entity, Wrapper<FinancialVoucherWord> updateWrapper) {
        boolean rs = baseMapper.update(entity, updateWrapper) > 0;
        if (rs) {
            this.updateDefault(entity);
        }
        return rs;
    }

    @Override
    public boolean remove(Wrapper<FinancialVoucherWord> wrapper) {
        FinancialVoucherWord word = this.getOne(wrapper);
        if (word.getIsDefault()) {
            throw new ServiceException("默认凭证字不能被删除！");
        }
        return super.remove(wrapper);
    }

    /**
     * 如果新增为默认，则把其他的都设置为非默认
     *
     * @param entity
     */
    private void updateDefault(FinancialVoucherWord entity) {
        LambdaQueryWrapper<FinancialVoucherWord> query;
        if (entity.getIsDefault()) {
            query = Wrappers.lambdaQuery();
            query.ne(FinancialVoucherWord::getId, entity.getId());
            query.eq(FinancialVoucherWord::getAccountSetsId, entity.getAccountSetsId());

            FinancialVoucherWord vw = new FinancialVoucherWord();
            vw.setIsDefault(false);
            baseMapper.update(vw, query);
        }
    }
}

