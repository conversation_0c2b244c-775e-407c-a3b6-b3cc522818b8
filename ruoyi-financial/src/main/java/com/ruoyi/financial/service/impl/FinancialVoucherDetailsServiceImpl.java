package com.ruoyi.financial.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.financial.domain.FinancialAccountingCategoryDetails;
import com.ruoyi.financial.domain.FinancialSubject;
import com.ruoyi.financial.domain.FinancialVoucherDetails;
import com.ruoyi.financial.domain.FinancialVoucherDetailsAuxiliary;
import com.ruoyi.financial.mapper.FinancialAccountingCategoryDetailsMapper;
import com.ruoyi.financial.mapper.FinancialSubjectMapper;
import com.ruoyi.financial.mapper.FinancialVoucherDetailsAuxiliaryMapper;
import com.ruoyi.financial.mapper.FinancialVoucherDetailsMapper;
import com.ruoyi.financial.service.IFinancialVoucherDetailsService;
import com.ruoyi.financial.utils.DoubleValueUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Service
public class FinancialVoucherDetailsServiceImpl extends ServiceImpl<FinancialVoucherDetailsMapper, FinancialVoucherDetails> implements IFinancialVoucherDetailsService {

    @Autowired
    private FinancialSubjectMapper subjectMapper;

    @Autowired
    private FinancialAccountingCategoryDetailsMapper categoryDetailsMapper;

    @Autowired
    private FinancialVoucherDetailsAuxiliaryMapper detailsAuxiliaryMapper;

    @Override
    public int batchInsert(List<FinancialVoucherDetails> list) {
        return baseMapper.batchInsert(list);
    }

    @Override
    public boolean save(FinancialVoucherDetails entity) {
        LambdaQueryWrapper<FinancialVoucherDetails> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialVoucherDetails::getAccountSetsId, entity.getAccountSetsId());
        qw.eq(FinancialVoucherDetails::getSubjectId, entity.getSubjectId());
        qw.eq(FinancialVoucherDetails::getSubjectCode, entity.getSubjectCode());
        qw.isNull(FinancialVoucherDetails::getVoucherId);

        if (this.baseMapper.selectCount(qw) > 0) {
            return super.update(entity, qw);
        }
        return super.save(entity);
    }

    /**
     * 期初数据
     *
     * @param accountSetsId
     * @param type
     * @return
     */
    @Override
    public List<FinancialVoucherDetails> balanceList(Integer accountSetsId, String type) {
        return this.baseMapper.selectBalanceList(accountSetsId, type);
    }

    /**
     * 期初试算平衡
     *
     * @param accountSetsId
     * @return
     */
    @Override
    public Map<String, Map<String, Double>> trialBalance(Integer accountSetsId) {
        Map<String, Double> beginningBalance = this.baseMapper.selectListInitialCheckData(accountSetsId);
        List<Map> liabilities = this.baseMapper.selectBassetsAndLiabilities(accountSetsId);
        Map<String, Double> bb = new HashMap<>();
        Map<String, Double> bl = new HashMap<>();
        bb.put("借", beginningBalance != null ? beginningBalance.get("debit_amount") : 0d);
        bb.put("贷", beginningBalance != null ? beginningBalance.get("credit_amount") : 0d);

        Map<String, List<Map>> collect = liabilities.stream().collect(Collectors.groupingBy(map -> (String) map.get("type")));
        collect.forEach((type, maps) -> {
            Optional<Map> borrow = maps.stream().filter(map -> "借".equals(map.get("balance_direction"))).findFirst();
            Optional<Map> credit = maps.stream().filter(map -> "贷".equals(map.get("balance_direction"))).findFirst();

            if (borrow.isPresent() && credit.isPresent()) {
                Double balanceBorrow = DoubleValueUtil.getNotNullVal((Double) borrow.get().get("debit_amount"), (Double) borrow.get().get("credit_amount"));
                Double balanceCredit = DoubleValueUtil.getNotNullVal((Double) credit.get().get("debit_amount"), (Double) credit.get().get("credit_amount"));
                bl.put(type, balanceBorrow - balanceCredit);
            } else if (borrow.isPresent()) {
                Double balanceBorrow = DoubleValueUtil.getNotNullVal((Double) borrow.get().get("debit_amount"), (Double) borrow.get().get("credit_amount"));
                bl.put(type, balanceBorrow);
            } else if (credit.isPresent()) {
                Double balanceCredit = DoubleValueUtil.getNotNullVal((Double) credit.get().get("debit_amount"), (Double) credit.get().get("credit_amount"));
                bl.put(type, balanceCredit);
            }
        });

        Map<String, Map<String, Double>> data = new HashMap<>();

        data.put("beginningBalance", bb);
        data.put("liabilities", bl);

        return data;
    }

    @Override
    public void saveAuxiliary(Integer accountSetsId, HashMap<String, Object> entity) {
        Integer subjectId = (Integer) entity.get("subjectId");
        JSONObject auxiliary = (JSONObject) entity.get("auxiliary");
        FinancialSubject subject = subjectMapper.selectById(subjectId);
        List<FinancialAccountingCategoryDetails> categoryDetails = categoryDetailsMapper.selectBatchIds(auxiliary.values().stream().mapToInt(o -> (Integer) o).boxed().collect(Collectors.toList()));

        StringBuilder auxiliaryTitle = new StringBuilder();
        StringBuilder subjectCode = new StringBuilder(subject.getCode());

        for (FinancialAccountingCategoryDetails cd : categoryDetails) {
            auxiliaryTitle.append("_").append(cd.getName());
            subjectCode.append("_").append(cd.getId());
        }

        LambdaQueryWrapper<FinancialVoucherDetails> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialVoucherDetails::getAccountSetsId, accountSetsId);
        qw.eq(FinancialVoucherDetails::getSubjectId, subjectId);
        qw.eq(FinancialVoucherDetails::getSubjectCode, subjectCode.toString());
        qw.isNull(FinancialVoucherDetails::getVoucherId);

        FinancialVoucherDetails details = this.baseMapper.selectOne(qw);
        if (details == null) {
            details = new FinancialVoucherDetails();
            details.setSummary("期初");
            details.setSubjectId(subjectId);
            details.setSubjectName(subjectCode.toString() + "-" + subject.getName());
            details.setSubjectCode(subjectCode.toString());
            details.setAuxiliaryTitle(auxiliaryTitle.toString());
            details.setAccountSetsId(accountSetsId);
            this.baseMapper.insert(details);
        }

        List<FinancialVoucherDetailsAuxiliary> voucherDetailsAuxiliaries = new ArrayList<>();

        for (FinancialAccountingCategoryDetails cd : categoryDetails) {
            LambdaQueryWrapper<FinancialVoucherDetailsAuxiliary> aqw = Wrappers.lambdaQuery();
            aqw.eq(FinancialVoucherDetailsAuxiliary::getVoucherDetailsId, details.getId());
            aqw.eq(FinancialVoucherDetailsAuxiliary::getAccountingCategoryId, cd.getAccountingCategoryId());
            aqw.eq(FinancialVoucherDetailsAuxiliary::getAccountingCategoryDetailsId, cd.getId());

            if (detailsAuxiliaryMapper.selectCount(aqw) == 0) {
                FinancialVoucherDetailsAuxiliary vda = new FinancialVoucherDetailsAuxiliary();
                vda.setVoucherDetailsId(details.getId());
                vda.setAccountingCategoryId(cd.getAccountingCategoryId());
                vda.setAccountingCategoryDetailsId(cd.getId());
                voucherDetailsAuxiliaries.add(vda);
            }
        }

        if (!voucherDetailsAuxiliaries.isEmpty()) {
            detailsAuxiliaryMapper.batchInsert(voucherDetailsAuxiliaries);
        }
    }

    @Override
    public List<FinancialVoucherDetails> auxiliaryList(Integer accountSetsId, String type) {
        return this.baseMapper.selectAuxiliaryList(accountSetsId, type);
    }

    /**
     * 科目会计期间的累计金额
     *
     * @param accountSetsId
     * @param codeList
     * @param currentAccountDate
     * @return
     */
    @Override
    public Map<String, FinancialVoucherDetails> getAggregateAmount(Integer accountSetsId, Set<String> codeList, Date currentAccountDate) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(currentAccountDate);
        List<FinancialVoucherDetails> details = this.baseMapper.selectAggregateAmount(accountSetsId, codeList, instance.get(Calendar.YEAR), instance.get(Calendar.MONTH) + 1);
        return details.stream().collect(Collectors.toMap(FinancialVoucherDetails::getSubjectCode, voucherDetails -> voucherDetails));
    }
}











