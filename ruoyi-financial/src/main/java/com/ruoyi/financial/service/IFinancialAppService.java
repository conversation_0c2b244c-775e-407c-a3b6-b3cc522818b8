package com.ruoyi.financial.service;

import com.alibaba.fastjson.JSONArray;

import java.util.List;
import java.util.Map;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
public interface IFinancialAppService{


    /**
     * <AUTHOR>
     * @Description 获取配置信息
     * @Date 2023/7/5 13:57
     * @Param []
     * @return java.util.Map<java.lang.String,java.util.List<java.util.Map<java.lang.String,java.lang.Object>>>
     **/
    Map<String, List<Map<String, Object>>> getConfig();

    /**
     * <AUTHOR>
     * @Description 获取菜单数据
     * @Date 2023/7/5 13:57
     * @Param [roleKey]
     * @return com.alibaba.fastjson.JSONArray
     **/
    JSONArray getMenu(String roleKey);
}




