package com.ruoyi.financial.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.redis.RedisConstants;
import com.ruoyi.common.core.redis.RedisPublisher;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.financial.controller.JsonResult;
import com.ruoyi.financial.domain.*;
import com.ruoyi.financial.excel.SubjectBalanceExcel;
import com.ruoyi.financial.mapper.*;
import com.ruoyi.financial.po.CheckUsePo;
import com.ruoyi.financial.po.ProjectInfoPo;
import com.ruoyi.financial.service.IFinancialSubjectService;
import com.ruoyi.financial.utils.DoubleValueUtil;
import com.ruoyi.financial.vo.BalanceVo;
import com.ruoyi.financial.vo.BindProjectVo;
import com.ruoyi.financial.vo.SubjectVo;
import com.ruoyi.financial.vo.VoucherDetailVo;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Slf4j
@Service
@AllArgsConstructor
public class FinancialSubjectServiceImpl extends ServiceImpl<FinancialSubjectMapper, FinancialSubject> implements IFinancialSubjectService {

    private FinancialAccountSetsMapper accountSetsMapper;
    private FinancialVoucherMapper voucherMapper;
    private FinancialSubjectMapper subjectMapper;
    private FinancialVoucherDetailsMapper voucherDetailsMapper;
    private FinancialVoucherTemplateDetailsMapper voucherTemplateDetailsMapper;
    @Autowired
    private RedisPublisher redisPublisher;

    @Override
    public int batchInsert(List<FinancialSubject> list) {
        return baseMapper.batchInsert(list);
    }

    @Override
    public List<SubjectVo> selectData(Wrapper<FinancialSubject> queryWrapper, boolean showAll) {
        QueryWrapper<FinancialSubject> qw = (QueryWrapper<FinancialSubject>) queryWrapper;
        qw.eq("status", 1);
        Map<Integer, FinancialSubject> subjectMap = baseMapper.selectList(qw).stream().collect(Collectors.toMap(FinancialSubject::getId, subject -> subject));
        List<FinancialSubject> list = baseMapper.selectNoChildrenSubject(qw);
        list.forEach(subject -> {
            if (subject.getLevel() != 1) {
                recursiveChildren(subjectMap, subject, subject.getParentId());
            }
        });

        if (showAll) {
            list.forEach(subject -> subjectMap.remove(subject.getId()));
            list.addAll(subjectMap.values());
        }

        return list.stream().sorted(Comparator.comparing(FinancialSubject::getCode)).map(subject -> {
            SubjectVo vo = new SubjectVo();
            BeanUtils.copyProperties(subject, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 明细科目
     *
     * @param accountSetsId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<FinancialSubject> accountBookList(Integer accountSetsId, Date startTime, Date endTime, boolean showNumPrice) {
        List<FinancialSubject> subjectList = baseMapper.selectAccountBookList(accountSetsId, startTime, endTime, showNumPrice);

        Map<Integer, FinancialSubject> temp = new HashMap<>();
        subjectList.forEach(subject -> this.recursiveParent(temp, subject));
        return temp.values().stream().sorted(Comparator.comparing(FinancialSubject::getCode)).distinct().collect(Collectors.toList());
    }

    /**
     * 余额明细科目
     *
     * @param accountSetsId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<FinancialSubject> balanceSubjectList(Integer accountSetsId, Date startTime, Date endTime, boolean showNumPrice) {
        List<FinancialSubject> subjectList = this.accountBookList(accountSetsId, startTime, endTime, showNumPrice);

        Map<Integer, FinancialSubject> temp = new HashMap<>();
        subjectList.forEach(subject -> this.recursiveParent(temp, subject));
        return temp.values().stream().sorted(Comparator.comparing(FinancialSubject::getCode)).distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void importVoucher(List<SubjectVo> voucherList, FinancialAccountSets accountSets) {
        AtomicInteger update = new AtomicInteger(0);
        AtomicInteger insert = new AtomicInteger(0);
        this.recursive(voucherList, update, insert, accountSets);
    }

    private void recursive(@NonNull List<SubjectVo> voucherList, AtomicInteger update, AtomicInteger insert, FinancialAccountSets accountSets) {
        for (SubjectVo vo : voucherList) {
            if (vo.getId() != null) {
                if (this.checkUse(vo.getId())) {
                    continue;
                }

                this.updateById(vo);
                update.incrementAndGet();
                if (!vo.getChildren().isEmpty()) {
                    vo.getChildren().forEach(s -> s.setParentId(vo.getId()));
                    this.recursive(vo.getChildren(), update, insert, accountSets);
                }
            } else {
                if (accountSets.getAccountingStandards() == (short) 0 && vo.getLevel() == (short) 1) {
                    continue;
                }

                //一般纳税人
                this.save(vo);
                insert.incrementAndGet();
                if (!vo.getChildren().isEmpty()) {
                    vo.getChildren().forEach(s -> s.setParentId(vo.getId()));
                    this.recursive(vo.getChildren(), update, insert, accountSets);
                }
            }
        }
    }

    /**
     * 科目余额
     *
     * @param accountSetsId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<BalanceVo> subjectBalance(Integer accountSetsId, Date startTime, Date endTime, boolean showNumPrice) {
        //当前查询账套
        FinancialAccountSets accountSets = accountSetsMapper.selectById(accountSetsId);
        List<FinancialSubject> subjects = this.accountBookList(accountSetsId, null, null, showNumPrice);
        //转换为余额对象
        Map<Integer, BalanceVo> sbvMap = subjects.stream().collect(Collectors.toMap(FinancialSubject::getId, subject -> {
            BalanceVo sbv = new BalanceVo();
            if(null != subject.getType()){
                sbv.setType(subject.getType().toString());
            }
            sbv.setSubjectId(subject.getId());
            sbv.setCode(subject.getCode());
            sbv.setParentId(subject.getParentId());
            sbv.setName(subject.getName());
            sbv.setLevel(subject.getLevel());
            sbv.setUnit(subject.getUnit());
            sbv.setBalanceDirection(subject.getBalanceDirection().toString());
            return sbv;
        }));
        //原始期初余额
        if (sbvMap.isEmpty()) {
            return new ArrayList<>();
        }


        //对比账套时间，判断是否是初始账套时间
        if (!DateFormatUtils.format(accountSets.getEnableDate(), "yyyyMM").equals(DateFormatUtils.format(startTime, "yyyyMM"))) {
            List<VoucherDetailVo> details = voucherMapper.selectSubjectDetail(sbvMap.keySet(), accountSetsId, startTime, null, showNumPrice);
            details.forEach(vd -> {
                if (sbvMap.containsKey(vd.getSubjectId())) {
                    BalanceVo sbv = sbvMap.get(vd.getSubjectId());
                    Double val = null;
                    switch (sbv.getBalanceDirection()) {
                        case "借":
                            //借-贷
                            val = DoubleValueUtil.getNotNullVal(vd.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vd.getCreditAmount());
                            break;
                        case "贷":
                            //贷-借
                            val = DoubleValueUtil.getNotNullVal(vd.getCreditAmount()) - DoubleValueUtil.getNotNullVal(vd.getDebitAmount());
                            break;
                    }
                    if(sbv.getType().equals("损益")){
                        sbv.setBeginningActiveBalance(null);
                    }else{
                        sbv.setBeginningActiveBalance(val);
                    }
                }
            });
        } else {
            LambdaQueryWrapper<FinancialVoucherDetails> qwi = Wrappers.lambdaQuery();
            qwi.eq(FinancialVoucherDetails::getAccountSetsId, accountSetsId);
            qwi.in(FinancialVoucherDetails::getSubjectId, sbvMap.keySet());
            qwi.isNull(FinancialVoucherDetails::getVoucherId);
            qwi.and(wrapper -> {
                wrapper.or(true).isNotNull(FinancialVoucherDetails::getCreditAmount);
                wrapper.or(true).isNotNull(FinancialVoucherDetails::getDebitAmount);
            });
            this.voucherDetailsMapper.selectList(qwi).forEach(ib -> {
                if (sbvMap.containsKey(ib.getSubjectId())) {
                    sbvMap.get(ib.getSubjectId()).setBeginningBalance(DoubleValueUtil.getNotNullVal(ib.getCreditAmount(), ib.getDebitAmount()));
                }
            });
        }

        //本期发生额
        List<VoucherDetailVo> details = voucherMapper.selectSubjectDetail(sbvMap.keySet(), accountSetsId, startTime, endTime, showNumPrice);
        details.forEach(vd -> {
            if (sbvMap.containsKey(vd.getSubjectId())) {
                BalanceVo sbv = sbvMap.get(vd.getSubjectId());
                sbv.setCurrentCreditAmount(vd.getCreditAmount());
                sbv.setCurrentDebitAmount(vd.getDebitAmount());
                if (showNumPrice && vd.getBalanceDirection() != null) {
                    switch (vd.getBalanceDirection()) {
                        case "借":
                            sbv.setCurrentDebitAmountNum(vd.getNum());
                            break;
                        case "贷":
                            sbv.setCurrentCreditAmountNum(vd.getNum());
                            break;
                    }
                }
            }
        });

        //合计
        BalanceVo aCombined = new BalanceVo();
        aCombined.setName("合计");
        //计算期末
        List<BalanceVo> balanceVos = sbvMap.values().stream().sorted(Comparator.comparing(BalanceVo::getCode)).collect(Collectors.toList());
        for (BalanceVo vo : balanceVos) {
            //期初
            Double bb = DoubleValueUtil.getNotNullVal(vo.getBeginningBalance());
            //本期借贷金额
            Double cc = DoubleValueUtil.getNotNullVal(vo.getCurrentCreditAmount());//本期贷方
            Double cd = DoubleValueUtil.getNotNullVal(vo.getCurrentDebitAmount());//本期借方
            //本期借贷数量
            Double ccNum = DoubleValueUtil.getNotNullVal(vo.getCurrentCreditAmountNum());
            Double cdNum = DoubleValueUtil.getNotNullVal(vo.getCurrentDebitAmountNum());

            //根据方向计算余额
            /*switch (vo.getBalanceDirection()) {
                case "借":
                    //期初+本期借-本期贷
                    vo.setEndingActiveTBalance(bb + cd - cc);
                    break;
                case "贷":
                    //期初-本期借+本期贷
                    vo.setEndingActiveTBalance(bb - cd + cc);
                    break;
            }*/

            if(vo.getType().equals("资产")){
                //期初+本期借-本期贷
                vo.setEndingActiveBalance(bb + cd - cc);
            }else if (vo.getType().equals("损益")){
                vo.setEndingActiveBalance(cd - cc);
            }else{
                //期初-本期借+本期贷
                vo.setEndingActiveBalance(bb - cd + cc);
            }

            if (showNumPrice) {
                switch (vo.getBalanceDirection()) {
                    case "借":
                        vo.setEndingDebitBalanceNum(cdNum - ccNum);
                        break;
                    case "贷":
                        vo.setEndingCreditBalanceNum(ccNum - cdNum);
                        break;
                }
            }

            //计算合计列
            aCombined.setBeginningCreditBalance(vo.getBeginningCreditBalance());
            aCombined.setBeginningDebitBalance(vo.getBeginningDebitBalance());

            aCombined.setCurrentCreditAmount(vo.getCurrentCreditAmount());
            aCombined.setCurrentCreditAmountNum(vo.getCurrentCreditAmountNum());
            aCombined.setCurrentDebitAmount(vo.getCurrentDebitAmount());
            aCombined.setCurrentDebitAmountNum(vo.getCurrentDebitAmountNum());

            aCombined.setEndingCreditBalance(vo.getEndingCreditBalance());
            aCombined.setEndingCreditBalanceNum(vo.getEndingCreditBalanceNum());
            aCombined.setEndingDebitBalance(vo.getEndingDebitBalance());
            aCombined.setEndingDebitBalanceNum(vo.getEndingDebitBalanceNum());
        }

        //计算父节点
        for (int i = balanceVos.size() - 1; i > 0; i--) {
            BalanceVo vo = balanceVos.get(i);
            if (vo.getLevel() != 1) {
                BalanceVo parent = sbvMap.get(vo.getParentId());
                if (parent != null) {
                    parent.setBeginningBalance(vo.getBeginningBalance());
                    parent.setCurrentDebitAmount(vo.getCurrentDebitAmount());
                    parent.setCurrentDebitAmountNum(vo.getCurrentDebitAmountNum());
                    parent.setCurrentCreditAmount(vo.getCurrentCreditAmount());
                    parent.setCurrentCreditAmountNum(vo.getCurrentCreditAmountNum());

                    if (vo.getEndingCreditBalance() != null) {
                        parent.setEndingCreditBalance(vo.getEndingCreditBalance());
                    }
                    if (vo.getEndingDebitBalance() != null) {
                        parent.setEndingDebitBalance(vo.getEndingDebitBalance());
                    }
                }
            }
        }
        if (balanceVos.size() > 0) {
            //过滤掉空行
            balanceVos = balanceVos.stream().filter(vo ->
                    (vo.getBeginningBalance() != null && vo.getBeginningBalance() != 0) ||
                            (vo.getEndingBalance() != null && vo.getEndingBalance() != 0) ||
                            (vo.getCurrentDebitAmount() != null && vo.getCurrentDebitAmount() != 0) ||
                            (vo.getCurrentCreditAmount() != null && vo.getCurrentCreditAmount() != 0)
            ).collect(Collectors.toList());
            balanceVos.add(aCombined);
        }

        return balanceVos;
    }

    /**
     * 科目汇总
     *
     * @param accountSetsId
     * @param startTime
     * @return
     */
    @Override
    public List subjectSummary(Integer accountSetsId, Date startTime, Date endTime, boolean showNumPrice) {
        List<FinancialSubject> subjects = this.accountBookList(accountSetsId, startTime, endTime, showNumPrice);
        //转换为余额对象
        Map<Integer, BalanceVo> sbvMap = subjects.stream().collect(Collectors.toMap(FinancialSubject::getId, subject -> {
            BalanceVo sbv = new BalanceVo();
            sbv.setSubjectId(subject.getId());
            sbv.setCode(subject.getCode());
            sbv.setUnit(subject.getUnit());
            sbv.setParentId(subject.getParentId());
            sbv.setName(subject.getName());
            sbv.setLevel(subject.getLevel());
            sbv.setBalanceDirection(subject.getBalanceDirection().toString());
            return sbv;
        }));

        if (sbvMap.isEmpty()) {
            return new ArrayList<>();
        }

        //本期发生额
        List<VoucherDetailVo> details = voucherMapper.selectSubjectDetail(sbvMap.keySet(), accountSetsId, startTime, endTime, showNumPrice);
        details.forEach(vd -> {
            if (sbvMap.containsKey(vd.getSubjectId())) {
                BalanceVo sbv = sbvMap.get(vd.getSubjectId());
                if (showNumPrice) {
                    if (vd.getBalanceDirection() != null) {
                        switch (vd.getBalanceDirection()) {
                            case "借":
                                sbv.setCurrentDebitAmount(vd.getDebitAmount());
                                sbv.setCurrentDebitAmountNum(vd.getNum());
                                break;
                            case "贷":
                                sbv.setCurrentCreditAmount(vd.getCreditAmount());
                                sbv.setCurrentCreditAmountNum(vd.getNum());
                                break;
                        }
                    }
                } else {
                    sbv.setCurrentCreditAmount(vd.getCreditAmount());
                    sbv.setCurrentDebitAmount(vd.getDebitAmount());
                }
            }
        });

        //合计
        BalanceVo aCombined = new BalanceVo();
        aCombined.setName("合计");
        //计算期末
        List<BalanceVo> balanceVos = sbvMap.values().stream().sorted(Comparator.comparing(BalanceVo::getCode)).collect(Collectors.toList());
        for (BalanceVo vo : balanceVos) {
            aCombined.setCurrentCreditAmount(vo.getCurrentCreditAmount());
            aCombined.setCurrentCreditAmountNum(vo.getCurrentCreditAmountNum());
            aCombined.setCurrentDebitAmount(vo.getCurrentDebitAmount());
            aCombined.setCurrentDebitAmountNum(vo.getCurrentDebitAmountNum());
        }

        //计算父节点
        for (int i = balanceVos.size() - 1; i > 0; i--) {
            BalanceVo vo = balanceVos.get(i);
            if (vo.getLevel() != 1) {
                BalanceVo parent = sbvMap.get(vo.getParentId());
                if (parent != null) {
                    parent.setCurrentDebitAmount(vo.getCurrentDebitAmount());
                    parent.setCurrentDebitAmountNum(vo.getCurrentDebitAmountNum());
                    parent.setCurrentCreditAmount(vo.getCurrentCreditAmount());
                    parent.setCurrentCreditAmountNum(vo.getCurrentCreditAmountNum());
                }
            }
        }

        if (balanceVos.size() > 0) {
            balanceVos.add(aCombined);
        }

        return balanceVos;
    }

    private void recursiveParent(Map<Integer, FinancialSubject> temp, FinancialSubject subject) {
        if(null == subject){
            return;
        }
        temp.put(subject.getId(), subject);
        if (subject.getLevel() != 1) {
            if (!temp.containsKey(subject.getParentId())) {
                FinancialSubject sbj = baseMapper.selectById(subject.getParentId());
                this.recursiveParent(temp, sbj);
            }
        }
    }

    private void recursiveChildren(Map<Integer, FinancialSubject> subjectMap, FinancialSubject subject, int parentId) {
        FinancialSubject parent = subjectMap.get(parentId);
        subject.setName(parent.getName() + "-" + subject.getName());
        if (parent.getLevel() != 1) {
            recursiveChildren(subjectMap, subject, parent.getParentId());
        }
    }

    public List<SubjectVo> listVo(Wrapper<FinancialSubject> queryWrapper) {
        QueryWrapper<FinancialSubject> qw = (QueryWrapper<FinancialSubject>) queryWrapper;
        qw.orderByAsc("code");
        return baseMapper.selectSubjectVo(queryWrapper);
    }

    /**
     * 检查科目是否已经被使用
     *
     * @param id
     * @return
     */
    @Override
    public Boolean checkUse(Integer id) {
        boolean vd = voucherDetailsMapper.checkUse(id) == 0;

        LambdaQueryWrapper<FinancialVoucherTemplateDetails> vtdqw = Wrappers.lambdaQuery();
        vtdqw.eq(FinancialVoucherTemplateDetails::getSubjectId, id);
        boolean vtd = voucherTemplateDetailsMapper.selectCount(vtdqw) == 0;

        List<CheckUsePo> checkUsePoList = voucherDetailsMapper.selectBySubjectId(id);
        //过滤作废凭证
        checkUsePoList = checkUsePoList.stream().filter(e->null == e.getValid() || e.getValid()).collect(Collectors.toList());
        boolean vsd = checkUsePoList.size() == 0;
        if(checkUsePoList.size() ==1){
            CheckUsePo checkUsePo = checkUsePoList.get(0);
            //只剩期初
            if(checkUsePo.getSummary().equals("期初")){
                vsd=true;
                voucherDetailsMapper.deleteById(checkUsePo.getVoucherDetailId());
            }
        }
        return !(vd && vtd && vsd);
    }

    /**
     * 科目余额
     *
     * @param accountSetsId
     * @param subjectId
     * @param categoryId
     * @param categoryDetailsId
     * @return
     */
    @Override
    public Double balance(Integer accountSetsId, Integer subjectId, Integer categoryId, Integer categoryDetailsId) {
        LambdaQueryWrapper<FinancialVoucherDetails> ibqw = Wrappers.lambdaQuery();
        ibqw.eq(FinancialVoucherDetails::getSubjectId, subjectId);
        ibqw.eq(FinancialVoucherDetails::getAccountSetsId, accountSetsId);
        ibqw.isNull(FinancialVoucherDetails::getVoucherId);

        List<FinancialVoucherDetails> ibs = this.voucherDetailsMapper.selectList(ibqw);
        FinancialVoucherDetails ib = null;
        if (ibs.size() > 0) {
            ib = ibs.get(0);
            for (int i = 1; i < ibs.size(); i++) {
                double creditAmount = Optional.ofNullable(ib.getCreditAmount()).orElse(0d) + Optional.ofNullable(ibs.get(i).getCreditAmount()).orElse(0d);
                double debitAmount = Optional.ofNullable(ib.getDebitAmount()).orElse(0d) + Optional.ofNullable(ibs.get(i).getDebitAmount()).orElse(0d);
                ib.setCreditAmount(creditAmount);
                ib.setDebitAmount(debitAmount);
            }
        }


        List<VoucherDetailVo> vds = voucherDetailsMapper.selectBalanceData(accountSetsId, subjectId, categoryId, categoryDetailsId);
        double balance = 0d;
        if (!vds.isEmpty()) {
            VoucherDetailVo vo = vds.get(0);
            if (vo != null) {
                if (vo.getBalanceDirection().equals("借")) {
                    balance = DoubleValueUtil.getNotNullVal(vo.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vo.getCreditAmount());
                } else {
                    balance = DoubleValueUtil.getNotNullVal(vo.getCreditAmount()) - DoubleValueUtil.getNotNullVal(vo.getDebitAmount());
                }
            }
        }

        //TODO 期初暂时没有辅助期初，categoryId == null过滤掉辅助
        if (ib != null && categoryId == null) {
            balance += DoubleValueUtil.getNotNullVal(ib.getDebitAmount(), ib.getCreditAmount());
        }

        return balance;
    }

    /**
     * 过滤出所有没有子节点的科目
     *
     * @param accountSetsId
     * @return
     */
    @Override
    public List<Integer> leafList(Integer accountSetsId) {
        return this.baseMapper.selectLeaf(accountSetsId);
    }

    @Override
    public boolean save(FinancialSubject entity) {
        LambdaQueryWrapper<FinancialSubject> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialSubject::getAccountSetsId, entity.getAccountSetsId());
        qw.eq(FinancialSubject::getName, entity.getName());
        qw.eq(FinancialSubject::getParentId, entity.getParentId());

        if (this.count(qw) > 0) {
            throw new ServiceException("科目名称已经存在！");
        }

        qw = Wrappers.lambdaQuery();
        qw.eq(FinancialSubject::getAccountSetsId, entity.getAccountSetsId());
        qw.eq(FinancialSubject::getCode, entity.getCode());

        if (this.count(qw) > 0) {
            throw new ServiceException("科目编码已经存在！");
        }

        return super.save(entity);
    }

    @Override
    public boolean update(FinancialSubject entity, Wrapper<FinancialSubject> updateWrapper) {
        LambdaQueryWrapper<FinancialSubject> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialSubject::getAccountSetsId, entity.getAccountSetsId());
        qw.eq(FinancialSubject::getName, entity.getName());
        qw.eq(FinancialSubject::getParentId, entity.getParentId());

        if (this.count(qw) > 0) {
            throw new ServiceException("科目名称已经存在！");
        }

        boolean result = super.update(entity, updateWrapper);
        //根据科目ID更新凭证信息
        voucherDetailsMapper.updateVoucherBySubjectId(entity.getId());
        return result;
    }

    @Override
    public boolean remove(Wrapper<FinancialSubject> wrapper) {
        //获取科目id
        Integer subjectId = getOne(wrapper).getId();
        if (this.checkUse(subjectId)) {
            throw new ServiceException("科目已被使用，不能删除！", 200);
        }
        boolean result = super.remove(wrapper);
        //发布财务经营分析频道
        redisPublisher.publish(RedisConstants.FINANCIAL_DATAQUERY_DEL_CHANNEL, subjectId.toString());
        return result;
    }

    @Override
    public List<SubjectBalanceExcel> exportSubjectBalance(Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice){
        List<BalanceVo> balanceVos = subjectBalance(accountSetsId, startTime, endTime, showNumPrice);

        List<SubjectBalanceExcel> subjectBalanceExcels = new ArrayList<>();
        balanceVos.forEach(balanceVo->{
            SubjectBalanceExcel subjectBalanceExcel = new SubjectBalanceExcel();
            BeanUtils.copyProperties(balanceVo, subjectBalanceExcel);
            if(null != balanceVo.getBeginningDebitBalance()){
                subjectBalanceExcel.setBeginningDebitBalance(BigDecimal.valueOf(balanceVo.getBeginningDebitBalance()));
            }
            if(null != balanceVo.getBeginningCreditBalance()){
                subjectBalanceExcel.setBeginningCreditBalance(BigDecimal.valueOf(balanceVo.getBeginningCreditBalance()));
            }
            if(null != balanceVo.getCurrentDebitAmount()){
                subjectBalanceExcel.setCurrentDebitAmount(BigDecimal.valueOf(balanceVo.getCurrentDebitAmount()));
            }
            if(null != balanceVo.getCurrentCreditAmount()){
                subjectBalanceExcel.setCurrentCreditAmount(BigDecimal.valueOf(balanceVo.getCurrentCreditAmount()));
            }
            if(null != balanceVo.getEndingDebitBalance()){
                subjectBalanceExcel.setEndingDebitBalance(BigDecimal.valueOf(balanceVo.getEndingDebitBalance()));
            }
            if(null != balanceVo.getEndingCreditBalance()){
                subjectBalanceExcel.setEndingCreditBalance(BigDecimal.valueOf(balanceVo.getEndingCreditBalance()));
            }
            subjectBalanceExcels.add(subjectBalanceExcel);
        });
        return subjectBalanceExcels;
    }

    @Override
    public List<Map<String,Object>> getSubjectSelectData(FinancialSubject financialSubject) {


        return subjectMapper.queryByParamList(financialSubject);
    }

    @Override
    public  List<Map<String,Object>> getSubjectTypeData(Long accountSetsId) {

        return subjectMapper.getSubjectType(accountSetsId);
    }

    /**
     * <AUTHOR>
     * @Description 复制科目
     * @Date 2023/9/28 11:25
     * @Param [sourceAccountSetsId, targetAccountSetsId]
     * @return void
     **/
    @Override
    public void copySubject(Integer sourceAccountSetsId, Integer targetAccountSetsId) {
        List<FinancialVoucher> financialVouchers = voucherMapper.selectByAccountSetsId(sourceAccountSetsId);
        if(!CollectionUtils.isEmpty(financialVouchers)){
            throw new ServiceException("源账套已有关联凭证，不允许操作！");
        }
        //删除初始化科目信息
        int count = subjectMapper.deleteByAccountSetsId(targetAccountSetsId);
        //查询源账套科目信息
        QueryWrapper qw = new QueryWrapper<>();
        qw.eq("account_sets_id", sourceAccountSetsId);
        List<SubjectVo> subjectList = listVo(qw);

        //插入目标账套科目信息
        recursiveSubject(subjectList, targetAccountSetsId);
    }

    @Override
    public List<Map<String, Object>> queryAllFirst() {
        return subjectMapper.getAllFirstSubject();
    }

    /**
     * <AUTHOR>
     * @Description 科目排序
     * @Date 2024/7/12 14:59
     * @Param [accountSetsId, id, level]
     * @return void
     **/
    @Override
    public void subjectCodeSort(Integer accountSetsId, Integer subjectId, Integer level) {
        Integer codeSet = level.equals(1) ? 3: 2;
        //科目排序
        subjectMapper.subCodeSort(accountSetsId, subjectId, codeSet);
        //更新凭证对应科目信息
        voucherDetailsMapper.updateVoucherMapSub(accountSetsId);
    }

    /**
     * <AUTHOR>
     * @Description 获取项目列表
     * @Date 2024/11/13 14:58
     * @Param [accountSetsId]
     * @return java.util.List<com.ruoyi.financial.po.ProjectInfoPo>
     **/
    @Override
    public List<ProjectInfoPo> getProjectList(Integer accountSetsId) {
        return subjectMapper.getProjectList(accountSetsId);
    }

    /**
     * <AUTHOR>
     * @Description 绑定项目
     * @Date 2024/11/12 10:59
     * @Param [bindProjectVo]
     * @return void
     **/
    @Override
    public JsonResult bindProject(BindProjectVo bindProjectVo) {
        //删除科目已绑定的项目
        subjectMapper.deleteAlReadyBindProject(bindProjectVo.getSubjectId());
        //绑定项目
        int result = subjectMapper.bindProject(bindProjectVo);
        //更新项目类型
        subjectMapper.updateProjectType(bindProjectVo.getProjectId());
        if (result>0){
            return JsonResult.successful();
        }
        return JsonResult.failure();
    }

    private void recursiveSubject(List<SubjectVo> subjects, Integer accountSetsId) {
        subjects.forEach(subject -> subject.setAccountSetsId(accountSetsId));
        subjects.forEach(subject -> {
            subjectMapper.insert(subject);

            if (subject.getChildren() != null && subject.getChildren().size() > 0) {
                subject.getChildren().forEach(subject1 -> subject1.setParentId(subject.getId()));
                this.recursiveSubject(subject.getChildren(), accountSetsId);
            }
        });
    }
}