package com.ruoyi.financial.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.financial.domain.*;
import com.ruoyi.financial.excel.AssetReportExcel;
import com.ruoyi.financial.excel.GeneralLedgerExcel;
import com.ruoyi.financial.excel.IncomeReportExcel;
import com.ruoyi.financial.mapper.*;
import com.ruoyi.financial.po.ReportTemplatePo;
import com.ruoyi.financial.service.IFinancialReportTemplateService;
import com.ruoyi.financial.utils.DoubleValueUtil;
import com.ruoyi.financial.vo.ReportDataVo;
import com.ruoyi.financial.vo.VoucherDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.kernel.service.impl</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年09月05日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Service
@Slf4j
public class FinancialReportTemplateServiceImpl extends ServiceImpl<FinancialReportTemplateMapper, FinancialReportTemplate> implements IFinancialReportTemplateService {

    @Autowired
    private FinancialReportTemplateItemsMapper itemsMapper;

    @Autowired
    private FinancialReportTemplateItemsFormulaMapper formulaMapper;

    @Autowired
    private FinancialVoucherMapper voucherMapper;

    @Autowired
    private FinancialSubjectMapper subjectMapper;

    @Autowired
    private FinancialAccountSetsMapper accountSetsMapper;

    private DateFormat sdf = new SimpleDateFormat("yyyy");

    @Override
    public int batchInsert(List<FinancialReportTemplate> list) {
        return baseMapper.batchInsert(list);
    }


    /**
     * 计算报表
     *
     * @param accountSetsId
     * @param id
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Map<Integer, ReportDataVo> view(Integer accountSetsId, Long id, Date startTime, Date endTime) {
        FinancialAccountSets accountSets = accountSetsMapper.selectById(accountSetsId);

        LambdaQueryWrapper<FinancialReportTemplate> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialReportTemplate::getAccountSetsId, accountSetsId);
        qw.eq(FinancialReportTemplate::getId, id);
        FinancialReportTemplate template = this.getOne(qw);
        //log.info("{}", template);
        Map<Integer, ReportDataVo> dataVoMap = new HashMap<>();
        if (template.getItems() != null) {
            //先计算表外公式
            template.getItems().stream().filter(rti -> rti.getSources() == 0 && !rti.getIsClassified()).forEach(item -> {
                ReportDataVo dataVo = new ReportDataVo();
                dataVo.setItemId(item.getId());

                //获取计算项
                if (item.getFormulas() != null && !item.getFormulas().isEmpty()) {
                    //所有计算项目的科目 id
                    List<String> fromTags = item.getFormulas().stream().map(FinancialReportTemplateItemsFormula::getFromTag).collect(Collectors.toList());
                    List<FinancialSubject> subjectList = subjectMapper.selectBatchIds(fromTags);
                    Map<Integer, FinancialSubject> subjectMap = subjectList.stream().collect(Collectors.toMap(FinancialSubject::getId, subject -> subject));
                    List<String> codes = subjectList.stream().map(FinancialSubject::getCode).collect(Collectors.toList());

                    List<VoucherDetailVo> detailVos;

                    if (template.getType() == 0) {
                        //个科目的期年统计数据
                        detailVos = voucherMapper.selectReportStatistical(accountSetsId, codes, endTime);
                    } else {
                        //资产负债查余额
                        detailVos = voucherMapper.selectReportBalanceStatistical(accountSetsId, codes, endTime);
                    }


                    //先根据期年进行分组，再根据科目 id 进行数据转换
                    Map<String, Map<String, VoucherDetailVo>> collect = detailVos.stream().collect(Collectors.groupingBy(VoucherDetailVo::getSummary, Collectors.toMap(VoucherDetailVo::getSubjectCode, vo -> vo)));

                    Map<String, VoucherDetailVo> periodData = collect.get("本期");
                    Map<String, VoucherDetailVo> yearData = collect.get("本年");

                    //如果本年余额为空
                    if ((yearData.isEmpty() || yearData.values().stream().allMatch(vo -> vo.isNull())) && template.getType() == 1 && sdf.format(accountSets.getEnableDate()).equals(sdf.format(endTime))) {
                        List<VoucherDetailVo> detailInitVos = voucherMapper.selectReportInitBalance(accountSetsId, codes);
                        yearData = detailInitVos.stream().collect(Collectors.groupingBy(VoucherDetailVo::getSummary, Collectors.toMap(VoucherDetailVo::getSubjectCode, vo -> vo))).get("本年");
                    }

                    //计算公式合计
                    dataVo.setCurrentPeriodAmount(getaCombined(subjectMap, item, periodData));
                    dataVo.setCurrentYearAmount(getaCombined(subjectMap, item, yearData));
                }

                dataVoMap.put(item.getId(), dataVo);
            });

            //表内
            template.getItems().stream().filter(rti -> rti.getSources() == 1 && !rti.getIsClassified()).forEach(item -> {
                ReportDataVo dataVo = new ReportDataVo();
                dataVo.setItemId(item.getId());
                Double periodNum = null;
                Double yearNum = null;
                if (item.getFormulas() != null && !item.getFormulas().isEmpty()) {
                    for (FinancialReportTemplateItemsFormula formula : item.getFormulas()) {
                        //获取表内已计算的
                        ReportDataVo dataVo1 = dataVoMap.get(Integer.parseInt(formula.getFromTag()));
                        if (dataVo1 != null) {
                            if (periodNum == null) {
                                periodNum = dataVo1.getCurrentPeriodAmount();
                            } else {
                                switch (formula.getCalculation().toString()) {
                                    case "+":
                                        periodNum += DoubleValueUtil.getNotNullVal(dataVo1.getCurrentPeriodAmount());
                                        break;
                                    case "-":
                                        periodNum -= DoubleValueUtil.getNotNullVal(dataVo1.getCurrentPeriodAmount());
                                        break;
                                }
                            }

                            if (yearNum == null) {
                                yearNum = dataVo1.getCurrentYearAmount();
                            } else {
                                switch (formula.getCalculation().toString()) {
                                    case "+":
                                        yearNum += DoubleValueUtil.getNotNullVal(dataVo1.getCurrentYearAmount());
                                        break;
                                    case "-":
                                        yearNum -= DoubleValueUtil.getNotNullVal(dataVo1.getCurrentYearAmount());
                                        break;
                                }
                            }
                        }
                    }
                }
                dataVo.setCurrentPeriodAmount(periodNum);
                dataVo.setCurrentYearAmount(yearNum);
                dataVoMap.put(item.getId(), dataVo);
            });
        }
        return dataVoMap;
    }

    /**
     * 根据工具合计
     *
     * @param item
     * @param periodData
     * @return
     */
    private Double getaCombined(Map<Integer, FinancialSubject> codes, FinancialReportTemplateItems item, Map<String, VoucherDetailVo> periodData) {
        double periodNum = 0d;
        if (periodData == null) {
            return periodNum;
        }
        //计算本期合计
        for (FinancialReportTemplateItemsFormula formula : item.getFormulas()) {
            FinancialSubject subject = codes.get(Integer.parseInt(formula.getFromTag()));
            VoucherDetailVo vo = periodData.get(subject.getCode());
            if (vo != null) {
                //0: " 净发生额", 1: "借方发生额", 2: "贷方发生额", 3: "余额", 4: "期末余额", 5: "期初余额"
                double money = 0;
                switch (formula.getAccessRules()) {
                    case 0://净发生额
                    case 3://余额
                        switch (subject.getBalanceDirection() + "") {
                            case "借":
                                money = DoubleValueUtil.getNotNullVal(vo.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vo.getCreditAmount());
                                break;
                            case "贷":
                                money = DoubleValueUtil.getNotNullVal(vo.getCreditAmount()) - DoubleValueUtil.getNotNullVal(vo.getDebitAmount());
                                break;
                        }
                        break;
                    case 1://借方发生额
                        money = DoubleValueUtil.getNotNullVal(vo.getDebitAmount());
                        break;
                    case 2://贷方发生额
                        money = DoubleValueUtil.getNotNullVal(vo.getCreditAmount());
                        break;
                    case 4://借方余额
                        money = DoubleValueUtil.getNotNullVal(vo.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vo.getCreditAmount());
                        break;
                    case 5://贷方余额
                        money = DoubleValueUtil.getNotNullVal(vo.getCreditAmount()) - DoubleValueUtil.getNotNullVal(vo.getDebitAmount());
                        break;
                }

                //计算公式
                switch (formula.getCalculation().toString()) {
                    case "+":
                        periodNum += money;
                        break;
                    case "-":
                        periodNum -= money;
                        break;
                }
            }
        }
        return periodNum;
    }

    @Override
    public FinancialReportTemplate getOne(Wrapper<FinancialReportTemplate> queryWrapper) {
        FinancialReportTemplate template = baseMapper.selectOne(queryWrapper);
        LambdaQueryWrapper<FinancialReportTemplateItems> rtiQw = Wrappers.lambdaQuery();
        rtiQw.orderByAsc(FinancialReportTemplateItems::getPos);
        rtiQw.eq(FinancialReportTemplateItems::getTemplateId, template.getId());
        template.setItems(itemsMapper.selectList(rtiQw));

        template.getItems().forEach(it -> {
            LambdaQueryWrapper<FinancialReportTemplateItemsFormula> rtifQw = Wrappers.lambdaQuery();
            rtifQw.eq(FinancialReportTemplateItemsFormula::getTemplateItemsId, it.getId());
            it.setFormulas(formulaMapper.selectList(rtifQw));
        });

        return template;
    }

    @Override
    public void exportDetail(HttpServletResponse response, Integer accountSetsId, Long reportId, Date startTime, Date endTime) {
        QueryWrapper qw = Wrappers.query();
        qw.eq("id", reportId);
        qw.eq("account_sets_id", accountSetsId);
        FinancialReportTemplate reportTemplate = getOne(qw);
        switch (reportTemplate.getType()){
            case 0://利润表导出
                List<FinancialReportTemplateItems> incomeReportTemplateItems = reportTemplate.getItems();
                Map<Integer, ReportDataVo> incomeViewMap = view(accountSetsId, reportId, startTime, endTime);
                List<IncomeReportExcel> incomeReportExcels = new ArrayList<>();
                incomeReportTemplateItems.forEach(reportTemplateItem->{
                    IncomeReportExcel incomeReportExcel = new IncomeReportExcel();
                    BeanUtils.copyProperties(reportTemplateItem, incomeReportExcel);
                    ReportDataVo reportDataVo = incomeViewMap.get(reportTemplateItem.getId());
                    if(null != reportDataVo.getCurrentYearAmount()){
                        incomeReportExcel.setCurrentYearAmount(BigDecimal.valueOf(reportDataVo.getCurrentYearAmount()));
                    }
                    if(null != reportDataVo.getCurrentPeriodAmount()){
                        incomeReportExcel.setCurrentPeriodAmount(BigDecimal.valueOf(reportDataVo.getCurrentPeriodAmount()));
                    }
                    incomeReportExcels.add(incomeReportExcel);
                });
                ExcelUtil<IncomeReportExcel> incomeExcelUtil = new ExcelUtil<>(IncomeReportExcel.class);
                incomeExcelUtil.exportExcel(response, incomeReportExcels, "利润表");
                break;
            case 1://资产负债表导出
                List<FinancialReportTemplateItems> assetReportTemplateItems = reportTemplate.getItems();
                //资产集合
                List<FinancialReportTemplateItems> assetList = assetReportTemplateItems.stream().filter(e -> e.getType() == 0).collect(Collectors.toList());
                //负债和所有者权益集合
                List<FinancialReportTemplateItems> debtList = assetReportTemplateItems.stream().filter(e -> e.getType() != 0).collect(Collectors.toList());
                Map<Integer, ReportDataVo> assetViewMap = view(accountSetsId, reportId, startTime, endTime);
                List<AssetReportExcel> assetReportExcels = new ArrayList<>();
                for (int i = 0; i < assetList.size(); i++) {
                    //资产数据填充
                    AssetReportExcel assetReportExcel = new AssetReportExcel();
                    FinancialReportTemplateItems assetItem = assetList.get(i);
                    ReportDataVo assetReportDataVo = assetViewMap.get(assetItem.getId());
                    assetReportExcel.setTitle(assetItem.getTitle());
                    assetReportExcel.setLineNum(assetItem.getLineNum());
                    if(null != assetReportDataVo.getCurrentYearAmount()){
                        assetReportExcel.setCurrentYearAmount(BigDecimal.valueOf(assetReportDataVo.getCurrentYearAmount()));
                    }
                    if(null != assetReportDataVo.getCurrentPeriodAmount()){
                        assetReportExcel.setCurrentPeriodAmount(BigDecimal.valueOf(assetReportDataVo.getCurrentPeriodAmount()));
                    }

                    //负债和所有者权益数据填充
                    if(i<debtList.size()){
                        FinancialReportTemplateItems debtItem = debtList.get(i);
                        ReportDataVo debtReportDataVo = assetViewMap.get(debtItem.getId());
                        assetReportExcel.setFsTitle(debtItem.getTitle());
                        assetReportExcel.setFsLineNum(debtItem.getLineNum());
                        if(null != debtReportDataVo.getCurrentYearAmount()){
                            assetReportExcel.setFsCurrentYearAmount(BigDecimal.valueOf(debtReportDataVo.getCurrentYearAmount()));
                        }
                        if(null != debtReportDataVo.getCurrentPeriodAmount()){
                            assetReportExcel.setFsCurrentPeriodAmount(BigDecimal.valueOf(debtReportDataVo.getCurrentPeriodAmount()));
                        }
                    }
                    assetReportExcels.add(assetReportExcel);
                }
                ExcelUtil<AssetReportExcel> assetExcelUtil = new ExcelUtil<>(AssetReportExcel.class);
                assetExcelUtil.exportExcel(response, assetReportExcels, "资产负债表");
                break;
            case 2:
                break;
        }

    }

    /**
     * <AUTHOR>
     * @Description 查询模板列表项信息
     * @Date 2024/10/24 15:00
     * @Param [templateKey, title]
     * @return java.util.List<com.ruoyi.financial.po.ReportTemplatePo>
     **/
    @Override
    public List<ReportTemplatePo> selectReportItemInfo(String templateKey, String title){
        return baseMapper.selectReportItemInfo(templateKey, title);
    }
}
