package com.ruoyi.financial.service;

import java.util.List;

import com.ruoyi.financial.domain.FinancialU8CBankaccount;
import com.ruoyi.financial.domain.vo.BankaccbasVO;

/**
 * 智慧财务系统关联用友U8C银行账户Service接口
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
public interface IFinancialU8CBankaccountService
{
    /**
     * 查询智慧财务系统关联用友U8C银行账户
     *
     * @param accountId 智慧财务系统关联用友U8C银行账户主键
     * @return 智慧财务系统关联用友U8C银行账户
     */
    public FinancialU8CBankaccount selectFinancialU8CBankaccountByAccountId(Long accountId);

    /**
     * 查询智慧财务系统关联用友U8C银行账户列表
     *
     * @param financialU8CBankaccount 智慧财务系统关联用友U8C银行账户
     * @return 智慧财务系统关联用友U8C银行账户集合
     */
    public List<FinancialU8CBankaccount> selectFinancialU8CBankaccountList(FinancialU8CBankaccount financialU8CBankaccount);

    /**
     * 保存智慧财务系统关联用友U8C银行账户
     *
     * @param financialU8CBankaccount 智慧财务系统关联用友U8C银行账户
     * @return 结果
     */
    public int insertFinancialU8CBankaccount(FinancialU8CBankaccount financialU8CBankaccount);

    /**
     * 查询修改记录
     * @param financialU8CBankaccount  搜索条件
     * @return
     */
    List<FinancialU8CBankaccount> selectUpdate(FinancialU8CBankaccount financialU8CBankaccount);

    /**
     * 获取U8C银行账户
     * @return
     */
    List<BankaccbasVO> getU8CBankaccount(FinancialU8CBankaccount financialU8CBankaccount);
}
