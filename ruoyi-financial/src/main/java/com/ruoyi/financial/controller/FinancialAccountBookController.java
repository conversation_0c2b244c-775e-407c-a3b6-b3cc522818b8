package com.ruoyi.financial.controller;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.financial.controller.base.BaseCrudController;
import com.ruoyi.financial.domain.FinancialSubject;
import com.ruoyi.financial.excel.DetailAccountsExcel;
import com.ruoyi.financial.excel.GeneralLedgerExcel;
import com.ruoyi.financial.excel.JournalAccountsExcel;
import com.ruoyi.financial.excel.SubjectBalanceExcel;
import com.ruoyi.financial.service.IFinancialSubjectService;
import com.ruoyi.financial.service.IFinancialVoucherService;
import com.ruoyi.financial.vo.BalanceVo;
import com.ruoyi.financial.vo.SubBalanceVo;
import com.ruoyi.financial.vo.SubjectVo;
import com.ruoyi.financial.vo.VoucherDetailVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.controller</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年09月07日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@RestController
@RequestMapping("/accountBook")
public class FinancialAccountBookController extends BaseCrudController {

    @Autowired
    private IFinancialSubjectService financialSubjectService;
    @Autowired
    private IFinancialVoucherService financialVoucherService;

    @RequestMapping("list")
    public JsonResult accountBookList(Integer accountSetsId, Date startTime, Date endTime) {

        List<FinancialSubject> data = financialSubjectService.accountBookList(accountSetsId, startTime, endTime, false);
        List<SubjectVo> collect = data.stream().map(subject -> {
            SubjectVo subjectVo = new SubjectVo();
            BeanUtils.copyProperties(subject, subjectVo);
            return subjectVo;
        }).collect(Collectors.toList());
        return JsonResult.successful(collect);
    }

    /**
     * 科目余额表
     *
     * @param subBalanceVo
     * @return
     */
    @RequestMapping("subjectBalance")
    public JsonResult subjectBalance(SubBalanceVo subBalanceVo) {
        List<BalanceVo> data = financialSubjectService.subjectBalance(subBalanceVo.getAccountSetsId(), subBalanceVo.getStartTime(), subBalanceVo.getEndTime(), subBalanceVo.getShowNumPrice());
        if(StringUtils.isNotBlank(subBalanceVo.getSubjectName())){
            data = data.stream().filter(e -> e.getName().contains(subBalanceVo.getSubjectName())).collect(Collectors.toList());
        }
        return JsonResult.successful(data);
    }

    /**
     * 科目汇总
     *
     * @param accountSetsId
     * @param startTime
     * @param endTime
     * @param showNumPrice
     * @return
     */
    @RequestMapping("subjectSummary")
    public JsonResult subjectSummary(Integer accountSetsId,Date startTime, Date endTime, Boolean showNumPrice) {
        List data = financialSubjectService.subjectSummary(accountSetsId, startTime, endTime, showNumPrice);
        return JsonResult.successful(data);
    }

    /**
     * 明细账
     *
     * @param accountSetsId
     * @param subjectId
     * @param startTime
     * @param subjectCode
     * @return
     */
    @GetMapping("details")
    public JsonResult accountBookDetails(Integer accountSetsId, Integer subjectId, Date startTime, Date endTime, String subjectCode, Boolean showNumPrice) {
        List<VoucherDetailVo> data = this.financialVoucherService.accountBookDetails(accountSetsId, subjectId, startTime, endTime, subjectCode, showNumPrice);
        return JsonResult.successful(data);
    }

    /**
     * 明细账
     *
     * @param accountSetsId
     * @param startTime
     * @return
     */
    @GetMapping("journalDetails")
    public JsonResult journalDetails(Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice) {
        List<VoucherDetailVo> data = this.financialVoucherService.journalDetails(accountSetsId, startTime, endTime, showNumPrice);
        return JsonResult.successful(data);
    }

    /**
     * 总账
     *
     * @param accountSetsId
     * @param startTime
     * @param endTime
     * @param showNumPrice
     * @return
     */
    @GetMapping("generalLedger")
    public JsonResult generalLedger(Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice) {
        List<Map<String, Object>> data = this.financialVoucherService.accountGeneralLedger(accountSetsId, startTime, endTime, showNumPrice);
        return JsonResult.successful(data);
    }

    /**
     * 辅助明细账
     *
     * @param accountSetsId
     * @param auxiliaryId
     * @param auxiliaryItemId
     * @return
     */
    @GetMapping("auxiliaryDetails")
    public JsonResult auxiliaryDetails(Integer accountSetsId, Integer auxiliaryId, Date startTime, Date endTime, Integer auxiliaryItemId, Boolean showNumPrice) {
        List data = this.financialVoucherService.auxiliaryDetails(accountSetsId, auxiliaryId, startTime, endTime, auxiliaryItemId, showNumPrice);
        return JsonResult.successful(data);
    }

    /**
     * 当期核算项目列表
     *
     * @param accountSetsId
     * @param auxiliaryId
     * @return
     */
    @GetMapping("auxiliaryList")
    public JsonResult auxiliaryList(Integer accountSetsId, Integer auxiliaryId) {
        List data = this.financialVoucherService.auxiliaryList(accountSetsId, auxiliaryId);
        return JsonResult.successful(data);
    }

    /**
     * 核算项目余额
     *
     * @param startTime
     * @param auxiliaryId
     * @return
     */
    @GetMapping("auxiliaryBalance")
    public JsonResult auxiliaryBalance(Integer accountSetsId, Date startTime, Date endTime, Integer auxiliaryId, Boolean showNumPrice) {
        List data = this.financialVoucherService.auxiliaryBalance(accountSetsId, auxiliaryId, startTime, endTime, showNumPrice);
        return JsonResult.successful(data);
    }

    /**
     * 导出外部系统平台运营情况数据列表
     */
    @GetMapping("getEarliestTime")
    public JsonResult getEarliestTime(Integer accountSetsId) {
        return JsonResult.successful(this.financialVoucherService.getEarliestTime(accountSetsId));
    }

    /**
     * 账簿明细账导出
     */
    @Log(title = "账簿明细账导出", businessType = BusinessType.EXPORT)
    @PostMapping("exportDetail")
    public void exportDetail(HttpServletResponse response, Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice, Integer subjectId, String subjectCode) {
        List<DetailAccountsExcel> detailAccountsExcels = this.financialVoucherService.exportDetail(accountSetsId, startTime, endTime, showNumPrice, subjectId, subjectCode);
        ExcelUtil<DetailAccountsExcel> excelUtil = new ExcelUtil<>(DetailAccountsExcel.class);
        excelUtil.exportExcel(response, detailAccountsExcels, "账簿明细账");
    }

    /**
     * 账簿总账导出
     */
    @Log(title = "账簿总账导出", businessType = BusinessType.EXPORT)
    @PostMapping("exportGeneralLedger")
    public void exportGeneralLedger(HttpServletResponse response, Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice) {
        List<GeneralLedgerExcel> generalLedgerExcels = this.financialVoucherService.exportGeneralLedger(accountSetsId, startTime, endTime, showNumPrice);
        ExcelUtil<GeneralLedgerExcel> excelUtil = new ExcelUtil<>(GeneralLedgerExcel.class);
        excelUtil.exportExcel(response, generalLedgerExcels, "账簿总账");
    }

    /**
     * 账簿总账导出
     */
    @Log(title = "账簿科目余额导出", businessType = BusinessType.EXPORT)
    @PostMapping("exportSubjectBalance")
    public void exportSubjectBalance(HttpServletResponse response, Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice) {
        List<SubjectBalanceExcel> subjectBalanceExcels = financialSubjectService.exportSubjectBalance(accountSetsId, startTime, endTime, showNumPrice);
        ExcelUtil<SubjectBalanceExcel> excelUtil = new ExcelUtil<>(SubjectBalanceExcel.class);
        excelUtil.exportExcel(response, subjectBalanceExcels, "账簿科目余额");
    }

    /**
     * 账簿明细账导出
     */
    @Log(title = "账簿序时账导出", businessType = BusinessType.EXPORT)
    @PostMapping("exportJournalDetail")
    public void exportJournalDetail(HttpServletResponse response, Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice) {
        List<JournalAccountsExcel> journalAccountsExcels = this.financialVoucherService.exportJournalDetail(accountSetsId, startTime, endTime, showNumPrice);
        ExcelUtil<JournalAccountsExcel> excelUtil = new ExcelUtil<>(JournalAccountsExcel.class);
        excelUtil.exportExcel(response, journalAccountsExcels, "账簿序时账");
    }
}

