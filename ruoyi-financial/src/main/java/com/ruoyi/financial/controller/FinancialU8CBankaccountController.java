package com.ruoyi.financial.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.financial.domain.FinancialU8CBankaccount;
import com.ruoyi.financial.domain.PushU8CLedger;
import com.ruoyi.financial.domain.vo.BankaccbasVO;
import com.ruoyi.financial.service.IFinancialU8CBankaccountService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 智慧财务系统关联用友U8C银行账户Controller
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
@RestController
@RequestMapping("/U8C/bankaccount")
public class FinancialU8CBankaccountController extends BaseController
{
    @Autowired
    private IFinancialU8CBankaccountService service;

    /**
     * 查询智慧财务系统关联用友U8C银行账户列表
     */
    @GetMapping("/list")
    public TableDataInfo list(FinancialU8CBankaccount financialU8CBankaccount)
    {
        startPage();
        List<FinancialU8CBankaccount> list = service.selectFinancialU8CBankaccountList(financialU8CBankaccount);
        return getDataTable(list);
    }

    /**
     * 导出智慧财务系统关联用友U8C银行账户列表
     */
    @Log(title = "智慧财务系统关联用友U8C银行账户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinancialU8CBankaccount financialU8CBankaccount)
    {
        List<FinancialU8CBankaccount> list = service.selectFinancialU8CBankaccountList(financialU8CBankaccount);
        ExcelUtil<FinancialU8CBankaccount> util = new ExcelUtil<FinancialU8CBankaccount>(FinancialU8CBankaccount.class);
        util.exportExcel(response, list, "智慧财务系统关联用友U8C银行账户数据");
    }

    /**
     * 获取智慧财务系统关联用友U8C银行账户详细信息
     */
    @GetMapping(value = "/{accountId}")
    public AjaxResult getInfo(@PathVariable("accountId") Long accountId)
    {
        return AjaxResult.success(service.selectFinancialU8CBankaccountByAccountId(accountId));
    }

    /**
     * 保存智慧财务系统关联用友U8C银行账户
     */
    @Log(title = "智慧财务系统关联用友U8C银行账户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinancialU8CBankaccount financialU8CBankaccount)
    {
        return toAjax(service.insertFinancialU8CBankaccount(financialU8CBankaccount));
    }

    /**
     * 查询修改记录
     * @param financialU8CBankaccount 查询的数据的id等
     * @return
     */
    @PostMapping("/selectUpdate")
    public TableDataInfo selectUpdate(@RequestBody FinancialU8CBankaccount financialU8CBankaccount){
        List<FinancialU8CBankaccount> list = service.selectUpdate(financialU8CBankaccount);
        return getDataTable(list);
    }

    /**
     * 查询U8C所有银行账户
     * @return
     */
    @PostMapping("/getU8CBankaccount")
    public TableDataInfo getU8CBankaccount(@RequestBody FinancialU8CBankaccount financialU8CBankaccount){
        List<BankaccbasVO> list = service.getU8CBankaccount(financialU8CBankaccount);
        return getDataTable(list);
    }
}

