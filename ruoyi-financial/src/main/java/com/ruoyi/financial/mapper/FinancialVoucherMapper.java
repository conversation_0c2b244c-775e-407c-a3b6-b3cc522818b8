package com.ruoyi.financial.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.financial.domain.FinancialVoucher;
import com.ruoyi.financial.excel.VoucherExcel;
import com.ruoyi.financial.vo.VoucherDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.kernel.model.mapper</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年10月21日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
public interface FinancialVoucherMapper extends BaseMapper<FinancialVoucher> {
    int batchInsert(@Param("list") List<FinancialVoucher> list);

    Integer selectMaxCode(@Param("accountSetsId") Integer accountSetsId, @Param("word") String word, @Param("year") int year, @Param("month") int month);

    List<FinancialVoucher> selectVoucher(@Param(Constants.WRAPPER) Wrapper wrapper);

    IPage<FinancialVoucher> selectVoucher(Page<FinancialVoucher> page, @Param(Constants.WRAPPER) Wrapper wrapper);

    List<VoucherDetailVo> selectAccountBookDetails(@Param("accountSetsId") Integer accountSetsId, @Param("subjectIds") List<Integer> subjectIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<VoucherDetailVo> selectAccountBookStatistical(@Param("accountSetsId") Integer accountSetsId, @Param("subjectId") Integer subjectId, @Param("subjectIds") List<Integer> subjectIds, @Param("endTime") Date endTime);

    List<VoucherDetailVo> selectAccountBookInitialBalance(@Param("accountSetsId") Integer accountSetsId, @Param("subjectIds") List<Integer> subjectIds, @Param("startTime") Date startTime);

    List<Map<String, Object>> selectBrokenData(@Param("accountSetsId") Integer accountSetsId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<VoucherDetailVo> selectReportStatistical(@Param("accountSetsId") Integer accountSetsId, @Param("codes") Collection<String> codes, @Param("accountDate") Date accountDate);

    List<Map<String, Object>> selectHomeReport(@Param("accountSetsId") Integer accountSetsId, @Param("voucherYear") Integer year);

    List<Map<String, Object>> selectHomeCostReport(@Param("accountSetsId") Integer accountSetsId, @Param("voucherYear") Integer year, @Param("voucherMonth") Integer month);

    List<Map<String, Object>> selectHomeCashReport(@Param("accountSetsId") Integer accountSetsId, @Param("voucherYear") Integer year, @Param("voucherMonth") Integer month);

    Integer selectBeforeId(@Param("accountSetsId") Integer accountSetsId, @Param("currentId") Integer currentId, @Param("voucherYear")Integer voucherYear, @Param("voucherMonth")Integer voucherMonth);

    Integer selectNextId(@Param("accountSetsId") Integer accountSetsId, @Param("currentId") Integer currentId, @Param("voucherYear")Integer voucherYear, @Param("voucherMonth")Integer voucherMonth);

    List<VoucherDetailVo> selectSubjectDetail(@Param("subjectIds") Set<Integer> subjectIds, @Param("accountSetsId") Integer accountSetsId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("showNumPrice") boolean showNumPrice);

    Integer updateAudit(@Param(Constants.WRAPPER) Wrapper wrapper);

    List<VoucherDetailVo> selectReportBalanceStatistical(@Param("accountSetsId") Integer accountSetsId, @Param("codes") List<String> codes, @Param("accountDate") Date accountDate);

    List<VoucherDetailVo> selectReportInitBalance(@Param("accountSetsId") Integer accountSetsId, @Param("codes") List<String> codes);

    Date selectMaxVoucherDate(@Param("accountSetsId") Integer accountSetsId);

    Date selectMinVoucherDate(@Param("accountSetsId") Integer accountSetsId);

    IPage<FinancialVoucher> listVoucher(Page<FinancialVoucher> pageable, @Param("ew")QueryWrapper qw);


    Boolean validVoucher(@Param("voucherId") Integer voucherId);

    List<FinancialVoucher> selectByAccountSetsId(@Param("accountSetsId")Integer accountSetsId);

    /**
     * <AUTHOR>
     * @Description 凭证号重新排序
     * @Date 2024/3/27 16:48
     * @Param [accountSetsId, s, s1]
     * @return void
     **/
    int reorderCode(@Param("accountSetsId")Integer accountSetsId, @Param("voucherYear")String voucherYear, @Param("voucherMonth")String voucherMonth);

    /**
     * 导出凭证信息
     * @param params
     * @return
     */
    List<VoucherExcel> exportVoucher(Map<String, String> params);
}