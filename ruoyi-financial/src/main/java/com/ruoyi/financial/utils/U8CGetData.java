package com.ruoyi.financial.utils;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.exception.ServiceException;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 获取用友U8C数据
 */
public class U8CGetData {

    /**
     * 获取U8C数据
     * @param apiUrl    请求地址
     * @param sendData  请求参数
     * @param userCode  用户名
     * @param passWord  密码
     * @param system    系统
     * @return
     */
    public String getU8CResponse(String apiUrl, Object sendData, String userCode, String passWord, String system) {
        String result = null;
        try {
            // 使用U8cloud系统中设置，具体节点路径为：
            // 应用集成 - 系统集成平台 - 系统信息设置
            // 设置信息中具体属性的对照关系如下：
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("system", system); // 系统编码
            map.put("usercode", userCode); // 用户
            map.put("password", passWord); // 密码，需要 MD5 加密后录入
            // 返回结果
            HttpClient httpClient = new HttpClient();
            PostMethod httpPost = new PostMethod(apiUrl);
            httpPost.setRequestHeader("content-type",
                    "application/json;charset=utf-8");
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                httpPost.setRequestHeader(entry.getKey(), entry.getValue()
                        .toString());
            }
            StringRequestEntity entity = new StringRequestEntity(JSON.toJSONString(sendData), "application/json", "UTF-8");
            httpPost.setRequestEntity(entity);
            httpClient.executeMethod(httpPost);
            result = httpPost.getResponseBodyAsString();
            if(result == null){
                throw new ServiceException("未接收到用友U8C返回的参数，请联系系统管理员处理");
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
