
SELECT @shouxinid1 := (select menu_id from sys_menu where menu_name = '授信及贷后资料管理');

SELECT @shouxinziliaoxiazaiid1 := (select menu_id from sys_menu where menu_name = '资料下载审批' and parent_id = @shouxinid1);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出', @shouxinziliaoxiazaiid1, 1, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:dataUpLoadApproval:export', '#', 'admin', '2024-11-07 10:03:10', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('下载流程', @shouxinziliaoxiazaiid1, 2, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:dataUpLoadApproval:downloadProcess', '#', 'admin', '2024-11-07 10:05:15', 'admin', '2024-11-07 10:06:10', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('直接用印流程', @shouxinziliaoxiazaiid1, 3, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:dataUpLoadApproval:sealsProcess', '#', 'admin', '2024-11-07 10:06:57', '', null, '');

SELECT @shouxinziliaomuluid1 := (select menu_id from sys_menu where menu_name = '资料目录' and parent_id = @shouxinid1);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('新建', @shouxinziliaomuluid1, 1, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:directoryMation:add', '#', 'admin', '2024-11-07 10:08:32', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('修改', @shouxinziliaomuluid1, 2, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:directoryMation:update', '#', 'admin', '2024-11-14 11:04:37', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('删除', @shouxinziliaomuluid1, 3, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:directoryMation:delete', '#', 'admin', '2024-11-14 11:04:56', '', null, '');

SELECT @luruziliaoid1 := (select menu_id from sys_menu where menu_name = '录入资料' and parent_id = @shouxinid1);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('新建', @luruziliaoid1, 1, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:inputData:add', '#', 'admin', '2024-11-07 10:10:01', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('提交', @luruziliaoid1, 2, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:inputData:submit', '#', 'admin', '2024-11-07 10:10:24', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出', @luruziliaoid1, 3, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:inputData:export', '#', 'admin', '2024-11-07 10:10:55', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('资料废弃', @luruziliaoid1, 4, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:inputData:abandoned', '#', 'admin', '2024-11-07 10:11:26', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('资料整理', @luruziliaoid1, 5, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:inputData:arrangement', '#', 'admin', '2024-11-07 10:11:57', '', null, '');

delete from sys_menu where menu_name= '部门授权' and parent_id = @luruziliaoid1;
delete from sys_menu where menu_name= '岗位授权' and parent_id = @luruziliaoid1;
delete from sys_menu where menu_name= '人员授权' and parent_id = @luruziliaoid1;

SELECT @lishixinxijilu1 := (select menu_id from sys_menu where menu_name = '历史信息记录' and parent_id = @shouxinid1);
INSERT INTO  sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('授权记录', @lishixinxijilu1, 1, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:historyMation:authorization', '#', 'admin', '2024-11-07 10:20:38', 'admin', '2024-11-07 10:28:34', '');
INSERT INTO  sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('过期资料', @lishixinxijilu1, 2, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:historyMation:overdue', '#', 'admin', '2024-11-07 10:21:58', 'admin', '2024-11-07 10:24:24', '');
INSERT INTO  sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('审核记录', @lishixinxijilu1, 3, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:historyMation:examine', '#', 'admin', '2024-11-07 10:22:16', 'admin', '2024-11-07 10:25:26', '');
INSERT INTO  sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('废弃资料', @lishixinxijilu1, 4, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:historyMation:abandoned', '#', 'admin', '2024-11-07 10:22:50', 'admin', '2024-11-07 10:24:51', '');

SELECT @ziliaofangfatongji1 := (select menu_id from sys_menu where menu_name = '资料发放统计' and parent_id = @shouxinid1);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出', @ziliaofangfatongji1, 1, '', null, null, 1, 0, 'F', '0', '0', 'dataManagement:dataStatistics:export', '#', 'admin', '2024-11-07 10:19:27', '', null, '');



SELECT @jianguanid1 := (select menu_id from sys_menu where menu_name = '监管报送资料管理');

SELECT @jgxiazid1 := (select menu_id from sys_menu where menu_name = '资料下载审批/用印审批' and parent_id = @jianguanid1);
INSERT INTO mgrdb_dev.sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出', @jgxiazid1, 1, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:dataUpLoadApproval:export', '#', 'admin', '2024-11-07 10:35:35', '', null, '');
INSERT INTO mgrdb_dev.sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('下载流程', @jgxiazid1, 2, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:dataUpLoadApproval:download', '#', 'admin', '2024-11-07 10:39:02', '', null, '');
INSERT INTO mgrdb_dev.sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('用印流程', @jgxiazid1, 3, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:seals', '#', 'admin', '2024-11-07 10:41:34', 'admin', '2024-11-07 10:41:53', '');

SELECT @jgziliaomulu1 := (select menu_id from sys_menu where menu_name = '资料目录' and parent_id = @jianguanid1);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('新建', @jgziliaomulu1, 1, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:directoryMation:add', '#', 'admin', '2024-11-07 10:34:33', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('修改', @jgziliaomulu1, 2, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:directoryMation:update', '#', 'admin', '2024-11-14 11:05:41', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('删除', @jgziliaomulu1, 3, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:directoryMation:delete', '#', 'admin', '2024-11-14 11:06:03', '', null, '');

SELECT @jgluriziliao1 := (select menu_id from sys_menu where menu_name = '录入资料' and parent_id = @jianguanid1);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('新建', @jgluriziliao1, 1, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:inputData:add', '#', 'admin', '2024-11-07 10:31:37', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('提交', @jgluriziliao1, 2, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:inputData:submit', '#', 'admin', '2024-11-07 10:31:56', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出列表', @jgluriziliao1, 3, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:inputData:export', '#', 'admin', '2024-11-07 10:32:15', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('资料废弃', @jgluriziliao1, 4, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:inputData:abandoned', '#', 'admin', '2024-11-07 10:32:59', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('资料整理', @jgluriziliao1, 5, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:inputData:arrangement', '#', 'admin', '2024-11-07 10:33:42', '', null, '');


SELECT @jglishixinxi1 := (select menu_id from sys_menu where menu_name = '历史信息记录' and parent_id = @jianguanid1);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('过期资料', @jglishixinxi1, 1, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:historyMation:overdue', '#', 'admin', '2024-11-07 10:28:57', 'admin', '2024-11-07 10:29:15', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('审核记录', @jglishixinxi1, 2, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:historyMation:examine', '#', 'admin', '2024-11-07 10:29:32', 'admin', '2024-11-07 10:30:01', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('废弃资料', @jglishixinxi1, 3, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:historyMation:abandoned', '#', 'admin', '2024-11-07 10:29:48', 'admin', '2024-11-07 10:30:12', '');

SELECT @jgziliaofafang1 := (select menu_id from sys_menu where menu_name = '资料发放统计' and parent_id = @jianguanid1);
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出', @jgziliaofafang1 , 1, '', null, null, 1, 0, 'F', '0', '0', 'dataManagementSupervise:dataStatistics:export', '#', 'admin', '2024-11-07 10:15:46', '', null, '');
