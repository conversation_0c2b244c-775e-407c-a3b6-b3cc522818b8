SELECT @x := (select user_id from sys_user where nick_name = '崔腾越');
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @x, 'DEBTCONVERSION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());

SELECT @y := (select user_id from sys_user where nick_name = '张赫');
INSERT INTO auth_main (`third_type`, `third_id`, `module_type`, `role_type`, `permission_scope`, `permission_type`, `permission_time`, `status`, `send_notify`, `create_id`, `create_dept_id`, `create_unit_id`, `create_time`,  `update_id`, `update_dept_id`, `update_unit_id`, `update_time`)
VALUES ('1', @y, 'DEBTCONVERSION','COMMON','UNIT0', '1', '9999-12-31 23:59:59', '0', '-1',  0, 1, 1, NOW(),  0, 1, 1, NOW());
