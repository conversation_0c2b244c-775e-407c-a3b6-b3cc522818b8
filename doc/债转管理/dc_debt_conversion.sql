/*
 Navicat Premium Data Transfer

 Source Server         : mgrdb_dev_localhost
 Source Server Type    : MySQL
 Source Server Version : 50740
 Source Host           : localhost:3306
 Source Schema         : 周伟

 Target Server Type    : MySQL
 Target Server Version : 50740
 File Encoding         : 65001

 Date: 12/05/2025 17:05:48
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for dc_debt_conversion
-- ----------------------------
DROP TABLE IF EXISTS `dc_debt_conversion`;
CREATE TABLE `dc_debt_conversion`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `file_id` bigint(20) NULL DEFAULT NULL COMMENT '关联上传文件',
  `loan_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借款申请编号',
  `borrower` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借款人',
  `id_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `phone_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `loan_time` datetime(0) NULL DEFAULT NULL COMMENT '借款时间',
  `guarantee_time` datetime(0) NULL DEFAULT NULL COMMENT '担保时间',
  `cust_id` bigint(20) NULL DEFAULT NULL COMMENT '担保公司',
  `partner_id` bigint(20) NULL DEFAULT NULL COMMENT '资产方',
  `fund_id` bigint(20) NULL DEFAULT NULL COMMENT '资金方',
  `loan_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '借款金额',
  `debt_recipient_id` bigint(20) NULL DEFAULT NULL COMMENT '债权接收方',
  `debt_conversion_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '债转通知编号',
  `invoice_status` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '申请开票状态 1.未申请 2.已申请',
  `push_channel` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推送渠道 1.小程序',
  `is_read` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '用户是否阅读  1.否 2.是 ',
  `read_time` datetime(0) NULL DEFAULT NULL COMMENT '阅读时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '债转通知明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dc_debt_conversion_file
-- ----------------------------
DROP TABLE IF EXISTS `dc_debt_conversion_file`;
CREATE TABLE `dc_debt_conversion_file`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cust_id` bigint(20) NULL DEFAULT NULL COMMENT '担保公司',
  `debt_conversion_theme` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '债转主题',
  `notice_launch_time` datetime(0) NULL DEFAULT NULL COMMENT '债转通知发起时间',
  `notice_complete_time` datetime(0) NULL DEFAULT NULL COMMENT '债转通知完成时间',
  `push_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '推送状态 1.未推送 2.推送中 3.推送完成',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '债转文件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dc_debt_user
-- ----------------------------
DROP TABLE IF EXISTS `dc_debt_user`;
CREATE TABLE `dc_debt_user`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `phone_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `id_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证',
  `cust_id` bigint(20) NULL DEFAULT NULL COMMENT '注册公司(担保公司)',
  `data_source` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源 1.小程序',
  `last_login_time` datetime(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '债转用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dc_invoicing_application
-- ----------------------------
DROP TABLE IF EXISTS `dc_invoicing_application`;
CREATE TABLE `dc_invoicing_application`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `file_id` bigint(20) NULL DEFAULT NULL COMMENT '关联上传文件',
  `loan_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借款申请编号',
  `borrower` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借款人',
  `phone_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `id_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `invoicing_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '申请开票金额(元)',
  `fund_id` bigint(20) NULL DEFAULT NULL COMMENT '资金方',
  `receiving_email` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收邮箱',
  `partner_id` bigint(20) NULL DEFAULT NULL COMMENT '资产方',
  `loan_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '借款金额(元)',
  `loan_time` datetime(0) NULL DEFAULT NULL COMMENT '借款时间',
  `settle_time` datetime(0) NULL DEFAULT NULL COMMENT '结清日期',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `invoicing_application_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开票编号',
  `invoicing_application_time` datetime(0) NULL DEFAULT NULL COMMENT '开票时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '开票申请明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dc_invoicing_application_file
-- ----------------------------
DROP TABLE IF EXISTS `dc_invoicing_application_file`;
CREATE TABLE `dc_invoicing_application_file`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `main_body_id` bigint(20) NULL DEFAULT NULL COMMENT '开票主体',
  `invoicing_theme` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开票主题',
  `invoice_status` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '开票状态(1.未开票 2.已开票)',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '开票申请文件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dc_invoicing_business
-- ----------------------------
DROP TABLE IF EXISTS `dc_invoicing_business`;
CREATE TABLE `dc_invoicing_business`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `invoicing_application_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开票编号',
  `invoicing_application_time` datetime(0) NULL DEFAULT NULL COMMENT '开票时间',
  `channel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道',
  `borrower` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借款人',
  `phone_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `id_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `invoicing_amount_total` decimal(18, 2) NULL DEFAULT NULL COMMENT '开票金额(元)',
  `file_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件名称',
  `file_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件地址',
  `main_body_id` bigint(20) NULL DEFAULT NULL COMMENT '开票主体',
  `receiving_email` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收邮箱',
  `push_time` datetime(0) NULL DEFAULT NULL COMMENT '邮箱推送时间',
  `invoicing_status` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '开票状态(1.开票中 2.开票成功 3.开票失败)',
  `invoicing_type` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票类型(1.普通发票-pdf)',
  `invoicing_head_type` int(11) NULL DEFAULT NULL COMMENT '抬头类型(1.个人)',
  `reason_delete_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除附件原因',
  `reason_fail` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '失败原因',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '开票申请业务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dc_invoicing_middle
-- ----------------------------
DROP TABLE IF EXISTS `dc_invoicing_middle`;
CREATE TABLE `dc_invoicing_middle`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `invoicing_business_id` bigint(20) NULL DEFAULT NULL COMMENT '开票业务 id',
  `correlation_id` bigint(20) NULL DEFAULT NULL COMMENT '关联数据 id',
  `correlation_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联数据类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '开票申请业务关联数据中间表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dim_base_prod_info
-- ----------------------------
DROP TABLE IF EXISTS `dim_base_prod_info`;
CREATE TABLE `dim_base_prod_info`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `product_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品编号',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `guarantor_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '担保方编号',
  `guarantor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '担保方',
  `platform_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台方编号',
  `platform_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台方',
  `financer_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金方编号',
  `financer_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金方',
  `project_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目编号',
  `system_no` bigint(20) NULL DEFAULT NULL COMMENT '系统',
  `oa_platform_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部系统平台编码',
  `oa_cust_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '担保公司编码',
  `oa_partner_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作方编码',
  `oa_fund_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金方编码',
  `oa_it_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科技方编码',
  `is_project_finish` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目是否已结束,Y是N否',
  `project_finish_date` date NULL DEFAULT NULL COMMENT '项目结束日期',
  `is_project_company` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户是否为企业,Y是C同时含有个人N否',
  `is_project_td` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否通道简版数据,Y是N否',
  `is_project_plan` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否有还款计划数据,Y有N否',
  `is_project_plan_update` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否有还款计划实还更新数据,Y有C有但未上线N否',
  `is_project_plan_reset` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否有还款计划缩期情况,Y有C有但未上线N否',
  `is_project_repay1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否有正常还款数据,Y有C有但未上线N否',
  `is_project_repay4` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否有提前还款数据,Y有C有但未上线N否',
  `is_project_repay5` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否有提前结清数据,Y有C有但未上线N否',
  `is_project_repay7` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否有代偿还款数据,Y有C有但未上线N否',
  `is_project_total_repay7` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否有累计代偿还款数据,Y有C有但未上线N否',
  `is_project_total_repay` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否有用户累计还款数据,Y有C有但未上线N否',
  `is_project_repay8` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否有追偿还款数据,Y有C有但未上线N否',
  `is_project_repay7_finish` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据是否代偿时结清,Y是N否',
  `is_project_repay8_normal` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '借据追偿还款是否按正常还款提供,Y是N否',
  `is_result_fpd10` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'FPD10是否可用，Y是N否',
  `is_result_vintage` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'vintage是否可用，Y是N否',
  `is_result_balance_distribution` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '余额分布是否可用，Y是N否',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态（0正常 1停用）',
  `check_status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核状态 0新增审核状态，1修改项目审核中，2删除项目审核中，3（-）',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0),
  `update_time` timestamp(0) NOT NULL,
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `biz_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务类型 01-个人业务 02-对公业务',
  `product_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品类型 11-个人住房商业贷款 12-个人商用房（含商住两用）贷款 13-个人住房公积金贷款 21-个人汽车消费贷款 41-个人经营性贷款 42-个人创业担保贷款 51-农户贷款 52-经营性农户贷款 53-消费性农户贷款 91-其他个人消费贷款 99-其他贷款',
  `min_credit_limit` bigint(20) NULL DEFAULT 0 COMMENT '最低授信额度',
  `max_credit_limit` bigint(20) NULL DEFAULT 0 COMMENT '最高授信额度',
  `min_inte_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '最低利率 年化小数',
  `max_inte_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '最高利率  年化小数',
  `min_credit_spread` int(11) NULL DEFAULT 0 COMMENT '最短授信期限',
  `max_credit_spread` int(11) NULL DEFAULT 0 COMMENT '最长授信期限',
  `grt_method` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '担保方式 01-信用 02-抵押 03-质押',
  `repayment_method` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '还款方式 11-分期等额本息 12-分期等额本金 13-到期还本分期结息 21-到期一次还本付息 90-不区分还款方式',
  `prepayment` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否允许提前还款 0-可以提前还款 1-不可以提前还款',
  `repayment_data` tinyint(4) NULL DEFAULT NULL COMMENT '还款数据优先级 1-优先还款信息 2-优先还款计划',
  `act_year_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '利率（实际年化）',
  `service_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '前期服务费费率',
  `guarantee_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '前期担保费费率',
  `margin_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '前期保证金费率',
  `compensate_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '前期代偿金费率',
  `period_service_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '年化服务费费率',
  `period_guarantee_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '年化担保费费率',
  `period_margin_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '年化保证金费率',
  `period_compensate_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '年化代偿金费率',
  `oint_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '罚息日利率',
  `define_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '逾期违约金/滞纳金费率',
  `adv_define_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '提前还款违约金费率',
  `compensate_days` smallint(6) NULL DEFAULT NULL COMMENT '代偿天数',
  `cpst_rule_no` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '代偿规则编号',
  `grace_day` smallint(6) NULL DEFAULT NULL COMMENT '宽限期',
  `interest_free_period` smallint(6) NULL DEFAULT NULL COMMENT '免息期',
  `is_cpst` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否提供代偿 0-是 1-否',
  `is_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否提供追偿 0-是 1-否',
  `fee1_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '费率1',
  `fee2_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '费率2',
  `fee3_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '费率3',
  `fee4_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '费率4',
  `fee5_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '费率5',
  `fee6_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '费率6',
  `fee7_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '费率7',
  `fee8_rate` decimal(11, 8) NULL DEFAULT 0.00000000 COMMENT '费率8',
  `dw_create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `dw_update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `is_end` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否结束 Y是  N否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dim_bill_no_info
-- ----------------------------
DROP TABLE IF EXISTS `dim_bill_no_info`;
CREATE TABLE `dim_bill_no_info`  (
  `bill_app_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '借据编号',
  `loan_month` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '放款月份',
  `product_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品编号',
  `loan_date` date NULL DEFAULT NULL COMMENT '放款日期',
  `due_date` date NULL DEFAULT NULL COMMENT '到期日期',
  `loan_amt` decimal(17, 2) NULL DEFAULT NULL COMMENT '放款金额',
  `loan_period` int(11) NULL DEFAULT NULL COMMENT '借款期限',
  `loan_term` int(11) NULL DEFAULT NULL COMMENT '分期期数',
  `guarantor_no` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '担保公司',
  `id_card_type` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件类型',
  `id_card` varchar(21) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件号码',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户姓名',
  `farmer_flag` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '农户标识',
  `data_source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据来源',
  `province_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户所在省份',
  `city_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户所在市',
  `dw_create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `dw_update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`bill_app_no`) USING BTREE,
  INDEX `idx_bill_app_no`(`bill_app_no`) USING BTREE,
  INDEX `idx_id_card_mobile`(`id_card`, `mobile`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '借据信息表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
