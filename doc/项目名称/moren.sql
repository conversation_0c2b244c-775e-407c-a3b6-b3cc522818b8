INSERT INTO sys_company
(company_name, company_short_name, is_inside, status, source, check_status, create_by, create_time, update_by, update_time, company_code, company_no, linkman, phone, email, postcode, business_address, website, registered_address, is_delete)
VALUES
    ('演示公司', '演示公司', '1', '0', '1', null, 'admin', '2023-09-12 13:40:18', null, '2025-01-20 13:51:47', 'YSGS', '', '', '', '', '', '', '', '', '0');
SELECT @演示公司 := LAST_INSERT_ID();

INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES ('公司类型', 'company_type', '0', 'admin', '2024-04-16 16:23:20', '', null, null);
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES ('公司业务类型', 'company_business_type', '0', 'admin', '2024-04-16 16:24:33', '', null, null);

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '演示公司类型', '1', 'company_type', '内部', null, 'default', 'N', '0', 'admin', '2024-04-16 16:40:28', 'admin', '2024-12-20 19:40:23', null);
SELECT @演示公司类型 := LAST_INSERT_ID();
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '演示公司业务类型', '1', 'company_business_type', '内部', null, 'default', 'N', '0', 'admin', '2024-04-16 16:40:28', 'admin', '2024-12-20 19:40:23', null);
SELECT @演示公司业务类型 := LAST_INSERT_ID();


INSERT INTO company_type_mapping (company_id, company_type_code, create_by, create_time, update_by, update_time) VALUES (@演示公司, @演示公司类型, 'admin', '2024-05-22 14:16:36', null, '2024-08-15 09:54:14');
INSERT INTO company_type_mapping (company_id, company_type_code, create_by, create_time, update_by, update_time) VALUES (@演示公司, @演示公司业务类型, 'admin', '2024-05-22 14:16:36', null, '2024-08-15 09:58:31');
