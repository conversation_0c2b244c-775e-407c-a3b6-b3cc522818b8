delete from sys_menu where menu_name = '绩效考核';
delete from sys_menu where menu_name = '年度计划';
delete from sys_menu where menu_name = '项目业绩录入';
delete from sys_menu where menu_name = '考核配置';
delete from sys_menu where menu_name = '考核结果';

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('绩效考核', 0, 19, 'perAppraisal', null, null, 1, 0, 'M', '0', '0', null, 'system', 'admin', '2024-07-10 09:17:20', '', null, '');
SELECT @temp := LAST_INSERT_ID();
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('年度计划', @temp, 1, 'annualPlan', 'perAppraisal/annualPlan/index.vue', null, 1, 1, 'C', '0', '0', '', 'edit', 'admin', '2024-07-10 09:24:02', 'admin', '2024-07-10 09:24:33', '');
SELECT @jihua := LAST_INSERT_ID();
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('项目业绩录入', @temp, 3, 'projectPerformance', 'perAppraisal/projectPerformance/index.vue', null, 1, 1, 'C', '0', '0', '', 'search', 'admin', '2024-07-18 15:49:53', 'admin', '2024-08-07 10:35:10', '');
SELECT @luru := LAST_INSERT_ID();
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('考核配置', @temp, 2, 'assessmentConfiguration', 'perAppraisal/assessmentConfiguration/index.vue', null, 1, 0, 'C', '0', '0', '', 'edit', 'admin', '2024-07-23 14:32:01', 'admin', '2024-09-12 14:01:10', '');
SELECT @peizhi := LAST_INSERT_ID();
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('考核结果', @temp, 4, 'assessmentResults', 'perAppraisal/assessmentResults/index.vue', null, 1, 1, 'C', '0', '0', '', 'search', 'admin', '2024-07-31 16:45:16', 'admin', '2024-08-07 10:35:17', '');
SELECT @jieguo := LAST_INSERT_ID();

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('增加公司年度计划', @jihua, 1, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:annualPlan:add', '#', 'admin', '2024-09-12 16:11:22', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('提交年度业绩审核', @jihua, 2, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:annualPlan:submit', '#', 'admin', '2024-09-12 16:12:17', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出列表', @jihua, 3, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:annualPlan:export', '#', 'admin', '2024-09-12 16:12:39', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('批量导入年度计划', @jihua, 4, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:annualPlan:import', '#', 'admin', '2024-09-12 16:13:11', '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('增加项目业绩', @luru, 1, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:projectPerformance:add', '#', 'admin', '2024-09-12 16:15:45', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('批量导入项目业绩', @luru, 2, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:projectPerformance:import', '#', 'admin', '2024-09-12 16:16:07', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('提交项目业绩审核', @luru, 3, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:projectPerformance:submit', '#', 'admin', '2024-09-12 16:16:28', '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('增加考核配置', @peizhi, 1, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:assessmentConfiguration:add', '#', 'admin', '2024-09-12 16:13:51', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('批量增加考核配置', @peizhi, 2, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:assessmentConfiguration:addBatch', '#', 'admin', '2024-09-12 16:14:22', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('提交考核配置', @peizhi, 3, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:assessmentConfiguration:submit', '#', 'admin', '2024-09-12 16:14:50', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('增加配置模板', @peizhi, 4, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:assessmentConfiguration:addTem', '#', 'admin', '2024-09-12 16:15:10', '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('考核结果确认', @jieguo, 1, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:assessmentResults:sure', '#', 'admin', '2024-09-12 16:17:02', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('重新生成考核结果', @jieguo, 2, '', null, null, 1, 0, 'F', '0', '0', 'perAppraisal:assessmentResults:again', '#', 'admin', '2024-09-12 16:17:47', '', null, '');
