create table kq_day_log_content
(
    id          bigint auto_increment comment '主键'
        primary key,
    main_id  bigint  null comment '主表主键',
    log_content  varchar(200) null comment '日志内容'
)
    comment '用户日报表-内容表' row_format = DYNAMIC;

create table kq_reward_punishment
(
    id                     bigint auto_increment comment '主键'
        primary key,
    user_id                bigint                               null comment '用户id',
    reward_punishment_code varchar(32)                          null comment '奖惩编码',
    type                   varchar(10)                          null comment '奖惩类型 1 奖励 2 惩罚',
    measure                varchar(10)                          null comment '奖惩措施 1 金额 2 物品',
    amount                 decimal(10,2)                        null comment '金额',
    reason                 varchar(500)                         null comment '奖惩是由',
    remark                 varchar(500)                         null comment '备注',
    status                 varchar(4) default '1'               null comment '状态 1 待审批 2 审批中 3 审批通过 4 废弃',
    effective              char       default '0'               null comment '生效状态 0 生效  1 废弃',
    item_name              varchar(200)                         null comment '物品名称',
    item_num               int                                  null comment '物品数量',
    audit_completion_time  datetime                             null comment '审批完成时间',
    is_delete              char       default '0'               null comment '删除标志（0代表存在 1代表删除）',
    process_id             varchar(40)                          null comment '流程id',
    void_reason            varchar(300)                         null comment '废弃原因',
    void_time              datetime                             null comment '废弃时间',
    create_by              varchar(64)                          null comment '创建者',
    create_time            datetime   default CURRENT_TIMESTAMP null comment '创建时间',
    update_by              varchar(64)                          null comment '更新人',
    update_time            datetime                             null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '奖惩表' row_format = DYNAMIC;


create table kq_file
(
    id             bigint auto_increment comment '主键'
        primary key,
    correlation_id bigint       null comment '关联数据id',
    file_type      char         not null comment '关联类型 1奖惩',
    file_name      varchar(255) not null comment '文件名',
    file_url       varchar(455) not null comment '文件路径',
    file_state     char         not null comment '状态 0.逻辑删除 1.未删除',
    create_by      varchar(255) not null comment '创建人',
    create_time    datetime     not null comment '创建时间',
    update_by      varchar(255) null comment '修改人',
    update_time    datetime     null comment '修改时间'
)
    comment '考勤管理-文件' row_format = DYNAMIC;


create table kq_business_trip
(
    id                     bigint auto_increment comment '主键'
        primary key,
    business_trip_code varchar(32)                          null comment '出差编码',
    applicant_user_id   bigint   null comment '申请人',
    business_trip_times  double  null comment '合计天数',
    project_id   bigint null comment '关联项目',
    companions varchar(2000) null comment '同行人',
    business_trip_reason  varchar(500) null comment '出差事由',
    remark                 varchar(500)                         null comment '备注',
    status                 varchar(4) default '1'               null comment '状态 1 待审批 2 审批中 3 审批通过 4 废弃',
    effective              char       default '0'               null comment '生效状态 0 生效  1 废弃',
    audit_completion_time  datetime                             null comment '审批完成时间',
    is_delete              char       default '0'               null comment '删除标志（0代表存在 1代表删除）',
    process_id             varchar(40)                          null comment '流程id',
    void_reason            varchar(300)                         null comment '废弃原因',
    void_time              datetime                             null comment '废弃时间',
    create_by              varchar(64)                          null comment '创建者',
    create_time            datetime   default CURRENT_TIMESTAMP null comment '创建时间',
    update_by              varchar(64)                          null comment '更新人',
    update_time            datetime                             null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '出差申请表' row_format = DYNAMIC;

create table kq_business_trip_slave
(
    id                     bigint auto_increment comment '主键'
        primary key,
    main_id               bigint   null comment '主表主键',
    set_out varchar(200) null comment '出发地点',
    reach varchar(200) null comment '到达地点',
    start_time        date                               null comment '开始时间',
    start_time_period time                               null comment '开始时间段',
    end_time          date                               null comment '结束时间',
    end_time_period   time                               null comment '结束时间段',
    times             double                             null comment '时长',
    vehicle varchar(200) null comment '交通工具'
)
    comment '出差申请-从表' row_format = DYNAMIC;
SELECT @绩效管理 := (select menu_id from sys_menu where menu_name= '绩效管理');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('奖惩申请', @绩效管理, 5, 'bonusPenalty', 'checkWork/bonusPenalty/index', null, 1, 1, 'C', '0', '0', '', 'button', 'admin', '2025-02-24 13:53:52', 'admin', '2025-02-25 15:04:46', '');
SELECT @奖惩申请 := (select menu_id from sys_menu where menu_name= '奖惩申请');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('新建', @奖惩申请, 1, '', null, null, 1, 0, 'F', '0', '0', 'bonusPenalty:add', '#', 'admin', '2025-02-24 14:01:26', 'admin', '2025-02-24 14:01:36', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出', @奖惩申请, 2, '', null, null, 1, 0, 'F', '0', '0', 'bonusPenalty:export', '#', 'admin', '2025-02-24 14:02:16', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('修改', @奖惩申请, 3, '', null, null, 1, 0, 'F', '0', '0', 'bonusPenalty:update', '#', 'admin', '2025-02-24 14:03:17', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('删除', @奖惩申请, 4, '', null, null, 1, 0, 'F', '0', '0', 'bonusPenalty:delete', '#', 'admin', '2025-02-24 14:03:37', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出word', @奖惩申请, 5, '', null, null, 1, 0, 'F', '0', '0', 'bonusPenalty:exportWord', '#', 'admin', '2025-02-24 14:04:01', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('作废奖惩申请', @奖惩申请, 6, '', null, null, 1, 0, 'F', '0', '0', 'bonusPenalty:cancel', '#', 'admin', '2025-02-24 17:01:40', 'admin', '2025-02-24 17:01:47', '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('出差申请', @绩效管理, 2, 'goErrand', 'checkWork/goErrand/index', null, 1, 1, 'C', '0', '0', '', 'guide', 'admin', '2025-02-27 15:33:15', 'admin', '2025-02-27 15:33:42', '');
SELECT @出差申请 := (select menu_id from sys_menu where menu_name= '出差申请');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('新建', @出差申请, 1, '', null, null, 1, 0, 'F', '0', '0', 'goErrand:add', '#', 'admin', '2025-02-27 15:35:46', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出', @出差申请, 2, '', null, null, 1, 0, 'F', '0', '0', 'goErrand:export', '#', 'admin', '2025-02-27 15:36:01', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('修改', @出差申请, 3, '', null, null, 1, 0, 'F', '0', '0', 'goErrand:update', '#', 'admin', '2025-02-27 15:36:22', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('删除', @出差申请, 4, '', null, null, 1, 0, 'F', '0', '0', 'goErrand:delete', '#', 'admin', '2025-02-27 15:36:36', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('作废出差申请', @出差申请, 5, '', null, null, 1, 0, 'F', '0', '0', 'goErrand:cancel', '#', 'admin', '2025-02-27 15:37:25', '', null, '');

INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES ('奖惩类型', 'bonus_penalty_type', '0', 'admin', '2025-02-24 14:39:20', '', null, '绩效管理奖惩类型');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '奖励', '1', 'bonus_penalty_type', null, null, 'default', 'N', '0', 'admin', '2025-02-24 14:39:45', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '惩罚', '2', 'bonus_penalty_type', null, null, 'default', 'N', '0', 'admin', '2025-02-24 14:39:51', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (40, '奖惩流程(多个提交)', 'rewardsPunishmentList', 'oaModule_type', null, null, 'default', 'N', '0', 'admin', '2025-02-26 10:50:51', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (41, '奖惩流程(单个)', 'rewardsPunishment', 'oaModule_type', null, null, 'default', 'N', '0', 'admin', '2025-02-26 10:51:14', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (42, '出差流程', 'businessTrip', 'oaModule_type', null, null, 'default', 'N', '0', 'admin', '2025-03-03 10:50:19', '', null, null);

