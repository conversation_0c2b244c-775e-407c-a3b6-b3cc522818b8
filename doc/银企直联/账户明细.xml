<?xml version="1.0" encoding="GBK"?>
<stream>
    <status></status><!--交易状态 char(7)-->
    <statusText></statusText><!--交易状态信息 varchar(254)-->
    <accountNo></accountNo><!--账号 char(19)-->
    <accountName></accountName><!--账户名称 varchar(122)-->
    <openBankName></openBankName><!--开户行名称 varchar(122)-->
    <totalRecords></totalRecords><!--总记录条数 int-->
    <returnRecords></returnRecords><!--返回记录条数 int-->
    <list name="userDataList">
        <row>
            <tranDate></tranDate><!--交易日期 char(8) 格式YYYYMMDD-->
            <tranTime></tranTime><!--交易时间 char(6) 格式hhmmss-->
            <tranNo></tranNo><!--柜员交易号 char(14)-->
            <sumTranNo></sumTranNo><!--总交易流水号 char(13)-->
            <tranAmount></tranAmount><!--交易金额 decimal(15,2)-->
            <creditDebitFlag></creditDebitFlag><!--借贷标识 借：D，贷：C char(1)-->
            <oppAccountNo></oppAccountNo><!--对方账号 varchar(32)-->
            <oppAccountName></oppAccountName><!--对方账户名称 varchar(122)-->
            <oppOpenBankName></oppOpenBankName><!--对方开户行名 varchar(122)-->
            <abstract></abstract><!--附言 varchar(102)-->
            <cashTransferFlag></cashTransferFlag><!--现转标识 0：现金；1：转帐 char(1)-->
            <opId></opId><!--网银制单员 char(20)-->
            <opName></opName><!--制单员姓名 varchar(20)-->
            <ckId></ckId><!--网银审核员char(20)-->
            <ckName></ckName><!--审核员姓名varchar(20)-->
            <balance></ balance><!--账户余额 decimal(15,2)-->
            <valueDate></valueDate><!--起息日期 char(8)-->
            <hostTranCode></ hostTranCode><!--主机交易码 varchar(7)-->
            <e3rtDate></e3rtDate><!--退汇日期 char(8)，格式YYYYMMDD-->
            <e3rtFlag></e3rtFlag><!--退汇标志 char(1)，0：退汇；1：非退汇-->
            <oriDebitAmt></oriDebitAmt><!--付款原有金额decimal (15,2)，仅当查询账号为信银国际账号时返回-->
            <oriDebitCry></oriDebitCry><!--付款原有币种char(2) ，仅当查询账号为信银国际账号时返回-->
            <oriCreditAmt></oriCreditAmt><!--收款原有金额decimal(15,2) ，仅当查询账号为信银国际账号时返回-->
            <oriCreditCry></oriCreditCry><!--收款原有币种char(2) ，仅当查询账号为信银国际账号时返回-->
            <traCryType></traCryType><!--交易币种char(2) ，仅当查询账号为信银国际账号时返回-->
            <tranRefNo></tranRefNo><!--信银国际交易参考号varchar(35) ，仅当查询账号为信银国际账号时返回-->
            <clientId></clientId><!--客户流水号char(20)，当查询账号为信银国际账号时返回-->
            <chkNum></chkNum><!--对账编号char(20)，当客户上送controlFlag标签并且字段值为1或2时返回-->
            <rlTranNo></rlTranNo><!--关联交易日志号char(14)，当客户上送controlFlag标签并且字段值为1或2时返回-->
            <rfTranDt></rfTranDt><!--冲账对方交易日期char(8)，当客户上送controlFlag标签并且字段值为1或2时返回-->
            <rfTranNo></rfTranNo><!--冲账对方柜员交易号char(14)，当客户上送controlFlag标签并且字段值为1或2时返回-->
            <subAcccNo></subAcccNo><!--附属账户char(19)，当客户上送controlFlag标签并且字段值为1或2时返回-->
            <hostTranDesc></hostTranDesc><!--摘要内容varchar(20)，当客户上送controlFlag标签并且字段值为1或2时返回-->
            <oriNum></oriNum><!--原始流水号varchar(36)，当客户上送controlFlag标签并且字段值为2时返回-->
        </row>
    </list>
</stream>
