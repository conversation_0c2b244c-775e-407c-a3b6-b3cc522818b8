-- auto-generated definition
create table hy_meeting
(
    id                           bigint auto_increment comment '主键'
        primary key,
    meeting_theme                varchar(600)                         null comment '会议主题',
    meeting_code                 varchar(12)                          null comment '会议编号',
    meeting_start_time           datetime                             null comment '会议开始时间',
    meeting_end_time             datetime                             null comment '会议结束时间',
    meeting_duration             varchar(12)                          null comment '会议时长',
    organizational_user          bigint                               null comment '会议组织人',
    organizational_dept          varchar(500)                         null comment '组织部门',
    meeting_room                 bigint                               null comment '会议室',
    attend_way                   varchar(600)                         null comment '参会方式',
    attend_user                  varchar(600)                         null comment '参会人员',
    external_attend_user         varchar(1000)                        null comment '外部参会人员',
    meeting_reminder_way         varchar(50)                          null comment '会议提醒方式',
    is_instantly_reminder        varchar(5)                           null comment '是否立即提醒',
    meeting_reminder_time_hour   int                                  null comment '会议提醒时间(时)',
    meeting_reminder_time_minute int                                  null comment '会议提醒时间(分)',
    meeting_content              varchar(600)                         null comment '会议内容',
    meeting_state                varchar(3) default '1'               null comment '会议状态 (1.审核中 2.未召开 3.审核失败 4.进行中 5.已结束)',
    process_id                   varchar(50)                          null comment '流程 id',
    remark                       varchar(600)                         null comment '备注',
    create_by                    varchar(64)                          null comment '创建者',
    create_time                  datetime   default CURRENT_TIMESTAMP null comment '创建时间',
    update_by                    varchar(64)                          null comment '更新者',
    update_time                  datetime   default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '会议管理' row_format = DYNAMIC;

-- auto-generated definition
create table hy_meeting_file
(
    id             bigint auto_increment comment '主键'
        primary key,
    correlation_id bigint       null comment '关联数据id',
    file_type      char         not null comment '关联类型 1.会议室文件 2.会议文件',
    file_name      varchar(255) not null comment '文件名',
    file_url       varchar(455) not null comment '文件路径',
    file_state     char         not null comment '状态 0.逻辑删除 1.未删除',
    create_by      varchar(255) not null comment '创建人',
    create_time    datetime     not null comment '创建时间',
    update_by      varchar(255) null comment '修改人',
    update_time    datetime     null comment '修改时间'
)
    comment '会议文件表' row_format = DYNAMIC;

-- auto-generated definition
create table hy_meeting_notify
(
    id              bigint auto_increment comment '主键'
        primary key,
    notify_module   varchar(50)                          null comment '通知模块',
    url             varchar(50)                          null comment '相关url',
    notify_type     char                                 null comment '通知类型 0通知 1待办',
    notify_msg      varchar(500)                         null comment '通知内容',
    dispose_user    bigint                               null comment '待处理人id',
    view_flag       char                                 null comment '阅读状态 0未阅 1已阅',
    status          char                                 null comment '状态 0正常 1禁用',
    remind_text     varchar(600)                         null comment '提醒正文',
    correlation_id  bigint                               null comment '关联id',
    hy_notify_type  varchar(5)                           null comment '会议通知类型(1.会议参会人员通知 2.会议室负责人通知 3.会议参会人员取消通知 4.会议室负责人取消通知)',
    view_time       datetime                             null comment '查看通知时间',
    fill_in_time    datetime                             null comment '填写回执时间',
    receipt_state   varchar(5) default '0'               null comment '回执状态 (0.未填写回执 1.参加 2.不参加)',
    receipt_content varchar(500)                         null comment '回执留言',
    is_vx_notify    varchar(2) default '0'               null comment '是否已发送微信提醒(0.发送 1.已发送)',
    create_by       varchar(64)                          null comment '创建者',
    create_time     datetime   default CURRENT_TIMESTAMP null comment '创建时间',
    update_by       varchar(64)                          null comment '更新人',
    update_time     datetime                             null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '会议相关提醒表' row_format = DYNAMIC;


-- auto-generated definition
create table hy_meeting_room
(
    id                                 bigint auto_increment comment '主键'
        primary key,
    meeting_room_name                  varchar(64)                           null comment '会议室名称',
    meeting_room_code                  varchar(64)                           null comment '会议室编号',
    meeting_room_location              varchar(64)                           null comment '会议室地点',
    meeting_room_manager               varchar(255)                          null comment '会议室负责人',
    meeting_room_status                varchar(64) default '0'               null comment '会议室状态(0.正常 1.禁用)',
    belong_company                     bigint                                null comment '所属公司',
    meeting_reminder_conditions        varchar(255)                          null comment '会议提醒条件',
    meeting_start_time_reminder_hour   int                                   null comment '会议开始前提醒时间(小时)',
    meeting_start_time_reminder_minute int                                   null comment '会议开始前提醒时间(分钟)',
    meeting_end_time_reminder_hour     int                                   null comment '会议结束前提醒时间(小时)',
    meeting_end_time_reminder_minute   int                                   null comment '会议结束前提醒时间(分钟)',
    meeting_notification_method        varchar(600)                          null comment '会议通知方式',
    meeting_start_notification         varchar(600)                          null comment '会议开始前通知内容',
    meeting_end_notification           varchar(600)                          null comment '会议结束前通知内容',
    meeting_cancel_notification        varchar(600)                          null comment '会议结束前通知内容',
    description                        varchar(600)                          null comment '会议室说明',
    remark                             varchar(600)                          null comment '备注',
    create_by                          varchar(64)                           null comment '创建者',
    create_time                        datetime    default CURRENT_TIMESTAMP null comment '创建时间',
    update_by                          varchar(64)                           null comment '更新者',
    update_time                        datetime    default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '会议室管理'  row_format = DYNAMIC;


INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('会议管理', 0, 15, 'meeting', null, null, 1, 0, 'M', '0', '0', '', 'system', 'admin', '2024-10-21 11:05:37', 'admin', '2024-10-21 11:07:39', '');
SELECT @meetingGL := LAST_INSERT_ID();

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('会议工作台', @meetingGL, 1, 'staging', 'meeting/staging/index', null, 1, 1, 'C', '0', '0', '', 'date', 'admin', '2024-10-21 11:06:39', 'admin', '2024-11-06 15:18:26', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('会议安排', @meetingGL, 2, 'arrangement', 'meeting/arrangement/index', null, 1, 1, 'C', '0', '0', '', 'date', 'admin', '2024-10-21 11:09:05', 'admin', '2024-11-05 15:02:50', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('会议通知', @meetingGL, 5, 'notice', 'meeting/notice/index', null, 1, 1, 'C', '0', '0', '', 'edit', 'admin', '2024-10-23 09:18:20', 'admin', '2024-11-06 15:18:34', '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('会议查询', @meetingGL, 3, 'search', 'meeting/search/index', null, 1, 1, 'C', '0', '0', '', 'search', 'admin', '2024-10-23 10:44:18', 'admin', '2024-11-06 15:18:29', '');
SELECT @meetingCX := LAST_INSERT_ID();
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('发起会议', @meetingCX, 1, '', null, null, 1, 0, 'F', '0', '0', 'search:add', '#', 'admin', '2024-10-23 10:45:30', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出列表', @meetingCX, 2, '', null, null, 1, 0, 'F', '0', '0', 'search:export', '#', 'admin', '2024-10-23 10:45:50', '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('会议室管理', @meetingGL, 4, 'management', 'meeting/management/index', null, 1, 1, 'C', '0', '0', '', 'edit', 'admin', '2024-10-23 11:22:58', 'admin', '2024-11-06 15:18:14', '');
SELECT @meetingRoomGL := LAST_INSERT_ID();
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('新增', @meetingRoomGL, 1, '', null, null, 1, 0, 'F', '0', '0', 'management:add', '#', 'admin', '2024-10-23 11:34:29', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('删除', @meetingRoomGL, 2, '', null, null, 1, 0, 'F', '0', '0', 'management:delete', '#', 'admin', '2024-10-23 11:34:49', '', null, '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导出列表', @meetingRoomGL, 3, '', null, null, 1, 0, 'F', '0', '0', 'management:export', '#', 'admin', '2024-10-23 11:35:07', '', null, '');


INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '会议流程', 'hylc', 'oaModule_type', null, 'default', 'N', '0', 'admin', '2024-11-05 13:43:20', '', null, null);
