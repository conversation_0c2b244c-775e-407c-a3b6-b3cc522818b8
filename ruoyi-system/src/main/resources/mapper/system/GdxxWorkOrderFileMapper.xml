<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.GdxxWorkOrderFileMapper">

    <resultMap type="GdxxWorkOrderFile" id="GdxxWorkOrderFileResult">
        <result property="id" column="id"/>
        <result property="sourceCategory" column="source_category"/>
        <result property="sourceTableId" column="source_table_id"/>
        <result property="fileAddress" column="file_address"/>
        <result property="status" column="status"/>
        <result property="fileName" column="file_name"/>
        <result property="creator" column="creator"/>
        <result property="creationTime" column="creation_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modificationTime" column="modification_time"/>
    </resultMap>

    <sql id="selectGdxxWorkOrderFileVo">
        select id,
               source_category,
               source_table_id,
               file_address,
               status,
               file_name,
               creator,
               creation_time,
               modifier,
               modification_time
        from gdxx_work_order_file
    </sql>

    <select id="selectGdxxWorkOrderFileList" parameterType="GdxxWorkOrderFile" resultMap="GdxxWorkOrderFileResult">
        <include refid="selectGdxxWorkOrderFileVo"/>
        <where>
            <if test="sourceCategory != null and sourceCategory != ''">and source_category = #{sourceCategory}</if>
            <if test="sourceTableId != null and sourceTableId != ''">and source_table_id = #{sourceTableId}</if>
            <if test="fileAddress != null and fileAddress != ''">and file_address = #{fileAddress}</if>
            <if test="status != null and status != ''">and status = #{status}</if>
            <if test="fileName != null and fileName != ''">and file_name like concat('%', #{fileName}, '%')</if>
        </where>
    </select>

    <select id="selectGdxxWorkOrderFileById" parameterType="Long" resultMap="GdxxWorkOrderFileResult">
        <include refid="selectGdxxWorkOrderFileVo"/>
        where id = #{id}
    </select>
    <select id="selectGdxxWorkOrderFileByIds" resultType="com.ruoyi.system.domain.GdxxWorkOrderFile">
        <include refid="selectGdxxWorkOrderFileVo"/>
        <where>
           id in       <foreach item="id" collection="fileIds" open="(" separator="," close=")">

            #{id}
        </foreach>
        </where>
    </select>

    <insert id="insertGdxxWorkOrderFile" parameterType="GdxxWorkOrderFile" useGeneratedKeys="true" keyProperty="id">
        insert into gdxx_work_order_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sourceCategory != null">source_category,</if>
            <if test="sourceTableId != null">source_table_id,</if>
            <if test="fileAddress != null">file_address,</if>
            <if test="status != null">status,</if>
            <if test="fileName != null">file_name,</if>
            <if test="creator != null">creator,</if>
            <if test="creationTime != null">creation_time,</if>
            <if test="modifier != null">modifier,</if>
            <if test="modificationTime != null">modification_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sourceCategory != null">#{sourceCategory},</if>
            <if test="sourceTableId != null">#{sourceTableId},</if>
            <if test="fileAddress != null">#{fileAddress},</if>
            <if test="status != null">#{status},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="creator != null">#{creator},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="modifier != null">#{modifier},</if>
            <if test="modificationTime != null">#{modificationTime},</if>
        </trim>
    </insert>

    <update id="updateGdxxWorkOrderFile" parameterType="GdxxWorkOrderFile">
        update gdxx_work_order_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="sourceCategory != null">source_category = #{sourceCategory},</if>
            <if test="sourceTableId != null">source_table_id = #{sourceTableId},</if>
            <if test="fileAddress != null">file_address = #{fileAddress},</if>
            <if test="status != null">status = #{status},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="creationTime != null">creation_time = #{creationTime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modificationTime != null">modification_time = #{modificationTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGdxxWorkOrderFileById" parameterType="Long">
        delete
        from gdxx_work_order_file
        where id = #{id}
    </delete>

    <delete id="deleteGdxxWorkOrderFileByIds" parameterType="String">
        delete from gdxx_work_order_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByFirStTypeAndFirstId">
        delete
        from gdxx_work_order_file
        where source_table_id = #{id}
          and source_category = #{orderTableZhubiao}
    </delete>
    <update id="updateNotInFileIdsAndFirstType">
        update gdxx_work_order_file set status = '1'
        <where>
            source_category = #{orderTableZhubiao} and source_table_id = #{workOrderMainId}
            and id not in
            <foreach item="id" collection="fileIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>
</mapper> 