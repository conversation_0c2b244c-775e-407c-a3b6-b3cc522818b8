<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DEarlyWarningMapper">
    
    <resultMap type="DEarlyWarning" id="DEarlyWarningResult">
        <result property="id"    column="id"    />
        <result property="querySql"    column="query_sql"    />
        <result property="criticalConditions"    column="critical_conditions"    />
        <result property="criticalValue"    column="critical_value"    />
        <result property="criticalInform"    column="critical_inform"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="address"    column="address"    />
    </resultMap>
<!--    /** 判断值 */
    @Excel(name = "判断值")
    private String criticalValue;
    /** 判断是否通知 */
    @Excel(name = "判断是否通知")
    private String criticalInform;-->
    <sql id="selectDEarlyWarningVo">
        select id, query_sql,address, critical_conditions,critical_inform, critical_value, remark, status, create_by, create_time, update_by, update_time from d_early_warning
    </sql>

    <select id="selectDEarlyWarningList" parameterType="DEarlyWarning" resultMap="DEarlyWarningResult">
        <include refid="selectDEarlyWarningVo"/>
        <where>  
            <if test="querySql != null  and querySql != ''"> and query_sql = #{querySql}</if>
            <if test="criticalConditions != null  and criticalConditions != ''"> and critical_conditions = #{criticalConditions}</if>
            <if test="criticalValue != null  and criticalValue != ''"> and critical_value = #{criticalValue}</if>
            <if test="criticalInform != null  and criticalInform != ''"> and critical_inform = #{criticalInform}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
          <!--  <if test="createTime != null "> and create_time = #{createTime}</if>-->
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
         <!--   <if test="updateTime != null "> and update_time = #{updateTime}</if>-->
            <if test="beginTime != null"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null "><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
    </select>
    
    <select id="selectDEarlyWarningById" parameterType="Long" resultMap="DEarlyWarningResult">
        <include refid="selectDEarlyWarningVo"/>
        where id = #{id}
    </select>
    <select id="querySql" resultType="java.lang.String">
        ${sql}
    </select>
    <select id="querySqlValue" resultType="java.lang.Object">
        ${sql}
    </select>

    <insert id="insertDEarlyWarning" parameterType="DEarlyWarning">
        insert into d_early_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="querySql != null">query_sql,</if>
            <if test="criticalConditions != null">critical_conditions,</if>
            <if test="criticalValue != null">critical_value,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="criticalInform != null">critical_inform,</if>
            <if test="address != null">address,</if>

         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="querySql != null">#{querySql},</if>
            <if test="criticalConditions != null">#{criticalConditions},</if>
            <if test="criticalValue != null">#{criticalValue},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="criticalInform != null">#{criticalInform},</if>
            <if test="address != null">#{address},</if>

         </trim>
    </insert>

    <update id="updateDEarlyWarning" parameterType="DEarlyWarning">
        update d_early_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="querySql != null">query_sql = #{querySql},</if>
            <if test="criticalConditions != null">critical_conditions = #{criticalConditions},</if>
            <if test="criticalValue != null">critical_value = #{criticalValue},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="criticalInform != null">critical_inform = #{criticalInform},</if>
            <if test="address != null">address = #{address},</if>

        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDEarlyWarningById" parameterType="Long">
        delete from d_early_warning where id = #{id}
    </delete>

    <delete id="deleteDEarlyWarningByIds" parameterType="String">
        delete from d_early_warning where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>