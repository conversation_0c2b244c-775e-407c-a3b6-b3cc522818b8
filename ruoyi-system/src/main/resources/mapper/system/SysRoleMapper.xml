<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysRoleMapper">

	<resultMap type="SysRole" id="SysRoleResult">
		<id     property="roleId"             column="role_id"               />
		<result property="roleName"           column="role_name"             />
		<result property="roleKey"            column="role_key"              />
		<result property="roleSort"           column="role_sort"             />
		<result property="dataScope"          column="data_scope"            />
		<result property="menuCheckStrictly"  column="menu_check_strictly"   />
		<result property="deptCheckStrictly"  column="dept_check_strictly"   />
		<result property="oaCheckStrictly"  column="oa_check_strictly"   />
		<result property="status"             column="status"                />
		<result property="delFlag"            column="del_flag"              />
		<result property="createBy"           column="create_by"             />
		<result property="createTime"         column="create_time"           />
		<result property="updateBy"           column="update_by"             />
		<result property="updateTime"         column="update_time"           />
		<result property="remark"             column="remark"                />
		<result property="roleType"           column="role_type"             />
		<collection  property="userList"   javaType="java.util.List" select="selectRoleUserListByRoleId" column="role_id"/>
		<collection  property="menuList"   javaType="java.util.List" select="selectRoleMenuListByRoleId" column="role_id"/>
		<collection  property="roleOaList"   javaType="java.util.List" select="selectRoleOaListByRoleId" column="role_id"/>
		<collection  property="tableData"   javaType="java.util.List" select="selectRoleDataListByRoleId" column="role_id"/>
	</resultMap>

	<resultMap id="sysUserResult" type="SysUser">
		<id property="userId" column="user_id"/>
		<result property="userName" column="user_name"/>
		<result property="nickName"     column="nick_name"    />
		<result property="status"   column="status" />
	</resultMap>

	<resultMap id="sysMenuResult" type="SysMenu">
		<id property="menuId" column="menu_id"/>
		<result property="menuName" column="menu_name"/>
		<result property="parentId"     column="parent_id"    />
		<result property="status"   column="status" />
	</resultMap>

	<resultMap id="sysRoleOaResult" type="SysRoleOa">
		<id property="roleId" column="role_id"/>
		<result property="oaId" column="oa_id"/>
		<result property="oaType"     column="oa_type"    />
	</resultMap>


	<resultMap id="sysRoleDataResult" type="SysRoleData">
        <result property="customId"    column="custom_id"    />
        <result property="roleId"    column="role_id"    />
        <result property="platformNo"    column="platform_no"    />
        <result property="custNo"    column="cust_no"    />
        <result property="partnerNo"    column="partner_no"    />
        <result property="fundNo"    column="fund_no"    />
	</resultMap>


	<sql id="selectRoleVo">
	    select distinct r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.menu_check_strictly, r.dept_check_strictly,r.oa_check_strictly,
            r.status, r.del_flag, r.create_time, r.remark,r.role_type
        from sys_role r
	        left join sys_user_role ur on ur.role_id = r.role_id
	        left join sys_user u on u.user_id = ur.user_id
	        left join sys_dept d on u.dept_id = d.dept_id
    </sql>
	<sql id="selectRoleUserVo">
		select  u.user_id as value,u.nick_name as label

		from sys_role r
				 left join sys_user_role ur on ur.role_id = r.role_id
				 left join sys_user u on u.user_id = ur.user_id
				 left join sys_dept d on u.dept_id = d.dept_id
	</sql>

    <select id="selectRoleList" parameterType="SysRole" resultMap="SysRoleResult">
		<include refid="selectRoleVo"/>
		where r.del_flag = '0'
		<if test="roleId != null and roleId != 0">
			AND r.role_id = #{roleId}
		</if>
		<if test="roleName != null and roleName != ''">
			AND r.role_name like concat('%', #{roleName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND r.status = #{status}
		</if>
		<if test="roleKey != null and roleKey != ''">
			AND r.role_key like concat('%', #{roleKey}, '%')
		</if>
		<if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
			and date_format(r.create_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
		</if>
		<if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
			and date_format(r.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
		</if>
		<if test="roleType != null and roleType != ''">
			AND r.role_type = #{roleType}
		</if>
		<if test="userName != null and userName != ''">
			AND (u.user_name like concat('%', #{userName}, '%') or u.nick_name like concat('%', #{userName}, '%'))
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
		<if test="orderField != null and orderField != ''">
		order by r.${orderField}
		</if>
		<if test="orderField == null or orderField == ''">
		order by r.role_name
		</if>
	</select>

	<select id="selectRoleUserListByRoleId" resultMap="sysUserResult">
	     select u.user_id,u.user_name,u.nick_name,u.status
		from  sys_user_role ur
		left join sys_user u on u.user_id=ur.user_id
		where ur.role_id=#{id} order by u.nick_name
	</select>

	<select id="selectRoleMenuListByRoleId" resultMap="sysMenuResult">
	     select m.menu_id,m.menu_name,m.status,m.parent_id
		from  sys_role_menu rm
		left join sys_menu m on m.menu_id=rm.menu_id
		where rm.role_id=#{id}
	</select>
	<select id="selectRoleOaListByRoleId" resultMap="sysRoleOaResult">
	     select ro.role_id,ro.oa_id,ro.oa_type
		from  sys_role_oa ro
		where ro.role_id=#{id}
	</select>
	<select id="selectRoleDataListByRoleId" resultMap="sysRoleDataResult">
	    select custom_id, role_id, platform_no, cust_no, partner_no, fund_no from sys_role_custom where role_id = #{id}
	</select>

	<select id="selectRolePermissionByroleName" parameterType="string" resultType="AddlbVo">
		<include refid="selectRoleUserVo"/>
		WHERE r.del_flag = '0'
		<if test="roleName != null and roleName != ''">
			AND r.role_name = #{roleName}
		</if>
	</select>

	<select id="selectRolePermissionByUserId" parameterType="Long" resultMap="SysRoleResult">
		<include refid="selectRoleVo"/>
		WHERE r.del_flag = '0' and r.status = '0' and ur.user_id = #{userId}
	</select>

	<select id="selectRoleAll" resultMap="SysRoleResult">
		<include refid="selectRoleVo"/>
	</select>

	<select id="selectRoleListByUserId" parameterType="Long" resultType="Long">
		select r.role_id
        from sys_role r
	        left join sys_user_role ur on ur.role_id = r.role_id
	        left join sys_user u on u.user_id = ur.user_id
	    where u.user_id = #{userId}
	</select>

	<select id="selectRoleById" parameterType="Long" resultMap="SysRoleResult">
		<include refid="selectRoleVo"/>
		where r.role_id = #{roleId}
	</select>

	<select id="selectRolesByUserName" parameterType="String" resultMap="SysRoleResult">
		<include refid="selectRoleVo"/>
		WHERE r.del_flag = '0' and u.user_name = #{userName}
	</select>

	<select id="checkRoleNameUnique" parameterType="String" resultMap="SysRoleResult">
		<include refid="selectRoleVo"/>
		 where r.role_name=#{roleName} limit 1
	</select>

	<select id="checkRoleKeyUnique" parameterType="String" resultMap="SysRoleResult">
		<include refid="selectRoleVo"/>
		 where r.role_key=#{roleKey} limit 1
	</select>
	<select id="getUserIdListByRoleKey" resultType="java.lang.Long">
		SELECT sur.user_id FROM sys_role sr LEFT JOIN sys_user_role sur ON sr.role_id = sur.role_id
		where
			sr.role_id=#{roleId}

</select>


	<insert id="insertRole" parameterType="SysRole" useGeneratedKeys="true" keyProperty="roleId">
 		insert into sys_role(
 			<if test="roleId != null and roleId != 0">role_id,</if>
 			<if test="roleName != null and roleName != ''">role_name,</if>
 			<if test="roleKey != null and roleKey != ''">role_key,</if>
 			<if test="roleSort != null and roleSort != ''">role_sort,</if>
 			<if test="dataScope != null and dataScope != ''">data_scope,</if>
 			<if test="menuCheckStrictly != null">menu_check_strictly,</if>
 			<if test="deptCheckStrictly != null">dept_check_strictly,</if>
 			<if test="oaCheckStrictly != null">oa_check_strictly,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="roleType != null and roleType != ''">role_type,</if>
 			create_time
 		)values(
 			<if test="roleId != null and roleId != 0">#{roleId},</if>
 			<if test="roleName != null and roleName != ''">#{roleName},</if>
 			<if test="roleKey != null and roleKey != ''">#{roleKey},</if>
 			<if test="roleSort != null and roleSort != ''">#{roleSort},</if>
 			<if test="dataScope != null and dataScope != ''">#{dataScope},</if>
 			<if test="menuCheckStrictly != null">#{menuCheckStrictly},</if>
 			<if test="deptCheckStrictly != null">#{deptCheckStrictly},</if>
 			<if test="oaCheckStrictly != null">#{oaCheckStrictly},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="roleType != null and roleType != ''">#{roleType},</if>
 			sysdate()
 		)
	</insert>

	<update id="updateRole" parameterType="SysRole">
 		update sys_role
 		<set>
 			<if test="roleName != null and roleName != ''">role_name = #{roleName},</if>
 			<if test="roleKey != null and roleKey != ''">role_key = #{roleKey},</if>
 			<if test="roleSort != null and roleSort != ''">role_sort = #{roleSort},</if>
 			<if test="dataScope != null and dataScope != ''">data_scope = #{dataScope},</if>
 			<if test="menuCheckStrictly != null">menu_check_strictly = #{menuCheckStrictly},</if>
 			<if test="deptCheckStrictly != null">dept_check_strictly = #{deptCheckStrictly},</if>
 			<if test="oaCheckStrictly != null">oa_check_strictly = #{oaCheckStrictly},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where role_id = #{roleId}
	</update>

	<delete id="deleteRoleById" parameterType="Long">
		update sys_role set del_flag = '2' where role_id = #{roleId}
 	</delete>

 	<delete id="deleteRoleByIds" parameterType="Long">
 	    update sys_role set del_flag = '2' where role_id in
 		<foreach collection="array" item="roleId" open="(" separator="," close=")">
 			#{roleId}
        </foreach>
 	</delete>











 	<resultMap id="oaFlowTreeVoResult" type="OaFlowTreeVo">
		<id property="id" column="id"/>
		<result property="label" column="label"/>
		<result property="parentId"     column="parent_id"    />
		<result property="oaType"   column="oaType" />
	</resultMap>




 	<select id="selectOaOpcAndFlowTempList"  resultMap="oaFlowTreeVoResult">
		 (SELECT CONCAT('opc',opc.id) AS id,CONCAT('opc',opc.parent_id) AS parent_id,opc.name AS label ,'opc' AS oaType
		FROM oa_process_classification opc
		where  opc.del_flag='0'
		ORDER BY opc.order_num)
		UNION ALL
		(SELECT CONCAT('opt',opt.id) AS id,CONCAT('opc',opt.classification_id) AS parent_id,opt.template_name AS label ,'opt' AS oaType
		FROM oa_process_template opt
		ORDER BY opt.order_num)
	</select>



	<select id="selectOaOpcAndFlowTempListByUserId"  parameterType="Long" resultMap="oaFlowTreeVoResult">
		 (SELECT distinct CONCAT('opc',opc.id) AS id,CONCAT('opc',opc.parent_id) AS parent_id,opc.name AS label ,'opc' AS oaType
		FROM oa_process_classification opc

		left join sys_role_oa ro on (opc.id = ro.oa_id and ro.oa_type='opc')
		left join sys_user_role ur on ro.role_id = ur.role_id
		left join sys_role r on ur.role_id = r.role_id

		where ur.user_id = #{userId} and opc.del_flag='0' and r.del_flag='0' and opc.status = '0'  AND  r.status = '0'
		ORDER BY opc.order_num)
		UNION ALL
		(SELECT distinct CONCAT('opt',opt.id) AS id,CONCAT('opc',opt.classification_id) AS parent_id,opt.template_name AS label ,'opt' AS oaType
		FROM oa_process_template opt

		left join sys_role_oa ro on (opt.id = ro.oa_id and ro.oa_type='opt')
		left join sys_user_role ur on ro.role_id = ur.role_id
		left join sys_role r on ur.role_id = r.role_id

		where ur.user_id = #{userId} and r.del_flag='0' and opt.is_enable = '0'  AND r.status = '0'
		ORDER BY opt.order_num)
	</select>


	<select id="selectOaOpcAndFlowTempListByRoleId" resultType="String">
		 (SELECT distinct CONCAT('opc',opc.id) AS id
		FROM oa_process_classification opc
		left join sys_role_oa ro on (opc.id = ro.oa_id and ro.oa_type='opc')
		where ro.role_id = #{roleId}
			<if test="oaCheckStrictly">
              and opc.id not in (select opc.parent_id from oa_process_classification opc inner join sys_role_oa ro on opc.id = ro.oa_id and ro.oa_type='opc' and ro.role_id = #{roleId})
            </if>
		ORDER BY opc.order_num)
		UNION ALL
		(SELECT distinct CONCAT('opt',opt.id) AS id
		FROM oa_process_template opt
		left join sys_role_oa ro on (opt.id = ro.oa_id and ro.oa_type='opt')
		where  ro.role_id = #{roleId}
		ORDER BY opt.order_num)
	</select>

 	<insert id="batchRoleOa">
		insert into sys_role_oa(role_id, oa_id,oa_type) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.roleId},#{item.oaId},#{item.oaType})
		</foreach>
	</insert>

	<delete id="deleteRoleOaByRoleId" parameterType="Long">
		delete from sys_role_oa where role_id=#{roleId}
	</delete>

	<select id="selectRoleOaTemplateListByRoleIds" resultMap="sysRoleOaResult">
		select ro.role_id,ro.oa_id,ro.oa_type
		from  sys_role_oa ro
		where ro.oa_type = 'opt' and ro.role_id IN
		<foreach item="roleId" collection="array" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</select>

	<update id="updateRoleOa">
		update sys_role_oa set oa_id = #{id,jdbcType=BIGINT} where oa_id=#{oldId,jdbcType=BIGINT} and oa_type ='opt'
	</update>
</mapper>
