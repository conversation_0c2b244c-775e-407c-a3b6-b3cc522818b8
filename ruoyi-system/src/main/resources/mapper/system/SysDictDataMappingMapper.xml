<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysDictDataMappingMapper">
    
    <resultMap type="SysDictDataMapping" id="SysDictDataMappingResult">
        <result property="dictddmId"    column="dictddm_id"    />
        <result property="dictdataId"    column="dictdata_id"    />
        <result property="platformNo"    column="platform_no"    />
        <result property="dictType"    column="dict_type"    />
        <result property="dictValue"    column="dict_value"    />
        <result property="dictValueMapping"    column="dict_value_mapping"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBr"    column="create_br"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBr"    column="update_br"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysDictDataMappingVo">
        select dictddm_id, dictdata_id, platform_no, dict_type, dict_value, dict_value_mapping, remark, status, create_br, create_time, update_br, update_time from sys_dict_data_mapping
    </sql>

    <select id="selectSysDictDataMappingList" parameterType="SysDictDataMapping" resultMap="SysDictDataMappingResult">
        <include refid="selectSysDictDataMappingVo"/>
        <where>
            <if test="dictdataId != null "> and dictdata_id = #{dictdataId}</if>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="dictType != null  and dictType != ''"> and dict_type = #{dictType}</if>
            <if test="dictValue != null  and dictValue != ''"> and dict_value = #{dictValue}</if>
            <if test="dictValueMapping != null  and dictValueMapping != ''"> and dict_value_mapping = #{dictValueMapping}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </select>
    
    <select id="selectSysDictDataMappingByDictddmId" parameterType="Long" resultMap="SysDictDataMappingResult">
        <include refid="selectSysDictDataMappingVo"/>
        where dictddm_id = #{dictddmId}
    </select>
        
    <insert id="insertSysDictDataMapping" parameterType="SysDictDataMapping" useGeneratedKeys="true" keyProperty="dictddmId">
        insert into sys_dict_data_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictdataId != null">dictdata_id,</if>
            <if test="platformNo != null and platformNo != ''">platform_no,</if>
            <if test="dictType != null">dict_type,</if>
            <if test="dictValue != null and dictValue != ''">dict_value,</if>
            <if test="dictValueMapping != null and dictValueMapping != ''">dict_value_mapping,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBr != null">create_br,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBr != null">update_br,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictdataId != null">#{dictdataId},</if>
            <if test="platformNo != null and platformNo != ''">#{platformNo},</if>
            <if test="dictType != null">#{dictType},</if>
            <if test="dictValue != null and dictValue != ''">#{dictValue},</if>
            <if test="dictValueMapping != null and dictValueMapping != ''">#{dictValueMapping},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBr != null">#{createBr},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBr != null">#{updateBr},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysDictDataMapping" parameterType="SysDictDataMapping">
        update sys_dict_data_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="dictdataId != null">dictdata_id = #{dictdataId},</if>
            <if test="platformNo != null and platformNo != ''">platform_no = #{platformNo},</if>
            <if test="dictType != null">dict_type = #{dictType},</if>
            <if test="dictValue != null and dictValue != ''">dict_value = #{dictValue},</if>
            <if test="dictValueMapping != null and dictValueMapping != ''">dict_value_mapping = #{dictValueMapping},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBr != null">create_br = #{createBr},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBr != null">update_br = #{updateBr},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where dictddm_id = #{dictddmId}
    </update>

    <delete id="deleteSysDictDataMappingByDictddmId" parameterType="Long">
        delete from sys_dict_data_mapping where dictddm_id = #{dictddmId}
    </delete>

    <delete id="deleteSysDictDataMappingByDictddmIds" parameterType="String">
        delete from sys_dict_data_mapping where dictddm_id in 
        <foreach item="dictddmId" collection="array" open="(" separator="," close=")">
            #{dictddmId}
        </foreach>
    </delete>



    <select id="selectMappingByAllCondition" parameterType="SysDictDataMapping" resultMap="SysDictDataMappingResult">
        <include refid="selectSysDictDataMappingVo"/>
        <where>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="dictType != null  and dictType != ''"> and dict_type = #{dictType}</if>
            <if test="dictValue != null  and dictValue != ''"> and dict_value = #{dictValue}</if>
            <if test="dictValueMapping != null  and dictValueMapping != ''"> and dict_value_mapping = #{dictValueMapping}</if>
        </where>
    </select>

    <select id="getSelectData" parameterType="string" resultType="map">
        select dm.dict_value_mapping AS value,dd.dict_label AS name from sys_dict_data_mapping dm LEFT JOIN sys_dict_data dd on dm.dict_value = dd.dict_value
        where  dm.dict_type = #{dictType} and dm.status = 0
    </select>

    <select id="selectByDictValue" resultMap="SysDictDataMappingResult">
        <include refid="selectSysDictDataMappingVo"></include>
        where status = 0 and dictdata_id = #{dictCode}
    </select>

    <select id="getReversDictData" parameterType="SysDictDataMapping" resultMap="SysDictDataMappingResult">
        <include refid="selectSysDictDataMappingVo"></include>
        where dict_type = #{dictType} AND dict_value = #{dictValue}
    </select>
</mapper>