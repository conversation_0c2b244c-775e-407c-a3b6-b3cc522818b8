package com.ruoyi.system.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.jcraft.jsch.*;
import com.ruoyi.common.constant.NewsOptionConstants;
import com.ruoyi.common.core.domain.entity.NewsConfigCenter;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.handle.*;
import com.ruoyi.system.handle.fatory.NewsFileHandleFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.Vector;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: 左东冉
 * @Create: 2024-03-11 17:32
 * @Description: 新闻文件处理工具类
 **/
@Slf4j
@Component
public class NewsFileHandleUtils {
    @Autowired
    private QhctNewsHandleImpl qhctNewsHandle;
    @Autowired
    private YndfNewsHandleImpl yndfNewsHandle;
    @Autowired
    private HnztNewsHandleImpl hnztNewsHandle;
    @Autowired
    private HnzhNewsHandleImpl hnzhNewsHandle;
    @Autowired
    private HbfcNewsHandleImpl hbfcNewsHandle;
    @Autowired
    private JhrsNewsHandleImpl jhrsNewsHandle;
    @Autowired
    private FjdyNewsHandleImpl fjdyNewsHandle;

    public NewsFileHandleFactory getHandle(String companyCode) {
        switch (companyCode) {
            case "QHCT":
                return qhctNewsHandle;
            case "YNDF":
                return yndfNewsHandle;
            case "HNZT":
                return hnztNewsHandle;
            case "HNZH":
                return hnzhNewsHandle;
            case "HBFC":
                return hbfcNewsHandle;
            case "FJDY":
                return fjdyNewsHandle;
            case "JHRS":
                return jhrsNewsHandle;
            default:
                // 处理默认情况，可以抛出异常或返回默认值
                return null;
        }
    }


    /**
     * 超过 maxSize 的部分用省略号代替
     *
     * @param originStr    原始字符串
     * @param maxSize      最大长度
     * @param abbrevMarker 省略符
     */
    public static String abbreviate(String originStr, int maxSize, String abbrevMarker) {
        Preconditions.checkArgument(maxSize > 0, "size 必须大于0");
        if (StringUtils.isEmpty(originStr)) {
            return StringUtils.EMPTY;
        }
        String defaultAbbrevMarker = "...";
        if (originStr.length() < maxSize) {
            return originStr;
        }
        // 截取前maxSize 个字符
        String head = originStr.substring(0, maxSize);
        // 最后一个字符是高代理项，则移除掉
        char lastChar = head.charAt(head.length() - 1);
        if (Character.isHighSurrogate(lastChar)) {
            head = head.substring(0, head.length() - 1);
        }


        return head + StringUtils.defaultIfEmpty(abbrevMarker, defaultAbbrevMarker);
    }

    /**
     * 通过字符串生成html文件
     *
     * @param htmlContent 文本内容
     * @param filePath    文件路径  例如 /data/app/info.html
     */
    public static void generateHTML(String htmlContent, String filePath) {
        try {
            // 创建一个文件写入器
            FileWriter fileWriter = new FileWriter(filePath);
            // 创建一个打印写入器，将内容写入文件
            PrintWriter printWriter = new PrintWriter(fileWriter);
            printWriter.println(htmlContent);
            // 关闭写入器
            printWriter.close();
        } catch (IOException e) {
            throw new RuntimeException("生成文件失败，" + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param filePath        文件路径
     * @param filePrefixArray 文件前缀数组（为空全部删除）
     */
    public static void handleDeleteInfoFile(String filePath, String... filePrefixArray) {
        File directory = new File(filePath);
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (null != filePrefixArray) {
                    for (String filePrefix : filePrefixArray) {
                        // 检查文件是否以指定前缀开头
                        if (file.getName().startsWith(filePrefix)) {
                            // 删除文件
                            file.delete();
                        }
                    }
                } else {
                    file.delete();
                }
            }
        }
    }

    /**
     * 从源文件夹复制到目标文件夹
     *
     * @param sourceFolder 源文件夹
     * @param targetFolder 目标文件夹
     */
    public static void migrateFiles(File sourceFolder, File targetFolder) {
        // 检查源文件夹和目标文件夹是否存在
        if (sourceFolder.exists() && targetFolder.exists()) {
            // 获取源文件夹中的所有文件和子文件夹
            File[] files = sourceFolder.listFiles();

            if (files != null) {
                for (File file : files) {
                    // 创建目标文件夹的路径，如果不存在则创建
                    File targetFile = new File(targetFolder, file.getName());

                    if (file.isFile()) {
                        // 如果是文件，则将文件复制到目标文件夹
                        copyFile(file, targetFile);
                    } else if (file.isDirectory()) {
                        // 如果是子文件夹，则递归调用迁移函数
                        migrateFiles(file, targetFile);
                    }
                }
            }
        } else {
            log.info("Source or target folder does not exist.");
        }
    }

    private static void copyFile(File sourceFile, File targetFile) {
        try {
            // 复制文件
            java.nio.file.Files.copy(sourceFile.toPath(), targetFile.toPath());
        } catch (Exception e) {
            log.error("Error copying file: " + e.getMessage());
        }
    }


    /**
     * 转换替换数据
     *
     * @param newsConfigCenter 新闻参数
     * @return 返回数据
     */
    public static String coverData(String indexText, NewsConfigCenter newsConfigCenter, String fileName) {
        String data = indexText;
        data = data.replace(NewsOptionConstants.INDEX_INFO_REPLACE_SYMBOL[0], fileName);
        data = data.replace(NewsOptionConstants.INDEX_INFO_REPLACE_SYMBOL[1], newsConfigCenter.getMajorTitle());
        if (StrUtil.isEmptyIfStr(newsConfigCenter.getTextContent())) {
            data = data.replace(NewsOptionConstants.INDEX_INFO_REPLACE_SYMBOL[2], "");
        } else {
            data = data.replace(NewsOptionConstants.INDEX_INFO_REPLACE_SYMBOL[2], NewsFileHandleUtils.abbreviate(removeTags(newsConfigCenter.getTextContent()), 40, "..."));
        }
        return data;
    }

    /**
     * 获取倒数的第一个和倒数第二个/ 之间的字符串
     *
     * @param str
     */
    public static String extractString(String str) {

        int lastSlashIndex = str.lastIndexOf('/');
        int secondLastSlashIndex = str.lastIndexOf('/', lastSlashIndex - 1);

        if (lastSlashIndex != -1 && secondLastSlashIndex != -1) {
            return str.substring(secondLastSlashIndex + 1, lastSlashIndex);
        } else {
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(extractString("/datafile/htdocs/jhrs/_news_image"));
    }
    /**
     * 去除html 中所有标签
     *
     * @param htmlString 带标签的字符串
     * @return 去除标签的字符串
     */
    public static String removeTags(String htmlString) {
        // 定义正则表达式模式，匹配 HTML 标签
        String pattern = "<.*?>";
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(htmlString);

        // 替换匹配到的标签为空格
        return matcher.replaceAll("");
    }


    /**
     * @param file     文件
     * @param fileName 新的随机文件名
     */
    public static void upload(MultipartFile file, String destPath, String fileName) {
        File dest = new File(destPath + File.separator + fileName);
        //判断文件父目录是否存在
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }
        try {
            //保存文件
            file.transferTo(dest);
        } catch (Exception e) {
            log.error("Save file exception. {}", e.getMessage());
        }

    }

//    public static void main(String[] args) {
//        transferFiles("***************", 22, "root", "zuodongran0609.", "C:\\Users\\<USER>\\Desktop\\hbfc", "/opt/lampp/htdocs/hbfc");
//    }

    /**
     * 通过SFTP 传输文件
     *
     * @param host             IP
     * @param port             端口
     * @param username         账号
     * @param password         密码
     * @param localFolderPath  本地路径
     * @param remoteFolderPath 远程路径
     */
    public static void transferFiles(String host, int port, String username, String password, String localFolderPath, String remoteFolderPath) {
        Session session = null;
        ChannelSftp sftpChannel = null;
        try {
            // 创建 JSch 对象
            JSch jsch = new JSch();
            // 建立与 SFTP 服务器的连接
            session = jsch.getSession(username, host, port);
            session.setPassword(password);
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect();
            // 创建 SFTP 通道
            sftpChannel = (ChannelSftp) session.openChannel("sftp");

            sftpChannel.connect();
            log.info("连接SFTP通道成功");

            // 列出本地文件夹中的文件和子文件夹
            Vector<File> files = listFiles(localFolderPath);
            if (dirExists(remoteFolderPath,sftpChannel)){
                // 清除路径的所有文件
                deleteDirectory(remoteFolderPath, sftpChannel ,false);
            }
            log.info("清除路径中文件成功,路径为:{}", remoteFolderPath);
            // 遍历文件并传输到远程服务器
            for (File file : files) {
                String replaceFileOrDir = file.getPath().replace(localFolderPath, remoteFolderPath);
                boolean dirExists = dirExists(replaceFileOrDir.replace("/" + file.getName(), ""), sftpChannel);
                if (!dirExists) {
                    mkdirDir(replaceFileOrDir.replace("/" + file.getName(), "").split("/"), sftpChannel);
                }
                sftpChannel.put(file.getPath(), replaceFileOrDir);
                log.info("同步文件:{}", file.getPath() +"____" +replaceFileOrDir);
            }
            // 关闭连接
            sftpChannel.disconnect();
            session.disconnect();
        } catch (Exception e) {
            throw new RuntimeException("同步文件失败 : " + e.getMessage());
        } finally {
            // 关闭连接
            if (null != sftpChannel) {
                sftpChannel.disconnect();
            }
            if (null != session) {
                session.disconnect();
            }
        }
    }

    /**
     * 递归删除远程服务器文件夹
     *
     * @param deleteDest 远程文件夹文件
     * @throws SftpException sftp异常
     */
    public static void deleteDirectory(String deleteDest, ChannelSftp sftp,boolean flag) throws SftpException {
        if (sftp.stat(deleteDest).isDir()) {
            sftp.cd(deleteDest);
            Vector<ChannelSftp.LsEntry> entries = sftp.ls(".");
            for (ChannelSftp.LsEntry entry : entries) {
                String fileName = entry.getFilename();
                if (fileName.equals(".") || fileName.equals("..")) {
                    continue;
                }
                deleteDirectory(entry.getFilename(), sftp,true);
            }
            if (flag) {
                sftp.cd("..");
                sftp.rmdir(deleteDest);
            }
        } else {
            sftp.rm(deleteDest);
        }
    }

    /**
     * 递归根据路径创建文件夹
     *
     * @param dirs 根据 / 分隔后的数组文件夹名称
     * @return
     */
    public static void mkdirDir(String[] dirs, ChannelSftp sftp) throws SftpException {
        StringBuilder builder = new StringBuilder();

        for (int i = 1; i < dirs.length; i++) {
            if (i == 0 && StrUtil.isEmptyIfStr(dirs[i])) {
                builder.append("/");
                break;
            }
            builder.append("/");
            builder.append(dirs[i]);
            boolean b = dirExists(builder.toString(), sftp);
            if (!b) {
                sftp.mkdir(builder.toString());
            }
        }
    }

    /**
     * 判断文件夹是否存在
     *
     * @param remoteFolderPath 远程地址
     * @param sftpChannel      sftp 连接
     * @return true 存在  false 不存在
     */
    private static boolean dirExists(String remoteFolderPath, ChannelSftp sftpChannel) {
        SftpATTRS stat = null;
        try {
            stat = sftpChannel.stat(remoteFolderPath);
            return true;
        } catch (SftpException e) {
            return false;
        }
    }

    /**
     * 获取文件夹中的所有图片
     *
     * @param folderPath 目录  \Users\Administrator\Desktop\image
     * @return 所有文件
     */
    public static Vector<File> listFiles(String folderPath) {
        Vector<File> files = new Vector<File>();
        File folder = new File(folderPath);
        File[] listFiles = folder.listFiles();
        if (listFiles != null) {
            for (File file : listFiles) {
                if (file.isFile()) {
                    files.addElement(file);
                } else if (file.isDirectory()) {
                    Vector<File> subFiles = listFiles(file.getPath());
                    files.addAll(subFiles);
                }
            }
        }
        return files;
    }


}
