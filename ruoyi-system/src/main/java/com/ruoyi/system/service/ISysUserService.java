package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserPost;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.domain.entity.SysUserAuth;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.OaFlowTreeVo;
import com.ruoyi.system.domain.vo.ProClassificationVo;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.domain.vo.SysUserPostVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService
{
    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser user);

    public List<SysUser> selectUserListOfDayLog(SysUser user);

    public List<SysUserAuth> listForPersonnel(SysUser user);

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    public SysUser selectUserByUserNameOfTrue(String userName);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserRoleGroup(String userName);

    /**
     * 根据用户ID查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserPostGroup(String userName);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    public String checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public String checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public String checkEmailUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    public void checkUserAllowed(SysUser user);

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    public void checkUserDataScope(Long userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user);

    public void insertUserPost(SysUser user);

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean registerUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUser(SysUser user);

    /**
     * 用户授权角色
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    public void insertUserAuth(Long userId, Long[] roleIds);

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserStatus(SysUser user);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserProfile(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    public boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    public int resetPwd(SysUser user);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(String userName, String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 导入用户数据
     *
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName);

    List<SysUser> selectUserByUserIds(List<Long> userIdList);

	public List<SysUser> selectUserListEnable();
	public List<SysUser> selectUserListAll();

    //根据部门id查找该部门的所有的用户
    List<SysUser> getUserListByDeptId(SysUser sysUser);

    List<SysUser> selectUserListByDeptIds(Long[] deptId);

    Map<String, Object> getMenuList(Long userId);

    //获取用户授权list
    List<SysUserAuth> getUserAuthorizationList(SysUser sysUser);

    //根据岗位id查找该岗位的所有的用户
    List<SysUser> getUserListByPostId(SysUser sysUser, LoginUser loginUser);


    public List<SysUser> selectUserListByPostId(Long[] postId);


    //根据用户名查主岗位
    String selectUserMainPost(String userName);

    /**
     * 根据岗位id查询已授权用户列表
     * @param sysUserPostVo
     * @return
     */
    List<SysUser> queryAccreditUserList(SysUserPostVo sysUserPostVo);

    /**
     * 拼接部门信息
     * @param userPostList
     */
    List<SysUserPost> jointDept(List<SysUserPost> userPostList,List<SysPost> sysPosts);

    /**
     * 根据用户id查询其岗位所属公司的所有oa流程
     * @param userId
     * @return
     */
    List<ProClassificationVo> selectUserProcessListByUserId(Long userId);

    /**
     * 通过用户ID数组查询用户ID
     * @param authUserIds   用户ID数组
     * @return 用户信息
     */
    List<SysUser> selectUserByIds(Long[] authUserIds);

    /**
     * 获取指定部门下的用户组织架构
     * @return
     */
    List<SysDept> getAssignDeptUserList( SysDept sysDept);
}
