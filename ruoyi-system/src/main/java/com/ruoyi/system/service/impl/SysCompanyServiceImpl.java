package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.FunctionNodeEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.mapper.IXmgAddTemporarilyMapper;
import com.ruoyi.system.mapper.SysCompanyMapper;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SystemSelectTypeMapper;
import com.ruoyi.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 全量公司信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
public class SysCompanyServiceImpl implements ISysCompanyService
{
    @Autowired
    private SysCompanyMapper sysCompanyMapper;
    @Autowired
    private ICompanyTypeMappingService companyTypeMappingService;
    @Autowired
    private ICompanyBusinessTypeMappingService companyBusinessTypeMappingService;
    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private ISysOperLogService sysOperLogService;
    @Autowired
    private ISysInfoEditRecordService sysInfoEditRecordService;
    @Resource
    private IXmgAddTemporarilyMapper xmgAddTemporarilyMapper;
    //@Autowired
    //private ISysInfoEditRecordService sysInfoEditRecordService;
    @Autowired
    private INewAuthorityService newAuthorityService;

    @Autowired
    private SystemSelectTypeMapper systemSelectTypeMapper;
    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    /**
     * 查询全量公司信息
     *
     * @param id 全量公司信息主键
     * @return 全量公司信息
     */
    @Override
    public SysCompanyVo selectSysCompanyById(Long id)
    {
        return sysCompanyMapper.selectSysCompanyById(id);
    }

    /**
     * 查询全量公司信息列表
     *
     * @param sysCompany 全量公司信息
     * @return 全量公司信息
     */
    @Override
    public List<SysCompanyVo> selectSysCompanyList(SysCompanyVo sysCompany)
    {
        PageUtils.startPage();
        return sysCompanyMapper.selectSysCompanyList(sysCompany);
    }

    /**
     * 查询全量公司信息列表
     *  ****绩效考核带权限****
     * @param sysCompany 全量公司信息
     * @return 全量公司信息
     */
    @Override
    public List<SysCompanyVo> selectSysCompanyListHaveAuthority(SysCompanyVo sysCompany)
    {
        LoginUser loginUser = getLoginUser();
//        List<Long> authorityComapnyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.PERFORMANCE.getCode());
//
//        if (sysCompany.getCompanyIdList() != null && !sysCompany.getCompanyIdList().isEmpty()){
//            authorityComapnyIds = authorityComapnyIds.stream().filter(vo -> sysCompany.getCompanyIdList().contains(vo)).collect(Collectors.toList());
//        }
//        if (authorityComapnyIds.isEmpty()){
//            return new ArrayList<>();
//        }
//        sysCompany.setCompanyIdList(authorityComapnyIds);
        PageUtils.startPage();
        return sysCompanyMapper.selectSysCompanyList(sysCompany);
    }
    /**
     * 新增全量公司信息
     *
     * @param sysCompany 全量公司信息
     * @return 结果
     */
    @Override
    @Transactional
    public Map<String,Object> insertSysCompany(SysCompanyVo sysCompany)
    {
        String errorMsg = "";
        Map<String,Object> map = new HashMap<>();
        Boolean flag = false;
        String addUuid = sysCompany.getAddUuid();
        try {
            if (null != sysCompany.getAddSource() && !sysCompany.getAddSource().equals("") && sysCompany.getAddSource().equals("xmgl")) {
                flag = true;
            }
            sysCompany.setCreateTime(DateUtils.getNowDate());
            if (flag) {
                sysCompany.setCheckStatus("0");//新增待审核状态
                sysCompany.setSource("2");//数据来源(2立项项目)
            }
            sysCompany.setCreateBy(SecurityUtils.getUsername());
            int i = sysCompanyMapper.insertSysCompany(sysCompany);

            if(sysCompany.getCompanyTypeCodes() != null && !sysCompany.getCompanyTypeCodes().isEmpty()){
                ArrayList<CompanyTypeMapping> companyTypeMappings = new ArrayList<>();
                sysCompany.getCompanyTypeCodes().forEach(vo -> {
                    CompanyTypeMapping companyTypeMapping = new CompanyTypeMapping();
                    companyTypeMapping.setCompanyId(sysCompany.getId());
                    companyTypeMapping.setCompanyTypeCode(vo);
                    companyTypeMapping.setCreateTime(DateUtils.getNowDate());
                    companyTypeMapping.setCreateBy(getLoginUser().getUsername());
                    companyTypeMappings.add(companyTypeMapping);
                });
                companyTypeMappingService.batchInsertCompanyTypeMapping(companyTypeMappings);
            }

            if(sysCompany.getCompanyBusinessTypeCodes() != null && !sysCompany.getCompanyBusinessTypeCodes().isEmpty()) {
                ArrayList<CompanyBusinessTypeMapping> companyBusinessTypeMappings = new ArrayList<>();
                sysCompany.getCompanyBusinessTypeCodes().forEach(vo -> {
                    CompanyBusinessTypeMapping companyBusinessTypeMapping = new CompanyBusinessTypeMapping();
                    companyBusinessTypeMapping.setCompanyId(sysCompany.getId());
                    companyBusinessTypeMapping.setCompanyBusinessTypeCode(vo);
                    companyBusinessTypeMapping.setCreateTime(DateUtils.getNowDate());
                    companyBusinessTypeMapping.setCreateBy(getLoginUser().getUsername());
                    companyBusinessTypeMappings.add(companyBusinessTypeMapping);
                });
                companyBusinessTypeMappingService.batchInsertCompanyBusinessTypeMapping(companyBusinessTypeMappings);
            }
            //返回新增
            Long id = sysCompany.getId();
            // add by niey 如果是在新增立项项目时创建的数据，需插入立项项目临时表 开始
            if (flag) {
                XmglAddTemporarilyVo addTemporarilyVo = new XmglAddTemporarilyVo();
                addTemporarilyVo.setAddType("2");
                addTemporarilyVo.setCompanyId(id);
                addTemporarilyVo.setCreateBy(SecurityUtils.getUsername());
                addTemporarilyVo.setCreateTime(DateUtils.getNowDate());
                addTemporarilyVo.setAddSource(sysCompany.getAddSource());
                if (null == addUuid || addUuid.equals("")) {
                    addUuid = IdUtils.fastSimpleUUID();
                    addTemporarilyVo.setAddUuid(addUuid);
                }else {
                    addTemporarilyVo.setAddUuid(sysCompany.getAddUuid());
                }
                xmgAddTemporarilyMapper.insertXmgAddTemporarily(addTemporarilyVo);
            }

            map.put("returnUUid", addUuid);
            map.put("returnId",id);
            return map;
            // add by niey 如果是再新增立项项目时创建的数据，需插入立项项目临时表 结束
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "新增了【" + sysCompany.getCompanyName() + "】公司";
            Long i = sysOperLogService.insertOperLogMessageOfRecord(AuthModuleEnum.COMPANY.getCode(), FunctionNodeEnum.UNITIN.getCode(), operMessage, 1, errorMsg,sysCompany.getId().toString());
            //添加前后对比记录
//            if (i> 0){
//                SysInfoEditRecord sysInfoEditRecord = new SysInfoEditRecord();
//                sysInfoEditRecord.setLogQueryId(i);
//                sysInfoEditRecord.setNewInfo(JSON.toJSONString(sysCompany));
//                sysInfoEditRecordService.insertSysInfoEditRecord(sysInfoEditRecord);
//            }
        }
    }

    /**
     * 修改全量公司信息
     *
     * @param sysCompany 全量公司信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateSysCompany(SysCompanyVo sysCompany)
    {
        Date nowDate = DateUtils.getNowDate();
        sysCompany.setUpdateTime(nowDate);
        String errorMsg = "";
        Map<Integer,String> updateMap =new HashMap<>();
        SysCompanyVo oldSysCompany = selectSysCompanyById(sysCompany.getId());
        try {
            SysCompanyVo companyById = selectSysCompanyById(sysCompany.getId());
            if (!companyById.getCompanyName().equals(sysCompany.getCompanyName())){
                updateMap.put(14,"修改了【" + sysCompany.getCompanyName() + "】的公司名称");
            }
            if (!companyById.getCompanyShortName().equals(sysCompany.getCompanyShortName())){
                updateMap.put(15,"修改了【" + sysCompany.getCompanyName() + "】的公司简称");
            }
            if (!companyById.getIsInside().equals(sysCompany.getIsInside())){
                updateMap.put(18,"修改了【" + sysCompany.getCompanyName() + "】是否内部公司");
            }
            if (!companyById.getStatus().equals(sysCompany.getStatus())){
                updateMap.put(19,"修改了【" + sysCompany.getCompanyName() + "】是否启用状态");
            }

            companyBusinessTypeMappingService.deleteCompanyBusinessTypeMappingByCompanyId(sysCompany.getId());
            companyTypeMappingService.deleteCompanyTypeMappingByCompanyId(sysCompany.getId());

            if(sysCompany.getCompanyTypeCodes() != null && !sysCompany.getCompanyTypeCodes().isEmpty()){
                List<Long> companyTypeCodeList = companyById.getCompanyTypeMappingList().stream()
                        .map(CompanyTypeMappingVo::getDictCode)
                        .filter(Objects::nonNull) // 排除 null 值
                        .sorted()
                        .collect(Collectors.toList());
                Collections.sort(sysCompany.getCompanyTypeCodes());

                if(!sysCompany.getCompanyTypeCodes().equals(companyTypeCodeList)){
                    updateMap.put(16,"修改了【" + sysCompany.getCompanyName() + "】的公司类型");
                }

                ArrayList<CompanyTypeMapping> companyTypeMappings = new ArrayList<>();
                sysCompany.getCompanyTypeCodes().forEach(vo -> {
                    CompanyTypeMapping companyTypeMapping = new CompanyTypeMapping();
                    companyTypeMapping.setCompanyId(sysCompany.getId());
                    companyTypeMapping.setCompanyTypeCode(vo);
                    companyTypeMapping.setCreateTime(nowDate);
                    companyTypeMapping.setCreateBy(getLoginUser().getUsername());
                    companyTypeMappings.add(companyTypeMapping);
                });
                companyTypeMappingService.batchInsertCompanyTypeMapping(companyTypeMappings);
            }

            List<Long> companyBusinessTypeCodeList = companyById.getCompanyBusinessTypeMappingList().stream()
                    .map(CompanyBusinessTypeMappingVo::getDictCode)
                    .filter(Objects::nonNull) // 排除 null 值
                    .sorted()
                    .collect(Collectors.toList());
            Collections.sort(sysCompany.getCompanyBusinessTypeCodes());

            if(!sysCompany.getCompanyBusinessTypeCodes().equals(companyBusinessTypeCodeList)){
                updateMap.put(17,"修改了【" + sysCompany.getCompanyName() + "】的支持业务类型");
            }
            if(!sysCompany.getCompanyBusinessTypeCodes().isEmpty()){
                ArrayList<CompanyBusinessTypeMapping> companyBusinessTypeMappings = new ArrayList<>();
                sysCompany.getCompanyBusinessTypeCodes().forEach(vo ->{
                    CompanyBusinessTypeMapping companyBusinessTypeMapping = new CompanyBusinessTypeMapping();
                    companyBusinessTypeMapping.setCompanyId(sysCompany.getId());
                    companyBusinessTypeMapping.setCompanyBusinessTypeCode(vo);
                    companyBusinessTypeMapping.setCreateTime(nowDate);
                    companyBusinessTypeMapping.setCreateBy(getLoginUser().getUsername());
                    companyBusinessTypeMappings.add(companyBusinessTypeMapping);
                });
                companyBusinessTypeMappingService.batchInsertCompanyBusinessTypeMapping(companyBusinessTypeMappings);
            }


            return sysCompanyMapper.updateSysCompany(sysCompany);
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.COMPANY.getCode(), FunctionNodeEnum.UNITIN.getCode(), "", 2, errorMsg,sysCompany.getId().toString());
            throw new RuntimeException(e);
        } finally {
            //当errorMsg为空时新增修改日志
            AtomicLong i = new AtomicLong(0L);
            if (errorMsg.isEmpty()){
                updateMap.forEach((key, value) -> {
                    i.getAndSet(sysOperLogService.insertOperLogMessageOfRecord(AuthModuleEnum.COMPANY.getCode(), FunctionNodeEnum.UNITIN.getCode(), value, key, "",sysCompany.getId().toString()));
                });
            }
            //添加前后对比记录
//            if (i.get() > 0){
//                SysInfoEditRecord sysInfoEditRecord = new SysInfoEditRecord();
//                sysInfoEditRecord.setLogQueryId(i.get());
//                sysInfoEditRecord.setOldInfo(JSON.toJSONString(oldSysCompany));
//                sysInfoEditRecord.setNewInfo(JSON.toJSONString(sysCompany));
//                sysInfoEditRecordService.insertSysInfoEditRecord(sysInfoEditRecord);
//            }
        }
    }

    /**
     * 批量删除全量公司信息
     *
     * @param ids 需要删除的全量公司信息主键
     * @return 结果
     */
    @Override
    public int deleteSysCompanyByIds(Long[] ids)
    {
        return sysCompanyMapper.deleteSysCompanyByIds(ids);
    }

    /**
     * 删除全量公司信息信息
     *
     * @param id 全量公司信息主键
     * @return 结果
     */
    @Override
    public int deleteSysCompanyById(Long id)
    {
        String errorMsg = "";
        SysCompanyVo sysCompanyVo = selectSysCompanyById(id);
        try {
            companyBusinessTypeMappingService.deleteCompanyBusinessTypeMappingByCompanyId(id);
            companyTypeMappingService.deleteCompanyTypeMappingByCompanyId(id);
            return sysCompanyMapper.deleteSysCompanyById(id);
        } catch (Exception e) {
                    e.printStackTrace();
                    StackTraceElement stackTraceElement = e.getStackTrace()[0];;
                    errorMsg = "错误信息:" + e.toString() + " at "
                            + stackTraceElement.getClassName() + "."
                            + stackTraceElement.getMethodName() + ":"
                            + stackTraceElement.getLineNumber();
                    throw new RuntimeException(e);
        } finally {
            String operMessage = "删除了【" + sysCompanyVo.getCompanyName() + "】公司";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.COMPANY.getCode(), FunctionNodeEnum.UNITIN.getCode(), operMessage, 3, errorMsg, sysCompanyVo.getId().toString());
        }
    }

    @Override
    public Object checkCompanyName(SysCompany sysCompany){
        List<SysCompany> sysCompanyList = sysCompanyMapper.selectSysCompanyListByCheck(sysCompany);
        Map<String,Boolean> map = new HashMap<>();
        //校验公司名称
        List<SysCompany> companyNameList = sysCompanyList.stream()
                .filter(vo -> !vo.getId().equals(sysCompany.getId()) && vo.getCompanyName().equals(sysCompany.getCompanyName()))
                .collect(Collectors.toList());
        if (!companyNameList.isEmpty()){
            map.put("isShowCompanyName",true);
        } else {
            map.put("isShowCompanyName",false);
        }
        //校验公司简称
        List<SysCompany> companyShortNameList = sysCompanyList.stream()
                .filter(vo -> !vo.getId().equals(sysCompany.getId()) && vo.getCompanyShortName().equals(sysCompany.getCompanyShortName()))
                .collect(Collectors.toList());
        if (!companyShortNameList.isEmpty()){
            map.put("isShowCompanyShortName",true);
        } else {
            map.put("isShowCompanyShortName",false);
        }
        //校验公司 code
        List<SysCompany> companyCodeList = sysCompanyList.stream()
                .filter(vo -> !vo.getId().equals(sysCompany.getId()) && vo.getCompanyCode().equals(sysCompany.getCompanyCode()))
                .collect(Collectors.toList());
        if (!companyCodeList.isEmpty()){
            map.put("isShowCompanyCode",true);
        } else {
            map.put("isShowCompanyCode",false);
        }

        return map;
    }


    /**
     * 添加公司类型
     * @param data
     * @return
     */
    @Override
    public Map<String,Object> insertCompanyType(SysDictData data){
        String errorMsg = "";
        String adduuid = "";
        Map<String,Object> map = new HashMap<>();
        try {
            data.setCreateBy(getUsername());
            data.setDictType("company_type");
            data.setDictValue(IdUtils.fastSimpleUUID());
            data.setListClass("default");
            Long i = sysDictDataService.insertDictDataReturnCode(data);
            // add by niey 如果是再新增立项项目时创建的数据，需插入立项项目临时表
            if(null != data.getAddSource() && !data.getAddSource().equals("") && data.getAddSource().equals("xmgl")){
                XmglAddTemporarilyVo addTemporarilyVo = new XmglAddTemporarilyVo();
                addTemporarilyVo.setAddType("3");
                addTemporarilyVo.setCompTypeId(i);
                addTemporarilyVo.setCreateBy(SecurityUtils.getUsername());
                addTemporarilyVo.setCreateTime(DateUtils.getNowDate());
                addTemporarilyVo.setAddSource(data.getAddSource());
                if (null == data.getAddUuid() || data.getAddUuid().equals("")){
                    adduuid = IdUtils.fastSimpleUUID();
                    addTemporarilyVo.setAddUuid(adduuid);
                }else {
                    addTemporarilyVo.setAddUuid(data.getAddUuid());
                }
                xmgAddTemporarilyMapper.insertXmgAddTemporarily(addTemporarilyVo);
                map.put("returnUUid",adduuid);
            }
            map.put("returnId",i);
            // add by niey 如果是再新增立项项目时创建的数据，需插入立项项目临时表 结束
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "新增了【" + data.getDictLabel() + "】公司类型";
            Long i = sysOperLogService.insertOperLogMessageOfRecord(AuthModuleEnum.COMPANY.getCode(), FunctionNodeEnum.COMPANYTYPE.getCode(), operMessage, 1, errorMsg,data.getDictCode().toString());
            //添加前后对比记录
//            if (i > 0){
//                SysInfoEditRecord sysInfoEditRecord = new SysInfoEditRecord();
//                sysInfoEditRecord.setLogQueryId(i);
//                sysInfoEditRecord.setNewInfo(JSON.toJSONString(data));
//                sysInfoEditRecordService.insertSysInfoEditRecord(sysInfoEditRecord);
//            }
        }
        return map;
    }

    @Override
    public List<CompanyTypeVo> getCompanyTypeList(SysDictData sysDictData){
        return sysCompanyMapper.getCompanyTypeList(sysDictData);
    }

    /**
     * 校验公司类型名称是否唯一
     * @param sysDictData
     * @return
     */
    @Override
    public Object checkCompanyType(SysDictData sysDictData){
        SysDictData newSysDictData = new SysDictData();
        newSysDictData.setDictType("company_type");
        List<SysDictData> sysDictDataList = sysDictDataService.selectDictDataList(newSysDictData).stream()
                     .filter(vo -> !Objects.equals(sysDictData.getDictCode(), vo.getDictCode()))
                     .collect(Collectors.toList());;

        List<SysDictData> typeCodeErrorList = sysDictDataList.stream()
                .filter(vo -> sysDictData.getDictLabel().equals(vo.getDictLabel()))
                .collect(Collectors.toList());
        boolean flag = false;
        if(sysDictData.getDictSort() != null && sysDictData.getDictSort() != 0){
            for (SysDictData sort : sysDictDataList) {
                if (sysDictData.getDictSort().equals(sort.getDictSort())) {
                    flag = true;
                    break;
                }
            }
        }

        HashMap<String, String> map = new HashMap<>();
        if (!typeCodeErrorList.isEmpty()){
            map.put("laberError", "系统中已经存在相同名称的公司类型!");
        }

        if (flag){
            String dictSortString = sysDictDataList.stream()
                    .map(SysDictData::getDictSort) // 获取 dictSort 字段值
                    .map(String::valueOf) // 将字段值转换为字符串
                    .filter(Objects::nonNull) // 过滤掉 null 值
                    .filter(str -> !str.isEmpty()) // 过滤掉空字符串
                    .distinct()
                    .collect(Collectors.joining(",")); // 使用逗号将字符串连接起来
            map.put("sortError", "序号重复，重复序号为" + dictSortString +"!");
        }
        return map;
    }


    /**
     * 添加公司支持业务类型
     * @param data
     * @return
     */
    @Override
    public Long insertCompanyBusinessType(SysDictData data){
        String errorMsg = "";
        try {
            data.setCreateBy(getUsername());
            data.setDictType("company_business_type");
            data.setDictValue(IdUtils.fastSimpleUUID());
            data.setListClass("default");
            return sysDictDataService.insertDictDataReturnCode(data);
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "新增了【" + data.getDictLabel() + "】公司支持类型";
            Long i = sysOperLogService.insertOperLogMessageOfRecord(AuthModuleEnum.COMPANY.getCode(), FunctionNodeEnum.COMPANYBUSINESSTYPE.getCode(), operMessage, 1, errorMsg,data.getDictCode().toString());
            //添加前后对比记录
//            if (i > 0){
//                SysInfoEditRecord sysInfoEditRecord = new SysInfoEditRecord();
//                sysInfoEditRecord.setLogQueryId(i);
//                sysInfoEditRecord.setNewInfo(JSON.toJSONString(data));
//                sysInfoEditRecordService.insertSysInfoEditRecord(sysInfoEditRecord);
//            }
        }
    }

    @Override
    public List<CompanyBusinessTypeVo> getCompanyBusinessTypeList(SysDictData sysDictData){
        return sysCompanyMapper.getCompanyBusinessTypeList(sysDictData);
    }


    /**
     * 校验公司类型名称是否唯一
     * @param sysDictData
     * @return
     */
    @Override
    public Object checkCompanyBusinessType(SysDictData sysDictData){
        SysDictData newSysDictData = new SysDictData();
        newSysDictData.setDictType("company_business_type");
        List<SysDictData> sysDictDataList = sysDictDataService.selectDictDataList(newSysDictData).stream()
                        .filter(vo -> !Objects.equals(sysDictData.getDictCode(), vo.getDictCode()))
                        .collect(Collectors.toList());

        List<SysDictData> typeCodeErrorList = sysDictDataList.stream()
                .filter(vo -> sysDictData.getDictLabel().equals(vo.getDictLabel()))
                .collect(Collectors.toList());
        boolean flag = false;
        if(sysDictData.getDictSort() != null && sysDictData.getDictSort() != 0){
            for (SysDictData sort : sysDictDataList) {
                if (sysDictData.getDictSort().equals(sort.getDictSort())) {
                    flag = true;
                    break;
                }
            }
        }

        HashMap<String, String> map = new HashMap<>();
        if (!typeCodeErrorList.isEmpty()){
            map.put("laberError", "系统中已经存在相同名称的公司类型!");
        }

        if (flag){
            String dictSortString = sysDictDataList.stream()
                    .map(SysDictData::getDictSort) // 获取 dictSort 字段值
                    .map(String::valueOf) // 将字段值转换为字符串
                    .collect(Collectors.joining(",")); // 使用逗号将字符串连接起来
            map.put("sortError", "序号重复，重复序号为" + dictSortString +"!");
        }
        return map;
    }

    @Override
    public int updateCompanyType(SysDictData data){
        String errorMsg = "";
        SysDictData oldData = sysDictDataService.selectDictDataById(data.getDictCode());
        try {
            return sysDictDataService.updateDictData(data);
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "修改了【" + data.getDictLabel() + "】公司类型";
            Long i = sysOperLogService.insertOperLogMessageOfRecord(AuthModuleEnum.COMPANY.getCode(), FunctionNodeEnum.COMPANYTYPE.getCode(), operMessage, 2, errorMsg,data.getDictCode().toString());
            //添加前后对比记录
//            if (i > 0){
//                SysInfoEditRecord sysInfoEditRecord = new SysInfoEditRecord();
//                sysInfoEditRecord.setLogQueryId(i);
//                sysInfoEditRecord.setOldInfo(JSON.toJSONString(oldData));
//                sysInfoEditRecord.setNewInfo(JSON.toJSONString(data));
//                sysInfoEditRecordService.insertSysInfoEditRecord(sysInfoEditRecord);
//            }
        }
    }

    @Override
    public int updateCompanyBusinessType(SysDictData data){
        String errorMsg = "";
        SysDictData oldData = sysDictDataService.selectDictDataById(data.getDictCode());
        try {
            return sysDictDataService.updateDictData(data);
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "修改了【" + data.getDictLabel() + "】公司支持类型";
            Long i = sysOperLogService.insertOperLogMessageOfRecord(AuthModuleEnum.COMPANY.getCode(), FunctionNodeEnum.COMPANYBUSINESSTYPE.getCode(), operMessage, 2, errorMsg,data.getDictCode().toString());
            //添加前后对比记录
//            if (i > 0){
//                SysInfoEditRecord sysInfoEditRecord = new SysInfoEditRecord();
//                sysInfoEditRecord.setLogQueryId(i);
//                sysInfoEditRecord.setOldInfo(JSON.toJSONString(oldData));
//                sysInfoEditRecord.setNewInfo(JSON.toJSONString(data));
//                sysInfoEditRecordService.insertSysInfoEditRecord(sysInfoEditRecord);
//            }
        }
    }

    public List<CompanyLogVo> getCompanyLogVoList(SysOperLog sysOperLog){
        return sysCompanyMapper.getCompanyLogVoList(sysOperLog);
    }

    @Override
    public void deleteCompanyType(Long dictCode){
        String errorMsg = "";
        SysDictData sysDictData = sysDictDataService.selectDictDataById(dictCode);
        try {
             sysDictDataService.deleteDictDataById(dictCode);
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "删除了【" + sysDictData.getDictLabel() + "】公司类型";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.COMPANY.getCode(), FunctionNodeEnum.COMPANYTYPE.getCode(), operMessage, 3, errorMsg,sysDictData.getDictCode().toString());
        }
    }

     public void deleteCompanyBusinessType(Long dictCode){
        String errorMsg = "";
         SysDictData sysDictData = sysDictDataService.selectDictDataById(dictCode);
        try {
             sysDictDataService.deleteDictDataById(dictCode);
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "删除了【" + sysDictData.getDictLabel() + "】公司业务类型";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.COMPANY.getCode(), FunctionNodeEnum.COMPANYBUSINESSTYPE.getCode(), operMessage, 3, errorMsg,sysDictData.getDictCode().toString());
        }
     }

    @Override
    public List<SysCompanyVo> selectSysCompanyListForAuthority(SysCompanyVo sysCompany, List<Long> companyIdList) {
        return sysCompanyMapper.selectSysCompanyListForAuthority(sysCompany, companyIdList);
    }

    @Override
    public Map<String, Object> getCompanyIsReference(SysCompanyVo sysCompany) {
        HashMap<String, Object> returnMap = new HashMap<>();

        //查询公司下的模版
         Map<String,Object> classNumMap = sysCompanyMapper.selectOaProcessClassNum(sysCompany.getId());
        int classnum = Integer.parseInt(classNumMap.get("num").toString());
        //查询与公司关联的项目
        Map<String,Object> projectNumMap = sysCompanyMapper.getProjectNumByComId(sysCompany.getId());
        int pronum = Integer.parseInt(projectNumMap.get("num").toString());

        if(pronum>0 && classnum>0){
            returnMap.put("code","Y");
            returnMap.put("msg","当前公司已被其他功能模块引用，无法删除！");
        }else  if(pronum>0 && classnum==0){
            returnMap.put("code","Y");
            returnMap.put("msg","当前公司存在已合作的项目，无法删除！");
        }else  if(pronum==0 && classnum==0){
            returnMap.put("code","N");
        }else  if(pronum==0 && classnum>0){
            returnMap.put("code","Y");
            returnMap.put("msg","当前公司已被其他功能模块引用，无法删除！");
        }
        return returnMap;
    }

    @Override
    public List<SysCompanyVo> selectCompanyListByCompanyShortNames(Set<String> companyNames){
        return sysCompanyMapper.selectCompanyListByCompanyShortNames(companyNames);
    }

    @Override
    public List<SysCompanyVo> selectSysCompanyListNewAuthority(SysCompanyVo sysCompany)
    {
        LoginUser loginUser = getLoginUser();
        List<Long> authorityComapnyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(loginUser.getUserId(), sysCompany.getAuthModuleEnumCode());

        if (sysCompany.getCompanyIdList() != null && !sysCompany.getCompanyIdList().isEmpty()){
            authorityComapnyIds = authorityComapnyIds.stream().filter(vo -> sysCompany.getCompanyIdList().contains(vo)).collect(Collectors.toList());
        }
        if (authorityComapnyIds.isEmpty()){
            return new ArrayList<>();
        }
        sysCompany.setCompanyIdList(authorityComapnyIds);
        PageUtils.startPage();
        return sysCompanyMapper.selectSysCompanyList(sysCompany);
    }

    @Override
    public List<SysCompanyVo> selectSysCompanyListNewAuthorityAndType(SysCompanyVo sysCompany)
    {
        LoginUser loginUser = getLoginUser();
        List<Long> authorityComapnyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(loginUser.getUserId(), sysCompany.getAuthModuleEnumCode());

        if (sysCompany.getCompanyIdList() != null && !sysCompany.getCompanyIdList().isEmpty()){
            authorityComapnyIds = authorityComapnyIds.stream().filter(vo -> sysCompany.getCompanyIdList().contains(vo)).collect(Collectors.toList());
        }
        if (authorityComapnyIds.isEmpty()){
            return new ArrayList<>();
        }
//        sysCompany.setCompanyIdList(authorityComapnyIds);

        SystemSelectType systemSelectType = new SystemSelectType();
        systemSelectType.setSelectCode(sysCompany.getSelectCode());
        systemSelectType.setModelCode(sysCompany.getAuthModuleEnumCode());
        List<String> companyTypeId =  systemSelectTypeMapper.queryDataByModelCode(systemSelectType.getSelectCode(),systemSelectType.getModelCode());
        List<SysCompanyVo>  sysCompanyVos = new ArrayList<>();
        //如果没有配置 或者配置为空则返回类型的公司
        if(companyTypeId.size() == 0){
            String comTypeId = null;
            if(systemSelectType.getSelectCode().equals("cust")){
                SysDictData byDictLabel = sysDictDataMapper.getByDictLabel("担保公司", "company_type");
                comTypeId = byDictLabel.getDictCode().toString();
            }else  if(systemSelectType.getSelectCode().equals("partner")){
                SysDictData byDictLabel = sysDictDataMapper.getByDictLabel("资产方", "company_type");
                comTypeId = byDictLabel.getDictCode().toString();
            } else if(systemSelectType.getSelectCode().equals("fund")){
                SysDictData byDictLabel = sysDictDataMapper.getByDictLabel("资金方", "company_type");
                comTypeId = byDictLabel.getDictCode().toString();
            }
            companyTypeId.add(comTypeId);
            sysCompanyVos =  getCompanyBySelectType(companyTypeId);
        }else {
            sysCompanyVos =  getCompanyBySelectType(companyTypeId);
        }

        List<Long> finalAuthorityComapnyIds = authorityComapnyIds;
        List<Long> ids = sysCompanyVos.stream()
                .filter(vo -> finalAuthorityComapnyIds.contains(vo.getId()))  // 过滤：只保留id在authorityComapnyIds中的元素
                .map(SysCompanyVo::getId)                                // 映射：提取id
                .collect(Collectors.toList());                          // 收集为List<Long>
        if (ids.isEmpty()){
            return new ArrayList<>();
        }
        sysCompany.setCompanyIdList(ids);
        PageUtils.startPage();
        return sysCompanyMapper.selectSysCompanyList(sysCompany);
    }

    @Override
    public List<SysCompanyVo> getCompanyBySelectType(List<String> companyTypeId) {
        return sysCompanyMapper.selectCompanyDataByTypeId(companyTypeId);
    }

    @Override
    public List<SysCompanyVo> selectSysCompanyListForAuthorityNew(SysCompanyVo sysCompany, List<Long> companyTypeCode, List<Long> companyIdList) {
        return sysCompanyMapper.selectSysCompanyListForAuthorityNew(sysCompany, companyTypeCode, companyIdList);
    }

    /**人员调用时(调入公司)
     *
     * @param sysCompany
     * @return
     */
    @Override
    public List<SysCompanyVo> selectSysCompanyListTransfer(SysCompanyVo sysCompany)
    {
        ArrayList<String> codeLable = new ArrayList<>();
        codeLable.add("内部担保公司");
        codeLable.add("内部科技公司");
        sysCompany.setCompanyTypeCodeNameList(codeLable);
        return sysCompanyMapper.selectSysCompanyList(sysCompany);
    }

    @Override
    public SysCompany selectSysCompanyByCompanyShortName(SysCompany sysCompany){
        return sysCompanyMapper.selectSysCompanyByCompanyShortName(sysCompany);
    }
}
