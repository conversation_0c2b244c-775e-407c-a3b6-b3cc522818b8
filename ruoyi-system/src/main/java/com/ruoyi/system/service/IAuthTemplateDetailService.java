package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.AuthTemplateDetail;

/**
 * 权限模板明细Service接口
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
public interface IAuthTemplateDetailService
{
    /**
     * 查询权限模板明细
     *
     * @param id 权限模板明细主键
     * @return 权限模板明细
     */
    public AuthTemplateDetail selectAuthTemplateDetailById(String id);

    /**
     * 查询权限模板明细列表
     *
     * @param authTemplateDetail 权限模板明细
     * @return 权限模板明细集合
     */
    public List<AuthTemplateDetail> selectAuthTemplateDetailList(AuthTemplateDetail authTemplateDetail);

    /**
     * 新增权限模板明细
     *
     * @param authTemplateDetail 权限模板明细
     * @return 结果
     */
    public int insertAuthTemplateDetail(AuthTemplateDetail authTemplateDetail);

    /**
     * 修改权限模板明细
     *
     * @param authTemplateDetail 权限模板明细
     * @return 结果
     */
    public int updateAuthTemplateDetail(AuthTemplateDetail authTemplateDetail);

    /**
     * 批量删除权限模板明细
     *
     * @param ids 需要删除的权限模板明细主键集合
     * @return 结果
     */
    public int deleteAuthTemplateDetailByIds(String[] ids);

    /**
     * 删除权限模板明细信息
     *
     * @param id 权限模板明细主键
     * @return 结果
     */
    public int deleteAuthTemplateDetailById(String id);
}
