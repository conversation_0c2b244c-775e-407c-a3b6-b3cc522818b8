package com.ruoyi.system.service;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.NewInfoTree;
import com.ruoyi.common.core.domain.entity.NewsConfigCenter;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.exception.file.InvalidExtensionException;
import com.ruoyi.system.mapper.NewsConfigCenterMapper;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface NewsConfigCenterService {


    /**
     * 通过ID 获取配置信息
     *
     * @param id ID
     * @return 配置信息
     */
    NewsConfigCenter getById(Integer id);


    /**
     * 查询数据
     *
     * @param newsConfigCenter 查询参数
     * @return 数据集合
     */
    List<NewsConfigCenter> listByEntity(NewsConfigCenter newsConfigCenter,List<String> companyCodes);

    /**
     * 插入数据
     *
     * @param newsConfigCenter 数据参数
     * @return 插入数量
     */
    int insert(NewsConfigCenter newsConfigCenter);

    /**
     * 更新数据
     *
     * @param newsConfigCenter 数据参数
     * @return 更新数量
     */
    int update(NewsConfigCenter newsConfigCenter);

    /**
     * 批量更新数据
     *
     * @param list 批量数据参数
     * @return 批量更新数量
     */
    int updateBatch(List<NewsConfigCenter> list);

    /**
     * 通过 ID 删除数据
     *
     * @param id ID
     * @return 删除数量
     */
    int deleteById(Integer id);

    /**
     * 通过 ID 集合删除数据
     *
     * @param list ID集合
     * @return 删除数量
     */
    int deleteByIds(List<Integer> list);

    /**
     * 通过公司编码找到待发布数据
     *
     * @param companyCode 公司编码
     * @return 待发布数据
     */
    List<NewsConfigCenter> listByCompanyCode(String companyCode);

    /**
     * 发布信息
     *
     * @param list   发布数据
     * @param devEnv 开发环境
     * @return 是否发布成功（true  发布成功， false 发布失败）
     */
    boolean releaseInfo(List<NewsConfigCenter> list, String companyCode, String devEnv);


    /**
     * 更新发布状态
     *
     * @param companyCode 公司编码
     * @return 更新数量
     */
    Integer updateStatusByCompanyCode(String companyCode);

    /**
     * 上传图片
     *
     * @param companyCode 公司编码
     * @param file        文件
     * @return 文件名称
     */
    String upImage(String companyCode, MultipartFile file) throws IOException, InvalidExtensionException;

    /**
     * 菜单列表
     *
     * @param code      编码
     * @param type      类型
     * @param dictTypes 字典类型
     * @return 所有数据
     */
    Map<String, List<SysDictData>> menuList(String code, String type, String[] dictTypes);

    /**
     * 获取新闻菜单树
     *
     * @param str 公司&一级菜单&二级菜单    字典类型编码
     * @return 新闻菜单树
     */
    List<NewInfoTree> treeSelect(String str);

    /**
     * 查询总数
     * @param center 实体
     * @param companyList 公司集合
     * @return 总数
     */
    Long countByEntityAndCompanycodes(NewsConfigCenter center, List<String> companyList);
}
