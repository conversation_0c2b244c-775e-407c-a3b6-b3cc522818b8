package com.ruoyi.system.service;

import com.ruoyi.system.domain.DDataSql;

import java.util.List;

/**
 * 外部系统平台数据查询sql配置Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface IDDataSqlService 
{
    /**
     * 查询外部系统平台数据查询sql配置
     * 
     * @param id 外部系统平台数据查询sql配置主键
     * @return 外部系统平台数据查询sql配置
     */
    public DDataSql selectDDataSqlById(Long id);

    /**
     * 查询外部系统平台数据查询sql配置列表
     * 
     * @param dDataSql 外部系统平台数据查询sql配置
     * @return 外部系统平台数据查询sql配置集合
     */
    public List<DDataSql> selectDDataSqlList(DDataSql dDataSql);

    /**
     * 新增外部系统平台数据查询sql配置
     * 
     * @param dDataSql 外部系统平台数据查询sql配置
     * @return 结果
     */
    public int insertDDataSql(DDataSql dDataSql);

    /**
     * 修改外部系统平台数据查询sql配置
     * 
     * @param dDataSql 外部系统平台数据查询sql配置
     * @return 结果
     */
    public int updateDDataSql(DDataSql dDataSql,String operName);

    /**
     * 批量删除外部系统平台数据查询sql配置
     * 
     * @param ids 需要删除的外部系统平台数据查询sql配置主键集合
     * @return 结果
     */
    public int deleteDDataSqlByIds(Long[] ids);

    /**
     * 删除外部系统平台数据查询sql配置信息
     * 
     * @param id 外部系统平台数据查询sql配置主键
     * @return 结果
     */
    public int deleteDDataSqlById(Long id);

    /**
     * 导入外部数据系统查询配置数据
     * @param mappingsList 导入集合
     * @param updateSupport 是否覆盖
     * @param operName  导入人
     * @return
     */
    String importDDataSql(List<DDataSql> mappingsList, boolean updateSupport, String operName);
}
