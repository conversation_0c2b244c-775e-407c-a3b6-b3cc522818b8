package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.system.domain.SysDictDataMapping;

import java.util.List;
import java.util.Map;

/**
 * 字典数据映射Service接口
 * 
 * <AUTHOR>
 * @date 2022-03-28
 */
public interface ISysDictDataMappingService 
{
    /**
     * 查询字典数据映射
     * 
     * @param dictddmId 字典数据映射主键
     * @return 字典数据映射
     */
    public SysDictDataMapping selectSysDictDataMappingByDictddmId(Long dictddmId);

    /**
     * 查询字典数据映射列表
     * 
     * @param sysDictDataMapping 字典数据映射
     * @return 字典数据映射集合
     */
    public List<SysDictDataMapping> selectSysDictDataMappingList(SysDictDataMapping sysDictDataMapping);

    public List<SysDictData> selectByDictType(SysDictDataMapping sysDictDataMapping);

    /**
     * 新增字典数据映射
     * 
     * @param sysDictDataMapping 字典数据映射
     * @return 结果
     */
    public int insertSysDictDataMapping(SysDictDataMapping sysDictDataMapping);

    /**
     * 修改字典数据映射
     * 
     * @param sysDictDataMapping 字典数据映射
     * @return 结果
     */
    public int updateSysDictDataMapping(SysDictDataMapping sysDictDataMapping,String operName);

    /**
     * 批量删除字典数据映射
     * 
     * @param dictddmIds 需要删除的字典数据映射主键集合
     * @return 结果
     */
    public int deleteSysDictDataMappingByDictddmIds(Long[] dictddmIds);

    /**
     * 删除字典数据映射信息
     * 
     * @param dictddmId 字典数据映射主键
     * @return 结果
     */
    public int deleteSysDictDataMappingByDictddmId(Long dictddmId);


    /**
     * 字典映射批量导入
     * @param mappingsList
     * @param isUpdateSupport
     * @param operName
     * @return
     */
    String importSysDictDataMapping(List<SysDictDataMapping> mappingsList, Boolean isUpdateSupport, String operName);

    /**
     * 添加缓存
     */
    void loadingDictCache();

    /**
     * 清空缓存
     */
    void clearDictCache();

    /**
     * 重置缓存
     */
    void resetDictCache();

    /**
     * 获取下拉框数据
     * @param selectDiceType
     */
    List<Map<String,Object>> getSelectData(String selectDiceType);
}
