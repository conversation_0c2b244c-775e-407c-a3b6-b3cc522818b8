package com.ruoyi.system.mapper;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SysRoleData;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-08-08
 */
public interface SysRoleCustomMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param customId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SysRoleData selectSysRoleCustomByCustomId(Long customId);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sysRoleCustom 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SysRoleData> selectSysRoleCustomList(SysRoleData sysRoleCustom);

    /**
     * 新增【请填写功能名称】
     * 
     * @param sysRoleCustom 【请填写功能名称】
     * @return 结果
     */
    public int insertSysRoleCustom(SysRoleData sysRoleCustom);

    /**
     * 修改【请填写功能名称】
     * 
     * @param sysRoleCustom 【请填写功能名称】
     * @return 结果
     */
    public int updateSysRoleCustom(SysRoleData sysRoleCustom);

    /**
     * 删除【请填写功能名称】
     * 
     * @param customId 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSysRoleCustomByCustomId(Long customId);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param customIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysRoleCustomByCustomIds(Long[] customIds);


    /**
     * 批处理更新
     *
     * @param list 列表
     * @return int
     */
    int updateBatch(List<SysRoleData> list);

    /**
     * 更新批选择性
     *
     * @param list 列表
     * @return int
     */
    int updateBatchSelective(List<SysRoleData> list);

    /**
     * 批量插入
     *
     * @param list 列表
     * @return int
     */
    int batchInsert(@Param("list") List<SysRoleData> list);

    /**
     * 按角色删除id
     *
     * @param roleId 角色id
     * @return int
     */
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户id查询所属角色自定义权限
     *
     * @param userId 用户id
     * @return {@link List}<{@link SysRoleCustom}>
     */
    List<SysRoleData> getUserRole(@Param("userId") Long userId);

    /**
     * 被用户id和参数获取用户角色权限
     *
     * @param userId     用户id
     * @param dictType   dict类型
     * @param dictValues dict类型值
     * @return {@link List}<{@link SysRoleCustom}>
     */
    List<SysRoleData> getUserRoleByUserIdAndparams(@Param("userId") Long userId,@Param("dictType")String dictType,@Param("dictValues")List<String> dictValues);


    /**
     * 通过角色id获得数据
     *
     * @param roleId 角色id
     * @return {@link List}<{@link SysRoleCustom}>
     */
    List<SysRoleData> getDataByRoleId(@Param("roleId") Long roleId);

	public int batchInsertData(@Param("list") List<SysRoleData> tableData);

}
