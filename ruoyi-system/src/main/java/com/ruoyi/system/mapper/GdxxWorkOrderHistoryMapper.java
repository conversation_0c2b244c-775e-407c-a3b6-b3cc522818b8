package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.GdxxWorkOrderHistory;

/**
 * 工单信息历史表Mapper接口
 */
public interface GdxxWorkOrderHistoryMapper {
    /**
     * 查询工单信息历史表
     * 
     * @param id 工单信息历史表主键
     * @return 工单信息历史表
     */
    public GdxxWorkOrderHistory selectGdxxWorkOrderHistoryById(Long id);

    /**
     * 查询工单信息历史表列表
     * 
     * @param gdxxWorkOrderHistory 工单信息历史表
     * @return 工单信息历史表集合
     */
    public List<GdxxWorkOrderHistory> selectGdxxWorkOrderHistoryList(GdxxWorkOrderHistory gdxxWorkOrderHistory);

    /**
     * 新增工单信息历史表
     * 
     * @param gdxxWorkOrderHistory 工单信息历史表
     * @return 结果
     */
    public int insertGdxxWorkOrderHistory(GdxxWorkOrderHistory gdxxWorkOrderHistory);

    /**
     * 修改工单信息历史表
     * 
     * @param gdxxWorkOrderHistory 工单信息历史表
     * @return 结果
     */
    public int updateGdxxWorkOrderHistory(GdxxWorkOrderHistory gdxxWorkOrderHistory);

    /**
     * 删除工单信息历史表
     * 
     * @param id 工单信息历史表主键
     * @return 结果
     */
    public int deleteGdxxWorkOrderHistoryById(Long id);

    /**
     * 批量删除工单信息历史表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGdxxWorkOrderHistoryByIds(Long[] ids);
} 