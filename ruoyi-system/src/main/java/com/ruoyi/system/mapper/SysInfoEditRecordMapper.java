package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysInfoEditRecord;

/**
 * oa编辑记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface SysInfoEditRecordMapper
{
    /**
     * 查询oa编辑记录
     *
     * @param id oa编辑记录主键
     * @return oa编辑记录
     */
    public SysInfoEditRecord selectSysInfoEditRecordById(Long id);

    public SysInfoEditRecord getEditRecord(Long logQueryId);

    /**
     * 查询oa编辑记录列表
     *
     * @param sysInfoEditRecord oa编辑记录
     * @return oa编辑记录集合
     */
    public List<SysInfoEditRecord> selectSysInfoEditRecordList(SysInfoEditRecord sysInfoEditRecord);

    /**
     * 新增oa编辑记录
     *
     * @param sysInfoEditRecord oa编辑记录
     * @return 结果
     */
    public int insertSysInfoEditRecord(SysInfoEditRecord sysInfoEditRecord);

    /**
     * 修改oa编辑记录
     *
     * @param sysInfoEditRecord oa编辑记录
     * @return 结果
     */
    public int updateSysInfoEditRecord(SysInfoEditRecord sysInfoEditRecord);

    /**
     * 删除oa编辑记录
     *
     * @param id oa编辑记录主键
     * @return 结果
     */
    public int deleteSysInfoEditRecordById(Long id);

    /**
     * 批量删除oa编辑记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysInfoEditRecordByIds(Long[] ids);
}
