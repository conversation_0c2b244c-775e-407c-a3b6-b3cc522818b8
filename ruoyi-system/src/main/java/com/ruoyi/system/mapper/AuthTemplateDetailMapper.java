package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AuthTemplateDetail;
import org.apache.ibatis.annotations.Mapper;

/**
 * 权限模板明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
@Mapper
public interface AuthTemplateDetailMapper
{
    /**
     * 查询权限模板明细
     *
     * @param id 权限模板明细主键
     * @return 权限模板明细
     */
    public AuthTemplateDetail selectAuthTemplateDetailById(String id);

    /**
     * 查询权限模板明细列表
     *
     * @param authTemplateDetail 权限模板明细
     * @return 权限模板明细集合
     */
    public List<AuthTemplateDetail> selectAuthTemplateDetailList(AuthTemplateDetail authTemplateDetail);

    /**
     * 新增权限模板明细
     *
     * @param authTemplateDetail 权限模板明细
     * @return 结果
     */
    public int insertAuthTemplateDetail(AuthTemplateDetail authTemplateDetail);

    /**
     * 修改权限模板明细
     *
     * @param authTemplateDetail 权限模板明细
     * @return 结果
     */
    public int updateAuthTemplateDetail(AuthTemplateDetail authTemplateDetail);

    /**
     * 删除权限模板明细
     *
     * @param id 权限模板明细主键
     * @return 结果
     */
    public int deleteAuthTemplateDetailById(String id);

    /**
     * 批量删除权限模板明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAuthTemplateDetailByIds(String[] ids);

    /**
     * 通过主表Id查明细表信息
     * @param id    主表ID
     * @return  明细表信息
     */
    List<AuthTemplateDetail> selectDetailByTemId(Long id);
}
