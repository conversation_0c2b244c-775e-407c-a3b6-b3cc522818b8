package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.SysBadDebtParams;
import com.ruoyi.system.domain.SysBadDebtParamsRef;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-01-17
 */
public interface SysBadDebtParamsMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SysBadDebtParams selectSysBadDebtParamsById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param sysBadDebtParams 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SysBadDebtParams> selectSysBadDebtParamsList( SysBadDebtParams sysBadDebtParams);

    /**
     * 新增【请填写功能名称】
     * 
     * @param sysBadDebtParams 【请填写功能名称】
     * @return 结果
     */
    public int insertSysBadDebtParams(SysBadDebtParams sysBadDebtParams);

    /**
     * 修改【请填写功能名称】
     * 
     * @param sysBadDebtParams 【请填写功能名称】
     * @return 结果
     */
    public int updateSysBadDebtParams(SysBadDebtParams sysBadDebtParams);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSysBadDebtParamsById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysBadDebtParamsByIds(Long[] ids);

    List<SysBadDebtParams> selectBadParamList(@Param("platformNos") List<String> platforms, @Param("custNos") List<String> custNos, @Param("partnerNos") List<String> partnerNos, @Param("fundNos") List<String> fundNos,@Param("productCode") List<String> productCode,@Param("sysBadDebtParams")SysBadDebtParams sysBadDebtParams);

    /**
     * 校验新增的数据是否存在
     *
     * @param sysBadDebtParams 利润测算坏账参数
     * @return 结果
     */
    SysBadDebtParams selectSysBadDebtByPlatformNoAndCustNoAndPartnerNoAndFundNoAndProductNo(SysBadDebtParams sysBadDebtParams);

    int deleteSysBadDebtParamsRefByBadDebtId(Long badDebtId);

    int insertSysBadDebtParamsRef(SysBadDebtParamsRef sysBadDebtParamsRef);

    List<SysBadDebtParamsRef> selectSysBadDebtParamsRefByBadDebtId(Long badDebtId);

    List<SysBadDebtParamsRef> selectAllSysBadDebtParamsRef();

    List<String> selectProductCodeList( @Param("platformNos") List<String> platformNos
            , @Param("custNos") List<String> custNos
            , @Param("partnerNos") List<String> partnerNos
            , @Param("fundNos") List<String> fundNos);
}
