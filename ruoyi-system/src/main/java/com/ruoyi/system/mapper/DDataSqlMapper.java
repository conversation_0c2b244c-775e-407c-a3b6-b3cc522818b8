package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.DDataSql;

import java.util.List;

/**
 * 外部系统平台数据查询sql配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-03-31
 */
public interface DDataSqlMapper 
{
    /**
     * 查询外部系统平台数据查询sql配置
     * 
     * @param id 外部系统平台数据查询sql配置主键
     * @return 外部系统平台数据查询sql配置
     */
    public DDataSql selectDDataSqlById(Long id);

    /**
     * 查询外部系统平台数据查询sql配置列表
     * 
     * @param dDataSql 外部系统平台数据查询sql配置
     * @return 外部系统平台数据查询sql配置集合
     */
    public List<DDataSql> selectDDataSqlList(DDataSql dDataSql);

    /**
     * 新增外部系统平台数据查询sql配置
     * 
     * @param dDataSql 外部系统平台数据查询sql配置
     * @return 结果
     */
    public int insertDDataSql(DDataSql dDataSql);

    /**
     * 修改外部系统平台数据查询sql配置
     * 
     * @param dDataSql 外部系统平台数据查询sql配置
     * @return 结果
     */
    public int updateDDataSql(DDataSql dDataSql);

    /**
     * 删除外部系统平台数据查询sql配置
     * 
     * @param id 外部系统平台数据查询sql配置主键
     * @return 结果
     */
    public int deleteDDataSqlById(Long id);

    /**
     * 批量删除外部系统平台数据查询sql配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDDataSqlByIds(Long[] ids);

    /**
     * 查询有没有特定条件的数据
     * @param sqlcode
     * @return
     */
    DDataSql selectDDataSqlByAllCondition(String sqlcode);
}
