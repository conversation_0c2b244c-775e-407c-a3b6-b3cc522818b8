package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.DDataSql;
import com.ruoyi.system.domain.DDataSqlManualTask;

import java.util.List;
import java.util.Map;

/**
 * 手动触发数据查询Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-04-07
 */
public interface DDataSqlManualTaskMapper 
{
    /**
     * 查询手动触发数据查询
     * 
     * @param id 手动触发数据查询主键
     * @return 手动触发数据查询
     */
    public DDataSqlManualTask selectDDataSqlManualTaskById(Long id);

    /**
     * 查询手动触发数据查询列表
     * 
     * @param dDataSqlManualTask 手动触发数据查询
     * @return 手动触发数据查询集合
     */
    public List<DDataSqlManualTask> selectDDataSqlManualTaskList(DDataSqlManualTask dDataSqlManualTask);

    /**
     * 新增手动触发数据查询
     * 
     * @param dDataSqlManualTask 手动触发数据查询
     * @return 结果
     */
    public int insertDDataSqlManualTask(DDataSqlManualTask dDataSqlManualTask);

    /**
     * 修改手动触发数据查询
     * 
     * @param dDataSqlManualTask 手动触发数据查询
     * @return 结果
     */
    public int updateDDataSqlManualTask(DDataSqlManualTask dDataSqlManualTask);

    /**
     * 删除手动触发数据查询
     * 
     * @param id 手动触发数据查询主键
     * @return 结果
     */
    public int deleteDDataSqlManualTaskById(Long id);

    /**
     * 批量删除手动触发数据查询
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDDataSqlManualTaskByIds(Long[] ids);

    /**
     * 查询符合执行条件的任务
     * @return
     */
    List<DDataSqlManualTask> queryDataList();


    /**
     * 选择测试
     * 查询需要执行的sql
     *
     * @param sqlid sqlid
     * @return {@link DDataSql}
     */
    DDataSql selectTest(String sqlid);


    /**
     * 得到平台备注
     * 查询所属平台的访问IP以及端口
     *
     * @param platformNo 平台编码
     * @return {@link String}
     */
    String getPlatformRemark(String platformNo);


    /**
     * 得到所有数据sql id
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> getAllDataSqlId();
}
