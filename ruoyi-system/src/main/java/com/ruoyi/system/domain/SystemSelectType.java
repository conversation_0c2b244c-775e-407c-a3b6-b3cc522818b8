package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.system.domain.SystemSelectTypeComtype;
import java.util.List;

/**
 * 【请填写功能名称】对象 system_select_type
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class SystemSelectType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 下拉框名称 */
    @Excel(name = "下拉框名称")
    private String selectName;

    /** 模块编码 */
    @Excel(name = "模块编码")
    private String modelCode;

    /** 下拉框编码 */
    @Excel(name = "下拉框编码")
    private String selectCode;



    /** 记录表修改前json */

    private String oldDataJson;

    /** 记录表修改后json */

    private String newDataJson;


    private List<SystemSelectTypeComtype> comtypes;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSelectName(String selectName) 
    {
        this.selectName = selectName;
    }

    public String getSelectName() 
    {
        return selectName;
    }
    public void setModelCode(String modelCode) 
    {
        this.modelCode = modelCode;
    }

    public String getModelCode() 
    {
        return modelCode;
    }
    public void setSelectCode(String selectCode) 
    {
        this.selectCode = selectCode;
    }

    public String getSelectCode() 
    {
        return selectCode;
    }

    public List<SystemSelectTypeComtype> getComtypes() {
        return comtypes;
    }

    public void setComtypes(List<SystemSelectTypeComtype> comtypes) {
        this.comtypes = comtypes;
    }

    public String getOldDataJson() {
        return oldDataJson;
    }

    public void setOldDataJson(String oldDataJson) {
        this.oldDataJson = oldDataJson;
    }

    public String getNewDataJson() {
        return newDataJson;
    }

    public void setNewDataJson(String newDataJson) {
        this.newDataJson = newDataJson;
    }

    @Override
    public String toString() {
        return "SystemSelectType{" +
                "id=" + id +
                ", selectName='" + selectName + '\'' +
                ", modelCode='" + modelCode + '\'' +
                ", selectCode='" + selectCode + '\'' +
                ", oldDataJson='" + oldDataJson + '\'' +
                ", newDataJson='" + newDataJson + '\'' +
                ", comtypes=" + comtypes +
                '}';
    }
}
