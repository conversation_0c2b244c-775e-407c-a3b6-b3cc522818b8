package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 工单信息动态表对象 gdxx_work_order_dynamic
 */
@Data
public class GdxxWorkOrderDynamic extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 工单信息主表主键 */
    @Excel(name = "工单信息主表主键")
    private Long workOrderMainId;

    /** 动态类型字典id（人员发布动态、修改工单动态） */
    @Excel(name = "动态类型字典id")
    private String dynamicType;

    /** 动态内容 */
    @Excel(name = "动态内容")
    private String dynamicContent;

    /** 工单信息历史表主键 */
    @Excel(name = "工单信息历史表主键")
    private Long wordOrderHistoryId;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private Date creationTime;

    /** 修改时间 */
    @Excel(name = "修改时间")
    private Date modificationTime;

    /** 修改人 */
    @Excel(name = "修改人")
    private String modifier;
} 