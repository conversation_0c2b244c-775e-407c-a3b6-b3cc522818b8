package com.ruoyi.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 权限模板明细对象 auth_template_detail
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
@Data
public class AuthTemplateDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 权限模板表ID */
    @Excel(name = "权限模板表ID")
    private Long templateId;

    /** 模块类型(枚举) */
    @Excel(name = "模块类型(枚举)")
    private String moduleType;

    /** 权限规则(枚举) */
    @Excel(name = "权限规则(枚举)")
    private String permissionRule;

    /** 生效状态：1 删除 0 生效 */
    @Excel(name = "生效状态：1 删除 0 生效")
    private String status;

    /** 创建人所在公司 */
    @Excel(name = "创建人所在公司")
    private Long createUnit;

    /** 修改人所在公司 */
    @Excel(name = "修改人所在公司")
    private Long updateUnit;

    /**
     * 用于校验是否拥有模块权限
     */
    private String checkFlag;
}
