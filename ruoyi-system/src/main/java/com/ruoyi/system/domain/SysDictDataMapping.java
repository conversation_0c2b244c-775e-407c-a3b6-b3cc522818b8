package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 字典数据映射对象 sys_dict_data_mapping
 * 
 * <AUTHOR>
 * @date 2022-04-28
 */
public class SysDictDataMapping extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long dictddmId;

    /** 字典表id */
    private Long dictdataId;

    /** 外部系统平台编码 */
    @Excel(name = "外部系统平台编码")
    private String platformNo;

    /** 字典类型 */
    @Excel(name = "字典类型")
    private String dictType;

    /** 字典键值 */
    @Excel(name = "字典键值")
    private String dictValue;

    /** 字典键值映射 */
    @Excel(name = "字典键值映射")
    private String dictValueMapping;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createBr;

    /** 修改人 */
    @Excel(name = "修改人")
    private String updateBr;

    public void setDictddmId(Long dictddmId) 
    {
        this.dictddmId = dictddmId;
    }

    public Long getDictddmId() 
    {
        return dictddmId;
    }
    public void setDictdataId(Long dictdataId) 
    {
        this.dictdataId = dictdataId;
    }

    public Long getDictdataId() 
    {
        return dictdataId;
    }
    public void setPlatformNo(String platformNo) 
    {
        this.platformNo = platformNo;
    }

    public String getPlatformNo() 
    {
        return platformNo;
    }
    public void setDictType(String dictType) 
    {
        this.dictType = dictType;
    }

    public String getDictType() 
    {
        return dictType;
    }
    public void setDictValue(String dictValue) 
    {
        this.dictValue = dictValue;
    }

    public String getDictValue() 
    {
        return dictValue;
    }
    public void setDictValueMapping(String dictValueMapping) 
    {
        this.dictValueMapping = dictValueMapping;
    }

    public String getDictValueMapping() 
    {
        return dictValueMapping;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setCreateBr(String createBr) 
    {
        this.createBr = createBr;
    }

    public String getCreateBr() 
    {
        return createBr;
    }
    public void setUpdateBr(String updateBr) 
    {
        this.updateBr = updateBr;
    }

    public String getUpdateBr() 
    {
        return updateBr;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dictddmId", getDictddmId())
            .append("dictdataId", getDictdataId())
            .append("platformNo", getPlatformNo())
            .append("dictType", getDictType())
            .append("dictValue", getDictValue())
            .append("dictValueMapping", getDictValueMapping())
            .append("remark", getRemark())
            .append("status", getStatus())
            .append("createBr", getCreateBr())
            .append("createTime", getCreateTime())
            .append("updateBr", getUpdateBr())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
