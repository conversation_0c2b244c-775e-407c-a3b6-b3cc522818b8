package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.entity.SysRoleData;
import lombok.Data;
import lombok.ToString;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SysRoleCustomVO.java
 * @Description 权限
 * @createTime 2022年08月09日 09:50:00
 */
@Data
@ToString
public class SysRoleCustomVO {
    /** 角色ID */
    private Long roleId;

    /** 角色名称 */
    private String roleName;

    /** 角色权限 */
    private String roleKey;


    /** 数据范围（1：所有数据权限；2：自定义数据权限； */
    private String dataScope;
    /**
     * 自定义权限数组
     */
    private List<SysRoleData> tableData;

    public SysRoleCustomVO(Long roleId, String roleName, String roleKey, String dataScope, List<SysRoleData> tableData) {
        this.roleId = roleId;
        this.roleName = roleName;
        this.roleKey = roleKey;
        this.dataScope = dataScope;
        this.tableData = tableData;
    }

    public List<SysRoleData> getTableData() {
        return tableData;
    }

    public void setTableData(List<SysRoleData> tableData) {
        this.tableData = tableData;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleKey() {
        return roleKey;
    }

    public void setRoleKey(String roleKey) {
        this.roleKey = roleKey;
    }

    public String getDataScope() {
        return dataScope;
    }

    public void setDataScope(String dataScope) {
        this.dataScope = dataScope;
    }



    public SysRoleCustomVO() {
    }


}
