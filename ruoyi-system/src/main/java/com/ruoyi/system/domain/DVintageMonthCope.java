package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 外部系统平台Vintage对象 d_vintage_month
 * 
 * <AUTHOR>
 * @date 2022-04-13
 */
public class DVintageMonthCope extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 外部系统平台编码 */
    @Excel(name = "外部系统平台编码")
    private String platformNo;

    /** 担保公司编码 */
    @Excel(name = "担保公司编码")
    private String custNo;

    /** 合作方编码 */
    @Excel(name = "合作方编码")
    private String partnerNo;

    /** 资金方编码 */
    @Excel(name = "资金方编码")
    private String fundNo;

    /** 产品编码 */
    @Excel(name = "产品编码")
    private String productNo;

    /** 放款月份 */
    @Excel(name = "放款月份")
    private String loanMonth;

    /** 统计月份 */
    @Excel(name = "统计月份")
    private String reconMonth;

    /** 放款金额 */
    @Excel(name = "放款金额")
    private BigDecimal loanAmount;

    /** vintage天数 */
    @Excel(name = "vintage天数")
    private Long vintageDay;

    /** vintage类型 */
    @Excel(name = "vintage类型")
    private String vintageType;

    /** vintage率 */
    @Excel(name = "vintage率")
    private BigDecimal vintageRate;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPlatformNo(String platformNo) 
    {
        this.platformNo = platformNo;
    }

    public String getPlatformNo() 
    {
        return platformNo;
    }
    public void setCustNo(String custNo) 
    {
        this.custNo = custNo;
    }

    public String getCustNo() 
    {
        return custNo;
    }
    public void setPartnerNo(String partnerNo) 
    {
        this.partnerNo = partnerNo;
    }

    public String getPartnerNo() 
    {
        return partnerNo;
    }
    public void setFundNo(String fundNo) 
    {
        this.fundNo = fundNo;
    }

    public String getFundNo() 
    {
        return fundNo;
    }
    public void setProductNo(String productNo) 
    {
        this.productNo = productNo;
    }

    public String getProductNo() 
    {
        return productNo;
    }
    public void setLoanMonth(String loanMonth) 
    {
        this.loanMonth = loanMonth;
    }

    public String getLoanMonth() 
    {
        return loanMonth;
    }
    public void setReconMonth(String reconMonth) 
    {
        this.reconMonth = reconMonth;
    }

    public String getReconMonth() 
    {
        return reconMonth;
    }
    public void setLoanAmount(BigDecimal loanAmount) 
    {
        this.loanAmount = loanAmount;
    }

    public BigDecimal getLoanAmount() 
    {
        return loanAmount;
    }
    public void setVintageDay(Long vintageDay) 
    {
        this.vintageDay = vintageDay;
    }

    public Long getVintageDay() 
    {
        return vintageDay;
    }
    public void setVintageType(String vintageType) 
    {
        this.vintageType = vintageType;
    }

    public String getVintageType() 
    {
        return vintageType;
    }
    public void setVintageRate(BigDecimal vintageRate) 
    {
        this.vintageRate = vintageRate;
    }

    public BigDecimal getVintageRate() 
    {
        return vintageRate;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("platformNo", getPlatformNo())
            .append("custNo", getCustNo())
            .append("partnerNo", getPartnerNo())
            .append("fundNo", getFundNo())
            .append("productNo", getProductNo())
            .append("loanMonth", getLoanMonth())
            .append("reconMonth", getReconMonth())
            .append("loanAmount", getLoanAmount())
            .append("vintageDay", getVintageDay())
            .append("vintageType", getVintageType())
            .append("vintageRate", getVintageRate())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
