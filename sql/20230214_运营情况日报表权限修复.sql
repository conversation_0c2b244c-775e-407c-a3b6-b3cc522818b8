SELECT @sjbbParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='数据报表');
SELECT @yyqkrbbParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='运营情况日报表');
UPDATE `sys_menu` SET `menu_name` = '运营情况日报表', `parent_id` = @sjbbParendId, `order_num` = 7, `path` = 'operation', `component` = 'data/operation/index', `query` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = NULL, `icon` = 'example', `create_by` = 'admin', `create_time` = '2023-01-04 13:43:57', `update_by` = 'admin', `update_time` = '2023-02-14 17:37:25', `remark` = '' WHERE `menu_id` = @yyqkrbbParendId;
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('运营情况日报表查询', @yyqkrbbParendId, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'data:external:list2', '#', 'admin', '2023-02-14 17:37:16', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('运营情况日报表导出', @yyqkrbbParendId, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'data:external:export2', '#', 'admin', '2023-02-03 14:28:04', '', NULL, '');