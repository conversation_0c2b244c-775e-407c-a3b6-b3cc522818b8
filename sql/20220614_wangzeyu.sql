--外部系统数据查询配置 更改字段为非必填
--begin
alter table  d_data_sql modify  sql_basic_query VARCHAR(1000) null COMMENT '基本查询条件';

alter table  d_data_sql modify  sql_additional_query VARCHAR(1000) null COMMENT '额外查询条件';

alter table  d_data_sql modify  mapping_field VARCHAR(1000) NOT null COMMENT '需要映射的字段';
--end
--手动任务查询配置 更改字段为非必填
--  20220614  11:05
--begin
alter table  d_data_sql_manual_task modify  additional_query VARCHAR(1000) null COMMENT '额外查询条件';
--end


-- 菜单名称修改
--  20220614  12:00
--begin
UPDATE sys_menu SET menu_name = '运营情况统计' WHERE menu_id = 2030;

UPDATE sys_menu SET menu_name = 'Vintage统计' WHERE menu_id = 2031;

UPDATE sys_menu SET menu_name = '余额分布统计' WHERE menu_id = 2032;
--end


-- 新增菜单
--  20220617  16:40
--begin
INSERT INTO `sys_menu` VALUES (2039, '运营情况统计堆叠', 2028, 4, 'rundata', 'echart/rundata/index', NULL, 1, 0, 'C', '0', '0', '', 'build', 'admin', '2022-06-17 09:12:49', 'admin', '2022-06-17 09:13:07', '');

---end