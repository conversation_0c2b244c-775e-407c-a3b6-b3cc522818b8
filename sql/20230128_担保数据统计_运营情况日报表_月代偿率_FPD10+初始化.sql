/*
初始化菜单语句 - start
*/
SELECT @parendId := (SELECT menu_id FROM sys_menu WHERE menu_name='ECharts');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('月代偿率', @parendId, 5, 'monCompen', 'echart/monCompen/index', NULL, 1, 0, 'C', '0', '0', '', 'druid', 'admin', '2022-12-27 14:18:58', 'admin', '2022-12-29 14:23:49', '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('FPD10+', @parendId, 2, 'overdue', 'echart/overdue/index', NULL, 1, 0, 'C', '0', '0', '', 'row', 'admin', '2023-01-03 14:37:25', 'admin', '2023-01-12 13:54:40', '');

SELECT @sjbbParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='数据报表');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('担保数据统计', @sjbbParendId, 6, 'guadata', 'data/guadata/index', NULL, 1, 0, 'C', '0', '0', '', 'example', 'admin', '2022-12-16 16:54:41', 'admin', '2022-12-16 16:59:41', '');

INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('运营情况日报表', @sjbbParendId, 7, 'operation', 'data/operation/index', NULL, 1, 0, 'C', '0', '0', '', 'example', 'admin', '2023-01-04 13:43:57', 'admin', '2023-01-10 10:30:16', '');
/*
初始化菜单语句 - end
*/