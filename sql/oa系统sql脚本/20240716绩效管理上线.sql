#drop  table  kq_ask_leave;
create table kq_ask_leave
(
    id                bigint auto_increment comment '主键'
        primary key,
    ask_leave_code    varchar(32)  null comment '请假编号',
    reason            varchar(600) null comment '原因',
    process_id        varchar(40)  null comment '流程id',
    state             char         null comment '状态 (未提交，审核中，审核通过，审核不通过)',
    is_delete         char         null comment '逻辑删除(0.是 1.否)',
    handover          varchar(255) null comment '工作交接人',
    lag_reason        varchar(255) null comment '滞后原因',
    void_reason       varchar(255) null comment '废弃原因',
    void_time        datetime         null comment '废弃时间',
    total_time         double       null comment '总计时长',
    application_time datetime null comment '申请时间',
    effective         char default '0' null comment '生效状态 0 生效  1 废弃',
    remark            varchar(255) null comment '备注',
    create_by         varchar(255) not null comment '创建人',
    create_time       datetime     not null comment '创建时间',
    update_by         varchar(255) null comment '修改人',
    update_time       datetime     null comment '修改时间'
)
    comment '请假表' collate = utf8mb4_general_ci
                     row_format = DYNAMIC;

#drop table kq_ask_leave_slave;
create table kq_ask_leave_slave
(
    id  bigint auto_increment comment '主键'
            primary key,
    leave_id   bigint null  comment '关联主表id',
    leave_type        char(3)      null comment '假种',
    start_time        date     null comment '开始时间',
    start_time_period time         null comment '开始时间段',
    end_time          date     null comment '结束时间',
    end_time_period   time         null comment '结束时间段',
    times             double       null comment '总计时长',
    create_by          varchar(64)                        null comment '创建者',
    create_time        datetime default CURRENT_TIMESTAMP  null comment '创建时间',
    update_by          varchar(64)                        null comment '更新者',
    update_time        datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '请假从表' collate = utf8mb4_general_ci
                     row_format = DYNAMIC;

create table kq_day_log
(
    id          bigint auto_increment comment '主键'
        primary key,
    log_date    date             null comment '日志日期',
    is_all_day  char             null comment '是否全天',
    start_time  time             null comment '开始时间',
    end_time    time             null comment '结束时间',
    log_content varchar(600)     null comment '日志内容',
    log_status  char             null comment '日志状态(1.草稿 2.保存并提交)',
    is_delete   char default '1' null comment '是否删除状态(0 是 1 否)',
    remark      varchar(255)     null comment '备注',
    create_by   varchar(255)     not null comment '创建人',
    create_time datetime         not null comment '创建时间',
    update_by   varchar(255)     null comment '修改人',
    update_time datetime         null comment '修改时间'
)
    comment '用户日报表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

# create table kq_holiday
# (
#     id           bigint auto_increment comment '主键'
#         primary key,
#     holiday_date date         null comment '日期',
#     dateExplain  varchar(10)  null comment '日期说明',
#     holiday      char         null comment '是否节假日(1.节假日 2.调休) ',
#     remark       varchar(255) null comment '备注',
#     create_by    varchar(255) not null comment '创建人',
#     create_time  datetime     not null comment '创建时间',
#     update_by    varchar(255) null comment '修改人',
#     update_time  datetime     null comment '修改时间'
# )
#     comment '假期表' collate = utf8mb4_general_ci
#                      row_format = DYNAMIC;


create table kq_calendar
(
    id   bigint auto_increment comment '主键' primary key,
    date date         null comment '日期',
    weekday varchar(10) null comment '星期',
    dateExplain  varchar(10)  null comment '日期说明',
    holiday      char         null comment '是否节假日(1.节假日 2.工作日) ',
    remark       varchar(255) null comment '备注',
    create_by    varchar(255) not null comment '创建人',
    create_time  datetime     not null comment '创建时间',
    update_by    varchar(255) null comment '修改人',
    update_time  datetime     null comment '修改时间'
)
    comment '考勤日历表' collate = utf8mb4_general_ci
                     row_format = DYNAMIC;

create table kq_month_log_main
(
    id               bigint auto_increment comment '主键'
        primary key,
    log_date         date         null comment '日志月份',
    auditing_status  char         null comment '评审状态',
    check_score      tinyint      null comment '考核分数',
    review_score     tinyint      null comment '复核分数',
    reporting_status char         null comment '填报状态',
    review_status    char         null comment '复核状态(1.未复核2.已复核)',
    submit_time      datetime     null comment '提交时间',
    auditing_time    datetime     null comment '审核时间',
    remark           varchar(255) null comment '备注',
    create_by        varchar(255) not null comment '创建人',
    create_time      datetime     not null comment '创建时间',
    update_by        varchar(255) null comment '修改人',
    update_time      datetime     null comment '修改时间'
)
    comment '月报主表' collate = utf8mb4_general_ci
                       row_format = DYNAMIC;

# create table kq_month_log_notify
# (
#     id            bigint auto_increment comment '主键'
#         primary key,
#     notify_module varchar(50)  null comment '通知模块',
#     url           varchar(50)  null comment '月份条件',
#     notify_type   char         null comment '通知类型 0通知 1待办',
#     notify_msg    varchar(500) null comment '通知内容',
#     dispose_user  bigint       null comment '待处理人id',
#     view_flag     char         null comment '阅读状态 0未阅 1已阅',
#     status        char         null comment '状态 0正常 1禁用',
#     remind_text   varchar(600) null comment '提醒正文',
#     month         varchar(10)  null comment '月份',
#     create_by     varchar(64)  null comment '创建者',
#     create_Time   datetime     null comment '创建时间',
#     update_by     varchar(64)  null comment '更新人',
#     update_Time   datetime     null on update CURRENT_TIMESTAMP comment '更新时间'
# )
#     comment '月报提醒表' collate = utf8mb4_general_ci
#                          row_format = DYNAMIC;

# drop table kq_month_log_notify;


create table kq_month_log_slave
(
    id           bigint auto_increment comment '主键'
        primary key,
    main_id      bigint        null comment '主表id',
    job_role     char          null comment '工作角色(1.主要负责2.协助负责)',
    work_content varchar(2000) null comment '工作内容',
    complete     tinyint       null comment '完成情况',
    is_delete    char          null comment '是否删除( 0.是1.否)',
    remark       varchar(255)  null comment '备注',
    create_by    varchar(255)  not null comment '创建人',
    create_time  datetime      not null comment '创建时间',
    update_by    varchar(255)  null comment '修改人',
    update_time  datetime      null comment '修改时间'
)
    comment '月报从表' collate = utf8mb4_general_ci
                       row_format = DYNAMIC;

#drop  table kq_work_overtime;
create table kq_work_overtime
(
    id          bigint auto_increment comment '主键'
        primary key,
    work_code   varchar(32)  null comment '加班编号',
    total_time       double       null comment '总计时长',
    content     varchar(600) null comment '工作内容',
    process_id  varchar(40)  null comment '流程id',
    state       char         null comment '状态 (未提交，审核中，审核通过，审核不通过)',
    is_delete   char         null comment '逻辑删除(0.是 1.否)',
    effective char(1) DEFAULT '0' null comment '生效状态 0 生效  1 废弃',
    application_time datetime null comment '申请时间',
    void_reason varchar(200) null comment '废弃原因',
    void_time        datetime         null comment '废弃时间',
    create_by   varchar(255) not null comment '创建人',
    create_time datetime     not null comment '创建时间',
    update_by   varchar(255) null comment '修改人',
    update_time datetime     null comment '修改时间'
)
    comment '加班申请表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

#drop table kq_work_overtime_slave;
create table kq_work_overtime_slave
(
    id  bigint auto_increment comment '主键'
            primary key,
    overtime_id   bigint null  comment '关联主表id',
    start_time        date     null comment '开始时间',
    start_time_period time         null comment '开始时间段',
    end_time          date     null comment '结束时间',
    end_time_period   time         null comment '结束时间段',
    times             double       null comment '总计时长',
    create_by          varchar(64)                        null comment '创建者',
    create_time        datetime default CURRENT_TIMESTAMP  null comment '创建时间',
    update_by          varchar(64)                        null comment '更新者',
    update_time        datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '加班从表' collate = utf8mb4_general_ci
                     row_format = DYNAMIC;

create table kq_notify
(
    id            bigint auto_increment comment '主键'
        primary key,
    notify_module varchar(50)  null comment '通知模块',
    url           varchar(50)  null comment '相关url',
    notify_type   char         null comment '通知类型 0通知 1待办',
    notify_msg    varchar(500) null comment '通知内容',
    dispose_user  bigint       null comment '待处理人id',
    view_flag     char         null comment '阅读状态 0未阅 1已阅',
    status        char         null comment '状态 0正常 1禁用',
    remind_text   varchar(600) null comment '提醒正文',
    correlation_id   bigint  null comment '关联id',
    create_by     varchar(64)  null comment '创建者',
    create_Time   datetime     null comment '创建时间',
    update_by     varchar(64)  null comment '更新人',
    update_Time   datetime     null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '考勤相关提醒表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

# drop table kq_void_handle;
create table kq_void_handle
(
    id            bigint auto_increment comment '主键' primary key,
    type varchar(50)  null comment '通知模块 1.请假 2.加班',
    correlation_id   bigint  null comment '关联id',
    list_state varchar(10) null comment '状态 1.未处理 2.已处理',
    handle_state  varchar(10) null comment '审核状态 1.同意 2.拒绝',
    refuse_reason varchar(500) null comment '拒绝原因',
    create_by_id     Long  null comment '创建者id',
    create_by          varchar(64)                        null comment '创建者',
    create_time        datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by          varchar(64)                        null comment '更新者',
    update_time        datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '废弃考勤处理表' collate = utf8mb4_general_ci
                         row_format = DYNAMIC;

ALTER TABLE rs_personnel_archives
    Add COLUMN local_leader bigint comment '所在公司上级';

ALTER TABLE rs_personnel_onboarding
    Add COLUMN local_leader bigint comment '所在公司上级';

ALTER TABLE kq_month_log_main
    Add COLUMN local_score tinyint comment '所在公司上级评分';


# delete from sys_menu where sys_menu.menu_name = '考勤管理';
# delete from sys_menu where sys_menu.menu_name = '日志填报';
# delete from sys_menu where sys_menu.menu_name = '月报填报';
# delete from sys_menu where sys_menu.menu_name = '月报评审';
# delete from sys_menu where sys_menu.menu_name = '请假申请';
# delete from sys_menu where sys_menu.menu_name = '加班申请';
# delete from sys_menu where sys_menu.menu_name = '日志查询';


INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)VALUES('绩效管理', 0, 17, 'checkWork', null, null, 1, 0, 'M', '0', '0', '', 'skill', 'admin', '2024-03-08 09:20:39', 'admin', '2024-07-03 09:42:14', '');
SELECT @kaoqin := LAST_INSERT_ID();
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('日志填报', @kaoqin, 1, 'log', 'checkWork/log/index', null, 1, 1, 'C', '0', '0', '', 'edit', 'admin', '2024-03-08 09:24:06', 'admin', '2024-07-08 09:43:54', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('月报填报', @kaoqin, 4, 'monthlyReport', 'checkWork/monthlyReport/index', null, 1, 1, 'C', '0', '0', '', 'edit', 'admin', '2024-03-12 09:08:13', 'admin', '2024-07-08 09:44:06', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('月报评审', @kaoqin, 5, 'review', 'checkWork/review/index', null, 1, 1, 'C', '0', '0', '', 'edit', 'admin', '2024-03-13 09:13:02', 'admin', '2024-07-08 09:44:14', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('请假申请', @kaoqin, 2, 'leave', 'checkWork/leave/index', null, 1, 1, 'C', '0', '0', '', 'edit', 'admin', '2024-03-13 17:13:00', 'test10', '2024-03-19 10:01:05', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('加班申请', @kaoqin, 3, 'overTime', 'checkWork/overTime/index', null, 1, 1, 'C', '0', '0', '', 'edit', 'admin', '2024-03-14 17:13:26', 'test10', '2024-03-19 10:01:01', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('日志查询', @kaoqin, 0, 'logSearch', 'checkWork/logSearch/index', null, 1, 1, 'C', '0', '0', '', 'search', 'admin', '2024-06-20 17:07:32', 'admin', '2024-07-08 09:43:49', '');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('处理作废申请', @kaoqin, 6, 'processingVoids', 'checkWork/processingVoids/index', null, 1, 0, 'C', '0', '0', '', 'edit', 'admin', '2024-07-04 14:14:13', 'admin', '2024-07-04 14:15:28', '');

SELECT @sys := (select menu_id from sys_menu where menu_name = '系统管理');
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES
('工作日管理', 1, @sys, 'workdayManage', 'system/workdayManage/index', null, 1, 0, 'C', '0', '0', '', 'job', 'admin', '2024-07-02 14:36:12', 'admin', '2024-07-02 14:36:36', '');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES ('绩效管理-交接人是否必填功能', 'achievements.handover.days', '2', 'Y', 'admin', '2024-07-03 09:44:34', 'admin', '2024-07-03 09:45:33', '请假管理配置请假天数超过2天时交接人需要必填');


INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES ('工作角色', 'check_work_role', '0', 'admin', '2024-03-12 09:58:52', '', null, '考勤管理工作角色');
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES ('假种', 'check_work_pseudotype', '0', 'admin', '2024-03-14 10:11:31', 'admin', '2024-03-14 10:11:59', '考勤管理请假假种');
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES ('审批状态', 'check_work_approve_status', '0', 'admin', '2024-03-14 10:15:52', 'admin', '2024-03-14 10:16:05', '考勤管理请假审批状态');


INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '主要负责', '1', 'check_work_role', null, 'default', 'N', '0', 'admin', '2024-03-12 09:59:44', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '协助负责', '2', 'check_work_role', null, 'default', 'N', '0', 'admin', '2024-03-12 09:59:54', '', null, null);

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '事假', '1', 'check_work_pseudotype', null, 'default', 'N', '0', 'admin', '2024-03-14 10:12:14', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '病假', '2', 'check_work_pseudotype', null, 'default', 'N', '0', 'admin', '2024-03-14 10:12:20', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '年假', '3', 'check_work_pseudotype', null, 'default', 'N', '0', 'admin', '2024-03-14 10:12:26', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '婚假', '4', 'check_work_pseudotype', null, 'default', 'N', '0', 'test10', '2024-03-18 11:09:25', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '丧假', '5', 'check_work_pseudotype', null, 'default', 'N', '0', 'test10', '2024-03-18 11:09:35', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '产假', '6', 'check_work_pseudotype', null, 'default', 'N', '0', 'test10', '2024-03-18 11:09:43', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '探亲假', '7', 'check_work_pseudotype', null, 'default', 'N', '0', 'test10', '2024-03-18 11:09:53', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '调休假', '8', 'check_work_pseudotype', null, 'default', 'N', '0', 'test10', '2024-03-18 11:10:02', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '陪产假', '9', 'check_work_pseudotype', null, 'default', 'N', '0', 'test10', '2024-03-18 11:10:11', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '育儿假', '10', 'check_work_pseudotype', null, 'default', 'N', '0', 'test10', '2024-03-18 11:10:26', '', null, null);

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '未提交', '1', 'check_work_approve_status', null, 'default', 'N', '0', 'admin', '2024-03-14 11:08:19', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '审核中', '2', 'check_work_approve_status', null, 'default', 'N', '0', 'admin', '2024-03-14 11:08:39', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '审核通过', '3', 'check_work_approve_status', null, 'default', 'N', '0', 'admin', '2024-03-14 11:08:49', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (0, '审核不通过', '4', 'check_work_approve_status', null, 'default', 'N', '0', 'admin', '2024-03-14 11:09:01', '', null, null);

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (11, '请假申请', '11', 'oaModule_type', null, 'default', 'N', '0', 'admin', '2024-03-14 15:12:46', 'admin', '2024-03-14 16:00:09', null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (12, '加班申请', '12', 'oaModule_type', null, 'default', 'N', '0', 'admin', '2024-03-15 10:07:19', '', null, null);
