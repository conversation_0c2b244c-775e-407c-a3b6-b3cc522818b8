
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('OA系统配置', 0, 13, 'oa', NULL, NULL, 1, 0, 'M', '0', '0', '', 'tree-table', 'admin', '2023-06-28 14:23:16', 'admin', '2023-06-28 14:23:39', '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES  ( 'OA办公', 0, 14, 'oaWork', NULL, NULL, 1, 0, 'M', '0', '0', NULL, '#', 'admin', '2023-07-14 15:09:40', '', NULL, '');
INSERT INTO `sys_menu`   (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)  VALUES ( '数据平台原数据首页', 0, 0, 'dataSystem', 'homePage/dataSystem/index', NULL, 1, 0, 'C', '1', '0', '', '#', 'admin', '2023-07-14 16:08:53', 'admin', '2023-07-17 09:25:23', '');



SELECT @oaSystemConfigParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='OA系统配置');
SELECT @oaWorkParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='OA办公');


INSERT INTO `sys_menu`  (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '付款人配置', @oaSystemConfigParendId, 0, 'payment', 'oa/payment/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'admin', '2023-06-28 14:27:58', 'admin', '2023-06-28 14:28:22', '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)   VALUES ( '收款人配置', @oaSystemConfigParendId, 1, 'collection', 'oa/collection/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', '2023-06-28 14:29:06', '', NULL, '');
INSERT INTO `sys_menu`  (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)    VALUES ( '项目名称配置', @oaSystemConfigParendId, 2, 'projectDeploy', 'oa/projectDeploy/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', '2023-06-30 17:10:58', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)   VALUES ( '凭证生成监控', @oaSystemConfigParendId, 3, 'documentMonitor', 'oa/documentMonitor/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'admin', '2023-07-03 17:52:58', 'admin', '2023-07-03 17:53:22', '');
INSERT INTO `sys_menu`  (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)  VALUES ( '项目与流程关联', @oaSystemConfigParendId, 4, 'projectAndFlow', 'oa/projectAndFlow/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', '2023-07-04 11:45:59', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)  VALUES ( '项目流程关联新增', @oaSystemConfigParendId, 5, 'projectAndFlowAdd', 'oa/projectAndFlowAdd/index', NULL, 1, 0, 'C', '1', '0', '', '#', 'admin', '2023-07-06 13:33:48', 'admin', '2023-07-06 13:34:58', '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)   VALUES ( '流程字典配置', @oaSystemConfigParendId, 6, 'dictData', 'oa/dictData/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', '2023-07-11 14:30:11', '', NULL, '');
INSERT INTO `sys_menu`  (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)   VALUES ( '记账凭证规则', @oaSystemConfigParendId, 7, 'tallyVoucher', 'oa/tallyVoucher/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', '2023-07-12 14:15:32', '', NULL, '');
INSERT INTO `sys_menu`  (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)   VALUES ( '记账凭证规则添加', @oaSystemConfigParendId, 8, 'tallyVoucheradd', 'oa/tallyVoucheradd/index', NULL, 1, 0, 'C', '1', '0', '', '#', 'admin', '2023-07-12 15:51:24', 'admin', '2023-08-07 08:58:09', '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)    VALUES ( '流程分类', @oaSystemConfigParendId, 9, 'classification', 'oa/classification/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', '2023-08-07 14:49:59', '', NULL, '');



INSERT INTO `sys_menu`  (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)   VALUES ( 'OA首页', @oaWorkParendId, 1, 'homepage', 'oaWork/homepage/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'admin', '2023-07-14 15:11:46', 'admin', '2023-07-14 15:42:42', '');
INSERT INTO `sys_menu`  (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)   VALUES ( '我的流程', @oaWorkParendId, 2, 'myActivite', 'oaWork/myActivite/index', NULL, 1, 0, 'C', '0', '0', '', '#', 'admin', '2023-07-24 10:38:44', 'admin', '2023-07-24 10:38:56', '');
INSERT INTO `sys_menu`  (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)   VALUES ( '本部门流程', @oaWorkParendId, 3, 'thisDeptFlow', 'oaWork/thisDeptFlow/index', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', '2023-08-03 09:41:29', '', NULL, '');




INSERT INTO `sys_job` (job_name,job_group,invoke_target,cron_expression,misfire_policy,concurrent,status,create_by,create_time,update_by,update_time,remark) VALUES ('获取企业微信用户', 'DEFAULT', 'com.ruoyi.quartz.task.qiyeVX.QiyeVX.getVXUserId', '* * * * * ?', '1', '1', '1', 'admin', '2023-05-23 16:20:42', 'admin', '2023-05-23 16:21:59', '');


INSERT INTO `sys_menu`  (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)  VALUES ( '企业微信', 1, 14, 'vxuser', 'system/qiyeVX/index', NULL, 1, 0, 'C', '0', '0', 'vxuser:user:list', '#', 'admin', '2023-05-23 15:35:41', 'admin', '2023-05-23 15:42:35', '企业微信菜单');
SELECT @qyvxParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='企业微信');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '企业微信查询', @qyvxParendId, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'vxuser:user:query', '#', 'admin', '2023-05-23 15:35:41', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)  VALUES ( '企业微信新增', @qyvxParendId, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'vxuser:user:add', '#', 'admin', '2023-05-23 15:35:41', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)  VALUES ( '企业微信修改', @qyvxParendId, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'vxuser:user:edit', '#', 'admin', '2023-05-23 15:35:41', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)  VALUES ( '企业微信删除', @qyvxParendId, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'vxuser:user:remove', '#', 'admin', '2023-05-23 15:35:41', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)  VALUES ( '企业微信导出', @qyvxParendId, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'vxuser:user:export', '#', 'admin', '2023-05-23 15:35:41', '', NULL, '');








SELECT @oaSystemConfigParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='OA系统配置');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('记账凭证规则详情', @oaSystemConfigParendId, 8, 'tallyVoucherDetail', 'oa/tallyVoucher/detail', NULL, 1, 1, 'C', '1', '0', NULL, '#', 'admin', '2023-11-21 10:29:47', '', NULL, '');
