-- ----------------------------
-- 2022-05-27 19.43
-- d_data_sql 表新增字段
-- ----------------------------
ALTER TABLE d_data_sql ADD COLUMN mapping_field VARCHAR(1000) DEFAULT NULL COMMENT '需要映射的字段' AFTER update_table_flag;

-- ----------------------------
-- 2022-8-15  11：00
-- 利润测算表
-- ----------------------------
DROP TABLE IF EXISTS `d_profit_data`;
CREATE TABLE `d_profit_data`  (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                  `platform_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部系统平台编码',
                                  `partner_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作方编码',
                                  `fund_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金方编码',
                                  `statistical_index` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计指标（1利润 2收入 3总成本 4不良成本 5流量成本 6运营成本）',
                                  `recon_year` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计年份（yyyy）',
                                  `data_january` decimal(17, 2) NULL DEFAULT NULL COMMENT '1月数据',
                                  `data_february` decimal(17, 2) NULL DEFAULT NULL COMMENT '2月数据',
                                  `data_march` decimal(17, 2) NULL DEFAULT NULL COMMENT '3月数据',
                                  `data_april` decimal(17, 2) NULL DEFAULT NULL COMMENT '4月数据',
                                  `data_may` decimal(17, 2) NULL DEFAULT NULL COMMENT '5月数据',
                                  `data_june` decimal(17, 2) NULL DEFAULT NULL COMMENT '6月数据',
                                  `data_july` decimal(17, 2) NULL DEFAULT NULL COMMENT '7月数据',
                                  `data_august` decimal(17, 2) NULL DEFAULT NULL COMMENT '8月数据',
                                  `data_september` decimal(17, 2) NULL DEFAULT NULL COMMENT '9月数据',
                                  `data_october` decimal(17, 2) NULL DEFAULT NULL COMMENT '10月数据',
                                  `data_november` decimal(17, 2) NULL DEFAULT NULL COMMENT '11月数据',
                                  `data_december` decimal(17, 2) NULL DEFAULT NULL COMMENT '12月数据',
                                  `year_total` decimal(17, 2) NULL DEFAULT NULL COMMENT '年度总和',
                                  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
                                  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  UNIQUE INDEX `index_unique_code`(`platform_no`, `partner_no`, `fund_no`, `statistical_index`, `recon_year`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- 自定义权限数据表
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_custom`;
CREATE TABLE `sys_role_custom`  (
                                    `custom_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '自定义主键id',
                                    `role_id` int(20) NULL DEFAULT NULL COMMENT '角色id',
                                    `platform_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部系统编码',
                                    `cust_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '担保公司编码',
                                    `partner_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作方编码',
                                    `fund_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金方编码',
                                    PRIMARY KEY (`custom_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 43 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- 利润测算参数设置表
-- ----------------------------
DROP TABLE IF EXISTS `d_profit_parameter`;
CREATE TABLE `d_profit_parameter`  (
                                       `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `platform_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部系统编码',
                                       `partner_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作方编码',
                                       `fund_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金方编码',
                                       `effective_date` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生效时间（yyyy-MM）',
                                       `expiry_date` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '失效时间（yyyy-MM）',
                                       `compensatory_rate` decimal(17, 2) NOT NULL COMMENT '代偿率（%）',
                                       `flow_cost` decimal(17, 2) NOT NULL COMMENT '流量费（元/笔）',
                                       `operation_rate` decimal(17, 2) NOT NULL COMMENT '运营成本比率（%）',
                                       `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                       `create_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                       `create_br` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                       `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                       `update_br` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- 新增菜单表数据
-- ----------------------------
INSERT INTO `sys_menu` VALUES (2040, '利润测算', 2007, 4, 'profit', 'data/profit/index', NULL, 1, 0, 'C', '0', '0', 'system:profit:index', 'build', 'admin', '2022-07-18 14:30:07', 'admin', '2022-08-11 16:07:08', '');
INSERT INTO `sys_menu` VALUES (2041, '利润测算统计', 2028, 5, 'profitCal', 'echart/profitCal/index', NULL, 1, 0, 'C', '0', '0', '', 'date-range', 'admin', '2022-07-20 09:22:19', 'admin', '2022-07-21 15:06:25', '');
INSERT INTO `sys_menu` VALUES (2042, '利润测算参数设置', 2007, 5, 'parameter', 'data/parameter/index', NULL, 1, 0, 'C', '0', '0', '', 'switch', 'admin', '2022-07-25 15:14:33', 'admin', '2022-07-25 15:14:50', '');
INSERT INTO `sys_menu` VALUES (2048, '利润测算查询', 2040, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:profit:list', '#', 'admin', '2022-08-11 15:57:11', 'admin', '2022-08-11 16:02:31', '');
INSERT INTO `sys_menu` VALUES (2049, '利润测算新增', 2040, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:profit:add', '#', 'admin', '2022-08-11 15:57:11', 'admin', '2022-08-11 16:02:44', '');
INSERT INTO `sys_menu` VALUES (2050, '利润测算修改', 2040, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:profit:edit', '#', 'admin', '2022-08-11 15:57:11', 'admin', '2022-08-11 16:03:02', '');
INSERT INTO `sys_menu` VALUES (2051, '利润测算删除', 2040, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:profit:remove', '#', 'admin', '2022-08-11 15:57:11', 'admin', '2022-08-11 16:03:23', '');
INSERT INTO `sys_menu` VALUES (2052, '利润测算导出', 2040, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:profit:export', '#', 'admin', '2022-08-11 15:57:11', 'admin', '2022-08-11 16:03:37', '');

-- ----------------------------
-- 新增定时任务表数据
-- ----------------------------
INSERT INTO `sys_job` VALUES (8, '计算上月利润数据', 'DEFAULT', 'profitDataTask.calculateProfitDataTask', '* * * * * ?', '1', '1', '1', 'admin', '2022-07-27 10:30:04', 'admin', '2022-08-02 17:12:29', '');

-- ----------------------------
-- 字典级联表新增字段
-- 2022/08/19 09：20
-- ----------------------------
ALTER TABLE sys_dict_data_ref ADD COLUMN dict_all_code VARCHAR(300) DEFAULT NULL COMMENT '字典全编码由系统_担保公司_合作方_资金方_产品构成，确保数据唯一' AFTER status;
-- ----------------------------
-- 用户表新增字段
-- 2022/08/30 10：55
-- ----------------------------
ALTER TABLE sys_user ADD COLUMN cust_no VARCHAR(100) DEFAULT NULL COMMENT '所属担保公司' AFTER password;

-- ----------------------------
-- 利润测算存量数据计算定时任务
-- 2022/08/31  14：56
-- ----------------------------
INSERT INTO `sys_job` VALUES (9, '计算利润存量数据', 'DEFAULT', 'profitDataTask.calculateProfitDataTaskAllData', '* * * * * ?', '1', '1', '1', 'admin', '2022-08-31 14:13:58', 'admin', '2022-08-31 14:14:20', '');




-- ----------------------------
-- 系统问题修复更新
-- 2022/10/21  15：56
-- ----------------------------
-- ----------------------------
-- 菜单表
-- ----------------------------

INSERT INTO `sys_menu` VALUES (2053, '字典映射查询', 1062, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:mapping:query', '#', 'admin', '2022-10-21 09:16:23', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2054, '字典映射新增', 1062, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:mapping:add', '#', 'admin', '2022-10-21 09:16:44', 'admin', '2022-10-21 09:17:22', '');
INSERT INTO `sys_menu` VALUES (2055, '字典映射修改', 1062, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:mapping:edit', '#', 'admin', '2022-10-21 09:17:04', 'admin', '2022-10-21 09:17:35', '');
INSERT INTO `sys_menu` VALUES (2056, '字典映射删除', 1062, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:mapping:remove', '#', 'admin', '2022-10-21 09:17:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2057, '字典映射导出', 1062, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:mapping:export', '#', 'admin', '2022-10-21 09:18:39', 'admin', '2022-10-21 09:19:05', '');
UPDATE sys_menu SET perms = 'system:dict:list' WHERE menu_id = '1061';
UPDATE sys_menu SET perms = 'system:mapping:list' WHERE menu_id = '1062';

-- ----------------------------
-- sql配置表数据数据优化
-- ----------------------------

ALTER TABLE d_data_sql MODIFY `sql_from` text NOT NULL COMMENT 'sql查询表';

UPDATE d_data_sql SET `sql_code` = 'init_每日统计-通道', `platform_no` = 'XJ', `cust_no` = NULL, `partner_no` = NULL, `fund_no` = NULL, `product_no` = NULL, `sql_field` = 't1.cust_no, t1.partner_no, t1.fund_no, t1.product_no, t1.recon_date, t1.total_begin_date, t1.total_end_date, t1.add_count, t1.add_amount, t1.add_act_guarantee_amount, t1.total_balance_amount, t1.total_fund_balance_amt, t1.total_repay_print_amount, t1.add_begin_date, t1.add_end_date, t1.total_compensate_print_amount, t2.total_count, t2.total_amount, t2.total_act_guarantee_amount', `sql_from` = '(SELECT pa.guarantee_code AS cust_no,pap.asset_code AS partner_no,pap.fund_code AS fund_no,pap.product_no AS product_no,stld.date_stat AS recon_date,DATE_FORMAT( pa.begin_time, \'%Y-%m-%d\' ) AS total_begin_date,stld.date_stat AS total_end_date,stld.loan_cnt AS add_count,stld.loan_amt AS add_amount,stld.loan_guarantee_amt AS add_act_guarantee_amount,(stld.fund_balance_amt+stld.compensate_prin_amt) AS total_balance_amount,stld.fund_balance_amt AS total_fund_balance_amt,stld.repay_prin_amt AS total_repay_print_amount,stld.date_stat AS add_begin_date,stld.date_stat AS add_end_date,stld.compensate_prin_amt AS total_compensate_print_amount, stld.update_time\r\nFROM sts_td_loan_day stld \r\nINNER JOIN pt_asset_product pap ON stld.product_no = pap.product_no \r\nINNER JOIN pt_asset_partner pa ON pap.asset_code = pa.asset_code) t1\r\nINNER JOIN (SELECT stld1.product_no AS product_no, stld1.date_stat AS recon_date, sum(stld2.loan_cnt) AS total_count, sum(stld2.loan_amt) AS total_amount, sum(stld2.loan_guarantee_amt) AS total_act_guarantee_amount\r\n            FROM (SELECT product_no, date_stat FROM sts_td_loan_day GROUP BY product_no,date_stat) stld1\r\n						INNER JOIN (SELECT product_no, date_stat, loan_cnt, loan_amt, loan_guarantee_amt FROM sts_td_loan_day) stld2 ON stld1.product_no=stld2.product_no AND stld1.date_stat >=stld2.date_stat GROUP BY stld1.product_no,stld1.date_stat) t2 \r\nON t1.product_no = t2.product_no and t1.recon_date = t2.recon_date', `sql_basic_query` = 'AND t1.cust_no !=\'\' AND t1.partner_no !=\'\' AND t1.fund_no !=\'\' AND t1.product_no !=\'\' AND t1.cust_no IS NOT NULL AND t1.partner_no IS NOT NULL AND t1.fund_no IS NOT NULL AND t1.product_no IS NOT NULL', `sql_additional_query` = NULL, `update_table` = 'd_data', `update_table_flag` = '3', `mapping_field` = 'cust_no?cust_no,partner_no?partner_no,fund_no?fund_no,product_no?product_no', `add_updata_field` = 'total_count=VALUES(total_count),\ntotal_amount=VALUES(total_amount),\ntotal_repay_print_amount=VALUES(total_repay_print_amount),\ntotal_balance_amount=VALUES(total_balance_amount),\ntotal_plan_balance_amt=VALUES(total_plan_balance_amt),\ntotal_fund_balance_amt=VALUES(total_fund_balance_amt),\ntotal_plan_fund_balance_amt=VALUES(total_plan_fund_balance_amt),\ntotal_compensate_print_amount=VALUES(total_compensate_print_amount),\ntotal_act_guarantee_amount=VALUES(total_act_guarantee_amount),\nadd_count=VALUES(add_count),\nadd_amount=VALUES(add_amount),\nadd_act_guarantee_amount=VALUES(add_act_guarantee_amount)', `before_task` = NULL, `ing_task` = NULL, `after_task` = NULL, `external_system_interface` = 'S2m/query', `request_method` = 'Post', `remark` = NULL, `task_status` = '0', `status` = '0', `create_by` = 'admin', `create_time` = '2022-06-14 14:15:08', `update_by` = 'admin', `update_time` = '2022-09-20 17:40:33' WHERE `id` = 5;
UPDATE d_data_sql SET `sql_code` = 'day_每日统计-通道', `platform_no` = 'XJ', `cust_no` = NULL, `partner_no` = NULL, `fund_no` = NULL, `product_no` = NULL, `sql_field` = 't1.cust_no, t1.partner_no, t1.fund_no, t1.product_no, t1.recon_date, t1.total_begin_date, t1.total_end_date, t1.add_count, t1.add_amount, t1.add_act_guarantee_amount, t1.total_balance_amount, t1.total_fund_balance_amt, t1.total_repay_print_amount, t1.add_begin_date, t1.add_end_date, t1.total_compensate_print_amount, t2.total_count, t2.total_amount, t2.total_act_guarantee_amount', `sql_from` = '(SELECT pa.guarantee_code AS cust_no,pap.asset_code AS partner_no,pap.fund_code AS fund_no,pap.product_no AS product_no,stld.date_stat AS recon_date,DATE_FORMAT( pa.begin_time, \'%Y-%m-%d\' ) AS total_begin_date,stld.date_stat AS total_end_date,stld.loan_cnt AS add_count,stld.loan_amt AS add_amount,stld.loan_guarantee_amt AS add_act_guarantee_amount,(stld.fund_balance_amt+stld.compensate_prin_amt) AS total_balance_amount,stld.fund_balance_amt AS total_fund_balance_amt,stld.repay_prin_amt AS total_repay_print_amount,stld.date_stat AS add_begin_date,stld.date_stat AS add_end_date,stld.compensate_prin_amt AS total_compensate_print_amount, stld.update_time\r\nFROM sts_td_loan_day stld \r\nINNER JOIN pt_asset_product pap ON stld.product_no = pap.product_no \r\nINNER JOIN pt_asset_partner pa ON pap.asset_code = pa.asset_code) t1\r\nINNER JOIN (SELECT stld1.product_no AS product_no, stld1.date_stat AS recon_date, sum(stld2.loan_cnt) AS total_count, sum(stld2.loan_amt) AS total_amount, sum(stld2.loan_guarantee_amt) AS total_act_guarantee_amount\r\n            FROM (SELECT product_no, date_stat FROM sts_td_loan_day GROUP BY product_no,date_stat) stld1\r\n						INNER JOIN (SELECT product_no, date_stat, loan_cnt, loan_amt, loan_guarantee_amt FROM sts_td_loan_day) stld2 ON stld1.product_no=stld2.product_no AND stld1.date_stat >=stld2.date_stat GROUP BY stld1.product_no,stld1.date_stat) t2 \r\nON t1.product_no = t2.product_no and t1.recon_date = t2.recon_date', `sql_basic_query` = 'AND t1.cust_no !=\'\' AND t1.partner_no !=\'\' AND t1.fund_no !=\'\' AND t1.product_no !=\'\' AND t1.cust_no IS NOT NULL AND t1.partner_no IS NOT NULL AND t1.fund_no IS NOT NULL AND t1.product_no IS NOT NULL and CONCAT(DATE_FORMAT(DATE_ADD(NOW(),INTERVAL -1 DAY),\'%Y-%m-%d\'),\' 00:00:00\') <= t1.update_time', `sql_additional_query` = NULL, `update_table` = 'd_data', `update_table_flag` = '3', `mapping_field` = 'cust_no?cust_no,partner_no?partner_no,fund_no?fund_no,product_no?product_no', `add_updata_field` = 'total_count=VALUES(total_count),\ntotal_amount=VALUES(total_amount),\ntotal_repay_print_amount=VALUES(total_repay_print_amount),\ntotal_balance_amount=VALUES(total_balance_amount),\ntotal_plan_balance_amt=VALUES(total_plan_balance_amt),\ntotal_fund_balance_amt=VALUES(total_fund_balance_amt),\ntotal_plan_fund_balance_amt=VALUES(total_plan_fund_balance_amt),\ntotal_compensate_print_amount=VALUES(total_compensate_print_amount),\ntotal_act_guarantee_amount=VALUES(total_act_guarantee_amount),\nadd_count=VALUES(add_count),\nadd_amount=VALUES(add_amount),\nadd_act_guarantee_amount=VALUES(add_act_guarantee_amount)', `before_task` = NULL, `ing_task` = NULL, `after_task` = NULL, `external_system_interface` = 'S2m/query', `request_method` = 'Post', `remark` = NULL, `task_status` = '0', `status` = '0', `create_by` = 'admin', `create_time` = '2022-06-14 14:15:08', `update_by` = 'admin', `update_time` = '2022-09-20 17:57:06' WHERE `id` = 10;

-- ----------------------------
-- 坏账参数设置新增字段
-- ----------------------------

ALTER TABLE sys_bad_debt_params ADD COLUMN status VARCHAR(1) NOT NULL DEFAULT  '0' COMMENT '状态（0正常，1不正常）' AFTER bad_debt_rate;