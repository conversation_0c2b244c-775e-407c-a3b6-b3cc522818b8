package com.ruoyi.quartz.task.PayrollFileTask;

import com.ruoyi.common.utils.spring.SpringUtils;
import org.ruoyi.core.payrollFile.domain.PayrollFileRecord;
import org.ruoyi.core.payrollFile.service.impl.PayrollFileRecordServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
public class PayrollFileTask {
    private static final Logger log = LoggerFactory.getLogger(PayrollFileTask.class);

    //定时任务调用此方法，修改薪资档案
    public void updatePayrollFile(){
        PayrollFileRecordServiceImpl iPayrollFileRecordService = SpringUtils.getBean(PayrollFileRecordServiceImpl.class);
        //查询所有薪资档案审批通过数据，获取最新的数据进行修改
        List<PayrollFileRecord> updateDate = iPayrollFileRecordService.selectUpdateDate();
        for (int i = 0; i < updateDate.size(); i++) {
            iPayrollFileRecordService.updateDateById(updateDate.get(i));
        }
    }
}
