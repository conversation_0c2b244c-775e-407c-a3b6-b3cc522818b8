package com.ruoyi.nocode.service.impl.busCont;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.nocode.domain.busCont.*;
import com.ruoyi.nocode.mapper.BusinessControlMapper;
import com.ruoyi.nocode.mapper.BusinessOperateMapper;
import com.ruoyi.nocode.service.busCont.BusinessOperateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Description：业务管控日志操作记录实现类
 * CreateTime：2024/11/25
 * Author：yu-qiang
 */
@Service
public class BusinessOperateServiceImpl implements BusinessOperateService {

    @Autowired
    private BusinessOperateMapper businessOperateMapper;

    @Autowired
    private BusinessControlMapper businessControlMapper;


    @Override
    public void addBusinessOperateLog(RuleDetailRequestDto dto,String operateType) {
        if(StringUtils.isNotEmpty(operateType)) {
            BusinessOperateLogInfo logInfo = new BusinessOperateLogInfo();
            logInfo.setProjectRuleId(dto.getProjectRuleId());
            logInfo.setOperateType(operateType);
            String userName;
            try {
                userName = SecurityUtils.getUsername();
            } catch (Exception e) {
                userName = "9999999";
            }
            logInfo.setCreateBy(userName);
            logInfo.setCreateTime(DateUtils.getNowDate());
            Map<String, Object> recordsDataMap = new LinkedHashMap<>();
            String recordsData;
            if ("1".equals(operateType)) {
                recordsDataMap.put("oldProjectName", dto.getOldProjectName());
                recordsDataMap.put("importType", dto.getImportType());
                recordsDataMap.put("loanDateDetailList", dto.getLoanDateDetailList());
                recordsDataMap.put("loanAmount", null);
            }else{
                 recordsData = businessOperateMapper.queryLastBusinessOperateLog(dto.getProjectRuleId(), null);
                if (recordsData != null) {
                    recordsDataMap = JSON.parseObject(recordsData, Map.class);
                }
            }
            if ("13".equals(operateType)) {
                recordsDataMap.clear();
                recordsDataMap.put("oldStatus",dto.getOldStatus());
                recordsDataMap.put("importedDataTypeOld", dto.getImportedDataTypeOld());
                recordsDataMap.put("importTypeOld", dto.getImportTypeOld());
                recordsDataMap.put("loanDateDetailListOld", dto.getLoanDateDetailListOld());
                recordsDataMap.put("importTypeNew", dto.getImportType());
                recordsDataMap.put("loanDateDetailListNew", dto.getLoanDateDetailList());
                recordsDataMap.put("importedDataType", dto.getImportedDataType());
            }
            ControlProjectRuleBo ruleBo = businessControlMapper.queryControlProjectRuleById(dto.getProjectRuleId());
            switch (operateType) {
                case "1":
                    break;
                case "2":
                case "3":
                    recordsDataMap.put("loanAmount", ruleBo.getPredictLoanAmount());
                    break;
                case "4":
                    recordsDataMap.put("loanDateDetailList", dto.getLoanDateDetailList());
                    recordsDataMap.put("loanAmount", ruleBo.getPredictLoanAmount());
                    break;
                default:
                    recordsDataMap.put("loanAmount", ruleBo.getLoanAmount());
            }
            recordsData = JSON.toJSONString(recordsDataMap);
            logInfo.setRecordsData(recordsData);
            businessOperateMapper.addBusinessOperateLog(logInfo);
        }
    }

    @Override
    public List<BusinessOperateLogInfo> queryBusinessOperateLog(Long projectRuleId) {
        List<BusinessOperateLogInfo> resultList = businessOperateMapper.queryBusinessOperateLog(projectRuleId);
        resultList.forEach(row ->{
            row.setRecordsDataInfo(JSON.parseObject(row.getRecordsData(), RecordsDataInfo.class));
            row.setRecordsData(null);
        });
        return resultList;
    }

    @Override
    public String queryLastBusinessOperateLog(Long projectRuleId, String operateType) {
        return businessOperateMapper.queryLastBusinessOperateLog(projectRuleId,operateType);
    }
}
