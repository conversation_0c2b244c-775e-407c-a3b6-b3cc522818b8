

package com.ruoyi.nocode.service.impl;

import com.github.pagehelper.Page;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.nocode.domain.ProcFormData;
import com.ruoyi.nocode.domain.ProcFormDef;
import com.ruoyi.nocode.domain.dto.ActTaskDTO;
import com.ruoyi.nocode.mapper.ProcFormDataMapper;
import com.ruoyi.nocode.mapper.ProcFormDefMapper;
import com.ruoyi.nocode.service.IActTaskService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruoyi.nocode.constant.FlowConstants.PROC_YES;

@Service
public class ActTaskServiceImpl implements IActTaskService {

    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private ProcFormDefMapper procFormDefMapper;
    @Autowired
    private ProcFormDataMapper procFormDataMapper;
    @Autowired
    private TaskService taskService;

    @Override
    public Page<ActTaskDTO> selectTaskList(PageDomain pageDomain) {
        Page<ActTaskDTO> list = new Page<ActTaskDTO>();
        String userId = SecurityUtils.getUsername();
        String userName = SecurityUtils.getUsername();
        int totalItems = taskService.createTaskQuery().taskCandidateOrAssigned(userName).list().size();
        List<org.activiti.engine.task.Task> tasks = taskService.createTaskQuery().taskCandidateOrAssigned(userName).orderByTaskCreateTime().desc().listPage((pageDomain.getPageNum() - 1) * pageDomain.getPageSize(), pageDomain.getPageSize());
        list.setTotal(totalItems);
        list.setTotal(totalItems);
        if (totalItems != 0) {
            Set<String> processInstanceIdIds = tasks.parallelStream().map(t -> t.getProcessInstanceId()).collect(Collectors.toSet());
            List<ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery().processInstanceIds(processInstanceIdIds).list();
            List<ActTaskDTO> actTaskDTOS = tasks.stream()
                    .map(t -> new ActTaskDTO(t, processInstanceList.parallelStream().filter(pi -> t.getProcessInstanceId().equals(pi.getId())).findAny().get()))
                    .collect(Collectors.toList());

            //获取代办任务对应的表单定义
            actTaskDTOS.stream().forEach(actTaskDTO -> {
                //无用代码，实际获取表单模板并未用到这里的定义，如业务量增加会导致内存溢出
                /*ProcFormDef procFormDef = new ProcFormDef();
                procFormDef.setRefProcKey(actTaskDTO.getDefinitionKey());
                List<ProcFormDef> procFormDefs = procFormDefMapper.selectProcFormDefList(procFormDef);
                if (!CollectionUtils.isEmpty(procFormDefs)) {
                    actTaskDTO.setFormDef(procFormDefs.get(0).getDefination());
                }*/
                Map<String, String> dataMap = procFormDataMapper.selectByBusinessKey(actTaskDTO.getBusinessKey(),SecurityUtils.getUsername());

                //获取公司  流程所属分类 模板名称
            if(null!= dataMap){

                if(dataMap.containsKey("theme")){
                    actTaskDTO.setTheme(dataMap.get("theme"));
                }

                actTaskDTO.setLoginUser(SecurityUtils.getLoginUser().getUser().getNickName());
                actTaskDTO.setCreateBy(dataMap.get("createBy"));
                actTaskDTO.setLastPass(dataMap.get("pass"));

                actTaskDTO.setTemplateName(dataMap.get("templateName"));
                actTaskDTO.setOpcName(dataMap.get("opcName"));
                actTaskDTO.setShortName(dataMap.get("shortName"));
            }
            });
            ProcFormData procFormData = new ProcFormData();
            procFormData.setCreateBy(userId.toString());
            procFormData.setWithProc(PROC_YES);
            procFormData.setStatus("4");
            List<Map<String,Object>> rejectList = procFormDataMapper.selectSPing(procFormData);
            rejectList.forEach(row ->{
                actTaskDTOS.removeIf(e ->e.getBusinessKey().equals(row.get("businessId")));
            });

            list.addAll(actTaskDTOS);

            list.setTotal(list.size());
        }


        return list;
    }
}
