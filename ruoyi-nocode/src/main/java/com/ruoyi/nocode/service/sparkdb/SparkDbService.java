package com.ruoyi.nocode.service.sparkdb;

import com.ruoyi.nocode.domain.busCont.AccountConfigDto;

import java.util.List;
import java.util.Map;

/**
 * Description：数据仓库接口处理
 * CreateTime：2024/11/12
 * Author：yu-qiang
 */
public interface SparkDbService {

    /**
     * 计算预估导入金额
     * @return
     */
    String computePredictLoanAmount();

    /**
     * 每日更新实际在贷金额
     */
    void updateControlLoanAmount();

    /**
     * 每5分钟查询一次手动导入方式放款金额
     */
    void updateManualLoanAmount();

    /**
     *通过业务管控规则ID查询区域放款信息
     * @param projectRuleId
     * @return
     */
    Map<String,Object> queryLoanAmountFormsById(String projectRuleId);

    /**
     * 新增账户配置信息
     * @param accountConfigDto 账户配置信息s
     * @return int
     */
    int insertAccountConfig(AccountConfigDto accountConfigDto);

    /**
     * 查询账户配置信息
     */
    List<AccountConfigDto> queryAccountConfigInfo();
}
