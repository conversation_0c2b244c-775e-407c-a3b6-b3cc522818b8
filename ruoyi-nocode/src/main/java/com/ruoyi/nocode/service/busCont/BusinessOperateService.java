package com.ruoyi.nocode.service.busCont;

import com.ruoyi.nocode.domain.busCont.BusinessOperateLogInfo;
import com.ruoyi.nocode.domain.busCont.RuleDetailRequestDto;

import java.util.List;

/**
 * Description：业务管控规则日志接口
 * CreateTime：2024/11/25
 * Author：yu-qiang
 */
public interface BusinessOperateService {

    /**
     * 业务管控操作新增日志信息
     * @param dto
     * @param operateType
     */
    void addBusinessOperateLog(RuleDetailRequestDto dto, String operateType);

    /**
     * 通过业务管控规则ID查询日志信息
     * @param projectRuleId
     * @return
     */
    List<BusinessOperateLogInfo> queryBusinessOperateLog(Long projectRuleId);


    /**
     * 根据管控规则ID获取最新一条日志信息
     * @param projectRuleId
     * @param operateType
     * @return
     */
    String queryLastBusinessOperateLog(Long projectRuleId,String operateType);
}
