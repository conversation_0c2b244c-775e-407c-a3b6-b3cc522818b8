package com.ruoyi.nocode.controller.busCont;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.nocode.domain.busCont.*;
import com.ruoyi.nocode.service.busCont.BusinessControlService;
import com.ruoyi.nocode.service.busCont.BusinessNotifyService;
import com.ruoyi.nocode.service.busCont.BusinessOperateService;
import com.ruoyi.nocode.service.sparkdb.SparkDbService;
import com.ruoyi.system.service.INewAuthorityService;
import org.ruoyi.core.license.domain.ZzCommonNotify;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * Description：业务管控controller
 * CreateTime：2024/10/14
 * Author：yu-qiang
 */
@RestController
@RequestMapping("/businessControl")
public class BusinessController extends BaseController {

    @Autowired
    private BusinessControlService businessControlService;

    @Autowired
    private BusinessOperateService businessOperateService;

    @Autowired
    private SparkDbService sparkDbService;

    @Autowired
    private BusinessNotifyService businessNotifyService;

    @Autowired
    private INewAuthorityService iNewAuthorityService;

    /**
     * 根据公司类型查询公司基本信息
     * @param companyTypeCodeList
     * @return
     */
    @GetMapping("/queryCompanyInfo")
    public AjaxResult queryCompanyInfo(@RequestParam List<Long> companyTypeCodeList,String companyType){

        return AjaxResult.success(businessControlService.queryCompanyInfo(companyTypeCodeList,companyType));
    }

    /**
     * 根据公司类型查询公司基本信息
     * @param dto
     * @return
     */
    @GetMapping("/queryGuaranteeCompanyDetail")
    public TableDataInfo queryGuaranteeCompanyDetail(GuaranteeCompanyDto dto){
        List<Long> companyIdList = iNewAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(getUserId(), AuthModuleEnum.BUSINESSCONTROLPROJ.getCode());
        if(companyIdList.isEmpty()){
            return getDataTable(new LinkedList<>());
        }
        startPage();
        List<GuaranteeCompanyDetail> resultList = businessControlService.queryGuaranteeCompanyDetail(dto.getCompanyId(), dto.getControlType(),companyIdList,dto.getCompanyTypeCodeList());
        return getDataTable(resultList);
    }

    /**
     * 更新担保公司担保详情
     * @param dto
     * @return
     */
    @PostMapping("/updateGuaranteeCompanyDetail")
    public AjaxResult updateGuaranteeCompanyDetail(@RequestBody GuaranteeCompanyDetail dto){
        return AjaxResult.success(businessControlService.updateGuaranteeCompanyDetail(dto));
    }

    /**
     * 根据业务管控类型查询项目基本信息
     * @param controlType
     * @return
     */
    @GetMapping("/queryNewProject")
    public AjaxResult queryNewProject(String controlType){
        return AjaxResult.success(businessControlService.queryNewProject(controlType));
    }

    /**
     * 查询项目详情信息
     * @param dto
     * @return
     */
    @GetMapping("/queryProjectDetails")
    public TableDataInfo queryProjectDetails(ProjectRequestDto dto){
        List<Long> companyIdList = iNewAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(getUserId(), AuthModuleEnum.BUSINESSCONTROLPROJ.getCode());
        if(companyIdList.isEmpty()){
            return getDataTable(new LinkedList<>());
        }
        dto.setCompanyIdList(companyIdList);
        startPage();
        List<ProjectDetailsBo> list = businessControlService.queryProjectDetails(dto);
        return getDataTable(list);
    }

    /**
     * 新增新项目详细信息
     * @param dto
     * @return
     */
    @PostMapping("/addProjectDetails")
    public AjaxResult addProjectDetails(@RequestBody ProjectDetailsDto dto){
        return AjaxResult.success(businessControlService.addProjectDetails(dto));
    }

    /**
     * 修改新项目详细信息
     * @param dto
     * @return
     */
    @PostMapping("/updateProjectDetails")
    public AjaxResult updateProjectDetails(@RequestBody ProjectUpdateDetailsDto dto){
        if(null == dto.getProjectId()){
            return AjaxResult.error("新项目ID不可为空");
        }
        dto.setIsUpdateLoan(true);
        return AjaxResult.success(businessControlService.updateProjectDetails(dto));
    }

    /**
     * 通过新项目ID删除新项目信息
     * @param projectId
     * @return
     */
    @GetMapping("/deleteProjectDetail")
    public AjaxResult deleteProjectDetail(Long projectId){
        if(null == projectId){
            return AjaxResult.error("新项目ID不可为空");
        }
        return AjaxResult.success(businessControlService.deleteProjectDetails(projectId));
    }

    /**
     * 通过新项目ID查询项目详情信息
     * @param projectId
     * @return
     */
    @GetMapping("/queryProjectDetailsById")
    public AjaxResult queryProjectDetails(Long projectId){
        return AjaxResult.success(businessControlService.queryProjectDetailsById(projectId));
    }

    /**
     *查询业务管控规则
     * @return
     */
    @PostMapping("/queryControlProjectRule")
    public TableDataInfo queryControlProjectRule(@RequestBody ControlProjectRuleDto dto){
        List<Long> companyIdList = iNewAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(getUserId(), AuthModuleEnum.BUSINESSCONTROLPROJ.getCode());
        if(companyIdList.isEmpty()){
            return getDataTable(new LinkedList<>());
        }
        dto.setCompanyIdList(companyIdList);
        startPage();
        List<ControlProjectRuleBo> resultList = businessControlService.queryControlProjectRule(dto);
        return getDataTable(resultList);
    }

    /**
     *查询业务管控规则汇总信息
     * @return
     */
    @PostMapping("/queryControlProjectSummary")
    public AjaxResult queryControlProjectSummary(@RequestBody ControlProjectRuleDto dto){
        Map<String,Object> resultMap= businessControlService.queryControlProjectSummary(dto);
        return AjaxResult.success(resultMap);
    }

    /**
     *给项目添加管控规则
     * @return
     */
    @PostMapping("/addControlProjectRule")
    public AjaxResult addControlProjectRule(@RequestBody RuleDetailRequestDto dto){
        return AjaxResult.success(businessControlService.addControlProjectRule(dto));
    }


    /**
     * 通过业务管控规则ID查看管控规则详细信息
     * @param projectRuleId
     * @return
     */
    @GetMapping("/queryControlProjectRuleById")
    public AjaxResult queryControlProjectRuleById(Long projectRuleId){
        return AjaxResult.success(businessControlService.queryControlProjectRuleById(projectRuleId));
    }

    /**
     *修改项目管控规则
     * @return
     */
    @PostMapping("/updateControlProjectRule")
    public AjaxResult updateControlProjectRule(@RequestBody RuleDetailRequestDto dto) {
        if (StringUtils.isEmpty(dto.getStatus())) {
            if (StringUtils.isNotEmpty(dto.getImportedDataType())) {
                dto.setStatus("11");
                dto.setOperateType("13");
            }
        } else {
            if(dto.getStatus().equals("1")){
                dto.setOperateType("1");
            }
            if(StringUtils.isNotEmpty(dto.getLoadedType()) && (dto.getLoadedType().equals("1"))){
                dto.setOperateType("3");
            }else if(StringUtils.isNotEmpty(dto.getLoadedType()) && (dto.getLoadedType().equals("2"))){
                dto.setOperateType("4");
            }
        }
        dto.setUpdateBy(SecurityUtils.getUsername());
        dto.setUpdateTime(DateUtils.getNowDate());
        return AjaxResult.success(businessControlService.updateControlProjectRule(dto));
    }

    /**
     * 通过担保公司ID获取项目基本信息
     * @param companyId
     * @return
     */
    @GetMapping("/getOldProjectInfo")
    public AjaxResult getOldProjectInfo(Long companyId){
        return AjaxResult.success(businessControlService.getOldProjectInfo(companyId));
    }

    /**
     * 通过担保公司ID老项目ID获取老项目放款信息
     * @param companyId
     * @return
     */
    @GetMapping("/getOldProjectLoanInfo")
    public TableDataInfo getOldProjectLoanInfo(Long companyId,Long projectId){
        return businessControlService.getOldProjectLoanInfo(companyId, projectId);
    }

    /**
     * 通过业务管控规则ID批量查看管控规则详细信息
     * @param projectRuleIdList
     * @return
     */
    @GetMapping("/queryControlProjectRuleByIdList")
    public AjaxResult queryControlProjectRuleByIdList(@RequestParam List<Long> projectRuleIdList){
        List<RuleDetailResponseBo> resultList = new LinkedList<>();
        for (Long projectRuleId:projectRuleIdList) {
            resultList.add(businessControlService.queryControlProjectRuleById(projectRuleId));
        }
        return AjaxResult.success(resultList);
    }

    /**
     * 发起流程时调用
     */
    @PostMapping("/startProd")
    public AjaxResult startProd(@RequestBody ControlRuleProcessRecord controlRuleProcessRecord)
    {
        LoginUser loginUser = getLoginUser();
        return AjaxResult.success(businessControlService.insertBusinessDataRecord(controlRuleProcessRecord,loginUser));
    }

    /**
     * 流程审批中查询列表信息
     * @param processId
     * @return
     */
    @GetMapping("/queryControlRuleProcessRecordInfo")
    public AjaxResult queryBusinessDataRecord(String processId)
    {
        ControlRuleProcessRecord controlRuleProcessRecord = businessControlService.queryBusinessDataRecord(processId);
        return  AjaxResult.success(controlRuleProcessRecord);
    }

    /**
     * 查询模板基本信息
     * @param oaProcessTemplate
     * @return
     */
    @GetMapping("/getTemplateInfo")
    public AjaxResult getTemplateInfo(OaProcessTemplate oaProcessTemplate) {
        return businessControlService.getTemplateInfo(oaProcessTemplate);
    }

    /**
     *通过管控规则ID修改管控规则状态,目前主要用于暂定自动导入
     * @return
     */
    @GetMapping("/updateControlRuleStatusById")
    public AjaxResult updateControlRuleStatusById(RuleDetailRequestDto dto){
        dto.setUpdateBy(SecurityUtils.getUsername());
        dto.setUpdateTime(DateUtils.getNowDate());
        return AjaxResult.success(businessControlService.updateControlProjectRule(dto));
    }

    /**
     *修改管控规则流程记录审批状态
     * @return
     */
    @GetMapping("/updateControlRuleProcessRecord")
    public AjaxResult updateControlRuleProcessRecord(ControlRuleProcessRecord dto){
        return AjaxResult.success(businessControlService.updateControlRuleProcessRecord(dto));
    }

    /**
     *批量设置预警信息
     * @return
     */
    @PostMapping("/batchUpdateWarningRuleInfo")
    public AjaxResult batchUpdateWarningRuleInfo(@RequestBody ProjectUpdateDetailsDto dto){
        return AjaxResult.success(businessControlService.batchUpdateWarningRuleInfo(dto));
    }

    /**
     *查询操作记录
     * @return
     */
    @GetMapping("/queryBusinessOperateLog")
    public TableDataInfo queryBusinessOperateLog(Long projectRuleId){
        List<BusinessOperateLogInfo> resultList = businessOperateService.queryBusinessOperateLog(projectRuleId);
        return getDataTable(resultList);
    }

    /**
     *通过业务管控规则ID获取超限后重新计算规则信息
     * @return
     */
    @GetMapping("/queryRuleDetailOverLimitInfo")
    public AjaxResult queryRuleDetailOverLimitInfo(Long projectRuleId){
        return AjaxResult.success(businessControlService.queryRuleDetailOverLimitInfo(projectRuleId));
    }

    /**
     *通过业务管控规则ID直接删除业务规则信息
     * @return
     */
    @GetMapping("/deleteControlProjectRuleById")
    public AjaxResult deleteControlProjectRuleById(Long projectRuleId){
        return AjaxResult.success(businessControlService.deleteControlProjectRuleById(projectRuleId));
    }

    /**
     *通过业务管控规则ID查询区域分布信息
     * @return
     */
    @GetMapping("/queryLoanAmountById")
    public AjaxResult queryLoanAmountById(String projectRuleId){
        return AjaxResult.success(sparkDbService.queryLoanAmountFormsById(projectRuleId));
    }

    /**
     *通过业务管控规则ID查询区域分布信息
     * @return
     */
    @PostMapping("/downloadLoanAmountById")
    public void downloadLoanAmountById(HttpServletResponse response, String projectRuleId){
        Map<String, Object> resultMap = sparkDbService.queryLoanAmountFormsById(projectRuleId);
        List<LoanAmountInfo> loanAmountInfoList = (List<LoanAmountInfo>) resultMap.get("loanAmountInfoList");
        if(null !=loanAmountInfoList && loanAmountInfoList.size() > 0){
            LoanAmountInfo info = new LoanAmountInfo();
            info.setProvince("合计");
            info.setLoanAmt(BigDecimal.valueOf((Long) resultMap.get("loanAmtTotal")));
            info.setLoanTot(Integer.parseInt(resultMap.get("loanTotTotal").toString()));
            info.setFundPrincBal(BigDecimal.valueOf((Long)resultMap.get("fundPrincBalTotal")));
            loanAmountInfoList.forEach(item -> {
                item.setLoanAmtRatio(item.getLoanAmtRatio().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                item.setLoanTotRatio(item.getLoanTotRatio().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
                item.setFundPrincBalRatio(item.getFundPrincBalRatio().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
            });
            loanAmountInfoList.add(info);
        }
        ExcelUtil<LoanAmountInfo> excelUtil= new ExcelUtil<>(LoanAmountInfo.class);
        excelUtil.exportExcel(response,loanAmountInfoList,"区域分布信息");

    }
    /**
     *修改业务管控规则为已读
     * @return
     */
    @GetMapping("/updateNotifyInfo")
    public AjaxResult updateNotifyInfo(ZzCommonNotify zzCommonNotify){
        return AjaxResult.success(businessNotifyService.updateNotifyInfo(zzCommonNotify));
    }

    /**
     *业务管控规则修改回滚
     * @return
     */
    @GetMapping("/rollBackControlRule")
    public AjaxResult rollBackControlRule(Long  projectRuleId,String operateType){
        return AjaxResult.success(businessControlService.rollBackControlRule(projectRuleId,operateType));
    }

    /**
     * 查询业务保证金修改记录
     * @param projectId
     * @return
     */
    @GetMapping("/queryBusinessMarginEditRecord")
    public AjaxResult queryBusinessMarginEditRecord(Long  projectId){
        if(null == projectId){
            return AjaxResult.error("项目规则ID不能为空");
        }
        return AjaxResult.success(businessControlService.queryBusinessMarginEditRecord(projectId));
    }

    /**
     * 查询账号管理信息
     * @return
     */
    @GetMapping("/queryAccountConfigInfo")
    public AjaxResult queryAccountConfigInfo(){
        return AjaxResult.success(sparkDbService.queryAccountConfigInfo());
    }
}
