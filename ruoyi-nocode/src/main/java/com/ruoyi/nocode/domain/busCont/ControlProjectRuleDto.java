package com.ruoyi.nocode.domain.busCont;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * Description：业务管控规则查询Dto
 * CreateTime：2024/10/23
 * Author：yu-qiang
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ControlProjectRuleDto extends BaseEntity {

    //业务管控类型
    @NotBlank(message = "业务管控类型管控类型不可为空")
    private String controlType;

    //新项目ID
    private Long projectId ;

    //担保公司ID
    private Long guaranteeCompanyId ;

    //资产端公司ID
    private Long assetCompanyId ;

    //资金端公司ID
    private Long fundCompanyId ;

    //担保类型
    private String guaranteeType;

    //是否管控中
    private String isControlFlag;

    //导入方式
    private String importType;

    //是否已装入
    private String loadedFlag;

    //状态
    private String status;

    //前端查询statusList
    private List<String> statusList;

    //权限控制，用户拥有所有担保公司权限的ID
    private List<Long> companyIdList;

    // 任务状态 0-未执行，1-已执行，99-执行中，仅手动导入生效
    private String taskStatus;
}
