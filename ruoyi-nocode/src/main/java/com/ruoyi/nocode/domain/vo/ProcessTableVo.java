package com.ruoyi.nocode.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 流程表格查询实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessTableVo implements Serializable {

    //节点类型：userTask;ExclusiveGateway
    private String nodeType;
    //节点ID
    private String nodesId;

    //节点名称
    private String nodesName;

    //代理人
    private String  assignee;

    //候选人
    private List<String> candidateUsers;

    //候选组
    private List<String> candidateGroups;

    //流入方向
    private List<String> incomingFlows;

    //流出方向
    private List<String> outgoingFlows;

    //流转方式
    private String flowType;

    //流向
    private String outgoing;

}
