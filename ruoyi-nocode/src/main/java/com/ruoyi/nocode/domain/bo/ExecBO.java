package com.ruoyi.nocode.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ExecBO {
    private static final long serialVersionUID = 1L;
    private String taskID;
    private String pass;
    private String comment;
    private String businessKey;
    private String  taskNodeName;
    private String  userName;
//    private List<ApproveFileBO> fileList;

    private String stepId;
    private String nextFlowApproveUserName;
    private String nextNodeId;

    //驳回节点
    private String rejectNodesInfo;

    //驳回后审批方式 1-按顺序流转；2-直接返回我的审批
    private String approvalSequence;

    //转办人
    private String transferUser;

    //加签人
    private String countersignUser;

    //转办流转是否仍然由转办人处理
    private Boolean transferFlag;

    //是否自动审批
    private Boolean automaticSkip;

    //上传文件时产生的随机标识位，因为上传文件在审批记录生成前先生成。
    // 那么就用这个标识作为审批记录的id（两者都是用UUID生成的随机字符。可以这样使用）
    private String soleFlag;

    //表单ID
    private String formID;
    //当前节点是否可编辑
    private Boolean editPayee;

    //审批节点上传的表单数据
    private String data;

    private String businessKeyToTS;

    //可编辑参数List
    private List<String> editPayeeList;

    //是否抄送
    private Boolean copyFlag;

    //抄送人员
    private List<String> copySignUser;

    //企业微信通知类型：1-"待审批";4-驳回，默认1
    private String rejectFlag = "1";

    //会签审批人员
    private List<String> assigneeList;

    //当前审批节点是否最后一个节点 1-是；0-否
    private String lastNodeFlag;
}
