package com.ruoyi.nocode.domain.busCont;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description：业务管控放款起止时间
 * CreateTime：2024/10/24
 * Author：yu-qiang
 */
@Data
public class LoanDateDetail implements Serializable {

    //项目业务规则ID
    private Long projectRuleId;

    //放款开始时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loanBeginDate;

    //放款结束时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loanEndDate;
}
