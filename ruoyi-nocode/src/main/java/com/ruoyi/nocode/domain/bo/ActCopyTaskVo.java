package com.ruoyi.nocode.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description：
 * CreateTime：2024/3/20
 * Author：yu-qiang
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActCopyTaskVo {

    //主键ID
    private long id;

    //业务ID
    private String businessKey;

    //处理用户
    private String userName;

    //执行流ID
    private String executionId;

    //流程实例ID
    private String processInstanceId;

    //流程定义ID
    private String processDefinitionId;

    //任务节点Code
    private String stepId;

    //任务节点名称
    private String taskNodeName;

    //是否已读
    private String readFlag;

    //抄送人
    private String createBy;
}
