package com.ruoyi.nocode.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Description：表单是否已读实体类
 * CreateTime：2024/4/12
 * Author：yu-qiang
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcFormReadRecodeVo implements Serializable {

    /** 主键ID */
    private  int id;

    /** 业务ID */
    private String businessKey;

    /** 处理人 */
    private String detailBy;

    /** 处理类型 1-驳回 2-待我审批 3-待我阅览*/
    private String businessStatus;

    /** 是否已读 F-未读，T-已读 */
    private String readFlag;

    /** 创建时间 */
    private Date createTime;
}
