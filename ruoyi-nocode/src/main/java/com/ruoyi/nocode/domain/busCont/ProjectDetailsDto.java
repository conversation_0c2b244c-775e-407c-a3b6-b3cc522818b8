package com.ruoyi.nocode.domain.busCont;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description：添加修改项目详情接口请求Dto
 * CreateTime：2024/10/18
 * Author：yu-qiang
 */
@Data
public class ProjectDetailsDto extends BaseEntity {

    //新项目id
    private Long projectId;
    //新项目名称
    @NotBlank(message = "新项目名称不可为空")
    private String projectName;

    @NotBlank(message = "业务管控类型不可为空")
    private String controlType;

    //担保公司ID
    @NotBlank(message = "担保公司ID不可为空")
    private Long guaranteeCompanyId ;

    //资产端公司ID
    @NotBlank(message = "资产端公司ID不可为空")
    private Long assetCompanyId ;

    //资金端公司ID
    @NotBlank(message = "资金端公司ID不可为空")
    private Long fundCompanyId ;

    //担保类型
    @NotBlank(message = "担保类型不可为空")
    @Pattern(regexp = "[1-3]",message = "担保类型只能为1、2、3")
    private String guaranteeType;

    //最大在贷限额
    private Double maxLoanLimit;

    //保证金
    private BigDecimal earnestMoney;

    //保证金比例下限
    private Double marginRate;

    //是否已添加业务管控
    private String controlRulesType;

    //是否管控中
    private String isControlFlag;

    //预警参数详情
    List<WarningRuleInfo> warningRuleList;
}
