<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.nocode.mapper.ProcWorkflowFormdataMapper">

    <resultMap type="ProcWorkflowFormdata" id="ProcWorkflowFormdataResult">
        <result property="id"    column="id"    />
        <result property="businessKey"    column="business_key"    />
        <result property="taskNodeName"    column="task_node_name"    />
        <result property="pass"    column="pass"    />
        <result property="comment"    column="comment"    />
        <result property="createName"    column="create_name"    />
        <result property="associationName"    column="association_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="stepId"    column="step_id"    />
    </resultMap>

    <resultMap id="approveNodesInfoMap" type="com.ruoyi.nocode.domain.vo.ApprovedNodesInfo">
        <result property="stepId" column="step_id" jdbcType="VARCHAR" />
        <result property="createBy"  column="create_by"  jdbcType="VARCHAR" />
        <result property="createName"  column="create_name" jdbcType="VARCHAR" />
        <result property="taskNodeName"  column="task_node_name" jdbcType="VARCHAR" />
    </resultMap>


    <sql id="selectProcWorkflowFormdataVo">
        select id, business_key, task_node_name, pass, comment, create_name, association_name, create_by, create_time, update_time, step_id from proc_workflow_formdata
    </sql>

    <select id="selectProcWorkflowFormdataList" parameterType="ProcWorkflowFormdata" resultMap="ProcWorkflowFormdataResult">
        select pwf.id, pwf.business_key, pwf.task_node_name, pwf.pass, pwf.comment, pwf.create_name, pwf.association_name, pwf.create_by, pwf.create_time, pwf.update_time, pwf.step_id, case when su.status='0' then '' when su.status='1' then '(停用)' else '' end as userStatus from proc_workflow_formdata pwf left join sys_user su on pwf.create_by=su.user_name
        <where>
            <if test="businessKey != null  and businessKey != ''"> and pwf.business_key = #{businessKey}</if>
            <if test="taskNodeName != null  and taskNodeName != ''"> and pwf.task_node_name like concat('%', #{taskNodeName}, '%')</if>
            <if test="pass != null  and pass != ''"> and pwf.pass = #{pass}</if>
            <if test="comment != null  and comment != ''"> and pwf.comment = #{comment}</if>
            <if test="createName != null  and createName != ''"> and pwf.create_name like concat('%', #{createName}, '%')</if>
        </where>
        order by pwf.create_time
    </select>

    <select id="selectProcWorkflowFormdataById" parameterType="String" resultMap="ProcWorkflowFormdataResult">
        <include refid="selectProcWorkflowFormdataVo"/>
        where id = #{id}
    </select>

    <insert id="insertProcWorkflowFormdata" parameterType="ProcWorkflowFormdata">
        insert into proc_workflow_formdata
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="businessKey != null">business_key,</if>
            <if test="taskNodeName != null">task_node_name,</if>
            <if test="pass != null">pass,</if>
            <if test="comment != null">comment,</if>
            <if test="createName != null">create_name,</if>
            <if test="associationName != null">association_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="stepId != null and stepId != ''">step_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="businessKey != null">#{businessKey},</if>
            <if test="taskNodeName != null">#{taskNodeName},</if>
            <if test="pass != null">#{pass},</if>
            <if test="comment != null">#{comment},</if>
            <if test="createName != null">#{createName},</if>
            <if test="associationName != null">#{associationName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="stepId != null and stepId != ''">#{stepId,jdbcType=VARCHAR},</if>
         </trim>
    </insert>

    <update id="updateProcWorkflowFormdata" parameterType="ProcWorkflowFormdata">
        update proc_workflow_formdata
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessKey != null">business_key = #{businessKey},</if>
            <if test="taskNodeName != null">task_node_name = #{taskNodeName},</if>
            <if test="pass != null">pass = #{pass},</if>
            <if test="comment != null">comment = #{comment},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="associationName != null">association_name = #{associationName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProcWorkflowFormdataById" parameterType="String">
        delete from proc_workflow_formdata where id = #{id}
    </delete>

    <delete id="deleteProcWorkflowFormdataByIds" parameterType="String">
        delete from proc_workflow_formdata where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectLastByBusinessKey" parameterType="String" resultMap="ProcWorkflowFormdataResult">
        <include refid="selectProcWorkflowFormdataVo"/>
        where
        pass not in ('8','9')
        <if test="businessKey != null  and businessKey != ''">
        and business_key = #{businessKey}
        </if>
        order by create_time desc limit 1
    </select>


    <delete id="deleteByBusinessId" parameterType="String">
        delete from proc_workflow_formdata where business_key = #{id}
    </delete>

    <select id="selectProcWorkflowFormdataFilesListByBusinessId" resultType="com.ruoyi.nocode.domain.dto.FlowFileInfoDTO">
        SELECT id, business_id AS businessId, step_id AS stepId, sole_flag AS soleFlag, file_name AS fileName,url ,upload_user_id AS uploadUserId, create_by AS createBy, create_time AS createTime, update_by AS updateBy, update_time AS updateTime FROM proc_workflow_formdata_files
        WHERE business_id=#{businessId,jdbcType=VARCHAR} ORDER BY create_time
    </select>

    <select id="selectProcWorkflowFormdataFilesList" resultType="com.ruoyi.nocode.domain.dto.FlowFileInfoDTO">
        SELECT id, business_id AS businessId, step_id AS stepId, sole_flag AS soleFlag, file_name AS fileName,url ,upload_user_id AS uploadUserId, create_by AS createBy, create_time AS createTime, update_by AS updateBy, update_time AS updateTime FROM proc_workflow_formdata_files
        WHERE business_id=#{businessId,jdbcType=VARCHAR} AND step_id=#{stepId,jdbcType=VARCHAR} AND commit_status=#{commitStatus,jdbcType=VARCHAR} ORDER BY create_time
    </select>

    <select id="selectApproveNodesInfo" resultMap="approveNodesInfoMap">
        select step_id,
               create_by,
               create_name,
               task_node_name
        from proc_workflow_formdata
        where business_key = #{businessId,jdbcType=VARCHAR}
          and pass = #{pass,jdbcType=VARCHAR}
        order by create_time
    </select>

    <select id="selectProcWorkflowFormdataListByIdList" resultMap="ProcWorkflowFormdataResult">
        <include refid="selectProcWorkflowFormdataVo"/>
        WHERE id
        IN
        <if test="idList != null and idList.size() != 0">
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
</mapper>
