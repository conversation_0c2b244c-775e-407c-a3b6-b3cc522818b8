<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.nocode.mapper.BusinessOperateMapper">
    <resultMap id="BusinessOperateLogMap" type="com.ruoyi.nocode.domain.busCont.BusinessOperateLogInfo">
        <result column="operate_id" property="operateId" />
        <result column="project_rule_id" property="projectRuleId" />
        <result column="operate_type" property="operateType" />
        <result column="records_data" property="recordsData" />
        <result column="create_by" property="createBy" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <insert id="addBusinessOperateLog" parameterType="com.ruoyi.nocode.domain.busCont.BusinessOperateLogInfo">
        insert into business_control_operate_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operateId != null">operate_id,</if>
            <if test="projectRuleId != null">project_rule_id,</if>
            <if test="operateType != null and operateType !=''">operate_type,</if>
            <if test="recordsData != null and recordsData != ''">records_data,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operateId != null">#{operateId},</if>
            <if test="projectRuleId != null">#{projectRuleId},</if>
            <if test="operateType != null and operateType !=''">#{operateType},</if>
            <if test="recordsData != null and recordsData != ''">#{recordsData},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <select id="queryLastBusinessOperateLog" resultType="java.lang.String">
        select records_data from business_control_operate_log
        where project_rule_id = #{projectRuleId}
        <if test="operateType !=null and operateType !=''">
            and operate_type = #{operateType}
        </if>
        order by operate_id desc limit 1
    </select>

    <select id="queryBusinessOperateLog" resultMap="BusinessOperateLogMap">
        SELECT a.operate_id,
               a.project_rule_id,
               a.operate_type,
               a.records_data,
               a.create_by,
               case when a.create_by ='9999999' then '系统'
                   else b.nick_name end as create_name,
               a.create_time
        FROM business_control_operate_log a
                 LEFT JOIN sys_user b ON a.create_by = b.user_name
        where project_rule_id = #{projectRuleId} order by operate_id desc
    </select>
</mapper>