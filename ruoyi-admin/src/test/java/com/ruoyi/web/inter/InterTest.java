package com.ruoyi.web.inter;

import com.ruoyi.common.core.domain.entity.NewsConfigCenter;
import com.ruoyi.system.mapper.NewsConfigCenterMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Author: 左东冉
 * @Create: 2024-04-02 15:39
 * @Description: TODO
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class InterTest {

    @Autowired
    private NewsConfigCenterMapper newsConfigCenterMapper;
    @Test
    public void test1(){
        NewsConfigCenter byId = newsConfigCenterMapper.getById(100);
        System.out.println(byId);
    }
}
