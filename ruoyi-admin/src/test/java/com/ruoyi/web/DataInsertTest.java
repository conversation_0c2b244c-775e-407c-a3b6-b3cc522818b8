package com.ruoyi.web;

import com.ruoyi.common.core.DateNumerationUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.domain.server.Sys;
import org.ruoyi.core.mapper.EChartsMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.ruoyi.core.domain.DData;
import org.ruoyi.core.domain.DVintageMonth;
import org.ruoyi.core.mapper.DDataMapper;
import org.ruoyi.core.mapper.DVintageMonthMapper;
import org.ruoyi.core.xmglproject.constant.XmglProjectEnum;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: 左东冉
 * @Create: 2022-04-14 16:41
 * @Description: 添加测试数据
 **/
@SpringBootTest
@RunWith(SpringRunner.class)

public class DataInsertTest {
    @Resource
    private DDataMapper dDataMapper;

    @Resource
    private DVintageMonthMapper dVintageMonthMapper;

    @Resource
    private EChartsMapper eChartsMapper;

    @Test
    public void insertData() {

        List<DData> listAll = new ArrayList<>();
        List<DData> dData = dDataMapper.queryAll(new DData());
        for (DData dDatum : dData) {
            dDatum.setId(null);
        }
        for (int i = 0; i < 100; i++) {
            listAll.addAll(dData);
        }
//        dDataMapper.insertBatch(listAll);

    }

    @Test
    public void insertVintage() {

        List<DVintageMonth> listAll = new ArrayList<>();
        List<DVintageMonth> dVintageMonthList = dVintageMonthMapper.selectDVintageMonthList(new DVintageMonth());
        for (DVintageMonth dVintageMonth : dVintageMonthList) {
            dVintageMonth.setId(null);
        }
        for (int i = 0; i < 100; i++) {
            listAll.addAll(dVintageMonthList);
        }
        dVintageMonthMapper.insertBatch(listAll);

    }
    @Test
    public void insertBatch() {
        List<DVintageMonth> list = new ArrayList<>();
        for (int i = 0; i < 213; i++) {
            DVintageMonth dVintageMonth = new DVintageMonth();
            dVintageMonth.setPlatformNo("xjsystempt");
            dVintageMonth.setCustNo("dbgs111");
            dVintageMonth.setPartnerNo("hzf1");
            dVintageMonth.setFundNo("zjf1");
            dVintageMonth.setProductNo("cpf1");
            dVintageMonth.setLoanMonth("2022-02");
            dVintageMonth.setReconMonth("2022-02");
            dVintageMonth.setLoanAmount(new BigDecimal("1"));
            dVintageMonth.setVintageDay(1L);
            dVintageMonth.setVintageType("1");
            dVintageMonth.setVintageRate(new BigDecimal("0.1"));
            dVintageMonth.setStatus("0");
            dVintageMonth.setVintageMoleCule(new BigDecimal("0"));
            dVintageMonth.setVintageDenominator(new BigDecimal("0"));
            dVintageMonth.setCreateBy("admin");
            dVintageMonth.setCreateTime(new Date());
            dVintageMonth.setUpdateBy("admin");
            dVintageMonth.setUpdateTime(new Date());
            dVintageMonth.setRemark("测试");
            list.add(dVintageMonth);
        }
        dVintageMonthMapper.insertBatch(list);
    }
    @Test
    public void enumProject() {


    }

}
