package com.ruoyi.web.controller.monitor;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysLogQuery;
import com.ruoyi.system.service.ISysLogQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 日志查询Controller
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/system/logquery")
public class SysLogQueryController extends BaseController
{
    @Autowired
    private ISysLogQueryService sysLogQueryService;

    /**
     * 查询日志查询列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SysLogQuery sysLogQuery)
    {
        startPage();
        List<SysLogQuery> list = sysLogQueryService.selectSysLogQueryList(sysLogQuery);
        return getDataTable(list);
    }

    /**
     * 导出日志查询列表
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLogQuery sysLogQuery)
    {
        List<SysLogQuery> list = sysLogQueryService.selectSysLogQueryList(sysLogQuery);
        ExcelUtil<SysLogQuery> util = new ExcelUtil<SysLogQuery>(SysLogQuery.class);
        util.exportExcel(response, list, "日志查询数据");
    }

    /**
     * 新增日志查询
     */
    @PostMapping
    public AjaxResult add(@RequestBody SysLogQuery sysLogQuery)
    {
        return toAjax(sysLogQueryService.insertSysLogQuery(sysLogQuery));
    }
}