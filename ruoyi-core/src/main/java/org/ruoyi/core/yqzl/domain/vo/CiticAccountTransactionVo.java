package org.ruoyi.core.yqzl.domain.vo;

import lombok.Data;
import org.ruoyi.core.yqzl.domain.CiticAccountTransaction;

import java.math.BigDecimal;


/**
 * 中信银行账户明细信息查询对象 yqzl_citic_account_transaction
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
public class CiticAccountTransactionVo extends CiticAccountTransaction
{
    /**
     * 借方发生额
     */
    private BigDecimal debitAmount;

    /**
     * 贷方发生额
     */
    private BigDecimal creditAmount;

    /**
     * 根据借贷标识自动设置借方和贷方金额
     * D-借方：将交易金额赋值给借方发生额
     * C-贷方：将交易金额赋值给贷方发生额
     */
    public void setCreditDebitAmounts() {
        if (getTranAmount() == null) {
            return;
        }

        // 重置金额字段
        this.debitAmount = null;
        this.creditAmount = null;

        String flag = getCreditDebitFlag();
        if ("D".equals(flag)) {
            // 借方：将交易金额赋值给借方发生额
            this.debitAmount = getTranAmount();
        } else if ("C".equals(flag)) {
            // 贷方：将交易金额赋值给贷方发生额
            this.creditAmount = getTranAmount();
        }
    }

    /**
     * 重写 setCreditDebitFlag 方法，在设置借贷标识时自动计算金额
     */
    @Override
    public void setCreditDebitFlag(String creditDebitFlag) {
        super.setCreditDebitFlag(creditDebitFlag);
        setCreditDebitAmounts();
    }

    /**
     * 重写 setTranAmount 方法，在设置交易金额时自动计算金额
     */
    @Override
    public void setTranAmount(BigDecimal tranAmount) {
        super.setTranAmount(tranAmount);
        setCreditDebitAmounts();
    }
}
