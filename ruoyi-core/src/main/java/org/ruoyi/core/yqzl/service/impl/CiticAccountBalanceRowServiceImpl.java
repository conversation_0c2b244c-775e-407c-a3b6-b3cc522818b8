package org.ruoyi.core.yqzl.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.http.HttpUtils;
import org.ruoyi.core.yqzl.domain.CiticAccountBalanceRow;
import org.ruoyi.core.yqzl.domain.req.CiticAccountBalanceQuery;
import org.ruoyi.core.yqzl.domain.util.CiticXml;
import org.ruoyi.core.yqzl.domain.util.XmlMessageGenerator;
import org.ruoyi.core.yqzl.domain.util.XmlMessageParser;
import org.ruoyi.core.yqzl.domain.vo.CiticAccountBalanceRowVo;
import org.ruoyi.core.yqzl.mapper.CiticAccountBalanceRowMapper;
import org.ruoyi.core.yqzl.service.ICiticAccountBalanceRowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;;

/**
 * 中信银行账户余额查询结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class CiticAccountBalanceRowServiceImpl implements ICiticAccountBalanceRowService
{
    @Autowired
    private CiticAccountBalanceRowMapper citicAccountBalanceRowMapper;
    @Autowired
    private CiticXml citicXml;
    /**
     * 查询中信银行账户余额查询结果
     *
     * @param id 中信银行账户余额查询结果主键
     * @return 中信银行账户余额查询结果
     */
    @Override
    public CiticAccountBalanceRow selectCiticAccountBalanceRowById(Long id)
    {
        return citicAccountBalanceRowMapper.selectCiticAccountBalanceRowById(id);
    }

    /**
     * 查询中信银行账户余额查询结果列表
     *
     * @param citicAccountBalanceRow 中信银行账户余额查询结果
     * @return 中信银行账户余额查询结果
     */
    @Override
    public List<CiticAccountBalanceRow> selectCiticAccountBalanceRowList(CiticAccountBalanceRowVo citicAccountBalanceRow)
    {
        return citicAccountBalanceRowMapper.selectCiticAccountBalanceRowList(citicAccountBalanceRow);
    }

    /**
     * 新增中信银行账户余额查询结果
     *
     * @param citicAccountBalanceRow 中信银行账户余额查询结果
     * @return 结果
     */
    @Override
    public int insertCiticAccountBalanceRow(CiticAccountBalanceRow citicAccountBalanceRow)
    {
        return citicAccountBalanceRowMapper.insertCiticAccountBalanceRow(citicAccountBalanceRow);
    }

    /**
     * 修改中信银行账户余额查询结果
     *
     * @param citicAccountBalanceRow 中信银行账户余额查询结果
     * @return 结果
     */
    @Override
    public int updateCiticAccountBalanceRow(CiticAccountBalanceRow citicAccountBalanceRow)
    {
        return citicAccountBalanceRowMapper.updateCiticAccountBalanceRow(citicAccountBalanceRow);
    }

    /**
     * 批量删除中信银行账户余额查询结果
     *
     * @param ids 需要删除的中信银行账户余额查询结果主键
     * @return 结果
     */
    @Override
    public int deleteCiticAccountBalanceRowByIds(Long[] ids)
    {
        return citicAccountBalanceRowMapper.deleteCiticAccountBalanceRowByIds(ids);
    }

    /**
     * 删除中信银行账户余额查询结果信息
     *
     * @param id 中信银行账户余额查询结果主键
     * @return 结果
     */
    @Override
    public int deleteCiticAccountBalanceRowById(Long id)
    {
        return citicAccountBalanceRowMapper.deleteCiticAccountBalanceRowById(id);
    }
    /**
     * 批量新增中信银行账户余额查询结果
     *
     * @param list 中信银行账户余额查询结果
     * @return 结果
     */
    public int batchInsertCiticAccountBalanceRow(List<CiticAccountBalanceRow> list) {
        return citicAccountBalanceRowMapper.batchInsertCiticAccountBalanceRow(list);
    }

    @Override
    public int GenerationDailyAccountBalanceRow() throws Exception {
        //fixme:暂时写死查询账号
        List<CiticAccountBalanceQuery> query = new ArrayList<>();
        CiticAccountBalanceQuery query1 = new CiticAccountBalanceQuery();
        query1.setAccountNo("8110701014101248723");
        CiticAccountBalanceQuery query2 = new CiticAccountBalanceQuery();
        query2.setAccountNo("8110701014401248724");
        query.add(query1);
        query.add(query2);
//        String reqContent = XmlMessageGenerator.generateXmlMessageList("DLBALQRY", yqzlUserName, query);
//
//        String res  = HttpUtils.sendPost(yqzlIp + "/open/sendActionOfOA", reqContent, Constants.GBK);
//        //转换
//        String xmlMessage = CiticXml.getXmlMessage(res);

        String responseXMl = citicXml.responseXMl("DLTRNALL", query);

        XmlMessageParser.XmlParseResult<CiticAccountBalanceRow> citicAccountBalanceRowXmlParseResult = XmlMessageParser.parseXmlResponse(responseXMl, CiticAccountBalanceRow.class);
        List<CiticAccountBalanceRow> resultList = XmlMessageParser.parseXmlToEntityList(responseXMl,CiticAccountBalanceRow.class);

        return citicAccountBalanceRowMapper.batchInsertCiticAccountBalanceRow(resultList);
    }
}
