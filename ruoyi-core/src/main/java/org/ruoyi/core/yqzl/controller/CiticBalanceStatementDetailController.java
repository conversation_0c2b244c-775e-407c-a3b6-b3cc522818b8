package org.ruoyi.core.yqzl.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.yqzl.domain.CiticBalanceStatementDetail;
import org.ruoyi.core.yqzl.service.ICiticBalanceStatementDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 中信银行余额对账单明细Controller
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("/yqzl/balance/statement/detail")
public class CiticBalanceStatementDetailController extends BaseController
{
    @Autowired
    private ICiticBalanceStatementDetailService citicBalanceStatementDetailService;

    /**
     * 查询中信银行余额对账单明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(CiticBalanceStatementDetail citicBalanceStatementDetail)
    {
        startPage();
        List<CiticBalanceStatementDetail> list = citicBalanceStatementDetailService.selectCiticBalanceStatementDetailList(citicBalanceStatementDetail);
        return getDataTable(list);
    }

    /**
     * 导出中信银行余额对账单明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:export')")
    @Log(title = "中信银行余额对账单明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CiticBalanceStatementDetail citicBalanceStatementDetail)
    {
        List<CiticBalanceStatementDetail> list = citicBalanceStatementDetailService.selectCiticBalanceStatementDetailList(citicBalanceStatementDetail);
        ExcelUtil<CiticBalanceStatementDetail> util = new ExcelUtil<CiticBalanceStatementDetail>(CiticBalanceStatementDetail.class);
        util.exportExcel(response, list, "中信银行余额对账单明细数据");
    }

    /**
     * 获取中信银行余额对账单明细详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:query')")
    @GetMapping(value = "/{accountNo}")
    public AjaxResult getInfo(@PathVariable("accountNo") String accountNo)
    {
        return AjaxResult.success(citicBalanceStatementDetailService.selectCiticBalanceStatementDetailByAccountNo(accountNo));
    }

    /**
     * 新增中信银行余额对账单明细
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:add')")
    @Log(title = "中信银行余额对账单明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CiticBalanceStatementDetail citicBalanceStatementDetail)
    {
        return toAjax(citicBalanceStatementDetailService.insertCiticBalanceStatementDetail(citicBalanceStatementDetail));
    }

    /**
     * 修改中信银行余额对账单明细
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:edit')")
    @Log(title = "中信银行余额对账单明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CiticBalanceStatementDetail citicBalanceStatementDetail)
    {
        return toAjax(citicBalanceStatementDetailService.updateCiticBalanceStatementDetail(citicBalanceStatementDetail));
    }

    /**
     * 删除中信银行余额对账单明细
     */
    //@PreAuthorize("@ss.hasPermi('system:detail:remove')")
    @Log(title = "中信银行余额对账单明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{accountNos}")
    public AjaxResult remove(@PathVariable String[] accountNos)
    {
        return toAjax(citicBalanceStatementDetailService.deleteCiticBalanceStatementDetailByAccountNos(accountNos));
    }
}
