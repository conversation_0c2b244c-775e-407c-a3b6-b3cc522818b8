package org.ruoyi.core.yqzl.service;

import org.ruoyi.core.yqzl.domain.CiticAccountTransaction;

import java.util.List;

/**
 * 中信银行账户明细信息查询Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface ICiticAccountTransactionService
{
    /**
     * 查询中信银行账户明细信息查询
     *
     * @param id 中信银行账户明细信息查询主键
     * @return 中信银行账户明细信息查询
     */
    public CiticAccountTransaction selectCiticAccountTransactionById(Long id);

    /**
     * 查询中信银行账户明细信息查询列表
     *
     * @param citicAccountTransaction 中信银行账户明细信息查询
     * @return 中信银行账户明细信息查询集合
     */
    public List<CiticAccountTransaction> selectCiticAccountTransactionList(CiticAccountTransaction citicAccountTransaction);

    /**
     * 新增中信银行账户明细信息查询
     *
     * @param citicAccountTransaction 中信银行账户明细信息查询
     * @return 结果
     */
    public int insertCiticAccountTransaction(CiticAccountTransaction citicAccountTransaction);

    /**
     * 修改中信银行账户明细信息查询
     *
     * @param citicAccountTransaction 中信银行账户明细信息查询
     * @return 结果
     */
    public int updateCiticAccountTransaction(CiticAccountTransaction citicAccountTransaction);

    /**
     * 批量删除中信银行账户明细信息查询
     *
     * @param ids 需要删除的中信银行账户明细信息查询主键集合
     * @return 结果
     */
    public int deleteCiticAccountTransactionByIds(Long[] ids);

    /**
     * 删除中信银行账户明细信息查询信息
     *
     * @param id 中信银行账户明细信息查询主键
     * @return 结果
     */
    public int deleteCiticAccountTransactionById(Long id);

    /**
     * 批量新增中信银行账户明细信息查询
     *
     * @param list 中信银行账户明细信息查询
     * @return 结果
     */
    public int batchInsertCiticAccountTransaction(List<CiticAccountTransaction> list);

    public int GenerationDailyAccountTransaction() throws Exception;
}
