package org.ruoyi.core.yqzl.mapper;

import org.ruoyi.core.yqzl.domain.CiticBalanceStatement;
import org.ruoyi.core.yqzl.domain.vo.CiticBalanceStatementVo;

import java.util.List;

/**
 * 中信银行余额对账单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface CiticBalanceStatementMapper
{
    /**
     * 查询中信银行余额对账单
     *
     * @param billNo 中信银行余额对账单主键
     * @return 中信银行余额对账单
     */
    public CiticBalanceStatement selectCiticBalanceStatementByBillNo(String billNo);

    /**
     * 查询中信银行余额对账单列表
     *
     * @param citicBalanceStatement 中信银行余额对账单
     * @return 中信银行余额对账单集合
     */
    public List<CiticBalanceStatement> selectCiticBalanceStatementList(CiticBalanceStatementVo citicBalanceStatement);

    /**
     * 新增中信银行余额对账单
     *
     * @param citicBalanceStatement 中信银行余额对账单
     * @return 结果
     */
    public int insertCiticBalanceStatement(CiticBalanceStatement citicBalanceStatement);

    /**
     * 修改中信银行余额对账单
     *
     * @param citicBalanceStatement 中信银行余额对账单
     * @return 结果
     */
    public int updateCiticBalanceStatement(CiticBalanceStatement citicBalanceStatement);

    /**
     * 删除中信银行余额对账单
     *
     * @param billNo 中信银行余额对账单主键
     * @return 结果
     */
    public int deleteCiticBalanceStatementByBillNo(String billNo);

    /**
     * 批量删除中信银行余额对账单
     *
     * @param billNos 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCiticBalanceStatementByBillNos(String[] billNos);

    /**
     * 批量新增余额对账单
     * @param list
     * @return
     */
    public int batchInsertCiticBalanceStatement(List<CiticBalanceStatement> list);
}
