package org.ruoyi.core.license.controller;

import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysUnit;
import com.ruoyi.system.domain.SysCompany;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.license.domain.ZzLicenseProcess;
import org.ruoyi.core.license.service.IZzLicenseProcessService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 证照流程Controller
 * 
 * <AUTHOR>
 * @date 2024-01-26
 */
@RestController
@RequestMapping("/licenseProcess/process")
public class ZzLicenseProcessController extends BaseController
{
    @Autowired
    private IZzLicenseProcessService zzLicenseProcessService;

    /**
     * 查询证照流程列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ZzLicenseProcess zzLicenseProcess)
    {
        startPage();
        List<ZzLicenseProcess> list = zzLicenseProcessService.selectZzLicenseProcessList(zzLicenseProcess);
        return getDataTable(list);
    }

    /**
     * 导出证照流程列表
     */
    @Log(title = "证照流程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZzLicenseProcess zzLicenseProcess)
    {
        List<ZzLicenseProcess> list = zzLicenseProcessService.selectZzLicenseProcessList(zzLicenseProcess);
        ExcelUtil<ZzLicenseProcess> util = new ExcelUtil<ZzLicenseProcess>(ZzLicenseProcess.class);
        util.exportExcel(response, list, "证照流程数据");
    }

    /**
     * 获取证照流程详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(zzLicenseProcessService.selectZzLicenseProcessById(id));
    }

    /**
     * 新增证照流程
     * 用户发起借用流程点击提交按钮调用
     */
    @Log(title = "证照流程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZzLicenseProcess zzLicenseProcess)
    {
        return toAjax(zzLicenseProcessService.insertZzLicenseProcess(zzLicenseProcess));
    }

    /**
     * 修改证照流程
     * 证照已收回/不签领/废弃时，修改流程状态
     */
    @Log(title = "证照流程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZzLicenseProcess zzLicenseProcess)
    {
        return toAjax(zzLicenseProcessService.updateZzLicenseProcess(zzLicenseProcess));
    }

    /**
     * 删除证照流程
     */
    @Log(title = "证照流程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zzLicenseProcessService.deleteZzLicenseProcessByIds(ids));
    }

    /**
     * 获取当前用户有oa流程发起权限的公司
     */
    @GetMapping("/getUserUnits")
    public AjaxResult selectUnitListByUserId(){
        List<SysCompany> list = zzLicenseProcessService.selectUserUnits();
        return AjaxResult.success(list);
    }

    /**
     * 删除已驳回流程状态的证照借用流程
     * 如果已驳回状态的流程中含有已签领状态的证照，则不允许删除
     */
    @GetMapping("/deleteRejected/{processId}")
    public AjaxResult deleteRejectedLicense(@PathVariable("processId")String processId){
        return AjaxResult.success(zzLicenseProcessService.deleteRejectedLicense(processId));
    }
}
