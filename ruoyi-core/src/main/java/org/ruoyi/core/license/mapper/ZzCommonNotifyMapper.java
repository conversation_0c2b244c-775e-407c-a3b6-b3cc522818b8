package org.ruoyi.core.license.mapper;

import java.util.List;
import org.ruoyi.core.license.domain.ZzCommonNotify;

/**
 * 证照-代办通知Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-05
 */
public interface ZzCommonNotifyMapper 
{
    /**
     * 查询证照-代办通知
     * 
     * @param id 证照-代办通知主键
     * @return 证照-代办通知
     */
    public ZzCommonNotify selectZzCommonNotifyById(Long id);

    /**
     * 查询证照-代办通知列表
     * 
     * @param zzCommonNotify 证照-代办通知
     * @return 证照-代办通知集合
     */
    public List<ZzCommonNotify> selectZzCommonNotifyList(ZzCommonNotify zzCommonNotify);

    /**
     * 新增证照-代办通知
     * 
     * @param zzCommonNotify 证照-代办通知
     * @return 结果
     */
    public int insertZzCommonNotify(ZzCommonNotify zzCommonNotify);

    /**
     * 修改证照-代办通知
     * 
     * @param zzCommonNotify 证照-代办通知
     * @return 结果
     */
    public int updateZzCommonNotify(ZzCommonNotify zzCommonNotify);

    /**
     * 删除证照-代办通知
     * 
     * @param id 证照-代办通知主键
     * @return 结果
     */
    public int deleteZzCommonNotifyById(Long id);

    /**
     * 批量删除证照-代办通知
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZzCommonNotifyByIds(Long[] ids);

    /**
     * 修改代办为已处理-主要针对业务管控
     *
     * @param zzCommonNotify 证照-代办通知
     * @return 结果
     */
    int updateZzCommonNotifyByLicenseId(ZzCommonNotify zzCommonNotify);
}
