package org.ruoyi.core.license.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 证照有效期预警对象 zz_license_expire_warn
 * 
 * <AUTHOR>
 * @date 2024-02-02
 */
public class ZzLicenseIndateExpireWarn extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 证照id */
    private String licenseId;

    /** 是否开启证照到期预警功能(1是 2否) */
    @Excel(name = "是否开启证照到期预警功能")
    private String isSwitch;

    /** 预警天数 */
    @Excel(name = "预警天数")
    private Long warnDay;

    /** 增加到证照预警列表 (1开启 2关闭) */
    @Excel(name = "增加到证照预警列表")
    private String addWarnLicense;

    /** 推送代办 (1开启 2关闭)*/
    @Excel(name = "推送代办")
    private String pushAck;

    /** 企业微信通知 (1开启 2关闭)*/
    @Excel(name = "企业微信通知")
    private String pushQyvx;

    public void setLicenseId(String licenseId) 
    {
        this.licenseId = licenseId;
    }

    public String getLicenseId() 
    {
        return licenseId;
    }
    public void setIsSwitch(String isSwitch) 
    {
        this.isSwitch = isSwitch;
    }

    public String getIsSwitch() 
    {
        return isSwitch;
    }
    public void setWarnDay(Long warnDay) 
    {
        this.warnDay = warnDay;
    }

    public Long getWarnDay() 
    {
        return warnDay;
    }
    public void setAddWarnLicense(String addWarnLicense) 
    {
        this.addWarnLicense = addWarnLicense;
    }

    public String getAddWarnLicense() 
    {
        return addWarnLicense;
    }
    public void setPushAck(String pushAck) 
    {
        this.pushAck = pushAck;
    }

    public String getPushAck() 
    {
        return pushAck;
    }
    public void setPushQyvx(String pushQyvx) 
    {
        this.pushQyvx = pushQyvx;
    }

    public String getPushQyvx() 
    {
        return pushQyvx;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("licenseId", getLicenseId())
            .append("isSwitch", getIsSwitch())
            .append("warnDay", getWarnDay())
            .append("addWarnLicense", getAddWarnLicense())
            .append("pushAck", getPushAck())
            .append("pushQyvx", getPushQyvx())
            .toString();
    }
}
