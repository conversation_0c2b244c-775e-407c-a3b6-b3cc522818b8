package org.ruoyi.core.kaohe.service.impl;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.kaohe.domain.CheckConfigTemplateSlave;
import org.ruoyi.core.kaohe.mapper.CheckConfigTemplateSlaveMapper;
import org.ruoyi.core.kaohe.service.ICheckConfigTemplateSlaveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 考核配置模版从Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Service
public class CheckConfigTemplateSlaveServiceImpl implements ICheckConfigTemplateSlaveService
{
    @Autowired
    private CheckConfigTemplateSlaveMapper checkConfigTemplateSlaveMapper;

    /**
     * 查询考核配置模版从
     *
     * @param id 考核配置模版从主键
     * @return 考核配置模版从
     */
    @Override
    public CheckConfigTemplateSlave selectCheckConfigTemplateSlaveById(Long id)
    {
        return checkConfigTemplateSlaveMapper.selectCheckConfigTemplateSlaveById(id);
    }

    /**
     * 查询考核配置模版从列表
     *
     * @param checkConfigTemplateSlave 考核配置模版从
     * @return 考核配置模版从
     */
    @Override
    public List<CheckConfigTemplateSlave> selectCheckConfigTemplateSlaveList(CheckConfigTemplateSlave checkConfigTemplateSlave)
    {
        return checkConfigTemplateSlaveMapper.selectCheckConfigTemplateSlaveList(checkConfigTemplateSlave);
    }

    /**
     * 新增考核配置模版从
     *
     * @param checkConfigTemplateSlave 考核配置模版从
     * @return 结果
     */
    @Override
    public int insertCheckConfigTemplateSlave(CheckConfigTemplateSlave checkConfigTemplateSlave)
    {
        checkConfigTemplateSlave.setCreateTime(DateUtils.getNowDate());
        return checkConfigTemplateSlaveMapper.insertCheckConfigTemplateSlave(checkConfigTemplateSlave);
    }

    /**
     * 修改考核配置模版从
     *
     * @param checkConfigTemplateSlave 考核配置模版从
     * @return 结果
     */
    @Override
    public int updateCheckConfigTemplateSlave(CheckConfigTemplateSlave checkConfigTemplateSlave)
    {
        checkConfigTemplateSlave.setUpdateTime(DateUtils.getNowDate());
        return checkConfigTemplateSlaveMapper.updateCheckConfigTemplateSlave(checkConfigTemplateSlave);
    }

    /**
     * 批量删除考核配置模版从
     *
     * @param ids 需要删除的考核配置模版从主键
     * @return 结果
     */
    @Override
    public int deleteCheckConfigTemplateSlaveByIds(Long[] ids)
    {
        return checkConfigTemplateSlaveMapper.deleteCheckConfigTemplateSlaveByIds(ids);
    }

    /**
     * 删除考核配置模版从信息
     *
     * @param id 考核配置模版从主键
     * @return 结果
     */
    @Override
    public int deleteCheckConfigTemplateSlaveById(Long id)
    {
        return checkConfigTemplateSlaveMapper.deleteCheckConfigTemplateSlaveById(id);
    }

    @Override
    public int deleteCheckConfigTemplateSlaveByMainId(Long id)
    {
        return checkConfigTemplateSlaveMapper.deleteCheckConfigTemplateSlaveByMainId(id);
    }

    @Override
    public int batchCheckConfigTemplateSlave(List<CheckConfigTemplateSlave> checkConfigSlaves){
        return checkConfigTemplateSlaveMapper.batchCheckConfigTemplateSlave(checkConfigSlaves);
    }
}
