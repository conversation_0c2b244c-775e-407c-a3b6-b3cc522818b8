package org.ruoyi.core.kaohe.service.impl;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.service.INewAuthorityService;
import com.ruoyi.system.service.ISysCompanyService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.kaohe.domain.CheckResult;
import org.ruoyi.core.kaohe.domain.KhProcess;
import org.ruoyi.core.kaohe.domain.vo.*;
import org.ruoyi.core.kaohe.mapper.CheckResultMapper;
import org.ruoyi.core.kaohe.service.*;
import org.ruoyi.core.personnel.service.IPersonnelArchivesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;


/**
 * 考核结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Service
public class CheckResultServiceImpl implements ICheckResultService
{
    @Autowired
    private CheckResultMapper checkResultMapper;
    @Autowired
    private IAnnualPlanService annualPlanService;
    @Autowired
    private ICheckConfigService checkConfigService;
    @Autowired
    private IAchievementEnterService achievementEnterService;
    @Autowired
    private ISysCompanyService companyService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IKhProcessService processService;
    @Autowired
    private IPersonnelArchivesService personnelArchivesService;
    @Autowired
    private INewAuthorityService newAuthorityService;
    /**
     * 查询考核结果
     *
     * @param id 考核结果主键
     * @return 考核结果
     */
    @Override
    public CheckResult selectCheckResultById(Long id)
    {
        return checkResultMapper.selectCheckResultById(id);
    }

    /**
     * 查询考核结果
     *
     * @param
     * @return 考核结果
     */
    @Override
    public CheckResult getCheckResult(CheckResultVo checkResult)
    {
        CheckResultVo checkResultVo = checkResultMapper.getCheckResult(checkResult);
        if (checkResultVo != null) {
            if ("1".equals(checkResult.getType())){
                SysCompanyVo sysCompanyVo = companyService.selectSysCompanyById(checkResult.getCompanyId());

                checkResultVo.setCompanyName(sysCompanyVo.getCompanyName());
            }
            if ("2".equals(checkResult.getType())){
                SysDept sysDept = deptService.selectDeptById(checkResult.getDeptId());
                SysCompanyVo sysCompanyVo = companyService.selectSysCompanyById(checkResult.getCompanyId());

                checkResultVo.setCompanyName(sysCompanyVo.getCompanyName());
                checkResultVo.setDeptName(sysDept.getDeptName());
            }
            if ("3".equals(checkResult.getType())){
                SysUser sysUser = userService.selectUserById(checkResult.getUserId());
                SysDept sysDept = deptService.selectDeptById(checkResult.getDeptId());
                SysCompanyVo sysCompanyVo = companyService.selectSysCompanyById(checkResult.getCompanyId());

                checkResultVo.setCompanyName(sysCompanyVo.getCompanyName());
                checkResultVo.setDeptName(sysDept.getDeptName());
                checkResultVo.setNickName(sysUser.getNickName());
            }
            //查询前几个季度的数据
            List<CheckResultVo> listBefor = checkResultMapper.selectCheckResultListBefor(checkResult);
            checkResultVo.setCheckResult(checkResult);
            listBefor.add(checkResultVo);
            BigDecimal distributionIndexDeviationYear = BigDecimal.ZERO;
            BigDecimal extensionIndexDeviationYear = BigDecimal.ZERO;
            BigDecimal extensionBankDeviationYear = BigDecimal.ZERO;

            if (listBefor != null){
                for (CheckResultVo vo : listBefor) {
                    BigDecimal distributionIndex = Optional.ofNullable(vo.getCheckResult().getDistributionIndex()).orElse(BigDecimal.ZERO);
                    BigDecimal extensionIndex = Optional.ofNullable(vo.getCheckResult().getExtensionIndex()).orElse(BigDecimal.ZERO);
                    BigDecimal extensionBank = Optional.ofNullable(vo.getCheckResult().getExtensionBank()).orElse(BigDecimal.ZERO);

                    BigDecimal completeDistributionIndex = Optional.ofNullable(vo.getCheckResult().getCompleteDistributionIndex()).orElse(BigDecimal.ZERO);
                    BigDecimal completeExtensionIndex = Optional.ofNullable(vo.getCheckResult().getCompleteExtensionIndex()).orElse(BigDecimal.ZERO);
                    BigDecimal completeExtensionBank = Optional.ofNullable(vo.getCheckResult().getCompleteExtensionBank()).orElse(BigDecimal.ZERO);

                    BigDecimal calibrationDistributionIndex = Optional.ofNullable(vo.getCheckResult().getCalibrationDistributionIndex()).orElse(BigDecimal.ZERO);
                    BigDecimal calibrationExtensionIndex = Optional.ofNullable(vo.getCheckResult().getCalibrationExtensionIndex()).orElse(BigDecimal.ZERO);
                    BigDecimal calibrationExtensionBank = Optional.ofNullable(vo.getCheckResult().getCalibrationExtensionBank()).orElse(BigDecimal.ZERO);

                    BigDecimal distributionIndexTemp = completeDistributionIndex.subtract(distributionIndex).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeDistributionIndex.subtract(distributionIndex);
                    BigDecimal extensionIndexTemp = completeExtensionIndex.subtract(extensionIndex).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeExtensionIndex.subtract(extensionIndex);
                    BigDecimal extensionBankTemp = completeExtensionBank.subtract(extensionBank).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeExtensionBank.subtract(extensionBank);

                    distributionIndexDeviationYear = distributionIndexDeviationYear.subtract(calibrationDistributionIndex).add(distributionIndexTemp);
                    extensionIndexDeviationYear = extensionIndexDeviationYear.subtract(calibrationExtensionIndex).add(extensionIndexTemp);
                    extensionBankDeviationYear = extensionBankDeviationYear.subtract(calibrationExtensionBank).add(extensionBankTemp);
                }
            }
            checkResultVo.setDistributionIndexDeviationYear(distributionIndexDeviationYear);
            checkResultVo.setExtensionIndexDeviationYear(extensionIndexDeviationYear);
            checkResultVo.setExtensionBankDeviationYear(extensionBankDeviationYear);
            return checkResultVo;
        }
        checkResultVo = new CheckResultVo();
        checkResult.setCheckResult(checkResultVo);
        if ("1".equals(checkResult.getType())){
            SysCompanyVo sysCompanyVo = companyService.selectSysCompanyById(checkResult.getCompanyId());

            checkResultVo.setCompanyName(sysCompanyVo.getCompanyName());
            checkResultVo.setCompanyShortName(sysCompanyVo.getCompanyShortName());
        }
        if ("2".equals(checkResult.getType())){
            SysDept sysDept = deptService.selectDeptById(checkResult.getDeptId());
            SysCompanyVo sysCompanyVo = companyService.selectSysCompanyById(checkResult.getCompanyId());

            checkResultVo.setCompanyName(sysCompanyVo.getCompanyName());
            checkResultVo.setCompanyShortName(sysCompanyVo.getCompanyShortName());
            checkResultVo.setDeptName(sysDept.getDeptName());
        }
        if ("3".equals(checkResult.getType())){
            SysUser sysUser = userService.selectUserById(checkResult.getUserId());
            SysDept sysDept = deptService.selectDeptById(checkResult.getDeptId());
            SysCompanyVo sysCompanyVo = companyService.selectSysCompanyById(checkResult.getCompanyId());

            checkResultVo.setCompanyName(sysCompanyVo.getCompanyName());
            checkResultVo.setCompanyShortName(sysCompanyVo.getCompanyShortName());
            checkResultVo.setDeptName(sysDept.getDeptName());
            checkResultVo.setNickName(sysUser.getNickName());
        }


        //查询用户的年度计划
        AnnualPlanVo userPlanVo = new AnnualPlanVo();
        userPlanVo.setUserId(checkResult.getUserId());
        userPlanVo.setYear(checkResult.getYear());
        userPlanVo.setType("3");
        AnnualPlanVo userPlan = annualPlanService.getAnnualPlanVo(userPlanVo);

        //查询部门的年度计划
        AnnualPlanVo deptPlanVo = new AnnualPlanVo();
        deptPlanVo.setDeptId(checkResult.getDeptId());
        deptPlanVo.setYear(checkResult.getYear());
        deptPlanVo.setType("2");
        AnnualPlanVo deptPlan = annualPlanService.getAnnualPlanVo(deptPlanVo);

        //查询公司的年度计划
        AnnualPlanVo companyPlanVo = new AnnualPlanVo();
        companyPlanVo.setCompanyId(checkResult.getCompanyId());
        companyPlanVo.setYear(checkResult.getYear());
        companyPlanVo.setType("1");
        AnnualPlanVo companyPlan = annualPlanService.getAnnualPlanVo(companyPlanVo);

        //查询人员考核配置数据
        CheckConfigSlaveVo userConfigSlaveVo = new CheckConfigSlaveVo();
        userConfigSlaveVo.setCorrelationId(checkResult.getUserId());
        userConfigSlaveVo.setYear(checkResult.getYear());
        userConfigSlaveVo.setType("2");
        CheckConfigSlaveVo userCheckConfig = checkConfigService.getCheckConfigSlaveVo(userConfigSlaveVo);

        //查询部门考核配置数据
        CheckConfigSlaveVo deptConfigSlaveVo = new CheckConfigSlaveVo();
        deptConfigSlaveVo.setCorrelationId(checkResult.getDeptId());
        deptConfigSlaveVo.setYear(checkResult.getYear());
        deptConfigSlaveVo.setType("1");
        CheckConfigSlaveVo deptCheckConfig = checkConfigService.getCheckConfigSlaveVo(deptConfigSlaveVo);

        //业绩录入
        AchievementEnterVo achievementEnterVo = new AchievementEnterVo();
        achievementEnterVo.setYear(checkResult.getYear());
        achievementEnterVo.setCompanyId(checkResult.getCompanyId());
        AchievementEnterVo achievementEnter = achievementEnterService.getAchievementEnterSum(achievementEnterVo);

        if (checkResultVo.getCompanyShortName() != null && checkResultVo.getCompanyShortName().equals("中保国信")){
            achievementEnter = achievementEnterService.getAchievementEnterSumByYear(achievementEnterVo);
        }

        AnnualPlanVo plan = new AnnualPlanVo();
        //根据查询到的动态数据(年度计划) 判断是否有值  (用户没有配置年度计划和考核配置，则以部门配置为准，没有部门配置，则以公司配置为准)
        if (userPlan != null && checkResult.getUserId() != null){
            plan = userPlan;
        } else if (deptPlan != null && checkResult.getDeptId() != null){
            plan = deptPlan;
        } else if (companyPlan != null && checkResult.getCompanyId() != null){
            plan = companyPlan;
        }
        if (checkResult.getQuarter() == 1){
            checkResultVo.setTotalIndex(plan.getQ1TotalIndex() != null ? plan.getQ1TotalIndex() : BigDecimal.ZERO );
            checkResultVo.setDistributionIndex(plan.getQ1DistributionIndex() != null ? plan.getQ1DistributionIndex() : BigDecimal.ZERO);
            checkResultVo.setExtensionIndex(plan.getQ1ExtensionIndex() != null ? plan.getQ1ExtensionIndex() : BigDecimal.ZERO);
            checkResultVo.setExtensionBank(plan.getQ1ExtensionBank() != null ? plan.getQ1ExtensionBank() : BigDecimal.ZERO);
        }
        if (checkResult.getQuarter() == 2){
            checkResultVo.setTotalIndex(plan.getQ2TotalIndex() != null ? plan.getQ2TotalIndex() : BigDecimal.ZERO);
            checkResultVo.setDistributionIndex(plan.getQ2DistributionIndex() != null ? plan.getQ2DistributionIndex() : BigDecimal.ZERO);
            checkResultVo.setExtensionIndex(plan.getQ2ExtensionIndex() != null ? plan.getQ2ExtensionIndex() : BigDecimal.ZERO);
            checkResultVo.setExtensionBank(plan.getQ2ExtensionBank() != null ? plan.getQ2ExtensionBank() : BigDecimal.ZERO);
        }
        if (checkResult.getQuarter() == 3){
            checkResultVo.setTotalIndex(plan.getQ3TotalIndex() != null ? plan.getQ3TotalIndex() : BigDecimal.ZERO);
            checkResultVo.setDistributionIndex(plan.getQ3DistributionIndex() != null ? plan.getQ3DistributionIndex() : BigDecimal.ZERO);
            checkResultVo.setExtensionIndex(plan.getQ3ExtensionIndex() != null ? plan.getQ3ExtensionIndex() : BigDecimal.ZERO);
            checkResultVo.setExtensionBank(plan.getQ3ExtensionBank() != null ? plan.getQ3ExtensionBank() : BigDecimal.ZERO);
        }
        if (checkResult.getQuarter() == 4){
            checkResultVo.setTotalIndex(plan.getQ4TotalIndex() != null ? plan.getQ4TotalIndex() : BigDecimal.ZERO);
            checkResultVo.setDistributionIndex(plan.getQ4DistributionIndex() != null ? plan.getQ4DistributionIndex() : BigDecimal.ZERO);
            checkResultVo.setExtensionIndex(plan.getQ4ExtensionIndex() != null ? plan.getQ4ExtensionIndex() : BigDecimal.ZERO);
            checkResultVo.setExtensionBank(plan.getQ4ExtensionBank() != null ? plan.getQ4ExtensionBank() : BigDecimal.ZERO);
        }

        CheckConfigSlaveVo slaveVo = new CheckConfigSlaveVo();
        if (userCheckConfig != null  && checkResult.getUserId() != null){
            slaveVo = userCheckConfig;
        } else if (deptCheckConfig != null  && checkResult.getDeptId() != null){
            slaveVo = deptCheckConfig;
        }
        //如果是公司类型则查找ceo 的配置
        if ("1".equals(checkResult.getType())){
            CheckConfigSlaveVo configSlaveVo = new CheckConfigSlaveVo();
            configSlaveVo.setYear(checkResult.getYear());
            Optional<CheckConfigSlaveVo> first = checkConfigService.getCheckConfigSlaveVoListOfCompany(configSlaveVo).stream()
                    .filter(vo -> Objects.equals(vo.getCompanyId(), checkResult.getCompanyId())).findFirst();
            if (first.isPresent()) {
                slaveVo = first.get();
            }
            //中保国信单独取--运营管理部
            if ("中保国信".equals(checkResultVo.getCompanyShortName())){
                slaveVo = checkConfigService.getCheckConfigSlaveVoOfZhongBao(deptConfigSlaveVo);
            }
        }

        checkResultVo.setAchievementWagesProportion(slaveVo.getAchievementWagesProportion() != null ? slaveVo.getAchievementWagesProportion() : BigDecimal.ZERO);
        checkResultVo.setDistributionProportion(slaveVo.getDistributionProportion() != null ? slaveVo.getDistributionProportion() : BigDecimal.ZERO);
        checkResultVo.setExtensionProportion(slaveVo.getExtensionProportion() != null ? slaveVo.getExtensionProportion() : BigDecimal.ZERO);

        if(achievementEnter != null){
            if (checkResult.getQuarter() == 1){
                checkResultVo.setCompleteTotalIndex(achievementEnter.getM1TotalIndex().add(achievementEnter.getM2TotalIndex().add(achievementEnter.getM3TotalIndex())));
                checkResultVo.setCompleteDistributionIndex(achievementEnter.getM1DistributionIndex().add(achievementEnter.getM2DistributionIndex().add(achievementEnter.getM3DistributionIndex())));
                checkResultVo.setCompleteExtensionIndex(achievementEnter.getM1ExtensionIndex().add(achievementEnter.getM2ExtensionIndex().add(achievementEnter.getM3ExtensionIndex())));
                checkResultVo.setCompleteExtensionBank(BigDecimal.ZERO);
            } else if (checkResult.getQuarter() == 2){
                checkResultVo.setCompleteTotalIndex(achievementEnter.getM4TotalIndex().add(achievementEnter.getM5TotalIndex().add(achievementEnter.getM6TotalIndex())));
                checkResultVo.setCompleteDistributionIndex(achievementEnter.getM4DistributionIndex().add(achievementEnter.getM5DistributionIndex().add(achievementEnter.getM6DistributionIndex())));
                checkResultVo.setCompleteExtensionIndex(achievementEnter.getM4ExtensionIndex().add(achievementEnter.getM5ExtensionIndex().add(achievementEnter.getM6ExtensionIndex())));
                checkResultVo.setCompleteExtensionBank(BigDecimal.ZERO);
            } else if (checkResult.getQuarter() == 3){
                checkResultVo.setCompleteTotalIndex(achievementEnter.getM7TotalIndex().add(achievementEnter.getM8TotalIndex().add(achievementEnter.getM9TotalIndex())));
                checkResultVo.setCompleteDistributionIndex(achievementEnter.getM7DistributionIndex().add(achievementEnter.getM8DistributionIndex().add(achievementEnter.getM9DistributionIndex())));
                checkResultVo.setCompleteExtensionIndex(achievementEnter.getM7ExtensionIndex().add(achievementEnter.getM8ExtensionIndex().add(achievementEnter.getM9ExtensionIndex())));
                checkResultVo.setCompleteExtensionBank(BigDecimal.ZERO);
            } else if (checkResult.getQuarter() == 4){
                checkResultVo.setCompleteTotalIndex(achievementEnter.getM10TotalIndex().add(achievementEnter.getM11TotalIndex().add(achievementEnter.getM12TotalIndex())));
                checkResultVo.setCompleteDistributionIndex(achievementEnter.getM10DistributionIndex().add(achievementEnter.getM11DistributionIndex().add(achievementEnter.getM12DistributionIndex())));
                checkResultVo.setCompleteExtensionIndex(achievementEnter.getM10ExtensionIndex().add(achievementEnter.getM11ExtensionIndex().add(achievementEnter.getM12ExtensionIndex())));
                checkResultVo.setCompleteExtensionBank(BigDecimal.ZERO);
            }
        } else {
            checkResultVo.setCompleteTotalIndex(BigDecimal.ZERO);
            checkResultVo.setCompleteDistributionIndex(BigDecimal.ZERO);
            checkResultVo.setCompleteExtensionIndex(BigDecimal.ZERO);
            checkResultVo.setCompleteExtensionBank(BigDecimal.ZERO);
        }


        if (checkResultVo.getDistributionIndex() != null && !checkResultVo.getDistributionIndex().equals(BigDecimal.ZERO) &&
            checkResultVo.getCompleteDistributionIndex() != null && checkResultVo.getDistributionProportion() != null
        ){
            //薪资分配占比(a)：( 本季度公司分配项目业绩(1月+2月+3月) / 本季度公司分配项目指标 ) * 考核配置分配占比
//            checkResultVo.setProjectSalaryDistributionProportion(checkResultVo.getCompleteDistributionIndex()
//                                                                .divide(checkResultVo.getDistributionIndex(),MathContext.DECIMAL128)
//                                                                .multiply(checkResultVo.getDistributionProportion())
//                                                                .setScale(2, RoundingMode.HALF_UP)
//                                                                );

            BigDecimal projectSalaryDistributionProportion = checkResultVo.getCompleteDistributionIndex()
                    .divide(checkResultVo.getDistributionIndex(),MathContext.DECIMAL128)
                    .multiply(checkResultVo.getDistributionProportion())
                    .setScale(2, RoundingMode.HALF_UP);
            checkResultVo.setProjectSalaryDistributionProportion(projectSalaryDistributionProportion.compareTo(checkResultVo.getDistributionProportion()) > 0 ? checkResultVo.getDistributionProportion() : projectSalaryDistributionProportion);
            //计算分配项目业绩偏差
            checkResultVo.setDistributionIndexDeviation(checkResultVo.getCompleteDistributionIndex().subtract(checkResultVo.getDistributionIndex()));
            //计算分配指标校准后完成状态
            if (checkResultVo.getDistributionIndexDeviation() != null &&  checkResultVo.getDistributionIndexDeviation().compareTo(BigDecimal.ZERO) < 0) {
                checkResultVo.setCalibrationDistributionIndexState("2");
            } else {
                checkResultVo.setCalibrationDistributionIndexState("1");
            }
        } else {
            checkResultVo.setProjectSalaryDistributionProportion(BigDecimal.ZERO);
            checkResultVo.setDistributionIndexDeviation(BigDecimal.ZERO);
        }

        if(checkResultVo.getExtensionIndex() != null && !checkResultVo.getExtensionIndex().equals(BigDecimal.ZERO) &&
            checkResultVo.getCompleteExtensionIndex() != null && checkResultVo.getExtensionProportion() != null
        ){
            //薪资自拓占比(b)：( 本季度公司自拓项目业绩((1月+2月+3月) / 本季度公司自拓项目指标 ) * 考核配置自拓占比
//            checkResultVo.setProjectSalaryExtensionProportion(checkResultVo.getCompleteExtensionIndex()
//                                                                .divide(checkResultVo.getExtensionIndex(),MathContext.DECIMAL128)
//                                                                .multiply(checkResultVo.getExtensionProportion())
//                                                                .setScale(2, RoundingMode.HALF_UP)
//                                                                );

            BigDecimal projectSalaryExtensionProportion = checkResultVo.getCompleteExtensionIndex()
                    .divide(checkResultVo.getExtensionIndex(),MathContext.DECIMAL128)
                    .multiply(checkResultVo.getExtensionProportion())
                    .setScale(2, RoundingMode.HALF_UP);
            checkResultVo.setProjectSalaryExtensionProportion(projectSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : projectSalaryExtensionProportion);
            //计算自拓业绩偏差
            checkResultVo.setExtensionIndexDeviation(checkResultVo.getCompleteExtensionIndex().subtract(checkResultVo.getExtensionIndex()));
            //计算自拓指标校准后完成状态
            if (checkResultVo.getExtensionIndexDeviation().compareTo(BigDecimal.ZERO) < 0) {
                checkResultVo.setCalibrationExtensionIndexProjectState("未完成");
            } else {
                checkResultVo.setCalibrationExtensionIndexProjectState("已完成-项目");
                if(checkResultVo.getExtensionIndex() == null || !checkResultVo.getExtensionIndex().equals(BigDecimal.ZERO)){
                    checkResultVo.setCalibrationExtensionIndexProjectState("已完成-项目");
                }
            }
        } else {
            checkResultVo.setProjectSalaryExtensionProportion(BigDecimal.ZERO);
            checkResultVo.setExtensionIndexDeviation(BigDecimal.ZERO);
        }

        if(checkResultVo.getExtensionBank() != null && !checkResultVo.getExtensionBank().equals(BigDecimal.ZERO) &&
                checkResultVo.getCompleteExtensionBank() != null && checkResultVo.getExtensionProportion() != null
        ){
            //薪资自拓占比(b)：( 本季度公司自拓项目业绩((1月+2月+3月) / 本季度公司自拓项目指标 ) * 考核配置自拓占比
            BigDecimal bankSalaryExtensionProportion = checkResultVo.getCompleteExtensionBank()
                    .divide(checkResultVo.getExtensionBank(), MathContext.DECIMAL128)
                    .multiply(checkResultVo.getExtensionProportion())
                    .setScale(2, RoundingMode.HALF_UP);
            checkResultVo.setBankSalaryExtensionProportion(bankSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : bankSalaryExtensionProportion);
            //计算自拓业绩偏差
            checkResultVo.setExtensionBankDeviation(checkResultVo.getCompleteExtensionBank().subtract(checkResultVo.getExtensionBank()));
            //计算自拓指标校准后完成状态
            if (checkResultVo.getExtensionBankDeviation().compareTo(BigDecimal.ZERO) < 0) {
                checkResultVo.setCalibrationExtensionIndexBankState("未完成");
            } else {
                checkResultVo.setCalibrationExtensionIndexBankState("已完成-银行");
            }
        } else {
            checkResultVo.setBankSalaryExtensionProportion(BigDecimal.ZERO);
            checkResultVo.setExtensionBankDeviation(BigDecimal.ZERO);
            if (checkResultVo.getExtensionBank() != null && checkResultVo.getExtensionBank().equals(BigDecimal.ZERO)) {
                checkResultVo.setCalibrationExtensionIndexBankState("已完成-银行");
            } else {
                checkResultVo.setCalibrationExtensionIndexBankState("未完成");
            }
        }

        //项目薪资总占比
        checkResultVo.setProjectSalaryTotalProportion(checkResultVo.getProjectSalaryDistributionProportion().add(checkResultVo.getProjectSalaryExtensionProportion()));
        //本年度业绩偏差
//        checkResult.setQuarter(checkResult.getQuarter() - 1);  //作为条件查询上季度
//        CheckResultVo checkResultVoLast = checkResultMapper.getCheckResult(checkResult);
//        if (checkResultVoLast != null) {
//            checkResultVo.setDistributionIndexDeviationYear(checkResultVoLast.getDistributionIndexDeviationCalibration());
//            checkResultVo.setExtensionIndexDeviationYear(checkResultVoLast.getDistributionIndexDeviationCalibration());
//            checkResultVo.setExtensionBankDeviationYear(checkResultVoLast.getExtensionBankDeviationCalibration());
//        } else {
//            checkResultVo.setDistributionIndexDeviationYear(BigDecimal.ZERO);
//            checkResultVo.setExtensionIndexDeviationYear(BigDecimal.ZERO);
//            checkResultVo.setExtensionBankDeviationYear(BigDecimal.ZERO);
//        }
//        checkResult.setQuarter(checkResult.getQuarter() + 1);  //恢复本季度数据
        //查询前几个季度的数据
        List<CheckResultVo> listBefor = checkResultMapper.selectCheckResultListBefor(checkResult);
        listBefor.add(checkResult);
        BigDecimal distributionIndexDeviationYear = BigDecimal.ZERO;
        BigDecimal extensionIndexDeviationYear = BigDecimal.ZERO;
        BigDecimal extensionBankDeviationYear = BigDecimal.ZERO;

        if (listBefor != null){
            for (CheckResultVo vo : listBefor) {
                BigDecimal distributionIndex = Optional.ofNullable(vo.getCheckResult().getDistributionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal extensionIndex = Optional.ofNullable(vo.getCheckResult().getExtensionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal extensionBank = Optional.ofNullable(vo.getCheckResult().getExtensionBank()).orElse(BigDecimal.ZERO);

                BigDecimal completeDistributionIndex = Optional.ofNullable(vo.getCheckResult().getCompleteDistributionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal completeExtensionIndex = Optional.ofNullable(vo.getCheckResult().getCompleteExtensionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal completeExtensionBank = Optional.ofNullable(vo.getCheckResult().getCompleteExtensionBank()).orElse(BigDecimal.ZERO);

                BigDecimal calibrationDistributionIndex = Optional.ofNullable(vo.getCheckResult().getCalibrationDistributionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal calibrationExtensionIndex = Optional.ofNullable(vo.getCheckResult().getCalibrationExtensionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal calibrationExtensionBank = Optional.ofNullable(vo.getCheckResult().getCalibrationExtensionBank()).orElse(BigDecimal.ZERO);

                BigDecimal distributionIndexTemp = completeDistributionIndex.subtract(distributionIndex).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeDistributionIndex.subtract(distributionIndex);
                BigDecimal extensionIndexTemp = completeExtensionIndex.subtract(extensionIndex).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeExtensionIndex.subtract(extensionIndex);
                BigDecimal extensionBankTemp = completeExtensionBank.subtract(extensionBank).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeExtensionBank.subtract(extensionBank);

                distributionIndexDeviationYear = distributionIndexDeviationYear.subtract(calibrationDistributionIndex).add(distributionIndexTemp);
                extensionIndexDeviationYear = extensionIndexDeviationYear.subtract(calibrationExtensionIndex).add(extensionIndexTemp);
                extensionBankDeviationYear = extensionBankDeviationYear.subtract(calibrationExtensionBank).add(extensionBankTemp);
            }
        }
        checkResultVo.setDistributionIndexDeviationYear(distributionIndexDeviationYear);
        checkResultVo.setExtensionIndexDeviationYear(extensionIndexDeviationYear);
        checkResultVo.setExtensionBankDeviationYear(extensionBankDeviationYear);
        //checkResult.setCheckResult(checkResultVo);
        return checkResult;
    }


    @Override
    public List<CheckResultVo> selectCheckResultListofQ3(CheckResultVo checkResult){
        List<CheckResultVo> checkResults = checkResultMapper.selectCheckResultListQ3(checkResult).stream()
                                                    .filter(vo -> vo.getQuarter() != 4)
                                                    .collect(Collectors.toList());
//        Optional<CheckResultVo> result = checkResults.stream()
//                .filter(vo -> vo.getQuarter() == 3) // 过滤 quarter = 3 的数据
//                .findFirst();
//        BigDecimal distributionIndexDeviationYear;
//        BigDecimal extensionIndexDeviationYear;
//        BigDecimal extensionBankDeviationYear;
//        if (result.isPresent()) {
//            distributionIndexDeviationYear = result.get().getCheckResult().getDistributionIndexDeviationYear();
//            extensionIndexDeviationYear = result.get().getCheckResult().getExtensionIndexDeviationYear();
//            extensionBankDeviationYear = result.get().getCheckResult().getExtensionBankDeviationYear();
//        } else {
//            extensionBankDeviationYear = BigDecimal.ZERO;
//            extensionIndexDeviationYear = BigDecimal.ZERO;
//            distributionIndexDeviationYear = BigDecimal.ZERO;
//        }

        checkResult.setQuarter(4);
        List<CheckResultVo> listBefor = checkResultMapper.selectCheckResultListBefor(checkResult);

        BigDecimal distributionIndexDeviationYear = BigDecimal.ZERO;
        BigDecimal extensionIndexDeviationYear = BigDecimal.ZERO;
        BigDecimal extensionBankDeviationYear = BigDecimal.ZERO;

        if (listBefor != null){
            for (CheckResultVo vo : listBefor) {
                BigDecimal distributionIndex = Optional.ofNullable(vo.getCheckResult().getDistributionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal extensionIndex = Optional.ofNullable(vo.getCheckResult().getExtensionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal extensionBank = Optional.ofNullable(vo.getCheckResult().getExtensionBank()).orElse(BigDecimal.ZERO);

                BigDecimal completeDistributionIndex = Optional.ofNullable(vo.getCheckResult().getCompleteDistributionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal completeExtensionIndex = Optional.ofNullable(vo.getCheckResult().getCompleteExtensionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal completeExtensionBank = Optional.ofNullable(vo.getCheckResult().getCompleteExtensionBank()).orElse(BigDecimal.ZERO);

                BigDecimal calibrationDistributionIndex = Optional.ofNullable(vo.getCheckResult().getCalibrationDistributionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal calibrationExtensionIndex = Optional.ofNullable(vo.getCheckResult().getCalibrationExtensionIndex()).orElse(BigDecimal.ZERO);
                BigDecimal calibrationExtensionBank = Optional.ofNullable(vo.getCheckResult().getCalibrationExtensionBank()).orElse(BigDecimal.ZERO);

                BigDecimal distributionIndexTemp = completeDistributionIndex.subtract(distributionIndex).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeDistributionIndex.subtract(distributionIndex);
                BigDecimal extensionIndexTemp = completeExtensionIndex.subtract(extensionIndex).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeExtensionIndex.subtract(extensionIndex);
                BigDecimal extensionBankTemp = completeExtensionBank.subtract(extensionBank).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeExtensionBank.subtract(extensionBank);

                distributionIndexDeviationYear = distributionIndexDeviationYear.subtract(calibrationDistributionIndex).add(distributionIndexTemp);
                extensionIndexDeviationYear = extensionIndexDeviationYear.subtract(calibrationExtensionIndex).add(extensionIndexTemp);
                extensionBankDeviationYear = extensionBankDeviationYear.subtract(calibrationExtensionBank).add(extensionBankTemp);
            }
        }

        for (CheckResultVo vo : checkResults) {
            vo.getCheckResult().setDistributionIndexDeviationYear(distributionIndexDeviationYear);
            vo.getCheckResult().setExtensionIndexDeviationYear(extensionIndexDeviationYear);
            vo.getCheckResult().setExtensionBankDeviationYear(extensionBankDeviationYear);
        }

        return checkResults.stream()
                .filter(vo -> (("2".equals(vo.getCheckResult().getCalibrationDistributionIndexState())) ||
                        ("4".equals(vo.getCheckResult().getCalibrationDistributionIndexState()))) &&
                        (vo.getCheckResult().getCalibrationExtensionIndexProjectState().contains("未完成") ||
                        vo.getCheckResult().getCalibrationExtensionIndexBankState().contains("未完成"))
                ).collect(Collectors.toList());
    }

    /**
     * 查询考核结果列表
     *
     * @param checkResult 考核结果
     * @return 考核结果
     */
    @Override
    public List<CheckResultVo> selectCheckResultList(CheckResultVo checkResult)
    {
        List<CheckResultVo> checkResults = new ArrayList<>();
        //获取用户权限公司
        LoginUser loginUser = getLoginUser();
        List<Long> authorityCompanyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.PERFORMANCE.getCode());
        if(authorityCompanyIds.isEmpty()){
            return new ArrayList<>();
        }
        checkResult.setAuthorityCompanyIds(authorityCompanyIds);

        if("3".equals(checkResult.getType())){
            //PageUtil.startPage();
            checkResults = checkResultMapper.selectUserList(checkResult);
            if(checkResults != null && !checkResults.isEmpty()){
                checkResults.forEach(slave -> {
                    slave.setDeptNameBelong(personnelArchivesService.getDeptBreadcrumb(slave.getDept()));
                });
            }
            Set<Long> userSets = checkResults.stream().map(CheckResultVo::getUserId).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<String> yearSets = checkResults.stream().map(CheckResultVo::getYear).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<Long> deptSets = checkResults.stream().map(CheckResultVo::getDeptId).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<Long> companySets = checkResults.stream().map(CheckResultVo::getCompanyId).filter(Objects::nonNull).collect(Collectors.toSet());

            //查询已经写入的考核
            CheckResultVo resultVo = new CheckResultVo();
            resultVo.setYears(yearSets);
            resultVo.setUserIds(userSets);
            resultVo.setType(checkResult.getType());
            List<CheckResultVo> checkResultsList = checkResultMapper.selectCheckResultList(resultVo);
            //根据查询到的以录入考核结果库数据
            Map<String, Map<Integer, Map<Long, CheckResultVo>>> userCheckResultsMap = checkResultsList.stream()
                                                .collect(Collectors.groupingBy(
                                                        CheckResultVo::getYear,  // 外层Map的key是年份
                                                        Collectors.groupingBy(
                                                                CheckResultVo::getQuarter,  // 第二层Map的key是季度
                                                                Collectors.toMap(
                                                                        CheckResultVo::getUserId,  // 第三层Map的key是userId
                                                                        vo -> vo  // AnnualPlanVo作为值
                                                                )
                                                        )
                                                ));
            checkResults.forEach(vo ->{
                if (userCheckResultsMap.containsKey(vo.getYear()) &&
                    userCheckResultsMap.get(vo.getYear()).containsKey(vo.getQuarter()) &&
                    userCheckResultsMap.get(vo.getYear()).get(vo.getQuarter()).containsKey(vo.getUserId())
                    ){
                    CheckResultVo checkResultVo = userCheckResultsMap.get(vo.getYear()).get(vo.getQuarter()).get(vo.getUserId());
                    if (checkResultVo.getDistributionIndexDeviation() != null && checkResultVo.getDistributionIndexDeviation().compareTo(BigDecimal.ZERO) < 0
                            && checkResultVo.getCompleteExtensionBank() != null){
                       //checkResultVo.setBankSalaryExtensionProportion(checkResultVo.getCompleteExtensionBank().divide(checkResultVo.getExtensionBank(), 4, RoundingMode.HALF_UP).multiply(checkResultVo.getExtensionProportion()));
                        BigDecimal bankSalaryExtensionProportion = checkResultVo.getCompleteExtensionBank()
                                .divide(checkResultVo.getExtensionBank(), MathContext.DECIMAL128)
                                .multiply(checkResultVo.getExtensionProportion())
                                .setScale(2, RoundingMode.HALF_UP);
                        checkResultVo.setBankSalaryExtensionProportion(bankSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : bankSalaryExtensionProportion);
                    } else {
                        checkResultVo.setBankSalaryExtensionProportion(BigDecimal.ZERO);
                    }
                    vo.setType(checkResult.getType());
                    vo.setCheckResult(checkResultVo);
                    vo.setSource("0");
                }
            });

            //查询用户的年度计划
            AnnualPlanVo userPlanVo = new AnnualPlanVo();
            userPlanVo.setUserIds(userSets);
            userPlanVo.setYears(yearSets);
            userPlanVo.setType("3");
            List<AnnualPlanVo> userPlanList = annualPlanService.selectAnnualPlanList(userPlanVo);
            Map<String, Map<Long, AnnualPlanVo>> userPlanMap = userPlanList.stream()
                        .collect(Collectors.groupingBy(AnnualPlanVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(AnnualPlanVo::getUserId, vo -> vo))); // 内层Map的key是userId

            //查询部门的年度计划
            AnnualPlanVo deptPlanVo = new AnnualPlanVo();
            deptPlanVo.setDeptIds(deptSets);
            deptPlanVo.setYears(yearSets);
            deptPlanVo.setType("2");
            List<AnnualPlanVo> deptPlanList = annualPlanService.selectAnnualPlanList(deptPlanVo);
            Map<String, Map<Long, AnnualPlanVo>> deptPlanMap = deptPlanList.stream()
                        .collect(Collectors.groupingBy(AnnualPlanVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(AnnualPlanVo::getDeptId, vo -> vo))); // 内层Map的key是deptId

            //查询公司的年度计划
            AnnualPlanVo companyPlanVo = new AnnualPlanVo();
            companyPlanVo.setCompanyIds(companySets);
            companyPlanVo.setYears(yearSets);
            companyPlanVo.setType("1");
            List<AnnualPlanVo> companyPlanList = annualPlanService.selectAnnualPlanList(companyPlanVo);
            Map<String, Map<Long, AnnualPlanVo>> companyPlanMap = companyPlanList.stream()
                        .collect(Collectors.groupingBy(AnnualPlanVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(AnnualPlanVo::getCompanyId, vo -> vo))); // 内层Map的key是companyId

            //查询人员考核配置数据
            CheckConfigSlaveVo userConfigSlaveVo = new CheckConfigSlaveVo();
            userConfigSlaveVo.setCorrelationIds(userSets);
            userConfigSlaveVo.setYears(yearSets);
            userConfigSlaveVo.setType("2");
            List<CheckConfigSlaveVo> userCheckConfig = checkConfigService.getCheckConfigSlaveVoList(userConfigSlaveVo);
            Map<String, Map<Long, CheckConfigSlaveVo>> userCheckConfigMap = userCheckConfig.stream()
                        .filter(vo -> vo.getCorrelationId() != null)
                        .collect(Collectors.groupingBy(CheckConfigSlaveVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(CheckConfigSlaveVo::getCorrelationId, vo -> vo))); // 内层Map的key是companyId

            //查询部门考核配置数据
            CheckConfigSlaveVo deptConfigSlaveVo = new CheckConfigSlaveVo();
            deptConfigSlaveVo.setCorrelationIds(deptSets);
            deptConfigSlaveVo.setYears(yearSets);
            deptConfigSlaveVo.setType("1");
            List<CheckConfigSlaveVo> deptCheckConfig = checkConfigService.getCheckConfigSlaveVoList(deptConfigSlaveVo);
            Map<String, Map<Long, CheckConfigSlaveVo>> deptCheckConfigMap = deptCheckConfig.stream()
                        .filter(vo -> vo.getCorrelationId() != null)
                        .collect(Collectors.groupingBy(CheckConfigSlaveVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(CheckConfigSlaveVo::getCorrelationId, vo -> vo))); // 内层Map的key是companyId
            //业绩录入
            AchievementEnterVo achievementEnterVo = new AchievementEnterVo();
            achievementEnterVo.setYears(yearSets);
            achievementEnterVo.setCompanyIds(companySets);
            List<AchievementEnterVo> achievementEnters = achievementEnterService.selectAchievementEnterSumList(achievementEnterVo);

            Map<String, Map<Long, AchievementEnterVo>> achievementEnterMap = achievementEnters.stream()
                        .filter(vo -> !"中保国信".equals(vo.getCompanyShortName()))  // 过滤掉 companyShortName 等于 "中保国信" 的数据
                        .collect(Collectors.groupingBy(AchievementEnterVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(AchievementEnterVo::getCompanyId, vo -> vo))); // 内层Map的key是companyId

            //业绩录入 中保国信算全部公司总和
            List<AchievementEnterVo> achievementEnterSumListByYear = achievementEnterService.selectAchievementEnterSumListByYear(achievementEnterVo);
            Map<String, AchievementEnterVo> achievementEnterSumListByYearMap = achievementEnterSumListByYear.stream()
                    .collect(Collectors.toMap(
                            AchievementEnterVo::getYear,  // Key 是 year
                            vo -> vo,                     // Value 是 AchievementEnterVo 对象
                            (existing, replacement) -> existing  // 处理重复的 key，选择保留现有的 value
                    ));

            checkResults.forEach(vo -> {
                //checkResult对象没有数据时去动态组数据
                if(vo.getSource() == null){
                    CheckResultVo checkResultVo = new CheckResultVo();
                    //vo.setSource("2");
                    //根据查询到的动态数据(年度计划) 判断是否有值  (用户没有配置年度计划和考核配置，则以部门配置为准，没有部门配置，则以公司配置为准)
                    if (userPlanMap.containsKey(vo.getYear()) && userPlanMap.get(vo.getYear()).containsKey(vo.getUserId())){
                        //vo.setSource("3");
                        AnnualPlanVo annualPlan = userPlanMap.get(vo.getYear()).get(vo.getUserId());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setTotalIndex(annualPlan.getQ1TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ1DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ1ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ1ExtensionBank());
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setTotalIndex(annualPlan.getQ2TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ2DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ2ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ2ExtensionBank());
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setTotalIndex(annualPlan.getQ3TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ3DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ3ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ3ExtensionBank());
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setTotalIndex(annualPlan.getQ4TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ4DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ4ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ4ExtensionBank());
                        }
                    } else if (deptPlanMap.containsKey(vo.getYear()) && deptPlanMap.get(vo.getYear()).containsKey(vo.getDeptId())){
                        //vo.setSource("2");
                        AnnualPlanVo annualPlan = deptPlanMap.get(vo.getYear()).get(vo.getDeptId());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setTotalIndex(annualPlan.getQ1TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ1DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ1ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ1ExtensionBank());
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setTotalIndex(annualPlan.getQ2TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ2DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ2ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ2ExtensionBank());
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setTotalIndex(annualPlan.getQ3TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ3DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ3ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ3ExtensionBank());
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setTotalIndex(annualPlan.getQ4TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ4DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ4ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ4ExtensionBank());
                        }
                    } else if (companyPlanMap.containsKey(vo.getYear()) && companyPlanMap.get(vo.getYear()).containsKey(vo.getCompanyId())){
                        //vo.setSource("1");
                        AnnualPlanVo annualPlan = companyPlanMap.get(vo.getYear()).get(vo.getCompanyId());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setTotalIndex(annualPlan.getQ1TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ1DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ1ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ1ExtensionBank());
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setTotalIndex(annualPlan.getQ2TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ2DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ2ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ2ExtensionBank());
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setTotalIndex(annualPlan.getQ3TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ3DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ3ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ3ExtensionBank());
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setTotalIndex(annualPlan.getQ4TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ4DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ4ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ4ExtensionBank());
                        }
                    }
                    //考核配置
                    if (userCheckConfigMap.containsKey(vo.getYear()) && userCheckConfigMap.get(vo.getYear()).containsKey(vo.getUserId())){
                        vo.setSource("3");
                        CheckConfigSlaveVo configSlaveVo = userCheckConfigMap.get(vo.getYear()).get(vo.getUserId());
                        checkResultVo.setAchievementWagesProportion(configSlaveVo.getAchievementWagesProportion());
                        checkResultVo.setDistributionProportion(configSlaveVo.getDistributionProportion());
                        checkResultVo.setExtensionProportion(configSlaveVo.getExtensionProportion());
                    } else if (deptCheckConfigMap.containsKey(vo.getYear()) && deptCheckConfigMap.get(vo.getYear()).containsKey(vo.getDeptId())){
                        vo.setSource("2");
                        CheckConfigSlaveVo configSlaveVo = deptCheckConfigMap.get(vo.getYear()).get(vo.getDeptId());
                        checkResultVo.setAchievementWagesProportion(configSlaveVo.getAchievementWagesProportion());
                        checkResultVo.setDistributionProportion(configSlaveVo.getDistributionProportion());
                        checkResultVo.setExtensionProportion(configSlaveVo.getExtensionProportion());
                    }

                    if (achievementEnterMap.containsKey(vo.getYear()) && achievementEnterMap.get(vo.getYear()).containsKey(vo.getCompanyId())){
                        AchievementEnterVo achievementEnter = achievementEnterMap.get(vo.getYear()).get(vo.getCompanyId());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM1TotalIndex().add(achievementEnter.getM2TotalIndex().add(achievementEnter.getM3TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM1DistributionIndex().add(achievementEnter.getM2DistributionIndex().add(achievementEnter.getM3DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM1ExtensionIndex().add(achievementEnter.getM2ExtensionIndex().add(achievementEnter.getM3ExtensionIndex())));
                        } else if (vo.getQuarter() == 2){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM4TotalIndex().add(achievementEnter.getM5TotalIndex().add(achievementEnter.getM6TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM4DistributionIndex().add(achievementEnter.getM5DistributionIndex().add(achievementEnter.getM6DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM4ExtensionIndex().add(achievementEnter.getM5ExtensionIndex().add(achievementEnter.getM6ExtensionIndex())));
                        } else if (vo.getQuarter() == 3){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM7TotalIndex().add(achievementEnter.getM8TotalIndex().add(achievementEnter.getM9TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM7DistributionIndex().add(achievementEnter.getM8DistributionIndex().add(achievementEnter.getM9DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM7ExtensionIndex().add(achievementEnter.getM8ExtensionIndex().add(achievementEnter.getM9ExtensionIndex())));
                        } else if (vo.getQuarter() == 4){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM10TotalIndex().add(achievementEnter.getM11TotalIndex().add(achievementEnter.getM12TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM10DistributionIndex().add(achievementEnter.getM11DistributionIndex().add(achievementEnter.getM12DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM10ExtensionIndex().add(achievementEnter.getM11ExtensionIndex().add(achievementEnter.getM12ExtensionIndex())));
                        }
                    }
                    //中保国信特殊出处理
                    if (vo.getCompanyShortName() != null && vo.getCompanyShortName().equals("中保国信") && achievementEnterSumListByYearMap.containsKey(vo.getYear())){
                        AchievementEnterVo achievementEnter = achievementEnterSumListByYearMap.get(vo.getYear());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM1TotalIndex().add(achievementEnter.getM2TotalIndex().add(achievementEnter.getM3TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM1DistributionIndex().add(achievementEnter.getM2DistributionIndex().add(achievementEnter.getM3DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM1ExtensionIndex().add(achievementEnter.getM2ExtensionIndex().add(achievementEnter.getM3ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM4TotalIndex().add(achievementEnter.getM5TotalIndex().add(achievementEnter.getM6TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM4DistributionIndex().add(achievementEnter.getM5DistributionIndex().add(achievementEnter.getM6DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM4ExtensionIndex().add(achievementEnter.getM5ExtensionIndex().add(achievementEnter.getM6ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM7TotalIndex().add(achievementEnter.getM8TotalIndex().add(achievementEnter.getM9TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM7DistributionIndex().add(achievementEnter.getM8DistributionIndex().add(achievementEnter.getM9DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM7ExtensionIndex().add(achievementEnter.getM8ExtensionIndex().add(achievementEnter.getM9ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM10TotalIndex().add(achievementEnter.getM11TotalIndex().add(achievementEnter.getM12TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM10DistributionIndex().add(achievementEnter.getM11DistributionIndex().add(achievementEnter.getM12DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM10ExtensionIndex().add(achievementEnter.getM11ExtensionIndex().add(achievementEnter.getM12ExtensionIndex())));
                        }
                    }
                    if (checkResultVo.getDistributionIndex() != null && !checkResultVo.getDistributionIndex().equals(BigDecimal.ZERO) &&
                            checkResultVo.getCompleteDistributionIndex() != null && checkResultVo.getDistributionProportion() != null
                    ){
                            //薪资分配占比(a)：( 本季度公司分配项目业绩(1月+2月+3月) / 本季度公司分配项目指标 ) * 考核配置分配占比
//                            checkResultVo.setProjectSalaryDistributionProportion(checkResultVo.getCompleteDistributionIndex()
//                                                                                .divide(checkResultVo.getDistributionIndex(),MathContext.DECIMAL128)
//                                                                                .multiply(checkResultVo.getDistributionProportion())
//                                                                                .setScale(2, RoundingMode.HALF_UP)
//                                                                                );

                        BigDecimal projectSalaryDistributionProportion = checkResultVo.getCompleteDistributionIndex()
                                .divide(checkResultVo.getDistributionIndex(),MathContext.DECIMAL128)
                                .multiply(checkResultVo.getDistributionProportion())
                                .setScale(2, RoundingMode.HALF_UP);
                        checkResultVo.setProjectSalaryDistributionProportion(projectSalaryDistributionProportion.compareTo(checkResultVo.getDistributionProportion()) > 0 ? checkResultVo.getDistributionProportion() : projectSalaryDistributionProportion);
                            //计算分配项目业绩偏差
                            checkResultVo.setDistributionIndexDeviation(checkResultVo.getCompleteDistributionIndex().subtract(checkResultVo.getDistributionIndex()));
                            //计算分配指标校准后完成状态
                            if (checkResultVo.getDistributionIndexDeviation() != null &&  checkResultVo.getDistributionIndexDeviation().compareTo(BigDecimal.ZERO) < 0) {
                                checkResultVo.setCalibrationDistributionIndexState("2");
                            } else {
                                checkResultVo.setCalibrationDistributionIndexState("1");
                            }
                        } else {
                            checkResultVo.setProjectSalaryDistributionProportion(BigDecimal.ZERO);
                        }

                        if(checkResultVo.getExtensionIndex() != null && !checkResultVo.getExtensionIndex().equals(BigDecimal.ZERO) &&
                            checkResultVo.getCompleteExtensionIndex() != null && checkResultVo.getExtensionProportion() != null
                        ){
                            //薪资自拓占比(b)：( 本季度公司自拓项目业绩((1月+2月+3月) / 本季度公司自拓项目指标 ) * 考核配置自拓占比
//                            checkResultVo.setProjectSalaryExtensionProportion(checkResultVo.getCompleteExtensionIndex()
//                                                                                .divide(checkResultVo.getExtensionIndex(),MathContext.DECIMAL128)
//                                                                                .multiply(checkResultVo.getExtensionProportion())
//                                                                                .setScale(2, RoundingMode.HALF_UP)
//                                                                                );

                            BigDecimal projectSalaryExtensionProportion = checkResultVo.getCompleteExtensionIndex()
                                    .divide(checkResultVo.getExtensionIndex(),MathContext.DECIMAL128)
                                    .multiply(checkResultVo.getExtensionProportion())
                                    .setScale(2, RoundingMode.HALF_UP);
                            checkResultVo.setProjectSalaryExtensionProportion(projectSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : projectSalaryExtensionProportion);
                            //计算自拓业绩偏差
                            checkResultVo.setExtensionIndexDeviation(checkResultVo.getCompleteExtensionIndex().subtract(checkResultVo.getExtensionIndex()));
                            //计算自拓指标校准后完成状态
                            if (checkResultVo.getExtensionIndexDeviation().compareTo(BigDecimal.ZERO) < 0) {
                                checkResultVo.setCalibrationExtensionIndexProjectState("未完成");
                            } else {
                                checkResultVo.setCalibrationExtensionIndexProjectState("已完成-项目");
                            }
                        } else {
                            checkResultVo.setProjectSalaryExtensionProportion(BigDecimal.ZERO);
                            if(checkResultVo.getExtensionIndex() == null || !checkResultVo.getExtensionIndex().equals(BigDecimal.ZERO)){
                                checkResultVo.setCalibrationExtensionIndexProjectState("已完成-项目");
                            }
                        }

                        if(checkResultVo.getExtensionBank() != null && !checkResultVo.getExtensionBank().equals(BigDecimal.ZERO) &&
                                checkResultVo.getCompleteExtensionBank() != null && checkResultVo.getExtensionProportion() != null
                        ){
                            //薪资自拓占比(b)：( 本季度公司自拓项目业绩((1月+2月+3月) / 本季度公司自拓项目指标 ) * 考核配置自拓占比
                            BigDecimal bankSalaryExtensionProportion = checkResultVo.getCompleteExtensionBank()
                                    .divide(checkResultVo.getExtensionBank(), MathContext.DECIMAL128)
                                    .multiply(checkResultVo.getExtensionProportion())
                                    .setScale(2, RoundingMode.HALF_UP);
                            checkResultVo.setBankSalaryExtensionProportion(bankSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : bankSalaryExtensionProportion);
                            //计算自拓业绩偏差
                            checkResultVo.setExtensionBankDeviation(checkResultVo.getCompleteExtensionBank().subtract(checkResultVo.getExtensionBank()));
                            //计算自拓指标校准后完成状态
                            if (checkResultVo.getExtensionBankDeviation().compareTo(BigDecimal.ZERO) < 0) {
                                checkResultVo.setCalibrationExtensionIndexBankState("未完成");
                            } else {
                                checkResultVo.setCalibrationExtensionIndexBankState("已完成-银行");
                            }
                        } else {
                            checkResultVo.setBankSalaryExtensionProportion(BigDecimal.ZERO);
                            if (checkResultVo.getExtensionBank() != null && checkResultVo.getExtensionBank().equals(BigDecimal.ZERO)) {
                                checkResultVo.setCalibrationExtensionIndexBankState("已完成-银行");
                            } else {
                                checkResultVo.setCalibrationExtensionIndexBankState("未完成");
                            }
                        }

                        //项目薪资总占比
                        checkResultVo.setProjectSalaryTotalProportion(checkResultVo.getProjectSalaryDistributionProportion().add(checkResultVo.getProjectSalaryExtensionProportion()));

                    vo.setType(checkResult.getType());
                    vo.setCheckResult(checkResultVo);
                }
            });
        }
        if("2".equals(checkResult.getType())){
            //PageUtil.startPage();
            checkResults = checkResultMapper.selectDeptList(checkResult);
            Set<String> yearSets = checkResults.stream().map(CheckResultVo::getYear).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<Long> deptSets = checkResults.stream().map(CheckResultVo::getDeptId).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<Long> companySets = checkResults.stream().map(CheckResultVo::getCompanyId).filter(Objects::nonNull).collect(Collectors.toSet());

            //查询已经写入的考核
            CheckResultVo resultVo = new CheckResultVo();
            resultVo.setYears(yearSets);
            resultVo.setDeptIds(deptSets);
            resultVo.setType(checkResult.getType());
            List<CheckResultVo> checkResultsList = checkResultMapper.selectCheckResultList(resultVo);
            //根据查询到的以录入考核结果库数据
            Map<String, Map<Integer, Map<Long, CheckResultVo>>> deptCheckResultsMap = checkResultsList.stream()
                                                .collect(Collectors.groupingBy(
                                                        CheckResultVo::getYear,  // 外层Map的key是年份
                                                        Collectors.groupingBy(
                                                                CheckResultVo::getQuarter,  // 第二层Map的key是季度
                                                                Collectors.toMap(
                                                                        CheckResultVo::getDeptId,  // 第三层Map的key是deptId
                                                                        vo -> vo,  // AnnualPlanVo作为值
                                                                        (existing, replacement) -> existing)
                                                        )
                                                ));
            checkResults.forEach(vo ->{
                            if (deptCheckResultsMap.containsKey(vo.getYear()) &&
                                deptCheckResultsMap.get(vo.getYear()).containsKey(vo.getQuarter()) &&
                                deptCheckResultsMap.get(vo.getYear()).get(vo.getQuarter()).containsKey(vo.getDeptId())
                                ){
                                CheckResultVo checkResultVo = deptCheckResultsMap.get(vo.getYear()).get(vo.getQuarter()).get(vo.getDeptId());
                                if (checkResultVo.getDistributionIndexDeviation() != null && checkResultVo.getDistributionIndexDeviation().compareTo(BigDecimal.ZERO) < 0
                                        && checkResultVo.getCompleteExtensionBank() != null
                                ){
                                    //checkResultVo.setBankSalaryExtensionProportion(checkResultVo.getCompleteExtensionBank().divide(checkResultVo.getExtensionBank(), 4, RoundingMode.HALF_UP).multiply(checkResultVo.getExtensionProportion()));
                                    BigDecimal bankSalaryExtensionProportion = checkResultVo.getCompleteExtensionBank()
                                            .divide(checkResultVo.getExtensionBank(), MathContext.DECIMAL128)
                                            .multiply(checkResultVo.getExtensionProportion())
                                            .setScale(2, RoundingMode.HALF_UP);
                                    checkResultVo.setBankSalaryExtensionProportion(bankSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : bankSalaryExtensionProportion);
                                } else {
                                    checkResultVo.setBankSalaryExtensionProportion(BigDecimal.ZERO);
                                }
                                vo.setType(checkResult.getType());
                                vo.setCheckResult(checkResultVo);
                                vo.setSource("0");
                            }
                        });

            //查询部门的年度计划
            AnnualPlanVo deptPlanVo = new AnnualPlanVo();
            deptPlanVo.setDeptIds(deptSets);
            deptPlanVo.setYears(yearSets);
            deptPlanVo.setType("2");
            List<AnnualPlanVo> deptPlanList = annualPlanService.selectAnnualPlanList(deptPlanVo);
            Map<String, Map<Long, AnnualPlanVo>> deptPlanMap = deptPlanList.stream()
                        .collect(Collectors.groupingBy(AnnualPlanVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(AnnualPlanVo::getDeptId, vo -> vo))); // 内层Map的key是deptId

            //查询公司的年度计划
            AnnualPlanVo companyPlanVo = new AnnualPlanVo();
            companyPlanVo.setCompanyIds(companySets);
            companyPlanVo.setYears(yearSets);
            companyPlanVo.setType("1");
            List<AnnualPlanVo> companyPlanList = annualPlanService.selectAnnualPlanList(companyPlanVo);
            Map<String, Map<Long, AnnualPlanVo>> companyPlanMap = companyPlanList.stream()
                        .collect(Collectors.groupingBy(AnnualPlanVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(AnnualPlanVo::getCompanyId, vo -> vo))); // 内层Map的key是companyId

            //查询部门考核配置数据
            CheckConfigSlaveVo deptConfigSlaveVo = new CheckConfigSlaveVo();
            deptConfigSlaveVo.setCorrelationIds(deptSets);
            deptConfigSlaveVo.setYears(yearSets);
            deptConfigSlaveVo.setType("1");
            List<CheckConfigSlaveVo> deptCheckConfig = checkConfigService.getCheckConfigSlaveVoList(deptConfigSlaveVo);
            Map<String, Map<Long, CheckConfigSlaveVo>> deptCheckConfigMap = deptCheckConfig.stream()
                        .collect(Collectors.groupingBy(CheckConfigSlaveVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(CheckConfigSlaveVo::getCorrelationId, vo -> vo))); // 内层Map的key是companyId

            //业绩录入
            AchievementEnterVo achievementEnterVo = new AchievementEnterVo();
            achievementEnterVo.setYears(yearSets);
            achievementEnterVo.setCompanyIds(companySets);
            List<AchievementEnterVo> achievementEnters = achievementEnterService.selectAchievementEnterSumList(achievementEnterVo);

            Map<String, Map<Long, AchievementEnterVo>> achievementEnterMap = achievementEnters.stream()
                        .filter(vo -> !"中保国信".equals(vo.getCompanyShortName()))  // 过滤掉 companyShortName 等于 "中保国信" 的数据
                        .collect(Collectors.groupingBy(AchievementEnterVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(AchievementEnterVo::getCompanyId, vo -> vo))); // 内层Map的key是companyId

            //业绩录入 中保国信算全部公司总和
            List<AchievementEnterVo> achievementEnterSumListByYear = achievementEnterService.selectAchievementEnterSumListByYear(achievementEnterVo);
            Map<String, AchievementEnterVo> achievementEnterSumListByYearMap = achievementEnterSumListByYear.stream()
                    .collect(Collectors.toMap(
                            AchievementEnterVo::getYear,  // Key 是 year
                            vo -> vo,                     // Value 是 AchievementEnterVo 对象
                            (existing, replacement) -> existing  // 处理重复的 key，选择保留现有的 value
                    ));

            checkResults.forEach(vo -> {
                if(vo.getSource() == null){
                    CheckResultVo checkResultVo = new CheckResultVo();
                    if (deptPlanMap.containsKey(vo.getYear()) && deptPlanMap.get(vo.getYear()).containsKey(vo.getDeptId())){
                        //vo.setSource("2");
                        AnnualPlanVo annualPlan = deptPlanMap.get(vo.getYear()).get(vo.getDeptId());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setTotalIndex(annualPlan.getQ1TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ1DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ1ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ1ExtensionBank());
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setTotalIndex(annualPlan.getQ2TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ2DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ2ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ2ExtensionBank());
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setTotalIndex(annualPlan.getQ3TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ3DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ3ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ3ExtensionBank());
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setTotalIndex(annualPlan.getQ4TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ4DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ4ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ4ExtensionBank());
                        }
                    } else if (companyPlanMap.containsKey(vo.getYear()) && companyPlanMap.get(vo.getYear()).containsKey(vo.getCompanyId())){
                        //vo.setSource("1");
                        AnnualPlanVo annualPlan = companyPlanMap.get(vo.getYear()).get(vo.getCompanyId());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setTotalIndex(annualPlan.getQ1TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ1DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ1ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ1ExtensionBank());
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setTotalIndex(annualPlan.getQ2TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ2DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ2ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ2ExtensionBank());
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setTotalIndex(annualPlan.getQ3TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ3DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ3ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ3ExtensionBank());
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setTotalIndex(annualPlan.getQ4TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ4DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ4ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ4ExtensionBank());
                        }
                    }
                    if (deptCheckConfigMap.containsKey(vo.getYear()) && deptCheckConfigMap.get(vo.getYear()).containsKey(vo.getDeptId())){
                        vo.setSource("2");
                        CheckConfigSlaveVo configSlaveVo = deptCheckConfigMap.get(vo.getYear()).get(vo.getDeptId());
                        checkResultVo.setAchievementWagesProportion(configSlaveVo.getAchievementWagesProportion());
                        checkResultVo.setDistributionProportion(configSlaveVo.getDistributionProportion());
                        checkResultVo.setExtensionProportion(configSlaveVo.getExtensionProportion());
                    }
                    if (achievementEnterMap.containsKey(vo.getYear()) && achievementEnterMap.get(vo.getYear()).containsKey(vo.getCompanyId())){
                        AchievementEnterVo achievementEnter = achievementEnterMap.get(vo.getYear()).get(vo.getCompanyId());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM1TotalIndex().add(achievementEnter.getM2TotalIndex().add(achievementEnter.getM3TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM1DistributionIndex().add(achievementEnter.getM2DistributionIndex().add(achievementEnter.getM3DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM1ExtensionIndex().add(achievementEnter.getM2ExtensionIndex().add(achievementEnter.getM3ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM4TotalIndex().add(achievementEnter.getM5TotalIndex().add(achievementEnter.getM6TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM4DistributionIndex().add(achievementEnter.getM5DistributionIndex().add(achievementEnter.getM6DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM4ExtensionIndex().add(achievementEnter.getM5ExtensionIndex().add(achievementEnter.getM6ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM7TotalIndex().add(achievementEnter.getM8TotalIndex().add(achievementEnter.getM9TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM7DistributionIndex().add(achievementEnter.getM8DistributionIndex().add(achievementEnter.getM9DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM7ExtensionIndex().add(achievementEnter.getM8ExtensionIndex().add(achievementEnter.getM9ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM10TotalIndex().add(achievementEnter.getM11TotalIndex().add(achievementEnter.getM12TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM10DistributionIndex().add(achievementEnter.getM11DistributionIndex().add(achievementEnter.getM12DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM10ExtensionIndex().add(achievementEnter.getM11ExtensionIndex().add(achievementEnter.getM12ExtensionIndex())));
                        }
                    }

                    //中保国信特殊出处理
                    if (vo.getCompanyShortName() != null && vo.getCompanyShortName().equals("中保国信") && achievementEnterSumListByYearMap.containsKey(vo.getYear())){
                        AchievementEnterVo achievementEnter = achievementEnterSumListByYearMap.get(vo.getYear());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM1TotalIndex().add(achievementEnter.getM2TotalIndex().add(achievementEnter.getM3TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM1DistributionIndex().add(achievementEnter.getM2DistributionIndex().add(achievementEnter.getM3DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM1ExtensionIndex().add(achievementEnter.getM2ExtensionIndex().add(achievementEnter.getM3ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM4TotalIndex().add(achievementEnter.getM5TotalIndex().add(achievementEnter.getM6TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM4DistributionIndex().add(achievementEnter.getM5DistributionIndex().add(achievementEnter.getM6DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM4ExtensionIndex().add(achievementEnter.getM5ExtensionIndex().add(achievementEnter.getM6ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM7TotalIndex().add(achievementEnter.getM8TotalIndex().add(achievementEnter.getM9TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM7DistributionIndex().add(achievementEnter.getM8DistributionIndex().add(achievementEnter.getM9DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM7ExtensionIndex().add(achievementEnter.getM8ExtensionIndex().add(achievementEnter.getM9ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM10TotalIndex().add(achievementEnter.getM11TotalIndex().add(achievementEnter.getM12TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM10DistributionIndex().add(achievementEnter.getM11DistributionIndex().add(achievementEnter.getM12DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM10ExtensionIndex().add(achievementEnter.getM11ExtensionIndex().add(achievementEnter.getM12ExtensionIndex())));
                        }
                    }

                    if (checkResultVo.getDistributionIndex() != null && !checkResultVo.getDistributionIndex().equals(BigDecimal.ZERO) &&
                            checkResultVo.getCompleteDistributionIndex() != null && checkResultVo.getDistributionProportion() != null){
                        //薪资分配占比(a)：( 本季度公司分配项目业绩(1月+2月+3月) / 本季度公司分配项目指标 ) * 考核配置分配占比
//                        checkResultVo.setProjectSalaryDistributionProportion(checkResultVo.getCompleteDistributionIndex()
//                                                                            .divide(checkResultVo.getDistributionIndex(),MathContext.DECIMAL128)
//                                                                            .multiply(checkResultVo.getDistributionProportion())
//                                                                            .setScale(2, RoundingMode.HALF_UP)
//                                                                            );

                        BigDecimal projectSalaryDistributionProportion = checkResultVo.getCompleteDistributionIndex()
                                .divide(checkResultVo.getDistributionIndex(),MathContext.DECIMAL128)
                                .multiply(checkResultVo.getDistributionProportion())
                                .setScale(2, RoundingMode.HALF_UP);
                        checkResultVo.setProjectSalaryDistributionProportion(projectSalaryDistributionProportion.compareTo(checkResultVo.getDistributionProportion()) > 0 ? checkResultVo.getDistributionProportion() : projectSalaryDistributionProportion);

                        //计算分配项目业绩偏差
                        checkResultVo.setDistributionIndexDeviation(checkResultVo.getCompleteDistributionIndex().subtract(checkResultVo.getDistributionIndex()));
                        //计算分配指标校准后完成状态
                        if (checkResultVo.getDistributionIndexDeviation() != null &&  checkResultVo.getDistributionIndexDeviation().compareTo(BigDecimal.ZERO) < 0) {
                            checkResultVo.setCalibrationDistributionIndexState("2");
                        } else {
                            checkResultVo.setCalibrationDistributionIndexState("1");
                        }
                    } else {
                        checkResultVo.setProjectSalaryDistributionProportion(BigDecimal.ZERO);
                    }
                    if(checkResultVo.getExtensionIndex() != null && !checkResultVo.getExtensionIndex().equals(BigDecimal.ZERO) &&
                            checkResultVo.getCompleteExtensionIndex() != null && checkResultVo.getExtensionProportion() != null){
                        //薪资自拓占比(b)：( 本季度公司自拓项目业绩((1月+2月+3月) / 本季度公司自拓项目指标 ) * 考核配置自拓占比
//                        checkResultVo.setProjectSalaryExtensionProportion(checkResultVo.getCompleteExtensionIndex()
//                                                                            .divide(checkResultVo.getExtensionIndex(),MathContext.DECIMAL128)
//                                                                            .multiply(checkResultVo.getExtensionProportion())
//                                                                            .setScale(2, RoundingMode.HALF_UP)
//                                                                            );

                        BigDecimal projectSalaryExtensionProportion = checkResultVo.getCompleteExtensionIndex()
                                .divide(checkResultVo.getExtensionIndex(),MathContext.DECIMAL128)
                                .multiply(checkResultVo.getExtensionProportion())
                                .setScale(2, RoundingMode.HALF_UP);
                        checkResultVo.setProjectSalaryExtensionProportion(projectSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : projectSalaryExtensionProportion);
                        //计算自拓业绩偏差
                        checkResultVo.setExtensionIndexDeviation(checkResultVo.getCompleteExtensionIndex().subtract(checkResultVo.getExtensionIndex()));
                        //计算自拓指标校准后完成状态
                        if (checkResultVo.getExtensionIndexDeviation().compareTo(BigDecimal.ZERO) < 0) {
                            checkResultVo.setCalibrationExtensionIndexProjectState("未完成");
                        } else {
                            checkResultVo.setCalibrationExtensionIndexProjectState("已完成-项目");
                        }
                    } else {
                        checkResultVo.setProjectSalaryExtensionProportion(BigDecimal.ZERO);
                        if(checkResultVo.getExtensionIndex() == null || checkResultVo.getExtensionIndex().equals(BigDecimal.ZERO)){
                            checkResultVo.setCalibrationExtensionIndexProjectState("已完成-项目");
                        }
                    }


                    if(checkResultVo.getExtensionBank() != null && !checkResultVo.getExtensionBank().equals(BigDecimal.ZERO) &&
                            checkResultVo.getCompleteExtensionBank() != null && checkResultVo.getExtensionProportion() != null
                    ){
                        //薪资自拓占比(b)：( 本季度公司自拓项目业绩((1月+2月+3月) / 本季度公司自拓项目指标 ) * 考核配置自拓占比
                        BigDecimal bankSalaryExtensionProportion = checkResultVo.getCompleteExtensionBank()
                                .divide(checkResultVo.getExtensionBank(), MathContext.DECIMAL128)
                                .multiply(checkResultVo.getExtensionProportion())
                                .setScale(2, RoundingMode.HALF_UP);
                        checkResultVo.setBankSalaryExtensionProportion(bankSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : bankSalaryExtensionProportion);
                        //计算自拓业绩偏差
                        checkResultVo.setExtensionBankDeviation(checkResultVo.getCompleteExtensionBank().subtract(checkResultVo.getExtensionBank()));
                        //计算自拓指标校准后完成状态
                        if (checkResultVo.getExtensionBankDeviation().compareTo(BigDecimal.ZERO) < 0) {
                            checkResultVo.setCalibrationExtensionIndexBankState("未完成");
                        } else {
                            checkResultVo.setCalibrationExtensionIndexBankState("已完成-银行");
                        }
                    } else {
                        checkResultVo.setBankSalaryExtensionProportion(BigDecimal.ZERO);
                        if (checkResultVo.getExtensionBank() == null || checkResultVo.getExtensionBank().equals(BigDecimal.ZERO)) {
                            checkResultVo.setCalibrationExtensionIndexBankState("已完成-银行");
                        } else {
                            checkResultVo.setCalibrationExtensionIndexBankState("未完成");
                        }
                    }

                    //项目薪资总占比
                    checkResultVo.setProjectSalaryTotalProportion(checkResultVo.getProjectSalaryDistributionProportion().add(checkResultVo.getProjectSalaryExtensionProportion()));
                    vo.setType(checkResult.getType());
                    vo.setCheckResult(checkResultVo);
                }
            });
        }
        if("1".equals(checkResult.getType())){
            //PageUtil.startPage();
            checkResults = checkResultMapper.selectCompanyList(checkResult);
            Set<String> yearSets = checkResults.stream().map(CheckResultVo::getYear).filter(Objects::nonNull).collect(Collectors.toSet());
            Set<Long> companySets = checkResults.stream().map(CheckResultVo::getCompanyId).filter(Objects::nonNull).collect(Collectors.toSet());
            //查询已经写入的考核
            CheckResultVo resultVo = new CheckResultVo();
            resultVo.setYears(yearSets);
            resultVo.setCompanyIds(companySets);
            resultVo.setType(checkResult.getType());
            List<CheckResultVo> checkResultsList = checkResultMapper.selectCheckResultList(resultVo);
            //根据查询到的以录入考核结果库数据
            Map<String, Map<Integer, Map<Long, CheckResultVo>>> companyCheckResultsMap = checkResultsList.stream()
                                                .collect(Collectors.groupingBy(
                                                        CheckResultVo::getYear,  // 外层Map的key是年份
                                                        Collectors.groupingBy(
                                                                CheckResultVo::getQuarter,  // 第二层Map的key是季度
                                                                Collectors.toMap(
                                                                        CheckResultVo::getCompanyId,  // 第三层Map的key是companyId
                                                                        vo -> vo  // AnnualPlanVo作为值
                                                                )
                                                        )
                                                ));

            checkResults.forEach(vo ->{
                if (companyCheckResultsMap.containsKey(vo.getYear()) &&
                    companyCheckResultsMap.get(vo.getYear()).containsKey(vo.getQuarter()) &&
                    companyCheckResultsMap.get(vo.getYear()).get(vo.getQuarter()).containsKey(vo.getCompanyId())
                    ){
                    CheckResultVo checkResultVo = companyCheckResultsMap.get(vo.getYear()).get(vo.getQuarter()).get(vo.getCompanyId());
                    if (checkResultVo.getDistributionIndexDeviation() != null &&  checkResultVo.getDistributionIndexDeviation().compareTo(BigDecimal.ZERO) < 0
                        && checkResultVo.getExtensionBank() != null && checkResultVo.getExtensionBank().compareTo(BigDecimal.ZERO) < 0
                            && checkResultVo.getCompleteExtensionBank() != null){
                       //checkResultVo.setBankSalaryExtensionProportion(checkResultVo.getCompleteExtensionBank().divide(checkResultVo.getExtensionBank(), 4, RoundingMode.HALF_UP).multiply(checkResultVo.getExtensionProportion()));
                        BigDecimal bankSalaryExtensionProportion = checkResultVo.getCompleteExtensionBank()
                                .divide(checkResultVo.getExtensionBank(), MathContext.DECIMAL128)
                                .multiply(checkResultVo.getExtensionProportion())
                                .setScale(2, RoundingMode.HALF_UP);
                        checkResultVo.setBankSalaryExtensionProportion(bankSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : bankSalaryExtensionProportion);
                    } else {
                        checkResultVo.setBankSalaryExtensionProportion(BigDecimal.ZERO);
                    }
                    vo.setType(checkResult.getType());
                    vo.setCheckResult(checkResultVo);
                    vo.setSource("0");
                }
            });

            //查询公司的年度计划
            AnnualPlanVo companyPlanVo = new AnnualPlanVo();
            companyPlanVo.setCompanyIds(companySets);
            companyPlanVo.setYears(yearSets);
            companyPlanVo.setType("1");
            List<AnnualPlanVo> companyPlanList = annualPlanService.selectAnnualPlanList(companyPlanVo);
            Map<String, Map<Long, AnnualPlanVo>> companyPlanMap = companyPlanList.stream()
                        .collect(Collectors.groupingBy(AnnualPlanVo::getYear,  // 外层Map的key是年份
                         Collectors.toMap(AnnualPlanVo::getCompanyId, vo -> vo))); // 内层Map的key是companyId

            //查询公司考核配置数据
            CheckConfigSlaveVo deptConfigSlaveVo = new CheckConfigSlaveVo();
            //deptConfigSlaveVo.setCorrelationIds(deptSets);
            deptConfigSlaveVo.setYears(yearSets);
            List<CheckConfigSlaveVo> companyCheckConfig = checkConfigService.getCheckConfigSlaveVoListOfCompany(deptConfigSlaveVo);
            Map<String, Map<Long, CheckConfigSlaveVo>> companyCheckConfigMap = Optional.ofNullable(companyCheckConfig)
                    .orElse(Collections.emptyList()).stream()
                    .collect(Collectors.groupingBy(CheckConfigSlaveVo::getYear,  // 外层Map的key是年份
                            Collectors.toMap(CheckConfigSlaveVo::getCompanyId, // 内层Map的key是companyId
                                    vo -> vo,
                                    (existing, replacement) ->
                                     (existing.getAchievementWagesProportion().compareTo(replacement.getAchievementWagesProportion()) >= 0) ? existing : replacement)  // 处理重复值,保留较大的值
                    ));

            //业绩录入
            AchievementEnterVo achievementEnterVo = new AchievementEnterVo();
            achievementEnterVo.setYears(yearSets);
            achievementEnterVo.setCompanyIds(companySets);
            List<AchievementEnterVo> achievementEnters = achievementEnterService.selectAchievementEnterSumList(achievementEnterVo);
            Map<String, Map<Long, AchievementEnterVo>> achievementEnterMap = achievementEnters.stream()
                    .filter(vo -> !"中保国信".equals(vo.getCompanyShortName()))  // 过滤掉 companyShortName 等于 "中保国信" 的数据
                    .collect(Collectors.groupingBy(AchievementEnterVo::getYear,  // 外层Map的key是年份
                            Collectors.toMap(AchievementEnterVo::getCompanyId, vo -> vo))); // 内层Map的key是companyId

            //业绩录入 中保国信算全部公司总和
            List<AchievementEnterVo> achievementEnterSumListByYear = achievementEnterService.selectAchievementEnterSumListByYear(achievementEnterVo);
            Map<String, AchievementEnterVo> achievementEnterSumListByYearMap = achievementEnterSumListByYear.stream()
                    .collect(Collectors.toMap(
                            AchievementEnterVo::getYear,  // Key 是 year
                            vo -> vo,                     // Value 是 AchievementEnterVo 对象
                            (existing, replacement) -> existing  // 处理重复的 key，选择保留现有的 value
                    ));

            checkResults.forEach(vo -> {
                if(vo.getSource() == null){
                    CheckResultVo checkResultVo = new CheckResultVo();
                    if (companyPlanMap.containsKey(vo.getYear()) && companyPlanMap.get(vo.getYear()).containsKey(vo.getCompanyId())){
                        //vo.setSource("2");
                        AnnualPlanVo annualPlan = companyPlanMap.get(vo.getYear()).get(vo.getCompanyId());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setTotalIndex(annualPlan.getQ1TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ1DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ1ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ1ExtensionBank());
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setTotalIndex(annualPlan.getQ2TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ2DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ2ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ2ExtensionBank());
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setTotalIndex(annualPlan.getQ3TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ3DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ3ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ3ExtensionBank());
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setTotalIndex(annualPlan.getQ4TotalIndex());
                            checkResultVo.setDistributionIndex(annualPlan.getQ4DistributionIndex());
                            checkResultVo.setExtensionIndex(annualPlan.getQ4ExtensionIndex());
                            checkResultVo.setExtensionBank(annualPlan.getQ4ExtensionBank());
                        }
                    }

                    if (companyCheckConfigMap.containsKey(vo.getYear()) && companyCheckConfigMap.get(vo.getYear()).containsKey(vo.getCompanyId())){
                        vo.setSource("2");
                        CheckConfigSlaveVo configSlaveVo = companyCheckConfigMap.get(vo.getYear()).get(vo.getCompanyId());
                        checkResultVo.setAchievementWagesProportion(configSlaveVo.getAchievementWagesProportion());
                        checkResultVo.setDistributionProportion(configSlaveVo.getDistributionProportion());
                        checkResultVo.setExtensionProportion(configSlaveVo.getExtensionProportion());
                    }
                    //中保国信单独取--运营管理部
                    if ("中保国信".equals(vo.getCompanyShortName())){
                        CheckConfigSlaveVo configSlaveVo = checkConfigService.getCheckConfigSlaveVoOfZhongBao(deptConfigSlaveVo);
                        if (configSlaveVo != null){
                            checkResultVo.setAchievementWagesProportion(configSlaveVo.getAchievementWagesProportion());
                            checkResultVo.setDistributionProportion(configSlaveVo.getDistributionProportion());
                            checkResultVo.setExtensionProportion(configSlaveVo.getExtensionProportion());
                        }
                    }

                    if (achievementEnterMap.containsKey(vo.getYear()) && achievementEnterMap.get(vo.getYear()).containsKey(vo.getCompanyId())){
                        AchievementEnterVo achievementEnter = achievementEnterMap.get(vo.getYear()).get(vo.getCompanyId());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM1TotalIndex().add(achievementEnter.getM2TotalIndex().add(achievementEnter.getM3TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM1DistributionIndex().add(achievementEnter.getM2DistributionIndex().add(achievementEnter.getM3DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM1ExtensionIndex().add(achievementEnter.getM2ExtensionIndex().add(achievementEnter.getM3ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM4TotalIndex().add(achievementEnter.getM5TotalIndex().add(achievementEnter.getM6TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM4DistributionIndex().add(achievementEnter.getM5DistributionIndex().add(achievementEnter.getM6DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM4ExtensionIndex().add(achievementEnter.getM5ExtensionIndex().add(achievementEnter.getM6ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM7TotalIndex().add(achievementEnter.getM8TotalIndex().add(achievementEnter.getM9TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM7DistributionIndex().add(achievementEnter.getM8DistributionIndex().add(achievementEnter.getM9DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM7ExtensionIndex().add(achievementEnter.getM8ExtensionIndex().add(achievementEnter.getM9ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM10TotalIndex().add(achievementEnter.getM11TotalIndex().add(achievementEnter.getM12TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM10DistributionIndex().add(achievementEnter.getM11DistributionIndex().add(achievementEnter.getM12DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM10ExtensionIndex().add(achievementEnter.getM11ExtensionIndex().add(achievementEnter.getM12ExtensionIndex())));
                        }
                    }
                    //中保国信特殊出处理
                    if (vo.getCompanyShortName().equals("中保国信") && achievementEnterSumListByYearMap.containsKey(vo.getYear())){
                        AchievementEnterVo achievementEnter = achievementEnterSumListByYearMap.get(vo.getYear());
                        if (vo.getQuarter() == 1){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM1TotalIndex().add(achievementEnter.getM2TotalIndex().add(achievementEnter.getM3TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM1DistributionIndex().add(achievementEnter.getM2DistributionIndex().add(achievementEnter.getM3DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM1ExtensionIndex().add(achievementEnter.getM2ExtensionIndex().add(achievementEnter.getM3ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 2){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM4TotalIndex().add(achievementEnter.getM5TotalIndex().add(achievementEnter.getM6TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM4DistributionIndex().add(achievementEnter.getM5DistributionIndex().add(achievementEnter.getM6DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM4ExtensionIndex().add(achievementEnter.getM5ExtensionIndex().add(achievementEnter.getM6ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 3){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM7TotalIndex().add(achievementEnter.getM8TotalIndex().add(achievementEnter.getM9TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM7DistributionIndex().add(achievementEnter.getM8DistributionIndex().add(achievementEnter.getM9DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM7ExtensionIndex().add(achievementEnter.getM8ExtensionIndex().add(achievementEnter.getM9ExtensionIndex())));
                        }
                        if (vo.getQuarter() == 4){
                            checkResultVo.setCompleteTotalIndex(achievementEnter.getM10TotalIndex().add(achievementEnter.getM11TotalIndex().add(achievementEnter.getM12TotalIndex())));
                            checkResultVo.setCompleteDistributionIndex(achievementEnter.getM10DistributionIndex().add(achievementEnter.getM11DistributionIndex().add(achievementEnter.getM12DistributionIndex())));
                            checkResultVo.setCompleteExtensionIndex(achievementEnter.getM10ExtensionIndex().add(achievementEnter.getM11ExtensionIndex().add(achievementEnter.getM12ExtensionIndex())));
                        }
                    }
                    if (checkResultVo.getDistributionIndex() != null && !checkResultVo.getDistributionIndex().equals(BigDecimal.ZERO) &&
                            checkResultVo.getCompleteDistributionIndex() != null && checkResultVo.getDistributionProportion() != null){
                        //薪资分配占比(a)：( 本季度公司分配项目业绩(1月+2月+3月) / 本季度公司分配项目指标 ) * 考核配置分配占比
//                        checkResultVo.setProjectSalaryDistributionProportion(checkResultVo.getCompleteDistributionIndex()
//                                .divide(checkResultVo.getDistributionIndex(),MathContext.DECIMAL128)
//                                .multiply(checkResultVo.getDistributionProportion())
//                                .setScale(2, RoundingMode.HALF_UP)
//                        );

                        BigDecimal projectSalaryDistributionProportion = checkResultVo.getCompleteDistributionIndex()
                                .divide(checkResultVo.getDistributionIndex(),MathContext.DECIMAL128)
                                .multiply(checkResultVo.getDistributionProportion())
                                .setScale(2, RoundingMode.HALF_UP);
                        checkResultVo.setProjectSalaryDistributionProportion(projectSalaryDistributionProportion.compareTo(checkResultVo.getDistributionProportion()) > 0 ? checkResultVo.getDistributionProportion() : projectSalaryDistributionProportion);
                        //计算分配项目业绩偏差
                        checkResultVo.setDistributionIndexDeviation(checkResultVo.getCompleteDistributionIndex().subtract(checkResultVo.getDistributionIndex()));
                        //计算分配指标校准后完成状态
                        if (checkResultVo.getDistributionIndexDeviation() != null &&  checkResultVo.getDistributionIndexDeviation().compareTo(BigDecimal.ZERO) < 0) {
                            checkResultVo.setCalibrationDistributionIndexState("2");
                        } else {
                            checkResultVo.setCalibrationDistributionIndexState("1");
                        }
                    } else {
                        checkResultVo.setProjectSalaryDistributionProportion(BigDecimal.ZERO);
                    }
                    if(checkResultVo.getExtensionIndex() != null && !checkResultVo.getExtensionIndex().equals(BigDecimal.ZERO) &&
                            checkResultVo.getCompleteExtensionIndex() != null && checkResultVo.getExtensionProportion() != null){
                        //薪资自拓占比(b)：( 本季度公司自拓项目业绩((1月+2月+3月) / 本季度公司自拓项目指标 ) * 考核配置自拓占比
//                        checkResultVo.setProjectSalaryExtensionProportion(checkResultVo.getCompleteExtensionIndex()
//                                .divide(checkResultVo.getExtensionIndex(),MathContext.DECIMAL128)
//                                .multiply(checkResultVo.getExtensionProportion())
//                                .setScale(2, RoundingMode.HALF_UP)
//                        );
                        BigDecimal projectSalaryExtensionProportion = checkResultVo.getCompleteExtensionIndex()
                                .divide(checkResultVo.getExtensionIndex(),MathContext.DECIMAL128)
                                .multiply(checkResultVo.getExtensionProportion())
                                .setScale(2, RoundingMode.HALF_UP);
                        checkResultVo.setProjectSalaryExtensionProportion(projectSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : projectSalaryExtensionProportion);
                        //计算自拓业绩偏差
                        checkResultVo.setExtensionIndexDeviation(checkResultVo.getCompleteExtensionIndex().subtract(checkResultVo.getExtensionIndex()));
                        //计算自拓指标校准后完成状态
                        if (checkResultVo.getExtensionIndexDeviation().compareTo(BigDecimal.ZERO) < 0) {
                            checkResultVo.setCalibrationExtensionIndexProjectState("未完成");
                        } else {
                            checkResultVo.setCalibrationExtensionIndexProjectState("已完成-项目");
                        }
                    } else {
                        checkResultVo.setProjectSalaryExtensionProportion(BigDecimal.ZERO);
                        if(checkResultVo.getExtensionIndex() == null || !checkResultVo.getExtensionIndex().equals(BigDecimal.ZERO)){
                            checkResultVo.setCalibrationExtensionIndexProjectState("已完成-项目");
                        }
                    }

                    if(checkResultVo.getExtensionBank() != null && !checkResultVo.getExtensionBank().equals(BigDecimal.ZERO) &&
                            checkResultVo.getCompleteExtensionBank() != null && checkResultVo.getExtensionProportion() != null
                    ){
                        //薪资自拓占比(b)：( 本季度公司自拓项目业绩((1月+2月+3月) / 本季度公司自拓项目指标 ) * 考核配置自拓占比
                        BigDecimal bankSalaryExtensionProportion = checkResultVo.getCompleteExtensionBank()
                                .divide(checkResultVo.getExtensionBank(), MathContext.DECIMAL128)
                                .multiply(checkResultVo.getExtensionProportion())
                                .setScale(2, RoundingMode.HALF_UP);
                        checkResultVo.setBankSalaryExtensionProportion(bankSalaryExtensionProportion.compareTo(checkResultVo.getExtensionProportion()) > 0 ? checkResultVo.getExtensionProportion() : bankSalaryExtensionProportion);
                        //计算自拓业绩偏差
                        checkResultVo.setExtensionBankDeviation(checkResultVo.getCompleteExtensionBank().subtract(checkResultVo.getExtensionBank()));
                        //计算自拓指标校准后完成状态
                        if (checkResultVo.getExtensionBankDeviation().compareTo(BigDecimal.ZERO) < 0) {
                            checkResultVo.setCalibrationExtensionIndexBankState("未完成");
                        } else {
                            checkResultVo.setCalibrationExtensionIndexBankState("已完成-银行");
                        }
                    } else {
                        checkResultVo.setBankSalaryExtensionProportion(BigDecimal.ZERO);
                        if (checkResultVo.getExtensionBank() != null && checkResultVo.getExtensionBank().equals(BigDecimal.ZERO)) {
                            checkResultVo.setCalibrationExtensionIndexBankState("已完成-银行");
                        } else {
                            checkResultVo.setCalibrationExtensionIndexBankState("未完成");
                        }
                    }

                    //项目薪资总占比
                    checkResultVo.setProjectSalaryTotalProportion(checkResultVo.getProjectSalaryDistributionProportion().add(checkResultVo.getProjectSalaryExtensionProportion()));
                    vo.setType(checkResult.getType());
                    vo.setCheckResult(checkResultVo);
                }
            });
        }

        if (checkResult.getCalibrationExtensionIndexBankState() != null && !checkResult.getCalibrationExtensionIndexBankState().isEmpty()){
            checkResults = checkResults.stream()
                    .filter(result -> checkResult.getCalibrationExtensionIndexBankState().equals(result.getCheckResult().getCalibrationExtensionIndexBankState()))
                    .collect(Collectors.toList());
        }
        if (checkResult.getCalibrationExtensionIndexProjectState() != null && !checkResult.getCalibrationExtensionIndexProjectState().isEmpty()){
            checkResults = checkResults.stream()
                    .filter(result -> checkResult.getCalibrationExtensionIndexProjectState().equals(result.getCheckResult().getCalibrationExtensionIndexProjectState()))
                    .collect(Collectors.toList());
        }
        if (checkResult.getCalibrationDistributionIndexState() != null && !checkResult.getCalibrationDistributionIndexState().isEmpty()){
            checkResults = checkResults.stream()
                    .filter(result -> checkResult.getCalibrationDistributionIndexState().equals(result.getCheckResult().getCalibrationDistributionIndexState()))
                    .collect(Collectors.toList());
        }
        return checkResults.stream()
                .filter(vo -> vo.getCheckResult().getTotalIndex() != null) // 过滤条件
                .peek(vo -> {
                    if (vo.getCheckResult().getExtensionProportion() == null){
                        vo.getCheckResult().setCalibrationExtensionIndexBankState("-");
                        vo.getCheckResult().setCalibrationExtensionIndexProjectState("-");
                    }
                })
                .collect(Collectors.toList()); // 收集结果;
    }

    /**
     * 新增考核结果
     *
     * @param checkResult 考核结果
     * @return 结果
     */
    @Override
    public int insertCheckResult(CheckResultVo checkResult)
    {
        if(checkResult.getCheckResult().getId() != null){
            return updateCheckResult(checkResult);
        }

        //查询前几个季度的数据
//        List<CheckResultVo> listBefor = checkResultMapper.selectCheckResultListBefor(checkResult);
//
//        BigDecimal distributionIndexDeviationYear = BigDecimal.ZERO;
//        BigDecimal extensionIndexDeviationYear = BigDecimal.ZERO;
//        BigDecimal extensionBankDeviationYear = BigDecimal.ZERO;
//
//        if (listBefor != null){
//            for (CheckResultVo vo : listBefor) {
//                BigDecimal distributionIndex = Optional.ofNullable(vo.getDistributionIndex()).orElse(BigDecimal.ZERO);
//                BigDecimal extensionIndex = Optional.ofNullable(vo.getExtensionIndex()).orElse(BigDecimal.ZERO);
//                BigDecimal extensionBank = Optional.ofNullable(vo.getExtensionBank()).orElse(BigDecimal.ZERO);
//
//                BigDecimal completeDistributionIndex = Optional.ofNullable(vo.getCompleteDistributionIndex()).orElse(BigDecimal.ZERO);
//                BigDecimal completeExtensionIndex = Optional.ofNullable(vo.getCompleteExtensionIndex()).orElse(BigDecimal.ZERO);
//                BigDecimal completeExtensionBank = Optional.ofNullable(vo.getCompleteExtensionBank()).orElse(BigDecimal.ZERO);
//
//                BigDecimal calibrationDistributionIndex = Optional.ofNullable(vo.getCalibrationDistributionIndex()).orElse(BigDecimal.ZERO);
//                BigDecimal calibrationExtensionIndex = Optional.ofNullable(vo.getCalibrationExtensionIndex()).orElse(BigDecimal.ZERO);
//                BigDecimal calibrationExtensionBank = Optional.ofNullable(vo.getCalibrationExtensionBank()).orElse(BigDecimal.ZERO);
//
//                BigDecimal distributionIndexTemp = completeDistributionIndex.subtract(distributionIndex).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeDistributionIndex.subtract(distributionIndex);
//                BigDecimal extensionIndexTemp = completeExtensionIndex.subtract(extensionIndex).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeExtensionIndex.subtract(extensionIndex);
//                BigDecimal extensionBankTemp = completeExtensionBank.subtract(extensionBank).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : completeExtensionBank.subtract(extensionBank);
//
//                distributionIndexDeviationYear = distributionIndexDeviationYear.subtract(calibrationDistributionIndex).add(distributionIndexTemp);
//                extensionIndexDeviationYear = extensionIndexDeviationYear.subtract(calibrationExtensionIndex).add(extensionIndexTemp);
//                extensionBankDeviationYear = extensionBankDeviationYear.subtract(calibrationExtensionBank).add(extensionBankTemp);
//            }
//        }
//        //新增时初始化
//        checkResult.getCheckResult().setDistributionIndexDeviationYear(distributionIndexDeviationYear);
//        checkResult.getCheckResult().setExtensionIndexDeviationYear(extensionIndexDeviationYear);
//        checkResult.getCheckResult().setExtensionBankDeviationYear(extensionBankDeviationYear);

        if(checkResult.getCheckResult().getExtensionBank() != null && !checkResult.getCheckResult().getExtensionBank().equals(BigDecimal.ZERO) &&
                checkResult.getCheckResult().getCompleteExtensionBank() != null && checkResult.getCheckResult().getExtensionProportion() != null
        ){
            //薪资自拓占比(b)：( 本季度公司自拓项目业绩((1月+2月+3月) / 本季度公司自拓项目指标 ) * 考核配置自拓占比
            BigDecimal bankSalaryExtensionProportion = checkResult.getCheckResult().getCompleteExtensionBank()
                    .divide(checkResult.getCheckResult().getExtensionBank(), MathContext.DECIMAL128)
                    .multiply(checkResult.getCheckResult().getExtensionProportion())
                    .setScale(2, RoundingMode.HALF_UP);
            checkResult.getCheckResult().setBankSalaryExtensionProportion(bankSalaryExtensionProportion.compareTo(checkResult.getCheckResult().getExtensionProportion()) > 0 ? checkResult.getCheckResult().getExtensionProportion() : bankSalaryExtensionProportion);
            //计算自拓业绩偏差
            checkResult.getCheckResult().setExtensionBankDeviation(checkResult.getCheckResult().getCompleteExtensionBank().subtract(checkResult.getCheckResult().getExtensionBank()));
            //计算自拓指标校准后完成状态
            if (checkResult.getCheckResult().getExtensionBankDeviation().compareTo(BigDecimal.ZERO) < 0) {
                checkResult.getCheckResult().setCalibrationExtensionIndexBankState("未完成");
            } else {
                checkResult.getCheckResult().setCalibrationExtensionIndexBankState("已完成-银行");
            }
        } else {
            checkResult.getCheckResult().setBankSalaryExtensionProportion(BigDecimal.ZERO);
            if (checkResult.getExtensionBank() == null || !checkResult.getExtensionBank().equals(BigDecimal.ZERO)) {
                checkResult.setCalibrationExtensionIndexBankState("已完成-银行");
            }
        }

        checkResult.setCreateBy(getLoginUser().getUsername());
        checkResult.setCreateTime(DateUtils.getNowDate());
        return checkResultMapper.insertCheckResult(checkResult);
    }

    /**
     * 修改考核结果
     *
     * @param checkResult 考核结果
     * @return 结果
     */
    @Override
    public int updateCheckResult(CheckResultVo checkResult)
    {
        checkResult.setUpdateBy(getLoginUser().getUsername());
        checkResult.setUpdateTime(DateUtils.getNowDate());
        return checkResultMapper.updateCheckResult(checkResult);
    }

    @Override
    public int replaceCheckResult(List<CheckResultVo> checkResults){
        if (checkResults.isEmpty()) {
            return 1;
        }
        checkResults.forEach(vo -> {
            if (vo.getId() == null){
                vo.setCreateBy(getLoginUser().getUsername());
                vo.setCreateTime(DateUtils.getNowDate());
            }
            vo.setUpdateBy(getLoginUser().getUsername());
            vo.setUpdateTime(DateUtils.getNowDate());
        });
        return checkResultMapper.replaceCheckResult(checkResults);
    }

    /**
     * 查询已经存入数据库的数据
     * @param checkResult 考核结果
     * @return
     */
    @Override
    public List<CheckResultVo> selectCheckResultListofGenerate(CheckResultVo checkResult){
        return checkResultMapper.selectCheckResultList(checkResult);
    }

    /**
     * 批量删除考核结果
     *
     * @param ids 需要删除的考核结果主键
     * @return 结果
     */
    @Override
    public int deleteCheckResultByIds(Long[] ids)
    {
        int i = checkResultMapper.deleteCheckResultByIds(ids);
        return i;
    }

    /**
     * 删除考核结果信息
     *
     * @param id 考核结果主键
     * @return 结果
     */
    @Override
    public int deleteCheckResultById(Long id)
    {
        return checkResultMapper.deleteCheckResultById(id);
    }

    @Override
    public CheckResult getCheckResultTotal(CheckResultVo checkResult){
        CheckResultVo checkResultVo = new CheckResultVo();
        checkResult.setQuarter(null);
        List<CheckResultVo> checkResultVos = checkResultMapper.selectCheckResultList(checkResult);
        BigDecimal completeExtensionBank = checkResultVos.stream()
                .map(vo -> vo.getCompleteExtensionBank() != null ? vo.getCompleteExtensionBank() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if ("1".equals(checkResult.getType())){
            SysCompanyVo sysCompanyVo = companyService.selectSysCompanyById(checkResult.getCompanyId());

            checkResultVo.setCompanyName(sysCompanyVo.getCompanyName());
            checkResultVo.setCompanyShortName(sysCompanyVo.getCompanyShortName());
        }
        if ("2".equals(checkResult.getType())){
            SysDept sysDept = deptService.selectDeptById(checkResult.getDeptId());
            SysCompanyVo sysCompanyVo = companyService.selectSysCompanyById(checkResult.getCompanyId());

            checkResultVo.setCompanyName(sysCompanyVo.getCompanyName());
            checkResultVo.setCompanyShortName(sysCompanyVo.getCompanyShortName());
            checkResultVo.setDeptName(sysDept.getDeptName());
        }
        if ("3".equals(checkResult.getType())){
            SysUser sysUser = userService.selectUserById(checkResult.getUserId());
            SysDept sysDept = deptService.selectDeptById(checkResult.getDeptId());
            SysCompanyVo sysCompanyVo = companyService.selectSysCompanyById(checkResult.getCompanyId());

            checkResultVo.setCompanyName(sysCompanyVo.getCompanyName());
            checkResultVo.setCompanyShortName(sysCompanyVo.getCompanyShortName());
            checkResultVo.setDeptName(sysDept.getDeptName());
            checkResultVo.setNickName(sysUser.getNickName());
        }

        //查询用户的年度计划
        AnnualPlanVo userPlanVo = new AnnualPlanVo();
        userPlanVo.setUserId(checkResult.getUserId());
        userPlanVo.setYear(checkResult.getYear());
        userPlanVo.setType("3");
        AnnualPlanVo userPlan = annualPlanService.getAnnualPlanVo(userPlanVo);

        //查询部门的年度计划
        AnnualPlanVo deptPlanVo = new AnnualPlanVo();
        deptPlanVo.setDeptId(checkResult.getDeptId());
        deptPlanVo.setYear(checkResult.getYear());
        deptPlanVo.setType("2");
        AnnualPlanVo deptPlan = annualPlanService.getAnnualPlanVo(deptPlanVo);

        //查询公司的年度计划
        AnnualPlanVo companyPlanVo = new AnnualPlanVo();
        companyPlanVo.setCompanyId(checkResult.getCompanyId());
        companyPlanVo.setYear(checkResult.getYear());
        companyPlanVo.setType("1");
        AnnualPlanVo companyPlan = annualPlanService.getAnnualPlanVo(companyPlanVo);

        //业绩录入
        AchievementEnterVo achievementEnterVo = new AchievementEnterVo();
        achievementEnterVo.setYear(checkResult.getYear());
        achievementEnterVo.setCompanyId(checkResult.getCompanyId());
        AchievementEnterVo achievementEnter = achievementEnterService.getAchievementEnterSum(achievementEnterVo);

        if (checkResultVo.getCompanyShortName() != null && checkResultVo.getCompanyShortName().equals("中保国信")){
            achievementEnter = achievementEnterService.getAchievementEnterSumByYear(achievementEnterVo);
        }


        AnnualPlanVo plan = new AnnualPlanVo();
        //根据查询到的动态数据(年度计划) 判断是否有值  (用户没有配置年度计划和考核配置，则以部门配置为准，没有部门配置，则以公司配置为准)
        if (userPlan != null && checkResult.getUserId() != null){
            plan = userPlan;
        } else if (deptPlan != null && checkResult.getDeptId() != null){
            plan = deptPlan;
        } else if (companyPlan != null && checkResult.getCompanyId() != null){
            plan = companyPlan;
        }
        if (achievementEnter != null){
            checkResultVo.setCompleteTotalIndex(
                    (achievementEnter.getM1TotalIndex() != null ? achievementEnter.getM1TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM2TotalIndex() != null ? achievementEnter.getM2TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM3TotalIndex() != null ? achievementEnter.getM3TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM4TotalIndex() != null ? achievementEnter.getM4TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM5TotalIndex() != null ? achievementEnter.getM5TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM6TotalIndex() != null ? achievementEnter.getM6TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM7TotalIndex() != null ? achievementEnter.getM7TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM8TotalIndex() != null ? achievementEnter.getM8TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM9TotalIndex() != null ? achievementEnter.getM9TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM10TotalIndex() != null ? achievementEnter.getM10TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM11TotalIndex() != null ? achievementEnter.getM11TotalIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM12TotalIndex() != null ? achievementEnter.getM12TotalIndex() : BigDecimal.ZERO)
            );
            checkResultVo.setCompleteDistributionIndex(
                    (achievementEnter.getM1DistributionIndex() != null ? achievementEnter.getM1DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM2DistributionIndex() != null ? achievementEnter.getM2DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM3DistributionIndex() != null ? achievementEnter.getM3DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM4DistributionIndex() != null ? achievementEnter.getM4DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM5DistributionIndex() != null ? achievementEnter.getM5DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM6DistributionIndex() != null ? achievementEnter.getM6DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM7DistributionIndex() != null ? achievementEnter.getM7DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM8DistributionIndex() != null ? achievementEnter.getM8DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM9DistributionIndex() != null ? achievementEnter.getM9DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM10DistributionIndex() != null ? achievementEnter.getM10DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM11DistributionIndex() != null ? achievementEnter.getM11DistributionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM12DistributionIndex() != null ? achievementEnter.getM12DistributionIndex() : BigDecimal.ZERO)
            );
            checkResultVo.setCompleteExtensionIndex(
                    (achievementEnter.getM1ExtensionIndex() != null ? achievementEnter.getM1ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM2ExtensionIndex() != null ? achievementEnter.getM2ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM3ExtensionIndex() != null ? achievementEnter.getM3ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM4ExtensionIndex() != null ? achievementEnter.getM4ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM5ExtensionIndex() != null ? achievementEnter.getM5ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM6ExtensionIndex() != null ? achievementEnter.getM6ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM7ExtensionIndex() != null ? achievementEnter.getM7ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM8ExtensionIndex() != null ? achievementEnter.getM8ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM9ExtensionIndex() != null ? achievementEnter.getM9ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM10ExtensionIndex() != null ? achievementEnter.getM10ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM11ExtensionIndex() != null ? achievementEnter.getM11ExtensionIndex() : BigDecimal.ZERO)
                            .add(achievementEnter.getM12ExtensionIndex() != null ? achievementEnter.getM12ExtensionIndex() : BigDecimal.ZERO)
            );
        } else {
            checkResultVo.setCompleteTotalIndex(BigDecimal.ZERO);
            checkResultVo.setCompleteDistributionIndex(BigDecimal.ZERO);
            checkResultVo.setCompleteExtensionIndex(BigDecimal.ZERO);
        }

        checkResultVo.setTotalIndex(
                (plan.getQ1TotalIndex() != null ? plan.getQ1TotalIndex() : BigDecimal.ZERO)
                        .add(plan.getQ2TotalIndex() != null ? plan.getQ2TotalIndex() : BigDecimal.ZERO)
                        .add(plan.getQ3TotalIndex() != null ? plan.getQ3TotalIndex() : BigDecimal.ZERO)
                        .add(plan.getQ4TotalIndex() != null ? plan.getQ4TotalIndex() : BigDecimal.ZERO)
        );
        checkResultVo.setDistributionIndex(
                (plan.getQ1DistributionIndex() != null ? plan.getQ1DistributionIndex() : BigDecimal.ZERO)
                        .add(plan.getQ2DistributionIndex() != null ? plan.getQ2DistributionIndex() : BigDecimal.ZERO)
                        .add(plan.getQ3DistributionIndex() != null ? plan.getQ3DistributionIndex() : BigDecimal.ZERO)
                        .add(plan.getQ4DistributionIndex() != null ? plan.getQ4DistributionIndex() : BigDecimal.ZERO)
        );
        checkResultVo.setExtensionIndex(
                (plan.getQ1ExtensionIndex() != null ? plan.getQ1ExtensionIndex() : BigDecimal.ZERO)
                        .add(plan.getQ2ExtensionIndex() != null ? plan.getQ2ExtensionIndex() : BigDecimal.ZERO)
                        .add(plan.getQ3ExtensionIndex() != null ? plan.getQ3ExtensionIndex() : BigDecimal.ZERO)
                        .add(plan.getQ4ExtensionIndex() != null ? plan.getQ4ExtensionIndex() : BigDecimal.ZERO)
        );
        checkResultVo.setExtensionBank(
                (plan.getQ1ExtensionBank() != null ? plan.getQ1ExtensionBank() : BigDecimal.ZERO)
                        .add(plan.getQ2ExtensionBank() != null ? plan.getQ2ExtensionBank() : BigDecimal.ZERO)
                        .add(plan.getQ3ExtensionBank() != null ? plan.getQ3ExtensionBank() : BigDecimal.ZERO)
                        .add(plan.getQ4ExtensionBank() != null ? plan.getQ4ExtensionBank() : BigDecimal.ZERO)
        );
        checkResultVo.setCompleteExtensionBank(completeExtensionBank);
        return checkResultVo;
    }

    @Override
    public List<CheckResultVo> getCompanyCheckResultList(CheckResultVo checkResultVo){
        //PageUtil.startPage();
        //获取用户权限公司
        LoginUser loginUser = getLoginUser();
        List<Long> authorityCompanyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.PERFORMANCE.getCode());
        if(authorityCompanyIds.isEmpty()){
            return new ArrayList<>();
        }
        checkResultVo.setAuthorityCompanyIds(authorityCompanyIds);

        List<CheckResultVo> checkResults = checkResultMapper.selectCompanyList(checkResultVo);
        Set<Long> companySets = checkResults.stream().map(CheckResultVo::getCompanyId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<String> yearSets = checkResults.stream().map(CheckResultVo::getYear).filter(Objects::nonNull).collect(Collectors.toSet());
        //查询已经写入的考核
        CheckResultVo resultVo = new CheckResultVo();
        resultVo.setYears(yearSets);
        resultVo.setCompanyIds(companySets);
        resultVo.setType("1");
        List<CheckResultVo> checkResultsList = checkResultMapper.selectCheckResultList(resultVo);
        //根据查询到的以录入考核结果库数据
        Map<String, Map<Integer, Map<Long, CheckResultVo>>> companyCheckResultsMap = checkResultsList.stream()
                .collect(Collectors.groupingBy(
                        CheckResultVo::getYear,  // 外层Map的key是年份
                        Collectors.groupingBy(
                                CheckResultVo::getQuarter,  // 第二层Map的key是季度
                                Collectors.toMap(
                                        CheckResultVo::getCompanyId,  // 第三层Map的key是comapnyId
                                        vo -> vo  // AnnualPlanVo作为值
                                )
                        )
                ));

        checkResults.forEach(vo ->{
            if (companyCheckResultsMap.containsKey(vo.getYear()) &&
                    companyCheckResultsMap.get(vo.getYear()).containsKey(vo.getQuarter()) &&
                    companyCheckResultsMap.get(vo.getYear()).get(vo.getQuarter()).containsKey(vo.getCompanyId())
            ){
                CheckResultVo checkResult = companyCheckResultsMap.get(vo.getYear()).get(vo.getQuarter()).get(vo.getCompanyId());
                vo.setId(checkResult.getId());
                vo.setGenerateStatus("2");
                vo.setState(checkResult.getState());
                vo.setConfirmStatus(checkResult.getConfirmStatus());
                vo.setGenerateName(checkResult.getGenerateName());
                vo.setCreateTime(checkResult.getCreateTime());
                vo.setUpdateTime(checkResult.getUpdateTime());
            } else {
                vo.setGenerateStatus("1");
                vo.setState("1");
                vo.setConfirmStatus("1");
            }
        });
        if(checkResultVo.getState() != null && !checkResultVo.getState().isEmpty()){
            checkResults.stream()
                    .filter(vo -> checkResultVo.getState().equals(vo.getState()))
                    .collect(Collectors.toList());
        }
        return checkResults;
    }

    @Override
    @Transactional
    public int insertCheckResultProcess(CheckResultVo checkResult){
        int i = processService.deleteKhProcessByProcessId(checkResult.getProcessId());
        ArrayList<KhProcess> khProcessList = new ArrayList<>();
        if (checkResult.getIds() != null && !checkResult.getIds().isEmpty()){
            checkResult.getIds().forEach(id -> {
                KhProcess process = new KhProcess();
                process.setType("4");
                process.setProcessId(checkResult.getProcessId());
                process.setCorrelationId(id);
                process.setCreateBy(getLoginUser().getUsername());
                process.setCreateTime(DateUtils.getNowDate());
                khProcessList.add(process);
            });
        }
        processService.batchInsert(khProcessList);


//        CheckResultVo checkResultVo = new CheckResultVo();
//        checkResultVo.setIds(checkResult.getIds());
//        List<CheckResultVo> checkResultVos = checkResultMapper.selectCheckResultList(checkResultVo);
//        checkResultVos.forEach( vo -> {
//            if (i == 0){
//                //查询部门集合
//                CheckResultVo deptCheckResulet = new CheckResultVo();
//                deptCheckResulet.setQuarters(Collections.singleton(vo.getQuarter()));
//                deptCheckResulet.setYears(Collections.singleton(vo.getYear()));
//                deptCheckResulet.setCompanyIds(Collections.singleton(vo.getCompanyId()));
//                deptCheckResulet.setType("2");
//                List<CheckResultVo> deptCheckResultVos = selectCheckResultList(deptCheckResulet);
//                //查询
//                CheckResultVo userCheckResulet = new CheckResultVo();
//                userCheckResulet.setQuarters(Collections.singleton(vo.getQuarter()));
//                userCheckResulet.setYears(Collections.singleton(vo.getYear()));
//                userCheckResulet.setCompanyIds(Collections.singleton(vo.getCompanyId()));
//                userCheckResulet.setType("3");
//                List<CheckResultVo> userCheckResultVos = selectCheckResultList(userCheckResulet);
//
//                deptCheckResultVos.addAll(userCheckResultVos);
//                replaceCheckResult(deptCheckResultVos);
//            }
//
//            vo.setState(checkResult.getState());
//            vo.setUpdateBy(getLoginUser().getUsername());
//            vo.setUpdateTime(DateUtils.getNowDate());
//            checkResultMapper.updateCheckResultList(vo);
//        });
        return 1;
    }
    /**
     *
     */
    @Override
    public int generateDeptUserCheckResult(CheckResultVo checkResult){
        CheckResultVo checkResultVo = new CheckResultVo();
        checkResultVo.setIds(checkResult.getIds());
        List<CheckResultVo> checkResultVos = checkResultMapper.selectCheckResultList(checkResultVo);
        checkResultVos.forEach( vo -> {
                //查询部门集合
                CheckResultVo deptCheckResulet = new CheckResultVo();
                deptCheckResulet.setQuarters(Collections.singleton(vo.getQuarter()));
                deptCheckResulet.setYears(Collections.singleton(vo.getYear()));
                deptCheckResulet.setCompanyIds(Collections.singleton(vo.getCompanyId()));
                deptCheckResulet.setType("2");
                List<CheckResultVo> deptCheckResultVos = selectCheckResultList(deptCheckResulet);
                //查询
                CheckResultVo userCheckResulet = new CheckResultVo();
                userCheckResulet.setQuarters(Collections.singleton(vo.getQuarter()));
                userCheckResulet.setYears(Collections.singleton(vo.getYear()));
                userCheckResulet.setCompanyIds(Collections.singleton(vo.getCompanyId()));
                userCheckResulet.setType("3");
                List<CheckResultVo> userCheckResultVos = selectCheckResultList(userCheckResulet);

                deptCheckResultVos.addAll(userCheckResultVos);
                replaceCheckResult(deptCheckResultVos);

            vo.setState(checkResult.getState());
            vo.setUpdateBy(getLoginUser().getUsername());
            vo.setUpdateTime(DateUtils.getNowDate());
            checkResultMapper.updateCheckResultList(vo);
        });
        return 1;
    }

    @Override
    public int passCheckResult(CheckResultVo checkResult){
        List<CheckResultVo> checkResultVos = checkResultMapper.selectCheckResultList(checkResult);
        checkResultVos.forEach(vo ->{
            vo.setConfirmStatus("3");
            vo.setState("3");
            vo.setUpdateBy(getLoginUser().getUsername());
            vo.setUpdateTime(DateUtils.getNowDate());
            checkResultMapper.updateCheckResultList(vo);
        });
        return 1;
    }

    @Override
    public int unpassCheckResult(CheckResultVo checkResult){
        List<CheckResultVo> checkResultVos = checkResultMapper.selectCheckResultList(checkResult);
        checkResultVos.forEach(vo ->{
            vo.setConfirmStatus("1");
            vo.setState("4");
            vo.setUpdateBy(getLoginUser().getUsername());
            vo.setUpdateTime(DateUtils.getNowDate());
            checkResultMapper.updateCheckResultList(vo);
        });
        return 1;
    }

    @Override
    public int resetCheckResult(CheckResultVo checkResult){
        return checkResultMapper.resetCheckResult(checkResult);
    }

    @Override
    public List<CheckResultVo> selectCheckResultNoAuditing(CheckResultVo checkResult){
        return checkResultMapper.selectCheckResultNoAuditing(checkResult);
    }

    @Override
    public List<CheckResultVo> selectCheckResultsProcess(CheckResultVo checkResult){
        CheckResultVo checkResultVo = new CheckResultVo();
        checkResultVo.setIds(checkResult.getIds());
        checkResultVo.setProcessId(checkResult.getProcessId());
        List<CheckResultVo> checkResultVos = checkResultMapper.selectCheckResultList(checkResultVo);
        checkResultVos.forEach( vo-> {
            vo.setType(checkResult.getType());
        });
        PageUtil.startPage();
        List<CheckResultVo> checkResultVos1 = checkResultMapper.selectCheckResultsProcess(checkResultVos);
        if(checkResultVos1 != null && !checkResultVos1.isEmpty() && "3".equals(checkResult.getType())){
            checkResultVos1.forEach(slave -> {
                slave.setDeptNameBelong(personnelArchivesService.getDeptBreadcrumb(slave.getDept()));
            });
        }
        return checkResultVos1;
    }

    /**
     * 判断是否属于库已录入数据
     * @param checkResult
     * @return
     */
    @Override
    public Boolean getExistence(CheckResultVo checkResult){
        List<CheckResultVo> checkResultVos = checkResultMapper.selectCheckResultNoAuditing(checkResult);
        return !checkResultVos.isEmpty();
    }

    @Override
    public List<CheckResultExcel> getCheckResultExcel(CheckResultVo checkResult){
        List<CheckResultExcel> checkResultExcels = new ArrayList<>();
        List<CheckResultVo> checkResultVos = selectCheckResultList(checkResult);
        HashMap<String, String> map = new HashMap<>();
        map.put("1","已完成");
        map.put("2","未完成");
        map.put("3","已完成 - 项目校准");
        map.put("4","未完成 - 项目校准");
        checkResultVos.forEach(vo -> {
            CheckResultExcel checkResultExcel = new CheckResultExcel();
            checkResultExcel.setCompanyName(vo.getCompanyName());
            checkResultExcel.setDeptName(vo.getDeptName());
            checkResultExcel.setNickName(vo.getNickName());
            checkResultExcel.setYear(vo.getYear());
            checkResultExcel.setQuarter(vo.getQuarter());
            checkResultExcel.setCompanyShortNameBelong(vo.getCompanyShortNameBelong());
            checkResultExcel.setDeptNameBelong(vo.getDeptNameBelong());
            checkResultExcel.setTotalIndex(vo.getCheckResult().getTotalIndex());
            checkResultExcel.setDistributionIndex(vo.getCheckResult().getDistributionIndex());
            checkResultExcel.setExtensionIndex(vo.getCheckResult().getExtensionIndex());
            checkResultExcel.setExtensionBank(vo.getCheckResult().getExtensionBank());
            checkResultExcel.setAchievementWagesProportion(vo.getCheckResult().getAchievementWagesProportion());
            checkResultExcel.setDistributionProportion(vo.getCheckResult().getDistributionProportion());
            checkResultExcel.setExtensionProportion(vo.getCheckResult().getExtensionProportion());
            checkResultExcel.setCompleteTotalIndex(vo.getCheckResult().getCompleteTotalIndex());
            checkResultExcel.setProjectSalaryTotalProportion(vo.getCheckResult().getProjectSalaryTotalProportion());
            checkResultExcel.setCompleteDistributionIndex(vo.getCheckResult().getCompleteDistributionIndex());
            checkResultExcel.setProjectSalaryDistributionProportion(vo.getCheckResult().getProjectSalaryDistributionProportion());
            checkResultExcel.setDistributionIndexDeviation(vo.getCheckResult().getDistributionIndexDeviation());
            checkResultExcel.setCalibrationDistributionIndex(vo.getCheckResult().getCalibrationDistributionIndex());
            checkResultExcel.setCalibrationDistributionIndexState(map.get(vo.getCheckResult().getCalibrationDistributionIndexState()));
            checkResultExcel.setCalibrationProjectSalaryDistributionProportion(vo.getCheckResult().getCalibrationProjectSalaryDistributionProportion());
            checkResultExcel.setCompleteExtensionIndex(vo.getCheckResult().getCompleteExtensionIndex());
            checkResultExcel.setProjectSalaryExtensionProportion(vo.getCheckResult().getProjectSalaryExtensionProportion());
            checkResultExcel.setExtensionIndexDeviation(vo.getCheckResult().getExtensionIndexDeviation());
            checkResultExcel.setCalibrationExtensionIndex(vo.getCheckResult().getCalibrationExtensionIndex());
            checkResultExcel.setCalibrationProjectSalaryExtensionProportion(vo.getCheckResult().getCalibrationProjectSalaryExtensionProportion());
            checkResultExcel.setCompleteExtensionBank(vo.getCheckResult().getCompleteExtensionBank());
            checkResultExcel.setBankSalaryExtensionProportion(vo.getCheckResult().getBankSalaryExtensionProportion());
            checkResultExcel.setExtensionBankDeviation(vo.getCheckResult().getExtensionBankDeviation());
            checkResultExcel.setCalibrationExtensionBank(vo.getCheckResult().getCalibrationExtensionBank());
            checkResultExcel.setCalibrationExtensionBankDeviation(vo.getCheckResult().getCalibrationExtensionBankDeviation());
            checkResultExcel.setCalibrationExtensionIndexBankState(vo.getCheckResult().getCalibrationExtensionIndexBankState());
            checkResultExcel.setCalibrationExtensionIndexProjectState(vo.getCheckResult().getCalibrationExtensionIndexProjectState());
            checkResultExcel.setCalibrationProjectSalaryBankProportion(vo.getCheckResult().getCalibrationProjectSalaryBankProportion());

            checkResultExcels.add(checkResultExcel);
        });
        return checkResultExcels;
    }
}
