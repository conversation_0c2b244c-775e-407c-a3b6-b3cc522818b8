package org.ruoyi.core.kaohe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.kaohe.domain.ColumnsInit;
import org.ruoyi.core.kaohe.service.IColumnsInitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 字段显示Controller
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@RestController
@RequestMapping("/columns/init")
public class ColumnsInitController extends BaseController
{
    @Autowired
    private IColumnsInitService columnsInitService;

    /**
     * 查询字段显示列表
     */
    //@PreAuthorize("@ss.hasPermi('system:init:list')")
    @GetMapping("/list")
    public TableDataInfo list(ColumnsInit columnsInit)
    {
        startPage();
        List<ColumnsInit> list = columnsInitService.selectColumnsInitList(columnsInit);
        return getDataTable(list);
    }

    @GetMapping("/enterList")
    public TableDataInfo enterList(ColumnsInit columnsInit)
    {
        startPage();
        List<ColumnsInit> list = columnsInitService.selectColumnsInitEnterList(columnsInit);
        return getDataTable(list);
    }

    @GetMapping("/checkResultList")
    public TableDataInfo selectColumnsCheckResultList(ColumnsInit columnsInit)
    {
        startPage();
        List<ColumnsInit> list = columnsInitService.selectColumnsCheckResultList(columnsInit);
        return getDataTable(list);
    }

    /**
     * 导出字段显示列表
     */
    //@PreAuthorize("@ss.hasPermi('system:init:export')")
    @Log(title = "字段显示", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ColumnsInit columnsInit)
    {
        List<ColumnsInit> list = columnsInitService.selectColumnsInitList(columnsInit);
        ExcelUtil<ColumnsInit> util = new ExcelUtil<ColumnsInit>(ColumnsInit.class);
        util.exportExcel(response, list, "字段显示数据");
    }

    /**
     * 获取字段显示详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:init:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(columnsInitService.selectColumnsInitById(id));
    }

    /**
     * 新增字段显示
     */
    //@PreAuthorize("@ss.hasPermi('system:init:add')")
    @Log(title = "字段显示", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ColumnsInit columnsInit)
    {
        return toAjax(columnsInitService.insertColumnsInit(columnsInit));
    }
    @PostMapping("/replaceColumnsInit")
    @Log(title = "字段显示", businessType = BusinessType.INSERT)
    public AjaxResult replaceColumnsInit(@RequestBody List<ColumnsInit> columnsInit)
    {
        return toAjax(columnsInitService.replaceColumnsInit(columnsInit));
    }

    /**
     * 修改字段显示
     */
    //@PreAuthorize("@ss.hasPermi('system:init:edit')")
    @Log(title = "字段显示", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ColumnsInit columnsInit)
    {
        return toAjax(columnsInitService.updateColumnsInit(columnsInit));
    }

    /**
     * 删除字段显示
     */
    //@PreAuthorize("@ss.hasPermi('system:init:remove')")
    @Log(title = "字段显示", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(columnsInitService.deleteColumnsInitByIds(ids));
    }
}
