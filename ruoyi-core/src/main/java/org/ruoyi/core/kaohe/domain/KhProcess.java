package org.ruoyi.core.kaohe.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 绩效考核流程对象 kh_process
 *
 * <AUTHOR>
 * @date 2024-08-13
 */

@Data
public class KhProcess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 流程类型 1.年度计划  */
    @Excel(name = "流程类型 1.年度计划 2.考核配置（审核）3.业绩录入(审核) 4.考核结果(录入)")
    private String type;

    /** 季度 */
    @Excel(name = "季度")
    private Integer quarter;

    /** 流程关联id */
    @Excel(name = "流程关联id")
    private String processId;

    /** 业务关联id */
    @Excel(name = "业务关联id")
    private Long correlationId;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("type", getType())
            .append("quarter", getQuarter())
            .append("processId", getProcessId())
            .append("correlationId", getCorrelationId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
