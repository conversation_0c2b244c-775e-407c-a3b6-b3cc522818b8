package org.ruoyi.core.kaohe.mapper;

import org.ruoyi.core.kaohe.domain.CheckConfigTemplate;
import org.ruoyi.core.kaohe.domain.vo.CheckConfigTemplateVo;

import java.util.List;

/**
 * 考核配置模版Mapper接口
 *时
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface CheckConfigTemplateMapper
{
    /**
     * 查询考核配置模版
     *
     * @param id 考核配置模版主键
     * @return 考核配置模版
     */
    public CheckConfigTemplateVo selectCheckConfigTemplateById(Long id);

    /**
     * 查询考核配置模版列表
     *
     * @param checkConfigTemplate 考核配置模版
     * @return 考核配置模版集合
     */
    public List<CheckConfigTemplateVo> selectCheckConfigTemplateList(CheckConfigTemplate checkConfigTemplate);

    /**
     * 新增考核配置模版
     *
     * @param checkConfigTemplate 考核配置模版
     * @return 结果
     */
    public int insertCheckConfigTemplate(CheckConfigTemplate checkConfigTemplate);

    /**
     * 修改考核配置模版
     *
     * @param checkConfigTemplate 考核配置模版
     * @return 结果
     */
    public int updateCheckConfigTemplate(CheckConfigTemplate checkConfigTemplate);

    /**
     * 删除考核配置模版
     *
     * @param id 考核配置模版主键
     * @return 结果
     */
    public int deleteCheckConfigTemplateById(Long id);

    /**
     * 批量删除考核配置模版
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckConfigTemplateByIds(Long[] ids);
}
