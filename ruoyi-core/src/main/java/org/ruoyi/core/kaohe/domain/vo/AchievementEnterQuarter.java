package org.ruoyi.core.kaohe.domain.vo;


import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AchievementEnterQuarter {
    private String month;

    @Excel(name = "项目总业绩(万元)")
    private BigDecimal totalIndex;

    @Excel(name = "分配项目业绩(万元)")
    private BigDecimal distributionIndex;

    @Excel(name = "自拓项目业绩(万元)")
    private BigDecimal extensionIndex;

}
