package org.ruoyi.core.kaohe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.kaohe.domain.AchievementEnter;
import org.ruoyi.core.kaohe.domain.vo.AchievementEnterImportExcel;
import org.ruoyi.core.kaohe.domain.vo.AchievementEnterVo;
import org.ruoyi.core.kaohe.service.IAchievementEnterService;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 项目业绩录入Controller
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/achievement/enter")
public class AchievementEnterController extends BaseController
{
    @Autowired
    private IAchievementEnterService achievementEnterService;

    /**
     * 查询项目业绩录入列表
     */
    //@PreAuthorize("@ss.hasPermi('system:enter:list')")
    @GetMapping("/list")
    public TableDataInfo list(AchievementEnterVo achievementEnter)
    {
        //startPage();
        List<AchievementEnterVo> list = achievementEnterService.selectAchievementEnterList(achievementEnter);
        return getDataTable(list);
    }

    @GetMapping("/listOfCheck")
    public TableDataInfo listOfCheck(AchievementEnterVo achievementEnter)
    {
        startPage();
        List<AchievementEnterVo> list = achievementEnterService.selectAchievementEnterListOfCheck(achievementEnter);
        return getDataTable(list);
    }

    /**
     * 导出项目业绩录入列表
     */
    //@PreAuthorize("@ss.hasPermi('system:enter:export')")
    @Log(title = "项目业绩录入", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AchievementEnterVo achievementEnter)
    {
        List<AchievementEnterVo> list = achievementEnterService.selectAchievementEnterList(achievementEnter);
        ExcelUtil<AchievementEnterVo> util = new ExcelUtil<AchievementEnterVo>(AchievementEnterVo.class);
        util.exportExcel(response, list, "项目业绩录入数据");
    }

    /**
     * 获取项目业绩录入详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:enter:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(achievementEnterService.selectAchievementEnterById(id));
    }

    @GetMapping(value = "/getAchievementEnter")
    public AjaxResult getAnnualPlanVo(AchievementEnterVo achievementEnter)
    {
        return AjaxResult.success(achievementEnterService.getAchievementEnter(achievementEnter));
    }

    @GetMapping(value = "/getProjectInfo/{id}")
    public AjaxResult getProjectInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(achievementEnterService.getProjectInfo(id));
    }

    /**
     * 新增项目业绩录入
     */
    //@PreAuthorize("@ss.hasPermi('system:enter:add')")
    @Log(title = "项目业绩录入", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AchievementEnter achievementEnter)
    {
        return toAjax(achievementEnterService.insertAchievementEnter(achievementEnter));
    }

    /**
     * 修改项目业绩录入
     */
    //@PreAuthorize("@ss.hasPermi('system:enter:edit')")
    @Log(title = "项目业绩录入", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AchievementEnter achievementEnter)
    {
        return toAjax(achievementEnterService.updateAchievementEnter(achievementEnter));
    }

    /**
     * 删除项目业绩录入
     */
    //@PreAuthorize("@ss.hasPermi('system:enter:remove')")
    @Log(title = "项目业绩录入", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(achievementEnterService.deleteAchievementEnterByIds(ids));
    }


    @Log(title = "考核配置流程", businessType = BusinessType.INSERT)
    @PostMapping("/insertAchievementEnterProcess")
    public AjaxResult insertAchievementEnterProcess(@RequestBody AchievementEnterVo achievementEnterVo)
    {
        return toAjax(achievementEnterService.insertAchievementEnterProcess(achievementEnterVo));
    }

    @Log(title = "通过业绩录入", businessType = BusinessType.INSERT)
    @PutMapping("/passAchievementEnterProcess")
    public AjaxResult passAchievementEnterProcess(@RequestBody AchievementEnterVo achievementEnterVo)
    {
        return toAjax(achievementEnterService.passAchievementEnterProcess(achievementEnterVo));
    }

    @Log(title = "废弃业绩录入", businessType = BusinessType.INSERT)
    @PutMapping("/unpassAchievementEnterProcess")
    public AjaxResult unpassAchievementEnterProcess(@RequestBody AchievementEnterVo achievementEnterVo)
    {
        return toAjax(achievementEnterService.unpassAchievementEnterProcess(achievementEnterVo));
    }

    @GetMapping("/selectAchievementEnterListProcess")
    public TableDataInfo selectAchievementEnterListProcess(AchievementEnterVo achievementEnter)
    {
        //startPage();
        List<AchievementEnterVo> list = achievementEnterService.selectAchievementEnterListProcess(achievementEnter);
        return getDataTable(list);
    }

    /**
     * 下拉框选择数据
     * @param oaProjectDeploy
     * @return {@link TableDataInfo}
     */
    @GetMapping("/getSelectListOfAchievement")
    public TableDataInfo getSelectListOfAchievement(OaProjectDeploy oaProjectDeploy)
    {
        List<OaProjectDeploy> list = achievementEnterService.selectOaProjectDeployList(oaProjectDeploy);
        return getDataTable(list);
    }

    @Log(title = "项目业绩模版导出", businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AchievementEnterImportExcel> util = new ExcelUtil<AchievementEnterImportExcel>(AchievementEnterImportExcel.class);
        util.importTemplateExcel(response, "业绩录入模版");
    }

    @Log(title = "项目业绩数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<AchievementEnterImportExcel> util = new ExcelUtil<AchievementEnterImportExcel>(AchievementEnterImportExcel.class);
        List<AchievementEnterImportExcel> achievementEnterImportExcelList = util.importExcel(file.getInputStream());
        String message = achievementEnterService.importData(achievementEnterImportExcelList);
        return AjaxResult.success(message);
    }
}
