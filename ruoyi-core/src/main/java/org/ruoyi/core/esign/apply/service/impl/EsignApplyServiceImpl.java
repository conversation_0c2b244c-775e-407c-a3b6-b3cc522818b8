package org.ruoyi.core.esign.apply.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.esign.apply.mapper.EsignApplyMapper;
import org.ruoyi.core.esign.apply.domain.EsignApply;
import org.ruoyi.core.esign.apply.service.IEsignApplyService;

/**
 * 富邦《担保明细清单》签章申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-06-26
 */
@Service
public class EsignApplyServiceImpl implements IEsignApplyService 
{
    @Autowired
    private EsignApplyMapper esignApplyMapper;

    /**
     * 查询富邦《担保明细清单》签章申请
     * 
     * @param id 富邦《担保明细清单》签章申请主键
     * @return 富邦《担保明细清单》签章申请
     */
    @Override
    public EsignApply selectEsignApplyById(Long id)
    {
        return esignApplyMapper.selectEsignApplyById(id);
    }

    /**
     * 查询富邦《担保明细清单》签章申请列表
     * 
     * @param esignApply 富邦《担保明细清单》签章申请
     * @return 富邦《担保明细清单》签章申请
     */
    @Override
    public List<EsignApply> selectEsignApplyList(EsignApply esignApply)
    {
        return esignApplyMapper.selectEsignApplyList(esignApply);
    }

    /**
     * 新增富邦《担保明细清单》签章申请
     * 
     * @param esignApply 富邦《担保明细清单》签章申请
     * @return 结果
     */
    @Override
    public int insertEsignApply(EsignApply esignApply)
    {
        esignApply.setCreateTime(DateUtils.getNowDate());
        return esignApplyMapper.insertEsignApply(esignApply);
    }

    /**
     * 修改富邦《担保明细清单》签章申请
     * 
     * @param esignApply 富邦《担保明细清单》签章申请
     * @return 结果
     */
    @Override
    public int updateEsignApply(EsignApply esignApply)
    {
        esignApply.setUpdateTime(DateUtils.getNowDate());
        return esignApplyMapper.updateEsignApply(esignApply);
    }

    /**
     * 批量删除富邦《担保明细清单》签章申请
     * 
     * @param ids 需要删除的富邦《担保明细清单》签章申请主键
     * @return 结果
     */
    @Override
    public int deleteEsignApplyByIds(Long[] ids)
    {
        return esignApplyMapper.deleteEsignApplyByIds(ids);
    }

    /**
     * 删除富邦《担保明细清单》签章申请信息
     * 
     * @param id 富邦《担保明细清单》签章申请主键
     * @return 结果
     */
    @Override
    public int deleteEsignApplyById(Long id)
    {
        return esignApplyMapper.deleteEsignApplyById(id);
    }

    /**
     * 查询待数据校验
     * @return
     */
	@Override
	public List<EsignApply> selectEsignApplyCheckList() {
		return esignApplyMapper.selectEsignApplyCheckList();
	}
	/**
	 * 查询待签章
	 * @return
	 */
	@Override
	public List<EsignApply> selectEsignApplyEsignList() {
		return esignApplyMapper.selectEsignApplyEsignList();
	}
}
