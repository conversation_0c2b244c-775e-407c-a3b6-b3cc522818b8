package org.ruoyi.core.personnel.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.ruoyi.core.personnel.domain.PersonnelTransfer;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 人员调动对象 rs_personnel_transfer
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@Data
public class PersonnelTransferVo extends PersonnelTransfer
{
    private String sponsor;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processCreateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;
    @Excel(name = "调入公司")
    private String inCompanyName;
    @Excel(name = "调出公司")
    private String outCompanyName;
    @Excel(name = "调入部门")
    private String inDeptName;
    @Excel(name = "调出部门")
    private String outDeptName;
    @Excel(name = "调入岗位")
    private String inPostName;
    @Excel(name = "调出岗位")
    private String outPostName;
    @Excel(name = "调动后上级领导")
    private String inLeaderName;

    private String outLeaderName;

    private String name;

    private String RecordId;

    private List<Long> deptIds;

    private List<Long> unitIds;
    /** 前端传过来的资料ids 导出接口 */
    private String ids;

    private List<Long> idArray;
}
