package org.ruoyi.core.personnel.service;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.personnel.domain.PersonnelResignation;
import org.ruoyi.core.personnel.domain.vo.PersonnelResignationVo;

import java.util.List;

/**
 * 人员离职Service接口
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
public interface IPersonnelResignationService
{
    /**
     * 查询人员离职
     *
     * @param id 人员离职主键
     * @return 人员离职
     */
    public PersonnelResignationVo selectPersonnelResignationById(Long id);

    /**
     * 查询人员离职列表
     *
     * @param personnelResignation 人员离职
     * @return 人员离职集合
     */
    public List<PersonnelResignationVo> selectPersonnelResignationList(PersonnelResignationVo personnelResignation);

    /**
     * 新增人员离职
     *
     * @param personnelResignation 人员离职
     * @return 结果
     */
    public AjaxResult insertPersonnelResignation(PersonnelResignationVo personnelResignation);

    /**
     * 修改人员离职
     *
     * @param personnelResignation 人员离职
     * @return 结果
     */
    public int updatePersonnelResignation(PersonnelResignation personnelResignation);

    /**
     * 批量删除人员离职
     *
     * @param ids 需要删除的人员离职主键集合
     * @return 结果
     */
    public int deletePersonnelResignationByIds(Long[] ids);

    /**
     * 删除人员离职信息
     *
     * @param id 人员离职主键
     * @return 结果
     */
    public int deletePersonnelResignationById(Long id);

    int passResignationById(Long id);

    int unpassResignationById(Long id);

    int commitResignationProcess(Long id);

    public List<PersonnelResignationVo> exportList(PersonnelResignationVo personnelResignation);
}
