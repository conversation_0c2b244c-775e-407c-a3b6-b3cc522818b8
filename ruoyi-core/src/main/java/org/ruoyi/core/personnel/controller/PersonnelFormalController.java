package org.ruoyi.core.personnel.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.personnel.domain.PersonnelArchives;
import org.ruoyi.core.personnel.domain.PersonnelFormal;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelFormalVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelOnboardingVo;
import org.ruoyi.core.personnel.service.IPersonnelFormalService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 人员转正Controller
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@RestController
@RequestMapping("/personnel/formal")
public class PersonnelFormalController extends BaseController
{
    @Autowired
    private IPersonnelFormalService personnelFormalService;

    /**
     * 查询人员转正列表
     */
    //@PreAuthorize("@ss.hasPermi('system:formal:list')")
    @GetMapping("/list")
    public TableDataInfo list(PersonnelFormal personnelFormal)
    {
        startPage();
        List<PersonnelFormal> list = personnelFormalService.selectPersonnelFormalList(personnelFormal);
        return getDataTable(list);
    }

    /**
     * 导出人员转正列表
     */
    //@PreAuthorize("@ss.hasPermi('system:formal:export')")
    @Log(title = "人员转正", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PersonnelOnboardingVo personnelOnboardingVo)
    {
        List<PersonnelFormalVo> list = personnelFormalService.exportList(personnelOnboardingVo);
        ExcelUtil<PersonnelFormalVo> util = new ExcelUtil<PersonnelFormalVo>(PersonnelFormalVo.class);
        util.exportExcel(response, list, "人员转正数据");
    }

    /**
     * 获取人员转正详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:formal:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(personnelFormalService.selectPersonnelFormalById(id));
    }

    /**
     * 新增人员转正
     */
    //@PreAuthorize("@ss.hasPermi('system:formal:add')")
    @Log(title = "人员转正", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PersonnelFormalVo personnelFormal)
    {
        return personnelFormalService.insertPersonnelFormal(personnelFormal);
    }

    /**
     * 修改人员转正
     */
    //@PreAuthorize("@ss.hasPermi('system:formal:edit')")
    @Log(title = "人员转正", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PersonnelFormalVo personnelFormal)
    {
        return toAjax(personnelFormalService.updatePersonnelFormal(personnelFormal));
    }

    /**
     * 删除人员转正
     */
    //@PreAuthorize("@ss.hasPermi('system:formal:remove')")
    @Log(title = "人员转正", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(personnelFormalService.deletePersonnelFormalByIds(ids));
    }

    /**
     * 查询转正列表
     */
    //@PreAuthorize("@ss.hasPermi('personnel:archives:list')")
    @GetMapping("/getFormalList")
    public TableDataInfo getFormalBeforeList(PersonnelOnboardingVo personnelOnboardingVo)
    {
        //startPage();
        List<PersonnelFormalVo> list = personnelFormalService.getFormalList(personnelOnboardingVo);
        return getDataTable(list);
    }


    /**
     * 审核通过转正
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "审核通过转正", businessType = BusinessType.UPDATE)
    @PostMapping("/passFormal")
    public AjaxResult passOnboardingById(@RequestBody Long id)
    {

        return toAjax(personnelFormalService.passFormalById(id));
    }

    /**
     * 审核不通过转正
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "审核不通过转正", businessType = BusinessType.UPDATE)
    @PostMapping("/unpassFormal")
    public AjaxResult unpassOnboardingById(@RequestBody Long id)
    {

        return toAjax(personnelFormalService.unpassFormalById(id));
    }

    /**
     * 提交修改状态
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "提交修改状态", businessType = BusinessType.UPDATE)
    @PostMapping("/commitFormalById")
    public AjaxResult commitFormalById(@RequestBody Long id)
    {
        return toAjax(personnelFormalService.commitFormalById(id));
    }

    @Anonymous
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) {
        return personnelFormalService.uploadFile(file);
    }

    @Anonymous
    @PostMapping("/uploadFilePro")
    public AjaxResult uploadFilePro(@RequestParam("file") MultipartFile file) {
        return personnelFormalService.uploadFilePro(file);
    }

    @Anonymous
    @PostMapping("/uploadFileBecome")
    public AjaxResult uploadFileBecome(@RequestParam("file") MultipartFile file) {
        return personnelFormalService.uploadFileBecome(file);
    }
}
