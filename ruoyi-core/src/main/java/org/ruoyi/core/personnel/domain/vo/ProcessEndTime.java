package org.ruoyi.core.personnel.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;import java.util.Date;

@Data
public class ProcessEndTime {
    /**
    * 流程id
    */
    private String businessKey;
    /**
    * 流程节点名称
    */
    private String actName;
    /**
    * 流程节点结束时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    private Long userId;
}
