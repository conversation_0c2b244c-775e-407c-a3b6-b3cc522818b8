package org.ruoyi.core.personnel.service;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.personnel.domain.PersonnelTransfer;
import org.ruoyi.core.personnel.domain.vo.PersonnelTransferVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 人员调动Service接口
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
public interface IPersonnelTransferService
{
    /**
     * 查询人员调动
     *
     * @param id 人员调动主键
     * @return 人员调动
     */
    public PersonnelTransferVo selectPersonnelTransferById(Long id);

    /**
     * 查询人员调动列表
     *
     * @param personnelTransfer 人员调动
     * @return 人员调动集合
     */
    public List<PersonnelTransfer> selectPersonnelTransferList(PersonnelTransfer personnelTransfer);

    /**
     * 新增人员调动
     *
     * @param personnelTransfer 人员调动
     * @return 结果
     */
    public AjaxResult insertPersonnelTransfer(PersonnelTransferVo personnelTransfer);

    /**
     * 修改人员调动
     *
     * @param personnelTransfer 人员调动
     * @return 结果
     */
    public int updatePersonnelTransfer(PersonnelTransfer personnelTransfer);

    /**
     * 批量删除人员调动
     *
     * @param ids 需要删除的人员调动主键集合
     * @return 结果
     */
    public int deletePersonnelTransferByIds(Long[] ids);

    /**
     * 删除人员调动信息
     *
     * @param id 人员调动主键
     * @return 结果
     */
    public int deletePersonnelTransferById(Long id);

    public List<PersonnelTransferVo> getTransferHistoryList(PersonnelTransfer personnelTransfer);

    int passTransferById(Long id);

    int unpassTransferById(Long id);

    public AjaxResult uploadFile(MultipartFile file);

    public List<PersonnelTransferVo> exportList(PersonnelTransferVo personnelTransfer);
}
