package org.ruoyi.core.mapper;

import org.ruoyi.core.cwproject.domain.CwProjectAckRef;

import java.util.List;

/**
 * 财务项目管理-提成基数确认及返费关联Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
public interface CwProjectAckRefMapper
{
    /**
     * 查询财务项目管理-提成基数确认及返费关联
     *
     * @param id 财务项目管理-提成基数确认及返费关联主键
     * @return 财务项目管理-提成基数确认及返费关联
     */
    public CwProjectAckRef selectCwProjectAckRefById(Long id);

    /**
     * 查询财务项目管理-提成基数确认及返费关联列表
     *
     * @param cwProjectAckRef 财务项目管理-提成基数确认及返费关联
     * @return 财务项目管理-提成基数确认及返费关联集合
     */
    public List<CwProjectAckRef> selectCwProjectAckRefList(CwProjectAckRef cwProjectAckRef);

    /**
     * 新增财务项目管理-提成基数确认及返费关联
     *
     * @param cwProjectAckRef 财务项目管理-提成基数确认及返费关联
     * @return 结果
     */
    public int insertCwProjectAckRef(CwProjectAckRef cwProjectAckRef);

    /**
     * 修改财务项目管理-提成基数确认及返费关联
     *
     * @param cwProjectAckRef 财务项目管理-提成基数确认及返费关联
     * @return 结果
     */
    public int updateCwProjectAckRef(CwProjectAckRef cwProjectAckRef);

    /**
     * 删除财务项目管理-提成基数确认及返费关联
     *
     * @param id 财务项目管理-提成基数确认及返费关联主键
     * @return 结果
     */
    public int deleteCwProjectAckRefById(Long id);

    /**
     * 批量删除财务项目管理-提成基数确认及返费关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCwProjectAckRefByIds(Long[] ids);
}
