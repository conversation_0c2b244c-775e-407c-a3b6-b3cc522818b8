package org.ruoyi.core.cwproject.excelimport;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.ruoyi.core.cwproject.domain.dto.ExcelImportOfCwProjectDto;
import org.ruoyi.core.oasystem.domain.OaDataManage;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 财务项目管理导入excel
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/11/15 13:55
 **/
public class ExcelImportOfCwProject {
    private InputStream inputStream;
    private Workbook wb;

    public void init(InputStream is) throws IOException {
        this.inputStream = is;
        this.wb = WorkbookFactory.create(is);
    }

    /**
     * 导入excel，读取数据
     *
     */
    public List<ExcelImportOfCwProjectDto> importExcelHandle(List<OaDataManage> projectTypeInfo) throws Exception
    {
        List<ExcelImportOfCwProjectDto> excelImportOfCwProjectDtoList = new ArrayList<>();
        Workbook sheets = this.wb;
        Sheet sheet = sheets.getSheetAt(0);
        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }

        // 获取最后一行的行号
        int lastRowNum = sheet.getLastRowNum() + 1; // 注意：lastRowNum 是从 0 开始计数的，所以需要加 1

        Long numFlag = null;
        String projectName = StringUtils.EMPTY;
        Long projectTypeRelevanceTypeId = null;
        String projectType1 = StringUtils.EMPTY;
        for (int i = 1; i < lastRowNum; i++) {
            //从第二行开始读取数据，第一行是表头
            Row row = sheet.getRow(i);
            if (row == null) {
                continue; // 如果行为空，跳过该行
            }
            ExcelImportOfCwProjectDto excelImportOfCwProjectDto = new ExcelImportOfCwProjectDto();
            //第一个单元格，就是序号
            Cell cell = row.getCell(0);
            if (cell != null) {
                Double numericCellValue = cell.getNumericCellValue();
                numFlag = numericCellValue.longValue();
            }
            excelImportOfCwProjectDto.setNumFlag(numFlag);
            Cell cell1 = row.getCell(1);
            if (cell1 != null) {
                //获取项目名称
                if (cell1.getStringCellValue() != null && !StringUtils.EMPTY.equals(cell1.getStringCellValue())) {
                    projectName = cell1.getStringCellValue();
                }
            }
            excelImportOfCwProjectDto.setProjectName(projectName);
            Cell cell2 = row.getCell(2);
            if (cell2 != null) {
                //获取项目类型
                String projectType = cell2.getStringCellValue();
                OaDataManage oaDataManage = projectTypeInfo.stream().filter(t -> projectType.equals(t.getDataName())).findFirst().orElse(null);
                if (oaDataManage != null) {
                    projectTypeRelevanceTypeId = oaDataManage.getId();
                    String dictValue = oaDataManage.getDataCode();
                    projectType1 = dictValue;
                }
//                projectTypeRelevanceTypeId = sysDictData.getDictCode();
//                String dictValue = sysDictData.getDictValue();
//                projectType1 = dictValue;
            }
            excelImportOfCwProjectDto.setProjectTypeRelevanceTypeId(projectTypeRelevanceTypeId);
            excelImportOfCwProjectDto.setProjectType(projectType1);
            Cell cell3 = row.getCell(3);
            //获取期次
            String phase = StringUtils.EMPTY;
            if ("STRING".equals(cell3.getCellType().name())) {
                phase = cell3.getStringCellValue();
            } else {
                phase = String.valueOf(cell3.getNumericCellValue());
            }
//            String phase = cell3.getStringCellValue();
            if (phase.length() > 7) {
                //当期次是范围期次时，进行处理
                int i1 = phase.indexOf("-");
                Date termBegin = DateUtils.parseDate(phase.substring(0, i1));
                Date termEnd = DateUtils.parseDate(phase.substring(i1 + 1));
                excelImportOfCwProjectDto.setTermBegin(termBegin);
                excelImportOfCwProjectDto.setTermEnd(termEnd);
            } else {
                //当期次是整数期次时，进行转换
                String termMonth = DateUtils.parseDateToStr(DateUtils.YYYY_MM, DateUtils.parseDate(phase));
                excelImportOfCwProjectDto.setTermMonth(termMonth);
            }
            Cell cell4 = row.getCell(4);
            //获取金额
            BigDecimal amount = new BigDecimal(String.valueOf(cell4.getNumericCellValue())).setScale(2, RoundingMode.HALF_UP);
            excelImportOfCwProjectDto.setAmount(amount);
            excelImportOfCwProjectDtoList.add(excelImportOfCwProjectDto);
        }
        return excelImportOfCwProjectDtoList;
    }
}
