package org.ruoyi.core.cwproject.domain.projectVO;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.ruoyi.core.cwproject.domain.CwProjectCust;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AddParojectVo {

    /** 主键 */
    private Long id;

    /** 项目类型 */
    private String projectType;

    //项目类型的字典表码
    private Long projectTypeCode;

    /** 项目名称 */
    private String projectName;

    /** 担保公司 */
    private String custName;

    /** 汇款公司 */
    private String incomeCustName;

    /** 返费公司集合 */
    private List<CwProjectCust> cwProjectCusts;
    /** 会计人员id */
    private List<Long> accountants;
    /** 出纳人员id */
    private List<Long> cashiers;
    /** 业务人员id */
    private List<Long> salesmans;

    /** 查看人员id */
    private List<Long> checkUserList;
    /** 导出人员id */
    private List<Long> exportUserList;

    private String prestoreIncomeFlag;

    //关联的OA项目id
    private Long oaProjectDeployId;

    //是否生成记账凭证 0-否，1-是
    private String generateCertificateFlag;

    //凭证所属账套id
    private Long accountSetsId;

    //担保费收入类型 0-直接收款，1-混合平台收益
    private String guaranteeIncomeType;

    //担保费收款方
    private Long guarantyPayee;
}
