package org.ruoyi.core.cwproject.service.impl;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.cwproject.domain.CwProjectAckRef;
import org.ruoyi.core.cwproject.service.ICwProjectAckRefService;
import org.ruoyi.core.mapper.CwProjectAckRefMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 财务项目管理-提成基数确认及返费关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-06
 */
@Service
public class CwProjectAckRefServiceImpl implements ICwProjectAckRefService
{
    @Autowired
    private CwProjectAckRefMapper cwProjectAckRefMapper;

    /**
     * 查询财务项目管理-提成基数确认及返费关联
     *
     * @param id 财务项目管理-提成基数确认及返费关联主键
     * @return 财务项目管理-提成基数确认及返费关联
     */
    @Override
    public CwProjectAckRef selectCwProjectAckRefById(Long id)
    {
        return cwProjectAckRefMapper.selectCwProjectAckRefById(id);
    }

    /**
     * 查询财务项目管理-提成基数确认及返费关联列表
     *
     * @param cwProjectAckRef 财务项目管理-提成基数确认及返费关联
     * @return 财务项目管理-提成基数确认及返费关联
     */
    @Override
    public List<CwProjectAckRef> selectCwProjectAckRefList(CwProjectAckRef cwProjectAckRef)
    {
        return cwProjectAckRefMapper.selectCwProjectAckRefList(cwProjectAckRef);
    }

    /**
     * 新增财务项目管理-提成基数确认及返费关联
     *
     * @param cwProjectAckRef 财务项目管理-提成基数确认及返费关联
     * @return 结果
     */
    @Override
    public int insertCwProjectAckRef(CwProjectAckRef cwProjectAckRef)
    {
        cwProjectAckRef.setCreateTime(DateUtils.getNowDate());
        return cwProjectAckRefMapper.insertCwProjectAckRef(cwProjectAckRef);
    }

    /**
     * 修改财务项目管理-提成基数确认及返费关联
     *
     * @param cwProjectAckRef 财务项目管理-提成基数确认及返费关联
     * @return 结果
     */
    @Override
    public int updateCwProjectAckRef(CwProjectAckRef cwProjectAckRef)
    {
        cwProjectAckRef.setUpdateTime(DateUtils.getNowDate());
        return cwProjectAckRefMapper.updateCwProjectAckRef(cwProjectAckRef);
    }

    /**
     * 批量删除财务项目管理-提成基数确认及返费关联
     *
     * @param ids 需要删除的财务项目管理-提成基数确认及返费关联主键
     * @return 结果
     */
    @Override
    public int deleteCwProjectAckRefByIds(Long[] ids)
    {
        return cwProjectAckRefMapper.deleteCwProjectAckRefByIds(ids);
    }

    /**
     * 删除财务项目管理-提成基数确认及返费关联信息
     *
     * @param id 财务项目管理-提成基数确认及返费关联主键
     * @return 结果
     */
    @Override
    public int deleteCwProjectAckRefById(Long id)
    {
        return cwProjectAckRefMapper.deleteCwProjectAckRefById(id);
    }
}
