package org.ruoyi.core.cwproject.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.AuthRoleEnum;
import com.ruoyi.common.enums.CwxmglEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.AuthorizedFeatureDetailDTO;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.ruoyi.core.constant.CwxmglConstants;
import org.ruoyi.core.cwproject.domain.*;
import org.ruoyi.core.cwproject.domain.dto.CwProjectIncomeAndFeeForLawDto;
import org.ruoyi.core.cwproject.domain.dto.CwProjectLawFeeDto;
import org.ruoyi.core.cwproject.domain.dto.CwProjectOverDetailCompanyDto;
import org.ruoyi.core.cwproject.domain.view.CwProjectDetailView;
import org.ruoyi.core.cwproject.domain.view.CwProjectLawDetailView;
import org.ruoyi.core.cwproject.domain.vo.CwProjectCustVo;
import org.ruoyi.core.cwproject.domain.vo.CwProjectFeeNoAlreadyVo;
import org.ruoyi.core.cwproject.domain.vo.CwProjectPayDateVo;
import org.ruoyi.core.cwproject.domain.vo.SchemeVo;
import org.ruoyi.core.cwproject.mapper.*;
import org.ruoyi.core.cwproject.service.ICwProjectCustService;
import org.ruoyi.core.oasystem.domain.OaDataManage;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.domain.OaTrader;
import org.ruoyi.core.oasystem.domain.vo.OaPayRebateRecordVo;
import org.ruoyi.core.oasystem.domain.vo.OaProjectTypeCorrelationVo;
import org.ruoyi.core.oasystem.domain.vo.OaTraderVo;
import org.ruoyi.core.oasystem.mapper.OaDataManageMapper;
import org.ruoyi.core.oasystem.mapper.OaPayRebateRecordMapper;
import org.ruoyi.core.oasystem.mapper.OaProjectDeployMapper;
import org.ruoyi.core.oasystem.mapper.OaTraderMapper;
import org.ruoyi.core.oasystem.service.IOaProjectTypeCorrelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.Collator;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static org.ruoyi.core.oasystem.service.impl.OaProjectNameRuleServiceImpl.getDataNameWithParents;

/**
 * 财务项目管理-返费公司与费率Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-10
 */
@Service
public class CwProjectCustServiceImpl implements ICwProjectCustService {
    @Autowired
    private CwProjectCustMapper cwProjectCustMapper;

    @Autowired
    private CwProjectFeeMapper cwProjectFeeMapper;

    @Autowired
    private CwProjectPayMapper cwProjectPayMapper;

    @Autowired
    private CwProjectMapper cwProjectMapper;

    @Autowired
    private CwProjectIncomeMapper cwProjectIncomeMapper;

    @Autowired
    private CwProjectUserMapper cwProjectUserMapper;

    @Autowired
    private CwProjectShowServiceImpl cwProjectShowService;

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Autowired
    private OaTraderMapper oaTraderMapper;

    @Autowired
    private OaPayRebateRecordMapper oaPayRebateRecordMapper;

    @Autowired
    private OaProjectDeployMapper oaProjectDeployMapper;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityService;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private ExportCwProjectMapper exportCwProjectMapper;
    @Autowired
    private OaDataManageMapper oaDataManageMapper;
    @Autowired
    private IOaProjectTypeCorrelationService oaProjectTypeCorrelationService;
    // @Autowired
    // private RedisCache redisCache;

    /**
     * 提供一个返费公司选择的下拉框，用于OA流程返费打款那块使用
     *
     * @param userId
     * @return 返回可以选择的返费公司下拉款
     **/
    public List<OaTraderVo> getOaTraderInfoByUserId(Long userId) {
        //先去找用户可以看到的项目
        List<Long> projectId = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(userId, AuthModuleEnum.FINANCEPROJ.getCode());
        //找这个项目的返费公司信息
        if (projectId.size() == 0) {
            return new ArrayList<>();
        }
        return oaTraderMapper.selectOaTraderListByOaProjectDeployId(projectId);
    }

    @Override
    public Map<String, Object> getUserRoleByProjectId(LoginUser loginUser, Long projectId) {
        Long userId = loginUser.getUserId();
        //首先通过项目id，找到对应的oaProjectDeployId
        CwProject cwProject = new CwProject();
        if (projectId != null) {
            cwProject = cwProjectMapper.selectCwProjectById(projectId);
        }
        //调查找用户在对应的项目中担任的角色
        List<String> roleList = newAuthorityService.getUserRoleByUserIdAndOaProjectDeployIdAndModuleType(userId, cwProject.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("hasRoleList", roleList);
        return returnMap;
    }

    @Override
    public List<?> getProjectDetailPageByPageParam(Long projectId, String projectType, Integer pageNum, Integer pageSize) {
        if (!"1".equals(projectType)) {
            //普通项目,根据分页相关的信息,对项目的分页进行处理
            PageHelper.startPage(pageNum, pageSize);
            Page<Long> incomeIdList = (Page<Long>) cwProjectCustMapper.queryIncomeIdListByProjectId(projectId);
            if (incomeIdList.size() == 0) {
                return new ArrayList<>();
            }
            //处理分页问题
            Page<CwProjectDetailView> returnPage = new Page<>();
            BeanUtil.copyProperties(incomeIdList, returnPage);
            returnPage.clear();
            //普通项目用incomeIdList去查找对应的信息,加到新建的分页当中
            List<CwProjectDetailView> projList = exportCwProjectMapper.selectCwProjectOverListDetileByIncomeIdList(incomeIdList.getResult());
//            List<CwProjectDetailView> projList = exportCwProjectMapper.selectCwProjectOverListDetileByProjectIdV2(cwProject.getId());
            //2024.10.22以下处理逻辑来自于selectCwProjectOverListDetileByProjectId方法
            List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectCustListByProjectId(projectId);
            long count = cwProjectCusts.stream().filter(t -> "1".equals(t.getReplaceFlag())).count();
            List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfoList = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByProjectId(projectId);
            List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = new ArrayList<>();
            if (count > 0L) {
                //说明有发生过替换,根据项目id查找cw_project_replace_fee_company_info表,得到所有替换的数据
                cwProjectReplaceFeeCompanyInfos = cwProjectReplaceFeeCompanyInfoList;
            }
            List<CwProjectReplaceFeeCompanyInfo> finalCwProjectReplaceFeeCompanyInfos = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> "0".equals(t.getStatus())).collect(Collectors.toList());
            List<CwProjectCust> cwProjectCustSchemeFlagUseSituation = cwProjectCustMapper.selectCwprojectCustSchemeFlagUseSituationByProjectId(projectId);
            List<CwProjectCust> cwProjectCustSchemeFlagUseSituationForNotSureCompany = cwProjectCustMapper.cwProjectCustSchemeFlagUseSituationForNotSureCompanyByProjectId(projectId);
            cwProjectCustSchemeFlagUseSituation.addAll(cwProjectCustSchemeFlagUseSituationForNotSureCompany);
            Map<String, List<CwProjectCust>> collect = cwProjectCustSchemeFlagUseSituation.stream().collect(Collectors.groupingBy(CwProjectCust::getSchemeFlag));
            List<CwProjectOverDetailCompanyDto> cwProjectCompany = new ArrayList<>();
            //遍历所有的返费公司与费率表
            for (CwProjectCust c:cwProjectCusts) {
                //然后去找对应的id
                List<CwProjectCust> v = collect.get(c.getSchemeFlag());
                int schemeFlagUseSituation = 0;
                int size = 0;
                Long id = c.getId();
                if (v != null) {
                    schemeFlagUseSituation = (int) v.stream().map(CwProjectCust::getProjectIncomeId).distinct().count();
                    //找到具体某一个返费公司与费率的具体使用情况
                    size = (int) v.stream().filter(t -> id.equals(t.getId())).count();
                }
                //给页面返回的数据
                CwProjectOverDetailCompanyDto cwProjectOverDetailCompanyDto = new CwProjectOverDetailCompanyDto();
                if ("1".equals(c.getReplaceFlag())) {
                    List<CwProjectReplaceFeeCompanyInfo> collect1 = finalCwProjectReplaceFeeCompanyInfos.stream().filter(a -> a.getOaTraderId().equals(c.getOaTraderId())).collect(Collectors.toList());
                    List<String> oldCompanyNameList = new ArrayList<>();
                    if (collect1.size() > 0) {
                        for (CwProjectReplaceFeeCompanyInfo cprfci:collect1) {
                            String oldOaTraderUserName = cprfci.getOldOaTraderUserName();
                            oldCompanyNameList.add(oldOaTraderUserName);
                        }
                    }
                    cwProjectOverDetailCompanyDto.setOldCompanyName(oldCompanyNameList);
                } else {
                    cwProjectOverDetailCompanyDto.setOldCompanyName(null);
                }
                cwProjectOverDetailCompanyDto.setId(id);
                cwProjectOverDetailCompanyDto.setCustName(c.getCustName());
                cwProjectOverDetailCompanyDto.setSchemeFlag("方案" + c.getSchemeFlag());
                cwProjectOverDetailCompanyDto.setSchemeFlagInt(Integer.parseInt(c.getSchemeFlag()));
                cwProjectOverDetailCompanyDto.setSchemeFlagUseSituation(schemeFlagUseSituation);
                cwProjectOverDetailCompanyDto.setPhaseSchemeFlagUseSituation(size);
                cwProjectOverDetailCompanyDto.setOaTraderId(c.getOaTraderId());
                cwProjectOverDetailCompanyDto.setReplaceFlag(c.getReplaceFlag());
                cwProjectCompany.add(cwProjectOverDetailCompanyDto);
            }

            Set<OaTrader> revealFeeCompanyList = new HashSet<>();
            for (CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo : cwProjectReplaceFeeCompanyInfoList) {
                Long oldOaTraderId = cwProjectReplaceFeeCompanyInfo.getOldOaTraderId();
                String oldOaTraderUserName = cwProjectReplaceFeeCompanyInfo.getOldOaTraderUserName();
                OaTrader oldOaTrader = new OaTrader();
                oldOaTrader.setId(oldOaTraderId);
                oldOaTrader.setUserName(oldOaTraderUserName);
                revealFeeCompanyList.add(oldOaTrader);
                Long newOaTraderId = cwProjectReplaceFeeCompanyInfo.getNewOaTraderId();
                String newOaTraderUserName = cwProjectReplaceFeeCompanyInfo.getNewOaTraderUserName();
                OaTrader newOaTrader = new OaTrader();
                newOaTrader.setId(newOaTraderId);
                newOaTrader.setUserName(newOaTraderUserName);
                revealFeeCompanyList.add(newOaTrader);
            }

            for (CwProjectDetailView cpdv:projList) {
                if (cpdv.getRevealFeeCompanyId() == null) {
                    Long feeCustId = cpdv.getFeeCustId();
                    String feeCustName = StringUtils.EMPTY;
                    if (feeCustId != null) {
                        feeCustName = cwProjectCompany.stream().filter(t -> t.getId().equals(feeCustId)).findFirst().map(CwProjectOverDetailCompanyDto::getCustName).orElse("数据错误，没有找到对应的返费公司");
                        if (feeCustId == -999L) {
                            feeCustName = "暂不确定公司";
                        }
                    }
                    cpdv.setFeeCustName(feeCustName);
                } else {
                    Long feeCustId = cpdv.getRevealFeeCompanyId();
                    String feeCustName = StringUtils.EMPTY;
                    if (feeCustId != null) {
                        feeCustName = revealFeeCompanyList.stream().filter(t -> t.getId().equals(feeCustId)).findFirst().map(OaTrader::getUserName).orElse("数据错误，没有找到对应的返费公司");
                        if (feeCustId == -999L) {
                            feeCustName = "暂不确定公司";
                        }
                    }
                    cpdv.setFeeCustName(feeCustName);
                }
            }
            //公司替换完成之后,对创建的Page对象进行赋值
            returnPage.addAll(projList);
            return returnPage;
        } else {
            //法催项目,根据分页相关的信息,对项目的分页进行处理
            PageHelper.startPage(pageNum, pageSize);
            Page<Long> phaseIdList = (Page<Long>) cwProjectCustMapper.queryPhaseIdListByProjectId(projectId);
            if (phaseIdList.size() == 0) {
                return new ArrayList<>();
            }
            //处理分页问题
            Page<CwProjectLawDetailView> returnPage = new Page<>();
            BeanUtil.copyProperties(phaseIdList, returnPage);
            returnPage.clear();
            //法催项目用phaseIdList去查找对应的信息,加到新建的分页当中
            List<CwProjectLawDetailView> projList = cwProjectIncomeMapper.selectLawCwProjectOverListDetileByPhaseIdList(phaseIdList.getResult());
            //2024.10.22以下处理逻辑来自于selectCwProjectLawDetileByProjectId方法
            List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectCustListByProjectIdForLaw(projectId);
            long count = cwProjectCusts.stream().filter(t -> "1".equals(t.getReplaceFlag())).count();
            List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfoList = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByProjectId(projectId);
            List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = new ArrayList<>();
            if (count > 0L) {
                //说明有发生过替换,根据项目id查找cw_project_replace_fee_company_info表,得到所有替换的数据
                cwProjectReplaceFeeCompanyInfos = cwProjectReplaceFeeCompanyInfoList;
            }
            List<CwProjectOverDetailCompanyDto> cwProjectCompany = new ArrayList<>();
            List<CwProjectReplaceFeeCompanyInfo> finalCwProjectReplaceFeeCompanyInfos = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> "0".equals(t.getStatus())).collect(Collectors.toList());
            cwProjectCusts.forEach(t -> {
                CwProjectOverDetailCompanyDto cwProjectOverDetailCompanyDto = new CwProjectOverDetailCompanyDto();
                if ("1".equals(t.getReplaceFlag())) {
                    List<CwProjectReplaceFeeCompanyInfo> collect = finalCwProjectReplaceFeeCompanyInfos.stream().filter(a -> a.getOaTraderId().equals(t.getOaTraderId())).collect(Collectors.toList());
                    List<String> oldCompanyNameList = new ArrayList<>();
                    if (collect.size() > 0) {
                        for (CwProjectReplaceFeeCompanyInfo cprfci:collect) {
                            String oldOaTraderUserName = cprfci.getOldOaTraderUserName();
                            oldCompanyNameList.add(oldOaTraderUserName);
                        }

                    }
                    cwProjectOverDetailCompanyDto.setOldCompanyName(oldCompanyNameList);
                } else {
                    cwProjectOverDetailCompanyDto.setOldCompanyName(null);
                }
                cwProjectOverDetailCompanyDto.setId(t.getId());
                cwProjectOverDetailCompanyDto.setCustName(t.getCustName());
                cwProjectCompany.add(cwProjectOverDetailCompanyDto);
            });

            Set<OaTrader> revealFeeCompanyList = new HashSet<>();
            for (CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo : cwProjectReplaceFeeCompanyInfoList) {
                Long oldOaTraderId = cwProjectReplaceFeeCompanyInfo.getOldOaTraderId();
                String oldOaTraderUserName = cwProjectReplaceFeeCompanyInfo.getOldOaTraderUserName();
                OaTrader oldOaTrader = new OaTrader();
                oldOaTrader.setId(oldOaTraderId);
                oldOaTrader.setUserName(oldOaTraderUserName);
                revealFeeCompanyList.add(oldOaTrader);
                Long newOaTraderId = cwProjectReplaceFeeCompanyInfo.getNewOaTraderId();
                String newOaTraderUserName = cwProjectReplaceFeeCompanyInfo.getNewOaTraderUserName();
                OaTrader newOaTrader = new OaTrader();
                newOaTrader.setId(newOaTraderId);
                newOaTrader.setUserName(newOaTraderUserName);
                revealFeeCompanyList.add(newOaTrader);
            }
            for (CwProjectLawDetailView cpldv:projList) {
                if (cpldv.getRevealFeeCompanyId() == null) {
                    Long projectCustId = cpldv.getProjectCustId();
                    String feeCustName = StringUtils.EMPTY;
                    if (projectCustId != null) {
                        feeCustName = cwProjectCompany.stream().filter(t -> t.getId().equals(projectCustId)).findFirst().map(CwProjectOverDetailCompanyDto::getCustName).orElse("数据错误，没有找到对应的返费公司");
                        if (projectCustId == -999L) {
                            feeCustName = "暂不确定公司";
                        }
                    }
                    cpldv.setFeeCustName(feeCustName);
                } else {
                    Long feeCustId = cpldv.getRevealFeeCompanyId();
                    String feeCustName = StringUtils.EMPTY;
                    if (feeCustId != null) {
                        feeCustName = revealFeeCompanyList.stream().filter(t -> t.getId().equals(feeCustId)).findFirst().map(OaTrader::getUserName).orElse("数据错误，没有找到对应的返费公司");
                        if (feeCustId == -999L) {
                            feeCustName = "暂不确定公司";
                        }
                    }
                    cpldv.setFeeCustName(feeCustName);
                }
            }
            //公司替换完成之后,对创建的Page对象进行赋值
            returnPage.addAll(projList);
            return returnPage;
        }
    }

    /**
     * 提供一个方法，用于核对要打款的金额和用户输入的金额。是否符合正常逻辑  用户输入的金额要比要打款的金额小
     *
     * @param oaTraderId 返费公司id
     * @param projectType 项目类型
     * @param userId 用户id
     * @return 返回用户可以输入的最大金额
     */
    public BigDecimal checkPayFeeFromOaSystem(Long oaTraderId, String projectType, Long userId) {
        //返回的金额对象
        BigDecimal returnBigDecimal = BigDecimal.ZERO;
        //通过userId查找用户的基本信息
        SysUser sysUser = userMapper.selectUserById(userId);
        //先用来查项目中的返费是否发生了替换，发生替换，那么进行判断最新的替换后的是否满足
        List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByOaTraderId(oaTraderId);
        //按projectId进行分组，然后再对替换时间进行倒叙，找每个项目最新的替换时间
        Map<Long, List<CwProjectReplaceFeeCompanyInfo>> collect = cwProjectReplaceFeeCompanyInfos.stream().collect(Collectors.groupingBy(CwProjectReplaceFeeCompanyInfo::getProjectId));
        //custId集合
        List<Long> custIdList = new ArrayList<>();
        //对每一个项目进行判断
        collect.forEach((projectId, groupList) -> {
            CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo = groupList.stream().max(Comparator.comparing(CwProjectReplaceFeeCompanyInfo::getReplaceTime)).orElse(null);
            if (cwProjectReplaceFeeCompanyInfo != null) {
                if (oaTraderId.equals(cwProjectReplaceFeeCompanyInfo.getNewOaTraderId())) {
                    List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectCustListByProjectIdAndOaTraderId(cwProjectReplaceFeeCompanyInfo.getProjectId(), cwProjectReplaceFeeCompanyInfo.getOaTraderId());
                    custIdList.addAll(cwProjectCusts.stream().map(CwProjectCust::getId).collect(Collectors.toList()));
                }
            }
        });
        //加完新的集合之后，查cust表id，因为要查对应的期次，必须要有custId
        List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectNoReplaceCwProjectCustListByOaTraderId(oaTraderId);
        custIdList.addAll(cwProjectCusts.stream().map(CwProjectCust::getId).collect(Collectors.toList()));

        //筛选用户负责的项目（角色是业务）
        List<Long> projectIdList = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleTypeAndRoleType(sysUser.getUserId(), AuthModuleEnum.FINANCEPROJ.getCode(), AuthRoleEnum.CWXMGL1.getCode());

        //收集对冲完的部分信息，让项目那块生成项目打款记录用
        List<CwProjectPayFormOa> cwProjectPayFormOaList = new ArrayList<>();

        //拿到所有的custId集合，去找对应的期次，进行倒叙排列
        if (custIdList.size() > 0) {
            List<Long> distinctCustIdList = custIdList.stream().distinct().collect(Collectors.toList());
            //找所有的期次信息，里面包含了项目的部分信息，用于判断对冲的是哪个项目类型等信息
            List<Map<String, Object>> cwprojectAndIncomeInfoList = cwProjectCustMapper.selectCwprojectAndIncomeInfoByCustIds(distinctCustIdList, projectType, projectIdList);

            //后续再对冲的时候，先做之前的信息查询。步骤如下
            List<Long> incomeIdList = cwprojectAndIncomeInfoList.stream().map(t -> Long.parseLong(t.get("incomeId").toString())).collect(Collectors.toList());
            //通过incomeId去找看对应对冲表的信息
            List<CwProjectPayFormOa> cwProjectPayFormOas = cwProjectCustMapper.selectCwProjectPayFormOaByIncomeIdList(incomeIdList);
            //找出来已经对冲完的
            List<Long> alreadyOverIncomeIds = cwProjectPayFormOas.stream().filter(t -> "0".equals(t.getOverFlag())).map(CwProjectPayFormOa::getProjectIncomeId).collect(Collectors.toList());
            //把要对冲的返费列表中，已经对冲完成的给剔除掉
            cwprojectAndIncomeInfoList = cwprojectAndIncomeInfoList.stream().filter(t -> !alreadyOverIncomeIds.contains(Long.parseLong(t.get("incomeId").toString()))).collect(Collectors.toList());
            //把没有完结的给拿出来
//            List<CwProjectPayFormOa> collect1 = cwProjectPayFormOas.stream().filter(t -> "1".equals(t.getOverFlag())).collect(Collectors.toList());
            //查到的信息就是已经按照期次时间从老到旧显示，直接从第一个开始金额累加
            for (Map<String, Object> map : cwprojectAndIncomeInfoList) {
                //修改map中的对冲金额信息
                Long incomeId = Long.parseLong(map.get("incomeId").toString());
//                String projectType = map.get("projectType").toString();
                CwProjectPayFormOa cwProjectPayFormOa1 = cwProjectPayFormOas.stream().filter(t -> incomeId.equals(t.getProjectIncomeId())).findFirst().orElse(null);
                if (cwProjectPayFormOa1 != null) {
                    //说明有历史对冲的没有完成，进行金额的替换，后续进行对冲
                    if ("1".equals(projectType)) {
                        //修改法催项目的feeRoud金额
                        map.put("feeRound", cwProjectPayFormOa1.getNoAlreadyPayFee());
                    } else {
                        //修改非法催项目的actuallyPayFeeAmt金额
                        map.put("actuallyPayFeeAmt", cwProjectPayFormOa1.getNoAlreadyPayFee());
                    }
                }
                if ("1".equals(projectType)) {
                    //法催项目金额累加
                    returnBigDecimal = returnBigDecimal.add(new BigDecimal(map.get("feeRound").toString()));
                } else {
                    //非法催项目金额累加
                    returnBigDecimal = returnBigDecimal.add(new BigDecimal(map.get("actuallyPayFeeAmt").toString()));
                }
            }
        }
        return returnBigDecimal;
    }


    /**
     * 提供一个方法，OA流程打完款之后，用于对冲现在的返费公司的返费
     *
     * @param oaTraderId 返费公司id
     * @param projectType 项目类型
     * @param amount 金额
     * @param userId 用户id
     * @return 返回后续要入库的对象
     */
    public List<Map<String, Object>> payFeeFromOaSystem(Long oaTraderId, String projectType, BigDecimal amount, Long userId) {
        //2024.09.26先记录原来的金额是谁
        BigDecimal amountRecord = amount;
        //通过userId查找用户的基本信息
        SysUser sysUser = userMapper.selectUserById(userId);
        int returnInt = 0;
        //先用来查项目中的返费是否发生了替换，发生替换，那么进行判断最新的替换后的是否满足
        List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByOaTraderId(oaTraderId);
        //按projectId进行分组，然后再对替换时间进行倒叙，找每个项目最新的替换时间
        Map<Long, List<CwProjectReplaceFeeCompanyInfo>> collect = cwProjectReplaceFeeCompanyInfos.stream().collect(Collectors.groupingBy(CwProjectReplaceFeeCompanyInfo::getProjectId));
        //custId集合
        List<Long> custIdList = new ArrayList<>();
        //对每一个项目进行判断
        collect.forEach((projectId, groupList) -> {
            CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo = groupList.stream().max(Comparator.comparing(CwProjectReplaceFeeCompanyInfo::getReplaceTime)).orElse(null);
            if (cwProjectReplaceFeeCompanyInfo != null) {
                if (oaTraderId.equals(cwProjectReplaceFeeCompanyInfo.getNewOaTraderId())) {
                    List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectCustListByProjectIdAndOaTraderId(cwProjectReplaceFeeCompanyInfo.getProjectId(), cwProjectReplaceFeeCompanyInfo.getOaTraderId());
                    custIdList.addAll(cwProjectCusts.stream().map(CwProjectCust::getId).collect(Collectors.toList()));
                }
            }
        });
        //加完新的集合之后，查cust表id，因为要查对应的期次，必须要有custId
        List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectNoReplaceCwProjectCustListByOaTraderId(oaTraderId);
        custIdList.addAll(cwProjectCusts.stream().map(CwProjectCust::getId).collect(Collectors.toList()));

        //筛选用户负责的项目（角色是业务）
        List<Long> projectIdList = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleTypeAndRoleType(sysUser.getUserId(), AuthModuleEnum.FINANCEPROJ.getCode(), AuthRoleEnum.CWXMGL1.getCode());

        //收集对冲完的部分信息，让项目那块生成项目打款记录用
        List<CwProjectPayFormOa> cwProjectPayFormOaList = new ArrayList<>();

        //拿到所有的custId集合，去找对应的期次，进行倒叙排列
        if (custIdList.size() > 0) {
            List<Long> distinctCustIdList = custIdList.stream().distinct().collect(Collectors.toList());
            //找所有的期次信息，里面包含了项目的部分信息，用于判断对冲的是哪个项目类型等信息
            List<Map<String, Object>> cwprojectAndIncomeInfoList = cwProjectCustMapper.selectCwprojectAndIncomeInfoByCustIds(distinctCustIdList, projectType, projectIdList);

            //后续再对冲的时候，先做之前的信息查询。步骤如下
            List<Long> incomeIdList = cwprojectAndIncomeInfoList.stream().map(t -> Long.parseLong(t.get("incomeId").toString())).collect(Collectors.toList());
            //通过incomeId去找看对应对冲表的信息
            List<CwProjectPayFormOa> cwProjectPayFormOas = cwProjectCustMapper.selectCwProjectPayFormOaByIncomeIdList(incomeIdList);
            //找出来已经对冲完的
            List<Long> alreadyOverIncomeIds = cwProjectPayFormOas.stream().filter(t -> "0".equals(t.getOverFlag())).map(CwProjectPayFormOa::getProjectIncomeId).collect(Collectors.toList());
            //把要对冲的返费列表中，已经对冲完成的给剔除掉
            cwprojectAndIncomeInfoList = cwprojectAndIncomeInfoList.stream().filter(t -> !alreadyOverIncomeIds.contains(Long.parseLong(t.get("incomeId").toString()))).collect(Collectors.toList());
            //把没有完结的给拿出来
//            List<CwProjectPayFormOa> collect1 = cwProjectPayFormOas.stream().filter(t -> "1".equals(t.getOverFlag())).collect(Collectors.toList());

            //对冲前，看是否已经
            //查到的信息就是已经按照期次时间从老到旧显示，直接从第一个开始对冲
            for (Map<String, Object> map : cwprojectAndIncomeInfoList) {
                //修改map中的对冲金额信息
                Long incomeId = Long.parseLong(map.get("incomeId").toString());
//                String projectType = map.get("projectType").toString();
                CwProjectPayFormOa cwProjectPayFormOa1 = cwProjectPayFormOas.stream().filter(t -> incomeId.equals(t.getProjectIncomeId())).findFirst().orElse(null);
                if (cwProjectPayFormOa1 != null) {
                    //说明有历史对冲的没有完成，进行金额的替换，后续进行对冲
                    if ("1".equals(projectType)) {
                        //修改法催项目的feeRoud金额
                        map.put("feeRound", cwProjectPayFormOa1.getNoAlreadyPayFee());
                    } else {
                        //修改非法催项目的actuallyPayFeeAmt金额
                        map.put("actuallyPayFeeAmt", cwProjectPayFormOa1.getNoAlreadyPayFee());
                    }
                }

                CwProjectPayFormOa cwProjectPayFormOa = new CwProjectPayFormOa();
                cwProjectPayFormOa.setOaProjectDeployId(Long.parseLong(map.get("oaProjectDeployId").toString()));
                cwProjectPayFormOa.setProjectId(Long.parseLong(map.get("projectId").toString()));
                cwProjectPayFormOa.setProjectIncomeId(incomeId);
                cwProjectPayFormOa.setStatus("0");
                cwProjectPayFormOa.setCreateId(sysUser.getUserId());
                cwProjectPayFormOa.setCreateBy(sysUser.getUserName());
                Date nowDate = DateUtils.getNowDate();
                cwProjectPayFormOa.setCreateTime(nowDate);
                cwProjectPayFormOa.setUpdateId(sysUser.getUserId());
                cwProjectPayFormOa.setUpdateBy(sysUser.getUserName());
                cwProjectPayFormOa.setUpdateTime(nowDate);
                if ("1".equals(projectType)) {
                    //对冲的是法催业务，那么就对冲他的返费取整金额
                    BigDecimal feeRound = new BigDecimal(map.get("feeRound").toString());
                    cwProjectPayFormOa.setPhaseId(Long.parseLong(map.get("phaseId").toString()));
                    cwProjectPayFormOa.setFeeRound(feeRound);
                    //对冲后剩余金额
                    amount = amount.subtract(feeRound);
                } else {
                    //对冲的是通道、分润、包含业务，那么就对冲的实付返费
                    BigDecimal actuallyPayFeeAmt = new BigDecimal(map.get("actuallyPayFeeAmt").toString());
                    cwProjectPayFormOa.setActuallyPayFeeAmt(actuallyPayFeeAmt);
                    //对冲后剩余金额
                    amount = amount.subtract(actuallyPayFeeAmt);
                }
                //2024.09.26实际上对冲了多少金额
                if (amount.compareTo(BigDecimal.ZERO) >= 0) {
                    //冲完以后amount的金额大于等于0，使用下面的公式
                    //进来的钱 - 剩余的钱 = 冲的钱
                    cwProjectPayFormOa.setAmountRecord(amountRecord.subtract(amount));
                    //进来的钱被重置成剩余的钱，用于后续的这个步骤的判断
                    amountRecord = amount;
                } else {
                    //冲完以后amount的金额小于0，说明进来的钱全冲了，还有剩余没冲的在记录
                    cwProjectPayFormOa.setAmountRecord(amountRecord);
                }
                if (amount.compareTo(BigDecimal.ZERO) > 0) {
                    //对冲完了，有剩余。可以继续对冲。继续循环
                    cwProjectPayFormOa.setOverFlag("0");
                    int a = cwProjectCustMapper.insertCwProjectPayFormOa(cwProjectPayFormOa);
                    if (a > 0) {
                        cwProjectPayFormOaList.add(cwProjectPayFormOa);
                    }
                    returnInt = a>0?returnInt + a:returnInt;
                } else if (amount.compareTo(BigDecimal.ZERO) < 0) {
                    //余额不够，无法对冲。跳出循环
                    cwProjectPayFormOa.setOverFlag("1");
                    //未对冲的金额
                    BigDecimal abs = amount.abs();
                    cwProjectPayFormOa.setNoAlreadyPayFee(abs);
                    int a = cwProjectCustMapper.insertCwProjectPayFormOa(cwProjectPayFormOa);
                    if (a > 0) {
                        cwProjectPayFormOaList.add(cwProjectPayFormOa);
                    }
                    returnInt = a>0?returnInt + a:returnInt;
                    break;
                } else {
                    //刚好对冲完，余额后续不能再对冲。那么跳出循环
                    cwProjectPayFormOa.setOverFlag("0");
                    int a = cwProjectCustMapper.insertCwProjectPayFormOa(cwProjectPayFormOa);
                    if (a > 0) {
                        cwProjectPayFormOaList.add(cwProjectPayFormOa);
                    }
                    returnInt = a>0?returnInt + a:returnInt;
                    break;
                }
            }
        }

        //然后对入库的对象按照项目id去算每个项目本次要生成的打款信息
        Map<Long, List<CwProjectPayFormOa>> groupMap = cwProjectPayFormOaList.stream().collect(Collectors.groupingBy(CwProjectPayFormOa::getProjectIncomeId));
        //要返回的集合
        List<Map<String, Object>> returnList = new ArrayList<>();
        groupMap.forEach((projectIncomeId, list1) -> {
            BigDecimal reduce = list1.stream().map(CwProjectPayFormOa::getAmountRecord).reduce(BigDecimal.ZERO, BigDecimal::add);
            Long oaProjectDeployId = list1.get(0).getOaProjectDeployId();
            Map<String, Object> map = new HashMap<>();
            map.put("projectId", oaProjectDeployId);
            map.put("amount", reduce);
            map.put("incomeId", projectIncomeId);
            returnList.add(map);
        });

        return returnList;
    }

    /**
     * 查询财务项目管理-返费公司与费率
     *
     * @param id 财务项目管理-返费公司与费率主键
     * @return 财务项目管理-返费公司与费率
     */
    @Override
    public CwProjectCust selectCwProjectCustById(Long id) {
        return cwProjectCustMapper.selectCwProjectCustById(id);
    }

    /**
     * 查询财务项目管理-返费公司与费率列表
     *
     * @param cwProjectCust 财务项目管理-返费公司与费率
     * @return 财务项目管理-返费公司与费率
     */
    @Override
    public List<CwProjectCust> selectCwProjectCustList(CwProjectCust cwProjectCust) {
        return cwProjectCustMapper.selectCwProjectCustList(cwProjectCust);
    }

    /**
     * 新增财务项目管理-返费公司与费率
     *
     * @param cwProjectCust 财务项目管理-返费公司与费率
     * @return 结果
     */
    @Override
    public int insertCwProjectCust(CwProjectCust cwProjectCust) {
        cwProjectCust.setCreateTime(DateUtils.getNowDate());
        return cwProjectCustMapper.insertCwProjectCust(cwProjectCust);
    }

    /**
     * 修改财务项目管理-返费公司与费率
     *
     * @param cwProjectCust 财务项目管理-返费公司与费率
     * @return 结果
     */
    @Override
    public int updateCwProjectCust(CwProjectCust cwProjectCust) {
        cwProjectCust.setUpdateTime(DateUtils.getNowDate());
        return cwProjectCustMapper.updateCwProjectCust(cwProjectCust);
    }

    /**
     * 批量删除财务项目管理-返费公司与费率
     *
     * @param ids 需要删除的财务项目管理-返费公司与费率主键
     * @return 结果
     */
    @Override
    public int deleteCwProjectCustByIds(Long[] ids) {
        return cwProjectCustMapper.deleteCwProjectCustByIds(ids);
    }

    /**
     * 删除财务项目管理-返费公司与费率信息
     *
     * @param id 财务项目管理-返费公司与费率主键
     * @return 结果
     */
    @Override
    public int deleteCwProjectCustById(Long id) {
        return cwProjectCustMapper.deleteCwProjectCustById(id);
    }

    /**
     * 查询财务项目管理-返费公司明细查询-初次进入页面-查询所有返费公司名称
     *
     * @return 财务项目管理-返费公司明细查询-初次进入页面-查询返费公司列表
     */
    @Override
    public Map<String, Object> selectCwProjectListFirst(LoginUser loginUser, List<Long> projectIds) {
//        Long userId = loginUser.getUserId();
//        List<SysRole> roles = loginUser.getUser().getRoles();
//        boolean b = roles.stream().anyMatch(r -> "admin".equals(r.getRoleKey()) || "caiwuAdmin".equals(r.getRoleKey()) || "renshi".equals(r.getRoleKey()) || "yewuAdmin".equals(r.getRoleKey()));
//        if (b) {
//            //如果是超级管理员，财务管理员，人事，业务管理员，则可以查看所有的项目
//            userId = null;
//        }
        Map<String, Object> map = new HashMap<>();
        Set<String> custNameSet = new HashSet<>();
        //先找到返费公司表的信息
        if (projectIds.size() == 0) {
            //如果用户没有项目id，说明他看不到项目的相关权限。直接返回空
            return map;
        }
        List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectListFirstByadmin(projectIds);
        //找到oaTraderId集合跟projectId集合
        List<Long> oaTraderIdList = cwProjectCusts.stream().map(CwProjectCust::getOaTraderId).distinct().collect(Collectors.toList());
        List<Long> projectIdList = cwProjectCusts.stream().map(CwProjectCust::getProjectId).distinct().collect(Collectors.toList());
        //用projectId集合去查找替换记录表中所有出现过projectId的
        List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByProjectIdListAndStatus(projectIdList, "0");
        //找到了特定范围内的oaTraderId
        List<Long> oaTraderIdListByprojectIdList = cwProjectReplaceFeeCompanyInfos.stream().map(CwProjectReplaceFeeCompanyInfo::getNewOaTraderId).distinct().collect(Collectors.toList());
        //找到了所有的oaTraderId
        oaTraderIdList.addAll(oaTraderIdListByprojectIdList);
        List<Long> finalOaTraderIdList = oaTraderIdList.stream().distinct().collect(Collectors.toList());
        //去oa_trader表找所有上面查找出来的所有oaTraderId的信息
        if (finalOaTraderIdList.size() != 0) {
            List<OaTrader> oaTraders = oaTraderMapper.selectOaTraderListByIdList(finalOaTraderIdList);
            oaTraders.stream().map(OaTrader::getUserName).forEach(custNameSet::add);
            map.put("cust_name", custNameSet);
        }
        return map;
    }

    /**
     * 查询财务项目管理-返费公司与费率
     *
     * @param cwProjectCust 财务项目管理-返费公司对象
     * @return 财务项目管理-返费公司明细查询
     */
    @Override
    public Map<String, Object> selectCwProjectCustListDetail(CwProjectCust cwProjectCust, LoginUser loginUser, List<Long> projectIds) {
        //整体思路：先那名字去找oa_trader表（查询结果可能不止一个，账号可能不一样，名字一样）。
        //然后找到这些以后，去cw_project_replace_fee_company_info找所涉及的projectId。
        //如果cw_project_replace_fee_company_info没有，去cust表找所涉及的projectId。
        //这两个步骤必须以此进行，因为有一些项目并没有发生替换。需要两个表都查一下。后续做的时候看看能不能联查查一次
        //然后找到以后就依次进行展示处理就行
        BigDecimal zero = new BigDecimal("0.00");
        Map<String, Object> mmp = new HashMap<>();
        Map<String, Object> mm = new HashMap<>();
        List<Map<String, Object>> result = new ArrayList<>();
        List<Map<String, Object>> resultForLaw = new ArrayList<>();
        //模糊查询-返费公司名称
        if (StringUtils.isEmpty(cwProjectCust.getCustName()) && StringUtils.isEmpty(cwProjectCust.getProjectType())) {
            Map<String, Object> map = new HashMap<>();
            map.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NO_SELECT_PARAM.getCode());
            map.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NO_SELECT_PARAM.getMsg());
            mm.put("info", map);
            mm.put("projectTypeFlag", "99999");
            return mm;
        } else {
            //通过前端传过来的返费公司名称，查oa_trader表
            OaTrader oaTrader = new OaTrader();
            oaTrader.setUserName(cwProjectCust.getCustName());
            List<OaTrader> oaTraders = oaTraderMapper.selectOaTraderList(oaTrader);
            List<Long> noReplaceOaTraderIds = oaTraders.stream().map(OaTrader::getId).collect(Collectors.toList());
            List<Long> noReplaceProjectIdList = cwProjectMapper.selectCwProjectIdsByOaTraderIdList(noReplaceOaTraderIds);
            //找到之后， 把id找出来
            List<Long> oaTraderIdList = oaTraders.stream().filter(t -> t.getUserName().equals(cwProjectCust.getCustName())).map(OaTrader::getId).distinct().collect(Collectors.toList());
            if (oaTraderIdList.size() == 0) {
                Map<String, Object> map = new HashMap<>();
                map.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
                map.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
                mm.put("info", map);
                mm.put("projectTypeFlag", "99999");
                return mm;
            }
            //发生过替换的oaTraderId去找projectId
            //发生过替换的最新的公司
            List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByOaTraderIdsAndStatus(oaTraderIdList, "0");
            //找到了所有的projectId
            //然后去遍历项目的 项目id，项目名，项目类型
            List<Long> projectIdList = cwProjectReplaceFeeCompanyInfos.stream().map(CwProjectReplaceFeeCompanyInfo::getProjectId).distinct().collect(Collectors.toList());
            projectIdList.addAll(noReplaceProjectIdList);
            if (projectIdList.size() == 0) {
                //说明没有替换，那么就把之前oaTraderIdList所设计的所有项目id拿过来
                projectIdList = cwProjectMapper.selectCwProjectIdsByOaTraderIdList(oaTraderIdList);
                if (projectIdList.size() == 0) {
                    Map<String, Object> map = new HashMap<>();
                    map.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
                    map.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
                    mm.put("info", map);
                    mm.put("projectTypeFlag", "99999");
                    return mm;
                }
            }
            //这一次不光要去找符合项目的信息，还要看用户是否有权限查看该项目
            //老前置条件
            String custName = cwProjectCust.getCustName();
            if ("all".equals(cwProjectCust.getProjectType())) {
                cwProjectCust.setProjectType(StrUtil.EMPTY);
            }
            String projectType = cwProjectCust.getProjectType();
//            List<SysRole> roles = loginUser.getUser().getRoles();
//            boolean b = roles.stream().anyMatch(r -> "admin".equals(r.getRoleKey()) || "caiwuAdmin".equals(r.getRoleKey()) || "renshi".equals(r.getRoleKey()) || "yewuAdmin".equals(r.getRoleKey()));
//            Long userId = null;
//            if (!b) {
//                userId = loginUser.getUserId();
//            }
            List<Map<String, Object>> list = new ArrayList<>();
            if (projectIds.size() != 0) {
                list = cwProjectCustMapper.selectCwProjectCustListDetailByProjectIds(projectIdList, projectType, projectIds);
            }
//            List<Map<String, Object>> list = cwProjectCustMapper.selectCwProjectCustListDetailByProjectIds(projectIdList, userId, projectType);
            list = list.stream().distinct().collect(Collectors.toList());
            if (list.size() == 0) {
                Map<String, Object> map = new HashMap<>();
                map.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
                map.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
                mm.put("info", map);
                mm.put("projectTypeFlag", "99999");
                return mm;
            }
            List<Map<String, Object>> resultList = new ArrayList<>();
            //项目的基本信息都已经得到，那么就去做业务的处理
            for (Map<String, Object> map:list) {
                Long projectId = (Long) map.get("id");
                //拿到关联的OA项目id
                Long oaProjectDeployId = Long.parseLong(map.get("oaProjectDeployId").toString());
                //返费明细表
                List<Map<String, Object>> feeDetailList = new ArrayList<>();
                //todo 打款明细表（暂时没办法做）
                //todo 返费合计（暂时没办法做，只能做出一小块 --> 返费取整金额）
                //找查询的返费
                List<CwProjectReplaceFeeCompanyInfo> collect = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> t.getProjectId().equals(projectId)).collect(Collectors.toList());
                List<Long> custIdList = collect.stream().map(CwProjectReplaceFeeCompanyInfo::getCustId).distinct().collect(Collectors.toList());
                //然后去遍历每个期次的信息，找到期次的custId，然后去替换表里找他是属于哪一个替换类型，以标注他是被替换还是替换
                if ("1".equals(map.get("project_type"))) {
                    //说明是法催，法催的话单独处理
                    //先找返费明细表

                    //todo todo 前置查询有问题，应该过滤掉不存在custId的期次
                    List<CwProjectIncome> lawPhaseAndIncomeList = cwProjectIncomeMapper.selectcwprojectincomeLawListAllByProjectId(projectId);
                    //找到期次
                    List<CwProjectIncome> phaseList = lawPhaseAndIncomeList.stream().filter(g -> "0".equals(g.getPhaseFlag())).collect(Collectors.toList());
                    //找到的期次是所有的期次，过滤掉没有包含custId的期次
                    List<Long> phaseIdList =  cwProjectIncomeMapper.selectCwprojectIncomeLawPhaseListByProjectIdAndCustIds(projectId, custIdList);
                    phaseList= phaseList.stream().filter(t -> phaseIdList.contains(t.getId())).collect(Collectors.toList());
                    //找所有的返费信息
                    for (CwProjectIncome phase:phaseList) {
                        Map<String, Object> feeDetail = new HashMap<>();
                        //法催项目，每个期次只有一个返费公司，所以直接使用查找
                        Map<String, Object> custObj = cwProjectCustMapper.selectCwProjectcustByPhaseId(phase.getId());
                        //通过期次id找到对应的
                        String replaceFlag = (String) custObj.get("replaceFlag");
                        Long custId = (Long) custObj.get("custId");
                        Long oaTraderId = Long.parseLong(custObj.get("oaTraderId").toString());
                        List<BigDecimal> feeList = cwProjectFeeMapper.selectLawAllFeeAmtByPhaseId(phase.getId(), custIdList);
                        List<BigDecimal> feeRoundList = cwProjectFeeMapper.selectLawAllFeeByPhaseId1(phase.getId(), custIdList);
                        BigDecimal feeRoundAmt = feeRoundList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal feeAmt = feeList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                        if ("0".equals(replaceFlag)) {
                            //没有发生替换，那么输入的名称就是返费的名称
                            feeDetail.put("feeName", custName);
                            //feeNameStatus为0，代表没有发生替换
                            feeDetail.put("feeNameStatus", "0");
                        } else {
                            //去库里找最新的返费的名字
                            String feeName  = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
                            if (feeName.equals(cwProjectCust.getCustName())) {
                                //说明是查取的公司替换了以前的公司，那么就展示以前的公司名
                                String oldFeeName = cwProjectCustMapper.selectOldOaTraderUserNameByCustIdAndNewOaTraderUserName(custId, feeName);
                                feeDetail.put("feeName", oldFeeName);
                                //feeNameStatus为1，代表发生替换，展示曾用公司
                                feeDetail.put("feeNameStatus", "1");
                            } else {
                                //说明是查取的公司被新的公司替换了，那么就展示现在最新的公司名
                                feeDetail.put("feeName", feeName);
                                //feeNameStatus为2，代表发生替换，展示最新公司
                                feeDetail.put("feeNameStatus", "2");
                            }
                        }
                        feeDetail.put("term", phase.getTermMonth());
                        feeDetail.put("phaseStatus", phase.getPhaseStatus());
                        feeDetail.put("feeRoundAmt", feeRoundAmt);
                        feeDetail.put("feeAmt", feeAmt);
                        feeDetail.put("replaceFlag", replaceFlag);
                        feeDetailList.add(feeDetail);
                    }
                    //返费项目列表
                    map.put("feeDetailList", feeDetailList);
                    Map<String, Object> lawProjectSumByProjectId = this.getLawProjectSumByProjectId(projectId);
                    map.put("sum", lawProjectSumByProjectId);
                    //打款明细
                    List<OaPayRebateRecordVo> flowAlreadyPayInfoFromOA = oaPayRebateRecordMapper.getFlowAlreadyPayInfoFromOA(oaProjectDeployId);
                    Collections.reverse(flowAlreadyPayInfoFromOA);
                    map.put("payDetail", flowAlreadyPayInfoFromOA);
                } else {
                    //项目是通道、分润项目
                    //找对应的期次
                    List<CwProjectIncome> incomeListNormal = cwProjectIncomeMapper.selectcwprojectincomeListAllByProjectId(projectId);
                    //找到的期次是所有的期次，过滤掉没有包含custId的期次
                    List<Long> incomeList =  cwProjectIncomeMapper.selectCwprojectIncomeListByProjectIdAndCustIds(projectId, custIdList);
                    incomeListNormal = incomeListNormal.stream().filter(t -> incomeList.contains(t.getId())).collect(Collectors.toList());
                    for (CwProjectIncome ci:incomeListNormal) {
                        Map<String, Object> feeDetail = new HashMap<>();
                        //通道、分润项目，每个期次可能有多个返费公司
                        List<Map<String, Object>> custObjList = cwProjectCustMapper.selectCwProjectcustByIncomeId(ci.getId());
                        //只有全部的返费公司没有发生替换，才能说明没有发生替换。
                        //为什么要这么说   因为可以有同名的返费公司，两个的id一个为1一个为2，他俩在需求当中是合并为一个公司进行统计的
                        boolean replaceFlag = custObjList.stream().allMatch(t -> "0".equals(t.get("replaceFlag")));
                        List<Long> custIds = new ArrayList<>();
                        List<Long> oaTraderIds = new ArrayList<>();
                        custObjList.forEach(t -> {
                            Long custId = (Long) t.get("custId");
                            Long oaTraderId = Long.parseLong(t.get("oaTraderId").toString());
                            custIds.add(custId);
                            oaTraderIds.add(oaTraderId);
                        });
                        List<BigDecimal> shouldPayFeeAmtList = cwProjectFeeMapper.selectAllShouldPayFeeAmtByIncomeIdAndCustIds(ci.getId(), custIdList);
                        List<BigDecimal> actuallyPayFeeAmtList = cwProjectFeeMapper.selectAllActuallyPayFeeAmtByIncomeIdAndCustIds(ci.getId(), custIdList);
                        BigDecimal shouldPayFeeAmt = shouldPayFeeAmtList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal actuallyPayFeeAmt = actuallyPayFeeAmtList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (replaceFlag) {
                            //没有发生替换，那么输入的名称就是返费的名称
                            feeDetail.put("feeName", custName);
                            feeDetail.put("feeNameStatus", "0");
                        } else {
                            //去库里找最新的返费的名字
                            boolean feeNameFlag = false;
                            Long custId = null;
                            String feeName = StringUtils.EMPTY;
                            List<String> feeNameList =  new ArrayList<>();
                            for (Long a:custIds) {
                                String s = cwProjectCustMapper.selectCwprojectFeeNameByCustId(a);
                                if (s == null) {
                                    //说明是没替换的，把这条略过
                                    continue;
                                }
                                feeNameList.add(s);
                                if (s.equals(cwProjectCust.getCustName())) {
                                    //说明这个发生了替换
                                    feeNameFlag = true;
                                    custId = a;
                                    break;
                                }
                            }
                            if (feeNameFlag) {
                                //说明是查取的公司替换了以前的公司，那么就展示以前的公司名
                                feeName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
                                String oldFeeName = cwProjectCustMapper.selectOldOaTraderUserNameByCustIdAndNewOaTraderUserName(custId, feeName);
                                feeDetail.put("feeName", oldFeeName);
                                feeDetail.put("feeNameStatus", "1");
                            } else {
                                //说明是查取的公司被新的公司替换了，那么就展示现在最新的公司名
                                feeDetail.put("feeName", feeNameList.get(0));
                                feeDetail.put("feeNameStatus", "2");
                            }
                        }
                        feeDetail.put("term", ci.getTermMonth());
                        feeDetail.put("phaseStatus", ci.getPhaseStatus());
                        feeDetail.put("shouldPayFeeAmt", shouldPayFeeAmt);
                        feeDetail.put("actuallyPayFeeAmt", actuallyPayFeeAmt);
                        feeDetail.put("replaceFlag", replaceFlag);
                        feeDetailList.add(feeDetail);
                    }
                    //返费项目列表
                    map.put("feeDetailList", feeDetailList);
                    //todo 打款明细表
                    //todo 项目合计
                    Map<String, Object> projectSumByProjectId = this.getProjectSumByProjectId(projectId);
                    map.put("sum", projectSumByProjectId);
                    //打款明细
                    List<OaPayRebateRecordVo> flowAlreadyPayInfoFromOA = oaPayRebateRecordMapper.getFlowAlreadyPayInfoFromOA(oaProjectDeployId);
                    Collections.reverse(flowAlreadyPayInfoFromOA);
                    map.put("payDetail", flowAlreadyPayInfoFromOA);
                }
                resultList.add(map);
            }
            Map<String, Object> map = new HashMap<>();
            map.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
            map.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
            map.put("list", resultList);
            return map;
        }
    }

    /**
     * 查询财务项目管理-完结项目归档查询-初次进入页面-查询所有已经完结的项目
     *
     * @return 财务项目管理-完结项目归档查询-初次进入页面
     */
    @Override
    public Map<String, Object> selectCwProjectOverListFirst(LoginUser loginUser) {
        Long userId = loginUser.getUserId();
        List<SysRole> roles = loginUser.getUser().getRoles();
        boolean b = roles.stream().anyMatch(r -> "admin".equals(r.getRoleKey()) || "caiwuAdmin".equals(r.getRoleKey()) || "renshi".equals(r.getRoleKey()) || "yewuAdmin".equals(r.getRoleKey()));
        if (b) {
            //如果是超级管理员，财务管理员，人事，业务管理员，则可以查看所有的项目
            userId = null;
        }
        Map<String, Object> map = new HashMap<>();
        Set<String> custNameSet = new HashSet<>();
        Set<String> incomeCustNameSet = new HashSet<>();
        cwProjectMapper.selectCwProjectListFirst(userId).forEach(
                t -> {
                    //担保公司
                    custNameSet.add(t.getCustName());
                    //汇款公司
                    incomeCustNameSet.add(t.getIncomeCustName());
                }
        );
        map.put("cust_name", custNameSet);
        map.put("income_cust_name", incomeCustNameSet);
        return map;
    }

    /**
     * 查询财务项目管理-列表查询
     *
     * @param cwProject 财务项目管理-主表对象
     * @return 财务项目管理-列表查询
     */
    @Override
    public Map<String, Object> selectCwProjectOversList(CwProject cwProject, String startDate, String endDate, Integer sumFlag, Long userId, LoginUser loginUser, List<Long> projectIds) {
        Map<String, Object> resp = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        AtomicReference<BigDecimal> incomeAmtAllSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> feeAmtAllSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> feeAmt2AllSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> grossProfitAmtAllSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> grossProfitAmt2AllSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> feeAmtAlreadyAllSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> feeAmtNoAlreadyAllSum = new AtomicReference<>(new BigDecimal("0.00"));
        SimpleDateFormat sbf = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat df2=new SimpleDateFormat("yyyy-MM-dd");
        //模糊查询-项目名称、担保公司、汇款公司
        List<String> role = cwProjectUserMapper.selectUserRoleIsAdmin(userId);
        boolean present = loginUser.getUser().getRoles().stream().anyMatch(r -> "admin".equals(r.getRoleKey()) || "caiwuAdmin".equals(r.getRoleKey()) || "renshi".equals(r.getRoleKey()) || "yewuAdmin".equals(r.getRoleKey()));
        //先找到主表的id
        Collator orderByChinese = Collator.getInstance(Locale.CHINESE);
        //条件查询所有符合的主表
//        List<Long> pIds = cwProjectShowService.getProjectId(loginUser,"0");
        List<CwProject> cwProjects;
        //下面这个if中的两个判断条件不能对调位置，否则报错
        if (projectIds == null || 0 != projectIds.size()) {
            cwProjects = cwProjectMapper.selectCwProjectByIds(projectIds, "0", cwProject);
        } else {
            cwProjects = new ArrayList<>();
        }
        Collections.sort(cwProjects , (e1, e2) -> {
            //两个参数是第一个是这个list，后面是进入的第一个和第二个，然后他的projectName进行比较
            return orderByChinese.compare(e1.getProjectName(), e2.getProjectName());
        });
        //条件查询所有符合的主表
        AtomicReference<BigDecimal> finalFeeNoAlreadyPay = new AtomicReference<>(new BigDecimal("0.00"));
        //查询项目类型数据集  组合一级二级名称
        Map<Long, OaDataManage> projectTypeMap = oaDataManageMapper.selectDataManageListByCode("project_type").stream()
                .filter(vo -> vo.getParentId() != null) //过滤最上一级节点
                .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
        //找出来的主表信息，1项目=n期次
        cwProjects.forEach(t -> {
            //2023.12.22 财务项目管理五期，对OA产生的打款数据进行统计，作为本项目的已经支付的返费
            BigDecimal feeAlreadyPay = oaPayRebateRecordMapper.selectOaPayRebateRecordByProjectId(t.getOaProjectDeployId());
            if (feeAlreadyPay == null) {
                feeAlreadyPay = BigDecimal.ZERO;
            }
            BigDecimal finalFeeAlreadyPay = feeAlreadyPay;
            Map<String, Object> map = new HashMap<>();
            //找对应的项目的用户情况
            List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(t.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
            //会计
            List<String> accountantList = userList.stream().filter(a -> "1".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
            //出纳
            List<String> cashierList = userList.stream().filter(a -> "2".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
            //业务
            List<String> businessList = userList.stream().filter(a -> "3".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
            //查看权限
            List<String> selectList = userList.stream().filter(a -> "88".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
//            //找user表
//            List<Map<String, Object>> users = cwProjectUserMapper.selectCwProjectUserByProjectId(t.getId());
//            List<String> accountantList = new ArrayList<>();
//            List<String> cashierList = new ArrayList<>();
//            List<String> businessList = new ArrayList<>();
//            List<String> selectList = new ArrayList<>();
            List<String> exportList = new ArrayList<>();
//            users.forEach(u -> {
//                if (CwxmglConstants.USER_FLAG_0.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                    String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                    accountantList.add(o);
//                } else if (CwxmglConstants.USER_FLAG_1.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                    String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                    cashierList.add(o);
//                } else if (CwxmglConstants.USER_FLAG_2.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                    String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                    businessList.add(o);
//                } else if (CwxmglConstants.USER_FLAG_3.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                    String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                    selectList.add(o);
//                } else if (CwxmglConstants.USER_FLAG_4.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                    String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                    exportList.add(o);
//                }
//            });
            //处理项目所属的各个期次，只需要处理数据即可
            //定义项目总的数据变量
            AtomicReference<BigDecimal> incomeAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> feeAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> feeAmt2Sum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> grossProfitAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> grossProfitAmt2Sum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> feeAmtAlreadySum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> feeAmtNoAlreadySum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicInteger allPhaseCount = new AtomicInteger();
            List<CwProjectIncome> cwProjectIncomes = cwProjectIncomeMapper.selectCwProjectIncomeAllInfoByProjectId(t.getId());
            if (0 == cwProjectIncomes.size() && ((startDate == null) || (endDate ==null))) {
                //更改逻辑，只要能查出来的都是可控制的
                map.put("controlFlag", "0");
                //项目id
                map.put("id", t.getId());
                //项目类型
                map.put("project_type", t.getProjectType());
                String dataNameWithParents = getDataNameWithParents(t.getProjectTypeId(), projectTypeMap);
                if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                    dataNameWithParents = dataNameWithParents.substring(1);
                }
                map.put("projectType", dataNameWithParents);
                map.put("projectPortfolioCode",t.getProjectPortfolioCode());
                //map.put("projectType", t.getProjectTypeChinese());
                //项目名称
                map.put("project_name", t.getProjectName());
                //担保公司
                map.put("cust_name", t.getCustName());
                //汇款公司
                map.put("income_cust_name", t.getIncomeCustName());
                //该项目的所有期次都弄完了以后，开始放入该项目的数据
                map.put("income_amt", incomeAmtSum);
                map.put("fee_amt", feeAmtSum);
                map.put("fee_amt2", feeAmt2Sum);
                map.put("gross_profit_amt", grossProfitAmtSum);
                map.put("gross_profit_amt2", grossProfitAmt2Sum);
                BigDecimal feeNoAlreadyPay = feeAmtSum.get().subtract(finalFeeAlreadyPay);
                finalFeeNoAlreadyPay.set(feeNoAlreadyPay);
                map.put("fee_no_already", finalFeeNoAlreadyPay.get());
                map.put("allPhase", allPhaseCount);

                map.put("accountant_list", accountantList);
                map.put("cashier_list", cashierList);
                map.put("business_list", businessList);
                map.put("select_list", selectList);
                map.put("export_list", exportList);
                //财务项目管理五期，拼接新增的字段
                map.put("feeAlreadyPay", finalFeeAlreadyPay);
                map.put("generateCertificateFlag", t.getGenerateCertificateFlag());
                map.put("guaranteeIncomeType", t.getGuaranteeIncomeType());
                map.put("payeeAbbreviation", t.getPayeeAbbreviation());
                //给一个id的唯一标识
                map.put("projectIddd:" + t.getId(), "99");
                list.add(map);
            }
            List<CwProjectIncome> collect = cwProjectIncomes.stream().filter(i -> "0".equals(i.getTerm())).collect(Collectors.toList());
            List<CwProjectIncome> collect1 = cwProjectIncomes.stream().filter(ii -> "1".equals(ii.getTerm())).collect(Collectors.toList());
            //筛选出来是整月的。进行时间对比
            collect.forEach(i -> {
                CwProjectIncome cwi = cwProjectIncomeMapper.selectCwProjectIncomeTerm2ById(i.getId(), startDate, endDate);
                if (cwi != null) {
                    if (Optional.ofNullable(cwi.getIncomeAmt()).isPresent()) {
                        incomeAmtSum.set(incomeAmtSum.get().add(cwi.getIncomeAmt()));
                        incomeAmtAllSum.set(incomeAmtAllSum.get().add(cwi.getIncomeAmt()));
                    }
                    if (Optional.ofNullable(cwi.getGrossProfitAmt()).isPresent()) {
                        grossProfitAmtSum.set(grossProfitAmtSum.get().add(cwi.getGrossProfitAmt()));
                        grossProfitAmtAllSum.set(grossProfitAmtAllSum.get().add(cwi.getGrossProfitAmt()));
                    }
                    if (Optional.ofNullable(cwi.getGrossProfitAmt2()).isPresent()) {
                        grossProfitAmt2Sum.set(grossProfitAmt2Sum.get().add(cwi.getGrossProfitAmt2()));
                        grossProfitAmt2AllSum.set(grossProfitAmt2AllSum.get().add(cwi.getGrossProfitAmt2()));
                    }
                    //普通项目查返费表，用incomeId查
                    CwProjectFee cwf = null;
                    if ("0".equals(t.getProjectType()) || "2".equals(t.getProjectType()) || "3".equals(t.getProjectType())) {
                        cwf = cwProjectFeeMapper.selectCwProjectCustFeeListDetailByIncomeId4(i.getId());
                    } else if ("1".equals(t.getProjectType())) {
                        //法催项目查返费表，用phaseId查
                        List<BigDecimal> feeAmtList = cwProjectFeeMapper.selectLawAllFeeByPhaseId(i.getId());
                        List<BigDecimal> feeAmt2List = cwProjectFeeMapper.selectLawAllFee2ByPhaseId(i.getId());
                        BigDecimal feeAmt = feeAmtList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal feeAmt2 = feeAmt2List.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (feeAmt != null){
                            cwf = new CwProjectFee();
                            cwf.setFeeAmt(feeAmt);
                            cwf.setFeeAmt2(feeAmt2);
                        }
                    }
                    if (cwf != null) {
                        feeAmtSum.set(feeAmtSum.get().add(cwf.getFeeAmt()));
                        feeAmt2Sum.set(feeAmt2Sum.get().add(cwf.getFeeAmt2()));
                        feeAmtAllSum.set(feeAmtAllSum.get().add(cwf.getFeeAmt()));
                        feeAmt2AllSum.set(feeAmt2AllSum.get().add(cwf.getFeeAmt2()));
                    }
                    //处理期次未完成条数
                    int c = 0;
                    if ("0".equals(t.getProjectType()) || "2".equals(t.getProjectType()) || "3".equals(t.getProjectType())) {
                        //普通项目
                        c = cwProjectIncomeMapper.selectCwProjectIncomeAllPhaseCountById(i.getId());
                    } else if ("1".equals(t.getProjectType())) {
                        //法催项目
                        c = cwProjectIncomeMapper.selectCwProjectLawIncomeAllPhaseCountById(i.getId());
                    }
                    allPhaseCount.set(allPhaseCount.get() + c);
                    if (!map.containsKey("projectIddd:"+t.getId())) {
                        //更改逻辑，只要能查出来的都是可控制的
                        map.put("controlFlag", "0");
                        //项目id
                        map.put("id", t.getId());
                        //项目类型
                        map.put("project_type", t.getProjectType());
                        String dataNameWithParents = getDataNameWithParents(t.getProjectTypeId(), projectTypeMap);
                        if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                            dataNameWithParents = dataNameWithParents.substring(1);
                        }
                        map.put("projectType", dataNameWithParents);
                        map.put("projectPortfolioCode",t.getProjectPortfolioCode());
                        //map.put("projectType", t.getProjectTypeChinese());
                        //项目名称
                        map.put("project_name", t.getProjectName());
                        //担保公司
                        map.put("cust_name", t.getCustName());
                        //汇款公司
                        map.put("income_cust_name", t.getIncomeCustName());
                        //该项目的所有期次都弄完了以后，开始放入该项目的数据
                        map.put("income_amt", incomeAmtSum);
                        map.put("fee_amt", feeAmtSum);
                        map.put("fee_amt2", feeAmt2Sum);
                        map.put("gross_profit_amt", grossProfitAmtSum);
                        map.put("gross_profit_amt2", grossProfitAmt2Sum);
                        BigDecimal feeNoAlreadyPay = feeAmtSum.get().subtract(finalFeeAlreadyPay);
                        finalFeeNoAlreadyPay.set(feeNoAlreadyPay);
                        if (Optional.ofNullable(finalFeeAlreadyPay).isPresent()) {
                            feeAmtAlreadySum.set(feeAmtAlreadySum.get().add(finalFeeAlreadyPay));
                            feeAmtAlreadyAllSum.set(feeAmtAlreadyAllSum.get().add(finalFeeAlreadyPay));
                        }
                        if (Optional.ofNullable(finalFeeNoAlreadyPay.get()).isPresent()) {
                            feeAmtNoAlreadySum.set(feeAmtNoAlreadySum.get().add(finalFeeNoAlreadyPay.get()));
                            feeAmtNoAlreadyAllSum.set(feeAmtNoAlreadyAllSum.get().add(finalFeeNoAlreadyPay.get()));
                        }
                        map.put("fee_no_already", finalFeeNoAlreadyPay.get());
                        map.put("allPhase", allPhaseCount);
                        map.put("accountant_list", accountantList);
                        map.put("cashier_list", cashierList);
                        map.put("business_list", businessList);
                        map.put("select_list", selectList);
                        map.put("export_list", exportList);
                        //财务项目管理五期，拼接新增的字段
                        map.put("feeAlreadyPay", finalFeeAlreadyPay);
                        map.put("generateCertificateFlag", t.getGenerateCertificateFlag());
                        map.put("guaranteeIncomeType", t.getGuaranteeIncomeType());
                        map.put("payeeAbbreviation", t.getPayeeAbbreviation());
                        //给一个id的唯一标识
                        map.put("projectIddd:" + t.getId(), "0");
                        list.add(map);
                    }
                }
            });
            //筛选出来是非整月的。进行时间对比
            collect1.forEach(i -> {
                //首先是否是跨月的？
                String fmtBegin = sbf.format(i.getTermBegin());
                String fmtEnd = sbf.format(i.getTermEnd());
                if (fmtBegin.equals(fmtEnd)) {
                    //不跨月，那就看是否在操作的时间范围内
                    String endDate1 = endDate + "-31";
                    CwProjectIncome cwi = cwProjectIncomeMapper.selectCwProjectIncomeTerm3ById(i.getId(), startDate, endDate1);
                    if (cwi != null) {
                        if (Optional.ofNullable(cwi.getIncomeAmt()).isPresent()) {
                            incomeAmtSum.set(incomeAmtSum.get().add(cwi.getIncomeAmt()));
                            incomeAmtAllSum.set(incomeAmtAllSum.get().add(cwi.getIncomeAmt()));
                        }
                        if (Optional.ofNullable(cwi.getGrossProfitAmt()).isPresent()) {
                            grossProfitAmtSum.set(grossProfitAmtSum.get().add(cwi.getGrossProfitAmt()));
                            grossProfitAmtAllSum.set(grossProfitAmtAllSum.get().add(cwi.getGrossProfitAmt()));
                        }
                        if (Optional.ofNullable(cwi.getGrossProfitAmt2()).isPresent()) {
                            grossProfitAmt2Sum.set(grossProfitAmt2Sum.get().add(cwi.getGrossProfitAmt2()));
                            grossProfitAmt2AllSum.set(grossProfitAmt2AllSum.get().add(cwi.getGrossProfitAmt2()));
                        }
                        //普通项目查返费表，用incomeId查
                        CwProjectFee cwf = null;
                        if ("0".equals(t.getProjectType()) || "2".equals(t.getProjectType()) || "3".equals(t.getProjectType())) {
                            cwf = cwProjectFeeMapper.selectCwProjectCustFeeListDetailByIncomeId4(i.getId());
                        } else if ("1".equals(t.getProjectType())) {
                            //法催项目查返费表，用phaseId查
                            List<BigDecimal> feeAmtList = cwProjectFeeMapper.selectLawAllFeeByPhaseId(i.getId());
                            List<BigDecimal> feeAmt2List = cwProjectFeeMapper.selectLawAllFee2ByPhaseId(i.getId());
                            BigDecimal feeAmt = feeAmtList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal feeAmt2 = feeAmt2List.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                            if (feeAmt != null){
                                cwf = new CwProjectFee();
                                cwf.setFeeAmt(feeAmt);
                                cwf.setFeeAmt2(feeAmt2);
                            }
                        }
                        if (cwf != null) {
                            feeAmtSum.set(feeAmtSum.get().add(cwf.getFeeAmt()));
                            feeAmt2Sum.set(feeAmt2Sum.get().add(cwf.getFeeAmt2()));
                            feeAmtAllSum.set(feeAmtAllSum.get().add(cwf.getFeeAmt()));
                            feeAmt2AllSum.set(feeAmt2AllSum.get().add(cwf.getFeeAmt2()));
                        }
                        //处理期次未完成条数
                        int c = 0;
                        if ("0".equals(t.getProjectType()) || "2".equals(t.getProjectType()) || "3".equals(t.getProjectType())) {
                            //普通项目
                            c = cwProjectIncomeMapper.selectCwProjectIncomeAllPhaseCountById(i.getId());
                        } else if ("1".equals(t.getProjectType())) {
                            //法催项目
                            c = cwProjectIncomeMapper.selectCwProjectLawIncomeAllPhaseCountById(i.getId());
                        }
                        allPhaseCount.set(allPhaseCount.get() + c);
                        if (!map.containsKey("projectIddd:"+t.getId())) {
                            //更改逻辑，只要能查出来的都是可控制的
                            map.put("controlFlag", "0");
                            //项目id
                            map.put("id", t.getId());
                            //项目类型
                            map.put("project_type", t.getProjectType());
                            String dataNameWithParents = getDataNameWithParents(t.getProjectTypeId(), projectTypeMap);
                            if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                                dataNameWithParents = dataNameWithParents.substring(1);
                            }
                            map.put("projectType", dataNameWithParents);
                            map.put("projectPortfolioCode",t.getProjectPortfolioCode());
                            //map.put("projectType", t.getProjectTypeChinese());
                            //项目名称
                            map.put("project_name", t.getProjectName());
                            //担保公司
                            map.put("cust_name", t.getCustName());
                            //汇款公司
                            map.put("income_cust_name", t.getIncomeCustName());
                            //该项目的所有期次都弄完了以后，开始放入该项目的数据
                            map.put("income_amt", incomeAmtSum);
                            map.put("fee_amt", feeAmtSum);
                            map.put("fee_amt2", feeAmt2Sum);
                            map.put("gross_profit_amt", grossProfitAmtSum);
                            map.put("gross_profit_amt2", grossProfitAmt2Sum);
                            BigDecimal feeNoAlreadyPay = feeAmtSum.get().subtract(finalFeeAlreadyPay);
                            finalFeeNoAlreadyPay.set(feeNoAlreadyPay);
                            if (Optional.ofNullable(finalFeeAlreadyPay).isPresent()) {
                                feeAmtAlreadySum.set(feeAmtAlreadySum.get().add(finalFeeAlreadyPay));
                                feeAmtAlreadyAllSum.set(feeAmtAlreadyAllSum.get().add(finalFeeAlreadyPay));
                            }
                            if (Optional.ofNullable(finalFeeNoAlreadyPay.get()).isPresent()) {
                                feeAmtNoAlreadySum.set(feeAmtNoAlreadySum.get().add(finalFeeNoAlreadyPay.get()));
                                feeAmtNoAlreadyAllSum.set(feeAmtNoAlreadyAllSum.get().add(finalFeeNoAlreadyPay.get()));
                            }
                            map.put("fee_no_already", finalFeeNoAlreadyPay.get());
                            map.put("allPhase", allPhaseCount);
                            map.put("accountant_list", accountantList);
                            map.put("cashier_list", cashierList);
                            map.put("business_list", businessList);
                            map.put("select_list", selectList);
                            map.put("export_list", exportList);
                            //财务项目管理五期，拼接新增的字段
                            map.put("feeAlreadyPay", finalFeeAlreadyPay);
                            map.put("generateCertificateFlag", t.getGenerateCertificateFlag());
                            map.put("guaranteeIncomeType", t.getGuaranteeIncomeType());
                            map.put("payeeAbbreviation", t.getPayeeAbbreviation());
                            //给一个id的唯一标识
                            map.put("projectIddd:" + t.getId(), "1");
                            list.add(map);
                        }

                    }
                } else {
                    //跨月，看他的end和相差的月份
                    Map<String, Object> monthDiff = cwProjectIncomeMapper.selectCwProjectIncomeDiffMonthById(i.getId());
                    if (monthDiff != null) {
                        List<Map<String, Object>> monthList =  cwProjectIncomeMapper.selectCwProjectIncomeDiffMonthListByTermEndAndMonthDiff(monthDiff.get("termEnd").toString(), Integer.parseInt(monthDiff.get("monthDiff").toString()));
                        //然后去对比传进来的参数是否是在这个集合里的
                        //进行判断，时间在monthList内可以查，startDate比monthList里最小的时间还小也可以查，endDate比monthList里最大的时间还大也可以查
                        String startDate1 = startDate + "-01";
                        String endDate1 = endDate + "-31";
                        CwProjectIncome cwi = cwProjectIncomeMapper.selectCwProjectIncomeByIdAndTime(i.getId(), startDate, endDate1, sumFlag);
                        if (cwi == null) {
                            //说明在大范围不正确，看看是否在小范围当中
                            boolean b = monthList.stream().anyMatch(m -> startDate.equals(m.get("month")) || endDate.equals(m.get("month")));
                            if (b) {
                                //就把这条期次给算上去
                                cwi = cwProjectIncomeMapper.selectCwProjectIncomeById(i.getId());
                                //按比例分配期次的数据
                                //需要平均分成几份，也就是多少天
                                int days = Integer.parseInt(String.valueOf((cwi.getTermEnd().getTime() - cwi.getTermBegin().getTime()) / (1000 * 60 * 60 * 24))) + 1;
                                //获取begin的最后一天
                                int beginLastDay = LocalDate.of(Integer.parseInt(df2.format(cwi.getTermBegin()).substring(0, 4)), Integer.parseInt(df2.format(cwi.getTermBegin()).substring(5, 7)), 1).with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
                                //获得endDate的最后一天
                                int endDateLastDay = LocalDate.of(Integer.parseInt(endDate.substring(0, 4)), Integer.parseInt(endDate.substring(5, 7)), 1).with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
                                String endDate2 = endDate + "-" + endDateLastDay;
                                //获取beginDate的最后一天
                                int beginDateLastDay = LocalDate.of(Integer.parseInt(startDate.substring(0, 4)), Integer.parseInt(startDate.substring(5, 7)), 1).with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
                                //获取end的第一天
                                int endFirstDay = LocalDate.of(Integer.parseInt(df2.format(cwi.getTermEnd()).substring(0, 4)), Integer.parseInt(df2.format(cwi.getTermEnd()).substring(5, 7)), 1).getDayOfMonth();
                                //输入的两个月份占了多少天数呢？也就是多少比例
                                long between = ChronoUnit.DAYS.between(LocalDate.parse(startDate1), LocalDate.parse(endDate2)) + 1;
                                //最后一天 - 格式化之后的天 + 1
                                int formatDat = Integer.parseInt(df2.format(cwi.getTermBegin()).substring(8, 10));
                                int beginDay = beginLastDay - formatDat + 1;
                                int endDay = Integer.parseInt(df2.format(cwi.getTermEnd()).substring(8, 10));
                                if (Optional.ofNullable(cwi.getIncomeAmt()).isPresent()) {
                                    incomeAmtSum.set(incomeAmtSum.get().add(cwi.getIncomeAmt()));
                                    BigDecimal divide = cwi.getIncomeAmt().divide(new BigDecimal(String.valueOf(days)), 12, BigDecimal.ROUND_HALF_UP);
                                    //分以下几种情况
                                    //输入开始月份和结束月份整个包含整个期次，直接全加
                                    if ((DateUtils.parseDate(startDate1).getTime() < cwi.getTermBegin().getTime())
                                            && (cwi.getTermEnd().getTime() < DateUtils.parseDate(endDate2).getTime())) {
                                        incomeAmtAllSum.set(incomeAmtAllSum.get().add(cwi.getIncomeAmt()));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //整个期次包含输入开始月份和结束月份，加两个月份天数差值
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(between))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        incomeAmtAllSum.set(incomeAmtAllSum.get().add(multiply));
                                    } else if ((DateUtils.parseDate(startDate1).getTime() <= cwi.getTermBegin().getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //输入的开始包含begin，输入的结束小于。前面天数+输入后面整个月份
                                        long l = DateUtils.parseDate(endDate2).getTime() - cwi.getTermBegin().getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDay + endDateLastDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        incomeAmtAllSum.set(incomeAmtAllSum.get().add(multiply));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (cwi.getTermEnd().getTime() <= DateUtils.parseDate(endDate2).getTime())) {
                                        long l = cwi.getTermEnd().getTime() - DateUtils.parseDate(startDate1).getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        //期次begin包含输入的，输入的结束大于。前面输入整个月+后面天数
                                        // int i1 = beginDateLastDay + endDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        incomeAmtAllSum.set(incomeAmtAllSum.get().add(multiply));
                                    }
                                }
                                if (Optional.ofNullable(cwi.getGrossProfitAmt()).isPresent()) {
                                    grossProfitAmtSum.set(grossProfitAmtSum.get().add(cwi.getGrossProfitAmt()));
                                    BigDecimal divide = cwi.getGrossProfitAmt().divide(new BigDecimal(String.valueOf(days)), 12, BigDecimal.ROUND_HALF_UP);
                                    //分以下几种情况
                                    //输入开始月份和结束月份整个包含整个期次，直接全加
                                    if ((DateUtils.parseDate(startDate1).getTime() < cwi.getTermBegin().getTime())
                                            && (cwi.getTermEnd().getTime() < DateUtils.parseDate(endDate2).getTime())) {
                                        grossProfitAmtAllSum.set(grossProfitAmtAllSum.get().add(cwi.getGrossProfitAmt()));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //整个期次包含输入开始月份和结束月份，加两个月份天数差值
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(between))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        grossProfitAmtAllSum.set(grossProfitAmtAllSum.get().add(multiply));
                                    } else if ((DateUtils.parseDate(startDate1).getTime() <= cwi.getTermBegin().getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //输入的开始包含begin，输入的结束小于。前面天数+输入后面整个月份
                                        long l = DateUtils.parseDate(endDate2).getTime() - cwi.getTermBegin().getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDay + endDateLastDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        grossProfitAmtAllSum.set(grossProfitAmtAllSum.get().add(multiply));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (cwi.getTermEnd().getTime() <= DateUtils.parseDate(endDate2).getTime())) {
                                        //期次begin包含输入的，输入的结束大于。前面输入整个月+后面天数
                                        long l = cwi.getTermEnd().getTime() - DateUtils.parseDate(startDate1).getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDateLastDay + endDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        grossProfitAmtAllSum.set(grossProfitAmtAllSum.get().add(multiply));
                                    }
                                }
                                if (Optional.ofNullable(cwi.getGrossProfitAmt2()).isPresent()) {
                                    grossProfitAmt2Sum.set(grossProfitAmt2Sum.get().add(cwi.getGrossProfitAmt2()));
                                    BigDecimal divide = cwi.getGrossProfitAmt2().divide(new BigDecimal(String.valueOf(days)), 12, BigDecimal.ROUND_HALF_UP);
                                    //分以下几种情况
                                    //输入开始月份和结束月份整个包含整个期次，直接全加
                                    if ((DateUtils.parseDate(startDate1).getTime() < cwi.getTermBegin().getTime())
                                            && (cwi.getTermEnd().getTime() < DateUtils.parseDate(endDate2).getTime())) {
                                        grossProfitAmt2AllSum.set(grossProfitAmt2AllSum.get().add(cwi.getGrossProfitAmt2()));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //整个期次包含输入开始月份和结束月份，加两个月份天数差值
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(between))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        grossProfitAmt2AllSum.set(grossProfitAmt2AllSum.get().add(multiply));
                                    } else if ((DateUtils.parseDate(startDate1).getTime() <= cwi.getTermBegin().getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //输入的开始包含begin，输入的结束小于。前面天数+输入后面整个月份
                                        long l = DateUtils.parseDate(endDate2).getTime() - cwi.getTermBegin().getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDay + endDateLastDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        grossProfitAmt2AllSum.set(grossProfitAmt2AllSum.get().add(multiply));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (cwi.getTermEnd().getTime() <= DateUtils.parseDate(endDate2).getTime())) {
                                        //期次begin包含输入的，输入的结束大于。前面输入整个月+后面天数
                                        long l = cwi.getTermEnd().getTime() - DateUtils.parseDate(startDate1).getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDateLastDay + endDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        grossProfitAmt2AllSum.set(grossProfitAmt2AllSum.get().add(multiply));
                                    }
                                }
                                CwProjectFee cwf = null;
                                if ("0".equals(t.getProjectType()) || "2".equals(t.getProjectType()) || "3".equals(t.getProjectType())) {
                                    cwf = cwProjectFeeMapper.selectCwProjectCustFeeListDetailByIncomeId4(i.getId());
                                } else if ("1".equals(t.getProjectType())) {
                                    //法催项目查返费表，用phaseId查
                                    List<BigDecimal> feeAmtList = cwProjectFeeMapper.selectLawAllFeeByPhaseId(i.getId());
                                    List<BigDecimal> feeAmt2List = cwProjectFeeMapper.selectLawAllFee2ByPhaseId(i.getId());
                                    BigDecimal feeAmt = feeAmtList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                                    BigDecimal feeAmt2 = feeAmt2List.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                                    if (feeAmt != null) {
                                        cwf = new CwProjectFee();
                                        cwf.setFeeAmt(feeAmt);
                                        cwf.setFeeAmt2(feeAmt2);
                                    }
                                }
//                                //查返费表，用incomeId查
//                                CwProjectFee cwf = cwProjectFeeMapper.selectCwProjectCustFeeListDetailByIncomeId4(i.getId());
                                if (cwf != null) {
                                    feeAmtSum.set(feeAmtSum.get().add(cwf.getFeeAmt()));
                                    feeAmt2Sum.set(feeAmt2Sum.get().add(cwf.getFeeAmt2()));
                                    BigDecimal divide = cwf.getFeeAmt().divide(new BigDecimal(String.valueOf(days)), 12, BigDecimal.ROUND_HALF_UP);
                                    BigDecimal divide1 = cwf.getFeeAmt2().divide(new BigDecimal(String.valueOf(days)), 12, BigDecimal.ROUND_HALF_UP);
                                    //分以下几种情况
                                    //输入开始月份和结束月份整个包含整个期次，直接全加
                                    if ((DateUtils.parseDate(startDate1).getTime() < cwi.getTermBegin().getTime())
                                            && (cwi.getTermEnd().getTime() < DateUtils.parseDate(endDate2).getTime())) {
                                        feeAmtAllSum.set(feeAmtAllSum.get().add(cwf.getFeeAmt()));
                                        feeAmt2AllSum.set(feeAmt2AllSum.get().add(cwf.getFeeAmt2()));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //整个期次包含输入开始月份和结束月份，加两个月份天数差值
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(between))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        BigDecimal multiply1 = divide1.multiply(new BigDecimal(String.valueOf(between))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        feeAmtAllSum.set(feeAmtAllSum.get().add(multiply));
                                        feeAmt2AllSum.set(feeAmt2AllSum.get().add(multiply1));
                                    } else if ((DateUtils.parseDate(startDate1).getTime() <= cwi.getTermBegin().getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //输入的开始包含begin，输入的结束小于。前面天数+输入后面整个月份
                                        long l = DateUtils.parseDate(endDate2).getTime() - cwi.getTermBegin().getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDay + endDateLastDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        BigDecimal multiply1 = divide1.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        feeAmtAllSum.set(feeAmtAllSum.get().add(multiply));
                                        feeAmt2AllSum.set(feeAmt2AllSum.get().add(multiply1));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (cwi.getTermEnd().getTime() <= DateUtils.parseDate(endDate2).getTime())) {
                                        //期次begin包含输入的，输入的结束大于。前面输入整个月+后面天数
                                        long l = cwi.getTermEnd().getTime() - DateUtils.parseDate(startDate1).getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDateLastDay + endDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        BigDecimal multiply1 = divide1.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        feeAmtAllSum.set(feeAmtAllSum.get().add(multiply));
                                        feeAmt2AllSum.set(feeAmt2AllSum.get().add(multiply1));
                                    }
                                }

                                //之前在前面，前面的话缺少feeAmt字段，导致出现问题，放到cwf.setFeeAmt(feeAmt);后面，用于计算未结清
                                if (Optional.ofNullable(finalFeeAlreadyPay).isPresent()) {
                                    // feeAmtAlreadySum.set(feeAmtAlreadySum.get().add(cwi.getFeeAmt()));
                                    BigDecimal divide = finalFeeAlreadyPay.divide(new BigDecimal(String.valueOf(days)), 12, BigDecimal.ROUND_HALF_UP);
                                    //分以下几种情况
                                    //输入开始月份和结束月份整个包含整个期次，直接全加
                                    if ((DateUtils.parseDate(startDate1).getTime() < cwi.getTermBegin().getTime())
                                            && (cwi.getTermEnd().getTime() < DateUtils.parseDate(endDate2).getTime())) {
                                        feeAmtAlreadyAllSum.set(feeAmtAlreadyAllSum.get().add(finalFeeAlreadyPay));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //整个期次包含输入开始月份和结束月份，加两个月份天数差值
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(between))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        feeAmtAlreadyAllSum.set(feeAmtAlreadyAllSum.get().add(multiply));
                                    } else if ((DateUtils.parseDate(startDate1).getTime() <= cwi.getTermBegin().getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //输入的开始包含begin，输入的结束小于。前面天数+输入后面整个月份
                                        long l = DateUtils.parseDate(endDate2).getTime() - cwi.getTermBegin().getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDay + endDateLastDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        feeAmtAlreadyAllSum.set(feeAmtAlreadyAllSum.get().add(multiply));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (cwi.getTermEnd().getTime() <= DateUtils.parseDate(endDate2).getTime())) {
                                        //期次begin包含输入的，输入的结束大于。前面输入整个月+后面天数
                                        long l = cwi.getTermEnd().getTime() - DateUtils.parseDate(startDate1).getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDateLastDay + endDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        feeAmtAlreadyAllSum.set(feeAmtAlreadyAllSum.get().add(multiply));
                                    }
                                }
                                BigDecimal feeNoAlreadyPay = feeAmtSum.get().subtract(finalFeeAlreadyPay);
                                finalFeeNoAlreadyPay.set(feeNoAlreadyPay);
                                if (Optional.ofNullable(finalFeeNoAlreadyPay.get()).isPresent()) {
//                                    feeAmtNoAlreadyAllSum.set(feeAmtNoAlreadyAllSum.get().add(finalFeeNoAlreadyPay.get()));
                                    BigDecimal divide = finalFeeNoAlreadyPay.get().divide(new BigDecimal(String.valueOf(days)), 12, BigDecimal.ROUND_HALF_UP);
                                    //分以下几种情况
                                    //输入开始月份和结束月份整个包含整个期次，直接全加
                                    if ((DateUtils.parseDate(startDate1).getTime() < cwi.getTermBegin().getTime())
                                            && (cwi.getTermEnd().getTime() < DateUtils.parseDate(endDate2).getTime())) {
                                        feeAmtNoAlreadyAllSum.set(feeAmtNoAlreadyAllSum.get().add(finalFeeNoAlreadyPay.get()));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //整个期次包含输入开始月份和结束月份，加两个月份天数差值
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(between))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        feeAmtNoAlreadyAllSum.set(feeAmtNoAlreadyAllSum.get().add(multiply));
                                    } else if ((DateUtils.parseDate(startDate1).getTime() <= cwi.getTermBegin().getTime())
                                            && (DateUtils.parseDate(endDate2).getTime() < cwi.getTermEnd().getTime())) {
                                        //输入的开始包含begin，输入的结束小于。前面天数+输入后面整个月份
                                        long l = DateUtils.parseDate(endDate2).getTime() - cwi.getTermBegin().getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDay + endDateLastDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        feeAmtNoAlreadyAllSum.set(feeAmtNoAlreadyAllSum.get().add(multiply));
                                    } else if ((cwi.getTermBegin().getTime() < DateUtils.parseDate(startDate1).getTime())
                                            && (cwi.getTermEnd().getTime() <= DateUtils.parseDate(endDate2).getTime())) {
                                        //期次begin包含输入的，输入的结束大于。前面输入整个月+后面天数
                                        long l = cwi.getTermEnd().getTime() - DateUtils.parseDate(startDate1).getTime();
                                        int i2 = Integer.parseInt(String.valueOf(l / (1000 * 60 * 60 * 24))) + 1;
                                        // int i1 = beginDateLastDay + endDay;
                                        BigDecimal multiply = divide.multiply(new BigDecimal(String.valueOf(i2))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                        feeAmtNoAlreadyAllSum.set(feeAmtNoAlreadyAllSum.get().add(multiply));
                                    }
                                }

                                //处理期次未完成条数
                                int c = 0;
                                if ("0".equals(t.getProjectType()) || "2".equals(t.getProjectType()) || "3".equals(t.getProjectType())) {
                                    //普通项目
                                    c = cwProjectIncomeMapper.selectCwProjectIncomeAllPhaseCountById(i.getId());
                                } else if ("1".equals(t.getProjectType())) {
                                    //法催项目
                                    c = cwProjectIncomeMapper.selectCwProjectLawIncomeAllPhaseCountById(i.getId());
                                }
                                allPhaseCount.set(allPhaseCount.get() + c);
                                if (!map.containsKey("projectIddd:"+t.getId())) {
                                    //更改逻辑，只要能查出来的都是可控制的
                                    map.put("controlFlag", "0");
                                    //项目id
                                    map.put("id", t.getId());
                                    //项目类型
                                    map.put("project_type", t.getProjectType());
                                    String dataNameWithParents = getDataNameWithParents(t.getProjectTypeId(), projectTypeMap);
                                    if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                                        dataNameWithParents = dataNameWithParents.substring(1);
                                    }
                                    map.put("projectType", dataNameWithParents);
                                    map.put("projectPortfolioCode",t.getProjectPortfolioCode());
                                    //map.put("projectType", t.getProjectTypeChinese());
                                    //项目名称
                                    map.put("project_name", t.getProjectName());
                                    //担保公司
                                    map.put("cust_name", t.getCustName());
                                    //汇款公司
                                    map.put("income_cust_name", t.getIncomeCustName());
                                    //该项目的所有期次都弄完了以后，开始放入该项目的数据
                                    map.put("income_amt", incomeAmtSum);
                                    map.put("fee_amt", feeAmtSum);
                                    map.put("fee_amt2", feeAmt2Sum);
                                    map.put("gross_profit_amt", grossProfitAmtSum);
                                    map.put("gross_profit_amt2", grossProfitAmt2Sum);
                                    map.put("fee_no_already", finalFeeNoAlreadyPay.get());
                                    map.put("allPhase", allPhaseCount);
                                    map.put("accountant_list", accountantList);
                                    map.put("cashier_list", cashierList);
                                    map.put("business_list", businessList);
                                    map.put("select_list", selectList);
                                    map.put("export_list", exportList);
                                    //财务项目管理五期，拼接新增的字段
                                    map.put("feeAlreadyPay", finalFeeAlreadyPay);
                                    map.put("generateCertificateFlag", t.getGenerateCertificateFlag());
                                    map.put("guaranteeIncomeType", t.getGuaranteeIncomeType());
                                    map.put("payeeAbbreviation", t.getPayeeAbbreviation());
                                    //给一个id的唯一标识
                                    map.put("projectIddd:" + t.getId(), "8");
                                    list.add(map);
                                }
                            }
                        } else {
                            //在大范围之中，直接加上
                            if (Optional.ofNullable(cwi.getIncomeAmt()).isPresent()) {
                                incomeAmtSum.set(incomeAmtSum.get().add(cwi.getIncomeAmt()));
                                incomeAmtAllSum.set(incomeAmtAllSum.get().add(cwi.getIncomeAmt()));
                            }
                            if (Optional.ofNullable(cwi.getGrossProfitAmt()).isPresent()) {
                                grossProfitAmtSum.set(grossProfitAmtSum.get().add(cwi.getGrossProfitAmt()));
                                grossProfitAmtAllSum.set(grossProfitAmtAllSum.get().add(cwi.getGrossProfitAmt()));
                            }
                            if (Optional.ofNullable(cwi.getGrossProfitAmt2()).isPresent()) {
                                grossProfitAmt2Sum.set(grossProfitAmt2Sum.get().add(cwi.getGrossProfitAmt2()));
                                grossProfitAmt2AllSum.set(grossProfitAmt2AllSum.get().add(cwi.getGrossProfitAmt2()));
                            }
                            //普通项目查返费表，用incomeId查
                            CwProjectFee cwf = null;
                            if ("0".equals(t.getProjectType()) || "2".equals(t.getProjectType()) || "3".equals(t.getProjectType())) {
                                cwf = cwProjectFeeMapper.selectCwProjectCustFeeListDetailByIncomeId4(i.getId());
                            } else if ("1".equals(t.getProjectType())) {
                                //法催项目查返费表，用phaseId查
                                List<BigDecimal> feeAmtList = cwProjectFeeMapper.selectLawAllFeeByPhaseId(i.getId());
                                List<BigDecimal> feeAmt2List = cwProjectFeeMapper.selectLawAllFee2ByPhaseId(i.getId());
                                BigDecimal feeAmt = feeAmtList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal feeAmt2 = feeAmt2List.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                                if (feeAmt != null){
                                    cwf = new CwProjectFee();
                                    cwf.setFeeAmt(feeAmt);
                                    cwf.setFeeAmt2(feeAmt2);
                                }
                            }
                            if (cwf != null) {
                                feeAmtSum.set(feeAmtSum.get().add(cwf.getFeeAmt()));
                                feeAmt2Sum.set(feeAmt2Sum.get().add(cwf.getFeeAmt2()));
                                feeAmtAllSum.set(feeAmtAllSum.get().add(cwf.getFeeAmt()));
                                feeAmt2AllSum.set(feeAmt2AllSum.get().add(cwf.getFeeAmt2()));
                            }
                            //处理期次未完成条数
                            int c = 0;
                            if ("0".equals(t.getProjectType()) || "2".equals(t.getProjectType()) || "3".equals(t.getProjectType())) {
                                //普通项目
                                c = cwProjectIncomeMapper.selectCwProjectIncomeAllPhaseCountById(i.getId());
                            } else if ("1".equals(t.getProjectType())) {
                                //法催项目
                                c = cwProjectIncomeMapper.selectCwProjectLawIncomeAllPhaseCountById(i.getId());
                            }
                            allPhaseCount.set(allPhaseCount.get() + c);
                            if (!map.containsKey("projectIddd:"+t.getId())) {
                                //更改逻辑，只要能查出来的都是可控制的
                                map.put("controlFlag", "0");
                                //项目id
                                map.put("id", t.getId());
                                //项目类型
                                map.put("project_type", t.getProjectType());
                                String dataNameWithParents = getDataNameWithParents(t.getProjectTypeId(), projectTypeMap);
                                if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                                    dataNameWithParents = dataNameWithParents.substring(1);
                                }
                                map.put("projectType", dataNameWithParents);
                                map.put("projectPortfolioCode",t.getProjectPortfolioCode());
//                                map.put("projectType", t.getProjectTypeChinese());
                                //项目名称
                                map.put("project_name", t.getProjectName());
                                //担保公司
                                map.put("cust_name", t.getCustName());
                                //汇款公司
                                map.put("income_cust_name", t.getIncomeCustName());
                                //该项目的所有期次都弄完了以后，开始放入该项目的数据
                                map.put("income_amt", incomeAmtSum);
                                map.put("fee_amt", feeAmtSum);
                                map.put("fee_amt2", feeAmt2Sum);
                                map.put("gross_profit_amt", grossProfitAmtSum);
                                map.put("gross_profit_amt2", grossProfitAmt2Sum);
                                BigDecimal feeNoAlreadyPay = feeAmtSum.get().subtract(finalFeeAlreadyPay);
                                finalFeeNoAlreadyPay.set(feeNoAlreadyPay);
                                if (Optional.ofNullable(finalFeeAlreadyPay).isPresent()) {
                                    feeAmtAlreadySum.set(feeAmtAlreadySum.get().add(finalFeeAlreadyPay));
                                    feeAmtAlreadyAllSum.set(feeAmtAlreadyAllSum.get().add(finalFeeAlreadyPay));
                                }
                                if (Optional.ofNullable(finalFeeNoAlreadyPay.get()).isPresent()) {
                                    feeAmtNoAlreadySum.set(feeAmtNoAlreadySum.get().add(finalFeeNoAlreadyPay.get()));
                                    feeAmtNoAlreadyAllSum.set(feeAmtNoAlreadyAllSum.get().add(finalFeeNoAlreadyPay.get()));
                                }
                                map.put("fee_no_already", finalFeeNoAlreadyPay.get());
                                map.put("allPhase", allPhaseCount);
                                map.put("accountant_list", accountantList);
                                map.put("cashier_list", cashierList);
                                map.put("business_list", businessList);
                                map.put("select_list", selectList);
                                map.put("export_list", exportList);
                                //财务项目管理五期，拼接新增的字段
                                map.put("feeAlreadyPay", finalFeeAlreadyPay);
                                map.put("generateCertificateFlag", t.getGenerateCertificateFlag());
                                map.put("guaranteeIncomeType", t.getGuaranteeIncomeType());
                                map.put("payeeAbbreviation", t.getPayeeAbbreviation());
                                //给一个id的唯一标识
                                map.put("projectIddd:" + t.getId(), "2");
                                list.add(map);
                            }
                        }
                    }
                }
            });


        });
        //需要总计的数
        Map<String, Object> map = new HashMap<>();
        map.put("incomeSum", incomeAmtAllSum);
        map.put("grossProfitAmtSum", grossProfitAmtAllSum);
        map.put("grossProfitAmt2Sum", grossProfitAmt2AllSum);
        map.put("feeAmtSum", feeAmtAllSum);
        map.put("feeAmt2Sum", feeAmt2AllSum);
        map.put("feeAlreadySum", feeAmtAlreadyAllSum);
        map.put("feeNoAlreadySum", feeAmtNoAlreadyAllSum);
        resp.put("sumResp", map);
        resp.put("resp", list);
        return resp;
    }

    /**
     * 查询财务项目管理-完结项目归档查询
     *
     * @param cwProject 财务项目管理-主表对象
     * @return 财务项目管理-完结项目归档查询
     */
    @Override
    public Map<String, Object> selectCwProjectOverList(CwProject cwProject, String startDate, String endDate, Integer sumFlag, Long userId, LoginUser loginUser, List<Long> projectIds) {
        Map<String, Object> resp = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        AtomicReference<BigDecimal> incomeSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> grossProfitAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> grossProfitAmt2Sum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> feeAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> feeAmt2Sum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> feeAlreadySum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> feeNoAlreadySum = new AtomicReference<>(new BigDecimal("0.00"));
        //模糊查询-项目名称、担保公司、汇款公司
        List<String> role = cwProjectUserMapper.selectUserRoleIsAdmin(userId);
        boolean present = loginUser.getUser().getRoles().stream().anyMatch(r -> "admin".equals(r.getRoleKey()) || "caiwuAdmin".equals(r.getRoleKey()) || "renshi".equals(r.getRoleKey()) || "yewuAdmin".equals(r.getRoleKey()));
        //先找到主表的id
        Collator orderByChinese = Collator.getInstance(Locale.CHINESE);
//        List<Long> pIds = cwProjectShowService.getProjectId(loginUser,"1");
        List<CwProject> cwProjects;
        //下面这个if中的两个判断条件不能对调位置，否则报错
        if (projectIds == null || 0 != projectIds.size()) {
            cwProjects = cwProjectMapper.selectCwProjectByIds(projectIds, "1", cwProject);
        } else  {
            cwProjects = new ArrayList<>();
        }
        Collections.sort(cwProjects , (e1, e2) -> {
            //两个参数是第一个是这个list，后面是进入的第一个和第二个，然后他的projectName进行比较
            return orderByChinese.compare(e1.getProjectName(), e2.getProjectName());
        });
        //查询项目类型数据集  组合一级二级名称
        Map<Long, OaDataManage> projectTypeMap = oaDataManageMapper.selectDataManageListByCode("project_type").stream()
                .filter(vo -> vo.getParentId() != null) //过滤最上一级节点
                .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
        cwProjects.forEach(
                t -> {
                    //2023.12.22 财务项目管理五期，对OA产生的打款数据进行统计，作为本项目的已经支付的返费
                    BigDecimal feeAlreadyPay = oaPayRebateRecordMapper.selectOaPayRebateRecordByProjectId(t.getOaProjectDeployId());
                    if (feeAlreadyPay == null) {
                        feeAlreadyPay = BigDecimal.ZERO;
                    }
                    BigDecimal finalFeeAlreadyPay = feeAlreadyPay;
                    CwProjectIncome cWincome = null;
                    int allPhaseCount = 0;
                    if ("0".equals(t.getProjectType()) || "2".equals(t.getProjectType()) || "3".equals(t.getProjectType())) {
                        //通过主表id去查询收入表 todo 财务项目管理四期，普通项目（通道、分润）的期次和所有期次
                        cWincome = cwProjectIncomeMapper.selectCwProjectIncomeListFlagTwo(t.getId(), startDate, endDate, sumFlag);
                        //找到所有的期次
                        allPhaseCount = cwProjectIncomeMapper.selectAllPhaseByProjectId(t.getId());
                    } else {
                        //todo 财务项目管理四期，法催项目的期次和所有的期次
                        cWincome = cwProjectIncomeMapper.selectCwProjectLawIncomeListFlagTwo(t.getId(), startDate, endDate, sumFlag);
                        //todo 财务项目管理四期，找到所有的期次
                        allPhaseCount = cwProjectIncomeMapper.selectAllLawPhaseByProjectId(t.getId());
                    }
                    BigDecimal incomeAmt = new BigDecimal("0.00");
                    BigDecimal grossProfitAmt = new BigDecimal("0.00");
                    BigDecimal grossProfitAmt2 = new BigDecimal("0.00");
                    BigDecimal feeAmtAlready = new BigDecimal("0.00");
                    BigDecimal unfeeAmt = new BigDecimal("0.00");
                    if (Optional.ofNullable(cWincome).isPresent()){
                        if (Optional.ofNullable(cWincome.getIncomeAmt()).isPresent()){
                            //收入
                            incomeAmt = cWincome.getIncomeAmt();
                        }
                        if (Optional.ofNullable(cWincome.getGrossProfitAmt()).isPresent()) {
                            //毛利
                            grossProfitAmt = cWincome.getGrossProfitAmt();
                        }
                        if (Optional.ofNullable(cWincome.getGrossProfitAmt2()).isPresent()) {
                            //提成毛利
                            grossProfitAmt2 = cWincome.getGrossProfitAmt2();
                        }
                        if (Optional.ofNullable(finalFeeAlreadyPay).isPresent()) {
                            //返费已结清
                            feeAmtAlready = finalFeeAlreadyPay;
                        }
                    }
                    //去找返费、提成返费
                    CwProjectFee cWfee = cwProjectFeeMapper.selectCwProjectFeeListFlagTwo(t.getId(), startDate, endDate, sumFlag);
                    BigDecimal feeAmt = new BigDecimal("0.00");
                    BigDecimal feeAmt2 = new BigDecimal("0.00");
                    Map<String, Object> map = new HashMap<>();
                    if (cWfee != null) {
                        feeAmt = cWfee.getFeeAmt();
                        feeAmt2 = cWfee.getFeeAmt2();
                        //财务项目管理五期，计算返费未结清
                        unfeeAmt = feeAmt.subtract(feeAmtAlready);
                    }
                    //找对应的项目的用户情况
                    List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(t.getOaProjectDeployId(), AuthModuleEnum.FINANCEPROJ.getCode());
                    //会计
                    List<String> accountantList = userList.stream().filter(a -> "1".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                    //出纳
                    List<String> cashierList = userList.stream().filter(a -> "2".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                    //业务
                    List<String> businessList = userList.stream().filter(a -> "3".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
                    //查看权限
                    List<String> selectList = userList.stream().filter(a -> "88".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());

                    //找user表
//                    List<Map<String, Object>> users = cwProjectUserMapper.selectCwProjectUserByProjectId(t.getId());
                    //更改逻辑，只要能查出来的都是可控制的
                    map.put("controlFlag", "0");
                    //把找的数据存放到map当中
                    map.put("id", t.getId());
                    //项目类型
                    map.put("project_type", t.getProjectType());

                    String dataNameWithParents = getDataNameWithParents(t.getProjectTypeId(), projectTypeMap);
                    if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                        dataNameWithParents = dataNameWithParents.substring(1);
                    }
                    map.put("projectType", dataNameWithParents);
//                    map.put("projectType", t.getProjectTypeChinese());
                    map.put("project_name", t.getProjectName());
                    map.put("cust_name", t.getCustName());
                    map.put("income_cust_name", t.getIncomeCustName());
                    map.put("income_amt", incomeAmt);
                    map.put("fee_amt", feeAmt);
                    map.put("fee_amt2", feeAmt2);
                    map.put("gross_profit_amt", grossProfitAmt);
                    map.put("gross_profit_amt2", grossProfitAmt2);
                    map.put("fee_no_already", unfeeAmt);
                    map.put("allPhase", allPhaseCount);
//                    List<String> accountantList = new ArrayList<>();
//                    List<String> cashierList = new ArrayList<>();
//                    List<String> businessList = new ArrayList<>();
//                    List<String> selectList = new ArrayList<>();
                    List<String> exportList = new ArrayList<>();
//                    users.forEach(u -> {
//                        if (CwxmglConstants.USER_FLAG_0.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                            String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                            accountantList.add(o);
//                        } else if (CwxmglConstants.USER_FLAG_1.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                            String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                            cashierList.add(o);
//                        } else if (CwxmglConstants.USER_FLAG_2.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                            String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                            businessList.add(o);
//                        } else if (CwxmglConstants.USER_FLAG_3.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                            String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                            selectList.add(o);
//                        } else if (CwxmglConstants.USER_FLAG_4.equals(u.get(CwxmglConstants.USER_FLAG))) {
//                            String o = (String) u.get(CwxmglConstants.NICK_NAME);
//                            exportList.add(o);
//                        }
//                    });
                    if (sumFlag != null) {
                        //计算总金额
                        incomeSum.set(incomeSum.get().add(incomeAmt));
                        grossProfitAmtSum.set(grossProfitAmtSum.get().add(grossProfitAmt));
                        grossProfitAmt2Sum.set(grossProfitAmt2Sum.get().add(grossProfitAmt2));
                        feeAmtSum.set(feeAmtSum.get().add(feeAmt));
                        feeAmt2Sum.set(feeAmt2Sum.get().add(feeAmt2));
                        feeAlreadySum.set(feeAlreadySum.get().add(feeAmtAlready));
                        feeNoAlreadySum.set(feeNoAlreadySum.get().add(unfeeAmt));
                    }
                    map.put("accountant_list", accountantList);
                    map.put("cashier_list", cashierList);
                    map.put("business_list", businessList);
                    map.put("select_list", selectList);
                    map.put("export_list", exportList);
                    //财务项目管理五期，拼接新增的字段
                    map.put("feeAlreadyPay", finalFeeAlreadyPay);
                    map.put("generateCertificateFlag", t.getGenerateCertificateFlag());
                    map.put("guaranteeIncomeType", t.getGuaranteeIncomeType());
                    map.put("payeeAbbreviation", t.getPayeeAbbreviation());
                    list.add(map);
                }
        );
        //首先通过判断sumFlag来确定是否需要算总数，也就是接的页面不同
        if (sumFlag != null){
            //需要总计的数
            Map<String, Object> map = new HashMap<>();
            map.put("incomeSum", incomeSum);
            map.put("grossProfitAmtSum", grossProfitAmtSum);
            map.put("grossProfitAmt2Sum", grossProfitAmt2Sum);
            map.put("feeAmtSum", feeAmtSum);
            map.put("feeAmt2Sum", feeAmt2Sum);
            map.put("feeAlreadySum", feeAlreadySum);
            map.put("feeNoAlreadySum", feeNoAlreadySum);
            resp.put("sumResp", map);
            // list.add(map);
        }
        resp.put("resp", list);
        return resp;
    }

    /**
     * 查询财务项目管理-完结项目归档查询-详细信息
     *
     * @param cwProject 财务项目管理-主表
     * @return 财务项目管理-完结项目归档查询
     */
    @Override
    public Map<String, Object> selectCwProjectOverListDetileByProjectId(CwProject cwProject, Integer sumFlag) {
        CwProject cwProject1 = cwProjectMapper.selectCwProjectById(cwProject.getId());
        Long oaProjectDeployId = cwProject1.getOaProjectDeployId();
        Map<String, Object> map = new HashMap<>();
        //通过项目的维度来查各种信息，使用主键id来查到相关的返费公司，调用上面的方法查其他
        List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectCustListByProjectId(cwProject.getId());
        long count = cwProjectCusts.stream().filter(t -> "1".equals(t.getReplaceFlag())).count();
        List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfoList = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByProjectId(cwProject.getId());
        List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = new ArrayList<>();
        if (count > 0L) {
            //说明有发生过替换,根据项目id查找cw_project_replace_fee_company_info表,得到所有替换的数据
            cwProjectReplaceFeeCompanyInfos = cwProjectReplaceFeeCompanyInfoList;
        }
        List<CwProjectReplaceFeeCompanyInfo> finalCwProjectReplaceFeeCompanyInfos = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> "0".equals(t.getStatus())).collect(Collectors.toList());
        //2024.10.24
        List<CwProjectReplaceFeeCompanyInfo> finalCwProjectReplaceFeeCompanyInfos1 = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> "1".equals(t.getStatus())).collect(Collectors.toList());
        //todo 财务项目管理四期，按照项目id来找返费公司与费率的方案使用情况
        List<CwProjectCust> cwProjectCustSchemeFlagUseSituation = cwProjectCustMapper.selectCwprojectCustSchemeFlagUseSituationByProjectId(cwProject.getId());
        //todo 找本项目下的暂不确定公司的项目
        List<CwProjectCust> cwProjectCustSchemeFlagUseSituationForNotSureCompany = cwProjectCustMapper.cwProjectCustSchemeFlagUseSituationForNotSureCompanyByProjectId(cwProject.getId());
        cwProjectCustSchemeFlagUseSituation.addAll(cwProjectCustSchemeFlagUseSituationForNotSureCompany);
        //按照方案的类别进行分组
        Map<String, List<CwProjectCust>> collect = cwProjectCustSchemeFlagUseSituation.stream().collect(Collectors.groupingBy(CwProjectCust::getSchemeFlag));
        List<CwProjectOverDetailCompanyDto> cwProjectCompany = new ArrayList<>();
        //遍历所有的返费公司与费率表
        for (CwProjectCust c:cwProjectCusts) {
            //然后去找对应的id
            List<CwProjectCust> v = collect.get(c.getSchemeFlag());
            int schemeFlagUseSituation = 0;
            int size = 0;
            Long id = c.getId();
            if (v != null) {
                schemeFlagUseSituation = (int) v.stream().map(CwProjectCust::getProjectIncomeId).distinct().count();
                //找到具体某一个返费公司与费率的具体使用情况
                size = (int) v.stream().filter(t -> id.equals(t.getId())).count();
            }
            //给页面返回的数据
            CwProjectOverDetailCompanyDto cwProjectOverDetailCompanyDto = new CwProjectOverDetailCompanyDto();
            if ("1".equals(c.getReplaceFlag())) {
                List<CwProjectReplaceFeeCompanyInfo> collect1 = finalCwProjectReplaceFeeCompanyInfos.stream().filter(a -> a.getOaTraderId().equals(c.getOaTraderId()) && a.getSchemeFlag().equals(c.getSchemeFlag())).collect(Collectors.toList());
                List<String> oldCompanyNameList = new ArrayList<>();
                //2024.10.24
                List<Map<String, Object>> mapReplaceList = new ArrayList<>();
                if (collect1.size() > 0) {
                    //判断之前的返费公司替换是否被撤回
                    if (!collect1.stream().anyMatch(i -> i.getOldOaTraderId().equals(c.getOaTraderId()))) {
                        //有被撤回的。那么就去找关系
                        //collect2找到的就是撤回掉的关系
                        List<CwProjectReplaceFeeCompanyInfo> collect2 = finalCwProjectReplaceFeeCompanyInfos1.stream().filter(a -> a.getOaTraderId().equals(c.getOaTraderId()) && a.getSchemeFlag().equals(c.getSchemeFlag())).sorted(Comparator.comparing(CwProjectReplaceFeeCompanyInfo::getId).reversed()).collect(Collectors.toList());
                        //然后找撤回掉的关系里最近的一个能对上的oaTraderId
                        CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo = collect2.stream().filter(a -> a.getOldOaTraderId().equals(c.getOaTraderId())).findFirst().orElse(null);
                        //先把没有过滤掉的给弄出来，后续去替换
                        for (CwProjectReplaceFeeCompanyInfo cprfci:collect1) {
                            String oldOaTraderUserName = cprfci.getOldOaTraderUserName();
                            Long oldOaTraderId = cprfci.getOldOaTraderId();
                            Map<String, Object> mapReplace = new HashMap<>();
                            mapReplace.put("oldOaTraderId", oldOaTraderId);
                            mapReplace.put("oldOaTraderUserName", oldOaTraderUserName);
                            mapReplaceList.add(mapReplace);
//                            oldCompanyNameList.add(oldOaTraderUserName);
                        }
                        if (cwProjectReplaceFeeCompanyInfo != null) {
                            Long id1 = cwProjectReplaceFeeCompanyInfo.getId();
                            List<CwProjectReplaceFeeCompanyInfo> collect3 = collect2.stream().filter(a -> a.getId() >= id1).collect(Collectors.toList());
                            //找到的collect3里包含着最近撤回的一套关系
                            for (CwProjectReplaceFeeCompanyInfo projectReplaceFeeCompanyInfo : collect3) {
                                Long newOaTraderId = projectReplaceFeeCompanyInfo.getNewOaTraderId();
                                Long oaTraderId = projectReplaceFeeCompanyInfo.getOaTraderId();
                                String oaTraderUserName = projectReplaceFeeCompanyInfo.getOaTraderUserName();
                                if (mapReplaceList.stream().anyMatch(a -> Long.valueOf(a.get("oldOaTraderId").toString()).equals(newOaTraderId))) {
                                    //把老的关系里的oaTraderId替换成新关系里的oldOatraderId
                                    Map<String, Object> map1 = mapReplaceList.stream().filter(a -> Long.valueOf(a.get("oldOaTraderId").toString()).equals(newOaTraderId)).findFirst().orElse(null);
                                    //获得存在的索引
                                    int i = mapReplaceList.indexOf(map1);
                                    //修改map1
                                    map1.put("oldOaTraderId", oaTraderId);
                                    map1.put("oldOaTraderUserName", oaTraderUserName);
                                    mapReplaceList.set(i, map1);
                                }
                            }
                        }
                        //替换完成之后，进行添加显示
                        oldCompanyNameList.addAll(mapReplaceList.stream().map(a -> a.get("oldOaTraderUserName").toString()).collect(Collectors.toList()));
                    } else {
                        //没有被撤回，那么就按照之前的逻辑可以正常显示没问题
                        for (CwProjectReplaceFeeCompanyInfo cprfci:collect1) {
                            String oldOaTraderUserName = cprfci.getOldOaTraderUserName();
                            oldCompanyNameList.add(oldOaTraderUserName);
                        }
                    }
                }
                cwProjectOverDetailCompanyDto.setOldCompanyName(oldCompanyNameList);
            } else {
                cwProjectOverDetailCompanyDto.setOldCompanyName(null);
            }
            cwProjectOverDetailCompanyDto.setId(id);
            if (null == c.getRate()) {
                cwProjectOverDetailCompanyDto.setRate("未设置");
            } else {
                String format1;
                if (Optional.ofNullable(sumFlag).isPresent()) {
                    //不为空，即不需要百分号
                    format1 = new BigDecimal(c.getRate().toString()).stripTrailingZeros().toPlainString();
                } else {
                    format1 = new BigDecimal(c.getRate().toString()).stripTrailingZeros().toPlainString() + "%";
                }
                cwProjectOverDetailCompanyDto.setRate(format1);
            }
            if (null == c.getTaxRate()) {
                cwProjectOverDetailCompanyDto.setTaxRate("未设置");
            } else {
                String format2;
                if (Optional.ofNullable(sumFlag).isPresent()){
                    //不为空，即不需要百分号
                    format2 = new BigDecimal(c.getTaxRate().toString()).stripTrailingZeros().toPlainString();
                } else {
                    format2 = new BigDecimal(c.getTaxRate().toString()).stripTrailingZeros().toPlainString() + "%";
                }
                cwProjectOverDetailCompanyDto.setTaxRate(format2);
            }
            cwProjectOverDetailCompanyDto.setCustName(c.getCustName());
            cwProjectOverDetailCompanyDto.setSchemeFlag("方案" + c.getSchemeFlag());
            cwProjectOverDetailCompanyDto.setSchemeFlagInt(Integer.parseInt(c.getSchemeFlag()));
            cwProjectOverDetailCompanyDto.setSchemeFlagUseSituation(schemeFlagUseSituation);
            cwProjectOverDetailCompanyDto.setPhaseSchemeFlagUseSituation(size);
            cwProjectOverDetailCompanyDto.setOaTraderId(c.getOaTraderId());
            cwProjectOverDetailCompanyDto.setReplaceFlag(c.getReplaceFlag());
            cwProjectCompany.add(cwProjectOverDetailCompanyDto);
        }

        //筛选出所有的方案并组成一个集合给前端
        List<String> schemeFlagList = cwProjectCompany.stream().collect(Collectors.groupingBy(CwProjectOverDetailCompanyDto::getSchemeFlag)).keySet().stream().collect(Collectors.toList());
        map.put("fee_list", cwProjectCompany);
        map.put("schemeFlagList", schemeFlagList);
        //找对应的项目的用户情况
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(oaProjectDeployId, AuthModuleEnum.FINANCEPROJ.getCode());
        //会计
        String accountant = userList.stream().filter(a -> "1".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
        //出纳
//        String cashierList = userList.stream().filter(a -> "2".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
        //业务
        String business = userList.stream().filter(a -> "3".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
        //查看权限
        String select = userList.stream().filter(a -> "88".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
        Map<String, Object> map1 = new HashMap<>();
        Map<String, Object> map3 = new HashMap<>();
        Map<String, Object> map4 = new HashMap<>();
//        Map<String, Object> map5 = new HashMap<>();
        map1.put("flag", "会计");
        map1.put("people_name", accountant);
        map3.put("flag", "业务");
        map3.put("people_name", business);
        map4.put("flag", "查看权限");
        map4.put("people_name", select);
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(map1);
        list.add(map3);
        list.add(map4);
        map.put("people_list", list);
        map.put("replaceCompanyInfo", cwProjectReplaceFeeCompanyInfoList);
        //根据主表来查各种信息
        //todo 财务项目管理四期，新增应付返费和实付返费
        AtomicReference<BigDecimal> shouldPayFeeAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> actuallyPayFeeAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> feeSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> incomeSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> grossProfitAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> grossProfitAmt2Sum = new AtomicReference<>(new BigDecimal("0.00"));
        AtomicReference<BigDecimal> fee2Sum = new AtomicReference<>(new BigDecimal("0.00"));
        List<Map<String, Object>> detailList = new ArrayList<>();
        DateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy.MM.dd");
        List<CwProjectIncome> cwProjectIncomes = cwProjectIncomeMapper.selectcwprojectincomeListByProjectId(cwProject.getId(), sumFlag);
        cwProjectIncomes.forEach(t -> {
            if (Optional.ofNullable(t).isPresent()){
                if (!Optional.ofNullable(t.getIncomeAmt()).isPresent()){
                    t.setIncomeAmt(new BigDecimal("0.00"));
                }
                if (!Optional.ofNullable(t.getGrossProfitAmt()).isPresent()){
                    t.setGrossProfitAmt(new BigDecimal("0.00"));
                }
                if (!Optional.ofNullable(t.getGrossProfitAmt2()).isPresent()){
                    t.setGrossProfitAmt2(new BigDecimal("0.00"));
                }
                if (!Optional.ofNullable(t.getFeeAmt()).isPresent()){
                    t.setFeeAmt(new BigDecimal("0.00"));
                }
                if (!Optional.ofNullable(t.getUnfeeAmt()).isPresent()){
                    t.setUnfeeAmt(new BigDecimal("0.00"));
                }
            }
                    if ("1".equals(t.getTerm())) {
                        Date termBegin = t.getTermBegin();
                        Date termEnd = t.getTermEnd();
                        String format1 = simpleDateFormat1.format(termBegin);
                        String format2 = simpleDateFormat1.format(termEnd);
                        t.setTermMonth(format1 + "-" + format2);
                    } else if ("0".equals(t.getTerm())){
                        String termMonth = t.getTermMonth();
                        termMonth = termMonth.replace("-", "年");
                        termMonth = termMonth + "月";
                        t.setTermMonth(termMonth);
                    } else {
                        t.setTermMonth("日期错误！");
                    }
                    BigDecimal bFeeAlreadySum = new BigDecimal("0.00");
                    BigDecimal bFeeNoAlreadySum = new BigDecimal("0.00");
                    AtomicReference<BigDecimal> bigDecimal1 = new AtomicReference<>(new BigDecimal("0.00"));
                    AtomicReference<BigDecimal> bigDecimal2 = new AtomicReference<>(new BigDecimal("0.00"));
                    AtomicReference<BigDecimal> bigDecimal3 = new AtomicReference<>(new BigDecimal("0.00"));
                    AtomicReference<BigDecimal> bigDecimal4 = new AtomicReference<>(new BigDecimal("0.00"));
                    List<CwProjectFee> cwProjectFees = cwProjectFeeMapper.selectCwProjectCustFeeListDetailByIncomeId2(t.getId());
                    cwProjectFees.forEach(
                            f -> {
                                if (Optional.ofNullable(f).isPresent()) {
                                    if (!Optional.ofNullable(f.getFeeAmt()).isPresent()){
                                        f.setFeeAmt(new BigDecimal("0.00"));
                                    }
                                    if (!Optional.ofNullable(f.getFeeAmt2()).isPresent()){
                                        f.setFeeAmt2(new BigDecimal("0.00"));
                                    }
                                    if (!Optional.ofNullable(f.getShouldPayFeeAmt()).isPresent()){
                                        f.setShouldPayFeeAmt(new BigDecimal("0.00"));
                                    }
                                    if (!Optional.ofNullable(f.getActuallyPayFeeAmt()).isPresent()){
                                        f.setActuallyPayFeeAmt(new BigDecimal("0.00"));
                                    }
                                } else {
                                    //解决空指针
                                    f = new CwProjectFee();
                                    f.setFeeAmt(new BigDecimal("0.00"));
                                    f.setFeeAmt2(new BigDecimal("0.00"));
                                    f.setShouldPayFeeAmt(new BigDecimal("0.00"));
                                    f.setActuallyPayFeeAmt(new BigDecimal("0.00"));
                                    f.setCustName("-");
                                    f.setFeeCustName("-");
                                }
                                Map<String, Object> fee = new HashMap<>();
                                String custName = f.getCustName();
                                String feeCustName = f.getFeeCustName();
                                BigDecimal feeAmt = f.getFeeAmt();
                                BigDecimal feeAmt2 = f.getFeeAmt2();
                                fee.put("custName", custName);
                                fee.put("feeCustName", feeCustName);
                                fee.put("feeAmt", feeAmt);
                                fee.put("feeAmt2", feeAmt2);
                                //累加查出来的返费和提成返费
                                //根据返费表的id去查对应的打款记录
                                List<Map<String, Object>> payOfFee = new ArrayList<>();
                                //返费已结清和返费未结清查income表，通过主键id来查到对应的income结果累加
                                CwProjectIncome cw = cwProjectIncomeMapper.selectCwProjectIncomeListById(t.getId());
                                if (Optional.ofNullable(cw).isPresent()) {
                                    //不为空
                                    if (!Optional.ofNullable(cw.getFeeAmt()).isPresent()) {
                                        cw.setFeeAmt(new BigDecimal("0.00"));
                                    }
                                    if (!Optional.ofNullable(cw.getUnfeeAmt()).isPresent()) {
                                        cw.setUnfeeAmt(new BigDecimal("0.00"));
                                    }
                                } else {
                                    //为空
                                    cw = new CwProjectIncome();
                                    cw.setFeeAmt(new BigDecimal("0.00"));
                                    cw.setUnfeeAmt(new BigDecimal("0.00"));
                                }
                                //找到了返费已结清和返费未结清，加入到map里
                                bigDecimal1.set(new BigDecimal(f.getFeeAmt().toString()));
                                bigDecimal2.set(new BigDecimal(f.getFeeAmt2().toString()));
                                bigDecimal3.set(new BigDecimal(f.getShouldPayFeeAmt().toString()));
                                bigDecimal4.set(new BigDecimal(f.getActuallyPayFeeAmt().toString()));
                                BigDecimal bFeeSum = new BigDecimal("0.00");
                                BigDecimal bFee2Sum = new BigDecimal("0.00");
                                BigDecimal bFeeShoudSum = new BigDecimal("0.00");
                                BigDecimal bFeeActuallySum = new BigDecimal("0.00");
                                bFeeSum = bFeeSum.add(feeSum.get());
                                feeSum.set(bFeeSum.add(bigDecimal1.get()));
                                bFee2Sum = bFee2Sum.add(fee2Sum.get());
                                fee2Sum.set(bFee2Sum.add(bigDecimal2.get()));
                                bFeeShoudSum = bFeeShoudSum.add(shouldPayFeeAmtSum.get());
                                shouldPayFeeAmtSum.set(bFeeShoudSum.add(bigDecimal3.get()));
                                bFeeActuallySum = bFeeActuallySum.add(actuallyPayFeeAmtSum.get());
                                actuallyPayFeeAmtSum.set(bFeeActuallySum.add(bigDecimal4.get()));
                            }
                    );
                    incomeSum.set(t.getIncomeAmt().add(incomeSum.get()));
                    grossProfitAmtSum.set(t.getGrossProfitAmt().add(grossProfitAmtSum.get()));
                    grossProfitAmt2Sum.set(t.getGrossProfitAmt2().add(grossProfitAmt2Sum.get()));
                }
        );
        //查找OA已支付的金额
        BigDecimal feeAlreadyPay = oaPayRebateRecordMapper.selectOaPayRebateRecordByProjectId(oaProjectDeployId);
        if (feeAlreadyPay == null) {
            feeAlreadyPay = BigDecimal.ZERO;
        }
        //剩下的未支付的返费
        BigDecimal feeNoAlreadyPay = actuallyPayFeeAmtSum.get().subtract(feeAlreadyPay);
        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("sum_income_sum", incomeSum.get());
        sumMap.put("sum_fee_amt", feeSum.get());
        sumMap.put("sum_should_pay_fee_amt", shouldPayFeeAmtSum.get());
        sumMap.put("sum_actually_pay_fee_amt", actuallyPayFeeAmtSum.get());
        sumMap.put("sum_gross_profit_amt", grossProfitAmtSum.get());
        sumMap.put("sum_gross_profit_amt2", grossProfitAmt2Sum.get());
        sumMap.put("sum_fee_amt_2", fee2Sum.get());
        sumMap.put("feeAlreadyPayOfOA", feeAlreadyPay);
        sumMap.put("feeNoAlreadyPayOfOA", feeNoAlreadyPay);
        sumMap.put("feeSum", actuallyPayFeeAmtSum.get());
        map.put("sum", sumMap);
        map.put("oaProjectDeployId", oaProjectDeployId);
        return map;
    }

    @Override
    public List<CwProjectCust> selectCwProjectCustListAll() {
        return cwProjectCustMapper.selectCwProjectCustListAll();
    }

    /**
     * 查询财务项目管理-查询该项目下是否有正在进行的状态
     */
    @Override
    public List<Map<String, Object>> selectCwProjectConduct(CwProject cwProject) {
        DateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd");
        DateFormat df = new SimpleDateFormat("yyyy-MM");
        //todo 财务项目管理四期，新增期次  【不做选择】  选项
        List<Map<String, Object>> list1 = new ArrayList<>();
        Map<String, Object> noSelect = new HashMap<>();
        noSelect.put("id", -999);
        noSelect.put("project_id", cwProject.getId());
        noSelect.put("term_month", "不做选择");
        noSelect.put("term_month_compare", "3999-12");
        List<Map<String, Object>> list = cwProjectCustMapper.selectCwProjectCounductByProjectId(cwProject.getId());
        String notSureCompany = "-存在未确定公司";
        list.forEach(
                t -> {
                    //找到这个期次下是否存在custId有-999或者出返费公司是否是暂不确定公司
                    if (Optional.ofNullable(t).isPresent()) {
                        //不是空
                        String term = StringUtils.EMPTY;
                        String termMonth = StringUtils.EMPTY;
                        Date termBegin = null;
                        Date termEnd = null;
                        if (t.containsKey("term")){
                            term = (String) t.get("term");
                        }
                        if (t.containsKey("term_month")) {
                            termMonth = (String) t.get("term_month");
                        }
                        if (t.containsKey("term_begin")) {
                            //首先把非整月的初始时间进行转化，放到term_month中
                            termBegin = (Date) t.get("term_begin");
                            //放到term_month中
                            if (termBegin != null) {
                                String format = df.format(termBegin);
                                t.put("term_month_compare", format);
                            }
                        }
                        if (t.containsKey("term_end")) {
                            termEnd = (Date) t.get("term_end");
                        }
                        if ("1".equals(term)) {
                            String format1 = simpleDateFormat.format(termBegin);
                            String format2 = simpleDateFormat.format(termEnd);
                            t.put("term_month", format1 + "-" + format2);
                        } else if ("0".equals(term)){
                            if (StringUtils.EMPTY.equals(termMonth)) {
                                t.put("term_month", "业务期次所属月份有误！请重新编辑！");
                            } else {
                                //把整月的给放到比较的k—v中
                                t.put("term_month_compare", termMonth);
                                termMonth = termMonth.replace("-", "年");
                                termMonth = termMonth + "月";
                                t.put("term_month", termMonth);
                            }
                        } else {
                            t.put("term_month", "日期错误！");
                        }
                    }
                }
        );
        //找已完结但是没有确定公司的期次
        List<Map<String, Object>> notSureCompanyAndPhaseIsOver = cwProjectCustMapper.selectCwProjectCounductByProjectId1(cwProject.getId());
        for (Map<String, Object> map:notSureCompanyAndPhaseIsOver) {
            if (Optional.ofNullable(map).isPresent()) {
                //不是空
                String term = StringUtils.EMPTY;
                String termMonth = StringUtils.EMPTY;
                Date termBegin = null;
                Date termEnd = null;
                if (map.containsKey("term")){
                    term = (String) map.get("term");
                }
                if (map.containsKey("term_month")) {
                    termMonth = (String) map.get("term_month");
                }
                if (map.containsKey("term_begin")) {
                    //首先把非整月的初始时间进行转化，放到term_month中
                    termBegin = (Date) map.get("term_begin");
                    //放到term_month中
                    if (termBegin != null) {
                        String format = df.format(termBegin);
                        map.put("term_month_compare", format);
                    }
                }
                if (map.containsKey("term_end")) {
                    termEnd = (Date) map.get("term_end");
                }
                if ("1".equals(term)) {
                    String format1 = simpleDateFormat.format(termBegin);
                    String format2 = simpleDateFormat.format(termEnd);
                    map.put("term_month", format1 + "-" + format2 + notSureCompany);
                } else if ("0".equals(term)){
                    if (StringUtils.EMPTY.equals(termMonth)) {
                        map.put("term_month", "业务期次所属月份有误！请重新编辑！");
                    } else {
                        //把整月的给放到比较的k—v中
                        map.put("term_month_compare", termMonth);
                        termMonth = termMonth.replace("-", "年");
                        termMonth = termMonth + "月" + notSureCompany;
                        map.put("term_month", termMonth);
                    }
                } else {
                    map.put("term_month", "日期错误！");
                }
                map.put("notSureCompanyFlag", "1");
            }
        }
        list1.addAll(list);
        list1.addAll(notSureCompanyAndPhaseIsOver);
        //todo 财务项目管理四期修改
        if (list1.size() != 0) {
            list1.add(noSelect);
            //最后根据term_month进行排序
            Collections.sort(list1, new Comparator<Map<String, Object>>() {
                @Override
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    return o2.get("term_month_compare").toString().compareTo(o1.get("term_month_compare").toString());
                }
            });
            return list1;
        } else {
            return list1;
        }
    }

    /**
     * 查询财务项目管理-修改该项目是否是整月非整月
     */
    @Override
    public Map<String, Object> selectCwProjectConductChange(CwProjectIncome cwProjectIncome) {
        return cwProjectCustMapper.selectCwProjectCounductByIncomeId(cwProjectIncome.getId());
    }

    /**
     * 查询财务项目管理-根据收入表id，查该期次的进行状态
     */
    @Override
    public Map<String, Object> selectCwProjectConductDetail(CwProjectIncome cwProjectIncome) {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> cwMap = new HashMap<>();
        Map<String, Object> sum = new HashMap<>();
        List<Map<String, Object>> list = cwProjectCustMapper.selectCwProjectCounductDatailByIncomeId(cwProjectIncome.getId());
        if (list.size() == 0) {
            //该项目还没开始
            map.put("status", "待录入金额");
            cwMap.put("id", cwProjectIncome.getId());
            cwMap.put("incomeAmt", "-");
            cwMap.put("feeList", "-");
            cwMap.put("grossProfitAmt", "-");
            cwMap.put("grossProfitAmt2", "-");
            sum.put("feeAmtSum", "-");
            sum.put("feeAmt2Sum", "-");
            map.put("detail", cwMap);
            map.put("sum", sum);
        } else {
            list.forEach(t -> {
                String phaseStatus = (String) t.get("phase_status");
                String dynamicTime = t.get("dynamic_time").toString();
                t.put("dynamic_time", dynamicTime);
                if ("0".equals(phaseStatus)) {
                    //已录入收入的信息
                    t.put("phase_status", "已录入金额");
                    map.put("status0", t);
                } else if ("1".equals(phaseStatus)) {
                    //已确认收入的信息
                    t.put("phase_status", "已完成");
                    map.put("status1", t);
                }
            });
        }
            //找剩余的信息
            CwProjectIncome cwIncome = cwProjectIncomeMapper.selectCwProjectIncomeById(cwProjectIncome.getId());
            BigDecimal incomeAmt = null;
            BigDecimal grossProfitAmt = null;
            BigDecimal grossProfitAmt2 = null;
            if (Optional.ofNullable(cwIncome).isPresent()) {
                if (Optional.ofNullable(cwIncome.getIncomeAmt()).isPresent()) {
                    incomeAmt = cwIncome.getIncomeAmt();
                }
                if (Optional.ofNullable(cwIncome.getGrossProfitAmt()).isPresent()) {
                    grossProfitAmt = cwIncome.getGrossProfitAmt();
                }
                if (Optional.ofNullable(cwIncome.getGrossProfitAmt2()).isPresent()) {
                    grossProfitAmt2 = cwIncome.getGrossProfitAmt2();
                }
            }
            //找到不是暂不确定公司的返费公司
            List<CwProjectFee> cwProjectFees = cwProjectFeeMapper.selectCwProjectCustFeeListDetailByIncomeId23(cwIncome.getId());
            //todo 财务项目管理四期，找到该期次（普通项目为收入）返费公司为 暂不确定公司的项目
        List<CwProjectFee> notSureCompanyCwProjectFees = cwProjectFeeMapper.selectCwProjectNotSureCompanyCustFeeListDetailByIncomeId2(cwIncome.getId());
        cwProjectFees.addAll(notSureCompanyCwProjectFees);
        cwProjectFees = cwProjectFees.stream().sorted(Comparator.comparing(CwProjectFee::getId)).collect(Collectors.toList());
            //todo 财务项目管理四期，找到这个返费对应的custId，然后进行方案的分组提取
        String schemeFlag = null;
        if (cwProjectFees.size() != 0) {
            schemeFlag = cwProjectFees.get(0).getSchemeFlag();
        }
            List<Map<String, Object>> feeList = new ArrayList<>();
            cwProjectFees.forEach(t -> {
                Map<String, Object> m = new HashMap<>();
                Long id = t.getId();
                String custName = t.getCustName();
                String feeCustName;
                if ("1".equals(t.getReplaceFlag())) {
                    //发生过替换，找最新的名字
                    Long custId = t.getCustId();
                    //找最新的名字
                    feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
                } else {
                    if (t.getCustId() == 0 || t.getCustId() == -999L) {
                        feeCustName = t.getFeeCustName();
                    } else {
                        feeCustName = t.getFeeCustName1();
                    }
                }
                BigDecimal feeAmt = t.getFeeAmt();
                BigDecimal feeAmt2 = t.getFeeAmt2();
                Long revealFeeCompanyId = t.getRevealFeeCompanyId();
                m.put("revealFeeCompanyId", revealFeeCompanyId);
                m.put("id", id);
                m.put("custId", t.getCustId());
                if (t.getCustId() == -999L) {
                    m.put("custId1", -999L);
                }
                m.put("custName", custName);
                if ("暂不确定公司".equals(custName)) {
                    m.put("companyId", -999L);
                }
                m.put("feeCustName", feeCustName);
                m.put("feeAmt", feeAmt);
                m.put("feeAmt2", feeAmt2);
                m.put("calculateType", t.getCalculateType());
                m.put("rate", t.getRate());
                m.put("taxRate", t.getTaxRate());
                if (t.getShouldPayFeeAmt() != null) {
                    m.put("shouldPayFeeAmt", t.getShouldPayFeeAmt());
                } else {
                    m.put("shouldPayFeeAmt", null);
                }
                if (t.getActuallyPayFeeAmt() != null) {
                    m.put("actuallyPayFeeAmt", t.getActuallyPayFeeAmt());
                }
                if ("1".equals(t.getCalculateType())) {
                    m.put("calculateTypeString", "手动计算");
                    m.put("status", false);
                } else if ("0".equals(t.getCalculateType())) {
                    m.put("calculateTypeString", "自动计算");
                    m.put("status", true);
                }
                if (t.getCustId() == -999L) {
                    m.put("calculateTypeFlag", "1");
                    cwMap.put("schemeFlagFromCustRemark", t.getCustRemark());
                } else {
                    m.put("calculateTypeFlag", null);
                }
                feeList.add(m);
            });
            AtomicReference<BigDecimal> feeAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> feeAmt2Sum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> shouldPayFeeAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> actuallyPayFeeAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
            cwProjectFeeMapper.selectCwProjectCustFeeListDetailByIncomeId(cwIncome.getId(), null).forEach(t -> {
                if (t != null){
                    feeAmtSum.set((BigDecimal) t.get("fee_amt"));
                    feeAmt2Sum.set((BigDecimal) t.get("fee_amt2"));
                    shouldPayFeeAmtSum.set((BigDecimal) t.get("should_pay_fee_amt"));
                    actuallyPayFeeAmtSum.set((BigDecimal) t.get("actually_pay_fee_amt"));
                }
            });
            //至此，所有信息都已经拿到，整合一下返回
            cwMap.put("id", cwIncome.getId());
            if (incomeAmt != null) {
                cwMap.put("incomeAmt", incomeAmt);
            } else {
                cwMap.put("incomeAmt", "-");
            }
            if (grossProfitAmt != null) {
                cwMap.put("grossProfitAmt", grossProfitAmt);
            } else {
                cwMap.put("grossProfitAmt", "-");
            }
            if (grossProfitAmt2 != null) {
                cwMap.put("grossProfitAmt2", grossProfitAmt2);
            } else {
                cwMap.put("grossProfitAmt2", "-");
            }
            if (schemeFlag != null && !"".equals(schemeFlag)) {
                cwMap.put("schemeFlag", "方案" + schemeFlag);
            } else if ((schemeFlag == null || "".equals(schemeFlag)) && cwMap.containsKey("schemeFlagFromCustRemark")){
                String schemeFlagFromCustRemark = (String) cwMap.get("schemeFlagFromCustRemark");
                if (cwMap.get("schemeFlagFromCustRemark") != null) {
                    cwMap.put("schemeFlag", cwMap.get("schemeFlagFromCustRemark"));
                }
                cwMap.remove("schemeFlagFromCustRemark");
            } else {
                cwMap.put("schemeFlag", "-");
            }
            cwMap.put("rejectionFlag", cwIncome.getRejectionFlag());
            cwMap.put("incomeRejectionReason", cwIncome.getIncomeRejectionReason());
            cwMap.put("feeRejectionReason", cwIncome.getFeeRejectionReason());
            cwMap.put("feeList", feeList);
            cwMap.put("collectionTime", cwIncome.getCollectionTime());
            if (cwIncome.getCollectionTime() != null) {
                cwMap.put("collectionTimeString", DateUtils.parseDateToStr("yyyy年MM月dd日", cwIncome.getCollectionTime()));
            } else {
                cwMap.put("collectionTimeString", null);
            }
            sum.put("feeAmtSum", feeAmtSum);
            sum.put("feeAmt2Sum", feeAmt2Sum);
            sum.put("shouldPayFeeAmtSum", shouldPayFeeAmtSum);
            sum.put("actuallyPayFeeAmtSum", actuallyPayFeeAmtSum);
            map.put("detail", cwMap);
            map.put("sum", sum);
        Map<String,Object> objectMap = cwProjectCustMapper.getPhaseStatus(cwProjectIncome.getId());
        map.put("phaseStatus",objectMap.get("phaseStatus"));
        map.put("remark",objectMap.get("remark"));
        return map;
    }

    /**
     * 查询财务项目管理-根据收入表id，查询返费公司和其总返费
     */
    @Override
    public List<CwProjectFee> selectCwprojectFeeDetail(CwProjectIncome cwProjectIncome) {
        //返费的没有金额的对象，就是需要手动去算的，这里就不标明了
        return cwProjectFeeMapper.selectCwProjectCustFeeListDetailByIncomeId3(cwProjectIncome.getId());
    }

    /**
     * 查询财务项目管理-根据收入表id，查询返费公司和其总返费
     */
    @Override
    public Map<String,Object> selectCwprojectPayListDetail(List<CwProjectFee> cwProjectFeeList) {
        Map<String,Object> resultMap = new HashMap<>();
        List<Map<String,Object>> objList = new ArrayList<>();
        AtomicInteger i = new AtomicInteger();
        cwProjectFeeList.forEach(t -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", t.getId());
            map.put("custId", t.getCustId());
            map.put("custName", t.getCustName());
            map.put("feeCustName", t.getFeeCustName());
            // todo 返费改为应付返费，但是key不做修改，因为页面逻辑处理细节之前都是泽宇写的，改动的话可能会出问题
            map.put("feeAmt", t.getActuallyPayFeeAmt());
            if ("暂不确定公司".equals(t.getCustName()) && t.getCustId() != -999) {
                map.put("controlFlag", 1);
            } else if ("暂不确定公司".equals(t.getCustName()) && t.getCustId() == -999){
                map.put("controlFlag", 2);
            } else {
                map.put("controlFlag", null);
            }
            AtomicReference<BigDecimal> payAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> differenceAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
            List<Map<String,Object>> payList = new ArrayList<>();
            cwProjectPayMapper.selectCwProjectPayListDetailByFeeId2(t.getId()).forEach(c -> {
                if (Optional.ofNullable(c).isPresent()) {
                    //不为空
                    payList.add(c);
                    BigDecimal payAmt = (BigDecimal) c.get("payAmt");
                    BigDecimal differenceAmt = (BigDecimal) c.get("differenceAmt");
                    if (Optional.ofNullable(payAmt).isPresent()){
                        //不为空
                        payAmtSum.set(payAmtSum.get().add(payAmt));
                        differenceAmtSum.set(differenceAmtSum.get().add(differenceAmt));
                    }
                }
            });
            BigDecimal result = payAmtSum.get().add(differenceAmtSum.get());
            if (result.compareTo(BigDecimal.ZERO) == 0) {
                //没有打款记录，说明该公司的返费一点也没支付，就是未开始
                map.put("payStatus", "未开始");
                map.put("payAlreadySum", "0.00");
                map.put("payNoAlreadySum", t.getActuallyPayFeeAmt());
                map.put("payButton", "true");
                map.put("changeButton", "false");
                i.getAndIncrement();
            } else if (result.compareTo(BigDecimal.ZERO) > 0
                    && result.compareTo(t.getActuallyPayFeeAmt()) == -1) {
                map.put("payStatus", "部分打款");
                map.put("payAlreadySum", result);
                map.put("payNoAlreadySum", t.getActuallyPayFeeAmt().subtract(result));
                map.put("payButton", "true");
                map.put("changeButton", "false");
                i.getAndIncrement();
            } else if (result.compareTo(t.getActuallyPayFeeAmt()) == 0) {
                //每次查到的打款金额放在这里
                map.put("payStatus", "已完成");
                map.put("payAlreadySum", result);
                map.put("payNoAlreadySum", "0.00");
                map.put("payButton", "false");
                map.put("changeButton", "true");
            }
            if (0 != payList.size()) {
                map.put("payList", payList);
            } else {
                Map<String, Object> nullMap = new HashMap<>();
                nullMap.put("payId", "");
                nullMap.put("payAmt", "");
                nullMap.put("differenceAmt", "");
                nullMap.put("payDate", "");
                payList.add(nullMap);
                map.put("payList", payList);
            }
            objList.add(map);
        });
        resultMap.put("result", objList);
        if (0 == i.get()) {
            //说明没有未开始和部分打款的
            resultMap.put("button", "true");
        } else {
            resultMap.put("button", "false");
        }
        return resultMap;
    }

    /**
     * 查询财务项目管理-根据主表id，查该项目的各种信息供修改
     */
    @Override
    public Map<String, Object> selectCwprojectDetail(CwProject cwProject) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Long> accountantList = new ArrayList<>();
        List<Long> cashierList = new ArrayList<>();
        List<Long> businessList = new ArrayList<>();

        List<Long> checkReturnList = new ArrayList<>();
        List<Long> exportReturnList = new ArrayList<>();
        CwProject cwProject1 = cwProjectMapper.selectCwProjectById(cwProject.getId());
        List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectCustListByProjectId(cwProject.getId());
        List<Map<String, Object>> userList = cwProjectUserMapper.selectCwProjectUserByProjectId2(cwProject.getId());
        resultMap.put("id", cwProject1.getId());
        resultMap.put("projectName", cwProject1.getProjectName());
        resultMap.put("custName", cwProject1.getCustName());
        resultMap.put("incomeCustName", cwProject1.getIncomeCustName());
        resultMap.put("custList", cwProjectCusts);
        resultMap.put("oaProjectDeployId", cwProject1.getOaProjectDeployId());
        resultMap.put("generateCertificateFlag", cwProject1.getGenerateCertificateFlag());
        resultMap.put("accountSetsId", cwProject1.getAccountSetsId());
        resultMap.put("guaranteeIncomeType", cwProject1.getGuaranteeIncomeType());
        resultMap.put("guarantyPayee", cwProject1.getGuarantyPayee());

        Map<Long, OaDataManage> projectTypeMap = oaDataManageMapper.selectDataManageListByCode("project_type").stream()
                .filter(vo -> vo.getParentId() != null) //过滤最上一级节点
                .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
        //组装一二级项目类型名称
        if (cwProject1.getProjectTypeRelevanceTypeId() != null){
            String dataNameWithParents = getDataNameWithParents(cwProject1.getProjectTypeRelevanceTypeId(), projectTypeMap);
            if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                dataNameWithParents = dataNameWithParents.substring(1);
            }
            resultMap.put("projectTypeName", dataNameWithParents);
        }
        if ("lawUrgingNo".equals(cwProject.getProjectPortfolioCode())) {
            Map<String, Object> map = this.selectCwProjectOverListDetileByProjectId(cwProject1, 1);
            List<CwProjectOverDetailCompanyDto> feeList = (List<CwProjectOverDetailCompanyDto>) map.get("fee_list");
            Map<Integer, List<CwProjectOverDetailCompanyDto>> collect = feeList.stream().collect(Collectors.groupingBy(CwProjectOverDetailCompanyDto::getSchemeFlagInt));
            List<SchemeVo> schemeVos = new ArrayList<>();
            for (Map.Entry<Integer, List<CwProjectOverDetailCompanyDto>> c:collect.entrySet()) {
                //方案标识
                Integer key = c.getKey();
                //按照方案标识分组后的返费方案
                List<CwProjectOverDetailCompanyDto> value = c.getValue();
                // todo 这里要特别注意，因为前端在处理数据对象的时候，因为还没有入库，所以返费标识按照 索引+1 的处理方式进行新增入库的
                // todo 现在要做修改的话，后端的 方案标识-1 作为前端修改的索引才是正确的
                int i = key - 1;
                List<CwProjectCustVo> cwProjectCustVos = new ArrayList<>();
                SchemeVo schemeVo = new SchemeVo();
                //方案计数
                int a = 0;
                for (CwProjectOverDetailCompanyDto cpodc: value) {
                    CwProjectCustVo cwProjectCustVo = new CwProjectCustVo();
                    cwProjectCustVo.setId(cpodc.getId());
                    cwProjectCustVo.setCustName(cpodc.getCustName());
                    cwProjectCustVo.setRate(new BigDecimal(cpodc.getRate()));
                    cwProjectCustVo.setTaxRate(new BigDecimal(cpodc.getTaxRate()));
                    cwProjectCustVo.setOaTraderId(cpodc.getOaTraderId());
                    cwProjectCustVo.setReplaceFlag(cpodc.getReplaceFlag());
                    cwProjectCustVo.setSchemeFlag(i);
                    cwProjectCustVo.setSchemeFlagUseSituation(cpodc.getSchemeFlagUseSituation());
                    if (cpodc.getSchemeFlagUseSituation() > 0) {
                        a = cpodc.getSchemeFlagUseSituation();
                    }
                    cwProjectCustVos.add(cwProjectCustVo);
                }
                if (a > 0) {
                    schemeVo.setUseSituation(a);
                }
                schemeVo.setCheckRepeatFlag(false);
                schemeVo.setCwProjectCusts(cwProjectCustVos);
                schemeVos.add(schemeVo);
            }
            resultMap.put("schemeList", schemeVos);
        }
        resultMap.put("prestoreIncomeFlag", cwProject1.getPrestoreIncomeFlag());
        userList.forEach(u -> {
            if (CwxmglConstants.USER_FLAG_0.equals(u.get(CwxmglConstants.USER_FLAG))) {
                accountantList.add(Long.valueOf(u.get("user_id").toString()));
            } else if (CwxmglConstants.USER_FLAG_1.equals(u.get(CwxmglConstants.USER_FLAG))) {
                cashierList.add(Long.valueOf(u.get("user_id").toString()));
            } else if (CwxmglConstants.USER_FLAG_2.equals(u.get(CwxmglConstants.USER_FLAG))) {
                businessList.add(Long.valueOf(u.get("user_id").toString()));
            }else if(CwxmglConstants.USER_FLAG_3.equals((u.get(CwxmglConstants.USER_FLAG)))){
                checkReturnList.add(Long.valueOf(u.get("user_id").toString()));
            }else if(CwxmglConstants.USER_FLAG_4.equals((u.get(CwxmglConstants.USER_FLAG)))){
                exportReturnList.add(Long.valueOf(u.get("user_id").toString()));
            }
        });
        resultMap.put("accountantList", accountantList);
        resultMap.put("cashierList", cashierList);
        resultMap.put("businessList", businessList);
        resultMap.put("checkreturnList", checkReturnList);
        resultMap.put("exportreturnList", exportReturnList);
        return resultMap;
    }

    /**
     * 查询财务项目管理-根据主表id，查该项目三个字段
     */
    @Override
    public Map<String, Object> selectCwprojectDetailThree(CwProject cwProject) {
        Map<String, Object> map = cwProjectMapper.selectCwprojectDetailThree(cwProject.getId());
        Map<Long, OaDataManage> projectTypeMap = oaDataManageMapper.selectDataManageListByCode("project_type").stream()
                .filter(vo -> vo.getParentId() != null) //过滤最上一级节点
                .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
        //组装一二级项目类型名称
        if (map.containsKey("projectTypeId") && map.get("projectTypeId") != null){
            String dataNameWithParents = getDataNameWithParents((Long) map.get("projectTypeId"), projectTypeMap);
           if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
               dataNameWithParents = dataNameWithParents.substring(1);
           }
            map.put("projectTypeName", dataNameWithParents);
        }
        return map;
    }

    @Override
    public Map<String, Object> selectCwProjectLawDetileByProjectId(CwProject cwProject, Integer sumFlag) {
        CwProject cwProject1 = cwProjectMapper.selectCwProjectById(cwProject.getId());
        Long oaProjectDeployId = cwProject1.getOaProjectDeployId();
        Map<String, Object> map = new HashMap<>();
        //通过项目的维度来查各种信息，使用主键id来查到相关的返费公司，调用上面的方法查其他
        List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectCustListByProjectIdForLaw(cwProject.getId());
        long count = cwProjectCusts.stream().filter(t -> "1".equals(t.getReplaceFlag())).count();
        List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfoList = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByProjectId(cwProject.getId());
        List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = new ArrayList<>();
        if (count > 0L) {
            //说明有发生过替换,根据项目id查找cw_project_replace_fee_company_info表,得到所有替换的数据
            cwProjectReplaceFeeCompanyInfos = cwProjectReplaceFeeCompanyInfoList;
        }
        List<CwProjectOverDetailCompanyDto> cwProjectCompany = new ArrayList<>();
        List<CwProjectReplaceFeeCompanyInfo> finalCwProjectReplaceFeeCompanyInfos = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> "0".equals(t.getStatus())).collect(Collectors.toList());
        //2024.10.24
        List<CwProjectReplaceFeeCompanyInfo> finalCwProjectReplaceFeeCompanyInfos1 = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> "1".equals(t.getStatus())).collect(Collectors.toList());
        cwProjectCusts.forEach(t -> {
            CwProjectOverDetailCompanyDto cwProjectOverDetailCompanyDto = new CwProjectOverDetailCompanyDto();
            if ("1".equals(t.getReplaceFlag())) {
                List<CwProjectReplaceFeeCompanyInfo> collect = finalCwProjectReplaceFeeCompanyInfos.stream().filter(a -> a.getOaTraderId().equals(t.getOaTraderId())).collect(Collectors.toList());
                List<String> oldCompanyNameList = new ArrayList<>();
                //2024.10.24
                List<Map<String, Object>> mapReplaceList = new ArrayList<>();
                if (collect.size() > 0) {
                    //判断之前的返费公司替换是否被撤回
                    if (!collect.stream().anyMatch(i -> i.getOldOaTraderId().equals(t.getOaTraderId()))) {
                        //有被撤回的。那么就去找关系
                        //collect2找到的就是撤回掉的关系
                        List<CwProjectReplaceFeeCompanyInfo> collect2 = finalCwProjectReplaceFeeCompanyInfos1.stream().filter(a -> a.getOaTraderId().equals(t.getOaTraderId())).sorted(Comparator.comparing(CwProjectReplaceFeeCompanyInfo::getId).reversed()).collect(Collectors.toList());
                        //然后找撤回掉的关系里最近的一个能对上的oaTraderId
                        CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo = collect2.stream().filter(a -> a.getOldOaTraderId().equals(t.getOaTraderId())).findFirst().orElse(null);
                        //先把没有过滤掉的给弄出来，后续去替换
                        for (CwProjectReplaceFeeCompanyInfo cprfci:collect) {
                            String oldOaTraderUserName = cprfci.getOldOaTraderUserName();
                            Long oldOaTraderId = cprfci.getOldOaTraderId();
                            Map<String, Object> mapReplace = new HashMap<>();
                            mapReplace.put("oldOaTraderId", oldOaTraderId);
                            mapReplace.put("oldOaTraderUserName", oldOaTraderUserName);
                            mapReplaceList.add(mapReplace);
//                            oldCompanyNameList.add(oldOaTraderUserName);
                        }
                        if (cwProjectReplaceFeeCompanyInfo != null) {
                            Long id1 = cwProjectReplaceFeeCompanyInfo.getId();
                            List<CwProjectReplaceFeeCompanyInfo> collect3 = collect2.stream().filter(a -> a.getId() >= id1).collect(Collectors.toList());
                            //找到的collect3里包含着最近撤回的一套关系
                            for (CwProjectReplaceFeeCompanyInfo projectReplaceFeeCompanyInfo : collect3) {
                                Long newOaTraderId = projectReplaceFeeCompanyInfo.getNewOaTraderId();
                                Long oaTraderId = projectReplaceFeeCompanyInfo.getOaTraderId();
                                String oaTraderUserName = projectReplaceFeeCompanyInfo.getOaTraderUserName();
                                if (mapReplaceList.stream().anyMatch(a -> Long.valueOf(a.get("oldOaTraderId").toString()).equals(newOaTraderId))) {
                                    //把老的关系里的oaTraderId替换成新关系里的oldOatraderId
                                    Map<String, Object> map1 = mapReplaceList.stream().filter(a -> Long.valueOf(a.get("oldOaTraderId").toString()).equals(newOaTraderId)).findFirst().orElse(null);
                                    //获得存在的索引
                                    int i = mapReplaceList.indexOf(map1);
                                    //修改map1
                                    map1.put("oldOaTraderId", oaTraderId);
                                    map1.put("oldOaTraderUserName", oaTraderUserName);
                                    mapReplaceList.set(i, map1);
                                }
                            }
                        }
                        //替换完成之后，进行添加显示
                        oldCompanyNameList.addAll(mapReplaceList.stream().map(a -> a.get("oldOaTraderUserName").toString()).collect(Collectors.toList()));
                    } else {
                        for (CwProjectReplaceFeeCompanyInfo cprfci:collect) {
                            String oldOaTraderUserName = cprfci.getOldOaTraderUserName();
                            oldCompanyNameList.add(oldOaTraderUserName);
                        }
                    }

                }
                cwProjectOverDetailCompanyDto.setOldCompanyName(oldCompanyNameList);
            } else {
                cwProjectOverDetailCompanyDto.setOldCompanyName(null);
            }
            cwProjectOverDetailCompanyDto.setId(t.getId());
            if (null == t.getRate()) {
                cwProjectOverDetailCompanyDto.setRate("未设置");
            } else {
                String format1;
                if (Optional.ofNullable(sumFlag).isPresent()) {
                    //不为空，即不需要百分号
                    format1 = new BigDecimal(t.getRate().toString()).stripTrailingZeros().toPlainString();
                } else {
                    format1 = new BigDecimal(t.getRate().toString()).stripTrailingZeros().toPlainString() + "%";
                }
                cwProjectOverDetailCompanyDto.setRate(format1);
            }
            //todo 财务项目管理四期，因为是法催项目，所以没有税率
            cwProjectOverDetailCompanyDto.setCustName(t.getCustName());
            cwProjectCompany.add(cwProjectOverDetailCompanyDto);
        });
        map.put("fee_list", cwProjectCompany);
        //找对应的项目的用户情况
        List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(oaProjectDeployId, AuthModuleEnum.FINANCEPROJ.getCode());
        //会计
        String accountant = userList.stream().filter(a -> "1".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
        //出纳
        String cashier = userList.stream().filter(a -> "2".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
        //业务
//        String business = userList.stream().filter(a -> "3".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
        //查看权限
        String select = userList.stream().filter(a -> "88".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
//        List<Map<String, Object>> users = cwProjectUserMapper.selectCwProjectUserByProjectId(cwProject.getId());
//        String accountant = StringUtils.EMPTY;
//        String cashier = StringUtils.EMPTY;
//        String business = StringUtils.EMPTY;
//        String select = StringUtils.EMPTY;
//        String export = StringUtils.EMPTY;
//        String flag = StringUtils.EMPTY;
        Map<String, Object> map1 = new HashMap<>();
        Map<String, Object> map2 = new HashMap<>();
        Map<String, Object> map4 = new HashMap<>();
        map1.put("flag", "会计");
        map1.put("people_name", accountant);
        map2.put("flag", "出纳");
        map2.put("people_name", cashier);
        map4.put("flag", "查看权限");
        map4.put("people_name", select);
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(map1);
        list.add(map2);
        list.add(map4);
        map.put("people_list", list);
        map.put("replaceCompanyInfo", cwProjectReplaceFeeCompanyInfoList);
        //通过期次id找收入
        List<CwProjectLawDetailView> projList = cwProjectIncomeMapper.selectLawCwProjectOverListDetileByProjectIdV2(cwProject.getId());
        //过滤有期次id的数据，收入有期次id，期次没有期次id。即过滤掉期次，只剩下收入
        Map<Long, List<CwProjectLawDetailView>> groupMap = projList.stream().filter(t -> t.getPhaseId() != null).collect(Collectors.groupingBy(CwProjectLawDetailView::getPhaseId));
        //过滤没有期次id的数据，即过滤掉收入，只剩下期次。
        List<CwProjectLawDetailView> collect1 = projList.stream().filter(t -> t.getPhaseId() == null).collect(Collectors.toList());
        //剩下还没有录入收入与返费的期次，即每个期次有且只有一条期次信息。
        Map<Long, List<CwProjectLawDetailView>> phaseNoIncomeAndFeeMap = collect1.stream().filter(t -> "待录入".equals(t.getPhaseStatus())).collect(Collectors.groupingBy(CwProjectLawDetailView::getProjectIncomeId));
        //把没有录入收入与返费的期次添加到groupMap当中，他的每一个value，即List<CwProjectLawDetailView>大小都为1
        groupMap.putAll(phaseNoIncomeAndFeeMap);
        //排序map
        Map<String, List<CwProjectLawDetailView>> sortMap = new TreeMap<>(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o2.compareTo(o1);
            }
        });
        AtomicReference<List<CwProjectLawDetailView>> collect = new AtomicReference<>(new ArrayList<>());
        groupMap.forEach((k, v) -> {
            String pinjieKey = StringUtils.EMPTY;
            if (v.size() != 1) {
                Optional<CwProjectLawDetailView> first = projList.stream().filter(t -> t.getProjectIncomeId().equals(k)).findFirst();
//            first.ifPresent(v::add);
                BigDecimal thisPhaseTrueComeAmt = v.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectLawDetailView::getTrueComeAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal thisPhaseServiceFee = v.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectLawDetailView::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal thisPhasePrincipal = v.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectLawDetailView::getPrincipal).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal thisPhaseFeeAmt = v.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectLawDetailView::getFeeAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal thisPhaseFeeRound = v.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectLawDetailView::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal thisPhaseFeeAmt2 = v.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectLawDetailView::getFeeAmt2).reduce(BigDecimal.ZERO, BigDecimal::add);
                //以下是新增字段
                BigDecimal thisPhaseCurrentFee = v.stream().filter(t -> "1".equals(t.getServiceProviderFlag()) && t.getCurrentFee() != null).map(CwProjectLawDetailView::getCurrentFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                //todo 财务项目管理四期 新增借条分润、法催利润
                BigDecimal thisPhaseJtfrAmt = v.stream().filter(t -> "1".equals(t.getServiceProviderFlag()) && t.getJtfrAmt() != null).map(CwProjectLawDetailView::getJtfrAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal thisPhaseLawProfit = v.stream().filter(t -> "1".equals(t.getServiceProviderFlag()) && t.getLawProfit() != null).map(CwProjectLawDetailView::getLawProfit).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (first.isPresent()) {
                    String termMonth = first.get().getTermMonth();
                    BigDecimal grossProfitAmt = first.get().getGrossProfitAmt();
                    BigDecimal grossProfitAmt2 = first.get().getGrossProfitAmt2();
                    String phaseStatus = first.get().getPhaseStatus();
                    String remark = first.get().getRemark();
                    Date collectionTime = first.get().getCollectionTime();
                    v.get(0).setTermMonth(termMonth);
                    v.get(0).setGrossProfitAmt(grossProfitAmt);
                    v.get(0).setGrossProfitAmt2(grossProfitAmt2);
                    v.get(0).setPhaseStatus(phaseStatus);
                    v.get(0).setRemark(remark);
                    v.get(0).setCollectionTime(collectionTime);
                    CwProjectLawDetailView cwProjectLawDetailView = first.get();
                    cwProjectLawDetailView.setPhaseId(k);
                    cwProjectLawDetailView.setServiceProvider("本期次合计");
                    cwProjectLawDetailView.setPhaseStatus(null);
                    cwProjectLawDetailView.setTrueComeAmt(thisPhaseTrueComeAmt);
                    cwProjectLawDetailView.setServiceFee(thisPhaseServiceFee);
                    cwProjectLawDetailView.setPrincipal(thisPhasePrincipal);
                    cwProjectLawDetailView.setFeeAmt(thisPhaseFeeAmt);
                    cwProjectLawDetailView.setFeeAmt2(thisPhaseFeeAmt2);
                    cwProjectLawDetailView.setFeeRound(thisPhaseFeeRound);
                    //以下的新增字段
                    cwProjectLawDetailView.setCurrentFee(thisPhaseCurrentFee);
                    //todo 财务项目管理四期 新增借条分润、法催利润
                    cwProjectLawDetailView.setJtfrAmt(thisPhaseJtfrAmt);
                    cwProjectLawDetailView.setLawProfit(thisPhaseLawProfit);
                    v.add(cwProjectLawDetailView);
                    pinjieKey = first.get().getTermMonthCompare();
                }
            } else {
                //list中这个期次的一个收入
                Optional<CwProjectLawDetailView> first = projList.stream().filter(t -> t.getProjectIncomeId().equals(k)).findFirst();
                if (first.isPresent()) {
                    String termMonth = first.get().getTermMonth();
                    BigDecimal grossProfitAmt = first.get().getGrossProfitAmt();
                    BigDecimal grossProfitAmt2 = first.get().getGrossProfitAmt2();
                    String phaseStatus = first.get().getPhaseStatus();
                    String termMonthCompare = first.get().getTermMonthCompare();
                    String remark = first.get().getRemark();
                    Date collectionTime = first.get().getCollectionTime();
                    v.get(0).setTermMonth(termMonth);
                    v.get(0).setGrossProfitAmt(grossProfitAmt);
                    v.get(0).setGrossProfitAmt2(grossProfitAmt2);
                    v.get(0).setPhaseStatus(phaseStatus);
                    v.get(0).setTermMonthCompare(termMonthCompare);
                    v.get(0).setRemark(remark);
                    v.get(0).setCollectionTime(collectionTime);
                }
                pinjieKey = v.get(0).getTermMonthCompare();
            }
            //todo 最新的累计挂起金额逻辑 - 找所有挂起金额
            List<Map<String, Object>> suspendAmtInfoList = cwProjectIncomeMapper.selectAllSuspendAmtInfo();
            //看是否有清除的
            Map<Long, List<Map<String, Object>>> suspendMap = suspendAmtInfoList.stream().filter(t -> t.get("suspendClearId") != null).collect(Collectors.groupingBy(item -> Long.valueOf(String.valueOf(item.get("suspendClearId")))));
            suspendMap.forEach((key1, value1) -> {
                BigDecimal currentFee = value1.stream().map(az -> new BigDecimal(String.valueOf(az.get("currentFee")))).reduce(BigDecimal.ZERO, BigDecimal::add);
                //通过期次的返费id，确定这个返费以及他下面的返费
                List<CwProjectLawDetailView> collect2 = v.stream().filter(a -> !"本期次合计".equals(a.getServiceProvider()) && a.getProjectFeeId() != null && a.getProjectFeeId().equals(key1)).collect(Collectors.toList());
                collect2.forEach(c -> c.setSuspendClearAmt(currentFee));
            });
            //对需要操作的数据进行过滤分组，过滤条件 ---> 期次id不为null 、 服务商名不为 本期次合计。按照期次id进行分组
            Map<Long, List<CwProjectLawDetailView>> collect2 = v.stream().filter(t -> t.getPhaseId() != null && !"本期次合计".equals(t.getServiceProvider())).collect(Collectors.groupingBy(CwProjectLawDetailView::getPhaseId));
            collect2.forEach((k1, v1) -> {
                //期次id进行分组完成之后，并不能满足需求，需要对入库时所对应的字段 ---> 服务商分组标识
                //为什么用这个字段？因为在入库时候把同一个服务商的底下的做标识入库。即，若有一个一级服务商和三个二级服务商，但是入库时的  服务商分组标识   都是一样的，例如是0
                Map<Integer, List<CwProjectLawDetailView>> collect4 = v1.stream().collect(Collectors.groupingBy(CwProjectLawDetailView::getServiceGroupFlag));
                collect4.forEach((k11, v11) -> {
                    //然后对服务商分组标识，进行判断，分组后看是否等于1。不等于1时，即一级服务商下有二级服务商，那么这个时候，一级服务商累计挂起不显示。
                    if (v11.size() != 1) {
                        v11.stream().filter(t -> "1".equals(t.getServiceProviderFlag()) && t.getSuspendClearAmt() == null).forEach(t -> t.setSuspendAmtSum(null));
                        v11.stream().filter(t -> "1".equals(t.getServiceProviderFlag()) && t.getSuspendClearAmt() != null).forEach(t -> t.setSuspendAmtSum(suspendAmtInfoList.stream().filter(a -> t.getServiceProvider().equals(a.get("serviceProvider")) && "1".equals(a.get("suspendFlag"))).map(a -> new BigDecimal(String.valueOf(a.get("currentFee")))).reduce(BigDecimal.ZERO, BigDecimal::add).abs()));
                        List<CwProjectLawDetailView> collect3 = v11.stream().filter(t -> "2".equals(t.getServiceProviderFlag())).collect(Collectors.toList());
                        collect3.forEach(t -> {
                            t.setSuspendAmtSum(suspendAmtInfoList.stream().filter(a -> t.getServiceProviderSecond().equals(a.get("serviceProviderSecond")) && "1".equals(a.get("suspendFlag"))).map(a -> new BigDecimal(String.valueOf(a.get("currentFee")))).reduce(BigDecimal.ZERO, BigDecimal::add).abs());
                        });
                    } else {
                        //等于1时，说明该服务商是只有一个一级服务商，那么就正常显示挂起累计的金额
                        v11.forEach(f -> f.setSuspendAmtSum(suspendAmtInfoList.stream().filter(a -> f.getServiceProvider().equals(a.get("serviceProvider")) && "1".equals(a.get("suspendFlag"))).map(a -> new BigDecimal(String.valueOf(a.get("currentFee")))).reduce(BigDecimal.ZERO, BigDecimal::add).abs()));
                    }
                });
            });
            sortMap.put(pinjieKey + ":" + k, v);
            collect.set(sortMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        });
        List<CwProjectLawDetailView> cwProjectLawDetailViews = collect.get();
        cwProjectLawDetailViews.forEach(t -> {
            if ("2".equals(t.getServiceProviderFlag())) {
                t.setServiceProvider(null);
            }
        });
        BigDecimal incomeSum = new BigDecimal("0.00");
        BigDecimal grossProfitAmtSum = new BigDecimal("0.00");
        BigDecimal grossProfitAmt2Sum = new BigDecimal("0.00");
        for (CwProjectLawDetailView cpldv:collect1) {
            if (cpldv.getIncomeAmt() != null) {
                incomeSum = incomeSum.add(cpldv.getIncomeAmt());
            }
            if (cpldv.getGrossProfitAmt() != null) {
                grossProfitAmtSum = grossProfitAmtSum.add(cpldv.getGrossProfitAmt());
            }
            if (cpldv.getGrossProfitAmt2() != null) {
                grossProfitAmt2Sum = grossProfitAmt2Sum.add(cpldv.getGrossProfitAmt2());
            }
        }
        BigDecimal feeSum = new BigDecimal("0.00");
        BigDecimal fee2Sum = new BigDecimal("0.00");
        BigDecimal trueComeAmtSum = new BigDecimal("0.00");
        BigDecimal serviceFeeSum = new BigDecimal("0.00");
        BigDecimal principalSum = new BigDecimal("0.00");
        BigDecimal feeRoundSum = new BigDecimal("0.00");
        BigDecimal currentFeeSum = new BigDecimal("0.00");
        BigDecimal jtfrAmtSum = new BigDecimal("0.00");
        BigDecimal lawProfitSum = new BigDecimal("0.00");
        List<CwProjectLawDetailView> collect2 = cwProjectLawDetailViews.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).collect(Collectors.toList());
        for (CwProjectLawDetailView cpldv:collect2) {
            if (cpldv.getFeeAmt() != null) {
                feeSum = feeSum.add(cpldv.getFeeAmt());
            }
            if (cpldv.getFeeAmt2() != null) {
                fee2Sum = fee2Sum.add(cpldv.getFeeAmt2());
            }
            if (cpldv.getTrueComeAmt() != null) {
                trueComeAmtSum = trueComeAmtSum.add(cpldv.getTrueComeAmt());
            }
            if (cpldv.getServiceFee() != null) {
                serviceFeeSum = serviceFeeSum.add(cpldv.getServiceFee());
            }
            if (cpldv.getPrincipal() != null) {
                principalSum = principalSum.add(cpldv.getPrincipal());
            }
            if (cpldv.getFeeRound() != null) {
                feeRoundSum = feeRoundSum.add(cpldv.getFeeRound());
            }
            if (cpldv.getCurrentFee() != null) {
                currentFeeSum = currentFeeSum.add(cpldv.getCurrentFee());
            }
            if (cpldv.getJtfrAmt() != null) {
                jtfrAmtSum = jtfrAmtSum.add(cpldv.getJtfrAmt());
            }
            if (cpldv.getLawProfit() != null) {
                lawProfitSum = lawProfitSum.add(cpldv.getLawProfit());
            }
        }
        List<CwProjectLawDetailView> collect3 = cwProjectLawDetailViews.stream().filter(t -> t.getPhaseId() != null && !"本期次合计".equals(t.getServiceProvider())).collect(Collectors.toList());
        BigDecimal payAmtSum = new BigDecimal("0.00");
        BigDecimal differenceAmtSum = new BigDecimal("0.00");
        for (CwProjectLawDetailView cpldv:collect3) {
            if (cpldv.getPayAmt() != null) {
                payAmtSum = payAmtSum.add(cpldv.getPayAmt());
            }
            if (cpldv.getDifferenceAmt() != null) {
                differenceAmtSum = differenceAmtSum.add(cpldv.getDifferenceAmt());
            }
        }
        long phaseNoFinish = cwProjectLawDetailViews.stream().filter(t -> t.getPhaseStatus()!= null && !StringUtils.EMPTY.equals(t.getPhaseStatus()) && !"已完成".equals(t.getPhaseStatus())).count();
        //从feeList当中找所有的oaTraderId，不管是新的旧的还是原来的，都找到，查名字（因为替换可能要显示出来）
        Set<OaTrader> revealFeeCompanyList = new HashSet<>();
        for (CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo : cwProjectReplaceFeeCompanyInfoList) {
            Long oldOaTraderId = cwProjectReplaceFeeCompanyInfo.getOldOaTraderId();
            String oldOaTraderUserName = cwProjectReplaceFeeCompanyInfo.getOldOaTraderUserName();
            OaTrader oldOaTrader = new OaTrader();
            oldOaTrader.setId(oldOaTraderId);
            oldOaTrader.setUserName(oldOaTraderUserName);
            revealFeeCompanyList.add(oldOaTrader);
            Long newOaTraderId = cwProjectReplaceFeeCompanyInfo.getNewOaTraderId();
            String newOaTraderUserName = cwProjectReplaceFeeCompanyInfo.getNewOaTraderUserName();
            OaTrader newOaTrader = new OaTrader();
            newOaTrader.setId(newOaTraderId);
            newOaTrader.setUserName(newOaTraderUserName);
            revealFeeCompanyList.add(newOaTrader);
        }
        for (CwProjectLawDetailView cpldv:cwProjectLawDetailViews) {
            if (cpldv.getRevealFeeCompanyId() == null) {
                Long projectCustId = cpldv.getProjectCustId();
                String feeCustName = StringUtils.EMPTY;
                if (projectCustId != null) {
                    feeCustName = cwProjectCompany.stream().filter(t -> t.getId().equals(projectCustId)).findFirst().map(CwProjectOverDetailCompanyDto::getCustName).orElse("数据错误，没有找到对应的返费公司");
                    if (projectCustId == -999L) {
                        feeCustName = "暂不确定公司";
                    }
                }
                cpldv.setFeeCustName(feeCustName);
            } else {
                Long feeCustId = cpldv.getRevealFeeCompanyId();
                String feeCustName = StringUtils.EMPTY;
                if (feeCustId != null) {
                    feeCustName = revealFeeCompanyList.stream().filter(t -> t.getId().equals(feeCustId)).findFirst().map(OaTrader::getUserName).orElse("数据错误，没有找到对应的返费公司");
                    if (feeCustId == -999L) {
                        feeCustName = "暂不确定公司";
                    }
                }
                cpldv.setFeeCustName(feeCustName);
            }
        }
        //查找OA已支付的金额
        BigDecimal feeAlreadyPay = oaPayRebateRecordMapper.selectOaPayRebateRecordByProjectId(oaProjectDeployId);
        if (feeAlreadyPay == null) {
            feeAlreadyPay = BigDecimal.ZERO;
        }
        //剩下的未支付的返费
        BigDecimal feeNoAlreadyPay = feeRoundSum.subtract(feeAlreadyPay);
        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("sum_income_sum", incomeSum);
        sumMap.put("sum_fee_amt", feeSum);
        sumMap.put("sum_gross_profit_amt", grossProfitAmtSum);
        sumMap.put("sum_gross_profit_amt2", grossProfitAmt2Sum);
        sumMap.put("sum_fee_amt_2", fee2Sum);
        sumMap.put("sum_pay_amt", payAmtSum);
        sumMap.put("sum_difference_amt", differenceAmtSum);
        sumMap.put("sum_fee_roud", feeRoundSum);
        sumMap.put("sum_current_fee", currentFeeSum);
        sumMap.put("sum_true_come_amt", trueComeAmtSum);
        sumMap.put("sum_service_fee", serviceFeeSum);
        sumMap.put("sum_principal", principalSum);
        sumMap.put("sum_jtfr_amt", jtfrAmtSum);
        sumMap.put("sum_law_profit", lawProfitSum);
        sumMap.put("feeAlreadyPayOfOA", feeAlreadyPay);
        sumMap.put("feeNoAlreadyPayOfOA", feeNoAlreadyPay);
        sumMap.put("feeSum", feeRoundSum);
        map.put("detail_list_v2", cwProjectLawDetailViews);
        map.put("phaseNoFinish", phaseNoFinish);
        map.put("sum", sumMap);
        return map;
    }

    @Override
    public List<Map<String, Object>> selectCwProjectConductLaw(CwProject cwProject) {
        DateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd");
        DateFormat df = new SimpleDateFormat("yyyy-MM");
        //todo 财务项目管理四期，新增期次  【不做选择】  选项
        List<Map<String, Object>> list1 = new ArrayList<>();
        Map<String, Object> noSelect = new HashMap<>();
        noSelect.put("id", -999);
        noSelect.put("project_id", cwProject.getId());
        noSelect.put("term_month", "不做选择");
        noSelect.put("term_month_compare", "3999-12");
        list1.add(noSelect);
        List<Map<String, Object>> list = cwProjectCustMapper.selectCwProjectCounductLawByProjectId(cwProject.getId());
        list.forEach(
                t -> {
                    if (Optional.ofNullable(t).isPresent()) {
                        //不是空
                        String term = StringUtils.EMPTY;
                        String termMonth = StringUtils.EMPTY;
                        Date termBegin = null;
                        Date termEnd = null;
                        if (t.containsKey("term")){
                            term = (String) t.get("term");
                        }
                        if (t.containsKey("term_month")) {
                            termMonth = (String) t.get("term_month");
                        }
                        if (t.containsKey("term_begin")) {
                            //首先把非整月的初始时间进行转化，放到term_month中
                            termBegin = (Date) t.get("term_begin");
                            //放到term_month中
                            if (termBegin != null) {
                                String format = df.format(termBegin);
                                t.put("term_month_compare", format);
                            }
                        }
                        if (t.containsKey("term_end")) {
                            termEnd = (Date) t.get("term_end");
                        }
                        if ("1".equals(term)) {
                            String format1 = simpleDateFormat.format(termBegin);
                            String format2 = simpleDateFormat.format(termEnd);
                            t.put("term_month", format1 + "-" + format2);
                        } else if ("0".equals(term)){
                            if (StringUtils.EMPTY.equals(termMonth)) {
                                t.put("term_month", "业务期次所属月份有误！请重新编辑！");
                            } else {
                                //把整月的给放到比较的k—v中
                                t.put("term_month_compare", termMonth);
                                termMonth = termMonth.replace("-", "年");
                                termMonth = termMonth + "月";
                                t.put("term_month", termMonth);
                            }
                        } else {
                            t.put("term_month", "日期错误！");
                        }
                    }
                }
        );
        //todo 财务项目管理四期修改
        if (list.size() != 0) {
            list1.addAll(list);
            //最后根据term_month进行排序
            Collections.sort(list1, new Comparator<Map<String, Object>>() {
                @Override
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    return o2.get("term_month_compare").toString().compareTo(o1.get("term_month_compare").toString());
                }
            });
            return list1;
        } else {
            return list;
        }
    }

    @Override
    public Map<String, Object> selectCwProjectConductLawDetail(CwProjectIncome cwProjectIncome) {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> cwMap = new HashMap<>();
        Map<String, Object> sum = new HashMap<>();
        List<Map<String, Object>> list = cwProjectCustMapper.selectCwProjectCounductLawDatailByIncomeId(cwProjectIncome.getId());
        //todo 财务项目管理四期，找出返费和返费公司
        Map<String, Object> custInfo = cwProjectCustMapper.selectLawCwProjectCustByPhaseId(cwProjectIncome.getId());
        if (list.size() == 0) {
            //该项目还没开始
            map.put("status", "录入收入与返费");
            cwMap.put("id", cwProjectIncome.getId());
            cwMap.put("incomeAmt", "-");
            cwMap.put("feeList", "-");
            cwMap.put("grossProfitAmt", "-");
            cwMap.put("grossProfitAmt2", "-");
            sum.put("incomeAmtSum", "-");
            sum.put("feeAmtSum", "-");
            sum.put("feeAmt2Sum", "-");
            map.put("detail", cwMap);
            map.put("sum", sum);
        } else {
            list.forEach(t -> {
                String phaseStatus = (String) t.get("phase_status");
                String dynamicTime = t.get("dynamic_time").toString();
                t.put("dynamic_time", dynamicTime);
                if ("6".equals(phaseStatus)) {
                    //已录入收入的信息
                    t.put("phase_status", "录入收入与返费");
                    map.put("status0", t);
                } else if ("7".equals(phaseStatus)) {
                    //已确认收入的信息
                    t.put("phase_status", "已完成");
                    map.put("status1", t);
                }
            });
        }
        //找期次的收入、毛利、提成毛利
        CwProjectIncome cwIncome = cwProjectIncomeMapper.selectCwProjectIncomeById(cwProjectIncome.getId());
        //这里的收入是总收入。
        BigDecimal incomeAmtSum = null;
        BigDecimal grossProfitAmt = null;
        BigDecimal grossProfitAmt2 = null;
        if (Optional.ofNullable(cwIncome).isPresent()) {
            if (Optional.ofNullable(cwIncome.getIncomeAmt()).isPresent()) {
                incomeAmtSum = cwIncome.getIncomeAmt();
            }
            if (Optional.ofNullable(cwIncome.getGrossProfitAmt()).isPresent()) {
                grossProfitAmt = cwIncome.getGrossProfitAmt();
            }
            if (Optional.ofNullable(cwIncome.getGrossProfitAmt2()).isPresent()) {
                grossProfitAmt2 = cwIncome.getGrossProfitAmt2();
            }
        }
        //根据期次id找对应的返费
        List<CwProjectIncomeAndFeeForLawDto> cwProjectFees = cwProjectFeeMapper.selectLawCwProjectCustFeeListDetailByIncomeId2(cwIncome.getId());
        //对查到的返费进行打款的判断，这里的返费id用来后续打款，所以在这里加上判断
        Map<Integer, List<CwProjectIncomeAndFeeForLawDto>> collect = cwProjectFees.stream().collect(Collectors.groupingBy(CwProjectIncomeAndFeeForLawDto::getServiceGroupFlag));
        //按照分组标识已经分好组了。然后判断分组的大小，如果是1，说明该返费是只有一个一级服务商。
        for (Map.Entry<Integer, List<CwProjectIncomeAndFeeForLawDto>> obj:collect.entrySet()) {
            List<CwProjectIncomeAndFeeForLawDto> dtoList = obj.getValue();
            if (dtoList.size() == 1) {
                if (dtoList.get(0).getFeeRound().compareTo(BigDecimal.ZERO) > 0) {
                    dtoList.get(0).setPayFlag("9");
                } else if (dtoList.get(0).getFeeRound().compareTo(BigDecimal.ZERO) == 0) {
                    //说明返费取整为0，即不用打款
                    dtoList.get(0).setPayFlag("10");
                }
            } else {
                for (CwProjectIncomeAndFeeForLawDto dto:dtoList) {
                    //todo 财务项目管理四期，一级服务商下有二级服务商时，是否展示挂起金额
                    if (dto.getIncomeAmt() != null) {
                        dto.setServiceProviderShowFlag("99");
                    }
                    if (dto.getServiceProviderSecond() != null && dto.getFeeRound().compareTo(BigDecimal.ZERO) > 0) {
                        dto.setPayFlag("9");
                    } else if (dto.getServiceProviderSecond() != null && dto.getFeeRound().compareTo(BigDecimal.ZERO) == 0) {
                        dto.setPayFlag("10");
                    }
                }
            }
        }
        for (CwProjectIncomeAndFeeForLawDto incomeAndFeeForLawDto:cwProjectFees) {
            if ("2".equals(incomeAndFeeForLawDto.getServiceProviderFlag())) {
                //todo 财务项目管理四期，因为法催项目服务商中，一级服务商和二级服务商合在一列显示。所以前面给 5 个空格，用来做前端渲染的判断
                incomeAndFeeForLawDto.setServiceProvider("     " + incomeAndFeeForLawDto.getServiceProviderSecond());
                incomeAndFeeForLawDto.setIncomeAmt(incomeAndFeeForLawDto.getServiceProviderSecondIncome());
            }
        }
        if (grossProfitAmt != null) {
            cwProjectFees.get(0).setGrossProfitAmt(grossProfitAmt);
        }
        if (grossProfitAmt2 != null) {
            cwProjectFees.get(0).setGrossProfitAmt2(grossProfitAmt2);
        }
        List<Map<String, Object>> feeList = new ArrayList<>();
        //返费金额合计
        BigDecimal feeAmtSum = cwProjectFees.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectIncomeAndFeeForLawDto::getFeeAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        //提成返费金额合计
        BigDecimal feeAmt2Sum = cwProjectFees.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectIncomeAndFeeForLawDto::getFeeAmt2).reduce(BigDecimal.ZERO, BigDecimal::add);
        //返费取整合计
        BigDecimal feeRoundSum = cwProjectFees.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectIncomeAndFeeForLawDto::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
        //todo 财务项目管理四期 本期返费合计
        BigDecimal currentFeeSum = cwProjectFees.stream().filter(t -> "1".equals(t.getServiceProviderFlag())).map(CwProjectIncomeAndFeeForLawDto::getCurrentFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        //todo 财务项目管理四期 借条分润合计
        BigDecimal jtfrAmtSum = cwProjectFees.stream().filter(t -> "1".equals(t.getServiceProviderFlag()) && t.getJtfrAmt() != null).map(CwProjectIncomeAndFeeForLawDto::getJtfrAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        //todo 财务项目管理四期 法催利润合计
        BigDecimal lawProfitSum = cwProjectFees.stream().filter(t -> "1".equals(t.getServiceProviderFlag()) && t.getLawProfit() != null).map(CwProjectIncomeAndFeeForLawDto::getLawProfit).reduce(BigDecimal.ZERO, BigDecimal::add);

        //至此，所有信息都已经拿到，整合一下返回
        cwMap.put("id", cwIncome.getId());
        if (incomeAmtSum != null) {
            cwMap.put("incomeAmt", incomeAmtSum);
        } else {
            cwMap.put("incomeAmt", "-");
        }
        if (cwIncome.getCollectionTime() != null) {
            cwMap.put("collectionTime", DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM, cwIncome.getCollectionTime())));
            cwMap.put("collectionTimeString", DateUtils.parseDateToStr("yyyy年MM月", cwIncome.getCollectionTime()));
        } else {
            cwMap.put("collectionTime", null);
            cwMap.put("collectionTimeString", null);
        }
        cwMap.put("feeList", cwProjectFees);
        sum.put("incomeAmtSum", incomeAmtSum);
        sum.put("feeAmtSum", feeAmtSum);
        sum.put("feeAmt2Sum", feeAmt2Sum);
        sum.put("feeRoundSum", feeRoundSum);
        sum.put("currentFeeSum", currentFeeSum);
        sum.put("jtfrAmtSum", jtfrAmtSum);
        sum.put("lawProfitSum", lawProfitSum);
        map.put("detail", cwMap);
        map.put("sum", sum);
        Map<String,Object> objectMap = cwProjectCustMapper.getPhaseStatus(cwProjectIncome.getId());
        map.put("phaseStatus",objectMap.get("phaseStatus"));
        map.put("remark",objectMap.get("remark"));
        if (null != custInfo) {
            if ("1".equals(custInfo.get("replaceFlag").toString())) {
                Long custId = (Long) custInfo.get("custId");
                //找最新的名字
                String custName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
                map.put("custName", custName);
            } else {
                map.put("custName", custInfo.get("custName"));
            }
            map.put("feeCustName", custInfo.get("feeCustName"));
        } else {
            map.put("feeCustName", "-");
            map.put("custName", "-");
        }
        return map;
    }

    @Override
    public Map<String, Object> selectCwprojectPayListLawDetail(List<CwProjectLawFeeDto> cwProjectFeeList) {
        Map<String,Object> resultMap = new HashMap<>();
        List<Map<String,Object>> objList = new ArrayList<>();
        AtomicInteger i = new AtomicInteger();
        cwProjectFeeList.forEach(t -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", t.getId());
            map.put("projectIncomeId", t.getProjectIncomeId());
            map.put("custName", t.getCustName());
            map.put("feeCustName", t.getFeeCustName());
            map.put("feeAmt", t.getFeeAmt());
            map.put("feeRound", t.getFeeRound());
            map.put("serviceProviderFlag", t.getServiceProviderFlag());
            map.put("serviceProvider", t.getServiceProvider());
            map.put("serviceProviderSecond", t.getServiceProviderSecond());
            AtomicReference<BigDecimal> payAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
            AtomicReference<BigDecimal> differenceAmtSum = new AtomicReference<>(new BigDecimal("0.00"));
            List<Map<String, Object>> payList = new ArrayList<>();
            cwProjectPayMapper.selectCwProjectPayListDetailByFeeId2(t.getId()).forEach(c -> {
                if (Optional.ofNullable(c).isPresent()) {
                    //不为空
                    payList.add(c);
                    BigDecimal payAmt = (BigDecimal) c.get("payAmt");
                    BigDecimal differenceAmt = (BigDecimal) c.get("differenceAmt");
                    if (Optional.ofNullable(payAmt).isPresent()) {
                        //不为空
                        payAmtSum.set(payAmtSum.get().add(payAmt));
                        differenceAmtSum.set(differenceAmtSum.get().add(differenceAmt));
                    }
                }
            });
            BigDecimal result = payAmtSum.get().add(differenceAmtSum.get());
            if (result.compareTo(BigDecimal.ZERO) == 0) {
                //没有打款记录，说明该公司的返费一点也没支付，就是未开始
                if ("9".equals(t.getPayFlag())) {
                    map.put("payStatus", "未开始");
                    map.put("payAlreadySum", "0.00");
                    map.put("payNoAlreadySum", t.getFeeRound());
                    map.put("payButton", "true");
                    map.put("changeButton", "false");
                    i.getAndIncrement();
                } else if ("10".equals(t.getPayFlag())) {
                    map.put("payStatus", "不需打款");
                    map.put("payAlreadySum", "");
                    map.put("payNoAlreadySum", "");
                    map.put("payButton", "false");
                    map.put("changeButton", "false");
                }
            } else if (result.compareTo(BigDecimal.ZERO) > 0
                    && result.compareTo(t.getFeeRound()) == -1) {
                if ("9".equals(t.getPayFlag())) {
                    map.put("payStatus", "部分打款");
                    map.put("payAlreadySum", result);
                    map.put("payNoAlreadySum", t.getFeeRound().subtract(result));
                    map.put("payButton", "true");
                    map.put("changeButton", "false");
                    i.getAndIncrement();
                } else if ("10".equals(t.getPayFlag())) {
                    map.put("payStatus", "不需打款");
                    map.put("payAlreadySum", "");
                    map.put("payNoAlreadySum", "");
                    map.put("payButton", "false");
                    map.put("changeButton", "false");
                }
            } else if (result.compareTo(t.getFeeRound()) == 0) {
                //每次查到的打款金额放在这里
                if ("9".equals(t.getPayFlag())) {
                    map.put("payStatus", "已完成");
                    map.put("payAlreadySum", result);
                    map.put("payNoAlreadySum", "0.00");
                    map.put("payButton", "false");
                    map.put("changeButton", "true");
                } else if ("10".equals(t.getPayFlag())) {
                    map.put("payStatus", "不需打款");
                    map.put("payAlreadySum", "");
                    map.put("payNoAlreadySum", "");
                    map.put("payButton", "false");
                    map.put("changeButton", "false");
                }
            }
            if (0 != payList.size()) {
                map.put("payList", payList);
            } else {
                Map<String, Object> nullMap = new HashMap<>();
                nullMap.put("payId", "");
                nullMap.put("payAmt", "");
                nullMap.put("differenceAmt", "");
                nullMap.put("payDate", "");
                payList.add(nullMap);
                map.put("payList", payList);
            }
            objList.add(map);
        });
        resultMap.put("result", objList);
        if (0 == i.get()) {
            //说明没有未开始和部分打款的
            resultMap.put("button", "true");
        } else {
            resultMap.put("button", "false");
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> findLawRecentlyCustFeeCompanyAndFeeCompany(Long projectId, String termMonth) {
        String date = StrUtil.sub(termMonth, 0, 4) + "-" + StrUtil.sub(termMonth, 5, 7);
        Map<String, Object> obj;
        Map<String, Object> zhengyueObj = cwProjectIncomeMapper.findLawRecentlyPhaseIdByProjectIdAndTremMonth(projectId, date);
        Map<String, Object> recentlyCustFeeCompanyAndFeeCompany1 = null;
        Map<String, Object> recentlyCustFeeCompanyAndFeeCompany2 = null;
        long termMonth1 = 0L;
        long termMonth2 = 0L;
        if (zhengyueObj != null) {
            recentlyCustFeeCompanyAndFeeCompany1 = cwProjectIncomeMapper.findRecentlyCustFeeCompanyAndFeeCompany1((Long) zhengyueObj.get("id"));
            termMonth1 = DateUtils.parseDate(zhengyueObj.get("termMonth")).getTime();
        }
        Map<String, Object> feizhengyueObj = cwProjectIncomeMapper.findLawRecentlyPhaseIdByProjectIdAndTremMonthTermOne(projectId, date);
        if (feizhengyueObj != null) {
            recentlyCustFeeCompanyAndFeeCompany2 = cwProjectIncomeMapper.findRecentlyCustFeeCompanyAndFeeCompany1((Long) feizhengyueObj.get("id"));
            termMonth2 = DateUtils.parseDate(feizhengyueObj.get("termMonth")).getTime();
        }
        if (zhengyueObj == null && feizhengyueObj == null) {
            return null;
        } else {
            if (termMonth1 > termMonth2) {
                obj = recentlyCustFeeCompanyAndFeeCompany1;
            } else {
                obj = recentlyCustFeeCompanyAndFeeCompany2;
            }
        }
        return obj;
    }

    @Override
    public List<Map<String, Object>> findLawSuspendFlagIsOneSum(List<String> serviceProviderNameList) {
        return cwProjectIncomeMapper.selectLawSuspendFlagIsOneSumByServiceProviderName(serviceProviderNameList);
    }

    @Override
    public Map<String, Object> selectFeeNoAlreadyQueryDetailByQueryTime(String queryTime, LoginUser loginUser, String projectType) {
        //给一个响应的map，里面有info，list，sum
        Map<String, Object> returnMap = new HashMap<>();
        BigDecimal sum = null;
        //首先，判断用户的身份
        List<SysRole> roles = loginUser.getUser().getRoles();
        boolean present = roles.stream().anyMatch(t -> "admin".equals(t.getRoleKey()) || "caiwuAdmin".equals(t.getRoleKey()) || "yewuAdmin".equals(t.getRoleKey()));
        if (StringUtils.isEmpty(queryTime)) {
            Map<String, Object> mm = new HashMap<>();
            mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NO_SELECT_PARAM.getCode());
            mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NO_SELECT_PARAM.getMsg());
            returnMap.put("info", mm);
            returnMap.put("projectTypeFlag", "99999");
            return returnMap;
        } else {
            if ("all".equals(projectType)) {
                projectType = StrUtil.EMPTY;
            }
            sum = BigDecimal.ZERO;
            if (present) {
                //可以查所有项目    ->     项目维度又分为两种：普通项目和法催项目
                //查询所有正常状态的项目，包含了普通和法催。
                List<CwProjectFeeNoAlreadyVo> resultList = new ArrayList<>();
                List<CwProjectFeeNoAlreadyVo> resultListForLaw = new ArrayList<>();
                //普通项目
                List<CwProjectFeeNoAlreadyVo> cwProjectFeeNoAlreadyVos = cwProjectIncomeMapper.selectFeeNoAlreadyQueryDetailByQueryTime(queryTime, projectType);
                if (StrUtil.EMPTY.equals(projectType) || "0".equals(projectType) || "2".equals(projectType)|| "3".equals(projectType)) {
                    Map<Long, List<CwProjectFeeNoAlreadyVo>> collect = cwProjectFeeNoAlreadyVos.stream().filter(t -> t.getUnfeeAmtSum() != null).collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectIncomeId));
                    for (Map.Entry<Long, List<CwProjectFeeNoAlreadyVo>> obj:collect.entrySet()) {
                        if (obj.getValue().size() != 0) {
                            BigDecimal unfeeAmtSum = obj.getValue().get(0).getUnfeeAmtSum();
                            sum = sum.add(unfeeAmtSum);
                        }
                    }
                } else {
                    sum = BigDecimal.ZERO;
                }
                Map<Long, List<CwProjectFeeNoAlreadyVo>> normalMap = cwProjectFeeNoAlreadyVos.stream().collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectId));
                normalMap.forEach((key, value) -> {
                    //对每一个项目的不同期次排序。
                    List<CwProjectFeeNoAlreadyVo> collect = value.stream().sorted(Comparator.comparing(CwProjectFeeNoAlreadyVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                    resultList.addAll(collect);
                });
                //法催项目
                List<CwProjectFeeNoAlreadyVo> cwProjectLawFeeNoAlreadyVos = cwProjectIncomeMapper.selectLawFeeNoAlreadyQueryDetailByQueryTime(queryTime);
                BigDecimal lawSum = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getUnfeeAmtSum() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectFeeNoAlreadyVo::getUnfeeAmtSum, BigDecimal::add));
                if (StrUtil.EMPTY.equals(projectType) || "1".equals(projectType)) {
                    sum = sum.add(lawSum);
                }
                //找法催项目的期次。
                Map<Long, List<CwProjectFeeNoAlreadyVo>> collectMap = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getPhaseId() == null && t.getUnfeeAmtSum() != null && t.getUnfeeAmtSum().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectIncomeId));
                //空集合用来装处理好的法催期次
                List<CwProjectFeeNoAlreadyVo> list = new ArrayList<>();
                collectMap.forEach((key, value) -> {
                    //todo 先去找有打款记录期次id
                    List<Long> collect1 = value.stream().filter(t -> t.getFeeAmtSum() != null && t.getFeeAmtSum().compareTo(BigDecimal.ZERO) > 0).map(CwProjectFeeNoAlreadyVo::getProjectIncomeId).collect(Collectors.toList());
                    List<CwProjectFeeNoAlreadyVo> collect = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getPhaseId() != null && t.getPhaseId().equals(key)).collect(Collectors.toList());
                    //todo 然后对返费取整求和   法催利润求和
                    BigDecimal feeRound = collect.stream().map(CwProjectFeeNoAlreadyVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal lawProfit = collect.stream().map(CwProjectFeeNoAlreadyVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //todo 再然后，去根据期次id去查对应的打款信息
                    if (collect1.size() != 0) {
                        for (Long phaseId:collect1) {
                            List<Map<String, Object>> payInfo = cwProjectIncomeMapper.selectCwprojectLawPayInfoByPhaseId(phaseId);
                            CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo = value.get(0);
                            for (int i = 0; i < payInfo.size(); i++) {
                                CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo1 = new CwProjectFeeNoAlreadyVo();
                                cwProjectFeeNoAlreadyVo1.setProjectId((Long) payInfo.get(i).get("projectId"));
                                cwProjectFeeNoAlreadyVo1.setPhaseId((Long) payInfo.get(i).get("phaseId"));
                                cwProjectFeeNoAlreadyVo1.setProjectIncomeId((Long) payInfo.get(i).get("projectIncomeId"));
                                cwProjectFeeNoAlreadyVo1.setProjectFeeId((Long) payInfo.get(i).get("projectFeeId"));
                                cwProjectFeeNoAlreadyVo1.setProjectCustId((Long) payInfo.get(i).get("custId"));
                                cwProjectFeeNoAlreadyVo1.setProjectName(cwProjectFeeNoAlreadyVo.getProjectName());
                                cwProjectFeeNoAlreadyVo1.setCustName((String) payInfo.get(i).get("custName"));
                                cwProjectFeeNoAlreadyVo1.setFeeCustName((String) payInfo.get(i).get("feeCustName"));
                                cwProjectFeeNoAlreadyVo1.setTermMonthCompare(cwProjectFeeNoAlreadyVo.getTermMonthCompare());
                                cwProjectFeeNoAlreadyVo1.setFeeNoAlreadyTerm(cwProjectFeeNoAlreadyVo.getFeeNoAlreadyTerm());
                                cwProjectFeeNoAlreadyVo1.setIncomeAmt(cwProjectFeeNoAlreadyVo.getIncomeAmt());
                                cwProjectFeeNoAlreadyVo1.setFeeAmtSum(cwProjectFeeNoAlreadyVo.getFeeAmtSum());
                                cwProjectFeeNoAlreadyVo1.setUnfeeAmtSum(cwProjectFeeNoAlreadyVo.getUnfeeAmtSum());
                                cwProjectFeeNoAlreadyVo1.setRemark(cwProjectFeeNoAlreadyVo.getRemark());
                                cwProjectFeeNoAlreadyVo1.setPayDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, (Date) payInfo.get(i).get("payDate")));
                                cwProjectFeeNoAlreadyVo1.setPayAmt((BigDecimal) payInfo.get(i).get("payAmt"));
                                cwProjectFeeNoAlreadyVo1.setDifferenceAmt((BigDecimal) payInfo.get(i).get("differenceAmt"));
                                cwProjectFeeNoAlreadyVo1.setProjectType(cwProjectFeeNoAlreadyVo.getProjectType());
                                cwProjectFeeNoAlreadyVo1.setFeeRound(feeRound);
                                cwProjectFeeNoAlreadyVo1.setLawProfit(lawProfit);
                                list.add(cwProjectFeeNoAlreadyVo1);
                            }
                        }
                    } else {
                        BigDecimal feeRound1 = collect.stream().map(CwProjectFeeNoAlreadyVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal lawProfit1 = collect.stream().map(CwProjectFeeNoAlreadyVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                        CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo = value.get(0);
                        cwProjectFeeNoAlreadyVo.setPhaseId(cwProjectFeeNoAlreadyVo.getProjectIncomeId());
                        String custName = collect.get(0).getCustName();
                        String feeCustName = collect.get(0).getFeeCustName();
                        collect.clear();
                        cwProjectFeeNoAlreadyVo.setCustName(custName);
                        cwProjectFeeNoAlreadyVo.setFeeCustName(feeCustName);
                        cwProjectFeeNoAlreadyVo.setFeeRound(feeRound1);
                        cwProjectFeeNoAlreadyVo.setLawProfit(lawProfit1);
                        collect.add(cwProjectFeeNoAlreadyVo);
                        list.add(cwProjectFeeNoAlreadyVo);
                    }
                });
                List<CwProjectFeeNoAlreadyVo> list1 = list.stream().filter(t -> t.getCustName() != null && t.getFeeCustName() != null).collect(Collectors.toList());
                Map<Long, List<CwProjectFeeNoAlreadyVo>> collect = list1.stream().collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectId));
                collect.forEach((key, value) -> {
                    //对每一个项目的不同期次排序。
                    List<CwProjectFeeNoAlreadyVo> collect1 = value.stream().sorted(Comparator.comparing(CwProjectFeeNoAlreadyVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                    resultListForLaw.addAll(collect1);
                });
                if (resultList.size() == 0 && resultListForLaw.size() == 0) {
                    Map<String, Object> mm = new HashMap<>();
                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
                    returnMap.put("info", mm);
                    returnMap.put("projectTypeFlag", "99999");
                    return returnMap;
                } else {
                    Map<String, Object> mm = new HashMap<>();
                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
                    //todo 判断查询的项目类型
                    if (StrUtil.EMPTY.equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("lawList", resultListForLaw);
                        returnMap.put("projectTypeFlag", "all");
                    } else if ("0".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "0");
                    } else if ("2".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "2");
                    } else if ("1".equals(projectType)) {
                        returnMap.put("lawList", resultListForLaw);
                        returnMap.put("projectTypeFlag", "1");
                    } else if ("3".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "3");
                    }
                    returnMap.put("info", mm);
                    returnMap.put("sum", sum);
                    return returnMap;
                }
            } else {
                List<CwProjectFeeNoAlreadyVo> resultList = new ArrayList<>();
                List<CwProjectFeeNoAlreadyVo> resultListForLaw = new ArrayList<>();
                //先找普通项目
                List<CwProjectFeeNoAlreadyVo> cwProjectFeeNoAlreadyVos = cwProjectIncomeMapper.selectFeeNoAlreadyQueryDetailByQueryTimeAndUserId(queryTime, loginUser.getUserId(), projectType);
                if (StrUtil.EMPTY.equals(projectType) || "0".equals(projectType) || "2".equals(projectType) || "3".equals(projectType)) {
                    Map<Long, List<CwProjectFeeNoAlreadyVo>> collect = cwProjectFeeNoAlreadyVos.stream().filter(t -> t.getUnfeeAmtSum() != null).collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectIncomeId));
                    for (Map.Entry<Long, List<CwProjectFeeNoAlreadyVo>> obj:collect.entrySet()) {
                        if (obj.getValue().size() != 0) {
                            BigDecimal unfeeAmtSum = obj.getValue().get(0).getUnfeeAmtSum();
                            sum = sum.add(unfeeAmtSum);
                        }
                    }
                } else {
                    sum = BigDecimal.ZERO;
                }
                Map<Long, List<CwProjectFeeNoAlreadyVo>> normalMap = cwProjectFeeNoAlreadyVos.stream().collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectId));
                normalMap.forEach((key, value) -> {
                    //对每一个项目的不同期次排序。
                    List<CwProjectFeeNoAlreadyVo> collect = value.stream().sorted(Comparator.comparing(CwProjectFeeNoAlreadyVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                    resultList.addAll(collect);
                });
                //再找法催项目
                List<CwProjectFeeNoAlreadyVo> cwProjectLawFeeNoAlreadyVos = cwProjectIncomeMapper.selectLawFeeNoAlreadyQueryDetailByQueryTimeAndUserId(queryTime, loginUser.getUserId());
                BigDecimal lawSum = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getUnfeeAmtSum() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectFeeNoAlreadyVo::getUnfeeAmtSum, BigDecimal::add));
                if (StrUtil.EMPTY.equals(projectType) || "1".equals(projectType)) {
                    sum = sum.add(lawSum);
                }
                //找法催项目的期次。
                Map<Long, List<CwProjectFeeNoAlreadyVo>> collectMap = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getPhaseId() == null && t.getUnfeeAmtSum() != null && t.getUnfeeAmtSum().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectIncomeId));
                //空集合用来装处理好的法催期次
                List<CwProjectFeeNoAlreadyVo> list = new ArrayList<>();
                collectMap.forEach((key, value) -> {
                    //todo 先去找有打款记录期次id
                    List<Long> collect1 = value.stream().filter(t -> t.getFeeAmtSum() != null && t.getFeeAmtSum().compareTo(BigDecimal.ZERO) > 0).map(CwProjectFeeNoAlreadyVo::getProjectIncomeId).collect(Collectors.toList());
                    List<CwProjectFeeNoAlreadyVo> collect = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getPhaseId() != null && t.getPhaseId().equals(key)).collect(Collectors.toList());
                    //todo 然后对返费取整求和   法催利润求和
                    BigDecimal feeRound = collect.stream().map(CwProjectFeeNoAlreadyVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal lawProfit = collect.stream().map(CwProjectFeeNoAlreadyVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //todo 再然后，去根据期次id去查对应的打款信息
                    if (collect1.size() != 0) {
                        for (Long phaseId:collect1) {
                            List<Map<String, Object>> payInfo = cwProjectIncomeMapper.selectCwprojectLawPayInfoByPhaseId(phaseId);
                            CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo = value.get(0);
                            for (int i = 0; i < payInfo.size(); i++) {
                                CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo1 = new CwProjectFeeNoAlreadyVo();
                                cwProjectFeeNoAlreadyVo1.setProjectId((Long) payInfo.get(i).get("projectId"));
                                cwProjectFeeNoAlreadyVo1.setPhaseId((Long) payInfo.get(i).get("phaseId"));
                                cwProjectFeeNoAlreadyVo1.setProjectIncomeId((Long) payInfo.get(i).get("projectIncomeId"));
                                cwProjectFeeNoAlreadyVo1.setProjectFeeId((Long) payInfo.get(i).get("projectFeeId"));
                                cwProjectFeeNoAlreadyVo1.setProjectCustId((Long) payInfo.get(i).get("custId"));
                                cwProjectFeeNoAlreadyVo1.setProjectName(cwProjectFeeNoAlreadyVo.getProjectName());
                                cwProjectFeeNoAlreadyVo1.setCustName((String) payInfo.get(i).get("custName"));
                                cwProjectFeeNoAlreadyVo1.setFeeCustName((String) payInfo.get(i).get("feeCustName"));
                                cwProjectFeeNoAlreadyVo1.setTermMonthCompare(cwProjectFeeNoAlreadyVo.getTermMonthCompare());
                                cwProjectFeeNoAlreadyVo1.setFeeNoAlreadyTerm(cwProjectFeeNoAlreadyVo.getFeeNoAlreadyTerm());
                                cwProjectFeeNoAlreadyVo1.setIncomeAmt(cwProjectFeeNoAlreadyVo.getIncomeAmt());
                                cwProjectFeeNoAlreadyVo1.setFeeAmtSum(cwProjectFeeNoAlreadyVo.getFeeAmtSum());
                                cwProjectFeeNoAlreadyVo1.setUnfeeAmtSum(cwProjectFeeNoAlreadyVo.getUnfeeAmtSum());
                                cwProjectFeeNoAlreadyVo1.setRemark(cwProjectFeeNoAlreadyVo.getRemark());
                                cwProjectFeeNoAlreadyVo1.setPayDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, (Date) payInfo.get(i).get("payDate")));
                                cwProjectFeeNoAlreadyVo1.setPayAmt((BigDecimal) payInfo.get(i).get("payAmt"));
                                cwProjectFeeNoAlreadyVo1.setDifferenceAmt((BigDecimal) payInfo.get(i).get("differenceAmt"));
                                cwProjectFeeNoAlreadyVo1.setProjectType(cwProjectFeeNoAlreadyVo.getProjectType());
                                cwProjectFeeNoAlreadyVo1.setFeeRound(feeRound);
                                cwProjectFeeNoAlreadyVo1.setLawProfit(lawProfit);
                                list.add(cwProjectFeeNoAlreadyVo1);
                            }
                        }
                    } else {
                        BigDecimal feeRound1 = collect.stream().map(CwProjectFeeNoAlreadyVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal lawProfit1 = collect.stream().map(CwProjectFeeNoAlreadyVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                        CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo = value.get(0);
                        cwProjectFeeNoAlreadyVo.setPhaseId(cwProjectFeeNoAlreadyVo.getProjectIncomeId());
                        String custName = collect.get(0).getCustName();
                        String feeCustName = collect.get(0).getFeeCustName();
                        collect.clear();
                        cwProjectFeeNoAlreadyVo.setCustName(custName);
                        cwProjectFeeNoAlreadyVo.setFeeCustName(feeCustName);
                        cwProjectFeeNoAlreadyVo.setFeeRound(feeRound1);
                        cwProjectFeeNoAlreadyVo.setLawProfit(lawProfit1);
                        collect.add(cwProjectFeeNoAlreadyVo);
                        list.add(cwProjectFeeNoAlreadyVo);
                    }
                });
                List<CwProjectFeeNoAlreadyVo> list1 = list.stream().filter(t -> t.getCustName() != null && t.getFeeCustName() != null).collect(Collectors.toList());
                Map<Long, List<CwProjectFeeNoAlreadyVo>> collect = list1.stream().collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectId));
                collect.forEach((key, value) -> {
                    //对每一个项目的不同期次排序。
                    List<CwProjectFeeNoAlreadyVo> collect1 = value.stream().sorted(Comparator.comparing(CwProjectFeeNoAlreadyVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                    resultList.addAll(collect1);
                    resultListForLaw.addAll(collect1);
                });
                if (resultList.size() == 0 && resultListForLaw.size() == 0) {
                    Map<String, Object> mm = new HashMap<>();
                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
                    returnMap.put("info", mm);
                    returnMap.put("projectTypeFlag", "99999");
                    return returnMap;
                } else {
                    Map<String, Object> mm = new HashMap<>();
                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
                    //todo 判断查询的项目类型
                    if (StrUtil.EMPTY.equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("lawList", resultListForLaw);
                        returnMap.put("projectTypeFlag", "all");
                    } else if ("0".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "0");
                    } else if ("2".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "2");
                    } else if ("1".equals(projectType)) {
                        returnMap.put("lawList", resultListForLaw);
                        returnMap.put("projectTypeFlag", "1");
                    } else if ("3".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "3");
                    }
                    returnMap.put("info", mm);
                    returnMap.put("sum", sum);
                    return returnMap;
                }
            }
        }
    }

    @Override
    public Map<String, Object> selectPayDateQueryDetailQueryTime(String queryStartTime, String queryEndTime, LoginUser loginUser, String projectType) {
        //给一个响应的map，里面有info，list，payAmtSum，differenceAmtSum
        Map<String, Object> returnMap = new HashMap<>();
        BigDecimal payAmtSum = null;
        BigDecimal differenceAmtSum = null;
        //首先，判断用户的身份
        List<SysRole> roles = loginUser.getUser().getRoles();
        boolean present = roles.stream().anyMatch(t -> "admin".equals(t.getRoleKey()) || "caiwuAdmin".equals(t.getRoleKey()) || "yewuAdmin".equals(t.getRoleKey()));
        if (StringUtils.isEmpty(queryStartTime) || StringUtils.isEmpty(queryEndTime)) {
            Map<String, Object> mm = new HashMap<>();
            mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NO_SELECT_PARAM.getCode());
            mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NO_SELECT_PARAM.getMsg());
            returnMap.put("info", mm);
            returnMap.put("projectTypeFlag", "99999");
            return returnMap;
        } else {
            if ("all".equals(projectType)) {
                projectType = StrUtil.EMPTY;
            }
            payAmtSum = BigDecimal.ZERO;
            differenceAmtSum = BigDecimal.ZERO;
            if (present) {
                //可以查所有项目    ->     项目维度又分为两种：普通项目和法催项目
                //查询所有正常状态的项目，包含了普通和法催。
                List<CwProjectPayDateVo> resultList = new ArrayList<>();
                List<CwProjectPayDateVo> resultListForLaw = new ArrayList<>();
                //普通项目
                List<CwProjectPayDateVo> cwProjectPayDateVos = cwProjectIncomeMapper.selectPayDateQueryDetailByQueryTime(queryStartTime, queryEndTime, projectType);
                if (StrUtil.EMPTY.equals(projectType) || "0".equals(projectType) || "2".equals(projectType) || "3".equals(projectType)) {
                    payAmtSum = cwProjectPayDateVos.stream().filter(t -> t.getPayAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getPayAmt, BigDecimal::add));
                    differenceAmtSum = cwProjectPayDateVos.stream().filter(t -> t.getDifferenceAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getDifferenceAmt, BigDecimal::add));
                }
                Map<Long, List<CwProjectPayDateVo>> normalMap = cwProjectPayDateVos.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId));
                normalMap.forEach((key, value) -> {
                    //对每一个项目的不同期次排序。
                    List<CwProjectPayDateVo> collect = value.stream().sorted(Comparator.comparing(CwProjectPayDateVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                    resultList.addAll(collect);
                });
                //法催项目
                List<CwProjectPayDateVo> cwProjectLawFeePayDateVos = cwProjectIncomeMapper.selectLawPayDateQueryDetailByQueryTime(queryStartTime, queryEndTime);
                BigDecimal lawPayAmtSum = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPayAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getPayAmt, BigDecimal::add));
                BigDecimal lawDifferenceAmtSum = cwProjectLawFeePayDateVos.stream().filter(t -> t.getDifferenceAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getDifferenceAmt, BigDecimal::add));
                if (StrUtil.EMPTY.equals(projectType) || "1".equals(projectType)) {
                    payAmtSum = payAmtSum.add(lawPayAmtSum);
                    differenceAmtSum = differenceAmtSum.add(lawDifferenceAmtSum);
                }
                //找法催项目的期次。
                Map<Long, List<CwProjectPayDateVo>> collectMap = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() == null).collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectIncomeId));
                //空集合用来装处理好的法催期次
                List<CwProjectPayDateVo> list = new ArrayList<>();
                collectMap.forEach((key, value) -> {
                    //todo 先去找有打款记录期次id
                    List<Long> collect1 = value.stream().filter(t -> t.getFeeAmtSum() != null && t.getFeeAmtSum().compareTo(BigDecimal.ZERO) > 0).map(CwProjectPayDateVo::getProjectIncomeId).collect(Collectors.toList());
                    List<CwProjectPayDateVo> collect = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() != null && t.getPhaseId().equals(key)).collect(Collectors.toList());
                    //todo 然后对返费取整求和   法催利润求和
                    BigDecimal feeRound = collect.stream().map(CwProjectPayDateVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal lawProfit = collect.stream().map(CwProjectPayDateVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //todo 再然后，去根据期次id去查对应的打款信息
                    if (collect1.size() != 0) {
                        for (Long phaseId:collect1) {
                            List<Map<String, Object>> payInfo = cwProjectIncomeMapper.selectCwprojectLawPayInfoByPhaseId(phaseId);
                            CwProjectPayDateVo cwProjectPayDateVo = value.get(0);
                            for (int i = 0; i < payInfo.size(); i++) {
                                CwProjectPayDateVo cwProjectPayDateVo1 = new CwProjectPayDateVo();
                                cwProjectPayDateVo1.setProjectId((Long) payInfo.get(i).get("projectId"));
                                cwProjectPayDateVo1.setPhaseId((Long) payInfo.get(i).get("phaseId"));
                                cwProjectPayDateVo1.setProjectIncomeId((Long) payInfo.get(i).get("projectIncomeId"));
                                cwProjectPayDateVo1.setProjectFeeId((Long) payInfo.get(i).get("projectFeeId"));
                                cwProjectPayDateVo1.setProjectCustId((Long) payInfo.get(i).get("custId"));
                                cwProjectPayDateVo1.setProjectName(cwProjectPayDateVo.getProjectName());
                                cwProjectPayDateVo1.setCustName((String) payInfo.get(i).get("custName"));
                                cwProjectPayDateVo1.setFeeCustName((String) payInfo.get(i).get("feeCustName"));
                                cwProjectPayDateVo1.setTermMonthCompare(cwProjectPayDateVo.getTermMonthCompare());
                                cwProjectPayDateVo1.setTerm(cwProjectPayDateVo.getTerm());
                                cwProjectPayDateVo1.setIncomeAmt(cwProjectPayDateVo.getIncomeAmt());
                                cwProjectPayDateVo1.setFeeAmtSum(cwProjectPayDateVo.getFeeAmtSum());
                                cwProjectPayDateVo1.setUnfeeAmtSum(cwProjectPayDateVo.getUnfeeAmtSum());
                                cwProjectPayDateVo1.setRemark(cwProjectPayDateVo.getRemark());
                                cwProjectPayDateVo1.setPayDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, (Date) payInfo.get(i).get("payDate")));
                                cwProjectPayDateVo1.setPayAmt((BigDecimal) payInfo.get(i).get("payAmt"));
                                cwProjectPayDateVo1.setDifferenceAmt((BigDecimal) payInfo.get(i).get("differenceAmt"));
                                cwProjectPayDateVo1.setProjectType(cwProjectPayDateVo.getProjectType());
                                cwProjectPayDateVo1.setFeeRound(feeRound);
                                cwProjectPayDateVo1.setLawProfit(lawProfit);
                                list.add(cwProjectPayDateVo1);
                            }
                        }
                    } else {
                        BigDecimal feeRound1 = collect.stream().map(CwProjectPayDateVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal lawProfit1 = collect.stream().map(CwProjectPayDateVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                        CwProjectPayDateVo cwProjectFeeNoAlreadyVo = value.get(0);
                        cwProjectFeeNoAlreadyVo.setPhaseId(cwProjectFeeNoAlreadyVo.getProjectIncomeId());
                        String custName = collect.get(0).getCustName();
                        String feeCustName = collect.get(0).getFeeCustName();
                        collect.clear();
                        cwProjectFeeNoAlreadyVo.setCustName(custName);
                        cwProjectFeeNoAlreadyVo.setFeeCustName(feeCustName);
                        cwProjectFeeNoAlreadyVo.setFeeRound(feeRound1);
                        cwProjectFeeNoAlreadyVo.setLawProfit(lawProfit1);
                        collect.add(cwProjectFeeNoAlreadyVo);
                        list.add(cwProjectFeeNoAlreadyVo);
                    }
                });
                List<CwProjectPayDateVo> list1 = list.stream().filter(t -> t.getCustName() != null && t.getFeeCustName() != null).collect(Collectors.toList());
                Map<Long, List<CwProjectPayDateVo>> collect = list1.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId));
                collect.forEach((key, value) -> {
                    //对每一个项目的不同期次排序。
                    List<CwProjectPayDateVo> collect1 = value.stream().sorted(Comparator.comparing(CwProjectPayDateVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                    resultListForLaw.addAll(collect1);
                });
                //通过期次找对应收入
                if (resultList.size() == 0 && resultListForLaw.size() == 0) {
                    Map<String, Object> mm = new HashMap<>();
                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
                    returnMap.put("info", mm);
                    returnMap.put("projectTypeFlag", "99999");
                    return returnMap;
                } else {
                    Map<String, Object> mm = new HashMap<>();
                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
                    //todo 判断查询的项目类型
                    if (StrUtil.EMPTY.equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("lawList", resultListForLaw);
                        returnMap.put("projectTypeFlag", "all");
                    } else if ("0".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "0");
                    } else if ("2".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "2");
                    } else if ("1".equals(projectType)) {
                        returnMap.put("lawList", resultListForLaw);
                        returnMap.put("projectTypeFlag", "1");
                    } else if ("3".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "3");
                    }
                    returnMap.put("info", mm);
                    returnMap.put("payAmtSum", payAmtSum);
                    returnMap.put("differenceAmtSum", differenceAmtSum);
                    return returnMap;
                }
            } else {
                List<CwProjectPayDateVo> resultList = new ArrayList<>();
                List<CwProjectPayDateVo> resultListForLaw = new ArrayList<>();
                //先找普通项目
                List<CwProjectPayDateVo> cwProjectPayDateVos = cwProjectIncomeMapper.selectPayDateQueryDetailByQueryTimeAndUserId(queryStartTime, queryEndTime, loginUser.getUserId(), projectType);
                if (StrUtil.EMPTY.equals(projectType) || "0".equals(projectType) || "2".equals(projectType) || "3".equals(projectType)) {
                    payAmtSum = cwProjectPayDateVos.stream().filter(t -> t.getPayAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getPayAmt, BigDecimal::add));
                    differenceAmtSum = cwProjectPayDateVos.stream().filter(t -> t.getDifferenceAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getDifferenceAmt, BigDecimal::add));
                }
                Map<Long, List<CwProjectPayDateVo>> normalMap = cwProjectPayDateVos.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId));
                normalMap.forEach((key, value) -> {
                    //对每一个项目的不同期次排序。
                    List<CwProjectPayDateVo> collect = value.stream().sorted(Comparator.comparing(CwProjectPayDateVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                    resultList.addAll(collect);
                });
                //再找法催项目
                List<CwProjectPayDateVo> cwProjectLawFeePayDateVos = cwProjectIncomeMapper.selectLawPayDateQueryDetailByQueryTimeAndUserId(queryStartTime, queryEndTime, loginUser.getUserId());
                BigDecimal lawPayAmtSum = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPayAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getPayAmt, BigDecimal::add));
                BigDecimal lawDifferenceAmtSum = cwProjectLawFeePayDateVos.stream().filter(t -> t.getDifferenceAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getDifferenceAmt, BigDecimal::add));
                if (StrUtil.EMPTY.equals(projectType) || "1".equals(projectType)) {
                    payAmtSum = payAmtSum.add(lawPayAmtSum);
                    differenceAmtSum = differenceAmtSum.add(lawDifferenceAmtSum);
                }
                //找法催项目的期次。
                Map<Long, List<CwProjectPayDateVo>> collectMap = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() == null).collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectIncomeId));
                //空集合用来装处理好的法催期次
                List<CwProjectPayDateVo> list = new ArrayList<>();
                collectMap.forEach((key, value) -> {
                    //todo 先去找有打款记录期次id
                    List<Long> collect1 = value.stream().filter(t -> t.getFeeAmtSum() != null && t.getFeeAmtSum().compareTo(BigDecimal.ZERO) > 0).map(CwProjectPayDateVo::getProjectIncomeId).collect(Collectors.toList());
                    List<CwProjectPayDateVo> collect = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() != null && t.getPhaseId().equals(key)).collect(Collectors.toList());
                    //todo 然后对返费取整求和   法催利润求和
                    BigDecimal feeRound = collect.stream().map(CwProjectPayDateVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal lawProfit = collect.stream().map(CwProjectPayDateVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //todo 再然后，去根据期次id去查对应的打款信息
                    if (collect1.size() != 0) {
                        for (Long phaseId:collect1) {
                            List<Map<String, Object>> payInfo = cwProjectIncomeMapper.selectCwprojectLawPayInfoByPhaseId(phaseId);
                            CwProjectPayDateVo cwProjectPayDateVo = value.get(0);
                            for (int i = 0; i < payInfo.size(); i++) {
                                CwProjectPayDateVo cwProjectPayDateVo1 = new CwProjectPayDateVo();
                                cwProjectPayDateVo1.setProjectId((Long) payInfo.get(i).get("projectId"));
                                cwProjectPayDateVo1.setPhaseId((Long) payInfo.get(i).get("phaseId"));
                                cwProjectPayDateVo1.setProjectIncomeId((Long) payInfo.get(i).get("projectIncomeId"));
                                cwProjectPayDateVo1.setProjectFeeId((Long) payInfo.get(i).get("projectFeeId"));
                                cwProjectPayDateVo1.setProjectCustId((Long) payInfo.get(i).get("custId"));
                                cwProjectPayDateVo1.setProjectName(cwProjectPayDateVo.getProjectName());
                                cwProjectPayDateVo1.setCustName((String) payInfo.get(i).get("custName"));
                                cwProjectPayDateVo1.setFeeCustName((String) payInfo.get(i).get("feeCustName"));
                                cwProjectPayDateVo1.setTermMonthCompare(cwProjectPayDateVo.getTermMonthCompare());
                                cwProjectPayDateVo1.setTerm(cwProjectPayDateVo.getTerm());
                                cwProjectPayDateVo1.setIncomeAmt(cwProjectPayDateVo.getIncomeAmt());
                                cwProjectPayDateVo1.setFeeAmtSum(cwProjectPayDateVo.getFeeAmtSum());
                                cwProjectPayDateVo1.setUnfeeAmtSum(cwProjectPayDateVo.getUnfeeAmtSum());
                                cwProjectPayDateVo1.setRemark(cwProjectPayDateVo.getRemark());
                                cwProjectPayDateVo1.setPayDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, (Date) payInfo.get(i).get("payDate")));
                                cwProjectPayDateVo1.setPayAmt((BigDecimal) payInfo.get(i).get("payAmt"));
                                cwProjectPayDateVo1.setDifferenceAmt((BigDecimal) payInfo.get(i).get("differenceAmt"));
                                cwProjectPayDateVo1.setProjectType(cwProjectPayDateVo.getProjectType());
                                cwProjectPayDateVo1.setFeeRound(feeRound);
                                cwProjectPayDateVo1.setLawProfit(lawProfit);
                                list.add(cwProjectPayDateVo1);
                            }
                        }
                    } else {
                        BigDecimal feeRound1 = collect.stream().map(CwProjectPayDateVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal lawProfit1 = collect.stream().map(CwProjectPayDateVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                        CwProjectPayDateVo cwProjectFeeNoAlreadyVo = value.get(0);
                        cwProjectFeeNoAlreadyVo.setPhaseId(cwProjectFeeNoAlreadyVo.getProjectIncomeId());
                        String custName = collect.get(0).getCustName();
                        String feeCustName = collect.get(0).getFeeCustName();
                        collect.clear();
                        cwProjectFeeNoAlreadyVo.setCustName(custName);
                        cwProjectFeeNoAlreadyVo.setFeeCustName(feeCustName);
                        cwProjectFeeNoAlreadyVo.setFeeRound(feeRound1);
                        cwProjectFeeNoAlreadyVo.setLawProfit(lawProfit1);
                        collect.add(cwProjectFeeNoAlreadyVo);
                        list.add(cwProjectFeeNoAlreadyVo);
                    }
                });
                List<CwProjectPayDateVo> list1 = list.stream().filter(t -> t.getCustName() != null && t.getFeeCustName() != null).collect(Collectors.toList());
                Map<Long, List<CwProjectPayDateVo>> collect = list1.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId));
                collect.forEach((key, value) -> {
                    //对每一个项目的不同期次排序。
                    List<CwProjectPayDateVo> collect1 = value.stream().sorted(Comparator.comparing(CwProjectPayDateVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                    resultListForLaw.addAll(collect1);
                });
                if (resultList.size() == 0 && resultListForLaw.size() == 0) {
                    Map<String, Object> mm = new HashMap<>();
                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
                    returnMap.put("info", mm);
                    returnMap.put("projectTypeFlag", "99999");
                    return returnMap;
                } else {
                    Map<String, Object> mm = new HashMap<>();
                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
                    //todo 判断查询的项目类型
                    if (StrUtil.EMPTY.equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("lawList", resultListForLaw);
                        returnMap.put("projectTypeFlag", "all");
                    } else if ("0".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "0");
                    } else if ("2".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "2");
                    } else if ("1".equals(projectType)) {
                        returnMap.put("lawList", resultListForLaw);
                        returnMap.put("projectTypeFlag", "1");
                    } else if ("3".equals(projectType)) {
                        returnMap.put("list", resultList);
                        returnMap.put("projectTypeFlag", "3");
                    }
                    returnMap.put("info", mm);
                    returnMap.put("payAmtSum", payAmtSum);
                    returnMap.put("differenceAmtSum", differenceAmtSum);
                    return returnMap;
                }
            }
        }
    }

    @Override
    public Map<String, Object> selectCollectionTimeQueryDetailQueryTime(String queryStartTime, String queryEndTime, LoginUser loginUser, String projectType, List<Long> projectIds) {
        //给一个响应的map，里面有info，list，payAmtSum，differenceAmtSum
        Map<String, Object> returnMap = new HashMap<>();
        BigDecimal incomeAmtSum = null;
        //首先，判断用户的身份
        List<SysRole> roles = loginUser.getUser().getRoles();
        boolean present = roles.stream().anyMatch(t -> "admin".equals(t.getRoleKey()) || "caiwuAdmin".equals(t.getRoleKey()) || "yewuAdmin".equals(t.getRoleKey()));
        if (StringUtils.isEmpty(queryStartTime) || StringUtils.isEmpty(queryEndTime)) {
            Map<String, Object> mm = new HashMap<>();
            mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NO_SELECT_PARAM.getCode());
            mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NO_SELECT_PARAM.getMsg());
            returnMap.put("info", mm);
            returnMap.put("projectTypeFlag", "99999");
            return returnMap;
        } else {
//            if ("all".equals(projectType)) {
//                projectType = StrUtil.EMPTY;
//            }
            incomeAmtSum = BigDecimal.ZERO;
            //可以查所有项目    ->     项目维度又分为两种：普通项目和法催项目
            //查询所有正常状态的项目，包含了普通和法催。
            List<CwProjectPayDateVo> resultList = new ArrayList<>();
            List<CwProjectPayDateVo> resultListForLaw = new ArrayList<>();

            //查询普通项目类型(非法催)
            OaProjectTypeCorrelationVo lawUrgingNo = oaProjectTypeCorrelationService.selectOaProjectTypeCorrelationByProjectPortfolioCode("lawUrgingNo");
            //普通项目
            List<CwProjectPayDateVo> cwProjectPayDateVos = cwProjectIncomeMapper.selectCollectionTimeQueryDetailByQueryTime(queryStartTime, queryEndTime, projectType, projectIds);
            if (projectType == null || projectType.isEmpty() || lawUrgingNo.getProjectCodeList().contains(projectType)) {
                incomeAmtSum = cwProjectPayDateVos.stream().filter(t -> t.getIncomeAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getIncomeAmt, BigDecimal::add));
            }
            Map<Long, List<CwProjectPayDateVo>> normalMap = cwProjectPayDateVos.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId));
            normalMap.forEach((key, value) -> {
                //对每一个项目的不同期次排序。
                List<CwProjectPayDateVo> collect = value.stream().sorted(Comparator.comparing(CwProjectPayDateVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                for (CwProjectPayDateVo cppd:collect) {
                    String replaceFlag = cppd.getReplaceFlag();
                    Long custId = cppd.getProjectCustId();
                    String feeCustName = StringUtils.EMPTY;
                    if ("1".equals(replaceFlag)) {
                        feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
                    } else {
                        feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
                    }
                    cppd.setFeeCustName(feeCustName);
                }
                resultList.addAll(collect);
            });

            //查询法催项目类型(法催)
            OaProjectTypeCorrelationVo lawUrging = oaProjectTypeCorrelationService.selectOaProjectTypeCorrelationByProjectPortfolioCode("lawUrging");

            //法催项目
            List<CwProjectPayDateVo> cwProjectLawFeePayDateVos = cwProjectIncomeMapper.selectLawCollectionTimeQueryDetailByQueryTime(queryStartTime, queryEndTime, projectIds);
            BigDecimal lawIncomeAmtSum = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() == null && t.getIncomeAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getIncomeAmt, BigDecimal::add));
            if (projectType == null || projectType.isEmpty() || lawUrging.getProjectCodeList().contains(projectType)) {
                incomeAmtSum = incomeAmtSum.add(lawIncomeAmtSum);
            }
            //找法催项目的期次。
            Map<Long, List<CwProjectPayDateVo>> collectMap = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() == null).collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectIncomeId));
            //空集合用来装处理好的法催期次
            List<CwProjectPayDateVo> list = new ArrayList<>();
            collectMap.forEach((key, value) -> {
                //todo 先去找有打款记录期次id
                List<Long> collect1 = value.stream().filter(t -> t.getFeeAmtSum() != null && t.getFeeAmtSum().compareTo(BigDecimal.ZERO) > 0).map(CwProjectPayDateVo::getProjectIncomeId).collect(Collectors.toList());
                List<CwProjectPayDateVo> collect = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() != null && t.getPhaseId().equals(key)).collect(Collectors.toList());
                for (CwProjectPayDateVo cppd:collect) {
                    String replaceFlag = cppd.getReplaceFlag();
                    Long custId = cppd.getProjectCustId();
                    String feeCustName = StringUtils.EMPTY;
                    if ("1".equals(replaceFlag)) {
                        feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
                    } else {
                        feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
                    }
                    cppd.setFeeCustName(feeCustName);
                }
                //todo 然后对返费取整求和   法催利润求和
                BigDecimal feeRound = collect.stream().map(CwProjectPayDateVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal lawProfit = collect.stream().map(CwProjectPayDateVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                //todo 再然后，去根据期次id去查对应的打款信息
                if (collect1.size() != 0) {
                    for (Long phaseId:collect1) {
                        List<Map<String, Object>> payInfo = cwProjectIncomeMapper.selectCwprojectLawPayInfoByPhaseId(phaseId);
                        CwProjectPayDateVo cwProjectPayDateVo = value.get(0);
                        for (int i = 0; i < 1; i++) {
                            CwProjectPayDateVo cwProjectPayDateVo1 = new CwProjectPayDateVo();
                            cwProjectPayDateVo1.setProjectId((Long) payInfo.get(i).get("projectId"));
                            cwProjectPayDateVo1.setPhaseId((Long) payInfo.get(i).get("phaseId"));
                            cwProjectPayDateVo1.setProjectIncomeId((Long) payInfo.get(i).get("projectIncomeId"));
                            cwProjectPayDateVo1.setProjectFeeId((Long) payInfo.get(i).get("projectFeeId"));
                            cwProjectPayDateVo1.setProjectCustId((Long) payInfo.get(i).get("custId"));
                            cwProjectPayDateVo1.setProjectName(cwProjectPayDateVo.getProjectName());
                            cwProjectPayDateVo1.setCustName((String) payInfo.get(i).get("custName"));
                            cwProjectPayDateVo1.setFeeCustName((String) payInfo.get(i).get("feeCustName"));
                            cwProjectPayDateVo1.setTermMonthCompare(cwProjectPayDateVo.getTermMonthCompare());
                            cwProjectPayDateVo1.setTerm(cwProjectPayDateVo.getTerm());
                            cwProjectPayDateVo1.setIncomeAmt(cwProjectPayDateVo.getIncomeAmt());
                            cwProjectPayDateVo1.setRemark(cwProjectPayDateVo.getRemark());
                            cwProjectPayDateVo1.setProjectType(cwProjectPayDateVo.getProjectType());
                            cwProjectPayDateVo1.setFeeRound(feeRound);
                            cwProjectPayDateVo1.setLawProfit(lawProfit);
                            list.add(cwProjectPayDateVo1);
                        }
                    }
                } else {
                    BigDecimal feeRound1 = collect.stream().map(CwProjectPayDateVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal lawProfit1 = collect.stream().map(CwProjectPayDateVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                    CwProjectPayDateVo cwProjectFeeNoAlreadyVo = value.get(0);
                    cwProjectFeeNoAlreadyVo.setPhaseId(cwProjectFeeNoAlreadyVo.getProjectIncomeId());
                    String custName = collect.get(0).getCustName();
                    String feeCustName = collect.get(0).getFeeCustName();
                    collect.clear();
                    cwProjectFeeNoAlreadyVo.setCustName(custName);
                    cwProjectFeeNoAlreadyVo.setFeeCustName(feeCustName);
                    cwProjectFeeNoAlreadyVo.setFeeRound(feeRound1);
                    cwProjectFeeNoAlreadyVo.setLawProfit(lawProfit1);
                    collect.add(cwProjectFeeNoAlreadyVo);
                    list.add(cwProjectFeeNoAlreadyVo);
                }
            });
            List<CwProjectPayDateVo> list1 = list.stream().filter(t -> t.getCustName() != null && t.getFeeCustName() != null).collect(Collectors.toList());
            Map<Long, List<CwProjectPayDateVo>> collect = list1.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId));
            collect.forEach((key, value) -> {
                //对每一个项目的不同期次排序。
                List<CwProjectPayDateVo> collect1 = value.stream().sorted(Comparator.comparing(CwProjectPayDateVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
//                    resultList.addAll(collect1);
                resultListForLaw.addAll(collect1);
            });
            //通过期次找对应收入
            if (resultList.size() == 0 && resultListForLaw.size() == 0) {
                Map<String, Object> mm = new HashMap<>();
                mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
                mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
                returnMap.put("info", mm);
                returnMap.put("projectTypeFlag", "99999");
                return returnMap;
            } else {
                Map<String, Object> mm = new HashMap<>();
                mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
                mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
                //todo 判断查询的项目类型
//                if (StrUtil.EMPTY.equals(projectType)) {
//                    returnMap.put("list", resultList);
//                    returnMap.put("lawList", resultListForLaw);
//                    returnMap.put("projectTypeFlag", "all");
//                } else if ("0".equals(projectType)) {
//                    returnMap.put("list", resultList);
//                    returnMap.put("projectTypeFlag", "0");
//                } else if ("2".equals(projectType)) {
//                    returnMap.put("list", resultList);
//                    returnMap.put("projectTypeFlag", "2");
//                } else if ("1".equals(projectType)) {
//                    returnMap.put("lawList", resultListForLaw);
//                    returnMap.put("projectTypeFlag", "1");
//                } else if ("3".equals(projectType)) {
//                    returnMap.put("list", resultList);
//                    returnMap.put("projectTypeFlag", "3");
//                }
                if (StrUtil.EMPTY.equals(projectType)) {
                    returnMap.put("list", resultList);
                    returnMap.put("lawList", resultListForLaw);
                    returnMap.put("projectTypeFlag", "all");
                    String lawUrgingName = String.join("、", lawUrging.getProjectTypeNameList());
                    returnMap.put("lawUrgingName", lawUrgingName);
                    String lawUrgingNoName = String.join("、", lawUrgingNo.getProjectTypeNameList());
                    returnMap.put("lawUrgingNoName", lawUrgingNoName);
                } else if (lawUrging.getProjectCodeList().contains(projectType)) {
                    returnMap.put("lawList", resultListForLaw);
                    returnMap.put("projectTypeFlag", "lawUrging");
                    int index = lawUrging.getProjectCodeList().indexOf(projectType);
                    returnMap.put("lawUrgingName", lawUrging.getProjectTypeNameList().get(index));
                } else if (lawUrgingNo.getProjectCodeList().contains(projectType)) {
                    returnMap.put("list", resultList);
                    returnMap.put("projectTypeFlag", "lawUrgingNo");
                    int index = lawUrgingNo.getProjectCodeList().indexOf(projectType);
                    returnMap.put("lawUrgingNoName", lawUrgingNo.getProjectTypeNameList().get(index));
                }
                returnMap.put("info", mm);
                returnMap.put("sum", incomeAmtSum);
                return returnMap;
            }

//            if (present) {
//                //可以查所有项目    ->     项目维度又分为两种：普通项目和法催项目
//                //查询所有正常状态的项目，包含了普通和法催。
//                List<CwProjectPayDateVo> resultList = new ArrayList<>();
//                List<CwProjectPayDateVo> resultListForLaw = new ArrayList<>();
//                //普通项目
//                List<CwProjectPayDateVo> cwProjectPayDateVos = cwProjectIncomeMapper.selectCollectionTimeQueryDetailByQueryTime(queryStartTime, queryEndTime, projectType);
//                if (StrUtil.EMPTY.equals(projectType) || "0".equals(projectType) || "2".equals(projectType) || "3".equals(projectType)) {
//                    incomeAmtSum = cwProjectPayDateVos.stream().filter(t -> t.getIncomeAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getIncomeAmt, BigDecimal::add));
//                }
//                Map<Long, List<CwProjectPayDateVo>> normalMap = cwProjectPayDateVos.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId));
//                normalMap.forEach((key, value) -> {
//                    //对每一个项目的不同期次排序。
//                    List<CwProjectPayDateVo> collect = value.stream().sorted(Comparator.comparing(CwProjectPayDateVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
//                    for (CwProjectPayDateVo cppd:collect) {
//                        String replaceFlag = cppd.getReplaceFlag();
//                        Long custId = cppd.getProjectCustId();
//                        String feeCustName = StringUtils.EMPTY;
//                        if ("1".equals(replaceFlag)) {
//                            feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
//                        } else {
//                            feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
//                        }
//                        cppd.setFeeCustName(feeCustName);
//                    }
//                    resultList.addAll(collect);
//                });
//                //法催项目
//                List<CwProjectPayDateVo> cwProjectLawFeePayDateVos = cwProjectIncomeMapper.selectLawCollectionTimeQueryDetailByQueryTime(queryStartTime, queryEndTime);
//                BigDecimal lawIncomeAmtSum = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() == null && t.getIncomeAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getIncomeAmt, BigDecimal::add));
//                if (StrUtil.EMPTY.equals(projectType) || "1".equals(projectType)) {
//                    incomeAmtSum = incomeAmtSum.add(lawIncomeAmtSum);
//                }
//                //找法催项目的期次。
//                Map<Long, List<CwProjectPayDateVo>> collectMap = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() == null).collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectIncomeId));
//                //空集合用来装处理好的法催期次
//                List<CwProjectPayDateVo> list = new ArrayList<>();
//                collectMap.forEach((key, value) -> {
//                    //todo 先去找有打款记录期次id
//                    List<Long> collect1 = value.stream().filter(t -> t.getFeeAmtSum() != null && t.getFeeAmtSum().compareTo(BigDecimal.ZERO) > 0).map(CwProjectPayDateVo::getProjectIncomeId).collect(Collectors.toList());
//                    List<CwProjectPayDateVo> collect = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() != null && t.getPhaseId().equals(key)).collect(Collectors.toList());
//                    for (CwProjectPayDateVo cppd:collect) {
//                        String replaceFlag = cppd.getReplaceFlag();
//                        Long custId = cppd.getProjectCustId();
//                        String feeCustName = StringUtils.EMPTY;
//                        if ("1".equals(replaceFlag)) {
//                            feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
//                        } else {
//                            feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
//                        }
//                        cppd.setFeeCustName(feeCustName);
//                    }
//                    //todo 然后对返费取整求和   法催利润求和
//                    BigDecimal feeRound = collect.stream().map(CwProjectPayDateVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    BigDecimal lawProfit = collect.stream().map(CwProjectPayDateVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    //todo 再然后，去根据期次id去查对应的打款信息
//                    if (collect1.size() != 0) {
//                        for (Long phaseId:collect1) {
//                            List<Map<String, Object>> payInfo = cwProjectIncomeMapper.selectCwprojectLawPayInfoByPhaseId(phaseId);
//                            CwProjectPayDateVo cwProjectPayDateVo = value.get(0);
//                            for (int i = 0; i < 1; i++) {
//                                CwProjectPayDateVo cwProjectPayDateVo1 = new CwProjectPayDateVo();
//                                cwProjectPayDateVo1.setProjectId((Long) payInfo.get(i).get("projectId"));
//                                cwProjectPayDateVo1.setPhaseId((Long) payInfo.get(i).get("phaseId"));
//                                cwProjectPayDateVo1.setProjectIncomeId((Long) payInfo.get(i).get("projectIncomeId"));
//                                cwProjectPayDateVo1.setProjectFeeId((Long) payInfo.get(i).get("projectFeeId"));
//                                cwProjectPayDateVo1.setProjectCustId((Long) payInfo.get(i).get("custId"));
//                                cwProjectPayDateVo1.setProjectName(cwProjectPayDateVo.getProjectName());
//                                cwProjectPayDateVo1.setCustName((String) payInfo.get(i).get("custName"));
//                                cwProjectPayDateVo1.setFeeCustName((String) payInfo.get(i).get("feeCustName"));
//                                cwProjectPayDateVo1.setTermMonthCompare(cwProjectPayDateVo.getTermMonthCompare());
//                                cwProjectPayDateVo1.setTerm(cwProjectPayDateVo.getTerm());
//                                cwProjectPayDateVo1.setIncomeAmt(cwProjectPayDateVo.getIncomeAmt());
//                                cwProjectPayDateVo1.setRemark(cwProjectPayDateVo.getRemark());
//                                cwProjectPayDateVo1.setProjectType(cwProjectPayDateVo.getProjectType());
//                                cwProjectPayDateVo1.setFeeRound(feeRound);
//                                cwProjectPayDateVo1.setLawProfit(lawProfit);
//                                list.add(cwProjectPayDateVo1);
//                            }
//                        }
//                    } else {
//                        BigDecimal feeRound1 = collect.stream().map(CwProjectPayDateVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        BigDecimal lawProfit1 = collect.stream().map(CwProjectPayDateVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        CwProjectPayDateVo cwProjectFeeNoAlreadyVo = value.get(0);
//                        cwProjectFeeNoAlreadyVo.setPhaseId(cwProjectFeeNoAlreadyVo.getProjectIncomeId());
//                        String custName = collect.get(0).getCustName();
//                        String feeCustName = collect.get(0).getFeeCustName();
//                        collect.clear();
//                        cwProjectFeeNoAlreadyVo.setCustName(custName);
//                        cwProjectFeeNoAlreadyVo.setFeeCustName(feeCustName);
//                        cwProjectFeeNoAlreadyVo.setFeeRound(feeRound1);
//                        cwProjectFeeNoAlreadyVo.setLawProfit(lawProfit1);
//                        collect.add(cwProjectFeeNoAlreadyVo);
//                        list.add(cwProjectFeeNoAlreadyVo);
//                    }
//                });
//                List<CwProjectPayDateVo> list1 = list.stream().filter(t -> t.getCustName() != null && t.getFeeCustName() != null).collect(Collectors.toList());
//                Map<Long, List<CwProjectPayDateVo>> collect = list1.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId));
//                collect.forEach((key, value) -> {
//                    //对每一个项目的不同期次排序。
//                    List<CwProjectPayDateVo> collect1 = value.stream().sorted(Comparator.comparing(CwProjectPayDateVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
////                    resultList.addAll(collect1);
//                    resultListForLaw.addAll(collect1);
//                });
//                //通过期次找对应收入
//                if (resultList.size() == 0 && resultListForLaw.size() == 0) {
//                    Map<String, Object> mm = new HashMap<>();
//                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
//                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
//                    returnMap.put("info", mm);
//                    returnMap.put("projectTypeFlag", "99999");
//                    return returnMap;
//                } else {
//                    Map<String, Object> mm = new HashMap<>();
//                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
//                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
//                    //todo 判断查询的项目类型
//                    if (StrUtil.EMPTY.equals(projectType)) {
//                        returnMap.put("list", resultList);
//                        returnMap.put("lawList", resultListForLaw);
//                        returnMap.put("projectTypeFlag", "all");
//                    } else if ("0".equals(projectType)) {
//                        returnMap.put("list", resultList);
//                        returnMap.put("projectTypeFlag", "0");
//                    } else if ("2".equals(projectType)) {
//                        returnMap.put("list", resultList);
//                        returnMap.put("projectTypeFlag", "2");
//                    } else if ("1".equals(projectType)) {
//                        returnMap.put("lawList", resultListForLaw);
//                        returnMap.put("projectTypeFlag", "1");
//                    } else if ("3".equals(projectType)) {
//                        returnMap.put("list", resultList);
//                        returnMap.put("projectTypeFlag", "3");
//                    }
//                    returnMap.put("info", mm);
//                    returnMap.put("sum", incomeAmtSum);
//                    return returnMap;
//                }
//            } else {
//                List<CwProjectPayDateVo> resultList = new ArrayList<>();
//                List<CwProjectPayDateVo> resultListForLaw = new ArrayList<>();
//                //先找普通项目
//                List<CwProjectPayDateVo> cwProjectPayDateVos = cwProjectIncomeMapper.selectCollectionTimeQueryDetailByQueryTimeAndUserId(queryStartTime, queryEndTime, loginUser.getUserId(), projectType);
//                if (StrUtil.EMPTY.equals(projectType) || "0".equals(projectType) || "2".equals(projectType) || "3".equals(projectType)) {
//                    incomeAmtSum = cwProjectPayDateVos.stream().filter(t -> t.getIncomeAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getIncomeAmt, BigDecimal::add));
//                }
//                Map<Long, List<CwProjectPayDateVo>> normalMap = cwProjectPayDateVos.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId));
//                normalMap.forEach((key, value) -> {
//                    //对每一个项目的不同期次排序。
//                    List<CwProjectPayDateVo> collect = value.stream().sorted(Comparator.comparing(CwProjectPayDateVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
//                    for (CwProjectPayDateVo cppd:collect) {
//                        String replaceFlag = cppd.getReplaceFlag();
//                        Long custId = cppd.getProjectCustId();
//                        String feeCustName = StringUtils.EMPTY;
//                        if ("1".equals(replaceFlag)) {
//                            feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
//                        } else {
//                            feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
//                        }
//                        cppd.setFeeCustName(feeCustName);
//                    }
//                    resultList.addAll(collect);
//                });
//                //再找法催项目
//                List<CwProjectPayDateVo> cwProjectLawFeePayDateVos = cwProjectIncomeMapper.selectLawCollectionTimeQueryDetailByQueryTimeAndUserId(queryStartTime, queryEndTime, loginUser.getUserId());
//                BigDecimal lawIncomeAmtSum = cwProjectLawFeePayDateVos.stream().filter(t -> t.getIncomeAmt() != null).collect(Collectors.reducing(BigDecimal.ZERO, CwProjectPayDateVo::getIncomeAmt, BigDecimal::add));
//                if (StrUtil.EMPTY.equals(projectType) || "1".equals(projectType)) {
//                    incomeAmtSum = incomeAmtSum.add(lawIncomeAmtSum);
//                }
//                //找法催项目的期次。
//                Map<Long, List<CwProjectPayDateVo>> collectMap = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() == null).collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectIncomeId));
//                //空集合用来装处理好的法催期次
//                List<CwProjectPayDateVo> list = new ArrayList<>();
//                collectMap.forEach((key, value) -> {
//                    //todo 先去找有打款记录期次id
//                    List<Long> collect1 = value.stream().filter(t -> t.getFeeAmtSum() != null && t.getFeeAmtSum().compareTo(BigDecimal.ZERO) > 0).map(CwProjectPayDateVo::getProjectIncomeId).collect(Collectors.toList());
//                    List<CwProjectPayDateVo> collect = cwProjectLawFeePayDateVos.stream().filter(t -> t.getPhaseId() != null && t.getPhaseId().equals(key)).collect(Collectors.toList());
//                    for (CwProjectPayDateVo cppd:collect) {
//                        String replaceFlag = cppd.getReplaceFlag();
//                        Long custId = cppd.getProjectCustId();
//                        String feeCustName = StringUtils.EMPTY;
//                        if ("1".equals(replaceFlag)) {
//                            feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
//                        } else {
//                            feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
//                        }
//                        cppd.setFeeCustName(feeCustName);
//                    }
//                    //todo 然后对返费取整求和   法催利润求和
//                    BigDecimal feeRound = collect.stream().map(CwProjectPayDateVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    BigDecimal lawProfit = collect.stream().map(CwProjectPayDateVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    //todo 再然后，去根据期次id去查对应的打款信息
//                    if (collect1.size() != 0) {
//                        for (Long phaseId:collect1) {
//                            List<Map<String, Object>> payInfo = cwProjectIncomeMapper.selectCwprojectLawPayInfoByPhaseId(phaseId);
//                            CwProjectPayDateVo cwProjectPayDateVo = value.get(0);
//                            for (int i = 0; i < 1; i++) {
//                                CwProjectPayDateVo cwProjectPayDateVo1 = new CwProjectPayDateVo();
//                                cwProjectPayDateVo1.setProjectId((Long) payInfo.get(i).get("projectId"));
//                                cwProjectPayDateVo1.setPhaseId((Long) payInfo.get(i).get("phaseId"));
//                                cwProjectPayDateVo1.setProjectIncomeId((Long) payInfo.get(i).get("projectIncomeId"));
//                                cwProjectPayDateVo1.setProjectFeeId((Long) payInfo.get(i).get("projectFeeId"));
//                                cwProjectPayDateVo1.setProjectCustId((Long) payInfo.get(i).get("custId"));
//                                cwProjectPayDateVo1.setProjectName(cwProjectPayDateVo.getProjectName());
//                                cwProjectPayDateVo1.setCustName((String) payInfo.get(i).get("custName"));
//                                cwProjectPayDateVo1.setFeeCustName((String) payInfo.get(i).get("feeCustName"));
//                                cwProjectPayDateVo1.setTermMonthCompare(cwProjectPayDateVo.getTermMonthCompare());
//                                cwProjectPayDateVo1.setTerm(cwProjectPayDateVo.getTerm());
//                                cwProjectPayDateVo1.setIncomeAmt(cwProjectPayDateVo.getIncomeAmt());
//                                cwProjectPayDateVo1.setRemark(cwProjectPayDateVo.getRemark());
//                                cwProjectPayDateVo1.setProjectType(cwProjectPayDateVo.getProjectType());
//                                cwProjectPayDateVo1.setFeeRound(feeRound);
//                                cwProjectPayDateVo1.setLawProfit(lawProfit);
//                                list.add(cwProjectPayDateVo1);
//                            }
//                        }
//                    } else {
//                        BigDecimal feeRound1 = collect.stream().map(CwProjectPayDateVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        BigDecimal lawProfit1 = collect.stream().map(CwProjectPayDateVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        CwProjectPayDateVo cwProjectFeeNoAlreadyVo = value.get(0);
//                        cwProjectFeeNoAlreadyVo.setPhaseId(cwProjectFeeNoAlreadyVo.getProjectIncomeId());
//                        String custName = collect.get(0).getCustName();
//                        String feeCustName = collect.get(0).getFeeCustName();
//                        collect.clear();
//                        cwProjectFeeNoAlreadyVo.setCustName(custName);
//                        cwProjectFeeNoAlreadyVo.setFeeCustName(feeCustName);
//                        cwProjectFeeNoAlreadyVo.setFeeRound(feeRound1);
//                        cwProjectFeeNoAlreadyVo.setLawProfit(lawProfit1);
//                        collect.add(cwProjectFeeNoAlreadyVo);
//                        list.add(cwProjectFeeNoAlreadyVo);
//                    }
//                });
//                List<CwProjectPayDateVo> list1 = list.stream().filter(t -> t.getCustName() != null && t.getFeeCustName() != null).collect(Collectors.toList());
//                Map<Long, List<CwProjectPayDateVo>> collect = list1.stream().collect(Collectors.groupingBy(CwProjectPayDateVo::getProjectId));
//                collect.forEach((key, value) -> {
//                    //对每一个项目的不同期次排序。
//                    List<CwProjectPayDateVo> collect1 = value.stream().sorted(Comparator.comparing(CwProjectPayDateVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
//                    resultListForLaw.addAll(collect1);
//                });
//                if (resultList.size() == 0 && resultListForLaw.size() == 0) {
//                    Map<String, Object> mm = new HashMap<>();
//                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
//                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
//                    returnMap.put("info", mm);
//                    returnMap.put("projectTypeFlag", "99999");
//                    return returnMap;
//                } else {
//                    Map<String, Object> mm = new HashMap<>();
//                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
//                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
//                    //todo 判断查询的项目类型
//                    if (StrUtil.EMPTY.equals(projectType)) {
//                        returnMap.put("list", resultList);
//                        returnMap.put("lawList", resultListForLaw);
//                        returnMap.put("projectTypeFlag", "all");
//                    } else if ("0".equals(projectType)) {
//                        returnMap.put("list", resultList);
//                        returnMap.put("projectTypeFlag", "0");
//                    } else if ("2".equals(projectType)) {
//                        returnMap.put("list", resultList);
//                        returnMap.put("projectTypeFlag", "2");
//                    } else if ("1".equals(projectType)) {
//                        returnMap.put("lawList", resultListForLaw);
//                        returnMap.put("projectTypeFlag", "1");
//                    } else if ("3".equals(projectType)) {
//                        returnMap.put("list", resultList);
//                        returnMap.put("projectTypeFlag", "3");
//                    }
//                    returnMap.put("info", mm);
//                    returnMap.put("sum", incomeAmtSum);
//                    return returnMap;
//                }
//            }
        }
    }

    @Override
    public Map<String, Object> selectRemarkQueryDetailByQueryRemark(String remark, LoginUser loginUser, List<Long> projectIds) {
        //给一个响应的map，里面有info，list，sum
        Map<String, Object> returnMap = new HashMap<>();
        //首先，判断用户的身份
        List<SysRole> roles = loginUser.getUser().getRoles();
        boolean present = roles.stream().anyMatch(t -> "admin".equals(t.getRoleKey()) || "caiwuAdmin".equals(t.getRoleKey()) || "yewuAdmin".equals(t.getRoleKey()));
        if (StringUtils.isEmpty(remark)) {
            Map<String, Object> mm = new HashMap<>();
            mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NO_SELECT_PARAM.getCode());
            mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NO_SELECT_PARAM.getMsg());
            returnMap.put("info", mm);
            returnMap.put("projectTypeFlag", "99999");
            return returnMap;
        } else {
            //可以查所有项目    ->     项目维度又分为两种：普通项目和法催项目
            //查询所有正常状态的项目，包含了普通和法催。
            List<CwProjectFeeNoAlreadyVo> resultList = new ArrayList<>();
            List<CwProjectFeeNoAlreadyVo> resultListForLaw = new ArrayList<>();
            //普通项目
            List<CwProjectFeeNoAlreadyVo> cwProjectFeeNoAlreadyVos = cwProjectIncomeMapper.selectRemarkQueryDetailByQueryRemark(remark, projectIds);
            Map<Long, List<CwProjectFeeNoAlreadyVo>> normalMap = cwProjectFeeNoAlreadyVos.stream().collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectId));
            normalMap.forEach((key, value) -> {
                //对每一个项目的不同期次排序。
                List<CwProjectFeeNoAlreadyVo> collect = value.stream().sorted(Comparator.comparing(CwProjectFeeNoAlreadyVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                for (CwProjectFeeNoAlreadyVo cpfna:collect) {
                    String replaceFlag = cpfna.getReplaceFlag();
                    Long custId = cpfna.getProjectCustId();
                    String feeCustName = StringUtils.EMPTY;
                    if ("1".equals(replaceFlag)) {
                        feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
                    } else {
                        feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
                    }
                    cpfna.setFeeCustName(feeCustName);
                }
                resultList.addAll(collect);
            });
            //法催项目
            List<CwProjectFeeNoAlreadyVo> cwProjectLawFeeNoAlreadyVos = cwProjectIncomeMapper.selectLawRemarkQueryDetailByQueryRemark(remark, projectIds);
            //找法催项目的期次。
            Map<Long, List<CwProjectFeeNoAlreadyVo>> collectMap = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getPhaseId() == null).collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectIncomeId));
            //空集合用来装处理好的法催期次
            List<CwProjectFeeNoAlreadyVo> list = new ArrayList<>();
            collectMap.forEach((key, value) -> {
                //todo 先去找有打款记录期次id
                List<Long> collect1 = value.stream().filter(t -> t.getFeeAmtSum() != null && t.getFeeAmtSum().compareTo(BigDecimal.ZERO) > 0).map(CwProjectFeeNoAlreadyVo::getProjectIncomeId).collect(Collectors.toList());
                List<CwProjectFeeNoAlreadyVo> collect = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getPhaseId() != null && t.getPhaseId().equals(key)).collect(Collectors.toList());
                for (CwProjectFeeNoAlreadyVo cpfna:collect) {
                    String replaceFlag = cpfna.getReplaceFlag();
                    Long custId = cpfna.getProjectCustId();
                    String feeCustName = StringUtils.EMPTY;
                    if ("1".equals(replaceFlag)) {
                        feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
                    } else {
                        feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
                    }
                    cpfna.setFeeCustName(feeCustName);
                }
                //todo 然后对返费取整求和   法催利润求和
                BigDecimal feeRound = collect.stream().map(CwProjectFeeNoAlreadyVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal lawProfit = collect.stream().map(CwProjectFeeNoAlreadyVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                //todo 再然后，去根据期次id去查对应的打款信息
                if (collect1.size() != 0) {
                    for (Long phaseId:collect1) {
                        List<Map<String, Object>> payInfo = cwProjectIncomeMapper.selectCwprojectLawPayInfoByPhaseId(phaseId);
                        CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo = value.get(0);
                        for (int i = 0; i < 1; i++) {
                            CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo1 = new CwProjectFeeNoAlreadyVo();
                            cwProjectFeeNoAlreadyVo1.setProjectId((Long) payInfo.get(i).get("projectId"));
                            cwProjectFeeNoAlreadyVo1.setPhaseId((Long) payInfo.get(i).get("phaseId"));
                            cwProjectFeeNoAlreadyVo1.setProjectIncomeId((Long) payInfo.get(i).get("projectIncomeId"));
                            cwProjectFeeNoAlreadyVo1.setProjectFeeId((Long) payInfo.get(i).get("projectFeeId"));
                            cwProjectFeeNoAlreadyVo1.setProjectCustId((Long) payInfo.get(i).get("custId"));
                            cwProjectFeeNoAlreadyVo1.setProjectName(cwProjectFeeNoAlreadyVo.getProjectName());
                            cwProjectFeeNoAlreadyVo1.setCustName((String) payInfo.get(i).get("custName"));
                            cwProjectFeeNoAlreadyVo1.setFeeCustName((String) payInfo.get(i).get("feeCustName"));
                            cwProjectFeeNoAlreadyVo1.setTermMonthCompare(cwProjectFeeNoAlreadyVo.getTermMonthCompare());
                            cwProjectFeeNoAlreadyVo1.setFeeNoAlreadyTerm(cwProjectFeeNoAlreadyVo.getFeeNoAlreadyTerm());
                            cwProjectFeeNoAlreadyVo1.setIncomeAmt(cwProjectFeeNoAlreadyVo.getIncomeAmt());
                            cwProjectFeeNoAlreadyVo1.setRemark(cwProjectFeeNoAlreadyVo.getRemark());
                            cwProjectFeeNoAlreadyVo1.setProjectType(cwProjectFeeNoAlreadyVo.getProjectType());
                            cwProjectFeeNoAlreadyVo1.setFeeRound(feeRound);
                            cwProjectFeeNoAlreadyVo1.setLawProfit(lawProfit);
                            list.add(cwProjectFeeNoAlreadyVo1);
                        }
                    }
                } else {
                    BigDecimal feeRound1 = collect.stream().map(CwProjectFeeNoAlreadyVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal lawProfit1 = collect.stream().map(CwProjectFeeNoAlreadyVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
                    CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo = value.get(0);
                    cwProjectFeeNoAlreadyVo.setPhaseId(cwProjectFeeNoAlreadyVo.getProjectIncomeId());
                    String custName = collect.get(0).getCustName();
                    String feeCustName = collect.get(0).getFeeCustName();
                    collect.clear();
                    cwProjectFeeNoAlreadyVo.setCustName(custName);
                    cwProjectFeeNoAlreadyVo.setFeeCustName(feeCustName);
                    cwProjectFeeNoAlreadyVo.setFeeRound(feeRound1);
                    cwProjectFeeNoAlreadyVo.setLawProfit(lawProfit1);
                    collect.add(cwProjectFeeNoAlreadyVo);
                    list.add(cwProjectFeeNoAlreadyVo);
                }
            });
            List<CwProjectFeeNoAlreadyVo> list1 = list.stream().filter(t -> t.getCustName() != null && t.getFeeCustName() != null).collect(Collectors.toList());
            Map<Long, List<CwProjectFeeNoAlreadyVo>> collect = list1.stream().collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectId));
            collect.forEach((key, value) -> {
                //对每一个项目的不同期次排序。
                List<CwProjectFeeNoAlreadyVo> collect1 = value.stream().sorted(Comparator.comparing(CwProjectFeeNoAlreadyVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
                resultListForLaw.addAll(collect1);
            });
            if (resultList.size() == 0 && resultListForLaw.size() == 0) {
                Map<String, Object> mm = new HashMap<>();
                mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
                mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
                returnMap.put("info", mm);
                returnMap.put("projectTypeFlag", "99999");
                return returnMap;
            } else {
                Map<String, Object> mm = new HashMap<>();
                mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
                mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
                returnMap.put("info", mm);
                returnMap.put("list", resultList);
                returnMap.put("lawList", resultListForLaw);
                returnMap.put("projectTypeFlag", "all");
                return returnMap;
            }


//            if (present) {
//                //可以查所有项目    ->     项目维度又分为两种：普通项目和法催项目
//                //查询所有正常状态的项目，包含了普通和法催。
//                List<CwProjectFeeNoAlreadyVo> resultList = new ArrayList<>();
//                List<CwProjectFeeNoAlreadyVo> resultListForLaw = new ArrayList<>();
//                //普通项目
//                List<CwProjectFeeNoAlreadyVo> cwProjectFeeNoAlreadyVos = cwProjectIncomeMapper.selectRemarkQueryDetailByQueryRemark(remark);
//                Map<Long, List<CwProjectFeeNoAlreadyVo>> normalMap = cwProjectFeeNoAlreadyVos.stream().collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectId));
//                normalMap.forEach((key, value) -> {
//                    //对每一个项目的不同期次排序。
//                    List<CwProjectFeeNoAlreadyVo> collect = value.stream().sorted(Comparator.comparing(CwProjectFeeNoAlreadyVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
//                    for (CwProjectFeeNoAlreadyVo cpfna:collect) {
//                        String replaceFlag = cpfna.getReplaceFlag();
//                        Long custId = cpfna.getProjectCustId();
//                        String feeCustName = StringUtils.EMPTY;
//                        if ("1".equals(replaceFlag)) {
//                            feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
//                        } else {
//                            feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
//                        }
//                        cpfna.setFeeCustName(feeCustName);
//                    }
//                    resultList.addAll(collect);
//                });
//                //法催项目
//                List<CwProjectFeeNoAlreadyVo> cwProjectLawFeeNoAlreadyVos = cwProjectIncomeMapper.selectLawRemarkQueryDetailByQueryRemark(remark);
//                //找法催项目的期次。
//                Map<Long, List<CwProjectFeeNoAlreadyVo>> collectMap = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getPhaseId() == null).collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectIncomeId));
//                //空集合用来装处理好的法催期次
//                List<CwProjectFeeNoAlreadyVo> list = new ArrayList<>();
//                collectMap.forEach((key, value) -> {
//                    //todo 先去找有打款记录期次id
//                    List<Long> collect1 = value.stream().filter(t -> t.getFeeAmtSum() != null && t.getFeeAmtSum().compareTo(BigDecimal.ZERO) > 0).map(CwProjectFeeNoAlreadyVo::getProjectIncomeId).collect(Collectors.toList());
//                    List<CwProjectFeeNoAlreadyVo> collect = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getPhaseId() != null && t.getPhaseId().equals(key)).collect(Collectors.toList());
//                    for (CwProjectFeeNoAlreadyVo cpfna:collect) {
//                        String replaceFlag = cpfna.getReplaceFlag();
//                        Long custId = cpfna.getProjectCustId();
//                        String feeCustName = StringUtils.EMPTY;
//                        if ("1".equals(replaceFlag)) {
//                            feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
//                        } else {
//                            feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
//                        }
//                        cpfna.setFeeCustName(feeCustName);
//                    }
//                    //todo 然后对返费取整求和   法催利润求和
//                    BigDecimal feeRound = collect.stream().map(CwProjectFeeNoAlreadyVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    BigDecimal lawProfit = collect.stream().map(CwProjectFeeNoAlreadyVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    //todo 再然后，去根据期次id去查对应的打款信息
//                    if (collect1.size() != 0) {
//                        for (Long phaseId:collect1) {
//                            List<Map<String, Object>> payInfo = cwProjectIncomeMapper.selectCwprojectLawPayInfoByPhaseId(phaseId);
//                            CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo = value.get(0);
//                            for (int i = 0; i < 1; i++) {
//                                CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo1 = new CwProjectFeeNoAlreadyVo();
//                                cwProjectFeeNoAlreadyVo1.setProjectId((Long) payInfo.get(i).get("projectId"));
//                                cwProjectFeeNoAlreadyVo1.setPhaseId((Long) payInfo.get(i).get("phaseId"));
//                                cwProjectFeeNoAlreadyVo1.setProjectIncomeId((Long) payInfo.get(i).get("projectIncomeId"));
//                                cwProjectFeeNoAlreadyVo1.setProjectFeeId((Long) payInfo.get(i).get("projectFeeId"));
//                                cwProjectFeeNoAlreadyVo1.setProjectCustId((Long) payInfo.get(i).get("custId"));
//                                cwProjectFeeNoAlreadyVo1.setProjectName(cwProjectFeeNoAlreadyVo.getProjectName());
//                                cwProjectFeeNoAlreadyVo1.setCustName((String) payInfo.get(i).get("custName"));
//                                cwProjectFeeNoAlreadyVo1.setFeeCustName((String) payInfo.get(i).get("feeCustName"));
//                                cwProjectFeeNoAlreadyVo1.setTermMonthCompare(cwProjectFeeNoAlreadyVo.getTermMonthCompare());
//                                cwProjectFeeNoAlreadyVo1.setFeeNoAlreadyTerm(cwProjectFeeNoAlreadyVo.getFeeNoAlreadyTerm());
//                                cwProjectFeeNoAlreadyVo1.setIncomeAmt(cwProjectFeeNoAlreadyVo.getIncomeAmt());
//                                cwProjectFeeNoAlreadyVo1.setRemark(cwProjectFeeNoAlreadyVo.getRemark());
//                                cwProjectFeeNoAlreadyVo1.setProjectType(cwProjectFeeNoAlreadyVo.getProjectType());
//                                cwProjectFeeNoAlreadyVo1.setFeeRound(feeRound);
//                                cwProjectFeeNoAlreadyVo1.setLawProfit(lawProfit);
//                                list.add(cwProjectFeeNoAlreadyVo1);
//                            }
//                        }
//                    } else {
//                        BigDecimal feeRound1 = collect.stream().map(CwProjectFeeNoAlreadyVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        BigDecimal lawProfit1 = collect.stream().map(CwProjectFeeNoAlreadyVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo = value.get(0);
//                        cwProjectFeeNoAlreadyVo.setPhaseId(cwProjectFeeNoAlreadyVo.getProjectIncomeId());
//                        String custName = collect.get(0).getCustName();
//                        String feeCustName = collect.get(0).getFeeCustName();
//                        collect.clear();
//                        cwProjectFeeNoAlreadyVo.setCustName(custName);
//                        cwProjectFeeNoAlreadyVo.setFeeCustName(feeCustName);
//                        cwProjectFeeNoAlreadyVo.setFeeRound(feeRound1);
//                        cwProjectFeeNoAlreadyVo.setLawProfit(lawProfit1);
//                        collect.add(cwProjectFeeNoAlreadyVo);
//                        list.add(cwProjectFeeNoAlreadyVo);
//                    }
//                });
//                List<CwProjectFeeNoAlreadyVo> list1 = list.stream().filter(t -> t.getCustName() != null && t.getFeeCustName() != null).collect(Collectors.toList());
//                Map<Long, List<CwProjectFeeNoAlreadyVo>> collect = list1.stream().collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectId));
//                collect.forEach((key, value) -> {
//                    //对每一个项目的不同期次排序。
//                    List<CwProjectFeeNoAlreadyVo> collect1 = value.stream().sorted(Comparator.comparing(CwProjectFeeNoAlreadyVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
//                    resultListForLaw.addAll(collect1);
//                });
//                if (resultList.size() == 0 && resultListForLaw.size() == 0) {
//                    Map<String, Object> mm = new HashMap<>();
//                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
//                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
//                    returnMap.put("info", mm);
//                    returnMap.put("projectTypeFlag", "99999");
//                    return returnMap;
//                } else {
//                    Map<String, Object> mm = new HashMap<>();
//                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
//                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
//                    returnMap.put("info", mm);
//                    returnMap.put("list", resultList);
//                    returnMap.put("lawList", resultListForLaw);
//                    returnMap.put("projectTypeFlag", "all");
//                    return returnMap;
//                }
//            } else {
//                List<CwProjectFeeNoAlreadyVo> resultList = new ArrayList<>();
//                List<CwProjectFeeNoAlreadyVo> resultListForLaw = new ArrayList<>();
//                //先找普通项目
//                List<CwProjectFeeNoAlreadyVo> cwProjectFeeNoAlreadyVos = cwProjectIncomeMapper.selectRemarkQueryDetailByQueryRemarkAndUserId(remark, loginUser.getUserId());
//                Map<Long, List<CwProjectFeeNoAlreadyVo>> normalMap = cwProjectFeeNoAlreadyVos.stream().collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectId));
//                normalMap.forEach((key, value) -> {
//                    //对每一个项目的不同期次排序。
//                    List<CwProjectFeeNoAlreadyVo> collect = value.stream().sorted(Comparator.comparing(CwProjectFeeNoAlreadyVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
//                    for (CwProjectFeeNoAlreadyVo cpfna:collect) {
//                        String replaceFlag = cpfna.getReplaceFlag();
//                        Long custId = cpfna.getProjectCustId();
//                        String feeCustName = StringUtils.EMPTY;
//                        if ("1".equals(replaceFlag)) {
//                            feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
//                        } else {
//                            feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
//                        }
//                        cpfna.setFeeCustName(feeCustName);
//                    }
//                    resultList.addAll(collect);
//                });
//                //再找法催项目
//                List<CwProjectFeeNoAlreadyVo> cwProjectLawFeeNoAlreadyVos = cwProjectIncomeMapper.selectLawRemarkQueryDetailByQueryRemarkAndUserId(remark, loginUser.getUserId());
//                //找法催项目的期次。
//                Map<Long, List<CwProjectFeeNoAlreadyVo>> collectMap = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getPhaseId() == null).collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectIncomeId));
//                //空集合用来装处理好的法催期次
//                List<CwProjectFeeNoAlreadyVo> list = new ArrayList<>();
//                collectMap.forEach((key, value) -> {
//                    //todo 先去找有打款记录期次id
//                    List<Long> collect1 = value.stream().filter(t -> t.getFeeAmtSum() != null && t.getFeeAmtSum().compareTo(BigDecimal.ZERO) > 0).map(CwProjectFeeNoAlreadyVo::getProjectIncomeId).collect(Collectors.toList());
//                    List<CwProjectFeeNoAlreadyVo> collect = cwProjectLawFeeNoAlreadyVos.stream().filter(t -> t.getPhaseId() != null && t.getPhaseId().equals(key)).collect(Collectors.toList());
//                    for (CwProjectFeeNoAlreadyVo cpfna:collect) {
//                        String replaceFlag = cpfna.getReplaceFlag();
//                        Long custId = cpfna.getProjectCustId();
//                        String feeCustName = StringUtils.EMPTY;
//                        if ("1".equals(replaceFlag)) {
//                            feeCustName = cwProjectCustMapper.selectCwprojectFeeNameByCustId(custId);
//                        } else {
//                            feeCustName = cwProjectCustMapper.selectOatraderUserNameByCustId(custId);
//                        }
//                        cpfna.setFeeCustName(feeCustName);
//                    }
//                    //todo 然后对返费取整求和   法催利润求和
//                    BigDecimal feeRound = collect.stream().map(CwProjectFeeNoAlreadyVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    BigDecimal lawProfit = collect.stream().map(CwProjectFeeNoAlreadyVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    //todo 再然后，去根据期次id去查对应的打款信息
//                    if (collect1.size() != 0) {
//                        for (Long phaseId:collect1) {
//                            List<Map<String, Object>> payInfo = cwProjectIncomeMapper.selectCwprojectLawPayInfoByPhaseId(phaseId);
//                            CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo = value.get(0);
//                            for (int i = 0; i < 1; i++) {
//                                CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo1 = new CwProjectFeeNoAlreadyVo();
//                                cwProjectFeeNoAlreadyVo1.setProjectId((Long) payInfo.get(i).get("projectId"));
//                                cwProjectFeeNoAlreadyVo1.setPhaseId((Long) payInfo.get(i).get("phaseId"));
//                                cwProjectFeeNoAlreadyVo1.setProjectIncomeId((Long) payInfo.get(i).get("projectIncomeId"));
//                                cwProjectFeeNoAlreadyVo1.setProjectFeeId((Long) payInfo.get(i).get("projectFeeId"));
//                                cwProjectFeeNoAlreadyVo1.setProjectCustId((Long) payInfo.get(i).get("custId"));
//                                cwProjectFeeNoAlreadyVo1.setProjectName(cwProjectFeeNoAlreadyVo.getProjectName());
//                                cwProjectFeeNoAlreadyVo1.setCustName((String) payInfo.get(i).get("custName"));
//                                cwProjectFeeNoAlreadyVo1.setFeeCustName((String) payInfo.get(i).get("feeCustName"));
//                                cwProjectFeeNoAlreadyVo1.setTermMonthCompare(cwProjectFeeNoAlreadyVo.getTermMonthCompare());
//                                cwProjectFeeNoAlreadyVo1.setFeeNoAlreadyTerm(cwProjectFeeNoAlreadyVo.getFeeNoAlreadyTerm());
//                                cwProjectFeeNoAlreadyVo1.setIncomeAmt(cwProjectFeeNoAlreadyVo.getIncomeAmt());
//                                cwProjectFeeNoAlreadyVo1.setRemark(cwProjectFeeNoAlreadyVo.getRemark());
//                                cwProjectFeeNoAlreadyVo1.setProjectType(cwProjectFeeNoAlreadyVo.getProjectType());
//                                cwProjectFeeNoAlreadyVo1.setFeeRound(feeRound);
//                                cwProjectFeeNoAlreadyVo1.setLawProfit(lawProfit);
//                                list.add(cwProjectFeeNoAlreadyVo1);
//                            }
//                        }
//                    } else {
//                        BigDecimal feeRound1 = collect.stream().map(CwProjectFeeNoAlreadyVo::getFeeRound).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        BigDecimal lawProfit1 = collect.stream().map(CwProjectFeeNoAlreadyVo::getLawProfit).filter(profit -> profit != null).reduce(BigDecimal.ZERO, BigDecimal::add);
//                        CwProjectFeeNoAlreadyVo cwProjectFeeNoAlreadyVo = value.get(0);
//                        cwProjectFeeNoAlreadyVo.setPhaseId(cwProjectFeeNoAlreadyVo.getProjectIncomeId());
//                        String custName = collect.get(0).getCustName();
//                        String feeCustName = collect.get(0).getFeeCustName();
//                        collect.clear();
//                        cwProjectFeeNoAlreadyVo.setCustName(custName);
//                        cwProjectFeeNoAlreadyVo.setFeeCustName(feeCustName);
//                        cwProjectFeeNoAlreadyVo.setFeeRound(feeRound1);
//                        cwProjectFeeNoAlreadyVo.setLawProfit(lawProfit1);
//                        collect.add(cwProjectFeeNoAlreadyVo);
//                        list.add(cwProjectFeeNoAlreadyVo);
//                    }
//                });
//                List<CwProjectFeeNoAlreadyVo> list1 = list.stream().filter(t -> t.getCustName() != null && t.getFeeCustName() != null).collect(Collectors.toList());
//                Map<Long, List<CwProjectFeeNoAlreadyVo>> collect = list1.stream().collect(Collectors.groupingBy(CwProjectFeeNoAlreadyVo::getProjectId));
//                collect.forEach((key, value) -> {
//                    //对每一个项目的不同期次排序。
//                    List<CwProjectFeeNoAlreadyVo> collect1 = value.stream().sorted(Comparator.comparing(CwProjectFeeNoAlreadyVo::getTermMonthCompare).reversed()).collect(Collectors.toList());
//                    resultListForLaw.addAll(collect1);
//                });
//                if (resultList.size() == 0 && resultListForLaw.size() == 0) {
//                    Map<String, Object> mm = new HashMap<>();
//                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.NOT_FOUND.getCode());
//                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.NOT_FOUND.getMsg());
//                    returnMap.put("info", mm);
//                    returnMap.put("projectTypeFlag", "99999");
//                    return returnMap;
//                } else {
//                    Map<String, Object> mm = new HashMap<>();
//                    mm.put(CwxmglConstants.ERROR_CODE, CwxmglEnum.SUCCESS.getCode());
//                    mm.put(CwxmglConstants.ERROR_MSG, CwxmglEnum.SUCCESS.getMsg());
//                    returnMap.put("info", mm);
//                    returnMap.put("list", resultList);
//                    returnMap.put("lawList", resultListForLaw);
//                    returnMap.put("projectTypeFlag", "all");
//                    return returnMap;
//                }
//            }
        }
    }

    @Override
    public Map<String, Object> selectPrestoreIncomeListByProjectId(Long projectId) {
        Map<String, Object> resultMap = new HashMap<>();
        List<CwProjectPrestoreIncome> cwProjectPrestoreIncomes = cwProjectCustMapper.selectPrestoreIncomeListByProjectId(projectId);
        if (cwProjectPrestoreIncomes.size() != 0) {
            //索引为0的数据固定就是这样，所以在遍历的时候从索引为1的数据开始遍历使用。
            if ("0".equals(cwProjectPrestoreIncomes.get(0).getFlag())) {
                cwProjectPrestoreIncomes.get(0).setPrestoreBalance(cwProjectPrestoreIncomes.get(0).getPrestoreAmt());
            } else if ("1".equals(cwProjectPrestoreIncomes.get(0).getFlag())) {
                cwProjectPrestoreIncomes.get(0).setPrestoreBalance(new BigDecimal("0.00").subtract(cwProjectPrestoreIncomes.get(0).getDeductionIncomeAmt()));
            }
            for (int i = 1; i < cwProjectPrestoreIncomes.size(); i++) {
                String flag = cwProjectPrestoreIncomes.get(i).getFlag();
                if ("0".equals(flag)) {
                    //说明该对象是预存对象。那么就在上一个对象的账户余额基础上加
                    //这个对象预存金额的加数
                    BigDecimal prestoreAmt = cwProjectPrestoreIncomes.get(i).getPrestoreAmt();
                    //上个对象的账户余额加数
                    BigDecimal prestoreBalance = cwProjectPrestoreIncomes.get(i - 1).getPrestoreBalance();
                    cwProjectPrestoreIncomes.get(i).setPrestoreBalance(prestoreAmt.add(prestoreBalance));
                } else if ("1".equals(flag)) {
                    //说明该对象是抵扣对象。那么就在上一个对象的账户余额基础上减
                    //这个对象抵扣金额的减数
                    BigDecimal deductionIncomeAmt = cwProjectPrestoreIncomes.get(i).getDeductionIncomeAmt();
                    //上个对象的账户余额的被减数
                    BigDecimal prestoreBalance = cwProjectPrestoreIncomes.get(i - 1).getPrestoreBalance();
                    cwProjectPrestoreIncomes.get(i).setPrestoreBalance(prestoreBalance.subtract(deductionIncomeAmt));
                }
            }
            //求各种合计值
            //预存金额合计值
            BigDecimal prestoreAmtSum = null;
            if (cwProjectPrestoreIncomes.stream().filter(t -> "0".equals(t.getFlag())).collect(Collectors.toList()).size() != 0) {
                prestoreAmtSum = cwProjectPrestoreIncomes.stream().filter(t -> "0".equals(t.getFlag())).map(CwProjectPrestoreIncome::getPrestoreAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            //抵扣金额合计值
            BigDecimal deductionIncomeAmtSum = null;
            if (cwProjectPrestoreIncomes.stream().filter(t -> "1".equals(t.getFlag())).collect(Collectors.toList()).size() != 0) {
                deductionIncomeAmtSum = cwProjectPrestoreIncomes.stream().filter(t -> "1".equals(t.getFlag())).map(CwProjectPrestoreIncome::getDeductionIncomeAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            //预存账户余额合计值  ------->  这个不是简单的求和，这个是有加有减。加减过程在上面的for里已经完成了。所以这个合计就是最后一个对象的账户余额
            BigDecimal prestoreBalanceSum = null;
            if (cwProjectPrestoreIncomes.size() != 0) {
                prestoreBalanceSum = cwProjectPrestoreIncomes.get(cwProjectPrestoreIncomes.size() - 1).getPrestoreBalance();
            }
            //然后对之前做好处理的进行排序。
            Collections.reverse(cwProjectPrestoreIncomes);
            resultMap.put("info", cwProjectPrestoreIncomes);
            resultMap.put("prestoreAmtSum", prestoreAmtSum);
            resultMap.put("deductionIncomeAmtSum", deductionIncomeAmtSum);
            resultMap.put("prestoreBalanceSum", prestoreBalanceSum);
            return resultMap;
        } else {
            //集合为空
            resultMap.put("info", cwProjectPrestoreIncomes);
            resultMap.put("prestoreAmtSum", null);
            resultMap.put("deductionIncomeAmtSum", null);
            resultMap.put("prestoreBalanceSum", null);
            return resultMap;
        }
    }

    @Override
    public List<CwProjectPrestoreIncome> selectPrestoreIncomeListForInByProjectId(Long projectId) {
        return cwProjectCustMapper.selectPrestoreIncomeListForInByProjectId(projectId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertPrestoreIncomeList(List<CwProjectPrestoreIncome> cwProjectPrestoreIncomes, LoginUser loginUser) {
        List<CwProjectPrestoreIncome> addList = cwProjectPrestoreIncomes.stream().filter(t -> t.getId() == null).collect(Collectors.toList());
        List<CwProjectPrestoreIncome> updateList = cwProjectPrestoreIncomes.stream().filter(t -> t.getId() != null).collect(Collectors.toList());
        String nickName = loginUser.getUser().getNickName();
        if (updateList.size() != 0) {
            //删除不在更新集合内的数据
            List<Long> idList = cwProjectCustMapper.selectDeleteCwProjectPrestoreIncomeById(updateList);
            if (idList.size() != 0) {
                int i = cwProjectCustMapper.deleteCwProjectPrestoreIncomeById(idList);
            }
            //有更新的，调用更新语句
            updateList.forEach(t -> {
                t.setUpdateBy(nickName);
                t.setUpdateTime(DateUtils.getNowDate());
            });
            int updateRows = cwProjectCustMapper.updateCwProjectPrestoreIncomeList(updateList);
        }
        if (addList.size() != 0) {
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH,false);
            CwProjectCustMapper mapper = sqlSession.getMapper(CwProjectCustMapper.class);
            //新增集合不为0，那么就新增
            for (CwProjectPrestoreIncome cppi:addList) {
                Date nowDate = DateUtils.getNowDate();
                cppi.setFlag("0");
                cppi.setStatus("0");
                cppi.setCreateBy(nickName);
                cppi.setUpdateBy(nickName);
                cppi.setCreateTime(nowDate);
                cppi.setUpdateTime(nowDate);
                int i = mapper.insertPrestoreIncome(cppi);
            }
            sqlSession.commit();
            sqlSession.clearCache();
            sqlSession.close();
        }
        return 1;
    }

    @Override
    public Map<String, Object> getProjectSumByProjectId(Long projectId) {
        CwProject cwProject = cwProjectMapper.selectCwProjectById(projectId);
        BigDecimal incomeAmt = cwProjectCustMapper.selectCwprojectIncomeAmtByProject(projectId);
        //找正常期次id集合
        List<Long> incomeIdList = cwProjectCustMapper.queryIncomeIdListByProjectId(projectId);
        //2024.10.28找正常期次的返费集合
        BigDecimal feeAmt = BigDecimal.ZERO;
        if (incomeIdList.size() != 0) {
            feeAmt = cwProjectCustMapper.selectCwprojectActuallyPayFeeAmtByProject(projectId, incomeIdList);
        }
        //通过项目id去找OA完结的已支付的金额
        BigDecimal feeAlreadyPay = oaPayRebateRecordMapper.selectOaPayRebateRecordByProjectId(cwProject.getOaProjectDeployId());
        if (feeAlreadyPay == null) {
            feeAlreadyPay = BigDecimal.ZERO;
        }
//        BigDecimal feeAlreadyPay = new BigDecimal("2000.00");
        //返费合计 - 返费已支付 = 返费未结清
        BigDecimal feeNoAlreadyPay = feeAmt.subtract(feeAlreadyPay);
        //封装对象返回
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("incomeAmt", incomeAmt);
        resultMap.put("feeAmt", feeAmt);
        resultMap.put("feeAlreadyPay", feeAlreadyPay);
        resultMap.put("feeNoAlreadyPay", feeNoAlreadyPay);
        return resultMap;
    }

    @Override
    public Map<String, Object> getLawProjectSumByProjectId(Long projectId) {
        CwProject cwProject = cwProjectMapper.selectCwProjectById(projectId);
        BigDecimal incomeAmt = cwProjectCustMapper.selectLawCwprojectIncomeAmtByProject(projectId);
        BigDecimal feeAmt = cwProjectCustMapper.selectLawCwprojectActuallyPayFeeAmtByProject(projectId);
        //通过项目id去找OA完结的已支付的金额
        BigDecimal feeAlreadyPay = oaPayRebateRecordMapper.selectOaPayRebateRecordByProjectId(cwProject.getOaProjectDeployId());
        if (feeAlreadyPay == null) {
            feeAlreadyPay = BigDecimal.ZERO;
        }
//        BigDecimal feeAlreadyPay = new BigDecimal("2000.00");
        //返费合计 - 返费已支付 = 返费未结清
        BigDecimal feeNoAlreadyPay = feeAmt.subtract(feeAlreadyPay);
        //封装对象返回
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("incomeAmt", incomeAmt);
        resultMap.put("feeAmt", feeAmt);
        resultMap.put("feeAlreadyPay", feeAlreadyPay);
        resultMap.put("feeNoAlreadyPay", feeNoAlreadyPay);
        return resultMap;
    }

    @Override
    public Map<String, Object> getProjectCertificateFlagByProjectId(Long projectId) {
        Map<String, Object> certficateFlagInfo = cwProjectCustMapper.selectCwprojectCertficateFlagInfo(projectId);
        return certficateFlagInfo;
    }

    @Override
    public Map<String, Object> getProjectFeeCompanyInfoByProjectId(Long projectId) {
        CwProject cwProject = cwProjectMapper.selectCwProjectById(projectId);
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        //先判断是否有替换的
        List<Map<String, Object>> projectFeeCompanyInfoList = cwProjectCustMapper.selectProjectFeeCompanyInfoByProjectId(projectId);
        //发生替换的条数
        long replaceFlag = projectFeeCompanyInfoList.stream().filter(t -> "1".equals(t.get("replaceFlag"))).count();
        //没有发生替换的公司
        List<Map<String, Object>> replaceFlagIsZeroList = projectFeeCompanyInfoList.stream().filter(t -> "0".equals(t.get("replaceFlag"))).collect(Collectors.toList());
        if (replaceFlag > 0L) {
            //有替换,那么去找被替换的最新的信息
            List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByProjectId(projectId);
            if ("1".equals(cwProject.getProjectType())) {
                Map<Long, List<CwProjectReplaceFeeCompanyInfo>> collect = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> "0".equals(t.getStatus())).collect(Collectors.groupingBy(CwProjectReplaceFeeCompanyInfo::getOaTraderId));
                collect.forEach((k, v) -> {
                    List<CwProjectReplaceFeeCompanyInfo> collect1 = v.stream().sorted(Comparator.comparing(CwProjectReplaceFeeCompanyInfo::getReplaceTime).reversed()).collect(Collectors.toList());
                    CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo = collect1.get(0);
                    Map<String, Object> map = new HashMap<>();
                    map.put("projectId", cwProjectReplaceFeeCompanyInfo.getProjectId());
                    map.put("replaceFlag", "1");
                    map.put("oaTraderId", cwProjectReplaceFeeCompanyInfo.getOaTraderId());
                    map.put("oldOaTraderId", cwProjectReplaceFeeCompanyInfo.getNewOaTraderId());
                    map.put("userName", cwProjectReplaceFeeCompanyInfo.getNewOaTraderUserName());
                    map.put("schemeFlag", cwProjectReplaceFeeCompanyInfo.getSchemeFlag());
                    list.add(map);
                });
            } else {
                //            Map<Long, List<CwProjectReplaceFeeCompanyInfo>> collect = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> "0".equals(t.getStatus())).collect(Collectors.groupingBy(CwProjectReplaceFeeCompanyInfo::getOaTraderId));
                Map<Long, Map<String, List<CwProjectReplaceFeeCompanyInfo>>> collect = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> "0".equals(t.getStatus())).collect(Collectors.groupingBy(CwProjectReplaceFeeCompanyInfo::getOaTraderId, Collectors.groupingBy(CwProjectReplaceFeeCompanyInfo::getSchemeFlag)));
                collect.forEach((k1, v1) -> {
                    v1.forEach((k, v) -> {
                        List<CwProjectReplaceFeeCompanyInfo> collect1 = v.stream().sorted(Comparator.comparing(CwProjectReplaceFeeCompanyInfo::getReplaceTime).reversed()).collect(Collectors.toList());
                        CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo = collect1.get(0);
                        Map<String, Object> map = new HashMap<>();
                        map.put("projectId", cwProjectReplaceFeeCompanyInfo.getProjectId());
                        map.put("replaceFlag", "1");
                        map.put("oaTraderId", cwProjectReplaceFeeCompanyInfo.getOaTraderId());
                        map.put("oldOaTraderId", cwProjectReplaceFeeCompanyInfo.getNewOaTraderId());
                        map.put("userName", cwProjectReplaceFeeCompanyInfo.getNewOaTraderUserName());
                        map.put("schemeFlag", cwProjectReplaceFeeCompanyInfo.getSchemeFlag());
                        list.add(map);
                    });
                });
            }
            //把没有发生替换的给装入集合之中
            list.addAll(replaceFlagIsZeroList);
            resultMap.put("feeCompanyInfo", list);
        } else {
            //没有替换的话直接返回给前端展示
            resultMap.put("feeCompanyInfo", projectFeeCompanyInfoList);
        }
        return resultMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addNewReplaceFeeCompanyInfo(CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo, LoginUser loginUser) {
        cwProjectReplaceFeeCompanyInfo.setReplaceTime(DateUtils.getNowDate());
        cwProjectReplaceFeeCompanyInfo.setStatus("0");
        cwProjectReplaceFeeCompanyInfo.setReplaceBy(loginUser.getUsername());
        cwProjectReplaceFeeCompanyInfo.setReplacePersonId(loginUser.getUserId());
        //获取要替换新返费公司的期次集合
        List<Long> replaceIncomeIdList = cwProjectReplaceFeeCompanyInfo.getReplaceIncomeIdList();
        String projectReplaceType = cwProjectReplaceFeeCompanyInfo.getProjectReplaceType();
        //查询当前除了替换新返费公司的期次集合外，其余的期次id
        List<Long> notReplaceIncomeIdList = cwProjectFeeMapper.queryProjectNotReplaceIncomeIdListByReplaceIncomeList(cwProjectReplaceFeeCompanyInfo.getProjectId(), replaceIncomeIdList, projectReplaceType);
        //不管已完成的期次发不发生替换，都把没有要选择的期次进行老的替换
        if (notReplaceIncomeIdList.size() > 0) {
            Long oaTraderId = cwProjectReplaceFeeCompanyInfo.getOaTraderId();
            for (Long incomeId : notReplaceIncomeIdList) {
                int a = cwProjectFeeMapper.updateCwProjectFeeRevealFeeCompanyIdByIncomeIdAndOaTraderId(incomeId, oaTraderId, projectReplaceType);
            }
        }
        //获取替换的新公司
        Long newOaTraderId = cwProjectReplaceFeeCompanyInfo.getNewOaTraderId();
        if ("1".equals(cwProjectReplaceFeeCompanyInfo.getReplaceHistoryIncomeFlag())) {
            for (Long incomeId : replaceIncomeIdList) {
                int a = cwProjectFeeMapper.updateCwProjectFeeRevealFeeCompanyIdByIncomeIdAndOaTraderId(incomeId, newOaTraderId, projectReplaceType);
            }
        }
        int i = cwProjectCustMapper.insertCwProjectReplaceFeeCompanyInfo(cwProjectReplaceFeeCompanyInfo);
        if (i > 0) {
            int a = cwProjectCustMapper.updateCwProjectCustReplaceFlagByProjectIdAndOaTraderId(cwProjectReplaceFeeCompanyInfo.getProjectId(), cwProjectReplaceFeeCompanyInfo.getOaTraderId(), "1", cwProjectReplaceFeeCompanyInfo.getSchemeFlag());
        }
        return i;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int revocationReplaceFeeCompanyInfo(CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo) {
        Long projectId = cwProjectReplaceFeeCompanyInfo.getProjectId();
        Long oaTraderId = cwProjectReplaceFeeCompanyInfo.getOaTraderId();
        //B换A，C换B。B换A撤销，相当于直接从A换到了C。B=A C=B B作为中间介质没有了，那么相当于直接A=C
        //撤销时，先取消之前展示的项目id
        Long oldOaTraderId = cwProjectReplaceFeeCompanyInfo.getOldOaTraderId();
        Long newOaTraderId = cwProjectReplaceFeeCompanyInfo.getNewOaTraderId();
        //先判断oldOaTraderId跟oaTraderId是否一致，如果是一致的，说明是可以直接撤回的
        if (oaTraderId.equals(oldOaTraderId)) {
            int b = cwProjectFeeMapper.updateCwProjectFeeRevealFeeCompanyIdIsNullByProjectIdAndOldOaTraderId(projectId, oldOaTraderId, newOaTraderId);
        } else {
            //找该返费公司的替换记录
            List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByProjectIdAndOaTraderId(projectId, oaTraderId, cwProjectReplaceFeeCompanyInfo.getSchemeFlag());
            //找第一个oaTraderId和oldOaTraderId一致的，记录好索引
            CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo1 = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> t.getOaTraderId().equals(t.getOldOaTraderId())).findFirst().orElse(null);
            int i = cwProjectReplaceFeeCompanyInfos.indexOf(cwProjectReplaceFeeCompanyInfo1);
            //然后通过前端穿过来的id，找到开始的索引
            CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo2 = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> t.getId().equals(cwProjectReplaceFeeCompanyInfo.getId())).findFirst().orElse(null);
            int b = cwProjectReplaceFeeCompanyInfos.indexOf(cwProjectReplaceFeeCompanyInfo2);
            b = b+1;
            //然后对替换记录进行for循环，结束的点就是这个index
            Long oldOaTraderIdRecently = null;
            for (int o = b; o <= i; o++) {
                CwProjectReplaceFeeCompanyInfo cwProjectReplaceFeeCompanyInfo3 = cwProjectReplaceFeeCompanyInfos.get(b);
                if ("0".equals(cwProjectReplaceFeeCompanyInfo3.getStatus())) {
                    oldOaTraderIdRecently = cwProjectReplaceFeeCompanyInfo3.getNewOaTraderId();
                    break;
                }
            }
            if (oldOaTraderIdRecently == null) {
                oldOaTraderIdRecently = oaTraderId;
            }
            int e1 = cwProjectFeeMapper.updateCwProjectFeeRevealFeeCompanyIdIsNullByProjectIdAndOldOaTraderId(projectId, oldOaTraderIdRecently, newOaTraderId);
        }
        //先根据id修改状态为1 - 禁用（替换的已经被撤销时为禁用）
        cwProjectReplaceFeeCompanyInfo.setStatus("1");
        int i = cwProjectCustMapper.updateCwprojectReplaceFeeCompanyInfo(cwProjectReplaceFeeCompanyInfo);
        //禁用成功之后，再根据项目id和oaTraderId查是否还有状态为0 - 正常的替换记录。
        //如果没有，说明已经没有替换了。那么就把cust表的替换标识给修改了
        List<CwProjectReplaceFeeCompanyInfo> cwProjectReplaceFeeCompanyInfos = cwProjectCustMapper.selectCwProjectReplaceFeeCompanyInfoListByProjectIdAndOaTraderId(projectId, oaTraderId, cwProjectReplaceFeeCompanyInfo.getSchemeFlag());
        long count = cwProjectReplaceFeeCompanyInfos.stream().filter(t -> "0".equals(t.getStatus())).count();
        if (count == 0L) {
            //修改cust表的替换标识给修改成0 - 否，即未替换
            int a = cwProjectCustMapper.updateCwProjectCustReplaceFlagByProjectIdAndOaTraderId(projectId, oaTraderId, "0", cwProjectReplaceFeeCompanyInfo.getSchemeFlag());
        }
        return i;
    }

    @Override
    public List<OaProjectDeploy> getFilterOaProjectDeployList(LoginUser loginUser) {
        //2024.06.07首先调用权限公用方法
        List<Long> oaProjectDeployIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.PROJNAME.getCode());
        //得到了相关的权限之后，去查看现有财务项目管理主表，然后再与新的项目名称还有关系表进行过滤
//        CwProject cwProject = new CwProject();
//        List<CwProject> cwProjects = cwProjectMapper.selectCwProjectList(cwProject);
        //找新的项目名称与关系表
        List<Map<String, Object>> mapList = cwProjectCustMapper.selectOaProjectDeployAndProjectType(oaProjectDeployIds, null);
        List<Long> oaProjectDeployIdList = mapList.stream().map(t -> Long.parseLong(t.get("id").toString())).distinct().collect(Collectors.toList());
        if (oaProjectDeployIdList.size() == 0) {
            return new ArrayList<>();
        }
        return oaProjectDeployMapper.selectOaProjectDeployListByOaProjectDeployIdList(oaProjectDeployIdList);
//        List<Long> oaProjectDeployIdList = cwProjects.stream().map(CwProject::getOaProjectDeployId).filter(t -> t != null).collect(Collectors.toList());
//        if (oaProjectDeployIdList.size() == 0) {
//            return oaProjectDeployMapper.getAllOaProjectDeployList();
//        } else {
//            return oaProjectDeployMapper.getFilterOaProjectDeployList(oaProjectDeployIdList);
//        }
    }

    @Override
    public List<OaPayRebateRecordVo> getFlowAlreadyPayInfoFromOA(Long projectId) {
        CwProject cwProject = cwProjectMapper.selectCwProjectById(projectId);
        if (cwProject == null) {
            return new ArrayList<>();
        }
        Long oaProjectDeployId = cwProject.getOaProjectDeployId();
        List<OaPayRebateRecordVo> list = oaPayRebateRecordMapper.getFlowAlreadyPayInfoFromOA(oaProjectDeployId);
        //找打款
        if ("1".equals(cwProject.getProjectType())) {
            //是法催
            //打款前
            BigDecimal bigDecimal = cwProjectCustMapper.selectLawCwprojectActuallyPayFeeAmtByProject(projectId);
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    list.get(i).setPayBefore(bigDecimal);
                    list.get(i).setPayAfter(bigDecimal.subtract(list.get(i).getAmount()));
                } else {
                    BigDecimal payBeforeNow = list.get(i - 1).getPayAfter();
                    list.get(i).setPayBefore(payBeforeNow);
                    list.get(i).setPayAfter(payBeforeNow.subtract(list.get(i).getAmount()));
                }
            }
            Collections.reverse(list);
            return list;
        } else {
            //是其他三个项目
            //打款前
            //找正常期次id集合
            List<Long> incomeIdList = cwProjectCustMapper.queryIncomeIdListByProjectId(projectId);
            //2024.10.28找正常期次的返费集合
            BigDecimal bigDecimal = BigDecimal.ZERO;
            if (incomeIdList.size() != 0) {
                bigDecimal = cwProjectCustMapper.selectCwprojectActuallyPayFeeAmtByProject(projectId, incomeIdList);
            }
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    list.get(i).setPayBefore(bigDecimal);
                    list.get(i).setPayAfter(bigDecimal.subtract(list.get(i).getAmount()));
                } else {
                    BigDecimal payBeforeNow = list.get(i - 1).getPayAfter();
                    list.get(i).setPayBefore(payBeforeNow);
                    list.get(i).setPayAfter(payBeforeNow.subtract(list.get(i).getAmount()));
                }
            }
            Collections.reverse(list);
            return list;
        }
//        return null;
    }

    @Override
    public int updateFeeCompany(List<CwProjectFee> cwProjectFeeList) {
        //2024.10.24在新增返费公司的时候，对revealFeeCompanyId进行赋值
        //具体赋值步骤：通过现在的custId，去找找看是否有替换的返费公司在，如果有，那么就赋给最新的oaTraderId，如果没有，那么就用custId所对应的oaTraderId
        //找是否有替换的标识
        Long projectId = cwProjectFeeList.get(0).getProjectId();
        CwProjectCust cwProjectCust = new CwProjectCust();
        cwProjectCust.setProjectId(projectId);
        List<CwProjectCust> cwProjectCusts = cwProjectCustMapper.selectCwProjectCustList(cwProjectCust);
        int a = 0;
        for (CwProjectFee cwProjectFee:cwProjectFeeList) {
            Long custId = cwProjectFee.getCustId();
            CwProjectCust cwProjectCust1 = cwProjectCusts.stream().filter(t -> t.getId().equals(custId)).findFirst().orElse(null);
            if ("1".equals(cwProjectCust1.getReplaceFlag())) {
                //发生过替换，那么找最新的一条替换记录中的newOaTraderId
                Long oaTraderId = cwProjectCust1.getOaTraderId();
                Long newOaTraderId = cwProjectCustMapper.queryLatestNewOaTraderIdByProjectIdAndOaTraderIdAndStatus(projectId, oaTraderId, "0");
                cwProjectFee.setRevealFeeCompanyId(newOaTraderId);
            }
            int i = cwProjectFeeMapper.updateCwProjectFee(cwProjectFee);
            if (i > 0) {
                a++;
            }
        }
        return a;
    }

    @Override
    public List<Map<String, Object>> getSelectProjectTypeByOaProjectDeployId(Long oaProjectDeployId) {
        return cwProjectCustMapper.selectOaProjectDeployAndProjectType(null, oaProjectDeployId);
    }
}
