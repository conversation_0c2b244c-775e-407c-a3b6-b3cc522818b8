package org.ruoyi.core.cwproject.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.cwproject.domain.CwProjectFee;
import org.ruoyi.core.cwproject.domain.CwProjectFee;

import java.util.List;
import java.util.Map;

/**
 * 财务项目管理-返费Service接口
 *
 * <AUTHOR>
 * @date 2022-11-10
 */
public interface ICwProjectFeeService
{
    /**
     * 查询财务项目管理-返费
     *
     * @param id 财务项目管理-返费主键
     * @return 财务项目管理-返费
     */
    public CwProjectFee selectCwProjectFeeById(Long id);

    /**
     * 查询财务项目管理-返费列表
     *
     * @param CwProjectFee 财务项目管理-返费
     * @return 财务项目管理-返费集合
     */
    public List<CwProjectFee> selectCwProjectFeeList(CwProjectFee CwProjectFee);

    /**
     * 新增财务项目管理-返费
     *
     * @param CwProjectFee 财务项目管理-返费
     * @return 结果
     */
    public int insertCwProjectFee(CwProjectFee CwProjectFee);

    /**
     * 修改财务项目管理-返费
     *
     * @param cwProjectFee 财务项目管理-返费
     * @return 结果
     */
    public int updateCwProjectFee(CwProjectFee cwProjectFee);

    /**
     * 批量删除财务项目管理-返费
     *
     * @param ids 需要删除的财务项目管理-返费主键集合
     * @return 结果
     */
    public int deleteCwProjectFeeByIds(Long[] ids);

    /**
     * 删除财务项目管理-返费信息
     *
     * @param id 财务项目管理-返费主键
     * @return 结果
     */
    public int deleteCwProjectFeeById(Long id);

    /**
     * 查询财务项目管理-待录入返费状态
     *
     * @param cwProjectFee 财务项目管理-返费
     * @return 财务项目管理-返费集合
     */
    List<Map<String, Object>> selectCwProjectFeeListFlagZero(CwProjectFee cwProjectFee, Long userId, LoginUser loginUser);

    /**
     * 查询财务项目管理-已录入未确认返费状态
     *
     * @param cwProjectFee 财务项目管理-返费
     * @return 财务项目管理-返费集合
     */
    List<Map<String, Object>> selectCwProjectFeeListFlagOne(CwProjectFee cwProjectFee, Long userId, LoginUser loginUser);

    List<CwProjectFee> selectCwProjectFeeListAll();
}
