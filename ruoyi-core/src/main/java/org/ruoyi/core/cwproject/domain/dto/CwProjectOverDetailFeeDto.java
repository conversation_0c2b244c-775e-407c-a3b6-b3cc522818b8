package org.ruoyi.core.cwproject.domain.dto;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 财务项目管理-完结项目归档查询详情-返费子对象 CwProjectOverDetailFeeDto
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@Getter
@Setter
@ToString
public class CwProjectOverDetailFeeDto extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 出返费公司 */
    private String custName;

    /** 返费公司 */
    private String feeCustName;

    /** 返费 */
    private BigDecimal feeAmt;

    /** 提成返费 */
    private BigDecimal feeAmt2;

    /** 打款状态 */
    private String payStatus;

    /** 提成日期 */
    private String payDate;

    /** 实际打款金额 */
    private BigDecimal payAmt;

    /** 抹平差额 */
    private BigDecimal differenceAmt;
}
