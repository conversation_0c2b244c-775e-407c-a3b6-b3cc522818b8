package org.ruoyi.core.cwproject.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.cwproject.domain.CwProjectUser;
import org.ruoyi.core.cwproject.service.ICwProjectUserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 财务项目管理-成员Controller
 *
 * <AUTHOR>
 * @date 2022-11-12
 */
@RestController
@RequestMapping("/system/users")
public class CwProjectUserController extends BaseController
{
    @Autowired
    private ICwProjectUserService ICwProjectUserService;

    /**
     * 查询财务项目管理-成员列表
     */
    @PreAuthorize("@ss.hasPermi('system:users:list')")
    @GetMapping("/list")
    public TableDataInfo list(CwProjectUser cwProjectUser)
    {
        startPage();
        List<CwProjectUser> list = ICwProjectUserService.selectCwProjectUserList(cwProjectUser);
        return getDataTable(list);
    }

    /**
     * 导出财务项目管理-成员列表
     */
    @PreAuthorize("@ss.hasPermi('system:users:export')")
    @Log(title = "财务项目管理-成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CwProjectUser cwProjectUser)
    {
        List<CwProjectUser> list = ICwProjectUserService.selectCwProjectUserList(cwProjectUser);
        ExcelUtil<CwProjectUser> util = new ExcelUtil<CwProjectUser>(CwProjectUser.class);
        util.exportExcel(response, list, "财务项目管理-成员数据");
    }

    /**
     * 获取财务项目管理-成员详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:users:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(ICwProjectUserService.selectCwProjectUserById(id));
    }

    /**
     * 新增财务项目管理-成员
     */
    @PreAuthorize("@ss.hasPermi('system:users:add')")
    @Log(title = "财务项目管理-成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CwProjectUser cwProjectUser)
    {
        return toAjax(ICwProjectUserService.insertCwProjectUser(cwProjectUser));
    }

    /**
     * 修改财务项目管理-成员
     */
    @PreAuthorize("@ss.hasPermi('system:users:edit')")
    @Log(title = "财务项目管理-成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CwProjectUser cwProjectUser)
    {
        return toAjax(ICwProjectUserService.updateCwProjectUser(cwProjectUser));
    }

    /**
     * 删除财务项目管理-成员
     */
    @PreAuthorize("@ss.hasPermi('system:users:remove')")
    @Log(title = "财务项目管理-成员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ICwProjectUserService.deleteCwProjectUserByIds(ids));
    }
}
