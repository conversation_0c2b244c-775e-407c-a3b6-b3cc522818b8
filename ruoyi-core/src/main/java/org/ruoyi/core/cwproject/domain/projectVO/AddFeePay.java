package org.ruoyi.core.cwproject.domain.projectVO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.ruoyi.core.cwproject.domain.CwProjectPay;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AddFeePay {
    /** 项目主键 */
    private Long projectId;

    /** 收入表主键 */
    private Long projectIncomeId;

    /** 返费表主键 */
    private Long projectFeeId;

    /** 期次Id */
    private Long phaseId;

    /** d打款表集合 */
    private List<CwProjectPay> payDataList;

    /** 备注 */
    private String remark;

    private Long custId;

    private String custName;

    private String feeCustName;

    private Long payCustId;
}
