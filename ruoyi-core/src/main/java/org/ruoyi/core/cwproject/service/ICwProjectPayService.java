package org.ruoyi.core.cwproject.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.cwproject.domain.CwProjectPay;

import java.util.List;
import java.util.Map;

/**
 * 财务项目管理-打款信息Service接口
 *
 * <AUTHOR>
 * @date 2022-11-10
 */
public interface ICwProjectPayService
{
    /**
     * 查询财务项目管理-打款信息
     *
     * @param id 财务项目管理-打款信息主键
     * @return 财务项目管理-打款信息
     */
    public CwProjectPay selectCwProjectPayById(Long id);

    /**
     * 查询财务项目管理-打款信息列表
     *
     * @param cwProjectPay 财务项目管理-打款信息
     * @return 财务项目管理-打款信息集合
     */
    public List<CwProjectPay> selectCwProjectPayList(CwProjectPay cwProjectPay);

    /**
     * 新增财务项目管理-打款信息
     *
     * @param cwProjectPay 财务项目管理-打款信息
     * @return 结果
     */
    public int insertCwProjectPay(CwProjectPay cwProjectPay);

    /**
     * 修改财务项目管理-打款信息
     *
     * @param cwProjectPay 财务项目管理-打款信息
     * @return 结果
     */
    public int updateCwProjectPay(CwProjectPay cwProjectPay);

    /**
     * 批量删除财务项目管理-打款信息
     *
     * @param ids 需要删除的财务项目管理-打款信息主键集合
     * @return 结果
     */
    public int deleteCwProjectPayByIds(Long[] ids);

    /**
     * 删除财务项目管理-打款信息信息
     *
     * @param id 财务项目管理-打款信息主键
     * @return 结果
     */
    public int deleteCwProjectPayById(Long id);

    /**
     * 查询财务项目管理-待出纳打款状态
     *
     * @param cwProjectPay 财务项目管理-打款
     * @return 财务项目管理-打款集合
     */
    List<Map<String, Object>> selectCwProjectPayListFlagZero(CwProjectPay cwProjectPay, Long userId, LoginUser loginUser);
}
