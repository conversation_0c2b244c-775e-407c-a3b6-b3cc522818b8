package org.ruoyi.core.cwproject.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.cwproject.domain.AddCwProjectIncomeAndFee;
import org.ruoyi.core.cwproject.domain.CwProject;
import org.ruoyi.core.cwproject.domain.CwProjectIncome;
import org.ruoyi.core.cwproject.domain.dto.CwProjectExportDto;
import org.ruoyi.core.cwproject.domain.dto.CwProjectIncomeForLawDto;
import org.ruoyi.core.cwproject.domain.projectVO.*;
import org.ruoyi.core.cwproject.service.ICwProjectIncomeService;
import org.ruoyi.core.cwproject.service.ICwProjectShowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/system/projectShow")
public class ProjectShowController  extends BaseController {

  @Autowired
   private ICwProjectShowService iCwProjectShowService;

  @Autowired
  private ICwProjectIncomeService iCwProjectIncomeService;
  /**
   * 返费数据统计列表
   */
  @PreAuthorize("@ss.hasPermi('system:projectShow:list')")
  @GetMapping("/list")
  public TableDataInfo list(CwProjectShowVo cwProjectShowVo)
  {
    startPage();
    List<CwProjectShowVo> list = iCwProjectShowService.selectCwProjectShowList(cwProjectShowVo);
    return getDataTable(list);
  }



  /**
   * 列表页面下拉框查询
   */
  @GetMapping("/getCustNameXiaLa")
  public Map<Object,Object> getCustNameXiaLa()
  {
    LoginUser loginUser = getLoginUser();
    Map<Object, Object> map = new HashMap<>();
    List<Map<String,Object>> list = iCwProjectShowService.getCustNameXiaLa();
    List<Map<String,Object>> list2 = iCwProjectShowService.getIncomeCustNameXiaLa();
    List<Map<String,Object>> list3 =  iCwProjectShowService.getFeeCustLixt();
    List<Map<String,Object>> list4 = iCwProjectShowService.getCustNameXiaLaDanBao();
    Map<String,Object> roleSelectMap =   iCwProjectShowService.getRoleSelectList(loginUser);
    map.put("custList",list);
    map.put("incomeList",list2);
    map.put("FeeCust",list3);
    map.put("custListDanBao",list4);
    map.putAll(roleSelectMap);
    return map;
  }


  /**
   * 查询所有的会计 财务 出纳下拉框
   */
  @GetMapping ("/userList")
  public Map listKuaiji()
  {
    HashMap<String, Object> userMap = new HashMap<>();
    List<Map<String,Object>> listKuaiji = iCwProjectShowService.listKuaiji();
    List<Map<String,Object>> listChuna = iCwProjectShowService.listchuna();
    List<Map<String,Object>> listYewu = iCwProjectShowService.listYewu();
    List<SysUser> userList =  iCwProjectShowService.listAllUser();
    userMap.put("kuaiji",listKuaiji);
    userMap.put("chuna",listChuna);
    userMap.put("yewu",listYewu);
    userMap.put("alluser",userList);
    //OA四期，加入财务角色 财务=会计+出纳
    List<Map<String, Object>> caiwuList = new ArrayList<>();
    caiwuList.addAll(listKuaiji);
    caiwuList.addAll(listChuna);
    List<Map<String, Object>> distinctCaiwuList = caiwuList.stream().distinct().collect(Collectors.toList());
    userMap.put("caiwu",distinctCaiwuList);
    return userMap;
  }
//  @GetMapping ("/isShow")
//  public Map isLookShow()
//  {
//    HashMap<String, Object> userMap = new HashMap<>();
//    LoginUser loginUser = getLoginUser();
//    loginUser.getUser()
//    return userMap;
//  }
  //getyewuidList

  /**
   * 获取所属项目业务idList
   */
  @GetMapping(value = "/getyewuidList")
  public Map<String,Object> getYewuIdList(CwProject cwProject)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    returnMap.put("idList",iCwProjectIncomeService.getyewuByIdList(cwProject));
    return returnMap;

  }
  /**
   * 修改业务负责人
   */

  @PostMapping("/updateYeWu")
  public Map updateYeWu(@RequestBody AddParojectVo addParojectVo)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.updateYewuUser(addParojectVo,loginUser.getUsername());
    returnMap.put("isok",b);
    return returnMap;
  }
  /**
   * 新增收入与返费    2023/04/27 加入修改收入与返费业务 - 通过标识来区分是新增还是修改
   */
  @PostMapping("/addshouru")
//  public Map addShouRu(@RequestBody CwProjectIncome cwProjectIncome)
  public Map addShouRu(@RequestBody AddCwProjectIncomeAndFee addCwProjectIncomeAndFee)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    CwProjectIncome cwProjectIncome = addCwProjectIncomeAndFee.getCwProjectIncome();
    AddfeeVO addfeeVO = addCwProjectIncomeAndFee.getAddfeeVO();
    String changeFlag = addCwProjectIncomeAndFee.getChangeFlag();
    if (null == changeFlag) {
      boolean b = iCwProjectIncomeService.addIncomeShouRu(cwProjectIncome, loginUser);
      boolean b1 = false;
      if (b) {
        b1 = iCwProjectIncomeService.addFeeList(addCwProjectIncomeAndFee, loginUser);
      }
      if (b1) {
        returnMap.put("isok", b1);
      }
      return returnMap;
    } else {
      boolean b = iCwProjectIncomeService.updateIncomeAndFee(addCwProjectIncomeAndFee, loginUser);
      if (b) {
        returnMap.put("isok", b);
      }
      return returnMap;
    }
  }
  /**
   * 修改收入
   */

  @PostMapping("/updataShouru")
  public Map updateShouRu(@RequestBody CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.updateIncomeShouRu(cwProjectIncome,loginUser.getUsername());
    returnMap.put("isok",b);
    return returnMap;
  }



  /**
   * 添加打款
   * @param addFeePay
   */
  @PostMapping("/addPay")
  public Map addPayFee(@RequestBody AddFeePay addFeePay)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.addFeePayList(addFeePay,loginUser.getUsername());
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 录入返费
   * @param addfeeVO
   */

  @PostMapping("/insertfee")
  public Map insertFee(@RequestBody AddfeeVO addfeeVO)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();

    AddCwProjectIncomeAndFee addCwProjectIncomeAndFee = new AddCwProjectIncomeAndFee();
    addCwProjectIncomeAndFee.setAddfeeVO(addfeeVO);
   boolean b = iCwProjectIncomeService.addFeeList(addCwProjectIncomeAndFee,loginUser);
   returnMap.put("isok",b);
    return returnMap;
  }
  /**
   * 修改返费
   * @param addfeeVO
   */

  @PostMapping("/updatefee")
  public Map updateFee(@RequestBody AddfeeVO addfeeVO)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();

    boolean b = iCwProjectIncomeService.updateFeeList(addfeeVO,loginUser.getUsername());
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 确认打款
   */
  @GetMapping(value = "/querendakuan")
  public Map querendakuan(CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
     boolean b = iCwProjectIncomeService.querenDakuan(cwProjectIncome,loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 确认返费
   */
  @GetMapping(value = "/querenFee")
  public Map querenFee(CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b =  iCwProjectIncomeService.querenFee(cwProjectIncome,loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }

    /**
   * 确认收入    2023/05/04 改为正常项目的确认金额  ---->  2023/11/21 财务项目管理五期，做了新的逻辑修改
   */
  @GetMapping(value = "/querenluru")
  public Map getInfo(CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.updateSubmitStatus(cwProjectIncome, loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }
  /**
   * 新增业务期次
   * @param cwProjectIncome
   */
  @PostMapping("/addyewuqici")
  public Map addYeWuQiCi(@RequestBody CwProjectIncome cwProjectIncome)
  {
//    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    return iCwProjectIncomeService.addIncomeQiCi(cwProjectIncome,loginUser);
//    boolean b = iCwProjectIncomeService.addIncomeQiCi(cwProjectIncome,loginUser);
//    returnMap.put("isok",b);
//    return returnMap;
  }

  /**
   * 修改业务期次
   * @param cwProjectIncome
   */
  @PostMapping("/updateyewuqici")
  public Map updateYeWuQiCi(@RequestBody CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.updateIncomeQiCi(cwProjectIncome,loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 新增项目
   * @param addParojectVo
   */
  @PostMapping
  public Map<String,Object> add(@RequestBody AddParojectVo addParojectVo)
  {
    LoginUser loginUser = getLoginUser();
    Long aLong = iCwProjectShowService.addProject(addParojectVo, loginUser.getUsername());
    HashMap<String, Object> returnMap = new HashMap<>();
    returnMap.put("id",aLong);
    return  returnMap;
  }


  /**
   * 修改项目
   * @param addParojectVo
   */
  @PostMapping("/updatePro")
  public void updatePro(@RequestBody AddParojectVo addParojectVo)
  {
    LoginUser loginUser = getLoginUser();
    iCwProjectShowService.updateProject(addParojectVo,loginUser.getUsername());
  }

  /**
   * 新增业务期次，全部
   */
  @GetMapping("/addFeeIncomePay")
  public void addFeeIncomePay(ProjectDataParticularsVo projectDataParticularsVo)
  {
    iCwProjectShowService.addFeeIncomePay(projectDataParticularsVo);
  }

    /**
   * 终止项目
   */
  @GetMapping(value = "/closeProject/{projectId}")
  public void closeProjectById(@PathVariable("projectId")Long projectId)
  {
    iCwProjectShowService.closeProject(projectId);
  }

  /**
   * 新增业务期次，全部
   */
  @GetMapping("/getUserList")
  public Map getUserList(CwProject cwProject)
  {
    return iCwProjectShowService.getUserListName(cwProject);
  }

  /**
   *
   */
  // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:export')")
  @Log(title = "财务项目管理-生成zip文件", businessType = BusinessType.EXPORT)
  @PostMapping("/over/detail/allExport")
  public AjaxResult overDetailExport() throws IOException {
    LoginUser loginUser = getLoginUser();
    String s = iCwProjectShowService.batchExcelZip(loginUser);
    return AjaxResult.success(s);
  }

  /**
   * 批量导出excel文件到一个压缩包里
   */
  // @PreAuthorize("@ss.hasPermi('cwxmgl:cust:export')")
  @Log(title = "财务项目管理-生成zip文件", businessType = BusinessType.EXPORT)
  @PostMapping("/over/detail/allExportNew")
  public AjaxResult overDetailExportNew() throws IOException {
    LoginUser loginUser = getLoginUser();
    String s = iCwProjectShowService.batchExcelZipNew(loginUser);
    return AjaxResult.success(s);
  }

  @PostMapping("/jtReportsts/download")
  public void download(HttpServletResponse response,CwProjectExportDto cwProjectExportDto) {
    //全路径
//    String zipFuliFileName = Stringutils.append(Global.getDownloadPath(), fileName);
    String absoluteFile = ExcelUtil.getAbsoluteFile(cwProjectExportDto.getProjectName());
    try {
      //下载名称
      response.setCharacterEncoding("utf-8");
      response.setContentType("multipart/form-data");
      response.setHeader("Content-Disposition", "attachment;fileName=" + cwProjectExportDto.getProjectName());
      FileUtils.writeBytes(absoluteFile, response.getOutputStream());
    }
    catch(Exception e){
      e.printStackTrace();
      }finally{
        FileUtils.deleteFile(absoluteFile);
      }

  }
  /**
   * 新增业务期次，全部
   */
  @GetMapping("/checkExportRole")
  public Map checkRoleExport(CwProject cwProject)
  {
    LoginUser loginUser = getLoginUser();
    return iCwProjectShowService.checkLoginRole(loginUser,cwProject);
  }

  /**
   * 新增业务期次，全部
   */
  @GetMapping("/checkExportProNum")
  public Map checkExportProNum()
  {
    LoginUser loginUser = getLoginUser();
    return iCwProjectShowService.checkExportProNum(loginUser);
  }

  @PostMapping(value = "/deleteincome")
  public Map deleteIncomeData(@RequestBody CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    boolean b = false;
    LoginUser loginUser = getLoginUser();
    cwProjectIncome.setStatus("1");
    int i = iCwProjectIncomeService.updateCwProjectIncome(cwProjectIncome);
    if(i>0){
      b = true;
    }
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 列表页面下拉框查询 - 法催项目
   */
  @GetMapping("/getXiaLaForLaw")
  public Map<Object,Object> getXiaLaForLaw()
  {
    LoginUser loginUser = getLoginUser();
    Map<Object, Object> map = new HashMap<>();
    List<Map<String,Object>> list = iCwProjectShowService.getServiceProvider();
    List<Map<String,Object>> list2 = iCwProjectShowService.getServiceProviderSecond();
    List<Map<String,Object>> list3 = iCwProjectShowService.getCustFeeListForLaw();
    map.put("yijifuwushangList",list);
    map.put("erjifuwushangList",list2);
    map.put("custFeeList",list3);
    return map;
  }

  /**
   * 新增业务期次 - 法催项目
   * @param cwProjectIncome
   */
  @PostMapping("/addyewuqiciForLaw")
  public Map<String, Object> addYeWuQiCiForLaw(@RequestBody CwProjectIncome cwProjectIncome)
  {
//    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    return iCwProjectIncomeService.addQiCiForLaw(cwProjectIncome,loginUser);
//    boolean b = iCwProjectIncomeService.addQiCiForLaw(cwProjectIncome,loginUser);
//    returnMap.put("isok",b);
//    return returnMap;
  }

  /**
   * 新增收入 - 法催项目
   */
  @PostMapping("/addIncomeForLaw")
  public Map<String,Object> addIncomeForLaw(@RequestBody List<CwProjectIncomeForLawDto> cwProjectIncomeForLawDtoList)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.addIncomeForLaw(cwProjectIncomeForLawDtoList, loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 确认收入与返费 - 法催项目  ---->  2023/11/21 财务项目管理五期，做了新的逻辑修改
   */
  @GetMapping(value = "/querenIncomeAndFeeForLaw")
  public Map querenIncomeAndFeeForLaw(CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b =  iCwProjectIncomeService.querenIncomeAndFeeForLaw(cwProjectIncome,loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 添加打款 - 法催项目
   * @param addFeePay
   */
  @PostMapping("/addPayForLaw")
  public Map addPayForLaw(@RequestBody AddFeePay addFeePay)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.addLawFeePayList(addFeePay,loginUser.getUsername());
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 确认打款 - 法催项目
   */
  @GetMapping(value = "/querendakuanForLaw")
  public Map querendakuanForLaw(CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.querenDakuanFowLaw(cwProjectIncome,loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 删除期次 - 法催项目
   */
  @GetMapping(value = "/deleteincomeForLaw")
  public Map deleteIncomeForLaw(CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    boolean b = false;
    LoginUser loginUser = getLoginUser();
    cwProjectIncome.setStatus("1");
    //通过期次id找收入
    int i = iCwProjectIncomeService.updateCwProjectIncomeLaw(cwProjectIncome);
//    int i = iCwProjectIncomeService.updateCwProjectIncome(cwProjectIncome);
    if(i>0){
      b = true;
    }
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 修改收入与返费 - 法催项目
   */
  @PostMapping("/changePhase")
  public Map<String,Object> changePhase(@RequestBody List<CwProjectIncomeForLawDto> cwProjectIncomeForLawDtoList)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    int a = iCwProjectIncomeService.deleteCwProjectIncomeByPhaseId(cwProjectIncomeForLawDtoList.get(0).getPhaseId());
    if (a > 0) {
      returnMap.put("isok",true);
    }
    return returnMap;
  }

  /**
   * 驳回收入
   */
  @PostMapping(value = "/rejectionIncome")
  public Map rejectionIncome(@RequestBody CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.rejectionIncome(cwProjectIncome, loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 驳回收入后修改收入金额   2023/04/28 加入驳回金额后修改收入与返费业务
   */
  @PostMapping("/updateIncomeSubmit")
  public Map updateIncomeSubmit(@RequestBody AddCwProjectIncomeAndFee addCwProjectIncomeAndFee)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.updateIncomeSubmit(addCwProjectIncomeAndFee, loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }

  /**
   * 驳回返费
   */
  @PostMapping(value = "/rejectionFee")
  public Map rejectionFee(@RequestBody CwProjectIncome cwProjectIncome)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();
    boolean b = iCwProjectIncomeService.rejectionFee(cwProjectIncome, loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }

//  /**
//   * 驳回返费后修改返费
//   */
//  @PostMapping("/updateIncomeSubmit")
//  public Map updateIncomeSubmit(@RequestBody CwProjectIncome cwProjectIncome)
//  {
//    HashMap<String, Object> returnMap = new HashMap<>();
//    LoginUser loginUser = getLoginUser();
//    boolean b = iCwProjectIncomeService.updateIncomeSubmit(cwProjectIncome, loginUser);
//    returnMap.put("isok",b);
//    return returnMap;
//  }
  /**
   * 驳回返费后修改返费
   * @param addfeeVO
   */

  @PostMapping("/updateFeeSubmit")
  public Map updateFeeSubmit(@RequestBody AddfeeVO addfeeVO)
  {
    HashMap<String, Object> returnMap = new HashMap<>();
    LoginUser loginUser = getLoginUser();

    boolean b = iCwProjectIncomeService.updateFeeSubmit(addfeeVO, loginUser);
    returnMap.put("isok",b);
    return returnMap;
  }

  @GetMapping("/createVoucher")
  public void createVoucher(CwProjectIncome cwProjectIncome){
    iCwProjectIncomeService.createProof(cwProjectIncome.getId(),cwProjectIncome.getStatus());
  }
}
