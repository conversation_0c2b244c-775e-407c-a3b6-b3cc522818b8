package org.ruoyi.core.cwproject.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.cwproject.domain.CwProject;
import org.ruoyi.core.cwproject.domain.CwProjectUser;

import java.util.List;
import java.util.Map;

/**
 * 财务项目管理-成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-09
 */
public interface CwProjectUserMapper 
{
    /**
     * 查询财务项目管理-成员
     * 
     * @param id 财务项目管理-成员主键
     * @return 财务项目管理-成员
     */
    public CwProjectUser selectCwProjectUserById(Long id);

    /**
     * 查询财务项目管理-成员列表
     * 
     * @param cwProjectUser 财务项目管理-成员
     * @return 财务项目管理-成员集合
     */
    public List<CwProjectUser> selectCwProjectUserList(CwProjectUser cwProjectUser);

    /**
     * 新增财务项目管理-成员
     * 
     * @param cwProjectUser 财务项目管理-成员
     * @return 结果
     */
    public int insertCwProjectUser(CwProjectUser cwProjectUser);

    /**
     * 修改财务项目管理-成员
     * 
     * @param cwProjectUser 财务项目管理-成员
     * @return 结果
     */
    public int updateCwProjectUser(CwProjectUser cwProjectUser);

    /**
     * 删除财务项目管理-成员
     * 
     * @param id 财务项目管理-成员主键
     * @return 结果
     */
    public int deleteCwProjectUserById(Long id);

    /**
     * 批量删除财务项目管理-成员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCwProjectUserByIds(Long[] ids);

    /**
     * 根据主表id查到负责的会计、出纳和业务
     *
     * @param id 主表表id
     * @return 结果
     */
    List<Map<String,Object>> selectCwProjectUserByProjectId(Long id);

    /**
     * 根据当前用户id查到主表id
     *
     * @param userId 当前用户id
     * @return 结果
     */
    List<CwProjectUser> selectCwProjectUserByUserId(Long userId);

    /**
     * 根据当前用户id，当前用户的角色为会计、出纳时，查到的主表id
     *
     * @param userId 当前用户id
     * @return 结果
     */
    List<CwProject> selectCwProjectUserByUserIdWhenUserFlagZeroAndOne(Long userId);

    /**
     * 根据当前用户id，当前用户的角色为业务时，查到的主表id
     *
     * @param userId 当前用户id
     * @return 结果
     */
    List<CwProject> selectCwProjectUserByUserIdWhenUserFlagTwo(Long userId);

    /**
     * 根据当前用户id，当前用户的角色为会计时，查到的主表id
     *
     * @param userId 当前用户id
     * @return 结果
     */
    List<CwProject> selectCwProjectUserByUserIdWhenUserFlagZero(Long userId);

    /**
     * 根据当前用户id，当前用户的角色为出纳时，查到的主表id
     *
     * @param userId 当前用户id
     * @return 结果
     */
    List<CwProject> selectCwProjectUserByUserIdWhenUserFlagOne(Long userId);

    /**
     * 判断当前用户是否为超级管理员，返回1-是，其余都不是
     *
     * @param userId 当前用户id
     * @return 结果
     */
    List<String> selectUserRoleIsAdmin(Long userId);

    int updatePsalesmanStatus(@Param("id") Long projectId);

    List<CwProjectUser> selectCwProjectUserByProjectId1(@Param("id") Long projectId);

    List<CwProjectUser> selectUserByProjectId(Long projectId);

    /**
     * 根据主表id查到负责的会计、出纳和业务
     *
     * @param id 主表表id
     * @return 结果
     */
    List<Map<String,Object>> selectCwProjectUserByProjectId2(Long id);

    void deleteByprojectId(@Param("id") Long id, @Param("userFlag") String userFlag);

    List selectByprojectid(@Param("id") Long id);

    List<Long> getUserIdListByProId(@Param("projectId") Long projectId,@Param("userFlag") String userFlag);



    List<String> getUserNameList(@Param("id") Long id, @Param("userFlag") String userFlag);

    List<Long> getProjectIdByUserId(@Param("userId") Long userId,@Param("projectFlag") String projectFlag);

    List<Long> checkExport(@Param("projectId") Long projectId, @Param("userId") Long userId, @Param("userFlag") String userFlag);

    List<Map<String,Object>> getExportProjectIdByUserId(@Param("userId") Long userId, @Param("projectFlag") String projectFlag, @Param("userFlag") String userFlag);

    List<Long> checkExportRoleNum(@Param("userId") Long userId, @Param("userFlag") String userFlag);

    CwProjectUser selectByProIdAndType(@Param("projectId") Long projectId,@Param("userType") String userType);
}
