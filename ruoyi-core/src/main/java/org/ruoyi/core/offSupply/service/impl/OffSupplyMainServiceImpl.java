package org.ruoyi.core.offSupply.service.impl;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.FunctionNodeEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.qiyeVX.AccessTokenUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.mapper.SysCompanyMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysOperLogService;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.ruoyi.core.cwproject.mapper.TopNotifyMapper;
import org.ruoyi.core.license.mapper.ZzPendingDetailMapper;
import org.ruoyi.core.offSupply.domain.*;
import org.ruoyi.core.offSupply.domain.vo.SupplySettleVo;
import org.ruoyi.core.offSupply.mapper.OffCategoryMainMapper;
import org.ruoyi.core.offSupply.mapper.OffSupplyFilesMapper;
import org.ruoyi.core.qiyeVX.domain.VxUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.offSupply.mapper.OffSupplyMainMapper;
import org.ruoyi.core.offSupply.service.IOffSupplyMainService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 办公用品维护Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
@Service
public class OffSupplyMainServiceImpl implements IOffSupplyMainService 
{
    @Autowired
    private OffSupplyMainMapper offSupplyMainMapper;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityServiceImpl;

    @Autowired
    private OffSupplyFilesMapper offSupplyFilesMapper;

    @Autowired
    private TopNotifyMapper topNotifyMapper;

    @Autowired
    private OffCategoryMainMapper offCategoryMainMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ZzPendingDetailMapper zzPendingDetailMapper;

    @Autowired
    private AccessTokenUtils accessTokenUtils;

    @Autowired
    private SysCompanyMapper sysCompanyMapper;

    @Autowired
    private OffCategoryMainServiceImpl offCategoryMainServiceImpl;

    @Autowired
    private ISysOperLogService sysOperLogService;

    /**
     * 查询办公用品维护详情
     * 
     * @param id 办公用品维护主键
     * @return 办公用品维护
     */
    @Override
    public OffSupplyMain selectOffSupplyMainById(Long id)
    {
        OffSupplyMain supplyMain = offSupplyMainMapper.selectOffSupplyMainById(id);
        //办公用品图片
        OffSupplyFiles supplyFile = offSupplyFilesMapper.selectSupplyFile("1", id);
        if (!Objects.isNull(supplyFile)){
            supplyMain.setSupplyPicture(supplyFile);
        }
        //办公用品附件
        List<OffSupplyFiles> supplyAttachs = offSupplyFilesMapper.selectOffSupplyFilesByTypeAndRelevancyId("5", id );
        if (CollectionUtils.isNotEmpty(supplyAttachs)){
            supplyMain.setSupplyFiles(supplyAttachs);
        }
        return supplyMain;
    }

    /**
     * 查询办公用品维护列表
     * 
     * @param offSupplyMain 办公用品维护
     * @return 办公用品维护
     */
    @Override
    public List<OffSupplyMain> selectOffSupplyMainList(OffSupplyMain offSupplyMain)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //新权限->获取用户有哪些公司权限
        List<Long> authorityCompanyIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.OFFSUPPLY.getCode());
        if (CollectionUtils.isEmpty(authorityCompanyIds)){
           return new ArrayList<>();
        }
        offSupplyMain.setAuthCompanyIds(authorityCompanyIds);
        if (offSupplyMain.getPageSize() != null && offSupplyMain.getPageNum() != null){
            PageHelper.startPage(offSupplyMain.getPageNum(),offSupplyMain.getPageSize());
        }
        return offSupplyMainMapper.selectOffSupplyMainList(offSupplyMain);
    }

    /**
     * 新增办公用品维护
     * @param offSupplyMain 办公用品维护
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertOffSupplyMain(OffSupplyMain offSupplyMain)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String errorMessage = "";
        String successMessage = "";
        int i = 0;
        try {
            offSupplyMain.setCreateTime(DateUtils.getNowDate());
            offSupplyMain.setCreateBy(loginUser.getUsername());
            offSupplyMain.setVersion(1);//初始化第1版
            //生成系统编号
            int nextNumber = offSupplyMainMapper.selectCountNumber() + 1;
            offSupplyMain.setSysCode("BGYP" + String.format("%03d", nextNumber));
            i = offSupplyMainMapper.insertOffSupplyMain(offSupplyMain);
            Long mainId = offSupplyMain.getId();
            //更新物品图片(物品图片已经在前端上传，此处只需要更新数据库即可)
            OffSupplyFiles supplyPicture = offSupplyMain.getSupplyPicture();
            if (!Objects.isNull(supplyPicture)){
                OffSupplyFiles picture = new OffSupplyFiles();
                picture.setRelevancyId(mainId);
                picture.setId(supplyPicture.getId());
                picture.setStatus("0");
                offSupplyFilesMapper.updateOffSupplyFiles(picture);
            }
            //更新物品附件(物品附件已经在前端上传，此处只需要更新数据库即可)
            List<OffSupplyFiles> supplyFiles = offSupplyMain.getSupplyFiles();
            if (!CollectionUtils.isEmpty(supplyFiles)){
                for (OffSupplyFiles supplyFile : supplyFiles) {
                    OffSupplyFiles picture = new OffSupplyFiles();
                    picture.setRelevancyId(mainId);
                    picture.setId(supplyFile.getId());
                    picture.setStatus("0");
                    offSupplyFilesMapper.updateOffSupplyFiles(picture);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
        } finally {
            //查询当前登录用户姓名
            SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
            successMessage = sysUser.getNickName() + "新增了办公用品【" + offSupplyMain.getItemName() + "】";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFSUPPLYMAIN.getCode(), successMessage, 1, errorMessage, "");
        }
        return i;
    }

    /**
     * 修改办公用品
     * 插入新数据，涉及版本迭代，版本号+1
     * @param offSupplyMain 办公用品维护
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateOffSupplyMain(OffSupplyMain offSupplyMain) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        int i = 0;
        String errorMessage = "";
        String successMessage = "";
        try {
            //获取旧的物品id
            Long oldId = offSupplyMain.getId();
            // offSupplyMain.setCreateTime(DateUtils.getNowDate());
            // offSupplyMain.setCreateBy(loginUser.getUsername());
            offSupplyMain.setUpdateTime(DateUtils.getNowDate());
            offSupplyMain.setUpdateBy(loginUser.getUsername());
            offSupplyMain.setVersion(offSupplyMain.getVersion() + 1);
            i = offSupplyMainMapper.insertOffSupplyMain(offSupplyMain);
            //更新物品图片
            OffSupplyFiles supplyPicture = offSupplyMain.getSupplyPicture();
            if (!Objects.isNull(supplyPicture)) {
                //删除旧的物品图片
                //offSupplyFilesMapper.deleteOffSupplyFilesBySupplyIdAndType(offSupplyMain.getId(), "1");
                //插入新图片
                OffSupplyFiles picture = new OffSupplyFiles();
                picture.setId(supplyPicture.getId());
                picture.setRelevancyId(offSupplyMain.getId());
                picture.setStatus("0");
                offSupplyFilesMapper.updateOffSupplyFiles(picture);
            }
            //更新物品附件
            List<OffSupplyFiles> supplyFiles = offSupplyMain.getSupplyFiles();
            if (!CollectionUtils.isEmpty(supplyFiles)) {
                //删除旧的附件信息
                //offSupplyFilesMapper.deleteOffSupplyFilesBySupplyIdAndType(offSupplyMain.getId(), "5");
                //插入新的物品附件
                for (OffSupplyFiles supplyFile : supplyFiles) {
                    OffSupplyFiles picture = new OffSupplyFiles();
                    picture.setId(supplyFile.getId());
                    picture.setRelevancyId(offSupplyMain.getId());
                    picture.setStatus("0");
                    offSupplyFilesMapper.updateOffSupplyFiles(picture);
                }
            }
            //修改记录
            OffSupplyHistory offSupplyHistory = offSupplyMain.getOffSupplyHistory();
            //根据id查询旧数据，获取字段信息
            OffSupplyMain supplyMain = offSupplyMainMapper.selectOffSupplyMainById(oldId);
            offSupplyHistory.setSupplyId(offSupplyMain.getId());
            offSupplyHistory.setSupplySysCode(supplyMain.getSysCode());//物品系统编号
            offSupplyHistory.setOldSupplyName(supplyMain.getItemName());//修改前物品名称
            offSupplyHistory.setNewSupplyName(offSupplyMain.getItemName());//修改后物品名称
            offSupplyHistory.setOldAmount(supplyMain.getAmount());//修改前物品数量
            offSupplyHistory.setNewAmount(offSupplyMain.getAmount());//修改前物品数量
            offSupplyHistory.setUpdateBy(loginUser.getUsername());//修改人
            offSupplyHistory.setUpdateTime(DateUtils.getNowDate());//修改时间
            offSupplyMainMapper.insertOffSupplyHistory(offSupplyHistory);
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
        } finally {
            //查询当前登录用户姓名
            SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
            successMessage = sysUser.getNickName() + "修改了办公用品【" + offSupplyMain.getItemName() + "】信息";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFSUPPLYMAIN.getCode(), successMessage, 2, errorMessage, "");
        }
        return i;
    }

    /**
     * 批量删除办公用品维护
     * 
     * @param ids 需要删除的办公用品维护主键
     * @return 结果
     */
    @Override
    public int deleteOffSupplyMainByIds(Long[] ids)
    {
        return offSupplyMainMapper.deleteOffSupplyMainByIds(ids);
    }

    /**
     * 删除办公用品维护信息
     * @param sysCode 办公用品唯一系统编号
     * @return 结果
     */
    @Override
    public int deleteOffSupplyMainBySysCode(String sysCode)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        int i = 0;
        String errorMessage = "";
        String successMessage = "";
        try {
            i = offSupplyMainMapper.deleteOffSupplyMainBySysCode(sysCode);
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
        } finally {
            //查询当前登录用户姓名
            SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
            //根据物品唯一系统编码查询物品信息
            OffSupplyMain supplyMain = offSupplyMainMapper.selectOffSupplyMainBySysCode(sysCode);
            successMessage = sysUser.getNickName() + "删除了办公用品【" + supplyMain.getItemName() + "】";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFSUPPLYMAIN.getCode(), successMessage, 3, errorMessage, "");
        }
        return i;
    }

    /**
     * 附件上传
     * @param file
     * @return
     */
    @Override
    public AjaxResult uploadFile(MultipartFile file, String fileType, String userName) {
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.OFF_SUPPLY, file);
            OffSupplyFiles offSupplyFile = new OffSupplyFiles();
            offSupplyFile.setFileName(name);
            offSupplyFile.setFileType(fileType);
            offSupplyFile.setFilePath(url);
            offSupplyFile.setStatus("0");
            offSupplyFile.setCreateBy(userName);
            offSupplyFile.setCreateTime(DateUtils.getNowDate());
            offSupplyFilesMapper.insertOffSupplyFiles(offSupplyFile);

            return AjaxResult.success(offSupplyFile);
        } catch (Exception e) {
            return AjaxResult.error();
        }
    }

    /**
     * 根据附件id删除附件信息
     * @param fileId
     * @return
     */
    @Override
    public int deleteFileById(Long fileId) {
        return offSupplyFilesMapper.deleteOffSupplyFilesById(fileId);
    }

    /**
     * 物品整理
     * @return
     */
    @Override
    public int updateSupplySettle(SupplySettleVo supplySettleVo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        int i = 0;
        String errorMessage = "";
        String successMessage = "";
        Long categoryId = supplySettleVo.getCategoryId();
        List<Long> ids = supplySettleVo.getIds();
        List<OffSupplyMain> offSupplyMainList = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids) || categoryId == null){
            return 0;
        }
        try {
            //根据物品id集合物品类别信息
            offSupplyMainList = offSupplyMainMapper.selectOffSupplyMainByIds(ids);
            //取物品所属类别id
            List<Long> categoryCollect = offSupplyMainList.stream().map(OffSupplyMain::getCategoryId).collect(Collectors.toList());
            OffCategoryMain categoryMain = offCategoryMainMapper.selectOffCategoryMainById(categoryId);

            if (categoryId != null && CollectionUtils.isNotEmpty(ids)){
                List<OffCategoryMain> categoryMainList = offCategoryMainMapper.selectOffCategoryMainListByIds(categoryCollect);
                for (OffCategoryMain offCategoryMain : categoryMainList) {
                    if (offCategoryMain.getCompanyId() != categoryMain.getCompanyId()){
                        throw new ServiceException("不可以跨公司对物品进行整理，请确认后重试！");
                    }
                }
                i = offSupplyMainMapper.updateSupplySettle(ids, categoryId);
            }
        }
        //catch (Exception e) {
        //    e.printStackTrace();
        //    errorMessage = e.getMessage();
        //}
        finally {
            //查询当前登录用户姓名
            SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
            //根据类别id查询类别信息
            OffCategoryMain offCategoryMain = offCategoryMainMapper.selectOffCategoryMainById(categoryId);
            //根据物品id集合查询物品信息
            String itemNames = "";
            if (CollectionUtils.isNotEmpty(offSupplyMainList)){
                for (OffSupplyMain offSupplyMain : offSupplyMainList) {
                    itemNames += "【" + offSupplyMain.getItemName() + "】,";
                }
            }
            successMessage = sysUser.getNickName() + "将"+ itemNames + "整理到了【" + offCategoryMain.getCategoryName() + "】类别下";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFSUPPLYMAIN.getCode(), successMessage, 2, errorMessage, "");
        }
        return i;
    }

    /**
     * 根据物品保质期到期时间前n天发送通知
     */
    public void sendNotifyByOffSupplyExpirationDate() {
        // 查询物品表中，保质期不为空，且开启到期提醒的物品
        List<OffSupplyMain> offSupplyMainList = offSupplyMainMapper.selectOffSupplyByExpireDateAndUseNotifyList();
        if (CollectionUtils.isEmpty(offSupplyMainList)) {
            return;
        }
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 不为空，循环遍历获取物品的到期时间，并与当前日期进行对比
        for (OffSupplyMain offSupplyMain : offSupplyMainList) {
            // 获取物品到期时间
            Date expireDate = offSupplyMain.getExpirationDate();
            // 获取当前日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            // 获取物品到期前n天
            Integer beforeNum = offSupplyMain.getBeforeNum();
            Date dateBeforeExpire = null;
            if (beforeNum != null) {
                calendar.add(Calendar.DATE, -beforeNum);
                dateBeforeExpire = calendar
                        .getTime();
            }
            String beforeDate = dateFormat.format(dateBeforeExpire);//到期前N天的日期
            String exDate = dateFormat.format(expireDate);//物品到期日
            // 判断物品到期时间是否小于当前日期，且物品到期前n天的日期大于等于物品到期时间
            if (beforeDate.compareTo(exDate) >= 0) {
                String sysMsg = "";
                String VXmsg = "";
                // 如果通知类型为3，则发送系统代办和微信通知
                String expireDateStr = DateUtils.parseDateToStr("yyyy年MM月dd日", expireDate);// 将Date类型的时间转换为年月日格式
                // 组装系统代办消息内容
                sysMsg = "办公用品管理中维护的物品【" + offSupplyMain.getItemName() + "】，将于" + expireDateStr + "保质期到期，请注意安排使用。";
                // 组装企业微信消息内容
                VXmsg = "<div class=\"gray\">" + "办公用品管理中维护的物品【" + offSupplyMain.getItemName() + "】，将于" + expireDateStr + "保质期到期，请注意安排使用。" + "</div>";
                // 获取当前物品的类别的预警人员id
                List<OffNotifyUser> offNotifyUserList = offCategoryMainMapper.selectOffNotifyUserByCategoryId(offSupplyMain.getCategoryId());

                // 判断是否有设置通知类型(0系统代办 1企业微信 3所有)
                if (offSupplyMain.getNotifyType().equals("") || offSupplyMain.getNotifyType() == null) {
                    continue;// 如果没有设置通知类型，跳过本次循环
                }
                if (offSupplyMain.getNotifyType().equals("3")) {
                    // 发送系统代办和企业微信通知
                    for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                        sendNotify(offSupplyMain, sysMsg, offNotifyUser.getUserId(), "2");
                        SysUser sysUser = sysUserMapper.selectUserById(offNotifyUser.getUserId());
                        sendQYWXNotify(offSupplyMain, VXmsg, sysUser.getUserName()); // 企业微信
                    }
                }
                // 发送系统代办
                if (offSupplyMain.getNotifyType().equals("0")) {
                    for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                        sendNotify(offSupplyMain, sysMsg, offNotifyUser.getUserId(), "2");
                    }
                }
                //发送企业微信通知
                if (offSupplyMain.getNotifyType().equals("1")) {
                    for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                        SysUser sysUser = sysUserMapper.selectUserById(offNotifyUser.getUserId());
                        sendQYWXNotify(offSupplyMain, VXmsg, sysUser.getUserName()); // 企业微信
                    }
                }
            }
        }
    }

    /**
     * 发送系统代办通知
     * @param obj 对象参数
     * @param msg 消息内容
     * @param userId 用户id
     * @param type 1过期提醒 2库存预警
     */
    public void sendNotify(Object obj, String msg, Long userId, String type){
        try {
            TopNotify topNotify = new TopNotify();
            if (type.equals("1")){
                OffSupplyMain offSupplyMain = (OffSupplyMain) obj;
                topNotify.setNotifyModule("【"+offSupplyMain.getItemName()+"】物品库存预警");
            }
            if (type.equals("2")){
                OffSupplyMain offSupplyMain = (OffSupplyMain) obj;
                topNotify.setNotifyModule("【"+offSupplyMain.getItemName()+"】保质期到期提醒");
            }
            //插入为代办
            topNotify.setNotifyMsg(msg);
            topNotify.setNotifyType("1");
            topNotify.setUrl("/offSupplyMain/offSupply");
            topNotify.setDisposeUser(userId);
            topNotify.setViewFlag("0");
            topNotify.setProcessId("");
            topNotify.setOaNotifyType("o");
            topNotify.setProjectId(-1L);
            topNotify.setIncomeId(-1L);
            topNotify.setCreateBy("admin");
            topNotify.setCreateTime(DateUtils.getNowDate());
            topNotify.setUpdateTime(DateUtils.getNowDate());
            topNotifyMapper.insertTopNotify(topNotify);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送企业微信通知
     * @param obj 对象参数
     * @param msg 消息内容
     * @param userName 用户名称
     */
    public void sendQYWXNotify(Object obj, String msg, String userName) {
        String sendUser = "";
        /** 发送通知--企业微信通知*/
        try {
            VxUser vxUser = zzPendingDetailMapper.selectUserQYWXInfoByUserName(userName);
            if (!Objects.isNull(vxUser)) {
                sendUser = sendUser + "|" + vxUser.getVxId();
            }
            // 发送企业微信消息
            accessTokenUtils.sendMsg(sendUser, msg, "11");
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询办公用品修改记录
     * @param supplySysCode 物品id
     * @return
     */
    @Override
    public List<OffSupplyHistory> selectOffSupplyRecord(String supplySysCode) {
        return offSupplyMainMapper.selectOffSupplyRecord(supplySysCode);
    }

    /**
     * 获取当前用户有权限的公司下拉框列表
     * @return AjaxResult
     */
    @Override
    public List<SysCompany> selectAuthCompany() {
        // 新权限->获取用户有哪些公司权限
        List<Long> authorityCompanyIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(SecurityUtils.getUserId(), AuthModuleEnum.OFFSUPPLY.getCode());
        if (CollectionUtils.isEmpty(authorityCompanyIds)){
           return new ArrayList<>();
        }
        return sysCompanyMapper.selectSysCompanyListByCompanyIdList(authorityCompanyIds);
    }

    /**
     * 根据不同类型(办公用品或礼品)查询物品
     * @param offSupplyMain
     * @return
     */
    @Override
    public AjaxResult selectOffReceiveMainByDifType(OffSupplyMain offSupplyMain) {
        AjaxResult ajaxResult = new AjaxResult();
        List<OffSupplyMain> offSupplyMainList = new ArrayList<>();
        List<OffCategoryMain> tree = new ArrayList<>();
        // 先查询当前用户有哪些公司的权限
        List<Long> authorityCompanyIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(SecurityUtils.getUserId(), AuthModuleEnum.OFFSUPPLY.getCode());
        if (CollectionUtils.isEmpty(authorityCompanyIds)){
            ajaxResult.put("supplyMain",new ArrayList<>());//物品列表
            ajaxResult.put("categoryMain",new ArrayList<>());//类别树形列表
           return ajaxResult;
        }
        offSupplyMain.setAuthCompanyIds(authorityCompanyIds);
        //然后根据类型，查询物品和它的所属类别
        offSupplyMainList = offSupplyMainMapper.selectOffSupplyListByType(offSupplyMain);//此查询用来做筛选，查询业务数据

        //查询所有办公用品或礼品的类别，以便组装树形结构列表
        if (offSupplyMain.getCategoryId() != null){
            offSupplyMain.setCategoryId(null);//设置null值，到xml不再通过这个字段查询数据
        }
        List<OffSupplyMain> allOffSupplyMainList = offSupplyMainMapper.selectOffSupplyListByType(offSupplyMain);//此查询用来获取所有办公用品或礼品的类别，以便组装树形结构列表
        if (!CollectionUtils.isEmpty(allOffSupplyMainList)){
            //获取类别集合
            List<Long> categoryIdCollect = offSupplyMainList.stream().map(OffSupplyMain::getCategoryId).collect(Collectors.toList());
            List<OffCategoryMain> categoryMainList = offCategoryMainMapper.selectOffCategoryMainListByIds(categoryIdCollect);
            if (!CollectionUtils.isEmpty(categoryMainList)){
                //组装树形结构列表
                tree = offCategoryMainServiceImpl.buildCategoryTree(categoryMainList);
            }
        }

        ajaxResult.put("supplyMain",offSupplyMainList);//物品列表
        ajaxResult.put("categoryMain",tree);//类别树形列表
        return ajaxResult;
    }

    /**
     * 根据历史记录id查询详情
     * @param historyId
     * @return
     */
    @Override
    public OffSupplyHistory selectOffSupplyRecordInfoById(String historyId) {
        return offSupplyMainMapper.selectSupplyHistoryInfoById(historyId);
    }

    /**
     * 修改办公用品状态
     * @param offSupplyMain
     * @return
     */
    @Override
    public int updateOffSupplyMainStatus(OffSupplyMain offSupplyMain) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //修改记录
        OffSupplyHistory offSupplyHistory = offSupplyMain.getOffSupplyHistory();
        Long oldId = offSupplyMain.getId();
        //根据id查询旧数据，获取字段信息
        OffSupplyMain supplyMain = offSupplyMainMapper.selectOffSupplyMainById(oldId);
        offSupplyHistory.setSupplyId(offSupplyMain.getId());
        offSupplyHistory.setSupplySysCode(supplyMain.getSysCode());//物品系统编号
        offSupplyHistory.setOldSupplyName(supplyMain.getItemName());//修改前物品名称
        offSupplyHistory.setNewSupplyName(offSupplyMain.getItemName());//修改后物品名称
        offSupplyHistory.setOldAmount(supplyMain.getAmount());//修改前物品数量
        offSupplyHistory.setNewAmount(offSupplyMain.getAmount());//修改前物品数量
        offSupplyHistory.setUpdateBy(loginUser.getUsername());//修改人
        offSupplyHistory.setUpdateTime(DateUtils.getNowDate());//修改时间
        //插入历史记录
        offSupplyMainMapper.insertOffSupplyHistory(offSupplyHistory);
        //修改状态
        int i = offSupplyMainMapper.updateOffSupplyMain(offSupplyMain);
        return i;
    }
}
