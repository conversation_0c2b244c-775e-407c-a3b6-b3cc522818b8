package org.ruoyi.core.offSupply.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 类别维护对象 off_category_main
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
public class OffCategoryMain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 类别名称 */
    @Excel(name = "类别名称", sort = 1)
    private String categoryName;

    /** 上级类别 */

    private Long parentId;

    /** 上级类别名称 */
    @Excel(name = "上级类别", sort = 2)
    private String parentName;

    /** 所属公司 */

    private Long companyId;

    /** 所属公司名称 */
    @Excel(name = "所属公司", sort = 3)
    private String companyShortName;

    /** 启用状态(0启用 1停用) */
    @Excel(name = "启用状态" , readConverterExp = "0=启用, 1=停用", sort = 4)
    private String status;

    /** 排序 */
    @Excel(name = "排序", sort = 5)
    private Integer shortNum;

    /** 删除标识 */
    private String delFlag;

    /** 通用授权-公司id集合 */
    private List<Long> authCompanyIds;

    /** 创建人姓名 */
    private String createByName;

    /** 分页参数 */
    private Integer pageSize;

    /** 分页参数 */
    private Integer pageNum;

    /** 库存预警提醒人员 */
    private List<OffNotifyUser> offNotifyUserList;

    /** 子类 */
    private List<OffCategoryMain> children;

    public List<OffCategoryMain> getChildren() {
        return children;
    }

    public void setChildren(List<OffCategoryMain> children) {
        this.children = children;
    }

    public List<OffNotifyUser> getOffNotifyUserList() {
        return offNotifyUserList;
    }

    public void setOffNotifyUserList(List<OffNotifyUser> offNotifyUserList) {
        this.offNotifyUserList = offNotifyUserList;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCategoryName(String categoryName) 
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName() 
    {
        return categoryName;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setCompanyId(Long companyId)
    {
        this.companyId = companyId;
    }

    public Long getCompanyId()
    {
        return companyId;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setShortNum(Integer shortNum) 
    {
        this.shortNum = shortNum;
    }

    public Integer getShortNum() 
    {
        return shortNum;
    }

    public List<Long> getAuthCompanyIds() {
        return authCompanyIds;
    }

    public void setAuthCompanyIds(List<Long> authCompanyIds) {
        this.authCompanyIds = authCompanyIds;
    }

    public String getCompanyShortName() {
        return companyShortName;
    }

    public void setCompanyShortName(String companyShortName) {
        this.companyShortName = companyShortName;
    }

    public String getCreateByName() {
        return createByName;
    }

    public void setCreateByName(String createByName) {
        this.createByName = createByName;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("categoryName", getCategoryName())
            .append("parentId", getParentId())
            .append("companyId", getCompanyId())
            .append("status", getStatus())
            .append("shortNum", getShortNum())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
