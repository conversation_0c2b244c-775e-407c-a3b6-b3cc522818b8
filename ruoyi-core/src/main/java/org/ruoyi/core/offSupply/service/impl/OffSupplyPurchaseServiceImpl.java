package org.ruoyi.core.offSupply.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.FunctionNodeEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysOperLogService;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.ruoyi.core.offSupply.domain.*;
import org.ruoyi.core.offSupply.domain.vo.SupplyPurchaseVo;
import org.ruoyi.core.offSupply.mapper.OffReceiveMainMapper;
import org.ruoyi.core.offSupply.mapper.OffSupplyFilesMapper;
import org.ruoyi.core.offSupply.mapper.OffSupplyMainMapper;
import org.ruoyi.core.offSupply.service.IOffSupplyMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.offSupply.mapper.OffSupplyPurchaseMapper;
import org.ruoyi.core.offSupply.service.IOffSupplyPurchaseService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 办公用品采购单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-24
 */
@Service
public class OffSupplyPurchaseServiceImpl implements IOffSupplyPurchaseService 
{
    @Autowired
    private OffSupplyPurchaseMapper offSupplyPurchaseMapper;

    @Autowired
    private OffSupplyFilesMapper offSupplyFilesMapper;

    @Autowired
    private OffSupplyMainMapper offSupplyMainMapper;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityServiceImpl;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ISysOperLogService sysOperLogService;

    @Autowired
    private OffReceiveMainMapper offReceiveMainMapper;

    @Autowired
    private IOffSupplyMainService offSupplyMainService;

    /**
     * 查询办公用品采购单
     * @param id 办公用品采购单主键
     * @return 办公用品采购单
     */
    @Override
    public OffSupplyPurchase selectOffSupplyPurchaseById(Long id)
    {
        OffSupplyPurchase offSupplyPurchase = offSupplyPurchaseMapper.selectOffSupplyPurchaseById(id);
        // 创建日期格式化对象
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年M月d日");
        // 格式化采购日期
        String formattedDate = sdf.format(offSupplyPurchase.getPurchaseDate());
        // 设置格式化后的采购日期（假设有一个字段来存储格式化后的日期，或者您可以覆盖原字段）
        // 这里我们假设添加一个新字段 formattedPurchaseDat 来存储格式化后的日期
        offSupplyPurchase.setPurchaseDateStr(formattedDate);
        //查询附件
        List<OffSupplyFiles> offSupplyFilesList = offSupplyFilesMapper.selectOffSupplyFilesByTypeAndRelevancyId("3", id);
        if (!CollectionUtils.isEmpty(offSupplyFilesList)){
            offSupplyPurchase.setOffSupplyFileList(offSupplyFilesList);
        }
        return offSupplyPurchase;
    }

    /**
     * 查询办公用品采购单列表
     * @param offSupplyPurchase 办公用品采购单
     * @return 办公用品采购单
     */
    @Override
    public List<OffSupplyPurchase> selectOffSupplyPurchaseList(OffSupplyPurchase offSupplyPurchase)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //新权限->获取用户有哪些公司权限
        List<Long> authorityCompanyIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.OFFSUPPLY.getCode());
        if (CollectionUtils.isEmpty(authorityCompanyIds)){
           return new ArrayList<>();
        }
        offSupplyPurchase.setAuthCompanyIds(authorityCompanyIds);
        offSupplyPurchase.setLoginUserName(SecurityUtils.getUsername());
        if (offSupplyPurchase.getPageSize() != null && offSupplyPurchase.getPageNum() != null){
            PageHelper.startPage(offSupplyPurchase.getPageNum(),offSupplyPurchase.getPageSize());
        }
        List<OffSupplyPurchase> offSupplyPurchases = offSupplyPurchaseMapper.selectOffSupplyPurchaseList(offSupplyPurchase);
        if (!CollectionUtils.isEmpty(offSupplyPurchases)){
            offSupplyPurchases.forEach(off ->{
                if (off.getDiscountRate().equals("") || off.getDiscountRate().equals("0")) {
                    off.setDiscountRate("无折扣");
                } else {
                    off.setDiscountRate(off.getDiscountRate());
                }
                //查询附件，用于批量发起采购申请
                off.setOffSupplyFileList(offSupplyFilesMapper.selectOffSupplyFilesByTypeAndRelevancyId("3", off.getId()));
            });
        }
        return offSupplyPurchases;
    }

    /**
     * 新增办公用品采购单
     * @param offSupplyPurchase 办公用品采购单
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertOffSupplyPurchase(OffSupplyPurchase offSupplyPurchase)
    {
        AjaxResult ajaxResult = new AjaxResult();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String errorMessage = "";
        String successMessage = "";
        int i = 0;
        try {
            offSupplyPurchase.setCreateTime(DateUtils.getNowDate());
            offSupplyPurchase.setCreateBy(SecurityUtils.getUsername());
            offSupplyPurchase.setDelFlag("0");
            //生成系统编号
            int nextNumber = offSupplyFilesMapper.selectCountNumber() + 1;
            offSupplyPurchase.setOrdersCode("BGYP" + String.format("%03d", nextNumber));
            i = offSupplyPurchaseMapper.insertOffSupplyPurchase(offSupplyPurchase);
            Long mainId = offSupplyPurchase.getId();
            //更新发票附件信息
            List<OffSupplyFiles> supplyFiles = offSupplyPurchase.getOffSupplyFileList();
            if (!CollectionUtils.isEmpty(supplyFiles)){
                for (OffSupplyFiles supplyFile : supplyFiles) {
                    OffSupplyFiles file = new OffSupplyFiles();
                    file.setId(supplyFile.getId());
                    file.setRelevancyId(offSupplyPurchase.getId());
                    file.setStatus("0");
                    offSupplyFilesMapper.updateOffSupplyFiles(file);
                }
            }
            // if (offSupplyPurchase.getStatus().equals("1")){
                //保存流程发起时的表单json
                if (!Objects.isNull(offSupplyPurchase.getOffReceivePurchaseDetail())){
                    //先根据流程id查询是否存在此流程，存在则删除旧数据
                    offReceiveMainMapper.deleteOffReceivePurchaseInfo(offSupplyPurchase.getProcessId());
                    OffReceivePurchaseDetail offReceivePurchaseDetail = offSupplyPurchase.getOffReceivePurchaseDetail();
                    offReceivePurchaseDetail.setCreateBy(loginUser.getUsername());
                    offReceivePurchaseDetail.setCreateTime(DateUtils.getNowDate());
                    offReceivePurchaseDetail.setProcessId(offSupplyPurchase.getProcessId());
                    offReceiveMainMapper.insertOffReceivePurchaseInfo(offReceivePurchaseDetail);
                }
            // }
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
        } finally {
            if (i > 0 && offSupplyPurchase.getStatus().equals("1")){
                SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
                successMessage = sysUser.getNickName() + "新增编号为【" + offSupplyPurchase.getOrdersCode() + "】的办公用品采购单,并提交了采购申请";
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFPURCHASEMAIN.getCode(), successMessage, 1, errorMessage, "");
            }
        }
        if (i > 0){
            ajaxResult.put("mgs","操作成功");
            ajaxResult.put("code","200");
            ajaxResult.put("id",offSupplyPurchase.getId());
        }else {
            ajaxResult.put("mgs","操作失败");
            ajaxResult.put("code","500");
        }
        return ajaxResult;
    }

    /**
     * 修改办公用品采购单
     * @param offSupplyPurchase 办公用品采购单
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateOffSupplyPurchase(OffSupplyPurchase offSupplyPurchase)
    {
        AjaxResult ajaxResult = new AjaxResult();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        int result = 0;
        String errorMessage = "";
        String successMessage = "";
        offSupplyPurchase.setUpdateTime(DateUtils.getNowDate());
        offSupplyPurchase.setUpdateBy(SecurityUtils.getUsername());
        //保存流程发起时的表单json
        if (!Objects.isNull(offSupplyPurchase.getOffReceivePurchaseDetail())){
            //先根据流程id查询是否存在此流程，存在则删除旧数据
            offReceiveMainMapper.deleteOffReceivePurchaseInfo(offSupplyPurchase.getProcessId());
            OffReceivePurchaseDetail offReceivePurchaseDetail = offSupplyPurchase.getOffReceivePurchaseDetail();
            offReceivePurchaseDetail.setCreateBy(loginUser.getUsername());
            offReceivePurchaseDetail.setCreateTime(DateUtils.getNowDate());
            offReceivePurchaseDetail.setProcessId(offSupplyPurchase.getProcessId());
            offReceiveMainMapper.insertOffReceivePurchaseInfo(offReceivePurchaseDetail);
        }
        //保存或提交
        if (offSupplyPurchase.getStatus().equals("0") || offSupplyPurchase.getStatus().equals("1")){
            offSupplyPurchaseMapper.updateOffSupplyPurchase(offSupplyPurchase);
            //更新发票附件信息
            List<OffSupplyFiles> supplyFiles = offSupplyPurchase.getOffSupplyFileList();
            if (!CollectionUtils.isEmpty(supplyFiles)){
                //删除旧的附件信息
                offSupplyFilesMapper.deleteOffSupplyFilesBySupplyIdAndType(offSupplyPurchase.getId(), "3");
                for (OffSupplyFiles supplyFile : supplyFiles) {
                    OffSupplyFiles file = new OffSupplyFiles();
                    file.setId(supplyFile.getId());
                    file.setRelevancyId(offSupplyPurchase.getId());
                    file.setStatus("0");
                    //保存新附件
                    offSupplyFilesMapper.updateOffSupplyFiles(file);
                }
            }
            if (offSupplyPurchase.getStatus().equals("1")) {
                SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
                successMessage = sysUser.getNickName() + "修改编号为【" + offSupplyPurchase.getOrdersCode() + "】的办公用品采购单,并提交了采购申请";
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFPURCHASEMAIN.getCode(), successMessage, 2, errorMessage, "");

            }
            result = offSupplyPurchaseMapper.updateOffSupplyPurchase(offSupplyPurchase);
        }
        ajaxResult.put("id", offSupplyPurchase.getId());
        if (result > 0) {
            ajaxResult.put("code", 200);
            ajaxResult.put("msg", "修改办公用品领用成功");
        }else {
            ajaxResult.put("code", 500);
            ajaxResult.put("msg", "修改办公用品领用失败");
        }
        return ajaxResult;
    }

    /**
     * 批量删除办公用品采购单
     * @param ids 需要删除的办公用品采购单主键
     * @return 结果
     */
    @Override
    public int deleteOffSupplyPurchaseByIds(Long[] ids)
    {
        return offSupplyPurchaseMapper.deleteOffSupplyPurchaseByIds(ids);
    }

    /**
     * 删除办公用品采购单信息
     * @param id 办公用品采购单主键
     * @return 结果
     */
    @Override
    public int deleteOffSupplyPurchaseById(Long id)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        int result = 0;
        String errorMessage = "";
        String successMessage = "";
        //根据采购单id查询采购单信息
        OffSupplyPurchase offSupplyPurchase = offSupplyPurchaseMapper.selectOffSupplyPurchaseById(id);
        SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
        try {
            //逻辑删除附件表信息
            offSupplyFilesMapper.deleteOffSupplyFilesBySupplyIdAndType(id, "3");
            result = offSupplyPurchaseMapper.deleteOffSupplyPurchaseById(id);
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
        } finally {
            if (offSupplyPurchase.getStatus().equals("1") || offSupplyPurchase.getStatus().equals("2")){
                successMessage = sysUser.getNickName() + "删除编号为【" + offSupplyPurchase.getOrdersCode() + "】的办公用品采购单";
                sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFPURCHASEMAIN.getCode(), successMessage, 3, errorMessage, "");
            }
        }
        return result;
    }

    /**
     * 修改流程状态
     * status 流程状态(驳回传1(审核中)，废弃和审核不通过传2，审核通过传3)
     * @param supplyPurchaseVo
     * @return
     */
    @Override
    public int updateInfoByRequestInfo(SupplyPurchaseVo supplyPurchaseVo) {
        int i = 0;
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //判断是否是提交状态
        if(supplyPurchaseVo.getStatus().equals("1")){
            //取采购单id数组
            Long[] ids = supplyPurchaseVo.getIds();
            if (ids.length > 0){
                //批量更新采购单为提交状态和流程id以及发起流程时所选公司id
                i = offSupplyPurchaseMapper.updateOffSupplyPurchaseByIds(ids, supplyPurchaseVo.getProcessId(), supplyPurchaseVo.getCompanyId(), supplyPurchaseVo.getStatus());
            }
            //插入流程发起时的表单json
            if (!Objects.isNull(supplyPurchaseVo.getOffReceivePurchaseDetail())){
                //先根据流程id查询是否存在此流程，存在则删除旧数据
                offReceiveMainMapper.deleteOffReceivePurchaseInfo(supplyPurchaseVo.getProcessId());
                OffReceivePurchaseDetail offReceivePurchaseDetail = supplyPurchaseVo.getOffReceivePurchaseDetail();
                offReceivePurchaseDetail.setCreateBy(loginUser.getUsername());
                offReceivePurchaseDetail.setCreateTime(DateUtils.getNowDate());
                offReceivePurchaseDetail.setProcessId(supplyPurchaseVo.getProcessId());
                offReceiveMainMapper.insertOffReceivePurchaseInfo(offReceivePurchaseDetail);
            }
        }
        //审批通过
        if (supplyPurchaseVo.getStatus().equals("3")){
            //取采购单id数组
            Long[] ids = supplyPurchaseVo.getIds();
            if (ids.length > 0){
                for (Long id : ids) {
                    //根据采购单id查询采购单信息
                    OffSupplyPurchase offSupplyPurchase = offSupplyPurchaseMapper.selectOffSupplyPurchaseById(id);
                    //查询物品系统编码
                    OffSupplyMain offSupplyMain = offSupplyMainMapper.selectOffSupplyMainById(offSupplyPurchase.getSupplyId());
                    //根据物品id查询物品信息
                    OffSupplyMain supplyMain = offSupplyMainMapper.selectOffSupplyMainBySysCode(offSupplyMain.getSysCode());

                    //根据物品id查询物品详情(保存修改记录用)
                    OffSupplyMain SupplyMainToBeJson = offSupplyMainService.selectOffSupplyMainById(offSupplyPurchase.getSupplyId());//查询旧数据
                    //转换为json(旧json)
                    String oldJson = JSON.toJSONString(SupplyMainToBeJson);

                    //现有物品数量
                    Integer amount = supplyMain.getAmount();
                    //本次采购的物品数量
                    Integer applyNum = offSupplyPurchase.getAmount();
                    //最新的物品数量
                    Integer newAmount = amount + applyNum;

                    //更新物品库存数量，其他值不变，然后转换为json(为修改记录使用)
                    SupplyMainToBeJson.setAmount(newAmount);
                    //转换为json(新json)
                    String newJson = JSON.toJSONString(SupplyMainToBeJson);

                    //更新物品库存数量
                    OffSupplyMain main = new OffSupplyMain();
                    main.setAmount(newAmount);
                    main.setId(supplyMain.getId());
                    main.setUpdateTime(DateUtils.getNowDate());
                    //修改记录入库
                    OffSupplyHistory offSupplyHistory = new OffSupplyHistory();
                    offSupplyHistory.setSupplyId(offSupplyMain.getId());//物品id
                    offSupplyHistory.setSupplySysCode(offSupplyMain.getSysCode());// 物品系统编号
                    offSupplyHistory.setOldSupplyName(offSupplyMain.getItemName());// 修改前物品名称
                    offSupplyHistory.setNewSupplyName(offSupplyMain.getItemName());// 修改后物品名称
                    offSupplyHistory.setOldJson(oldJson);//修改前的json
                    offSupplyHistory.setNewJson(newJson);//修改后的json
                    offSupplyHistory.setOldAmount(amount);// 修改前物品数量
                    offSupplyHistory.setNewAmount(newAmount);// 修改后物品数量
                    offSupplyHistory.setUpdateBy(offSupplyPurchase.getCreateBy());// 修改人
                    offSupplyHistory.setUpdateTime(DateUtils.getNowDate());// 修改时间
                    offSupplyHistory.setUpdateRemark("办公用品采购单审批通过后增加库存(采购单编号：" + offSupplyPurchase.getOrdersCode() + ")");
                    offSupplyMainMapper.insertOffSupplyHistory(offSupplyHistory);

                    //更新物品库存
                    offSupplyMainMapper.updateOffSupplyMain(main);
                    //更新采购单状态
                    OffSupplyPurchase supplyPurchase = new OffSupplyPurchase();
                    supplyPurchase.setId(id);
                    supplyPurchase.setStatus("3");
                    offSupplyPurchaseMapper.updateOffSupplyPurchaseStatus(supplyPurchase);
                }
            }
        }
        //审批不通过或废弃
        if (supplyPurchaseVo.getStatus().equals("2")){
            //取采购单id数组
            Long[] ids = supplyPurchaseVo.getIds();
            if (ids.length > 0){
                //批量更新为废弃或审批不通过
                i = offSupplyPurchaseMapper.updateOffSupplyPurchaseByIds(ids, supplyPurchaseVo.getProcessId(), supplyPurchaseVo.getCompanyId(), supplyPurchaseVo.getStatus());
            }
        }
        return i;
    }
}
