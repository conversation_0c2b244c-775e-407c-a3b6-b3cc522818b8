package org.ruoyi.core.offSupply.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.FunctionNodeEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysOperLogService;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.ruoyi.core.offSupply.domain.OffNotifyUser;
import org.ruoyi.core.offSupply.domain.OffSupplyMain;
import org.ruoyi.core.offSupply.mapper.OffSupplyMainMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.offSupply.mapper.OffCategoryMainMapper;
import org.ruoyi.core.offSupply.domain.OffCategoryMain;
import org.ruoyi.core.offSupply.service.IOffCategoryMainService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 类别维护Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
@Service
public class OffCategoryMainServiceImpl implements IOffCategoryMainService 
{
    @Autowired
    private OffCategoryMainMapper offCategoryMainMapper;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityServiceImpl;

    @Autowired
    private OffSupplyMainMapper offSupplyMainMapper;

    @Autowired
    private ISysOperLogService sysOperLogService;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询类别维护详情
     * 
     * @param id 类别维护主键
     * @return 类别维护
     */
    @Override
    public OffCategoryMain selectOffCategoryMainById(Long id)
    {
        OffCategoryMain offCategoryMain = offCategoryMainMapper.selectOffCategoryMainById(id);
        //根据类别id查询库存预警人员
        List<OffNotifyUser> offNotifyUserList = offCategoryMainMapper.selectOffNotifyUserByCategoryId(id);
        if (!CollectionUtils.isEmpty(offNotifyUserList)){
            offCategoryMain.setOffNotifyUserList(offNotifyUserList);
        }
        return offCategoryMain;
    }

    /**
     * 查询类别维护列表
     * 
     * @param offCategoryMain 类别维护
     * @return 类别维护
     */
    @Override
    public List<OffCategoryMain> selectOffCategoryMainList(OffCategoryMain offCategoryMain)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 新权限->获取用户有哪些公司权限
        List<Long> authorityCompanyIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.OFFSUPPLY.getCode());
        if (CollectionUtils.isEmpty(authorityCompanyIds)){
           return new ArrayList<>();
        }
        offCategoryMain.setAuthCompanyIds(authorityCompanyIds);
        if (offCategoryMain.getPageSize() != null && offCategoryMain.getPageNum() != null){
            PageHelper.startPage(offCategoryMain.getPageNum(),offCategoryMain.getPageSize());
        }
        List<OffCategoryMain> offCategoryMains = offCategoryMainMapper.selectOffCategoryMainList(offCategoryMain);
        return offCategoryMains;
    }

    /**
     * 新增类别维护
     * 
     * @param offCategoryMain 类别维护
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertOffCategoryMain(OffCategoryMain offCategoryMain) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String errorMessage = "";
        String successMessage = "";
        int i = 0;
        try {
            //如果是顶级类别，则父id为0
            if (offCategoryMain.getParentId() == null) {
                offCategoryMain.setParentId(0L);
            }
            offCategoryMain.setCreateTime(DateUtils.getNowDate());
            offCategoryMain.setCreateBy(loginUser.getUsername());
            i = offCategoryMainMapper.insertOffCategoryMain(offCategoryMain);
            {
                List<OffNotifyUser> offNotifyUserList = offCategoryMain.getOffNotifyUserList();
                Long id = offCategoryMain.getId();
                if (!CollectionUtils.isEmpty(offNotifyUserList)) {
                    List<OffNotifyUser> list = new ArrayList<OffNotifyUser>();
                    for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                        offNotifyUser.setCategoryId(id);
                        offNotifyUser.setCreateBy(loginUser.getUsername());
                        offNotifyUser.setCreateTime(DateUtils.getNowDate());
                        list.add(offNotifyUser);
                    }
                    if (list.size() > 0) {
                        offCategoryMainMapper.batchOffNotifyUser(list);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
        } finally {
            //查询当前登录用户姓名
            SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
            successMessage = sysUser.getNickName() + "新增了办公用品类别【" + offCategoryMain.getCategoryName() + "】";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFCATEGORYMAIN.getCode(), successMessage, 1, errorMessage, "");
            return i;
        }
    }

    /**
     * 修改类别维护
     * 
     * @param offCategoryMain 类别维护
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateOffCategoryMain(OffCategoryMain offCategoryMain) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String errorMessage = "";
        String successMessage = "";
        int i = 0;

        try {
            offCategoryMain.setUpdateTime(DateUtils.getNowDate());
            offCategoryMain.setUpdateBy(loginUser.getUsername());
            // 处理库存预警提醒人员
            {
                Long categoryId = offCategoryMain.getId();
                List<OffNotifyUser> offNotifyUserList = offCategoryMain.getOffNotifyUserList();
                if (!CollectionUtils.isEmpty(offNotifyUserList)) {
                    // 先根据类别id删除原有库存预警提醒人员
                    offCategoryMainMapper.deleteOffNotifyUserByCategoryId(categoryId);
                    List<OffNotifyUser> list = new ArrayList<OffNotifyUser>();
                    for (OffNotifyUser offNotifyUser : offNotifyUserList) {
                        offNotifyUser.setCategoryId(categoryId);
                        offNotifyUser.setCreateBy(loginUser.getUsername());
                        offNotifyUser.setCreateTime(DateUtils.getNowDate());
                        list.add(offNotifyUser);
                    }
                    if (list.size() > 0) {
                        offCategoryMainMapper.batchOffNotifyUser(list);
                    }
                }
            }
            i = offCategoryMainMapper.updateOffCategoryMain(offCategoryMain);
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
        } finally {
            // 查询当前登录用户姓名
            SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
            successMessage = sysUser.getNickName() + "修改了办公用品类别【" + offCategoryMain.getCategoryName() + "】";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFCATEGORYMAIN.getCode(), successMessage, 2, errorMessage, "");
            return i;
        }
    }

    /**
     * 批量删除类别维护
     * 
     * @param ids 需要删除的类别维护主键
     * @return 结果
     */
    @Override
    public int deleteOffCategoryMainByIds(Long[] ids)
    {
        return offCategoryMainMapper.deleteOffCategoryMainByIds(ids);
    }

    /**
     * 删除类别维护信息
     * 
     * @param id 类别维护主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteOffCategoryMainById(Long id) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String errorMessage = "";
        String successMessage = "";
        int i = 0;
        OffCategoryMain offCategoryMain = new OffCategoryMain();
        AjaxResult ajaxResult = new AjaxResult();
        try {
            offCategoryMain = selectOffCategoryMainById(id);
            //先判断当前类别是否是主类别
            if (offCategoryMain.getParentId() == 0) {
                //第一种情况：当前要删除的类别是主类，并且主类别没有任何子类和物品
                List<OffSupplyMain> offSupplyMainList = offSupplyMainMapper.selectOffSupplyMainByCategoryId(id);//查询当前类别下的物品
                List<OffCategoryMain> offCategoryMainList = offCategoryMainMapper.selectOffCategoryMainByParentId(id, "1");//查询当前主类别下是否存在子类别
                if (CollectionUtils.isEmpty(offSupplyMainList) && CollectionUtils.isEmpty(offCategoryMainList)) {
                    //删除当前主类别
                    offCategoryMainMapper.deleteOffCategoryMainById(id);
                    ajaxResult.put("code", 200);
                    ajaxResult.put("msg", "操作成功");
                    return ajaxResult;
                }
                //第二种情况：当前要删除的类别是主类，根据祖类别id，递归查询所有子类别下是否存在物品，不存在物品，则删除
                List<Long> descendantCategoryIds = findAllDescendantCategoryIds(id);//获取当前类别和其所有子类别
                //根据类别id集合查询物品
                if (CollectionUtils.isEmpty(descendantCategoryIds)) {
                    List<OffSupplyMain> offSupplyMains = offSupplyMainMapper.selectOffSupplyMainByCategoryIds(descendantCategoryIds);
                    if (CollectionUtils.isEmpty(offSupplyMains)) {
                        //为空，则表示不存在物品，则可以删除主类和其所有子类
                        //将类别id转换为array数组
                        Long[] ids = descendantCategoryIds.toArray(new Long[0]);
                        //删除当前主类别
                        offCategoryMainMapper.deleteOffCategoryMainByIds(ids);
                        ajaxResult.put("code", 200);
                        ajaxResult.put("msg", "操作成功");
                        return ajaxResult;
                    } else {
                        ajaxResult.put("code", 500);
                        ajaxResult.put("msg", "类别【" + offCategoryMain.getCategoryName() + "】为主类别，请转移本类别下的物品，再进行删除");
                        return ajaxResult;
                    }
                }
            }
            //第三种情况：当前要删除的类别是子类别
            if (offCategoryMain.getParentId() != 0) {
                List<Long> descendantCategoryIds = findAllDescendantCategoryIds(id);//获取当前类别和其所有子类别
                //根据类别id集合查询物品
                if (!CollectionUtils.isEmpty(descendantCategoryIds)) {
                    List<OffSupplyMain> offSupplyMains = offSupplyMainMapper.selectOffSupplyMainByCategoryIds(descendantCategoryIds);//根据类别id集合查询物品
                    //为空，则表示不存在物品，则可以删除当前类别和其所有子类
                    if (!CollectionUtils.isEmpty(offSupplyMains)) {
                        //将类别id转换为array数组
                        Long[] ids = descendantCategoryIds.toArray(new Long[0]);
                        //将当前类别和其所有子类别的所有物品，修改成当前类别的上一级类别目录下
                        Long categoryId = offCategoryMain.getParentId();
                        //批量修改物品所属类别id
                        if (!CollectionUtils.isEmpty(offSupplyMains)) {
                            List<Long> supplyIds = offSupplyMains.stream().map(OffSupplyMain::getId).collect(Collectors.toList());
                            offSupplyMainMapper.updateOffSupplyMainCategoryIdByCategoryIds(supplyIds, categoryId);
                        }
                        //删除当前主类别.
                        offCategoryMainMapper.deleteOffCategoryMainByIds(ids);
                        ajaxResult.put("code", 200);
                        ajaxResult.put("msg", "操作成功");
                        return ajaxResult;
                    }else {
                        Long[] ids = descendantCategoryIds.toArray(new Long[0]);
                        offCategoryMainMapper.deleteOffCategoryMainByIds(ids);
                        ajaxResult.put("code", 200);
                        ajaxResult.put("msg", "操作成功");
                        return ajaxResult;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
        } finally {
            // 查询当前登录用户姓名
            SysUser sysUser = sysUserMapper.selectUserByUserName(loginUser.getUsername());
            successMessage = sysUser.getNickName() + "删除了办公用品类别【" + offCategoryMain.getCategoryName() + "】";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.OFFSUPPLY.getCode(), FunctionNodeEnum.OFFCATEGORYMAIN.getCode(), successMessage, 3, errorMessage, "");
        }
        return ajaxResult;
    }

    /**
     * 根据类别ID递归查询所有子级、孙子级等类别ID
     *
     * @param id 类别ID
     * @return 包含所有子级、孙子级等类别ID的列表
     */
    public List<Long> findAllDescendantCategoryIds(Long id) {
        List<Long> descendantCategoryIds = new ArrayList<>();
        Set<Long> visitedIds = new HashSet<>(); // 用于记录已经访问过的类别ID

        // 辅助递归函数，增加visitedIds参数
        findAllDescendantCategoryIdsRecursive(id, descendantCategoryIds, visitedIds);

        return descendantCategoryIds;
    }

    private void findAllDescendantCategoryIdsRecursive(Long id, List<Long> descendantCategoryIds, Set<Long> visitedIds) {
        if (visitedIds.contains(id)) {
            // 如果已经访问过这个ID，则跳过，避免循环引用导致的死循环
            return;
        }

        visitedIds.add(id); // 标记当前ID为已访问

        // 查询当前类别下的子类别
        List<OffCategoryMain> offCategoryMainList = offCategoryMainMapper.selectOffCategoryMainByParentId(id, "2");
        for (OffCategoryMain category : offCategoryMainList) {
            // 添加当前子类别的ID到结果列表中
            descendantCategoryIds.add(category.getId());

            // 递归查询子类别的子类别
            findAllDescendantCategoryIdsRecursive(category.getId(), descendantCategoryIds, visitedIds);
        }
    }
    /**
     * 查询类别维护列表（树形结构）
     * @return
     */
    @Override
    public List<OffCategoryMain> selectOffCategoryMainListByTree() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        OffCategoryMain offCategoryMain = new OffCategoryMain();
        // 新权限->获取用户有哪些公司权限
        List<Long> authorityCompanyIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.OFFSUPPLY.getCode());
        if (CollectionUtils.isEmpty(authorityCompanyIds)) {
           return new ArrayList<>();
        }
        offCategoryMain.setAuthCompanyIds(authorityCompanyIds);

        // 查询到所有类别
        List<OffCategoryMain> offCategoryMains = offCategoryMainMapper.selectOffCategoryMainList(offCategoryMain);

        // 构建树形结构
        List<OffCategoryMain> tree = buildCategoryTree(offCategoryMains);

        return tree;
    }

    /**
     * 修改启停状态
     * @param offCategoryMain
     * @return
     */
    @Override
    public int updateOffCategoryMainStatus(OffCategoryMain offCategoryMain) {
        return offCategoryMainMapper.updateOffCategoryMain(offCategoryMain);
    }

    /**
     * 判断返回删除条件
     * @param id
     * @return
     */
    @Override
    public AjaxResult queryJudgeById(Long id) {
        AjaxResult ajaxResult = new AjaxResult();
        Boolean isParent = false;//是否是主类别标识
        Boolean hasChild = false;//是否存在子类别
        Boolean hasSupply = false;//是否存在物品

        //查询当前类别是否是主类别
        OffCategoryMain offCategoryMain = offCategoryMainMapper.selectOffCategoryMainById(id);
        if (offCategoryMain.getParentId() == 0){
            isParent = true;
        }
        //查询当前类别是否有下级类别
        List<OffCategoryMain> offCategoryMainList = offCategoryMainMapper.selectOffCategoryMainByParentId(id, "1");
        if(!CollectionUtils.isEmpty(offCategoryMainList)){
            hasChild = true;
        }
        //查询当前类别下是否存在物品
        List<OffSupplyMain> offSupplyMainList = offSupplyMainMapper.selectOffSupplyMainByCategoryId(id);
        if (!CollectionUtils.isEmpty(offSupplyMainList)){
            hasSupply = true;
        }

        ajaxResult.put("isParent",isParent);
        ajaxResult.put("hasChild",hasChild);
        ajaxResult.put("hasSupply",hasSupply);
        return ajaxResult;
    }

    /**
     * 辅助方法：构建类别维护的树形结构
     *
     * @param offCategoryMains 类别维护列表
     * @return 树形结构的类别维护列表
     */
    public List<OffCategoryMain> buildCategoryTree(List<OffCategoryMain> offCategoryMains) {
        List<OffCategoryMain> tree = new ArrayList<>();
        Map<Long, OffCategoryMain> idToCategoryMap = new HashMap<>();

        // 将所有类别按ID存入Map，方便后续查找
        for (OffCategoryMain category : offCategoryMains) {
            idToCategoryMap.put(category.getId(), category);
        }

        // 遍历类别列表，构建树形结构
        for (OffCategoryMain category : offCategoryMains) {
            // 顶级类别（parentId为0或null）作为根节点
            if (category.getParentId() == 0 || category.getParentId() == null) {
                tree.add(findChildren(category, idToCategoryMap));
            }
        }

        return tree;
    }

    /**
     * 辅助方法：递归查找并设置子类别
     *
     * @param parent 父类别
     * @param idToCategoryMap 类别ID到类别的映射
     * @return 带有子类别的父类别
     */
    public OffCategoryMain findChildren(OffCategoryMain parent, Map<Long, OffCategoryMain> idToCategoryMap) {
        List<OffCategoryMain> children = new ArrayList<>();
        for (OffCategoryMain category : idToCategoryMap.values()) {
            // 如果当前类别的父ID等于传入父类别的ID，则它是该父类别的子类别
            if (parent.getId().equals(category.getParentId())) {
                // 递归查找子类别的子类别
                OffCategoryMain childWithChildren = findChildren(category, idToCategoryMap);
                children.add(childWithChildren);
            }
        }
        // 设置父类别的子类别列表
        parent.setChildren(children);
        return parent;
    }
}
