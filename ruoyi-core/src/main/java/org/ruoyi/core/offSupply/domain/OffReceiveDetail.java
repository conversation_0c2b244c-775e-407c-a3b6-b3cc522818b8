package org.ruoyi.core.offSupply.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 办公用品申请详情对象 off_receive_detail
 * 
 * <AUTHOR>
 * @date 2025-03-21
 */
public class OffReceiveDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 领用申请ID */
    @Excel(name = "领用申请ID")
    private Long receiveId;

    /** 物品ID */
    @Excel(name = "物品ID")
    private Long supplyId;

    /** 物品名称 */
    private String supplyName;

    /** 物品类型 */
    private String itemType;

    /** 申请前数量 */
    private Integer beforeNum;

    /** 申请数量 */
    @Excel(name = "申请数量")
    private Integer applyNum;

    /** 剩余数量 */
    @Excel(name = "剩余数量")
    private Integer residueNum;

    /** 流程ID */
    @Excel(name = "流程ID")
    private String processId;

    /** 物品所属类别 */
    private String categoryName;

    /** 计量单位 */
    private String measureUnit;

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getSupplyName() {
        return supplyName;
    }

    public void setSupplyName(String supplyName) {
        this.supplyName = supplyName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setReceiveId(Long receiveId) 
    {
        this.receiveId = receiveId;
    }

    public Long getReceiveId() 
    {
        return receiveId;
    }

    public Long getSupplyId() {
        return supplyId;
    }

    public void setSupplyId(Long supplyId) {
        this.supplyId = supplyId;
    }

    public Integer getBeforeNum() {
        return beforeNum;
    }

    public void setBeforeNum(Integer beforeNum) {
        this.beforeNum = beforeNum;
    }

    public Integer getApplyNum() {
        return applyNum;
    }

    public void setApplyNum(Integer applyNum) {
        this.applyNum = applyNum;
    }

    public Integer getResidueNum() {
        return residueNum;
    }

    public void setResidueNum(Integer residueNum) {
        this.residueNum = residueNum;
    }

    public void setProcessId(String processId)
    {
        this.processId = processId;
    }

    public String getProcessId() 
    {
        return processId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("receiveId", getReceiveId())
            .append("supplyId", getSupplyId())
            .append("applyNum", getApplyNum())
            .append("residueNum", getResidueNum())
            .append("processId", getProcessId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}
