package org.ruoyi.core.meeting.domain;

import lombok.Data;
import org.apache.catalina.Manager;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 会议室管理对象 hy_meeting_room
 *
 * <AUTHOR>
 * @date 2024-10-24
 */
@Data
public class MeetingRoom extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 会议室名称 */
    @Excel(name = "会议室名称")
    private String meetingRoomName;

    /** 会议室编号 */
    @Excel(name = "会议室编号")
    private String meetingRoomCode;

    /** 会议室地点 */
    @Excel(name = "会议室地点")
    private String meetingRoomLocation;

    /** 会议室负责人 */
    //@Excel(name = "会议室负责人")
    private String meetingRoomManager;

    /** 会议室状态 */
    @Excel(name = "会议室状态" , readConverterExp = "0=启用,1=停用")
    private String meetingRoomStatus;

//    /** 所属公司 */
//    @Excel(name = "所属公司")
//    private Long belongCompany;

    /** 会议提醒条件 */
    @Excel(name = "会议提醒条件" , readConverterExp = "1=会议录音开启提醒 ,2=会议录像开启提醒 ,3=会议开启提醒")
    private String meetingReminderConditions;

    /** 会议开始前提醒时间(小时) */
    @Excel(name = "会议开始前提醒时间(小时)")
    private Integer meetingStartTimeReminderHour;

    /** 会议开始前提醒时间(分钟) */
    @Excel(name = "会议开始前提醒时间(分钟)")
    private Integer meetingStartTimeReminderMinute;

    /** 会议结束前提醒时间(小时) */
    @Excel(name = "会议结束前提醒时间(小时)")
    private Integer meetingEndTimeReminderHour;

    /** 会议结束前提醒时间(分钟) */
    @Excel(name = "会议结束前提醒时间(分钟)")
    private Integer meetingEndTimeReminderMinute;

    /** 会议通知方式 */
    @Excel(name = "会议通知方式" , readConverterExp = "1=系统代办通知,2=企业微信通知")
    private String meetingNotificationMethod;

    /** 会议开始前通知内容 */
    @Excel(name = "会议开始前通知内容")
    private String meetingStartNotification;

    /** 会议结束前通知内容 */
    @Excel(name = "会议结束前通知内容")
    private String meetingEndNotification;

    /** 会议结束前通知内容 */
    @Excel(name = "会议取消通知内容")
    private String meetingCancelNotification;

    /** 会议室说明 */
    @Excel(name = "会议室说明")
    private String description;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("meetingRoomName", getMeetingRoomName())
                .append("meetingRoomCode", getMeetingRoomCode())
                .append("meetingRoomLocation", getMeetingRoomLocation())
                .append("meetingRoomStatus", getMeetingRoomStatus())
//                .append("belongCompany", getBelongCompany())
                .append("meetingReminderConditions", getMeetingReminderConditions())
                .append("meetingStartTimeReminderHour", getMeetingStartTimeReminderHour())
                .append("meetingStartTimeReminderMinute", getMeetingStartTimeReminderMinute())
                .append("meetingEndTimeReminderHour", getMeetingEndTimeReminderHour())
                .append("meetingEndTimeReminderMinute", getMeetingEndTimeReminderMinute())
                .append("meetingNotificationMethod", getMeetingNotificationMethod())
                .append("meetingStartNotification", getMeetingStartNotification())
                .append("meetingEndNotification", getMeetingEndNotification())
                .append("meetingCancelNotification", getMeetingCancelNotification())
                .append("description", getDescription())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
