package org.ruoyi.core.meeting.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.meeting.domain.MeetingFile;
import org.ruoyi.core.meeting.domain.MeetingRoom;
import org.ruoyi.core.meeting.domain.vo.MeetingFileVo;
import org.ruoyi.core.meeting.domain.vo.MeetingRoomVo;
import org.ruoyi.core.meeting.mapper.MeetingRoomMapper;
import org.ruoyi.core.meeting.service.IMeetingFileService;
import org.ruoyi.core.meeting.service.IMeetingRoomService;
import org.ruoyi.core.personnel.domain.PersonnelFile;
import org.ruoyi.core.personnel.domain.vo.PersonnelFileVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 会议室管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-24
 */
@Service
public class MeetingRoomServiceImpl implements IMeetingRoomService
{
    @Autowired
    private MeetingRoomMapper meetingRoomMapper;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IMeetingFileService meetingFileService;
    /**
     * 查询会议室管理
     *
     * @param id 会议室管理主键
     * @return 会议室管理
     */
    @Override
    public MeetingRoomVo selectMeetingRoomById(Long id)
    {
        MeetingRoomVo meetingRoom = meetingRoomMapper.selectMeetingRoomById(id);
        if(meetingRoom.getMeetingRoomManagerIds() != null && !meetingRoom.getMeetingRoomManagerIds().isEmpty()){
            List<SysUser> sysUsers = sysUserService.selectUserByUserIds(meetingRoom.getMeetingRoomManagerIds());
            meetingRoom.setMeetingRoomManagerList(sysUsers);
        }
        MeetingFile forFile = new MeetingFile();
        forFile.setCorrelationId(meetingRoom.getId());
        forFile.setFileState("1");
        forFile.setFileType("1");
        List<MeetingFile> forFiles = meetingFileService.selectMeetingFileList(forFile);
        meetingRoom.setFiles(forFiles);

        return meetingRoom;
    }

    /**
     * 查询会议室管理列表
     *
     * @param meetingRoom 会议室管理
     * @return 会议室管理
     */
    @Override
    public List<MeetingRoomVo> selectMeetingRoomList(MeetingRoomVo meetingRoom)
    {
        List<MeetingRoomVo> meetingRooms = meetingRoomMapper.selectMeetingRoomList(meetingRoom);
        List<Long> meetingRoomManagerList = meetingRooms.stream()
                .flatMap(vo -> vo.getMeetingRoomManagerIds().stream())
                .collect(Collectors.toList());
        if (!meetingRooms.isEmpty() && !meetingRoomManagerList.isEmpty()) {
            List<SysUser> sysUsers = sysUserService.selectUserByUserIds(meetingRoomManagerList);
            Map<Long, SysUser> sysUserMap = sysUsers.stream()
                    .collect(Collectors.toMap(SysUser::getUserId, sysUser -> sysUser));
            meetingRooms.forEach(vo -> {
                if (vo.getMeetingRoomManagerIds() != null && !vo.getMeetingRoomManagerIds().isEmpty()) {
                    List<SysUser> sysUsers1 = vo.getMeetingRoomManagerIds().stream()
                            .filter(sysUserMap::containsKey)
                            .map(sysUserMap::get)
                            .collect(Collectors.toList());
                    vo.setMeetingRoomManagerList(sysUsers1);
                }
            });
        }
        return meetingRooms;
    }

    /**
     * 新增会议室管理
     *
     * @param meetingRoom 会议室管理
     * @return 结果
     */
    @Override
    public int insertMeetingRoom(MeetingRoomVo meetingRoom)
    {
        meetingRoom.setCreateTime(DateUtils.getNowDate());
        LoginUser loginUser = getLoginUser();
        meetingRoom.setCreateBy(loginUser.getUser().getUserName());
        meetingRoom.setMeetingRoomCode("HYS" + String.format("%02d", (meetingRoomMapper.getCount()) + 1));
        int i = meetingRoomMapper.insertMeetingRoom(meetingRoom);
        if (meetingRoom.getFileIds() != null){
            MeetingFileVo meetingFile = new MeetingFileVo();
            meetingFile.setIds(meetingRoom.getFileIds());
            meetingFile.setCorrelationId(meetingRoom.getId());
            meetingFileService.correlationFile(meetingFile);
        }
        return i;
    }

    /**
     * 修改会议室管理
     *
     * @param meetingRoom 会议室管理
     * @return 结果
     */
    @Override
    public int updateMeetingRoom(MeetingRoomVo meetingRoom)
    {
        meetingRoom.setUpdateTime(DateUtils.getNowDate());

        //修改前先取消关联文件
        MeetingFileVo deletelFile = new MeetingFileVo();
        deletelFile.setFileType("1");
        deletelFile.setCorrelationId(meetingRoom.getId());
        meetingFileService.deleteByCorrelationId(deletelFile);
        //关联最新的文件
        if(meetingRoom.getFileIds() != null && !meetingRoom.getFileIds().isEmpty()){
            MeetingFileVo personnelFile = new MeetingFileVo();
            personnelFile.setIds(meetingRoom.getFileIds());
            personnelFile.setCorrelationId(meetingRoom.getId());
            meetingFileService.correlationFile(personnelFile);
        }
        return meetingRoomMapper.updateMeetingRoom(meetingRoom);
    }

    /**
     * 批量删除会议室管理
     *
     * @param ids 需要删除的会议室管理主键
     * @return 结果
     */
    @Override
    public int deleteMeetingRoomByIds(Long[] ids)
    {
        return meetingRoomMapper.deleteMeetingRoomByIds(ids);
    }

    /**
     * 删除会议室管理信息
     *
     * @param id 会议室管理主键
     * @return 结果
     */
    @Override
    public int deleteMeetingRoomById(Long id)
    {
        return meetingRoomMapper.deleteMeetingRoomById(id);
    }

    @Override
    public AjaxResult uploadFile(MultipartFile file){
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.MEETING_SYSTEM, file);
            MeetingFile meetingFile = new MeetingFile();
            meetingFile.setFileUrl(url);
            meetingFile.setFileName(name);
            meetingFile.setFileState("0");
            meetingFile.setFileType("1");
            meetingFile.setCreateTime(DateUtils.getNowDate());
            meetingFile.setCreateBy(getUsername());
            meetingFileService.insertMeetingFile(meetingFile);
            return AjaxResult.success(meetingFile);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
