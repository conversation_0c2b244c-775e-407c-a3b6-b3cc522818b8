package org.ruoyi.core.meeting.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.meeting.domain.MeetingRoom;
import org.ruoyi.core.meeting.domain.vo.MeetingRoomVo;
import org.ruoyi.core.meeting.service.IMeetingRoomService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 会议室管理Controller
 *
 * <AUTHOR>
 * @date 2024-10-24
 */
@RestController
@RequestMapping("/meeting/room")
public class MeetingRoomController extends BaseController
{
    @Autowired
    private IMeetingRoomService meetingRoomService;

    /**
     * 查询会议室管理列表
     */
    //@PreAuthorize("@ss.hasPermi('meeting:room:list')")
    @GetMapping("/list")
    public TableDataInfo list(MeetingRoomVo meetingRoom)
    {
        startPage();
        List<MeetingRoomVo> list = meetingRoomService.selectMeetingRoomList(meetingRoom);
        return getDataTable(list);
    }

    /**
     * 导出会议室管理列表
     */
    //@PreAuthorize("@ss.hasPermi('meeting:room:export')")
    @Log(title = "会议室管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MeetingRoomVo meetingRoom)
    {
        List<MeetingRoomVo> list = meetingRoomService.selectMeetingRoomList(meetingRoom);
        list.forEach(vo -> {
            if (vo.getMeetingRoomManagerList() != null && !vo.getMeetingRoomManagerList().isEmpty()) {
                String MeetingRoomManagerListString = vo.getMeetingRoomManagerList().stream()
                        .map(SysUser::getNickName)  // 获取每个 User 的 nickname
                        .collect(Collectors.joining(","));  // 拼接成一个字符串，逗号分隔
                vo.setMeetingRoomManagerListString(MeetingRoomManagerListString);
            }
        });
        ExcelUtil<MeetingRoomVo> util = new ExcelUtil<MeetingRoomVo>(MeetingRoomVo.class);
        util.exportExcel(response, list, "会议室管理数据");
    }

    /**
     * 获取会议室管理详细信息
     */
    //@PreAuthorize("@ss.hasPermi('meeting:room:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(meetingRoomService.selectMeetingRoomById(id));
    }

    /**
     * 新增会议室管理
     */
    //@PreAuthorize("@ss.hasPermi('meeting:room:add')")
    @Log(title = "会议室管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MeetingRoomVo meetingRoom)
    {
        return toAjax(meetingRoomService.insertMeetingRoom(meetingRoom));
    }

    /**
     * 修改会议室管理
     */
    //@PreAuthorize("@ss.hasPermi('meeting:room:edit')")
    @Log(title = "会议室管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MeetingRoomVo meetingRoom)
    {
        return toAjax(meetingRoomService.updateMeetingRoom(meetingRoom));
    }

    /**
     * 删除会议室管理
     */
    //@PreAuthorize("@ss.hasPermi('meeting:room:remove')")
    @Log(title = "会议室管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(meetingRoomService.deleteMeetingRoomByIds(ids));
    }

    @Anonymous
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) {
        return meetingRoomService.uploadFile(file);
    }
}
