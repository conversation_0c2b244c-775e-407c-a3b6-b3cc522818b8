package org.ruoyi.core.dm.domain;

import java.math.BigDecimal;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 产品首逾率对象 dm_aqa_fpd_analysis
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@Data
public class DmAqaFpdAnalysis extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 产品编号 */
    //@Excel(name = "产品编号")
    private String productNo;

    /** 放款月份 */
    @Excel(name = "放款月份", sort = 2)
    private String loanMonth;

    @Excel(name = "放款金额", sort = 3)
    private BigDecimal loanAmt;

    /** 首逾一天及以上本金 */
    //@Excel(name = "首逾一天及以上本金")
    private BigDecimal balDpd1;

    /** 首逾三天及以上本金 */
    //@Excel(name = "首逾三天及以上本金")
    private BigDecimal balDpd3;

    /** 首逾七天及以上本金 */
   // @Excel(name = "首逾七天及以上本金")
    private BigDecimal balDpd7;

    /** 第一期应还本金 */
    //@Excel(name = "第一期应还本金")
    private BigDecimal term1Principal;

    /** 总笔数 */
    //@Excel(name = "总笔数")
    private Long totalCnt;

    /** 逾期一天及以上的笔数 */
    //@Excel(name = "逾期一天及以上的笔数")
    private Long cntDpd1;

    /** 逾期三天及以上的笔数 */
   // @Excel(name = "逾期三天及以上的笔数")
    private Long cntDpd3;

    /** 逾期七天及以上的笔数 */
    //@Excel(name = "逾期七天及以上的笔数")
    private Long cntDpd7;

}
