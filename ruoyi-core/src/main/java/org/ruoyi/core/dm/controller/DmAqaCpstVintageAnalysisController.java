package org.ruoyi.core.dm.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import org.ruoyi.core.dm.domain.DmAqaCpstVintageAnalysis;
import org.ruoyi.core.dm.domain.vo.DmAqaCpstVintageAnalysisVo;
import org.ruoyi.core.dm.service.IDmAqaCpstVintageAnalysisService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 产品代偿率Controller
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@RestController
@RequestMapping("/cpstVintage/analysis")
public class DmAqaCpstVintageAnalysisController extends BaseController
{
    @Autowired
    private IDmAqaCpstVintageAnalysisService dmAqaCpstVintageAnalysisService;

    /**
     * 查询产品代偿率列表
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody DmAqaCpstVintageAnalysisVo dmAqaCpstVintageAnalysis)
    {
        List<DmAqaCpstVintageAnalysisVo> list = dmAqaCpstVintageAnalysisService.selectDmAqaCpstVintageAnalysisList(dmAqaCpstVintageAnalysis);
        return getDataTable(list);
    }

    /**
     * 导出产品代偿率列表
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:export')")
    @Log(title = "产品代偿率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DmAqaCpstVintageAnalysisVo dmAqaCpstVintageAnalysis)
    {
        Gson gson = new Gson();
        Map<String,String> resultMap = gson.fromJson(dmAqaCpstVintageAnalysis.getSortMapString(), Map.class);
        dmAqaCpstVintageAnalysis.setSortMap(resultMap);
        List<DmAqaCpstVintageAnalysisVo> list = dmAqaCpstVintageAnalysisService.selectDmAqaCpstVintageAnalysisList(dmAqaCpstVintageAnalysis);
        ExcelUtil<DmAqaCpstVintageAnalysisVo> util = new ExcelUtil<DmAqaCpstVintageAnalysisVo>(DmAqaCpstVintageAnalysisVo.class);
        util.exportExcel(response, list, "产品代偿率数据");
    }

    /**
     * 获取产品代偿率详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(dmAqaCpstVintageAnalysisService.selectDmAqaCpstVintageAnalysisById(id));
    }

    /**
     * 新增产品代偿率
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:add')")
    @Log(title = "产品代偿率", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DmAqaCpstVintageAnalysis dmAqaCpstVintageAnalysis)
    {
        return toAjax(dmAqaCpstVintageAnalysisService.insertDmAqaCpstVintageAnalysis(dmAqaCpstVintageAnalysis));
    }

    /**
     * 修改产品代偿率
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:edit')")
    @Log(title = "产品代偿率", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DmAqaCpstVintageAnalysis dmAqaCpstVintageAnalysis)
    {
        return toAjax(dmAqaCpstVintageAnalysisService.updateDmAqaCpstVintageAnalysis(dmAqaCpstVintageAnalysis));
    }

    /**
     * 删除产品代偿率
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:remove')")
    @Log(title = "产品代偿率", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dmAqaCpstVintageAnalysisService.deleteDmAqaCpstVintageAnalysisByIds(ids));
    }
}
