package org.ruoyi.core.dm.mapper;

import org.ruoyi.core.dm.domain.DmAqaVintageAnalysis;
import org.ruoyi.core.dm.domain.vo.DmAqaVintageAnalysisVo;
import org.ruoyi.core.domain.DProjectParameter;

import java.util.List;

/**
 * 产品vintage分析Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
public interface DmAqaVintageAnalysisMapper
{
    /**
     * 查询产品vintage分析
     *
     * @param id 产品vintage分析主键
     * @return 产品vintage分析
     */
    public DmAqaVintageAnalysis selectDmAqaVintageAnalysisById(Long id);

    /**
     * 查询产品vintage分析列表
     *
     * @param dmAqaVintageAnalysis 产品vintage分析
     * @return 产品vintage分析集合
     */
    public List<DmAqaVintageAnalysisVo> selectDmAqaVintageAnalysisList(DmAqaVintageAnalysisVo dmAqaVintageAnalysis);

    /**
     * 新增产品vintage分析
     *
     * @param dmAqaVintageAnalysis 产品vintage分析
     * @return 结果
     */
    public int insertDmAqaVintageAnalysis(DmAqaVintageAnalysis dmAqaVintageAnalysis);

    /**
     * 修改产品vintage分析
     *
     * @param dmAqaVintageAnalysis 产品vintage分析
     * @return 结果
     */
    public int updateDmAqaVintageAnalysis(DmAqaVintageAnalysis dmAqaVintageAnalysis);

    /**
     * 删除产品vintage分析
     *
     * @param id 产品vintage分析主键
     * @return 结果
     */
    public int deleteDmAqaVintageAnalysisById(Long id);

    /**
     * 批量删除产品vintage分析
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDmAqaVintageAnalysisByIds(Long[] ids);

    public List<DProjectParameter> selectDProjectParameter(DmAqaVintageAnalysisVo dmAqaVintageAnalysis);

}
