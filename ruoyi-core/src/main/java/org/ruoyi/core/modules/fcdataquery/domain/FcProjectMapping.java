package org.ruoyi.core.modules.fcdataquery.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目和科目关联对象 fc_project_mapping
 * 
 * <AUTHOR>
 * @date 2024-09-24
 */
public class FcProjectMapping extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Integer projectId;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 项目类型 */
    @Excel(name = "项目类型")
    private String projectType;

    /** 账套ID */
    @Excel(name = "账套ID")
    private Integer accountSetsId;

    /** 科目ID */
    @Excel(name = "科目ID")
    private Integer subjectId;

    /** 科目编码 */
    @Excel(name = "科目编码")
    private String subjectCode;

    /** 科目名称 */
    @Excel(name = "科目名称")
    private String subjectName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setProjectId(Integer projectId)
    {
        this.projectId = projectId;
    }

    public Integer getProjectId()
    {
        return projectId;
    }
    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public void setAccountSetsId(Integer accountSetsId)
    {
        this.accountSetsId = accountSetsId;
    }

    public Integer getAccountSetsId()
    {
        return accountSetsId;
    }
    public void setSubjectId(Integer subjectId)
    {
        this.subjectId = subjectId;
    }

    public Integer getSubjectId()
    {
        return subjectId;
    }
    public void setSubjectCode(String subjectCode) 
    {
        this.subjectCode = subjectCode;
    }

    public String getSubjectCode() 
    {
        return subjectCode;
    }
    public void setSubjectName(String subjectName) 
    {
        this.subjectName = subjectName;
    }

    public String getSubjectName() 
    {
        return subjectName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("projectName", getProjectName())
            .append("projectType", getProjectType())
            .append("accountSetsId", getAccountSetsId())
            .append("subjectId", getSubjectId())
            .append("subjectCode", getSubjectCode())
            .append("subjectName", getSubjectName())
            .toString();
    }
}
