package org.ruoyi.core.modules.fcdataquery.service;

import org.ruoyi.core.modules.fcdataquery.po.ThirdTecServiceDetailPo;
import org.ruoyi.core.modules.fcdataquery.po.ThirdTecServicePo;
import org.ruoyi.core.modules.fcdataquery.vo.ParamsVo;
import org.ruoyi.core.modules.fcdataquery.vo.ThirdTecServiceDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.ThirdTecServiceVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 财务经营分析公共接口
 * @Date 2024/12/17 14:57
 * @Param
 * @return
 **/
public interface IFcCommonService
{

    /**
     * <AUTHOR>
     * @Description 获取项目集合
     * @Date 2024/12/17 14:56
     * @Param [paramsVo]
     * @return java.util.List<java.lang.Long>
     **/
    public List<Long> getProjectList(ParamsVo paramsVo);

    /**
     * <AUTHOR>
     * @Description 获取项目id集合
     * @Date 2024/12/26 14:58
     * @Param [paramsVo]
     * @return java.util.List<java.lang.Integer>
     **/
    public List<Integer> getProjectIds(ParamsVo paramsVo);

    /**
     * <AUTHOR>
     * @Description 获取用户关联公司对应的账套id
     * @Date 2024/12/26 13:51
     * @Param []
     * @return java.util.List<java.lang.Long>
     **/
    public List<Integer> getAccountSetsList();
}
