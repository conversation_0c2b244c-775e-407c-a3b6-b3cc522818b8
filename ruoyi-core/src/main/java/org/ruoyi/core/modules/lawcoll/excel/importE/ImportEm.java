package org.ruoyi.core.modules.lawcoll.excel.importE;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 法催对账历史对象 law_coll_check_history
 * 
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
public class ImportEm
{
    @ExcelIgnore
    private String sheetName;

    @ExcelProperty("借据号")
    private String cApplyNo;

    @ExcelProperty("资金方")
    private String fundCode;

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("回款金额")
    private BigDecimal cRepayTotalAmt;

    @ExcelProperty("回款本金")
    private BigDecimal cRepayPrinAmt;

    @ExcelProperty("回款日期")
    private String cRepayDate;

    @ExcelProperty("结清状态")
    private String status;

    @ExcelProperty("回款途径")
    private String repaySrc;

    @ExcelProperty("放款日期")
    private String loanTime;

    @ExcelProperty("代偿日期")
    private String cCompensatoryDate;

    @ExcelProperty("代偿总金额")
    private BigDecimal compensatoryTotalAmt;

    @ExcelProperty("代偿本金")
    private BigDecimal compensatoryPrinAmt;

    @ExcelProperty("批次")
    private String cBatchNo;

    @ExcelProperty("逾期天数")
    private Integer cOvdDays;

    @ExcelProperty("服务费费率")
    private String cServiceRate;

    @ExcelProperty("服务费")
    private BigDecimal cServiceAmt;
}
