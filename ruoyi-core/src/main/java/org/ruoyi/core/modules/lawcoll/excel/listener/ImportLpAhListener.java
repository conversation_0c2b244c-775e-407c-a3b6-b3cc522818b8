package org.ruoyi.core.modules.lawcoll.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.ruoyi.core.modules.lawcoll.domain.LawCollCheck;
import org.ruoyi.core.modules.lawcoll.domain.LawCollCheckDetail;
import org.ruoyi.core.modules.lawcoll.excel.importE.ImportLpAh;
import org.ruoyi.core.modules.lawcoll.mapper.LawCollCheckDetailMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模板的读取类
 *
 * <AUTHOR> Zhuang
 */
// 有个很重要的点 DemoDataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
@Slf4j
public class ImportLpAhListener implements ReadListener<ImportLpAh> {
    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    private List<ImportLpAh> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);


    /**
     * 假设这个是一个DAO，当然有业务逻辑这个也可以是一个service。当然如果不用存储这个对象没用。
     */

    private LawCollCheck lawCollCheck;
    private LawCollCheckDetailMapper lawCollCheckDetailMapper;


    /**
     * 如果使用了spring,请使用这个构造方法。每次创建Listener的时候需要把spring管理的类传进来
     *
     * @param lawCollCheckDetailMapper
     */
    public ImportLpAhListener(LawCollCheck lawCollCheck, LawCollCheckDetailMapper lawCollCheckDetailMapper) {
        this.lawCollCheck = lawCollCheck;
        this.lawCollCheckDetailMapper = lawCollCheckDetailMapper;
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        String sheetName = context.readSheetHolder().getSheetName();
        String[] mustExistHeads = {"客户姓名", "回款时间", "到账金额/元", "账龄", "协议约定佣金比例", "甲方佣金/元", "乙方佣金/元"};
        List<String> headList = new ArrayList<>(Arrays.asList(mustExistHeads));

        List<String> allHeads = headMap.values().stream().map(ReadCellData::getStringValue).collect(Collectors.toList());
        for(int i=0; i < headMap.size(); i++){
            ReadCellData<?> readCellData = headMap.get(i);
            if(null == readCellData || StringUtils.isBlank(readCellData.getStringValue())){
                continue;
            }
            if(!headList.contains(readCellData.getStringValue())){
                allHeads.remove(readCellData.getStringValue());
            }
        }
        headList.removeAll(allHeads);
        if(headList.size()>0){
            JSONObject responseJson = new JSONObject();
            responseJson.put("errDesc", "headError");
            responseJson.put("errMsg", "未在工作表<span style=\"color:red\">【"+sheetName+"】</span>中找到所有字段：客户姓名、回款时间、到账金额/元、账龄、协议约定佣金比例、甲方佣金/元、乙方佣金/元");
            throw new ServiceException(responseJson.toJSONString(), 200);
        }
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. It is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(ImportLpAh data, AnalysisContext context) {
        try {
            log.info("解析到一条数据:{}", JSON.toJSONString(data));
            data.setSheetName(context.readSheetHolder().getSheetName());
            cachedDataList.add(data);
            // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
            if (cachedDataList.size() >= BATCH_COUNT) {
                saveData();
                // 存储完成清理 list
                cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            String sheetName = context.readSheetHolder().getSheetName();
            throw new ServiceException("【"+sheetName+"】读取失败");
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("所有数据解析完成！");
        cachedDataList.clear();
        /*List<ApplyNoGroupByPo> poList = lawCollCheckDetailMapper.selectDistinctLp(lawCollCheck.getImportIdentify());
        poList = poList.stream().filter(e -> e.getCount() > 1).collect(Collectors.toList());
        if(poList.size()>0){
            List<String> applyNos = poList.stream().map(ApplyNoGroupByPo::getCApplyNo).collect(Collectors.toList());
            JSONObject responseJson = new JSONObject();
            responseJson.put("errDesc", "applyNoRepeat");
            responseJson.put("errMsg", "以下借据号存在借据号、回款日期、真实回款金额都相同的条目：\n<span style=\"color:red\"><br>"+String.join("<br>", applyNos)+"</span><br><br>请删除重复条目后再次上传");
            lawCollCheckDetailMapper.clearTempData();
            throw new ServiceException(responseJson.toJSONString(), 200);
        }*/
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", cachedDataList.size());
        List<LawCollCheckDetail> details = new ArrayList<>();
        cachedDataList.forEach(e->{
            LawCollCheckDetail lawCollCheckDetail = new LawCollCheckDetail();
            BeanUtils.copyProperties(e, lawCollCheckDetail);
            lawCollCheckDetail.setImportIdentify(lawCollCheck.getImportIdentify());
            details.add(lawCollCheckDetail);
        });
        if(!CollectionUtils.isEmpty(details)){
            lawCollCheckDetailMapper.insertLawCollCheckDetailTemp(details);
            log.info("存储数据库成功！");
        }
    }
}
