package org.ruoyi.core.modules.fcdataquery.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.financial.po.ReportTemplatePo;
import com.ruoyi.financial.service.IFinancialReportTemplateService;
import com.ruoyi.financial.vo.ReportDataVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.ruoyi.core.modules.fcdataquery.domain.FcProjectSts;
import org.ruoyi.core.modules.fcdataquery.mapper.FcProjectStsMapper;
import org.ruoyi.core.modules.fcdataquery.po.CompanyNetProfitPo;
import org.ruoyi.core.modules.fcdataquery.service.IFcCommonService;
import org.ruoyi.core.modules.fcdataquery.service.IFcCompanyNetProfitService;
import org.ruoyi.core.modules.fcdataquery.service.IFcDataQueryService;
import org.ruoyi.core.modules.fcdataquery.vo.CompanyNetProfitVo;
import org.ruoyi.core.modules.fcdataquery.vo.ProjectStsVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 总裁办-公司净利润Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@Slf4j
@Service
public class FcCompanyNetProfitServiceImpl implements IFcCompanyNetProfitService
{

    @Autowired
    private FcProjectStsMapper fcProjectStsMapper;
    @Autowired
    private IFinancialReportTemplateService financialReportTemplateService;
    @Autowired
    private IFcDataQueryService fcDataQueryService;
    @Autowired
    private IFcCommonService fcCommonService;

    /**
     * <AUTHOR>
     * @Description 总裁办-公司净利润统计
     * @Date 2024/10/23 9:26
     * @Param [startTime, endTime]
     * @return void
     **/
    @Override
    public void companyNetProfitSts(String dateStat){
        log.info("总裁办-公司净利润统计开始："+dateStat);

        //获取利润表模板-净利润项
        List<ReportTemplatePo> reportTemplatePoList = financialReportTemplateService.selectReportItemInfo("lrb", "四、净利润（亏损失以“-”号填列）");

        List<FcProjectSts> list = new ArrayList<>();
        for (ReportTemplatePo reportTemplatePo:reportTemplatePoList) {
            //查询利润表相关金额统计
            Map<Integer, ReportDataVo> reportDataVoMap = financialReportTemplateService.view(reportTemplatePo.getAccountSetsId(), reportTemplatePo.getTemplateId(), DateUtils.parseDate(dateStat), DateUtils.parseDate(dateStat));

            FcProjectSts fcProjectSts = new FcProjectSts();
            fcProjectSts.setProjectId(0);
            fcProjectSts.setAccountSetsId(reportTemplatePo.getAccountSetsId());
            fcProjectSts.setAccountSetsName(reportTemplatePo.getAccountSetsName());

            String[] split = dateStat.split("-");
            fcProjectSts.setDataDay(dateStat);
            fcProjectSts.setDataYear(split[0]);
            fcProjectSts.setDataQuarter(DateUtils.getQuarter(dateStat));
            fcProjectSts.setDataMonth(DateUtils.getMonthForDate(dateStat));
            fcProjectSts.setStsIdentify("10");//公司净利润
            fcProjectSts.setChannelName("");
            Double currentPeriodAmount = reportDataVoMap.get(reportTemplatePo.getTemplateItemsId()).getCurrentPeriodAmount();
            fcProjectSts.setTotalAmount(new BigDecimal(null != currentPeriodAmount?currentPeriodAmount:0d));
            list.add(fcProjectSts);
        }
        //批量新增项目日统计
        fcProjectStsMapper.batchInsertOrUpdateProjectSts(list);
        log.info("总裁办-公司净利润统计结束："+dateStat);
    }

    /**
     * <AUTHOR>
     * @Description 查询总裁办-公司净利润列表
     * @Date 2024/10/14 14:32
     * @Param [projectIncomeVo]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.ProjectIncomePo>
     **/
    @Override
    public List<CompanyNetProfitPo> selectCompanyNetProfitList(CompanyNetProfitVo companyNetProfitVo) {
        //请求参数处理
        reqParamsDeal(companyNetProfitVo);//获取用户对应的账套数据
        getAccountSetsList(companyNetProfitVo);

        //数据填充
        ProjectStsVo projectStsVo = new ProjectStsVo();
        BeanUtils.copyProperties(companyNetProfitVo, projectStsVo);
        projectStsVo.setStsIdentify("10");//数据标识:公司净利润

        //查询账套统计数据
        List<FcProjectSts> fcProjectStsList = fcProjectStsMapper.selectFcProjectStsList(projectStsVo);
        //处理账套统计数据集合
        List<CompanyNetProfitPo> companyNetProfitPoList = dealProjectStsList(companyNetProfitVo, fcProjectStsList);
        return companyNetProfitPoList;
    }

    /**
     * <AUTHOR>
     * @Description 获取用户对应的账套数据
     * @Date 2024/10/30 14:54
     * @Param [companyNetProfitVo]
     * @return void
     **/
    private void getAccountSetsList(CompanyNetProfitVo companyNetProfitVo){
        if(CollectionUtils.isEmpty(companyNetProfitVo.getAccountsSetsIds()) && CollectionUtils.isEmpty(companyNetProfitVo.getCompanyTypes())){
            //查询用户对应的账套信息
            List<Integer> accountsSetsIds = fcCommonService.getAccountSetsList();
            companyNetProfitVo.setAccountsSetsIds(accountsSetsIds);
        }else if(!CollectionUtils.isEmpty(companyNetProfitVo.getAccountsSetsIds()) && CollectionUtils.isEmpty(companyNetProfitVo.getCompanyTypes())){
            return;
        }else if(CollectionUtils.isEmpty(companyNetProfitVo.getAccountsSetsIds()) && !CollectionUtils.isEmpty(companyNetProfitVo.getCompanyTypes())){
            companyNetProfitVo.setAccountsSetsIds(getAccountSetsIdByCompanyType(companyNetProfitVo));
        }else if(!CollectionUtils.isEmpty(companyNetProfitVo.getAccountsSetsIds()) && !CollectionUtils.isEmpty(companyNetProfitVo.getCompanyTypes())){
            companyNetProfitVo.setAccountsSetsIds((List<Integer>) CollectionUtils.intersection(getAccountSetsIdByCompanyType(companyNetProfitVo), companyNetProfitVo.getAccountsSetsIds()));
        }

        if(CollectionUtils.isEmpty(companyNetProfitVo.getAccountsSetsIds())){
            List<Integer> list = new ArrayList<>();
            list.add(-1);
            companyNetProfitVo.setAccountsSetsIds(list);
        }
    }

    /**
     * <AUTHOR>
     * @Description 根据公司类型获取账套信息
     * @Date 2024/12/16 16:56
     * @Param [companyNetProfitVo]
     * @return java.util.List<java.lang.Integer>
     **/
    private List<Integer> getAccountSetsIdByCompanyType(CompanyNetProfitVo companyNetProfitVo){
        //查询公司类型对应的账套id
        if(!CollectionUtils.isEmpty(companyNetProfitVo.getCompanyTypes())){
            List<Integer> accountSetsIds = fcDataQueryService.getCompanyIdByType(companyNetProfitVo.getCompanyTypes());
            //获取用户对应的账套id
            List<Integer> accountsSetsIds = fcCommonService.getAccountSetsList();
            //交集
            return (List<Integer>) CollectionUtils.intersection(accountSetsIds, accountsSetsIds);
        }
        return new ArrayList<>();
    }

    /**
     * <AUTHOR>
     * @Description 请求参数处理
     * @Date 2024/10/28 15:10
     * @Param [projectIncomeVo]
     * @return void
     **/
    private void reqParamsDeal(CompanyNetProfitVo companyNetProfitVo){
        int timeRange = 11;
        //初始值
        if(StringUtils.isBlank(companyNetProfitVo.getStartTime())){
            String yearMonthStr = DateUtils.dateForStr(new Date());
            if("month".equals(companyNetProfitVo.getTimeType())){
                companyNetProfitVo.setEndTime(yearMonthStr);
                companyNetProfitVo.setStartTime(DateUtils.getNextMonth(companyNetProfitVo.getEndTime(), -timeRange));
            }else if("quarter".equals(companyNetProfitVo.getTimeType())){
                companyNetProfitVo.setEndTime(DateUtils.getQuarter(yearMonthStr+"-01"));
                companyNetProfitVo.setStartTime(DateUtils.getNextQuarter(companyNetProfitVo.getEndTime(), -timeRange));
            }else if("year".equals(companyNetProfitVo.getTimeType())){
                companyNetProfitVo.setEndTime(yearMonthStr.substring(0,4));
                companyNetProfitVo.setStartTime(DateUtils.getNextYear(companyNetProfitVo.getEndTime(), -timeRange));
            }
        }

        if("quarter".equals(companyNetProfitVo.getTimeType()) && !companyNetProfitVo.getStartTime().contains(".0")){
            companyNetProfitVo.setStartTime(companyNetProfitVo.getStartTime().replace(".", "-0"));
            companyNetProfitVo.setEndTime(companyNetProfitVo.getEndTime().replace(".", "-0"));
        }else{
            companyNetProfitVo.setStartTime(companyNetProfitVo.getStartTime().replace(".", "-"));
            companyNetProfitVo.setEndTime(companyNetProfitVo.getEndTime().replace(".", "-"));
        }

        //转换时间格式
        if("month".equals(companyNetProfitVo.getTimeType())){
            Integer diffMon = DateUtils.calcMob(companyNetProfitVo.getStartTime() + "-01", companyNetProfitVo.getEndTime() + "-01");
            if(diffMon>timeRange){//月份差超过6个月取前6个月
                companyNetProfitVo.setEndTime(DateUtils.getNextMonth(companyNetProfitVo.getStartTime(), timeRange));
            }
        } else if("quarter".equals(companyNetProfitVo.getTimeType())){
            Integer diffQuarter = DateUtils.calcQuarter(companyNetProfitVo.getStartTime() + "-01", companyNetProfitVo.getEndTime() + "-01");
            if(diffQuarter>timeRange){//季度差超过6个季度取前6个季度
                companyNetProfitVo.setEndTime(DateUtils.getNextQuarter(companyNetProfitVo.getStartTime(), timeRange));
            }
        } else if("year".equals(companyNetProfitVo.getTimeType())){
            int yearDiff = Integer.parseInt(companyNetProfitVo.getEndTime())-Integer.parseInt(companyNetProfitVo.getStartTime());
            if(yearDiff>timeRange){//年份差超过6年取前6年
                companyNetProfitVo.setEndTime(DateUtils.getNextYear(companyNetProfitVo.getStartTime(), timeRange));
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 处理账套统计集合数据
     * @Date 2024/10/30 14:39
     * @Param [companyNetProfitVo, fcProjectStsList]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.CompanyNetProfitPo>
     **/
    private List<CompanyNetProfitPo> dealProjectStsList(CompanyNetProfitVo companyNetProfitVo, List<FcProjectSts> fcProjectStsList){
        List<CompanyNetProfitPo> companyNetProfitPoList = new ArrayList<>();
        Map<Integer, List<FcProjectSts>> accountSetsIdMap = fcProjectStsList.stream().filter(e->null != e.getAccountSetsId()).collect(Collectors.groupingBy(FcProjectSts::getAccountSetsId));
        for (Integer accountSetsId: accountSetsIdMap.keySet()) {
            //获取时间范围
            List<String> timeList = DateUtils.getTimeRange(companyNetProfitVo.getStartTime(), companyNetProfitVo.getEndTime(), companyNetProfitVo.getTimeType());
            //获取账套对应的子集
            List<FcProjectSts> projectStsList = accountSetsIdMap.get(accountSetsId);

            CompanyNetProfitPo companyNetProfitPo = new CompanyNetProfitPo();
            companyNetProfitPo.setAccountSetsId(accountSetsId);//项目ID
            companyNetProfitPo.setAccountSetsName(projectStsList.get(0).getAccountSetsName());//项目名称

            Map<String, BigDecimal> amtMap = new TreeMap<>();
            if("month".equals(companyNetProfitVo.getTimeType())){

                Map<String, List<FcProjectSts>> monthMap = projectStsList.stream().collect(Collectors.groupingBy(FcProjectSts::getDataMonth));
                //排除不存在的月份统计
                timeList.removeAll(monthMap.keySet());
                if(!CollectionUtils.isEmpty(timeList)){
                    for (String monthStr: timeList) {
                        String[] split = monthStr.split("-");
                        amtMap.put(split[0]+"年"+split[1]+"月", BigDecimal.ZERO.setScale(2));
                    }
                }
                for (String monthStr: monthMap.keySet()) {
                    List<FcProjectSts> monthProjectStsList = monthMap.get(monthStr);
                    BigDecimal sumTotalAmt = monthProjectStsList.stream().map(FcProjectSts::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);//金额和
                    String[] split = monthStr.split("-");
                    amtMap.put(split[0]+"年"+split[1]+"月", sumTotalAmt);
                }
            }
            if("quarter".equals(companyNetProfitVo.getTimeType())){
                Map<String, List<FcProjectSts>> quarterMap = projectStsList.stream().collect(Collectors.groupingBy(FcProjectSts::getDataQuarter));
                //排除不存在的月份统计
                timeList.removeAll(quarterMap.keySet());
                if(!CollectionUtils.isEmpty(timeList)){
                    for (String quarterStr: timeList) {
                        String[] split = quarterStr.split("-");
                        amtMap.put(split[0]+"年"+split[1]+"季度", BigDecimal.ZERO.setScale(2));
                    }
                }
                for (String quarterStr: quarterMap.keySet()) {
                    List<FcProjectSts> monthProjectStsList = quarterMap.get(quarterStr);
                    BigDecimal sumTotalAmt = monthProjectStsList.stream().map(FcProjectSts::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);//金额和
                    String[] split = quarterStr.split("-");
                    amtMap.put(split[0]+"年"+split[1]+"季度", sumTotalAmt);
                }
            }

            if("year".equals(companyNetProfitVo.getTimeType())){
                Map<String, List<FcProjectSts>> yearMap = projectStsList.stream().collect(Collectors.groupingBy(FcProjectSts::getDataYear));
                //排除不存在的年份统计
                timeList.removeAll(yearMap.keySet());
                if(!CollectionUtils.isEmpty(timeList)){
                    for (String yearStr: timeList) {
                        amtMap.put(yearStr, BigDecimal.ZERO.setScale(2));
                    }
                }
                for (String yearStr: yearMap.keySet()) {
                    List<FcProjectSts> yearProjectStsList = yearMap.get(yearStr);
                    BigDecimal sumTotalAmt = yearProjectStsList.stream().map(FcProjectSts::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);//金额和
                    amtMap.put(yearStr, sumTotalAmt);
                }
            }
            companyNetProfitPo.setAmtMap(amtMap);//金额map集合

            BigDecimal sumTotalAmt = projectStsList.stream().map(FcProjectSts::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);//金额和
            companyNetProfitPo.setTotalAmt(sumTotalAmt);//金额和
            companyNetProfitPoList.add(companyNetProfitPo);
        }
        return companyNetProfitPoList;
    }
}
