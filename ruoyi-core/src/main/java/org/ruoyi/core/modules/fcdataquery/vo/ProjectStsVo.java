package org.ruoyi.core.modules.fcdataquery.vo;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 项目收入VO类
 * @Date 2024/9/27 10:14
 * @Param
 * @return
 **/
@Data
public class ProjectStsVo extends BaseEntity {

    /** 项目ID */
    private List<Long> projectIds;

    /** 账套ID */
    private List<Integer> accountsSetsIds;

    /** 数据标识 00-收入 01-毛利 02-净毛利 10-公司净利润 */
    private String stsIdentify;

    /** 时间维度 day month year */
    private String timeType;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 分组类型 */
    private String groupBy;
}
