package org.ruoyi.core.modules.fcdataquery.po;

import lombok.Data;

import java.math.BigDecimal;

/**
 *@Authoer: huoruidong
 *@Description: TODO
 *@Date: 2024/11/13 10:30
 **/
@Data
public class CompensatePo {

    /**
     * 项目id
     **/
    private Integer projectId;

    /**
     * 项目名称
     **/
    private String projectName;

    /**
     * 期初金额
     **/
    private BigDecimal initialAmt;

    /**
     * 收到代偿款
     **/
    private BigDecimal receiveCompensateAmt;

    /**
     * 实际代偿款
     **/
    private BigDecimal actCompensateAmt;

    /**
     * 余额
     **/
    private BigDecimal balanceAmt;

    /**
     * 手工录入代偿款
     **/
    private BigDecimal miCompensateAmt;

    /**
     * 累计代偿款
     **/
    private BigDecimal accumCompensateAmt;
}
