package org.ruoyi.core.modules.lawcoll.service;/**
 * @Authoer: huoruidong
 * @Description: TODO
 * @Date: 2023/7/19 13:37
 **/

import org.ruoyi.core.modules.lawcoll.po.LegalProceedingsPo;
import org.ruoyi.core.modules.lawcoll.vo.LpVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 *@Authoer: huoruidong
 *@Description: TODO
 *@Date: 2023/7/19 13:37
 **/
public interface ILawCollLpService {

    /**
     * 查询法律诉讼业务数据
     *
     * @param importIdentify 法催对账历史主键
     * @return 法催对账历史
     */
    public List<LegalProceedingsPo> selectLpByImportIdentify(String importIdentify);

    /**
     * <AUTHOR>
     * @Description 导入法律诉讼对账单
     * @Date 2023/7/19 15:45
     * @Param [multipartFile]
     * @return void
     **/
    List<LegalProceedingsPo> importLp(MultipartFile file1, MultipartFile file2, LpVo lpVo);

    /**
     * <AUTHOR>
     * @Description 导出法律诉讼结果
     * @Date 2023/7/24 16:52
     * @Param [lawCollCheck]
     * @return void
     **/
    void exportLp(HttpServletResponse response, String importIdentify) throws Exception;
}
