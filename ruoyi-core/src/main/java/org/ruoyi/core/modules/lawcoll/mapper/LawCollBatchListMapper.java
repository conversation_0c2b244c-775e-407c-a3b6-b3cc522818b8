package org.ruoyi.core.modules.lawcoll.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.modules.lawcoll.domain.LawCollBatchList;

import java.util.List;
import java.util.Map;

/**
 * 法催批次明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-03-26
 */
@DataSource(DataSourceType.SLAVE)
public interface LawCollBatchListMapper 
{

    /**
     * 根据借据号查询法催批次明细
     * 
     * @param loanNo 借据号
     * @return 法催批次明细集
     */
    LawCollBatchList selectLawCollBatchListByLoanNo(String loanNo);

}
