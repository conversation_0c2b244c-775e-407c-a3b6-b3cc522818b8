package org.ruoyi.core.modules.fcdataquery.service;

import org.ruoyi.core.modules.fcdataquery.po.DividendsDistributedPo;
import org.ruoyi.core.modules.fcdataquery.vo.DividendsDistributedVo;

import java.util.List;

/**
 * 总裁办-待分配红利Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IFcDividendsDistributedService
{

    /**
     * <AUTHOR>
     * @Description 总裁办-待分配红利统计
     * @Date 2024/10/23 9:25
     * @Param [startTime, endTime]
     * @return void
     **/
    public void dividendsDistributedSts(String dateStat);

    /**
     * <AUTHOR>
     * @Description 查询总裁办-待分配红利列表
     * @Date 2024/10/14 14:11
     * @Param [projectIncomeVo]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.ProjectIncomePo>
     **/
    List<DividendsDistributedPo> selectDividendsDistributedList(DividendsDistributedVo dividendsDistributedVo);


}
