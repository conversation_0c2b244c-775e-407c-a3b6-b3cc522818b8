package org.ruoyi.core.modules.fcdataquery.service;

import org.ruoyi.core.modules.fcdataquery.po.ThirdTecServiceDetailPo;
import org.ruoyi.core.modules.fcdataquery.po.ThirdTecServicePo;
import org.ruoyi.core.modules.fcdataquery.vo.ThirdTecServiceDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.ThirdTecServiceVo;

import java.util.List;

/**
 * 运营部-第三方技术服务费Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IFcThirdTecService
{

    /**
     * <AUTHOR>
     * @Description 运营部-第三方技术服务费统计
     * @Date 2024/10/23 9:25
     * @Param [startTime, endTime]
     * @return void
     **/
    public void thirdTecServiceSts(String dateStat);

    /**
     * <AUTHOR>
     * @Description 查询运营部-第三方技术服务费列表
     * @Date 2024/10/14 14:11
     * @Param [thirdTecServiceVo]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.ThirdTecServicePo>
     **/
    List<ThirdTecServicePo> selectThirdTecServiceList(ThirdTecServiceVo thirdTecServiceVo);

    /**
     * <AUTHOR>
     * @Description 获取第三方技术服务费详情
     * @Date 2024/10/30 15:32
     * @Param [thirdTecServiceDetailVo]
     * @return org.ruoyi.core.modules.fcdataquery.po.ThirdTecServiceDetailPo
     **/
    List<ThirdTecServiceDetailPo> getThirdTecServiceDetail(ThirdTecServiceDetailVo thirdTecServiceDetailVo);
}
