package org.ruoyi.core.modules.fcdataquery.service;

import org.ruoyi.core.modules.fcdataquery.po.AccountSetsInfoPo;
import org.ruoyi.core.modules.fcdataquery.po.BeneficiaryInfoPo;
import org.ruoyi.core.modules.fcdataquery.po.ChannelInfoPo;
import org.ruoyi.core.modules.fcdataquery.po.CompanyTypePo;

import java.util.List;

/**
 * 经营分析Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-13
 */
public interface IFcDataQueryService
{

    /**
     * <AUTHOR>
     * @Description 查询公司类型
     * @Date 2024/12/13 16:10
     * @Param []
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.CompanyTypePo>
     **/
    public List<CompanyTypePo> selectCompanyType();

    /**
     * <AUTHOR>
     * @Description 根据公司类型获取公司id
     * @Date 2024/12/13 16:29
     * @Param [dictCode]
     * @return java.util.List<java.lang.Integer>
     **/
    public List<Integer> getCompanyIdByType(List<Integer> dictCodes);

    /**
     * <AUTHOR>
     * @Description 获取渠道信息
     * @Date 2024/12/24 14:08
     * @Param []
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.ChannelInfoPo>
     **/
    public List<ChannelInfoPo> selectChannelInfo();

    /**
     * <AUTHOR>
     * @Description 获取收款单位信息
     * @Date 2024/12/26 9:40
     * @Param [type]
     * @return java.util.List<java.lang.String>
     **/
    public List<BeneficiaryInfoPo> selectBeneficiaryInfo(Integer type);

    /**
     * <AUTHOR>
     * @Description 获取用户有权限的公司对应的账套id
     * @Date 2024/12/26 14:10
     * @Param []
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.AccountSetsInfoPo>
     **/
    public List<AccountSetsInfoPo> selectAccountSets();
}
