package org.ruoyi.core.modules.fcdataquery.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.modules.fcdataquery.domain.FcSubjectSts;
import org.ruoyi.core.modules.fcdataquery.vo.ProjectIncomeDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.ProjectIncomeVo;

import java.util.List;

/**
 * 财务科目日统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-26
 */
public interface FcSubjectStsMapper 
{
    /**
     * 查询财务科目日统计
     * 
     * @param id 财务科目日统计主键
     * @return 财务科目日统计
     */
    public FcSubjectSts selectFcSubjectStsById(Long id);

    /**
     * 查询财务科目日统计列表
     * 
     * @param projectIncomeVo vo类
     * @param projectIdList 项目id集合
     * @param subjectCodeList 科目编码集合
     * @return 财务科目日统计集合
     */
    public List<FcSubjectSts> selectFcSubjectStsList(@Param("params")ProjectIncomeVo projectIncomeVo, @Param("projectIds")List<Integer> projectIdList,
                                                     @Param("subjectCodes")List<String> subjectCodeList);

    /**
     * 新增财务科目日统计
     * 
     * @param fcSubjectSts 财务科目日统计
     * @return 结果
     */
    public int insertFcSubjectSts(FcSubjectSts fcSubjectSts);

    /**
     * 修改财务科目日统计
     * 
     * @param fcSubjectSts 财务科目日统计
     * @return 结果
     */
    public int updateFcSubjectSts(FcSubjectSts fcSubjectSts);

    /**
     * <AUTHOR>
     * @Description 批量新增科目日统计
     * @Date 2024/10/14 10:21
     * @Param [list]
     * @return int
     **/
    public int batchInsertOrUpdateSubjectSts(List<FcSubjectSts> list);

    /**
     * <AUTHOR>
     * @Description 更新项目信息
     * @Date 2024/10/14 10:54
     * @Param []
     * @return int
     **/
    public int updateProjectInfoToMappting();
    public int updateProjectInfoToSubject(String dataDay);
    public int updateProjectInfoToProject(String dataDay);

    /**
     * <AUTHOR>
     * @Description 更新科目信息
     * @Date 2024/12/12 10:34
     * @Param []
     * @return int
     **/
    public int updateSubjectInfo(String dataDay);

    /**
     * <AUTHOR>
     * @Description 删除科目不存在的统计数据
     * @Date 2024/12/12 10:39
     * @Param []
     * @return int
     **/
    public int delSubjectStsNotExist(String dataDay);

    /**
     * <AUTHOR>
     * @Description 临时处理，后续删掉
     * @Date 2024/10/22 10:55
     * @Param [oaProjectDeploy]
     * @return int
     **/
    public int updateSubjectSts(String dataDay);

    /**
     * <AUTHOR>
     * @Description 删除统计
     * @Date 2024/12/23 15:51
     * @Param []
     * @return int
     **/
    public int delSubjectStsByDay();

    /**
     * <AUTHOR>
     * @Description 科目统计分组查询
     * @Date 2024/10/22 10:15
     * @Param [subjectCodeList, startTime, endTime]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.SubjectStsPo>
     **/
    public List<FcSubjectSts> getSubjectStsGroupBy(@Param("accountSetsId")Integer accountSetsId, @Param("dataMonth")String dataMonth);

    /**
     * <AUTHOR>
     * @Description 通过项目id获取科目统计数据
     * @Date 2024/10/30 15:58
     * @Param [vo]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.domain.FcSubjectSts>
     **/
    public List<FcSubjectSts> getSubjectStsByProjectId(ProjectIncomeDetailVo vo);

    /**
     * <AUTHOR>
     * @Description 通过科目id删除日统计
     * @Date 2024/12/10 15:08
     * @Param [subjectId]
     * @return int
     **/
    public int delBySubjectId(Integer subjectId);
}
