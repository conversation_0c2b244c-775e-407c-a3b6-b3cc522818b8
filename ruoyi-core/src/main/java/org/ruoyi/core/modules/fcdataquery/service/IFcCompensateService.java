package org.ruoyi.core.modules.fcdataquery.service;

import org.ruoyi.core.modules.fcdataquery.po.CompensateDetailPo;
import org.ruoyi.core.modules.fcdataquery.po.CompensatePo;
import org.ruoyi.core.modules.fcdataquery.vo.CompensateDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.CompensateVo;

import java.util.List;

/**
 * 运营部-代偿款Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IFcCompensateService
{

    /**
     * <AUTHOR>
     * @Description 运营部-代偿款统计
     * @Date 2024/10/23 9:25
     * @Param [startTime, endTime]
     * @return void
     **/
    public void compensateSts(String dateStat);

    /**
     * <AUTHOR>
     * @Description 查询运营部-代偿款列表
     * @Date 2024/10/14 14:11
     * @Param [compensateVo]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.CompensatePo>
     **/
    List<CompensatePo> selectCompensateList(CompensateVo compensateVo);

    /**
     * <AUTHOR>
     * @Description 获取代偿款详情
     * @Date 2024/10/30 15:32
     * @Param [compensateDetailVo]
     * @return org.ruoyi.core.modules.fcdataquery.po.CompensateDetailPo
     **/
    List<CompensateDetailPo> getCompensateDetail(CompensateDetailVo compensateDetailVo);
}
