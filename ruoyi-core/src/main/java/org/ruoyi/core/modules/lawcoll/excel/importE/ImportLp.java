package org.ruoyi.core.modules.lawcoll.excel.importE;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 法催对账历史对象 law_coll_check_history
 * 
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
public class ImportLp
{
    @ExcelIgnore
    private String sheetName;

    @ExcelProperty("借据编号")
    private String cApplyNo;

    @ExcelProperty("回款途径")
    private String repaySrc;

    @ExcelProperty("批次")
    private String cBatchNo;

    @ExcelProperty("委托逾期天数")
    private Integer cOvdDays;

    @ExcelProperty("回款日期")
    private String cRepayDate;

    @ExcelProperty("真实回款金额（元）")
    private BigDecimal cActRepayTotalAmt;

    @ExcelProperty("服务费比例")
    private String cServiceRate;

    @ExcelProperty("服务费金额")
    private BigDecimal cServiceAmt;

    @ExcelProperty("诉讼费")
    private BigDecimal cCostsAmt;

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("放款日期")
    private String loanTime;

    @ExcelProperty("代偿日期")
    private String cCompensatoryDate;

    @ExcelProperty("结清状态")
    private String status;

    @ExcelProperty("本次律师费结算基数")
    private BigDecimal lawyerFeeSettleBase;

    @ExcelProperty("律师费待结算金额")
    private BigDecimal cLawyerFee;
}
