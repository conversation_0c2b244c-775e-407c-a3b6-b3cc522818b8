package org.ruoyi.core.modules.fcdataquery.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.financial.mapper.FinancialVoucherDetailsMapper;
import com.ruoyi.financial.po.SubjectDayStsPo;
import org.ruoyi.core.modules.fcdataquery.domain.FcSubjectSts;
import org.ruoyi.core.modules.fcdataquery.mapper.FcProjectStsMapper;
import org.ruoyi.core.modules.fcdataquery.mapper.FcSubjectStsMapper;
import org.ruoyi.core.modules.fcdataquery.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *@Authoer: huoruidong
 *@Description: 财务科目日统计
 *@Date: 2024/9/27 14:31
 **/
@Service
public class FcSubjectDayStsServiceImpl implements FcSubjectDayStsService {

    @Autowired
    private FinancialVoucherDetailsMapper financialVoucherDetailsMapper;
    @Autowired
    private FcSubjectStsMapper fcSubjectStsMapper;
    @Autowired
    private FcProjectStsMapper fcProjectStsMapper;
    @Autowired
    private IFcProjectIncomeService fcProjectIncomeService;//总裁办-项目收入
    @Autowired
    private IFcCompanyNetProfitService fcCompanyNetProfitService;//总裁办-公司净利润
    @Autowired
    private IFcDividendsDistributedService fcDividendsDistributedService;//总裁办-待分配红利
    @Autowired
    private IFcPlatformMarginService fcPlatformMarginService;//运营部-平台方保证金
    @Autowired
    private IFcFundMarginService fcFundMarginService;//运营部-资金方保证金
    @Autowired
    private IFcCompensateService fcCompensateService;//运营部-代偿款
    @Autowired
    private IFcPlatformTecService fcPlatformTecService;//运营部-平台技术服务费
    @Autowired
    private IFcThirdTecService fcThirdTecService;//运营部-第三方技术服务费
    @Autowired
    private IFcOfflineRepayService fcOfflineRepayService;//运营部-线下还款
    @Autowired
    private IFcPayableInfoFeeService fcPayableInfoFeeService;//运营部-应付信息费

    /**
     * <AUTHOR>
     * @Description 财务科目日统计
     * @Date 2024/9/27 14:33
     * @Param [startTime, endTime]
     * @return boolean
     **/
    @Override
    public void subjectDaySts(String startTime, String endTime) {
        try {
            if(StringUtils.isBlank(startTime)){
                startTime = "2024-01-01";
            }
            if(StringUtils.isBlank(endTime)){
                endTime = LocalDate.now().toString();
            }

            List<String> dateList = DateUtils.getTimeRange(startTime, endTime, DateUtils.DateFormat.YYYY_MM_DD);
            for (String dateStr: dateList) {
                //按日统计凭证合计
                List<SubjectDayStsPo> subjectDayStsPoList = financialVoucherDetailsMapper.selectSubjectDaySts(dateStr);
                List<FcSubjectSts> list = new ArrayList<>();
                //填充科目日统计
                subjectDayStsPoList.forEach(e->{
                    FcSubjectSts fcSubjectSts = new FcSubjectSts();
                    fcSubjectSts.setAccountSetsId(e.getAccountSetsId());//账套ID
                    fcSubjectSts.setAccountSetsName(e.getAccountSetsName());//账套名称
                    fcSubjectSts.setDataDay(e.getVoucherDate());//数据日
                    fcSubjectSts.setDataYear(e.getVoucherYear().toString());//数据年
                    fcSubjectSts.setDataQuarter(DateUtils.getQuarter(e.getVoucherDate()));//数据季度
                    fcSubjectSts.setDataMonth(DateUtils.getMonthForDate(e.getVoucherDate()));//数据月
                    fcSubjectSts.setSubjectId(e.getSubjectId());//科目ID
                    fcSubjectSts.setSubjectCode(e.getSubjectCode());//科目编码
                    fcSubjectSts.setSubjectName(e.getSubjectName());//科目名称
                    fcSubjectSts.setDebitAmount(e.getDebitAmount());//借方金额合计
                    fcSubjectSts.setCreditAmount(e.getCreditAmount());//贷方金额合计
                    list.add(fcSubjectSts);

                    if(list.size() >= 500){
                        fcSubjectStsMapper.batchInsertOrUpdateSubjectSts(list);
                        list.clear();
                    }
                });

                if(list.size()>0){
                    fcSubjectStsMapper.batchInsertOrUpdateSubjectSts(list);
                    list.clear();
                }
                //更新项目信息
                fcSubjectStsMapper.updateProjectInfoToMappting();
                fcSubjectStsMapper.updateProjectInfoToSubject(dateStr);
                fcSubjectStsMapper.updateProjectInfoToProject(dateStr);
                //更新科目信息
                fcSubjectStsMapper.updateSubjectInfo(dateStr);
                //对应科目统计在凭证明细里不存在时，更新科目借贷金额
                fcSubjectStsMapper.updateSubjectSts(dateStr);
                //删除科目不存在的统计数据
                fcSubjectStsMapper.delSubjectStsNotExist(dateStr);
            }
        } catch (Exception e) {
            System.out.println("财务科目日统计异常："+e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * <AUTHOR>
     * @Description 项目统计
     * @Date 2024/10/24 14:38
     * @Param [startTime, endTime]
     * @return void
     **/
    @Override
    public void projectDaySts(String startTime, String endTime, String stsType) {
        //清除项目日统计数据
        if(stsType.equals("all")){
            fcProjectStsMapper.clearProjectSts();
        }
        if(StringUtils.isBlank(startTime)){
            startTime = "2024-01-01";
        }
        if(StringUtils.isBlank(endTime)){
            endTime = LocalDate.now().toString();
        }

        List<String> dateList = DateUtils.getTimeRange(startTime, endTime, DateUtils.DateFormat.YYYY_MM_DD);

        List<String> monthList = dateList.stream().map(e-> e.substring(0,7)).distinct().collect(Collectors.toList());
        for (String monthStr: monthList) {
            switch(stsType){
                case "00":
                    //总裁办-项目收入
                    fcProjectIncomeService.projectIncomeSts(DateUtils.getMonthEndDate(monthStr+"-01"));
                    break;
                case "10":
                    //总裁办-公司净利润
                    fcCompanyNetProfitService.companyNetProfitSts(DateUtils.getMonthEndDate(monthStr+"-01"));
                    break;
                case "20":
                    //总裁办-待分配红利
                    fcDividendsDistributedService.dividendsDistributedSts(DateUtils.getMonthEndDate(monthStr+"-01"));
                    break;
                case "all":
                    //总裁办-项目收入
                    fcProjectIncomeService.projectIncomeSts(DateUtils.getMonthEndDate(monthStr+"-01"));
                    //总裁办-公司净利润
                    fcCompanyNetProfitService.companyNetProfitSts(DateUtils.getMonthEndDate(monthStr+"-01"));
                    //总裁办-待分配红利
                    fcDividendsDistributedService.dividendsDistributedSts(DateUtils.getMonthEndDate(monthStr+"-01"));
                    break;
            }
        }

        for (String dateStr: dateList) {
            switch(stsType) {
                case "A00":
                    //运营部-平台方保证金
                    fcPlatformMarginService.platformMarginSts(dateStr);
                    break;
                case "A01":
                    //运营部-资金方保证金
                    fcFundMarginService.fundMarginSts(dateStr);
                    break;
                case "A02":
                    //运营部-代偿款
                    fcCompensateService.compensateSts(dateStr);
                    break;
                case "A03":
                    //运营部-平台技术服务费
                    fcPlatformTecService.platformTecServiceSts(dateStr);
                    break;
                case "A04":
                    //运营部-第三方技术服务费
                    fcThirdTecService.thirdTecServiceSts(dateStr);
                    break;
                case "A05":
                    //运营部-线下还款
                    fcOfflineRepayService.offlineRepaySts(dateStr);
                    break;
                case "A06":
                    //运营部-应付信息费
                    fcPayableInfoFeeService.payableInfoFeeSts(dateStr);
                    break;
                case "all":
                    //运营部-平台方保证金
                    fcPlatformMarginService.platformMarginSts(dateStr);
                    //运营部-资金方保证金
                    fcFundMarginService.fundMarginSts(dateStr);
                    //运营部-代偿款
                    fcCompensateService.compensateSts(dateStr);
                    //运营部-平台技术服务费
                    fcPlatformTecService.platformTecServiceSts(dateStr);
                    //运营部-第三方技术服务费
                    fcThirdTecService.thirdTecServiceSts(dateStr);
                    //运营部-线下还款
                    fcOfflineRepayService.offlineRepaySts(dateStr);
                    //运营部-应付信息费
                    fcPayableInfoFeeService.payableInfoFeeSts(dateStr);
                    break;
            }
        }
        //对应科目统计在凭证明细里不存在时，项目统计完后删除科目统计
        fcSubjectStsMapper.delSubjectStsByDay();
        //科目统计中不存在该项目的数据则删除
        fcProjectStsMapper.delProjectStsNotExist(stsType);
        //与临时表比较删除多余统计数据
        fcProjectStsMapper.deletePorjectStsComTemp(stsType);
        //将临时表的数据插入或更新到科目日统计表中
        fcProjectStsMapper.insertOrUpdate(stsType);
    }

    /**
     * <AUTHOR>
     * @Description 删除科目日统计
     * @Date 2024/12/10 15:09
     * @Param [subjectId]
     * @return void
     **/
    @Override
    public void delSubjectSts(Integer subjectId) {
        fcSubjectStsMapper.delBySubjectId(subjectId);
    }
}
