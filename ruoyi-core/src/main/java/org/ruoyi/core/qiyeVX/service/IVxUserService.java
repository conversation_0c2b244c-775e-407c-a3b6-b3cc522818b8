package org.ruoyi.core.qiyeVX.service;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.qiyeVX.domain.VxUser;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IVxUserService.java
 * @Description TODO
 * @createTime 2023年05月23日 09:53:00
 */
public interface IVxUserService {


    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public VxUser selectVxUserById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param vxUser 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<VxUser> selectVxUserList(VxUser vxUser);

    /**
     * 新增【请填写功能名称】
     *
     * @param vxUser 【请填写功能名称】
     * @return 结果
     */
    public int insertVxUser(VxUser vxUser);

    /**
     * 修改【请填写功能名称】
     *
     * @param vxUser 【请填写功能名称】
     * @return 结果
     */
    public int updateVxUser(VxUser vxUser);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteVxUserByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteVxUserById(Long id);


    List<Map<String, Object>> getDataUsers();
}
