package org.ruoyi.core.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.domain.DProfitParameter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IDProfitParameterService.java
 * @Description 利润测算参数设置
 * @createTime 2022年07月25日 14:37:00
 */
public interface IDProfitParameterService {

    /**
     * 参数设置
     * 利润测算参数列表
     *
     * @param dProfitParameter d利润参数
     * @return {@link List}<{@link DProfitParameter}>
     */
    List<DProfitParameter> selectDProfitParameterList(DProfitParameter dProfitParameter,LoginUser loginUser);


    /**
     * 导出dprofit参数列表
     *
     * @param dProfitParameter d利润参数
     * @return {@link List}<{@link DProfitParameter}>
     */
    public List<DProfitParameter>exportDProfitParameterList(DProfitParameter dProfitParameter,LoginUser loginUser);
    /**
     * 利润测算参数  id
     *
     * @param id id
     * @return {@link DProfitParameter}
     */
    public DProfitParameter selectDProfitParameterById(Long id);

    /**
     * 插入利润测算参数
     *
     * @param dProfitParameter d利润参数
     * @return int
     */
    public int insertDProfitParameter(DProfitParameter dProfitParameter);

    /**
     * 更新利润测算参数
     *
     * @param dProfitParameter d利润参数
     * @return int
     */
    public int updateDProfitParameter(DProfitParameter dProfitParameter);

    /**
     * 删除利润测算参数由ids
     *
     * @param ids id
     * @return int
     */
    public int deleteDProfitParameterByIds(Long[] ids);

    /**
     * 删除利润测算参数通过id
     *
     * @param id id
     * @return int
     */
    public int deleteDProfitParameterById(Long id);

    /**
     * 导入利润测算数据
     *
     * @param mappingsList 映射列表
     * @param operName     ③名字
     * @return {@link String}
     */
    String importProfitParameter(List<DProfitParameter> mappingsList, String operName);

    /**
     * 校验新增的数据是否存在
     *
     * @param dProfitParameter 利润测算参数
     * @return 结果
     */
    Boolean checkParamsDProfitParameter(DProfitParameter dProfitParameter);
}
