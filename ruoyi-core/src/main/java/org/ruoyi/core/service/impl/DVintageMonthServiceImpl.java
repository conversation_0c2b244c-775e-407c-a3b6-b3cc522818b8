package org.ruoyi.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.gson.Gson;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.dictMapping.DataMapping;
import org.apache.logging.log4j.util.Strings;
import org.ruoyi.core.domain.DVintageMonth;
import org.ruoyi.core.mapper.DVintageMonthMapper;
import org.ruoyi.core.service.IDVintageMonthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 外部系统平台VintageService业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-13
 */
@Service
public class DVintageMonthServiceImpl implements IDVintageMonthService {
    @Resource
    private DVintageMonthMapper dVintageMonthMapper;
    @Resource
    private SysSelectDataRefServiceImpl sysSelectDataRefService;
    private static final Logger log = LoggerFactory.getLogger(DVintageMonthServiceImpl.class);

    /**
     * 查询外部系统平台Vintage
     *
     * @param id 外部系统平台Vintage主键
     * @return 外部系统平台Vintage
     */
    @Override
    public DVintageMonth selectDVintageMonthById(Long id) {
        return dVintageMonthMapper.selectDVintageMonthById(id);
    }

    /**
     * 查询外部系统平台Vintage列表
     *
     * @param dVintageMonth 外部系统平台Vintage
     * @return 外部系统平台Vintage
     */
    @Override
    @DataScope()
    public List<DVintageMonth> selectDVintageMonthList(DVintageMonth dVintageMonth) {

        List<String> platforms = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPlatformNo())) {
//            platforms = Arrays.asList(dVintageMonth.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getCustNo())) {
//            custNos = Arrays.asList(dVintageMonth.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dVintageMonth.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;

//        if (Strings.isNotEmpty(dVintageMonth.getFundNo())) {
//            fundNos = Arrays.asList(dVintageMonth.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dVintageMonth.getProductNo())) {
            products = Arrays.asList(dVintageMonth.getProductNo().split(","));
        }
        String isMapping = null;
        if (Strings.isNotEmpty(dVintageMonth.getIsMapping())) {
            isMapping = dVintageMonth.getIsMapping();
        }
        // 放款
        String loanMonth = null;
        if (Strings.isNotEmpty(dVintageMonth.getLoanMonth())) {
            loanMonth = dVintageMonth.getLoanMonth();
        }
        // 统计
        String reconMonth = null;
        if (Strings.isNotEmpty(dVintageMonth.getReconMonth())) {
            reconMonth = dVintageMonth.getReconMonth();
        }
//        if (dVintageMonth.getMoreSearch() != null && dVintageMonth.getMoreSearch().length() > 2){
//            Gson gson = new Gson();
//            // 将字符串转换为 Map<String, List<Long>>
//            Map<String, List<String>> moreSearch = gson.fromJson(dVintageMonth.getMoreSearch(), Map.class);
//            dVintageMonth.setMoreSearchMap(moreSearch);
//        }
        PageUtils.startPage();
        return dVintageMonthMapper.queryAllForSelect(platforms, custNos, partnerNos, fundNos, products, isMapping,loanMonth,reconMonth,dVintageMonth);
    }

    /**
     * 查询外部系统平台Vintage列表
     *
     * @param dVintageMonth 外部系统平台Vintage
     * @return 外部系统平台Vintage
     */
    @Override
    @DataScope()
    public List<DVintageMonth> exportDVintageMonthList(DVintageMonth dVintageMonth) {

        List<String> platforms = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPlatformNo())) {
//            platforms = Arrays.asList(dVintageMonth.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getCustNo())) {
//            custNos = Arrays.asList(dVintageMonth.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dVintageMonth.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;

//        if (Strings.isNotEmpty(dVintageMonth.getFundNo())) {
//            fundNos = Arrays.asList(dVintageMonth.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dVintageMonth.getProductNo())) {
            products = Arrays.asList(dVintageMonth.getProductNo().split(","));
        }
        String isMapping = null;
        if (Strings.isNotEmpty(dVintageMonth.getIsMapping())) {
            isMapping = dVintageMonth.getIsMapping();
        }
        // 放款
        String loanMonth = null;
        if (Strings.isNotEmpty(dVintageMonth.getLoanMonth())) {
            loanMonth = dVintageMonth.getLoanMonth();
        }
        // 统计
        String reconMonth = null;
        if (Strings.isNotEmpty(dVintageMonth.getReconMonth())) {
            reconMonth = dVintageMonth.getReconMonth();
        }
        if (dVintageMonth.getMoreSearch() != null && dVintageMonth.getMoreSearch().length() > 2){
            Gson gson = new Gson();
            // 将字符串转换为 Map<String, List<Long>>
            Map<String, List<String>> moreSearch = gson.fromJson(dVintageMonth.getMoreSearch(), Map.class);
            dVintageMonth.setMoreSearchMap(moreSearch);
        }
        return dVintageMonthMapper.queryAllForSelect(platforms, custNos, partnerNos, fundNos, products, isMapping,loanMonth,reconMonth,dVintageMonth);
    }

    /**
     * 新增外部系统平台Vintage
     *
     * @param dVintageMonth 外部系统平台Vintage
     * @return 结果
     */
    @Override
    public int insertDVintageMonth(DVintageMonth dVintageMonth) {
        dVintageMonth.setCreateTime(DateUtils.getNowDate());
        dVintageMonth.setUpdateTime(DateUtils.getNowDate());
        return dVintageMonthMapper.insertDVintageMonth(dVintageMonth);
    }

    /**
     * 修改外部系统平台Vintage
     *
     * @param dVintageMonth 外部系统平台Vintage
     * @return 结果
     */

    @Override
    public int updateDVintageMonth(DVintageMonth dVintageMonth) {
        dVintageMonth.setUpdateTime(DateUtils.getNowDate());
        return dVintageMonthMapper.updateDVintageMonth(dVintageMonth);
    }

    /**
     * 批量删除外部系统平台Vintage
     *
     * @param ids 需要删除的外部系统平台Vintage主键
     * @return 结果
     */
    @Override
    public int deleteDVintageMonthByIds(Long[] ids) {
        return dVintageMonthMapper.deleteDVintageMonthByIds(ids);
    }

    /**
     * 删除外部系统平台Vintage信息
     *
     * @param id 外部系统平台Vintage主键
     * @return 结果
     */
    @Override
    public int deleteDVintageMonthById(Long id) {
        return dVintageMonthMapper.deleteDVintageMonthById(id);
    }

    /**
     * 重新映射数据
     *
     * @return
     */

    @Override
    public void anewMappingData() {
        //得到所有映射失败的数据
        DVintageMonth dVintageMonth = new DVintageMonth();
        dVintageMonth.setIsMapping("N");
        List<DVintageMonth> notMappingData = dVintageMonthMapper.selectDVintageMonthList(dVintageMonth);
        if(notMappingData.size()>0){
            List<Map<String, Object>> maps = (List<Map<String, Object>>) JSONArray.parse(JSON.toJSONString(notMappingData));
            String mappingSplits = "custNo,partnerNo,fundNo,productNo";
            List<Map<String, Object>> dataDictMapping = DataMapping.getDataDictMapping(maps, mappingSplits);
            String s = JSON.toJSONString(dataDictMapping);
            List<DVintageMonth> dVintageMonthMappings = JSONArray.parseArray(s, DVintageMonth.class);
            int mappingNum = dVintageMonthMapper.updateBatch(dVintageMonthMappings);
            log.info("DVintageMonthServiceImpl.anewMappingData共处理" + mappingNum + "条数据");
        }else {
            log.info("DVintageMonthServiceImpl.anewMappingData 没有查询到需要映射的数据，处理结束！");
        }

    }

    @Override
    @DataScope()
    public List<Map<String, Object>> exportDVintageMonthList1(DVintageMonth dVintageMonth) {
        List<String> platforms = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPlatformNo())) {
//            platforms = Arrays.asList(dVintageMonth.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getCustNo())) {
//            custNos = Arrays.asList(dVintageMonth.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dVintageMonth.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getFundNo())) {
//            fundNos = Arrays.asList(dVintageMonth.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dVintageMonth.getProductNo())) {
            products = Arrays.asList(dVintageMonth.getProductNo().split(","));
        }
        String isMapping = null;
        if (Strings.isNotEmpty(dVintageMonth.getIsMapping())) {
            isMapping = dVintageMonth.getIsMapping();
        }
        // 放款
        String loanMonth = null;
        if (Strings.isNotEmpty(dVintageMonth.getLoanMonth())) {
            loanMonth = dVintageMonth.getLoanMonth();
        }
        // 统计
        String reconMonth = null;
        if (Strings.isNotEmpty(dVintageMonth.getReconMonth())) {
            reconMonth = dVintageMonth.getReconMonth();
        }
//        if (dVintageMonth.getMoreSearch() != null && dVintageMonth.getMoreSearch().length() > 2){
//            Gson gson = new Gson();
//            // 将字符串转换为 Map<String, List<Long>>
//            Map<String, List<String>> moreSearch = gson.fromJson(dVintageMonth.getMoreSearch(), Map.class);
//            dVintageMonth.setMoreSearchMap(moreSearch);
//        }
        return dVintageMonthMapper.queryAllForSelect1(platforms, custNos, partnerNos, fundNos, products, isMapping,loanMonth,reconMonth,dVintageMonth);
    }

}
