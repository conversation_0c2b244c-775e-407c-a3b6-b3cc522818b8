package org.ruoyi.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.encryption.AESUtils;
import com.ruoyi.common.utils.encryption.KeyUtils;
import com.ruoyi.common.utils.encryption.RSAUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.ruoyi.core.constant.DataConstants;
import org.ruoyi.core.domain.DSecretKey;
import org.ruoyi.core.mapper.DSecretKeyMapper;
import org.ruoyi.core.service.DSecretKeyService;
import org.springframework.beans.BeansException;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统对接秘钥表(DSecretKey)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-31 11:29:31
 */
@Service("dSecretKeyService")
@Slf4j
public class DSecretKeyServiceImpl implements DSecretKeyService {


    // 非对称加密的 AES 默认长度秘钥
    public static final int AES_INTI_KEY_SIZE = 128;

    @Resource
    private DSecretKeyMapper dSecretKeyMapper;

    @PostConstruct
    public void init() {
        cacheSecretData();
    }

    /**
     * 秘密数据缓存
     */
    private void cacheSecretData() {
        deleteAndSetRedisSecretData(false);
        DSecretKey secretKey = new DSecretKey();
        // 查询状态为正常的秘钥
        secretKey.setStatus(String.valueOf(0));
        List<DSecretKey> list = dSecretKeyMapper.query(secretKey);
        List<Boolean> cacheStatusList = list.stream().map(this::cacheSecretData).collect(Collectors.toList());
        if (cacheStatusList.contains(false)) {
            log.error("初始化秘钥进入 Redis 中部分失败");
        }
        log.info("初始化秘钥到 Redis 成功");
    }

    /**
     * 秘密数据缓存
     *
     * @param dSecretKey d密钥
     * @return boolean 成功状态
     */
    private boolean cacheSecretData(DSecretKey dSecretKey) {
        try {
            // Redis 缓存秘钥 例: secret_codes:XFJR001:1
            SpringUtils.getBean(RedisCache.class).setCacheObject(Constants.SECRET_CODE_KEY + dSecretKey.getPlatformNo() + ":" + dSecretKey.getUseType(), dSecretKey);
            return true;
        } catch (BeansException e) {
            return false;
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public DSecretKey queryById(Integer id) {
        return this.dSecretKeyMapper.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param dSecretKey 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DSecretKey> queryByPage(DSecretKey dSecretKey) {
        return this.dSecretKeyMapper.queryAllByLimit(dSecretKey);
    }

    /**
     * 新增数据
     *
     * @param dSecretKey 实例对象
     * @return 实例对象
     */
    @Override
    public int insert(DSecretKey dSecretKey) {
        // 存储的 AES 秘钥
        if (DataConstants.NO_SYMMETRIC_ENCRYPTION.equals(dSecretKey.getKeyType()) && "RSA".equals(dSecretKey.getKeyAlgorithm())) {
            Map<String, String> keys = RSAUtils.createKeys(Integer.parseInt(dSecretKey.getKeyLength()));
            dSecretKey.setPrivateKey(keys.get("privateKey"));
            dSecretKey.setPublicKey(keys.get("publicKey"));
        } else if (DataConstants.NO_SYMMETRIC_ENCRYPTION.equals(dSecretKey.getKeyType()) && "SM2".equals(dSecretKey.getKeyAlgorithm())) {
            try {
                String[] keys = KeyUtils.generateSmKey(Integer.parseInt(dSecretKey.getKeyLength()));
                if (Strings.isNullOrEmpty(Arrays.toString(keys)) || keys.length < 2){
                    return 0;
                }
                dSecretKey.setPrivateKey(keys[1]);
                dSecretKey.setPublicKey(keys[0]);
            } catch (Exception e) {
                log.error("创建秘钥失败 dSecretKey :{}", JSONObject.toJSONString(dSecretKey));
                return 0;
            }
        } else if (DataConstants.SYMMETRIC_ENCRYPTION.equals(dSecretKey.getKeyType()) && "AES".equals(dSecretKey.getKeyAlgorithm())) {
            try {
                String aesKey = AESUtils.initSecretKey(Integer.parseInt(dSecretKey.getKeyLength()));
                dSecretKey.setSecretKey(aesKey);
            } catch (Exception e) {
                log.error("创建秘钥失败 dSecretKey :{}", JSONObject.toJSONString(dSecretKey));
                return 0;
            }
        }
        cacheSecretData(dSecretKey);
        return this.dSecretKeyMapper.insert(dSecretKey);
    }

    /**
     * 修改数据
     *
     * @param dSecretKey 实例对象
     * @return 实例对象
     */
    @Override
    public int update(DSecretKey dSecretKey) {
        dSecretKey.setUpdateTime(new Date());
        // 先进行修改秘钥信息
        int rows = this.dSecretKeyMapper.update(dSecretKey);
        deleteAndSetRedisSecretData(true);
        return rows;
    }

    /**
     * 删除并更新
     *
     * @param flag 标志
     */
    private void deleteAndSetRedisSecretData(boolean flag) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        Collection<String> keys = redisCache.keys(Constants.SECRET_CODE_KEY+"*");
        redisCache.deleteObject(keys);
        if (flag) {
            cacheSecretData();
        }
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Integer id) {
        DSecretKey dSecretKey = dSecretKeyMapper.queryById(id);
        SpringUtils.getBean(RedisCache.class).deleteObject(Constants.SECRET_CODE_KEY + dSecretKey.getPlatformNo() + ":" + dSecretKey.getUseType());
        return this.dSecretKeyMapper.deleteById(id) > 0;
    }

    @Override
    public List<DSecretKey> export(DSecretKey dSecretKey) {
        return dSecretKeyMapper.queryAllByLimit(dSecretKey);
    }
}
