package org.ruoyi.core.service;

import org.ruoyi.core.domain.DVintageMonth;

import java.util.List;
import java.util.Map;

/**
 * @Author: 左东冉
 * @Create: 2022-04-13 15:22
 * @Description: vintage Service层接口
 **/
public interface IDVintageMonthService {
    /**
     * 查询 VINTAGE 月 通过 ID
     *
     * @param id id
     * @return {@link DVintageMonth}
     */
    DVintageMonth selectDVintageMonthById(Long id);

    /**
     * 查询 VINTAGE 月 清单
     *
     * @param dVintageMonth d月
     * @return {@link List}<{@link DVintageMonth}>
     */
    List<DVintageMonth> selectDVintageMonthList(DVintageMonth dVintageMonth);

    List<DVintageMonth> exportDVintageMonthList(DVintageMonth dVintageMonth);

    /**
     * 新增 VINTAGE
     *
     * @param dVintageMonth d月
     * @return int
     */
    int insertDVintageMonth(DVintageMonth dVintageMonth);

    /**
     * 更新 VINTAGE 月
     *
     * @param dVintageMonth d月
     * @return int
     */
    int updateDVintageMonth(DVintageMonth dVintageMonth);

    /**
     * 删除 VINTAGE 由 IDS
     *
     * @param ids ID 数组
     * @return int
     */
    int deleteDVintageMonthByIds(Long[] ids);

    /**
     * 删除 VINTAGE 由 ID
     *
     * @param id ID
     * @return int
     */
    int deleteDVintageMonthById(Long id);

    /**
     * 重新映射数据
     * @return
     */
    void anewMappingData();

    List<Map<String, Object>> exportDVintageMonthList1(DVintageMonth dVintageMonth);
}
