package org.ruoyi.core.service.impl;

import java.util.List;import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.FunctionNodeEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.SysInfoEditRecord;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.service.ISysInfoEditRecordService;
import com.ruoyi.system.service.ISysOperLogService;
import org.ruoyi.core.domain.DProjectParameter;
import org.ruoyi.core.mapper.DProjectParameterMapper;
import org.ruoyi.core.service.IDProjectParameterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;


/**
 * 业务项目配置参数Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Service
public class DProjectParameterServiceImpl implements IDProjectParameterService
{
    @Autowired
    private DProjectParameterMapper dProjectParameterMapper;
    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    @Autowired
    private ISysOperLogService sysOperLogService;
    //@Autowired
    //private ISysInfoEditRecordService sysInfoEditRecordService;
    @Autowired
    private SysSelectDataRefServiceImpl sysSelectDataRefService;
    /**
     * 查询业务项目配置参数
     *
     * @param id 业务项目配置参数主键
     * @return 业务项目配置参数
     */
    @Override
    public DProjectParameter selectDProjectParameterById(Integer id)
    {
        return dProjectParameterMapper.selectDProjectParameterById(id);
    }

    /**
     * 查询业务项目配置参数列表
     *
     * @param dProjectParameter 业务项目配置参数
     * @return 业务项目配置参数
     */
    @Override
    public List<DProjectParameter> selectDProjectParameterList(DProjectParameter dProjectParameter)
    {
        List<DProjectParameter> dProjectParameters = dProjectParameterMapper.selectDProjectParameterList(dProjectParameter);
        if (!dProjectParameters.isEmpty()){
              List<String> productNoList = dProjectParameters.stream().map(DProjectParameter::getProductNo).collect(Collectors.toList());
              //历史数据存在为空,用字典数据进行关联产品名称
              Map<String, String> productNoMap = sysDictDataMapper.getDictLable("product_no",productNoList).stream()
                                 .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
              for (DProjectParameter parameter : dProjectParameters) {
                  if(parameter.getProductName() == null){
                      parameter.setProductName(productNoMap.get(parameter.getProductNo()));
                  }
              }
        }
        return dProjectParameters;
    }

    /**
     * 新增业务项目配置参数
     *
     * @param dProjectParameter 业务项目配置参数
     * @return 结果
     */
    @Override
    public int insertDProjectParameter(DProjectParameter dProjectParameter)
    {
        String errorMsg = "";
        try {
            dProjectParameter.setCreateTime(DateUtils.getNowDate());
            LoginUser loginUser = getLoginUser();
            dProjectParameter.setCreateBy(loginUser.getUsername());
            dProjectParameter.setUpdateBy(loginUser.getUsername());
            int i = dProjectParameterMapper.insertDProjectParameter(dProjectParameter);
            sysSelectDataRefService.insertSelectData(dProjectParameter,0);
            return dProjectParameter.getId();
        }catch (Exception e) {
                e.printStackTrace();
                StackTraceElement stackTraceElement = e.getStackTrace()[0];;
                errorMsg = "错误信息:" + e.toString() + " at "
                        + stackTraceElement.getClassName() + "."
                        + stackTraceElement.getMethodName() + ":"
                        + stackTraceElement.getLineNumber();
                throw new RuntimeException(e);
            } finally {
                String operMessage = "新增了【" + dProjectParameter.getProductName() + "】产品";
                Long i = sysOperLogService.insertOperLogMessageOfRecord(AuthModuleEnum.PRODUCT.getCode(), FunctionNodeEnum.PRODUCTIN.getCode(), operMessage, 1, errorMsg,dProjectParameter.getId().toString());
                //添加前后对比记录
//                if (i > 0){
//                    SysInfoEditRecord sysInfoEditRecord = new SysInfoEditRecord();
//                    sysInfoEditRecord.setLogQueryId(i);
//                    sysInfoEditRecord.setNewInfo(JSON.toJSONString(dProjectParameter));
//                    sysInfoEditRecordService.insertSysInfoEditRecord(sysInfoEditRecord);
//                }
        }
    }

    /**
     * 修改业务项目配置参数
     *
     * @param dProjectParameter 业务项目配置参数
     * @return 结果
     */
    @Override
    public int updateDProjectParameter(DProjectParameter dProjectParameter)
    {
        String errorMsg = "";
        DProjectParameter oldDProjectParameter = selectDProjectParameterById(dProjectParameter.getId());
        try {
            dProjectParameter.setUpdateTime(DateUtils.getNowDate());
            dProjectParameter.setUpdateBy(getLoginUser().getUsername());
            sysSelectDataRefService.insertSelectData(dProjectParameter,1);
            return dProjectParameterMapper.updateDProjectParameter(dProjectParameter);
        }catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "修改了【" + dProjectParameter.getProductName() + "】产品";
            Long i = sysOperLogService.insertOperLogMessageOfRecord(AuthModuleEnum.PRODUCT.getCode(), FunctionNodeEnum.PRODUCTIN.getCode(), operMessage, 2, errorMsg,dProjectParameter.getId().toString());
            //添加前后对比记录
//            if (i > 0){
//                SysInfoEditRecord sysInfoEditRecord = new SysInfoEditRecord();
//                sysInfoEditRecord.setLogQueryId(i);
//                sysInfoEditRecord.setOldInfo(JSON.toJSONString(oldDProjectParameter));
//                sysInfoEditRecord.setNewInfo(JSON.toJSONString(dProjectParameter));
//                sysInfoEditRecordService.insertSysInfoEditRecord(sysInfoEditRecord);
//            }
        }
    }

    /**
     * 批量删除业务项目配置参数
     *
     * @param ids 需要删除的业务项目配置参数主键
     * @return 结果
     */
    @Override
    public int deleteDProjectParameterByIds(Integer[] ids)
    {
        return dProjectParameterMapper.deleteDProjectParameterByIds(ids);
    }

    /**
     * 删除业务项目配置参数信息
     *
     * @param id 业务项目配置参数主键
     * @return 结果
     */
    @Override
    public int deleteDProjectParameterById(Integer id)
    {
        String errorMsg = "";
        DProjectParameter dProjectParameter = selectDProjectParameterById(id);
        try {
            sysSelectDataRefService.deleteProduct(Long.parseLong(id.toString()));
            return dProjectParameterMapper.deleteDProjectParameterById(id);
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "删除了【" + dProjectParameter.getProductName() + "】产品";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PRODUCT.getCode(), FunctionNodeEnum.PRODUCTIN.getCode(), operMessage, 3, errorMsg, id.toString());
        }
    }
}
