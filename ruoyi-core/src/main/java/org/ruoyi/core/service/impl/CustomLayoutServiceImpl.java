package org.ruoyi.core.service.impl;

import java.util.List;

import org.ruoyi.core.domain.CustomLayout;
import org.ruoyi.core.mapper.CustomLayoutMapper;
import org.ruoyi.core.service.ICustomLayoutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-05-08
 */
@Service
public class CustomLayoutServiceImpl implements ICustomLayoutService
{
    @Autowired
    private CustomLayoutMapper customLayoutMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public CustomLayout selectCustomLayoutById(Long id)
    {
        return customLayoutMapper.selectCustomLayoutById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param customLayout 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<CustomLayout> selectCustomLayoutList(CustomLayout customLayout)
    {
        return customLayoutMapper.selectCustomLayoutList(customLayout);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param customLayout 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertCustomLayout(CustomLayout customLayout)
    {
        return customLayoutMapper.insertCustomLayout(customLayout);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param customLayout 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateCustomLayout(CustomLayout customLayout)
    {
        return customLayoutMapper.updateCustomLayout(customLayout);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteCustomLayoutByIds(Long[] ids)
    {
        return customLayoutMapper.deleteCustomLayoutByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteCustomLayoutById(Long id)
    {
        return customLayoutMapper.deleteCustomLayoutById(id);
    }
}
