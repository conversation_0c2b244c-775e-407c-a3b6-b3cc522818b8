package org.ruoyi.core.yybbsc.excelutils;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.ruoyi.core.yybbsc.domain.StsIncomeForecastLoanInfo;
import org.ruoyi.core.yybbsc.domain.dto.StsIncomeForecastRepaymentInfoDto;
import org.ruoyi.core.yybbsc.domain.dto.StsOperateDayDto;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName: ExcelImport
 * @Description: 运营报表生成导入Excel相关处理
 * @Date: 2022/12/19 17:26
 * @Author: dyh
 */
public class ExcelImport {
    private InputStream inputStream;
    private Workbook wb;


    public void init(InputStream is) throws IOException {
        this.inputStream = is;
        this.wb = WorkbookFactory.create(is);
    }

    /**
     * 对excel表单指定表格索引名转换成list
     *
     * @param sheetName 表格索引名
     * @param index 列的索引（本需求是固定值）
     * @return 转换后集合
     */
    public BigDecimal importExcelDomain(String sheetName, int index) throws Exception
    {
        Workbook sheets = this.wb;
        BigDecimal entity = null;
        Sheet sheet = null;
        if (StringUtils.isNotEmpty(sheetName))
        {
            // 如果指定sheet名,则取指定sheet中的内容.
            sheet = sheets.getSheet(sheetName);
        }
        else
        {
            // 如果传入的sheet名不存在则默认指向第1个sheet.
            sheet = sheets.getSheetAt(0);
        }

        if (sheet == null)
        {
            throw new IOException("文件sheet不存在");
        }

        int rows = sheet.getPhysicalNumberOfRows();

        if (rows > 0)
        {
            // if (rows == 1) {
                // 取第2行数据,默认第一行是表头.
                Row row = sheet.getRow(1);
                entity = new BigDecimal(String.valueOf(row.getCell(index).getNumericCellValue()));
            // }
        }
        return entity;
    }

    /**
     * 对excel表单指定表格索引名转换成list
     *
     * @param sheetName 表格索引名
     * @param index 列的索引（本需求是固定值）
     * @return 转换后集合
     */
    public BigDecimal importExcelDomains(String sheetName, int index) throws Exception
    {
        Workbook sheets = this.wb;
        BigDecimal entity = new BigDecimal("0.00");
        Sheet sheet = null;
        if (StringUtils.isNotEmpty(sheetName))
        {
            // 如果指定sheet名,则取指定sheet中的内容.
            sheet = sheets.getSheet(sheetName);
        }
        else
        {
            // 如果传入的sheet名不存在则默认指向第1个sheet.
            sheet = sheets.getSheetAt(0);
        }

        if (sheet == null)
        {
            throw new IOException("文件sheet不存在");
        }

        int rows = sheet.getPhysicalNumberOfRows();

        if (rows > 0)
        {
            for (int i = 2; i < rows; i++) {
                // 从第3行取数据,默认第一行是表头.
                Row row = sheet.getRow(i);
                //做判断，看---->中保收入记账报表中是否代偿为是
                if ("是".equals(row.getCell(13).getStringCellValue())) {
                    entity = entity.add(new BigDecimal(String.valueOf(row.getCell(index).getNumericCellValue())));
                }
            }
        }
        return entity;
    }

    /**
     * 对excel表单指定表格索引名转换成list
     *
     * @return 转换后集合
     */
    public List<StsOperateDayDto> checkExcelDomains() throws Exception
    {
        Workbook sheets = this.wb;
        Sheet sheet = sheets.getSheetAt(0);
        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }
        int rows = sheet.getPhysicalNumberOfRows();
        List<StsOperateDayDto> stsList = new ArrayList<>();
        if (rows > 0)
        {
            //遍历所有行
            for (int i = 1; i < rows-1; i++) {
                StsOperateDayDto stsDto = new StsOperateDayDto();
                // 从第2行取数据,默认第一行是表头.
                Row row = sheet.getRow(i);
                //获取日期给Dto
                stsDto.setReconDate(DateUtils.parseDate(row.getCell(0).getStringCellValue()));
                //获取放贷金额给Dto
                stsDto.setLoanAmt(new BigDecimal(String.valueOf(row.getCell(5).getNumericCellValue())));
                //获取实还本金给Dto
                stsDto.setActPrintAmt(new BigDecimal(String.valueOf(row.getCell(7).getNumericCellValue())));
                //获取用户实还息费给Dto
                stsDto.setActIntAmt(new BigDecimal(String.valueOf(row.getCell(8).getNumericCellValue())));
                //获取借条分润给Dto
                stsDto.setJtFrAmt(new BigDecimal(String.valueOf(row.getCell(11).getNumericCellValue())));
                //获取中保分润给Dto
                stsDto.setZbFrAmt(new BigDecimal(String.valueOf(row.getCell(12).getNumericCellValue())));
                //获取客户贷款余额给Dto
                stsDto.setUserBalanceAmt(new BigDecimal(String.valueOf(row.getCell(13).getNumericCellValue())));
                //放入List
                stsList.add(stsDto);
            }
        }
        return stsList;
    }

    /**
     * 对账单中，放款表解析
     *
     * @return 转换后集合
     */
    public List<StsIncomeForecastLoanInfo> importExcelStaOfAccOfSheetLoan() throws Exception
    {
        List<StsIncomeForecastLoanInfo> list = new ArrayList<>();
        Workbook sheets = this.wb;
        Sheet sheet = sheets.getSheetAt(0);
        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }

        int rows = sheet.getPhysicalNumberOfRows();

        //正则去逗号
        String regex = "(?<=[\\d])(,)(?=[\\d])";
        NumberFormat nf = NumberFormat.getInstance();
        if (rows > 0)
        {
            for (int i = 4; i <= rows; i++) {
                // 从第7行取数据,默认第一行是表头.
                Row row = sheet.getRow(i);
                //正则校验日期
                String checkRegex = "\\d{4}(\\-|\\/|.)\\d{1,2}\\1\\d{1,2}";
                Cell cell1 = row.getCell(0);
                if (cell1 != null) {
                    if ("STRING".equals(row.getCell(0).getCellType().name())) {
                        if (row != null && row.getCell(0) != null && !StringUtils.EMPTY.equals(row.getCell(0).getStringCellValue()) && Pattern.compile(checkRegex).matcher(row.getCell(0) + "-01").matches()) {
                            //获取放款月份
                            String loanMonth = row.getCell(1).getStringCellValue();
                            //获取放款金额
                            BigDecimal loanAmt = new BigDecimal(nf.format(row.getCell(2).getNumericCellValue()).replaceAll(regex, ""));
                            //获取产品类型
                            String productType = StringUtils.EMPTY;
                            String stringCellValue = row.getCell(3).getStringCellValue();
                            switch (stringCellValue) {
                                case "无":
                                    productType = "0";
                                    break;
                                case "随借随还":
                                    productType = "1";
                                    break;
                                case "等本等息":
                                    productType = "2";
                                    break;
                                case "先息后本":
                                    productType = "3";
                                    break;
                                default:
                                    break;
                            }
                            //获取期数
                            String name = row.getCell(4).getCellType().name();
                            int phase = -999;
                            //在测试的时候发现有单元格格式不同的情况，有的是字符串，有的是int类型
                            if ("STRING".equals(name)) {
                                phase = Integer.parseInt(row.getCell(4).getStringCellValue());
                            } else if ("NUMERIC".equals(name)) {
                                phase = (int) row.getCell(4).getNumericCellValue();
                            }
                            //组装对象
                            StsIncomeForecastLoanInfo stsIncomeForecastLoanInfo = StsIncomeForecastLoanInfo.builder()
                                    .loanMonth(loanMonth)
                                    .loanAmt(loanAmt)
                                    .productType(productType)
                                    .phase(phase)
                                    .build();
                            list.add(stsIncomeForecastLoanInfo);
                        }
                    } else {
                        Cell cell = row.getCell(1);
                        String cellTypeToString = getCellTypeToString(cell);
                        if (row != null && row.getCell(0) != null && !"BLANK".equals(cellTypeToString)) {
                            //获取放款月份
                            String loanMonth = StringUtils.EMPTY;
                            if ("STRING".equals(cellTypeToString)) {
                                loanMonth = row.getCell(1).getStringCellValue();
                            } else {
                                Date dateCellValue = cell.getDateCellValue();
                                loanMonth = DateUtils.parseDateToStr(DateUtils.YYYY_MM, dateCellValue);
                            }
                            //获取放款金额
                            BigDecimal loanAmt = new BigDecimal(nf.format(row.getCell(2).getNumericCellValue()).replaceAll(regex, ""));
                            //获取产品类型
                            String productType = StringUtils.EMPTY;
                            String stringCellValue = row.getCell(3).getStringCellValue();
                            switch (stringCellValue) {
                                case "无":
                                    productType = "0";
                                    break;
                                case "随借随还":
                                    productType = "1";
                                    break;
                                case "等本等息":
                                    productType = "2";
                                    break;
                                case "先息后本":
                                    productType = "3";
                                    break;
                                default:
                                    break;
                            }
                            //获取期数
                            String name = row.getCell(4).getCellType().name();
                            int phase = -999;
                            //在测试的时候发现有单元格格式不同的情况，有的是字符串，有的是int类型
                            if ("STRING".equals(name)) {
                                phase = Integer.parseInt(row.getCell(4).getStringCellValue());
                            } else if ("NUMERIC".equals(name)) {
                                phase = (int) row.getCell(4).getNumericCellValue();
                            }
                            //组装对象
                            StsIncomeForecastLoanInfo stsIncomeForecastLoanInfo = StsIncomeForecastLoanInfo.builder()
                                    .loanMonth(loanMonth)
                                    .loanAmt(loanAmt)
                                    .productType(productType)
                                    .phase(phase)
                                    .build();
                            list.add(stsIncomeForecastLoanInfo);
                        }
                    }
                }
//                if (row != null && row.getCell(0) != null && !StringUtils.EMPTY.equals(row.getCell(0).getStringCellValue()) && Pattern.compile(checkRegex).matcher(row.getCell(0) + "-01").matches()) {
//                    //获取放款月份
//                    String loanMonth = row.getCell(1).getStringCellValue();
//                    //获取放款金额
//                    BigDecimal loanAmt = new BigDecimal(nf.format(row.getCell(2).getNumericCellValue()).replaceAll(regex, ""));
//                    //获取产品类型
//                    String productType = StringUtils.EMPTY;
//                    String stringCellValue = row.getCell(3).getStringCellValue();
//                    switch (stringCellValue) {
//                        case "无":
//                            productType = "0";
//                            break;
//                        case "随借随还":
//                            productType = "1";
//                            break;
//                        case "等本等息":
//                            productType = "2";
//                            break;
//                        case "先息后本":
//                            productType = "3";
//                            break;
//                        default:
//                            break;
//                    }
//                    //获取期数
//                    String name = row.getCell(4).getCellType().name();
//                    int phase = -999;
//                    //在测试的时候发现有单元格格式不同的情况，有的是字符串，有的是int类型
//                    if ("STRING".equals(name)) {
//                        phase = Integer.parseInt(row.getCell(4).getStringCellValue());
//                    } else if ("NUMERIC".equals(name)) {
//                        phase = (int) row.getCell(4).getNumericCellValue();
//                    }
//                    //组装对象
//                    StsIncomeForecastLoanInfo stsIncomeForecastLoanInfo = StsIncomeForecastLoanInfo.builder()
//                            .loanMonth(loanMonth)
//                            .loanAmt(loanAmt)
//                            .productType(productType)
//                            .phase(phase)
//                            .build();
//                    list.add(stsIncomeForecastLoanInfo);
//                }
            }
        }
        List<StsIncomeForecastLoanInfo> collect = list.stream().filter(t -> t.getPhase() != -999).collect(Collectors.toList());
        return collect;
    }

    /**
     * 对账单中，还款表解析
     *
     * @return 转换后集合
     */
    public List<StsIncomeForecastRepaymentInfoDto> importExcelStaOfAccOfSheetRepayment() throws Exception
    {
        List<StsIncomeForecastRepaymentInfoDto> list = new ArrayList<>();
        Workbook sheets = this.wb;
        Sheet sheet = sheets.getSheetAt(1);
        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }

        int rows = sheet.getPhysicalNumberOfRows();

        //正则去逗号
        String regex = "(?<=[\\d])(,)(?=[\\d])";
        NumberFormat nf = NumberFormat.getInstance();
        if (rows > 0)
        {
            for (int i = 4; i <= rows; i++) {
                // 从第10行开始做判断,默认第一行是表头.
                Row row = sheet.getRow(i);
                Cell cell = row.getCell(4);
                String cellTypeToString = getCellTypeToString(cell);
                if ("STRING".equals(cellTypeToString)) {
                    //判断放款月份是否是这个月的
                    if (row.getCell(4) != null && !StringUtils.EMPTY.equals(row.getCell(4).getStringCellValue()) ) {
                        Cell cell1 = row.getCell(1);
                        String cellTypeToString1 = getCellTypeToString(cell1);
                        String loanMonth = StringUtils.EMPTY;
                        if ("STRING".equals(cellTypeToString1)) {
                            loanMonth = row.getCell(1).getStringCellValue();
                        } else {
                            loanMonth = DateUtils.parseDateToStr(DateUtils.YYYY_MM, row.getCell(1).getDateCellValue());
                        }
                        //获取产品类型
                        String productType = StringUtils.EMPTY;
                        String stringCellValue = row.getCell(2).getStringCellValue();
                        switch (stringCellValue) {
                            case "无":
                                productType = "0";
                                break;
                            case "随借随还":
                                productType = "1";
                                break;
                            case "等本等息":
                                productType = "2";
                                break;
                            case "先息后本":
                                productType = "3";
                                break;
                            default:
                                break;
                        }
                        String name = row.getCell(3).getCellType().name();
                        int phase = -1;
                        if ("STRING".equals(name)) {
                            phase = Integer.parseInt(row.getCell(3).getStringCellValue());
                        } else if ("NUMERIC".equals(name)) {
                            phase = (int) row.getCell(3).getNumericCellValue();
                        }
//                        int phase = Integer.parseInt(row.getCell(3).getStringCellValue());
                        //获取还款月份
                        String repaymentMonth = row.getCell(4).getStringCellValue();
                        //获取还款本金
                        BigDecimal repaymentPrintAmount = new BigDecimal(nf.format(row.getCell(5).getNumericCellValue()).replaceAll(regex, ""));
                        //获取还款利息
                        BigDecimal repaymentIntAmount = new BigDecimal(nf.format(row.getCell(6).getNumericCellValue()).replaceAll(regex, ""));
                        //获取还款罚息
                        BigDecimal repaymentOintAmt = new BigDecimal(nf.format(row.getCell(7).getNumericCellValue()).replaceAll(regex, ""));
                        //获取还款复利
                        BigDecimal repaymentFlAmt = new BigDecimal(nf.format(row.getCell(8).getNumericCellValue()).replaceAll(regex, ""));
                        //获取提前还款违约金
                        BigDecimal advDefineAmt = new BigDecimal(nf.format(row.getCell(9).getNumericCellValue()).replaceAll(regex, ""));
                        //获取活动抵扣金额
                        BigDecimal deduceAmt = new BigDecimal(nf.format(row.getCell(10).getNumericCellValue()).replaceAll(regex, ""));
                        //获取红线减免金额
                        BigDecimal reduceAmt = new BigDecimal(nf.format(row.getCell(11).getNumericCellValue()).replaceAll(regex, ""));
                        //组装对象
                        StsIncomeForecastRepaymentInfoDto stsIncomeForecastRepaymentInfoDto = StsIncomeForecastRepaymentInfoDto.builder()
                                .loanMonth(loanMonth)
                                .productType(productType)
                                .phase(phase)
                                .repaymentMonth(repaymentMonth)
                                .repaymentPrintAmount(repaymentPrintAmount)
                                .repaymentIntAmount(repaymentIntAmount)
                                .repaymentOintAmt(repaymentOintAmt)
                                .repaymentFlAmt(repaymentFlAmt)
                                .advDefineAmt(advDefineAmt)
                                .deductAmt(deduceAmt)
                                .reduceAmt(reduceAmt)
                                .build();
                        list.add(stsIncomeForecastRepaymentInfoDto);
                    }
                } else {
                    //判断放款月份是否是这个月的
                    if (row.getCell(4) != null && row.getCell(4).getDateCellValue() != null) {
                        Cell cell1 = row.getCell(1);
                        String cellTypeToString1 = getCellTypeToString(cell1);
                        String loanMonth = StringUtils.EMPTY;
                        if ("STRING".equals(cellTypeToString1)) {
                            loanMonth = row.getCell(1).getStringCellValue();
                        } else {
                            loanMonth = DateUtils.parseDateToStr(DateUtils.YYYY_MM, row.getCell(1).getDateCellValue());
                        }
                        //获取产品类型
                        String productType = StringUtils.EMPTY;
                        String stringCellValue = row.getCell(2).getStringCellValue();
                        switch (stringCellValue) {
                            case "无":
                                productType = "0";
                                break;
                            case "随借随还":
                                productType = "1";
                                break;
                            case "等本等息":
                                productType = "2";
                                break;
                            case "先息后本":
                                productType = "3";
                                break;
                            default:
                                break;
                        }
                        String name = row.getCell(3).getCellType().name();
                        int phase = -1;
                        if ("STRING".equals(name)) {
                            phase = Integer.parseInt(row.getCell(3).getStringCellValue());
                        } else if ("NUMERIC".equals(name)) {
                            phase = (int) row.getCell(3).getNumericCellValue();
                        }
//                        int phase = Integer.parseInt(row.getCell(3).getStringCellValue());
                        //获取还款月份
                        String repaymentMonth = DateUtils.parseDateToStr(DateUtils.YYYY_MM, row.getCell(4).getDateCellValue());
                        //获取还款本金
                        BigDecimal repaymentPrintAmount = new BigDecimal(nf.format(row.getCell(5).getNumericCellValue()).replaceAll(regex, ""));
                        //获取还款利息
                        BigDecimal repaymentIntAmount = new BigDecimal(nf.format(row.getCell(6).getNumericCellValue()).replaceAll(regex, ""));
                        //获取还款罚息
                        BigDecimal repaymentOintAmt = new BigDecimal(nf.format(row.getCell(7).getNumericCellValue()).replaceAll(regex, ""));
                        //获取还款复利
                        BigDecimal repaymentFlAmt = new BigDecimal(nf.format(row.getCell(8).getNumericCellValue()).replaceAll(regex, ""));
                        //获取提前还款违约金
                        BigDecimal advDefineAmt = new BigDecimal(nf.format(row.getCell(9).getNumericCellValue()).replaceAll(regex, ""));
                        //获取活动抵扣金额
                        BigDecimal deduceAmt = new BigDecimal(nf.format(row.getCell(10).getNumericCellValue()).replaceAll(regex, ""));
                        //获取红线减免金额
                        BigDecimal reduceAmt = new BigDecimal(nf.format(row.getCell(11).getNumericCellValue()).replaceAll(regex, ""));
                        //组装对象
                        StsIncomeForecastRepaymentInfoDto stsIncomeForecastRepaymentInfoDto = StsIncomeForecastRepaymentInfoDto.builder()
                                .loanMonth(loanMonth)
                                .productType(productType)
                                .phase(phase)
                                .repaymentMonth(repaymentMonth)
                                .repaymentPrintAmount(repaymentPrintAmount)
                                .repaymentIntAmount(repaymentIntAmount)
                                .repaymentOintAmt(repaymentOintAmt)
                                .repaymentFlAmt(repaymentFlAmt)
                                .advDefineAmt(advDefineAmt)
                                .deductAmt(deduceAmt)
                                .reduceAmt(reduceAmt)
                                .build();
                        list.add(stsIncomeForecastRepaymentInfoDto);
                    }
                }
//                //判断放款月份是否是这个月的
//                if (row.getCell(4) != null && !StringUtils.EMPTY.equals(row.getCell(4).getStringCellValue()) ) {
//                    String loanMonth = row.getCell(1).getStringCellValue();
//                    //获取产品类型
//                    String productType = StringUtils.EMPTY;
//                    String stringCellValue = row.getCell(2).getStringCellValue();
//                    switch (stringCellValue) {
//                        case "无":
//                            productType = "0";
//                            break;
//                        case "随借随还":
//                            productType = "1";
//                            break;
//                        case "等本等息":
//                            productType = "2";
//                            break;
//                        case "先息后本":
//                            productType = "3";
//                            break;
//                        default:
//                            break;
//                    }
//                    String name = row.getCell(3).getCellType().name();
//                    int phase = -1;
//                    if ("STRING".equals(name)) {
//                        phase = Integer.parseInt(row.getCell(3).getStringCellValue());
//                    } else if ("NUMERIC".equals(name)) {
//                        phase = (int) row.getCell(3).getNumericCellValue();
//                    }
////                        int phase = Integer.parseInt(row.getCell(3).getStringCellValue());
//                    //获取还款月份
//                    String repaymentMonth = row.getCell(4).getStringCellValue();
//                    //获取还款本金
//                    BigDecimal repaymentPrintAmount = new BigDecimal(nf.format(row.getCell(5).getNumericCellValue()).replaceAll(regex, ""));
//                    //获取还款利息
//                    BigDecimal repaymentIntAmount = new BigDecimal(nf.format(row.getCell(6).getNumericCellValue()).replaceAll(regex, ""));
//                    //获取还款罚息
//                    BigDecimal repaymentOintAmt = new BigDecimal(nf.format(row.getCell(7).getNumericCellValue()).replaceAll(regex, ""));
//                    //获取还款复利
//                    BigDecimal repaymentFlAmt = new BigDecimal(nf.format(row.getCell(8).getNumericCellValue()).replaceAll(regex, ""));
//                    //获取提前还款违约金
//                    BigDecimal advDefineAmt = new BigDecimal(nf.format(row.getCell(9).getNumericCellValue()).replaceAll(regex, ""));
//                    //获取活动抵扣金额
//                    BigDecimal deduceAmt = new BigDecimal(nf.format(row.getCell(10).getNumericCellValue()).replaceAll(regex, ""));
//                    //获取红线减免金额
//                    BigDecimal reduceAmt = new BigDecimal(nf.format(row.getCell(11).getNumericCellValue()).replaceAll(regex, ""));
//                    //组装对象
//                    StsIncomeForecastRepaymentInfoDto stsIncomeForecastRepaymentInfoDto = StsIncomeForecastRepaymentInfoDto.builder()
//                            .loanMonth(loanMonth)
//                            .productType(productType)
//                            .phase(phase)
//                            .repaymentMonth(repaymentMonth)
//                            .repaymentPrintAmount(repaymentPrintAmount)
//                            .repaymentIntAmount(repaymentIntAmount)
//                            .repaymentOintAmt(repaymentOintAmt)
//                            .repaymentFlAmt(repaymentFlAmt)
//                            .advDefineAmt(advDefineAmt)
//                            .deductAmt(deduceAmt)
//                            .reduceAmt(reduceAmt)
//                            .build();
//                    list.add(stsIncomeForecastRepaymentInfoDto);
//                }
            }
        }
        return list;
    }

    /**
     * 对账单中，插入之前月份放款的本月的还款
     *
     * @return 转换后集合
     */
    public List<StsIncomeForecastRepaymentInfoDto> importExcelStaOfAccOfSheetRepaymentBefore(String repaymentMonth1) throws Exception
    {
        List<StsIncomeForecastRepaymentInfoDto> list = new ArrayList<>();
        Workbook sheets = this.wb;
        Sheet sheet = sheets.getSheetAt(1);
        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }

        int rows = sheet.getPhysicalNumberOfRows();

        //正则去逗号
        String regex = "(?<=[\\d])(,)(?=[\\d])";
        NumberFormat nf = NumberFormat.getInstance();
        if (rows > 0) {
            for (int i = 4; i <= rows; i++) {
                // 从第10行开始做判断,默认第一行是表头.
                Row row = sheet.getRow(i);
                //判断还款月份是否是这个月的
                if (row.getCell(4) != null && row.getCell(4).getStringCellValue().equals(repaymentMonth1)) {
                    if (!repaymentMonth1.equals(row.getCell(1).getStringCellValue())) {
                        String loanMonth = row.getCell(1).getStringCellValue();
                        //获取产品类型
                        String productType = StringUtils.EMPTY;
                        String stringCellValue = row.getCell(2).getStringCellValue();
                        switch (stringCellValue) {
                            case "无":
                                productType = "0";
                                break;
                            case "随借随还":
                                productType = "1";
                                break;
                            case "等本等息":
                                productType = "2";
                                break;
                            case "先息后本":
                                productType = "3";
                                break;
                            default:
                                break;
                        }
                        //获取期数
                        int phase = -1;
                        if ("NUMERIC".equals(row.getCell(3).getCellType().name())) {
                            phase = (int) row.getCell(3).getNumericCellValue();
                        }
                        if ("STRING".equals(row.getCell(3).getCellType().name())) {
                            phase = Integer.parseInt(row.getCell(3).getStringCellValue());
                        }
                        //获取还款月份
                        String repaymentMonth = row.getCell(4).getStringCellValue();
                        //获取还款本金
                        BigDecimal repaymentPrintAmount = new BigDecimal(nf.format(row.getCell(5).getNumericCellValue()).replaceAll(regex, ""));
                        //获取还款利息
                        BigDecimal repaymentIntAmount = new BigDecimal(nf.format(row.getCell(6).getNumericCellValue()).replaceAll(regex, ""));
                        //获取还款罚息
                        BigDecimal repaymentOintAmt = new BigDecimal(nf.format(row.getCell(7).getNumericCellValue()).replaceAll(regex, ""));
                        //获取还款复利
                        BigDecimal repaymentFlAmt = new BigDecimal(nf.format(row.getCell(8).getNumericCellValue()).replaceAll(regex, ""));
                        //获取提前还款违约金
                        BigDecimal advDefineAmt = new BigDecimal(nf.format(row.getCell(9).getNumericCellValue()).replaceAll(regex, ""));
                        //获取活动抵扣金额
                        BigDecimal deduceAmt = new BigDecimal(nf.format(row.getCell(10).getNumericCellValue()).replaceAll(regex, ""));
                        //获取红线减免金额
                        BigDecimal reduceAmt = new BigDecimal(nf.format(row.getCell(11).getNumericCellValue()).replaceAll(regex, ""));
                        //组装对象
                        StsIncomeForecastRepaymentInfoDto stsIncomeForecastRepaymentInfoDto = StsIncomeForecastRepaymentInfoDto.builder()
                                .loanMonth(loanMonth)
                                .productType(productType)
                                .phase(phase)
                                .repaymentMonth(repaymentMonth)
                                .repaymentPrintAmount(repaymentPrintAmount)
                                .repaymentIntAmount(repaymentIntAmount)
                                .repaymentOintAmt(repaymentOintAmt)
                                .repaymentFlAmt(repaymentFlAmt)
                                .advDefineAmt(advDefineAmt)
                                .deductAmt(deduceAmt)
                                .reduceAmt(reduceAmt)
                                .build();
                        list.add(stsIncomeForecastRepaymentInfoDto);
                    }
                }
            }
        }
        return list;
    }

    /**
     * 还款表解析 - 先解析一个还款月份，看是否符合导入规则
     *
     * @return 转换后集合
     */
    public Date importExcelRepay() throws Exception
    {
//        List<StsIncomeForecastLoanInfo> list = new ArrayList<>();
        Workbook sheets = this.wb;
        Sheet sheet = sheets.getSheetAt(0);
        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }

        int rows = sheet.getPhysicalNumberOfRows();

        //正则去逗号
        String regex = "(?<=[\\d])(,)(?=[\\d])";
        NumberFormat nf = NumberFormat.getInstance();
        Date dateCellValue = null;
        if (rows > 0)
        {
            // 从第2行取数据,默认第一行是表头.
            Row row = sheet.getRow(1);
            //获取还款月份
            dateCellValue = row.getCell(1).getDateCellValue();
        }
        return dateCellValue;
    }

//    /**
//     * 还款表解析
//     *
//     * @return 转换后集合
//     */
//    public List<StsIncomeForecastLoanInfo> importExcelRepay() throws Exception
//    {
////        List<StsIncomeForecastLoanInfo> list = new ArrayList<>();
//        Workbook sheets = this.wb;
//        Sheet sheet = sheets.getSheetAt(0);
//        if (sheet == null) {
//            throw new IOException("文件sheet不存在");
//        }
//
//        int rows = sheet.getPhysicalNumberOfRows();
//
//        //正则去逗号
//        String regex = "(?<=[\\d])(,)(?=[\\d])";
//        NumberFormat nf = NumberFormat.getInstance();
//        if (rows > 0)
//        {
//            for (int i = 1; i < rows; i++) {
//                // 从第2行取数据,默认第一行是表头.
//                Row row = sheet.getRow(i);
//                //获取放款月份
//                String loanMonth = row.getCell(1).getStringCellValue();
//                //获取放款金额
//                BigDecimal loanAmt = new BigDecimal(nf.format(row.getCell(2).getNumericCellValue()).replaceAll(regex, ""));
//                //获取产品类型
//                String productType = StringUtils.EMPTY;
//                String stringCellValue = row.getCell(3).getStringCellValue();
//                switch (stringCellValue) {
//                    case "无"  :
//                        productType = "0";
//                        break;
//                    case "随借随还" :
//                        productType = "1";
//                        break;
//                    case "等本等息" :
//                        productType = "2";
//                        break;
//                    case "先息后本" :
//                        productType = "3";
//                        break;
//                    default:
//                        productType = "0";
//                }
//                //获取期数
//                int phase = Integer.parseInt(row.getCell(4).getStringCellValue());
//                //组装对象
//                StsIncomeForecastLoanInfo stsIncomeForecastLoanInfo = StsIncomeForecastLoanInfo.builder()
//                        .loanMonth(loanMonth)
//                        .loanAmt(loanAmt)
//                        .productType(productType)
//                        .phase(phase)
//                        .build();
//                list.add(stsIncomeForecastLoanInfo);
//            }
//        }
//        return list;
//    }

    //设置单元格合并样式

    public static void setBorder(CellRangeAddress a,SXSSFSheet sheet) {
        RegionUtil.setBorderTop(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, a, sheet);
    }

    //标题样式
    public static CellStyle getHeaderFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 16);//字体大小
        font.setBold(true);//加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }

    //表头样式
    public static CellStyle getTitleFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 13);//字体大小
        font.setBold(true);//加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }

    //内容样式
    public static CellStyle getDataFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);//字体大小
        font.setBold(false);//不加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setWrapText(true);
        return cellStyle;
    }

    //处理数据
    public static String getValue(Object object){
        if (object==null){
            return "";
        }else {
            return object.toString();
        }
    }

    public static String getCellTypeToString(Cell cell) {
        String cellType = "";
        if (cell != null) {
            switch (cell.getCellType()) {
                case BLANK:
                    cellType = "BLANK";
                    break;
                case BOOLEAN:
                    cellType = "BOOLEAN";
                    break;
                case ERROR:
                    cellType = "ERROR";
                    break;
                case FORMULA:
                    cellType = "FORMULA";
                    break;
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        cellType = "DATE";
                    } else {
                        cellType = "NUMBER";
                    }
                    break;
                case STRING:
                    cellType = "STRING";
                    break;
                default:
                    cellType = "UNKNOW";
                    break;
            }
        }
        return cellType;
    }
}
