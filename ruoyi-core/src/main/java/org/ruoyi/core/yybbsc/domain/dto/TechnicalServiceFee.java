package org.ruoyi.core.yybbsc.domain.dto;

import lombok.*;

import java.math.BigDecimal;

/**
 * 放款月份的每个还款月的放款金额和技术管理费
 *
 * <AUTHOR>
 * @date 2023-01-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class TechnicalServiceFee {

    private static final long serialVersionUID = 1L;

    /** 放款月份 */
    private String loanMonth;

    /** 技术服务费 */
    private BigDecimal technicalServiceFee;
}
