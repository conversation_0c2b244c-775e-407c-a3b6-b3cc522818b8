package org.ruoyi.core.yybbsc.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 收入预测报表配置 - 页面展示vo
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ConfigurationVo {

    private static final long serialVersionUID = 1L;

    /** 参数项名称 */
    private String parameterName;

    /** 参数码 */
    private String parameterCode;

    /** 最后维护人 */
    private String lastUpdateUserName;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss")
    private Date updateTime;
}
