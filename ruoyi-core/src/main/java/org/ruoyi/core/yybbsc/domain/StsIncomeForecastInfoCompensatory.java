package org.ruoyi.core.yybbsc.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 收入预测报-收入预测信息对象 sts_income_forecast_info
 *
 * <AUTHOR>
 * @date 2023-01-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class StsIncomeForecastInfoCompensatory extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 预留产品代码字段：0-富邦 */
    private String productNo;

    /** 放款月份 */
    @Excel(name = "放款月份")
    private String loanMonth;

    /** 代偿月份 */
    @Excel(name = "代偿月份")
    private String compensatoryMonth;

    /** 代偿总金额 */
    @Excel(name = "代偿总金额")
    private BigDecimal compensateTotalAmt;
}
