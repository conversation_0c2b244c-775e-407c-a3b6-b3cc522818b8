package org.ruoyi.core.yybbsc.domain.dto;

import lombok.*;

import java.math.BigDecimal;

/**
 * 放款月份的每个还款月的放款金额和技术管理费
 *
 * <AUTHOR>
 * @date 2023-01-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class StsIncomeForecastTotalDtoPart1 {

    private static final long serialVersionUID = 1L;

    /** 放款月份 */
    private String loanMonth;

    /** 放款金额 */
    private BigDecimal loanAmt;

    /** 技术服务费 */
    private BigDecimal technicalServiceFee;

    /** 0-无，1-随借随还，2-等本等息，3-先息后本 */
    private String productType;

    /** 期数 */
    private String phase;

    /** 还款月份 */
    private String repaymentMonth;
}
