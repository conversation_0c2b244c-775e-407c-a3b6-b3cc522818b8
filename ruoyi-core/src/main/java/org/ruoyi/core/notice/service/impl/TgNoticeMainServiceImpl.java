package org.ruoyi.core.notice.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.AuthRoleEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.ruoyi.core.notice.constant.FileConstant;
import org.ruoyi.core.notice.domain.TgNoticeMain;
import org.ruoyi.core.notice.domain.TgNoticesFile;
import org.ruoyi.core.notice.domain.TgReadDownloadHistory;
import org.ruoyi.core.notice.domain.TgVersionRelation;
import org.ruoyi.core.notice.domain.vo.TgIndexNoticeVo;
import org.ruoyi.core.notice.mapper.TgNoticeMainMapper;
import org.ruoyi.core.notice.mapper.TgNoticesFileMapper;
import org.ruoyi.core.notice.mapper.TgVersionRelationMapper;
import org.ruoyi.core.notice.service.ITgNoticeMainService;
import org.ruoyi.core.notice.service.ITgReadRelationService;
import org.ruoyi.core.xmglproject.domain.AuthDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 通知公告Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-11
 */
@Service
public class TgNoticeMainServiceImpl implements ITgNoticeMainService
{
    @Autowired
    private TgNoticeMainMapper tgNoticeMainMapper;

    @Autowired
    private TgNoticesFileMapper tgNoticesFileMapper;

    @Autowired
    private TgVersionRelationMapper tgVersionRelationMapper;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityServiceImpl;

    @Autowired
    private ITgReadRelationService tgReadRelationService;
    /**
     * 查询通知公告详情
     * 
     * @param id 通知公告主键
     * @return 通知公告
     */
    @Override
    public TgNoticeMain selectTgNoticeMainById(Long id)
    {
        TgNoticeMain tgNoticeMain = tgNoticeMainMapper.selectTgNoticeMainById(id);
        if (!Objects.isNull(tgNoticeMain)){
            //获取附件
            List<TgNoticesFile> noticesFileList = tgNoticesFileMapper.selectTgNoticesFileByNoticeId(tgNoticeMain.getId(),FileConstant.RELATION_TYPE_NOTICE);
            tgNoticeMain.setNoticesFileList(noticesFileList);
        }
        return tgNoticeMain;
    }

    /**
     * 查询通知公告列表
     * 
     * @param tgNoticeMain 通知公告
     * @return 通知公告
     */
    @Override
    public List<TgNoticeMain> selectTgNoticeMainList(TgNoticeMain tgNoticeMain)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        //获取有权限的公司id
        List<Long> newAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(user.getUserId(), AuthModuleEnum.NOTICE.getCode());
        if (CollectionUtils.isEmpty(newAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType)){
            return new ArrayList<>();
        }
        tgNoticeMain.setCompanyIdList(newAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType);
        if (null != tgNoticeMain.getPageSize() && null != tgNoticeMain.getPageNum()){
            PageHelper.startPage(tgNoticeMain.getPageNum(),tgNoticeMain.getPageSize());
        }
        return tgNoticeMainMapper.selectTgNoticeMainList(tgNoticeMain);
    }

    /**
     * 新增通知公告
     * 
     * @param tgNoticeMain 通知公告
     * @return 结果
     */
    @Override
    public int insertTgNoticeMain(TgNoticeMain tgNoticeMain)
    {
        Long id = tgNoticeMain.getId();
        int i = 0;
        //id为空，是新增数据
        if (null == id){
            tgNoticeMain.setCreateBy(getUsername());
            tgNoticeMain.setCreateTime(DateUtils.getNowDate());
            i = tgNoticeMainMapper.insertTgNoticeMain(tgNoticeMain);
            //获取公告id
            Long mainId = tgNoticeMain.getId();

            //处理附件
            List<TgNoticesFile> noticesFileList = tgNoticeMain.getNoticesFileList();
            if (!CollectionUtils.isEmpty(noticesFileList)){
                for (TgNoticesFile tgNoticesFile : noticesFileList) {
                    tgNoticesFile.setRelationId(mainId);
                }
                //更新附件表关联id
                tgNoticesFileMapper.updateTgNoticesFileList(noticesFileList);
            }
        }
        return i;
    }

    /**
     * 修改通知公告
     * 
     * @param tgNoticeMain 通知公告
     * @return 结果
     */
    @Override
    public int updateTgNoticeMain(TgNoticeMain tgNoticeMain)
    {
        tgNoticeMain.setUpdateBy(getUsername());
        tgNoticeMain.setUpdateTime(DateUtils.getNowDate());
        //id不为空，是修改数据
        tgNoticeMain.setUpdateBy(getUsername());
        tgNoticeMain.setUpdateTime(DateUtils.getNowDate());

        /** 处理附件 */
        List<TgNoticesFile> noticesFileList = tgNoticeMain.getNoticesFileList();
        if (!CollectionUtils.isEmpty(noticesFileList)){
            Long mainId = tgNoticeMain.getId();
            //逻辑删除旧的附件信息
            tgNoticesFileMapper.deleteTgNoticesFileByRelationIdAndType(mainId, FileConstant.RELATION_TYPE_NOTICE);
            for (TgNoticesFile tgNoticesFile : noticesFileList) {
                tgNoticesFile.setRelationId(mainId);
            }
            //更新附件表关联id
            tgNoticesFileMapper.updateTgNoticesFileList(noticesFileList);
        }
        return tgNoticeMainMapper.updateTgNoticeMain(tgNoticeMain);
    }

    /**
     * 批量删除通知公告
     * 
     * @param ids 需要删除的通知公告主键
     * @return 结果
     */
    @Override
    public int deleteTgNoticeMainByIds(Long[] ids)
    {
        return tgNoticeMainMapper.deleteTgNoticeMainByIds(ids);
    }

    /**
     * 删除通知公告信息
     * 
     * @param id 通知公告主键
     * @return 结果
     */
    @Override
    public int deleteTgNoticeMainById(Long id)
    {
        TgNoticeMain noticeMain = this.selectTgNoticeMainById(id);
        if (noticeMain.getPublishStatus().equals("1")){
            throw new ServiceException("当前公告已发布，不允许删除，请撤回后重试！");
        }
        TgNoticeMain tgNoticeMain = new TgNoticeMain();
        tgNoticeMain.setId(id);
        tgNoticeMain.setUpdateBy(getUsername());
        tgNoticeMain.setUpdateTime(DateUtils.getNowDate());
        //将附件改为失效状态
        tgNoticesFileMapper.deleteTgNoticesFileByRelationIdAndType(id,"1");
        return tgNoticeMainMapper.deleteTgNoticeMainById(tgNoticeMain);
    }

    /**
     * 获取用户主岗的所属公司
     * @param userId
     * @return
     */
    @Override
    public SysCompany getUserHomePostCompany(String userId) {
        return tgNoticeMainMapper.getUserHomePostCompany(userId);
    }

    /**
     * 发布/撤回公告
     * @param tgNoticeMain
     * @return
     */
    @Override
    public int publishRevocationNoticeMain(TgNoticeMain tgNoticeMain) {
        tgNoticeMain.setUpdateBy(getUsername());
        tgNoticeMain.setUpdateTime(DateUtils.getNowDate());
        //发布
        if (null != tgNoticeMain.getPublishStatus() && !tgNoticeMain.getPublishStatus().equals("") && tgNoticeMain.getPublishStatus().equals("1")) {
            // 如果是发布通告，处理公告版本
            Integer version = tgNoticeMain.getVersion();
            // 如果版本号为空，则是第一次发布
            if (null == version) {
                TgVersionRelation tgVersionRelation = new TgVersionRelation();
                tgVersionRelation.setNoticeId(tgNoticeMain.getId());
                tgVersionRelation.setVersion(1);
                tgVersionRelation.setCreateBy(getUsername());
                tgVersionRelation.setCreateTime(DateUtils.getNowDate());
                tgVersionRelationMapper.insertTgVersionRelation(tgVersionRelation);
            } else {
                // 不为空，在原来版本基础上加1
                TgVersionRelation tgVersionRelation = new TgVersionRelation();
                tgVersionRelation.setNoticeId(tgNoticeMain.getId());
                tgVersionRelation.setVersion(version + 1);
                tgVersionRelation.setUpdateBy(getUsername());
                tgVersionRelation.setUpdateTime(DateUtils.getNowDate());
                tgVersionRelationMapper.updateTgVersionRelation(tgVersionRelation);
            }
            //获取公告所属公司
            Long publishCompanyId = tgNoticeMain.getPublishCompany();
            if (null == publishCompanyId){
                throw new ServiceException("发布人未配置主岗位或主岗位没有所属公司，请联系上级授权！");
            }
            //获取该公司下所有用户，并保存到公告已读/未读表中
            tgReadRelationService.batchSave(tgNoticeMain.getId(),publishCompanyId);
        //撤回
        } else if (null != tgNoticeMain.getPublishStatus() && !tgNoticeMain.getPublishStatus().equals("") && tgNoticeMain.getPublishStatus().equals("0")) {
            //撤回时取消置顶和重点
            tgNoticeMain.setIsHeader("0");
            tgNoticeMain.setIsEmphasis("0");
            //删除已读未读表数据
            tgReadRelationService.deleteTgReadRelationByNoticeId(tgNoticeMain.getId());
        }
        // 修改为发布/撤回状态
        return tgNoticeMainMapper.updateTgNoticeMain(tgNoticeMain);
    }

    /**
     * 查询首页面公告列表
     * @param tgNoticeMain
     * @return
     */
    @Override
    public List<TgIndexNoticeVo> selectIndexNoticeList(TgNoticeMain tgNoticeMain) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<TgIndexNoticeVo> tgIndexNoticeVoList = new ArrayList<>();
        //获取有权限的公司id
        List<Long> newAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(user.getUserId(), AuthModuleEnum.NOTICE.getCode());
        if (CollectionUtils.isEmpty(newAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType)){
            return new ArrayList<>();
        }
        tgNoticeMain.setCompanyIdList(newAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType);
        tgNoticeMain.setLoginUserId(user.getUserId());
        if (null != tgNoticeMain.getPageSize() && null != tgNoticeMain.getPageNum()){
            PageHelper.startPage(tgNoticeMain.getPageNum(),tgNoticeMain.getPageSize());
        }
        tgIndexNoticeVoList = tgNoticeMainMapper.selectIndexTgNoticeMainList(tgNoticeMain);
        return tgIndexNoticeVoList;
    }

    /**
     * 查询用户有权限的公司
     * @return
     */
    @Override
    public List<SysCompany> selectAuthCompanyListByUserId(Long userId) {
        List<SysCompany> companyList = new ArrayList<>();
        //获取有权限的公司id
        List<Long> newAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(userId, AuthModuleEnum.NOTICE.getCode());
        if (!CollectionUtils.isEmpty(newAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType)){
            companyList = tgNoticeMainMapper.selectCompanyListByCompanyIds(newAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType);
        }
        return companyList;
    }
}
