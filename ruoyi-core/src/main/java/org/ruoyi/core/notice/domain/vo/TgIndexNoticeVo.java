package org.ruoyi.core.notice.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 首页面公告列表VO
 */
@Data
public class TgIndexNoticeVo {

    /** 主键id */
    private Long id;

    /** 通知名称 */
    @Excel(name = "通知名称",width = 40, sort = 1)
    private String noticeName;

    /** 通知公告类型 */
    private Long noticeType;

    /** 通知公告类型字典 */
    @Excel(name = "类型", sort = 2)
    private String noticeTypeLabel;

    /** 状态(0未读 1已读) */
    private String readType;

    /** 状态label(0未读 1已读) */
    @Excel(name = "状态", sort = 3)
    private String readTypeLabel;

    /** 是否置顶(0否 1是) */
    private String isHeader;

    /** 是否重点(0否 1是) */
    private String isEmphasis;

    /** 创建者 */
    private String createBy;

    /** 创建人姓名 */
    @Excel(name = "创建人", sort = 4)
    private String createNickName;

    /** 创建时间 */
    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 所属公司名称 */
    private String companyShortName;
}
