package org.ruoyi.core.notice.mapper;

import org.ruoyi.core.notice.domain.TgVersionRelation;

import java.util.List;

public interface TgVersionRelationMapper {

    /**
     * 查询公告版本关联
     *
     * @param noticeId 公告版本关联主键
     * @return 公告版本关联
     */
    public TgVersionRelation selectTgVersionRelationByNoticeId(Long noticeId);

    /**
     * 查询公告版本关联列表
     *
     * @param tgVersionRelation 公告版本关联
     * @return 公告版本关联集合
     */
    public List<TgVersionRelation> selectTgVersionRelationList(TgVersionRelation tgVersionRelation);

    /**
     * 新增公告版本关联
     *
     * @param tgVersionRelation 公告版本关联
     * @return 结果
     */
    public int insertTgVersionRelation(TgVersionRelation tgVersionRelation);

    /**
     * 修改公告版本关联
     *
     * @param tgVersionRelation 公告版本关联
     * @return 结果
     */
    public int updateTgVersionRelation(TgVersionRelation tgVersionRelation);

    /**
     * 删除公告版本关联
     *
     * @param noticeId 公告版本关联主键
     * @return 结果
     */
    public int deleteTgVersionRelationByNoticeId(Long noticeId);

    /**
     * 批量删除公告版本关联
     *
     * @param noticeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTgVersionRelationByNoticeIds(Long[] noticeIds);
}
