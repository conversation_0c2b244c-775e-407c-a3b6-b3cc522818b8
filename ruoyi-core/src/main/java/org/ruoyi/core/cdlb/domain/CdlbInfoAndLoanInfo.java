package org.ruoyi.core.cdlb.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

public class CdlbInfoAndLoanInfo {

    /** 主键 */
    private Long id;

    /** 车贷绿本管理表主键 */
    @Excel(name = "车贷绿本管理表主键")
    private Long projectId;

    /** 车贷绿本管理-入库申请表主键 */
    @Excel(name = "车贷绿本管理-入库申请表主键")
    private Long projectInId;

    /** 车贷绿本管理-出库申请表主键 */
    @Excel(name = "车贷绿本管理-出库申请表主键")
    private Long projectOutId;

    /** 合同编号 */
    @Excel(name = "合同编号")
    private String contractCode;

    /** 客户姓名 */
    @Excel(name = "客户姓名")
    private String clientName;

    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String clientCardId;

    /** 是否绑定借据 Y是 N否 */
    @Excel(name = "是否绑定借据 Y是 N否")
    private String loanBinding;

    /** 已绑定的借据申请编号,未绑定时默认为N */
   /* @Excel(name = "已绑定的借据申请编号,未绑定时默认为N")
    private String loanNo;*/

    /** 邮寄日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "邮寄日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date mailDate;

    /** 绿本状态标识：10入库录入11入库申请12入库登记13入库完成20出库申请21出库审核22出库登记23出库完成 */
    @Excel(name = "绿本状态标识：10入库录入11入库申请12入库登记13入库完成20出库申请21出库审核22出库登记23出库完成")
    private String lbFlag;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;

    /** 是否绑定绿本信息 Y是 N否 */
    @Excel(name = "是否绑定绿本信息 Y是 N否")
    private String cdlbBinding;

    /** 已绑定的绿本信息表ID,未绑定时默认为0 */
    @Excel(name = "已绑定的绿本信息表ID,未绑定时默认为0")
    private Long cdlbId;

    /** 外部系统平台编码 */
    @Excel(name = "外部系统平台编码")
    private String platformNo;

    /** 担保公司编码 */
    @Excel(name = "担保公司编码")
    private String custNo;

    /** 合作方编码 */
    @Excel(name = "合作方编码")
    private String partnerNo;

    /** 资金方编码 */
    @Excel(name = "资金方编码")
    private String fundNo;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String productNo;

    /** 借据申请编号 */
    @Excel(name = "借据申请编号")
    private String loanNo;

    /** 客户姓名 */
   /* @Excel(name = "客户姓名")
    private String clientName;*/

    /** 身份证号码 */
/*    @Excel(name = "身份证号码")
    private String clientCardId;*/

    /** 身份证地址 */
    @Excel(name = "身份证地址")
    private String clientCardAddress;

    /** 借据状态 */
    @Excel(name = "借据状态")
    private String loanStatus;

    /** 进件时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "进件时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applyTime;

    /** 借款金额（元） */
    @Excel(name = "借款金额", readConverterExp = "元=")
    private BigDecimal loanAmt;

    /** 在贷余额（元） */
    @Excel(name = "在贷余额", readConverterExp = "元=")
    private BigDecimal balanceAmt;

    /** 借款期限（期数） */
    @Excel(name = "借款期限", readConverterExp = "期=数")
    private Integer totalTerm;

    /** 放款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "放款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loanTime;

    /** 到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dueDate;

    /** 放款流水号 */
    @Excel(name = "放款流水号")
    private String loanReqNo;

    /** 还款方式 */
    @Excel(name = "还款方式")
    private String repayWay;

    /** 借款用途 */
    @Excel(name = "借款用途")
    private String loanUse;

    /** 车辆品牌 */
    @Excel(name = "车辆品牌")
    private String carBrandName;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String carNo;

    /** 车架号 */
    @Excel(name = "车架号")
    private String carVin;

  /*  *//** 状态，0正常 1禁用 *//*
    @Excel(name = "状态，0正常 1禁用")
    private String status;*/

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 备注 */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getProjectInId() {
        return projectInId;
    }

    public void setProjectInId(Long projectInId) {
        this.projectInId = projectInId;
    }

    public Long getProjectOutId() {
        return projectOutId;
    }

    public void setProjectOutId(Long projectOutId) {
        this.projectOutId = projectOutId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientCardId() {
        return clientCardId;
    }

    public void setClientCardId(String clientCardId) {
        this.clientCardId = clientCardId;
    }

    public String getLoanBinding() {
        return loanBinding;
    }

    public void setLoanBinding(String loanBinding) {
        this.loanBinding = loanBinding;
    }

    public Date getMailDate() {
        return mailDate;
    }

    public void setMailDate(Date mailDate) {
        this.mailDate = mailDate;
    }

    public String getLbFlag() {
        return lbFlag;
    }

    public void setLbFlag(String lbFlag) {
        this.lbFlag = lbFlag;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCdlbBinding() {
        return cdlbBinding;
    }

    public void setCdlbBinding(String cdlbBinding) {
        this.cdlbBinding = cdlbBinding;
    }

    public Long getCdlbId() {
        return cdlbId;
    }

    public void setCdlbId(Long cdlbId) {
        this.cdlbId = cdlbId;
    }

    public String getPlatformNo() {
        return platformNo;
    }

    public void setPlatformNo(String platformNo) {
        this.platformNo = platformNo;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getPartnerNo() {
        return partnerNo;
    }

    public void setPartnerNo(String partnerNo) {
        this.partnerNo = partnerNo;
    }

    public String getFundNo() {
        return fundNo;
    }

    public void setFundNo(String fundNo) {
        this.fundNo = fundNo;
    }

    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getClientCardAddress() {
        return clientCardAddress;
    }

    public void setClientCardAddress(String clientCardAddress) {
        this.clientCardAddress = clientCardAddress;
    }

    public String getLoanStatus() {
        return loanStatus;
    }

    public void setLoanStatus(String loanStatus) {
        this.loanStatus = loanStatus;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public BigDecimal getBalanceAmt() {
        return balanceAmt;
    }

    public void setBalanceAmt(BigDecimal balanceAmt) {
        this.balanceAmt = balanceAmt;
    }

    public Integer getTotalTerm() {
        return totalTerm;
    }

    public void setTotalTerm(Integer totalTerm) {
        this.totalTerm = totalTerm;
    }

    public Date getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(Date loanTime) {
        this.loanTime = loanTime;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getRepayWay() {
        return repayWay;
    }

    public void setRepayWay(String repayWay) {
        this.repayWay = repayWay;
    }

    public String getLoanUse() {
        return loanUse;
    }

    public void setLoanUse(String loanUse) {
        this.loanUse = loanUse;
    }

    public String getCarBrandName() {
        return carBrandName;
    }

    public void setCarBrandName(String carBrandName) {
        this.carBrandName = carBrandName;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public String getCarVin() {
        return carVin;
    }

    public void setCarVin(String carVin) {
        this.carVin = carVin;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "CdlbInfoAndLoanInfo{" +
                "id=" + id +
                ", projectId=" + projectId +
                ", projectInId=" + projectInId +
                ", projectOutId=" + projectOutId +
                ", contractCode='" + contractCode + '\'' +
                ", clientName='" + clientName + '\'' +
                ", clientCardId='" + clientCardId + '\'' +
                ", loanBinding='" + loanBinding + '\'' +
                ", mailDate=" + mailDate +
                ", lbFlag='" + lbFlag + '\'' +
                ", status='" + status + '\'' +
                ", cdlbBinding='" + cdlbBinding + '\'' +
                ", cdlbId=" + cdlbId +
                ", platformNo='" + platformNo + '\'' +
                ", custNo='" + custNo + '\'' +
                ", partnerNo='" + partnerNo + '\'' +
                ", fundNo='" + fundNo + '\'' +
                ", productNo='" + productNo + '\'' +
                ", loanNo='" + loanNo + '\'' +
                ", clientCardAddress='" + clientCardAddress + '\'' +
                ", loanStatus='" + loanStatus + '\'' +
                ", applyTime=" + applyTime +
                ", loanAmt=" + loanAmt +
                ", balanceAmt=" + balanceAmt +
                ", totalTerm=" + totalTerm +
                ", loanTime=" + loanTime +
                ", dueDate=" + dueDate +
                ", loanReqNo='" + loanReqNo + '\'' +
                ", repayWay='" + repayWay + '\'' +
                ", loanUse='" + loanUse + '\'' +
                ", carBrandName='" + carBrandName + '\'' +
                ", carNo='" + carNo + '\'' +
                ", carVin='" + carVin + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", remark='" + remark + '\'' +
                '}';
    }
}
