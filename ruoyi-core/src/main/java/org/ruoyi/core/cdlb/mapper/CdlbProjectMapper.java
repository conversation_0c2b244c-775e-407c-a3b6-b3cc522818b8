package org.ruoyi.core.cdlb.mapper;


import org.ruoyi.core.cdlb.domain.CdlbProject;

import java.util.List;

/**
 * 车贷绿本管理主Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-02
 */
public interface CdlbProjectMapper 
{
    /**
     * 查询车贷绿本管理主
     * 
     * @param id 车贷绿本管理主主键
     * @return 车贷绿本管理主
     */
    public CdlbProject selectCdlbProjectById(Long id);

    /**
     * 查询车贷绿本管理主列表
     * 
     * @param cdlbProject 车贷绿本管理主
     * @return 车贷绿本管理主集合
     */
    public List<CdlbProject> selectCdlbProjectList(CdlbProject cdlbProject);

    /**
     * 新增车贷绿本管理主
     * 
     * @param cdlbProject 车贷绿本管理主
     * @return 结果
     */
    public int insertCdlbProject(CdlbProject cdlbProject);

    /**
     * 修改车贷绿本管理主
     * 
     * @param cdlbProject 车贷绿本管理主
     * @return 结果
     */
    public int updateCdlbProject(CdlbProject cdlbProject);

    /**
     * 删除车贷绿本管理主
     * 
     * @param id 车贷绿本管理主主键
     * @return 结果
     */
    public int deleteCdlbProjectById(Long id);

    /**
     * 批量删除车贷绿本管理主
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCdlbProjectByIds(Long[] ids);



}
