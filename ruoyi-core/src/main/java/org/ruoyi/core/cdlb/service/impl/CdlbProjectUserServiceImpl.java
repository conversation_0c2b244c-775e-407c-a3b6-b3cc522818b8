package org.ruoyi.core.cdlb.service.impl;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.cdlb.domain.CdlbProjectUser;
import org.ruoyi.core.cdlb.mapper.CdlbProjectUserMapper;
import org.ruoyi.core.cdlb.service.ICdlbProjectUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车贷绿本管理-成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-02
 */
@Service
public class CdlbProjectUserServiceImpl implements ICdlbProjectUserService
{
    @Autowired
    private CdlbProjectUserMapper cdlbProjectUserMapper;

    /**
     * 查询车贷绿本管理-成员
     * 
     * @param id 车贷绿本管理-成员主键
     * @return 车贷绿本管理-成员
     */
    @Override
    public CdlbProjectUser selectCdlbProjectUserById(Long id)
    {
        return cdlbProjectUserMapper.selectCdlbProjectUserById(id);
    }

    /**
     * 查询车贷绿本管理-成员列表
     * 
     * @param cdlbProjectUser 车贷绿本管理-成员
     * @return 车贷绿本管理-成员
     */
    @Override
    public List<CdlbProjectUser> selectCdlbProjectUserList(CdlbProjectUser cdlbProjectUser)
    {
        return cdlbProjectUserMapper.selectCdlbProjectUserList(cdlbProjectUser);
    }

    /**
     * 新增车贷绿本管理-成员
     * 
     * @param cdlbProjectUser 车贷绿本管理-成员
     * @return 结果
     */
    @Override
    public int insertCdlbProjectUser(CdlbProjectUser cdlbProjectUser)
    {
        cdlbProjectUser.setCreateTime(DateUtils.getNowDate());
        return cdlbProjectUserMapper.insertCdlbProjectUser(cdlbProjectUser);
    }

    /**
     * 修改车贷绿本管理-成员
     * 
     * @param cdlbProjectUser 车贷绿本管理-成员
     * @return 结果
     */
    @Override
    public int updateCdlbProjectUser(CdlbProjectUser cdlbProjectUser)
    {
        cdlbProjectUser.setUpdateTime(DateUtils.getNowDate());
        return cdlbProjectUserMapper.updateCdlbProjectUser(cdlbProjectUser);
    }

    /**
     * 批量删除车贷绿本管理-成员
     * 
     * @param ids 需要删除的车贷绿本管理-成员主键
     * @return 结果
     */
    @Override
    public int deleteCdlbProjectUserByIds(Long[] ids)
    {
        return cdlbProjectUserMapper.deleteCdlbProjectUserByIds(ids);
    }

    /**
     * 删除车贷绿本管理-成员信息
     * 
     * @param id 车贷绿本管理-成员主键
     * @return 结果
     */
    @Override
    public int deleteCdlbProjectUserById(Long id)
    {
        return cdlbProjectUserMapper.deleteCdlbProjectUserById(id);
    } @Override
    public int deleteCdlbProjectUserByProjectId(Long id)
    {
        return cdlbProjectUserMapper.deleteCdlbProjectUserByProjectId(id);
    }
}