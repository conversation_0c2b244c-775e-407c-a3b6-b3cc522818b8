package org.ruoyi.core.cdlb.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.cdlb.domain.CdlbInfoDynamic;
import org.ruoyi.core.cdlb.domain.FlowVo;
import org.ruoyi.core.cdlb.enums.CdStatusList;
import org.ruoyi.core.cdlb.service.ICdlbInfoDynamicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 车贷绿本状态-动态Controller
 * 
 * <AUTHOR>
 * @date 2023-03-20
 */
@RestController
@RequestMapping("/cdlb/infodynamic")
public class CdlbInfoDynamicController extends BaseController
{
    @Autowired
    private ICdlbInfoDynamicService cdlbInfoDynamicService;

    /**
     * 查询车贷绿本状态-动态列表
     */
   /* @PreAuthorize("@ss.hasPermi('system:dynamic:list')")*/
    @GetMapping("/list")
    public TableDataInfo list(CdlbInfoDynamic cdlbInfoDynamic)
    {
        startPage();

        List<CdlbInfoDynamic> list = cdlbInfoDynamicService.selectCdlbInfoDynamicList(cdlbInfoDynamic);
        return getDataTable(list);
    }
    /**
     * 车贷出库流程
     */

    @GetMapping("/listliuchengchu")
    public TableDataInfo listliuchengchu(CdlbInfoDynamic cdlbInfoDynamic) {

        List<CdlbInfoDynamic> list = cdlbInfoDynamicService.selectCdlbInfoDynamicList(cdlbInfoDynamic);
        Integer aaa = 0;//长度
        Integer symbocd = 8;//长度
        Integer dynamcd = 5;//长度
        Integer dynaecd = 6;//长度
        for (CdlbInfoDynamic projectDynamic : list) {
            String operName = projectDynamic.getOperName();//操作人名称
            int length = operName.length();
            if (length>aaa) {
                aaa = length;
            }
//            String symbolEnumInfo = CdStatusList.getSymbolEnumInfovo(projectDynamic.getApplyFlag());//操作状态
//            int length2 = symbolEnumInfo.length();
//            if (length2>symbocd) {
//                symbocd = length2;
//            }
//            if (StringUtils.isNotEmpty(projectDynamic.getDynamicTitle())){
//            String dy  = projectDynamic.getDynamicTitle();//操作人标题
//            int length3 = dy.length();
//            if (length3>dynamcd) {
//                dynamcd = length3;
//            }
//            }
//            if (StringUtils.isNotEmpty(projectDynamic.getDynamicRemarkTitle())) {
//                String dynamicRemarkTitle = projectDynamic.getDynamicRemarkTitle();//操作人备注标题
//                int length4 = dynamicRemarkTitle.length();
//                if (length4 > dynaecd) {
//                    dynaecd = length4;
//                }
//            }

        }
        Integer i = aaa * 16;
        Integer isymbocd = symbocd * 16;
        Integer idynamcd = (dynamcd+1) * 16;
        Integer idynaecd = (dynaecd+1) * 16;
        list = list.stream().sorted(Comparator.comparing(CdlbInfoDynamic::getId)).collect(Collectors.toList());
        List<FlowVo> flowVos = new ArrayList<>();
        String  applyFlagGao = "";
        String aasd =       " font-size: 14px;   color:#131313; ";
        for (CdlbInfoDynamic projectDynamic : list) {
            String applyFlag = projectDynamic.getApplyFlag();//状态
            applyFlagGao =applyFlag;
            String dynamicTitle = projectDynamic.getDynamicTitle();//操作人标题
            String operName = projectDynamic.getOperName();//操作人名称
            String dynamicTimeTitle = projectDynamic.getDynamicTimeTitle();//操作人时间标题
            Date dynamicTime = projectDynamic.getDynamicTime();//操作时间
            String dynamicRemarkTitle = projectDynamic.getDynamicRemarkTitle();//操作人备注标题
            String remark = projectDynamic.getRemark();//备注
            // String garageState = projectDynamic.getGarageState();//出入库状态
            String symbolEnumInfo = CdStatusList.getSymbolEnumInfovo(applyFlag);
            String ct="";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
            String format = simpleDateFormat.format(dynamicTime);
            if (StringUtils.isNotEmpty(dynamicRemarkTitle)){
                if (StringUtils.isEmpty(projectDynamic.getRemark())){
                    remark = "";
                }
                if (applyFlag.equals("19")||applyFlag.equals("29")||applyFlag.equals("28")){
                    ct = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+"<div style='    display: inline-block; width: "+idynaecd+"px;  text-align: right; font-size: 14px; color:#131313;'>"+dynamicRemarkTitle+"：</div>"+"<span  style='    font-size: 14px; font-weight:bold;  color:#ee0606; line-break: anywhere;  ' >"+remark+"</span>";
                }else {
                    ct = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+"<div style='    display: inline-block; width: "+idynaecd+"px;  text-align: right; font-size: 14px; color:#131313;'>"+dynamicRemarkTitle+"：</div>"+"<span style=' line-break: anywhere;  font-size: 14px; color:#131313;' >"+remark+"</span>";
                }

            }
            String content = "<div>"+"<div style='    display: inline-block; width: "+isymbocd+"px;'>"+symbolEnumInfo+"</div>"+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+
                    "<div style='    display: inline-block; width: "+idynamcd+"px; text-align: right; "+aasd+"'>"+dynamicTitle+"：</div>"+
                    "<div style='    display: inline-block; width: "+i+"px;  "+aasd+"'>"+operName+"</div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+"<span style='"+aasd+"'>"+dynamicTimeTitle+"：</span>"+"<span style='"+aasd+"'>"+format+"</span>"+ct+"</div>";
            FlowVo flowVo = new FlowVo();
            flowVo.setIcon("el-icon-map");
            flowVo.setContent(content);
            flowVos.add(flowVo);
        }
        for (FlowVo flowVo : flowVos) {
            String content = flowVo.getContent();
            String substring = content.substring(0, 18);
          //  String sl =  " style='   font-size: 14px; font-weight:bold;   color:#131313;  '";
            String sl =  "  font-size: 14px; font-weight:bold;   color:#131313; ";
            String substring1 = content.substring(18, content.length());
            flowVo.setContent(substring+sl+substring1);
        }

        if (applyFlagGao.equals("28")||applyFlagGao.equals("29")){
            FlowVo flowVo2 = new FlowVo();
            flowVo2.setIcon("el-icon-map");
            flowVo2.setContent("<div>"+jianjvc(isymbocd,"提交出库申请")+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+jianjvcb(idynamcd,"申请人")+"</div>");
            flowVos.add(flowVo2);
            FlowVo flowVo4 = new FlowVo();
            flowVo4.setIcon("el-icon-map");
            flowVo4.setContent("<div>"+jianjvc(isymbocd,"风控合规部审核")+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+jianjvcb(idynamcd,"审核人")+"</div>");
            flowVos.add(flowVo4);
            FlowVo flowVo = new FlowVo();
            flowVo.setIcon("el-icon-map");
            flowVo.setContent("<div>"+jianjvc(isymbocd,"出库登记")+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+jianjvcb(idynamcd,"出库登记人")+"</div>");
            flowVos.add(flowVo);
            FlowVo flowVo1 = new FlowVo();
            flowVo1.setIcon("el-icon-map");
            flowVo1.setContent("<div>出库完成</div>");
            flowVos.add(flowVo1);
        }
        if (applyFlagGao.equals("20")){
            FlowVo flowVo4 = new FlowVo();
            flowVo4.setIcon("el-icon-map");
            flowVo4.setContent("<div>"+jianjvc(isymbocd,"风控合规部审核")+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+jianjvcb(idynamcd,"审核人")+"</div>");
            flowVos.add(flowVo4);
            FlowVo flowVo = new FlowVo();
            flowVo.setIcon("el-icon-map");
            flowVo.setContent("<div>"+jianjvc(isymbocd,"出库登记")+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+jianjvcb(idynamcd,"出库登记人")+"</div>");
            flowVos.add(flowVo);
            FlowVo flowVo1 = new FlowVo();
            flowVo1.setIcon("el-icon-map");
            flowVo1.setContent("<div>出库完成</div>");
            flowVos.add(flowVo1);
        }
        if (applyFlagGao.equals("21")){

            FlowVo flowVo = new FlowVo();
            flowVo.setIcon("el-icon-map");
            flowVo.setContent("<div>"+jianjvc(isymbocd,"出库登记")+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+jianjvcb(idynamcd,"出库登记人")+"</div>");
            flowVos.add(flowVo);
            FlowVo flowVo1 = new FlowVo();
            flowVo1.setIcon("el-icon-map");
            flowVo1.setContent("<div>出库完成</div>");
            flowVos.add(flowVo1);
        }
        if (applyFlagGao.equals("22")){
            FlowVo flowVo1 = new FlowVo();
            flowVo1.setIcon("el-icon-map");
            flowVo1.setContent("<div style='   font-size: 14px; font-weight:bold;   color:#131313;  '  >出库完成</div>");
            flowVos.add(flowVo1);
        }
        return getDataTable(flowVos);
    }


    /**
     * 车贷入库流程
     */

    @GetMapping("/listliucheng")
    public TableDataInfo listliucheng(CdlbInfoDynamic cdlbInfoDynamic) {

        List<CdlbInfoDynamic> list = cdlbInfoDynamicService.selectCdlbInfoDynamicList(cdlbInfoDynamic);
        list = list.stream().sorted(Comparator.comparing(CdlbInfoDynamic::getId)).collect(Collectors.toList());
        Integer aaa = 0;//长度
        Integer symbocd = 6;//长度
        Integer dynamcd = 5;//长度
        Integer dynaecd = 6;//长度
        for (CdlbInfoDynamic projectDynamic : list) {
            String operName = projectDynamic.getOperName();//操作人名称
            int length = operName.length();
            if (length>aaa) {
                aaa = length;
            }
//            String symbolEnumInfo = CdStatusList.getSymbolEnumInfovo(projectDynamic.getApplyFlag());//操作状态
//            int length2 = symbolEnumInfo.length();
//            if (length2>symbocd) {
//                symbocd = length2;
//            }
//            if (StringUtils.isNotEmpty(projectDynamic.getDynamicTitle())){
//            String dy  = projectDynamic.getDynamicTitle();//操作人标题
//            int length3 = dy.length();
//            if (length3>dynamcd) {
//                dynamcd = length3;
//            }
//            }
//            if (StringUtils.isNotEmpty(projectDynamic.getDynamicRemarkTitle())) {
//                String dynamicRemarkTitle = projectDynamic.getDynamicRemarkTitle();//操作人备注标题
//                int length4 = dynamicRemarkTitle.length();
//                if (length4 > dynaecd) {
//                    dynaecd = length4;
//                }
//            }


        }
        Integer i = aaa * 16;
        Integer isymbocd = symbocd * 16;
        Integer idynamcd = (dynamcd+1) * 16;
        Integer idynaecd = (dynaecd+1) * 16;
        List<FlowVo> flowVos = new ArrayList<>();
        String  applyFlagGao = "";
        list = list.stream().sorted(Comparator.comparing(CdlbInfoDynamic::getId)).collect(Collectors.toList());
        String aasd =       " font-size: 14px;   color:#131313; ";
        for (CdlbInfoDynamic projectDynamic : list) {
            String applyFlag = projectDynamic.getApplyFlag();//状态
            applyFlagGao =applyFlag;
            String dynamicTitle = projectDynamic.getDynamicTitle();//操作人标题
            String operName = projectDynamic.getOperName();//操作人名称
            String dynamicTimeTitle = projectDynamic.getDynamicTimeTitle();//操作人时间标题
            Date dynamicTime = projectDynamic.getDynamicTime();//操作时间
            String dynamicRemarkTitle = projectDynamic.getDynamicRemarkTitle();//操作人备注标题
            String remark = projectDynamic.getRemark();//备注
            // String garageState = projectDynamic.getGarageState();//出入库状态
            String symbolEnumInfo = CdStatusList.getSymbolEnumInfovo(applyFlag);
            String ct="";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
            String format = simpleDateFormat.format(dynamicTime);
            if (StringUtils.isNotEmpty(dynamicRemarkTitle)){
                if (StringUtils.isEmpty(projectDynamic.getRemark())){
                    remark = "";
                }
                if (applyFlag.equals("19")||applyFlag.equals("29")||applyFlag.equals("28")){
                    ct = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+"<div style='    display: inline-block; width: "+idynaecd+"px;  text-align: right;        font-size: 14px;   color:#131313;'>"+dynamicRemarkTitle+"：</div>"+"<span  style='   font-size: 14px; font-weight:bold;   color:#ee0606; line-break: anywhere; ' >"+remark+"</span>";
                }else {
                    ct = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+"<div style='    display: inline-block; width: "+idynaecd+"px;  text-align: right;     font-size: 14px;   color:#131313;'>"+dynamicRemarkTitle+"：</div>"+"<span style=' line-break: anywhere;        font-size: 14px;   color:#131313;' >"+remark+"</span>";
                }

            }
            String content = "<div>"+"<div style='    display: inline-block; width: "+isymbocd+"px;'>"+symbolEnumInfo+"</div>"+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+"<div style='    display: inline-block; width: "+idynamcd+"px; text-align: right;   font-size: 14px;   color:#131313;'>"+dynamicTitle+"：</div>"+"<div style='    display: inline-block; width: "+i+"px;   font-size: 14px;   color:#131313;'>"+operName+"</div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+"<span  style='       font-size: 14px;   color:#131313;'>"+dynamicTimeTitle+"：</span >"+"<span style='       font-size: 14px;   color:#131313;'>"+format+"</span>"+ct+"</div>";
            FlowVo flowVo = new FlowVo();
            flowVo.setIcon("el-icon-map");
            flowVo.setContent(content);
            flowVos.add(flowVo);
        }
        for (FlowVo flowVo : flowVos) {
            String content = flowVo.getContent();
            String substring = content.substring(0, 18);
            //  String sl =  " style='   font-size: 14px; font-weight:bold;   color:#131313;  '";
            String sl =  "  font-size: 14px; font-weight:bold;   color:#131313; ";
            String substring1 = content.substring(18, content.length());
            flowVo.setContent(substring+sl+substring1);
        }
        if (applyFlagGao.equals("10")||applyFlagGao.equals("19")){
            FlowVo flowVo2 = new FlowVo();
            flowVo2.setIcon("el-icon-map");
            flowVo2.setContent("<div>"+"<div style='    display: inline-block; width: "+isymbocd+"px;'>"+"入库申请"+"</div>"+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+jianjvcb(idynamcd,"申请人")+"</div>");
            flowVos.add(flowVo2);
            FlowVo flowVo = new FlowVo();
            flowVo.setIcon("el-icon-map");
            flowVo.setContent("<div>"+"<div style='    display: inline-block; width: "+isymbocd+"px;'>"+"入库登记"+"</div>"+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+jianjvcb(idynamcd,"入库登记人")+"</div>");
            flowVos.add(flowVo);
            FlowVo flowVo1 = new FlowVo();
            flowVo1.setIcon("el-icon-map");
            flowVo1.setContent("<div>入库完成</div>");
            flowVos.add(flowVo1);
        }
        if (applyFlagGao.equals("11")){

            FlowVo flowVo = new FlowVo();
            flowVo.setIcon("el-icon-map");
            flowVo.setContent("<div>"+"<div style='    display: inline-block; width: "+isymbocd+"px;'>"+"入库登记"+"</div>"+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+jianjvcb(idynamcd,"入库登记人")+"</div>");
            flowVos.add(flowVo);
            FlowVo flowVo1 = new FlowVo();
            flowVo1.setIcon("el-icon-map");
            flowVo1.setContent("<div>入库完成</div>");
            flowVos.add(flowVo1);
        }
        if (applyFlagGao.equals("12")){
            FlowVo flowVo1 = new FlowVo();
            flowVo1.setIcon("el-icon-map");
            flowVo1.setContent("<div style='   font-size: 14px; font-weight:bold;   color:#131313;  '  >入库完成</div>");
            flowVos.add(flowVo1);
        }
        return getDataTable(flowVos);
    }

    public  String jianjvc(Integer s,String s1){
        String jj=  "<div style='    display: inline-block; width: "+s+"px;'>"+s1+"</div>";
        return jj;
    }public  String jianjvcb(Integer s,String s1){
    String jj= "<div style='    display: inline-block; width: "+s+"px; text-align: right;'>"+s1+"：</div>";
    return jj;
}   /**
     * 导出车贷绿本状态-动态列表
     */
    /*@PreAuthorize("@ss.hasPermi('system:dynamic:export')")*/
    @Log(title = "车贷绿本状态-动态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CdlbInfoDynamic cdlbInfoDynamic)
    {
        List<CdlbInfoDynamic> list = cdlbInfoDynamicService.selectCdlbInfoDynamicList(cdlbInfoDynamic);
        ExcelUtil<CdlbInfoDynamic> util = new ExcelUtil<CdlbInfoDynamic>(CdlbInfoDynamic.class);
        util.exportExcel(response, list, "车贷绿本状态-动态数据");
    }

    /**
     * 获取车贷绿本状态-动态详细信息
     */
   /* @PreAuthorize("@ss.hasPermi('system:dynamic:query')")*/
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cdlbInfoDynamicService.selectCdlbInfoDynamicById(id));
    }

    /**
     * 新增车贷绿本状态-动态
     */
   /* @PreAuthorize("@ss.hasPermi('system:dynamic:add')")*/
    @Log(title = "车贷绿本状态-动态", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CdlbInfoDynamic cdlbInfoDynamic)
    {
        return toAjax(cdlbInfoDynamicService.insertCdlbInfoDynamic(cdlbInfoDynamic));
    }

    /**
     * 修改车贷绿本状态-动态
     */
   /* @PreAuthorize("@ss.hasPermi('system:dynamic:edit')")*/
    @Log(title = "车贷绿本状态-动态", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CdlbInfoDynamic cdlbInfoDynamic)
    {
        return toAjax(cdlbInfoDynamicService.updateCdlbInfoDynamic(cdlbInfoDynamic));
    }

    /**
     * 删除车贷绿本状态-动态
     */
    /*@PreAuthorize("@ss.hasPermi('system:dynamic:remove')")*/
    @Log(title = "车贷绿本状态-动态", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cdlbInfoDynamicService.deleteCdlbInfoDynamicByIds(ids));
    }
}