package org.ruoyi.core.cdlb.domain;


import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 绿本文件关联对象 cdlb_files
 * 
 * <AUTHOR>
 * @date 2023-06-13
 */
public class CdlbFiles extends BaseEntity
{
    private static final long serialVersionUID = 1L;



    /** $column.columnComment */
    private Long id;

    /** 绿本id */
    @Excel(name = "绿本id")
    private Long cdlbInfoId;

    /** 姓名 */
    @Excel(name = "文件名")
    private String name;
    private String nameone;

    /** 图片地址 */
    @Excel(name = "文件地址")
    private String url;

    /** 类型 (男：1，女：2) */
    @Excel(name = "类型 (男：1，女：2)")
    private String type;

    /** 状态（0正常，1禁用） */
    @Excel(name = "状态", readConverterExp = "0=正常，1禁用")
    private String status;

    public String getNameone() {
        return nameone;
    }

    public void setNameone(String nameone) {
        this.nameone = nameone;
    }

    @Override
    public String toString() {
        return "CdlbFiles{" +
                "id=" + id +
                ", cdlbInfoId=" + cdlbInfoId +
                ", name='" + name + '\'' +
                ", url='" + url + '\'' +
                ", type='" + type + '\'' +
                ", status='" + status + '\'' +
                '}';
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCdlbInfoId() {
        return cdlbInfoId;
    }

    public void setCdlbInfoId(Long cdlbInfoId) {
        this.cdlbInfoId = cdlbInfoId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
