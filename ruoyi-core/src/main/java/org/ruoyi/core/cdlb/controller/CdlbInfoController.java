package org.ruoyi.core.cdlb.controller;


import com.alibaba.fastjson.JSON;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysDictType;
import com.ruoyi.common.core.domain.entity.SysDictTypeData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.AddlbVo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import com.ruoyi.system.service.ISysRoleService;
import org.apache.commons.collections.CollectionUtils;
import org.ruoyi.core.cdlb.domain.*;
import org.ruoyi.core.cdlb.enums.CdStatusList;
import org.ruoyi.core.cdlb.service.*;
import org.ruoyi.core.cdlb.utils.CdlbExcelUtils;
import org.ruoyi.core.cdlb.utils.GenerateOddNumbersUtil;
import org.ruoyi.core.cdlb.utils.IDUtils;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.ruoyi.core.cwproject.service.ITopNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 车贷绿本信息Controller
 *
 * <AUTHOR>
 * @date 2023-03-09
 */
@RestController
@RequestMapping("/cdlb/info")
public class CdlbInfoController extends BaseController {


    private final String guaranteeNameV = "担保公司";
    private final String cdlbxiangmujingli = "车贷绿本项目经理";
    private final String cdlbfengxianjingli = "车贷绿本风险经理";
    private final String cdlbbangongshizhuren = "车贷绿本办公室主任";

    @Value("${cdlb.fileUrl}")
    private String cdlbFileUrl;
    @Autowired
    private ICdlbFilesService cdlbFilesService;
    @Autowired
    private ITopNotifyService iTopNotifyService;

    @Autowired
    private ICdlbProjectUserService projectUserService;
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ICdlbInfoService cdlbInfoService;
    @Autowired
    private ISysRoleService sysRoleService;
    //出入库申请记录
    @Autowired
    private ICdlbInOutApplyService cdlbInOutApplyService;
    //车贷绿本动态表
    @Autowired
    private ICdlbProjectDynamicService cdlbProjectDynamicService;
    //绿本状态信息表
    @Autowired
    private ICdlbInfoDynamicService cdlbInfoDynamicService;


    @Autowired
    private ICdlbProjectService cdlbProjectService;
    @Autowired
    private ICdlbLoanInfoService cdlbLoanInfoService;

    /**
     * 查询担保公司列表，各种经理列表
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/cshlist")
    public AjaxResult huoqvchushihuashujv() {
        ParamVo paramVo = new ParamVo();
        SysDictType dictType = dictTypeService.selectDictNameByType(guaranteeNameV);
        if (dictType != null) {
            SysDictData dictData = new SysDictData();
            dictData.setDictType(dictType.getDictType());
            List<SysDictData> sysDictData = dictDataService.selectDictDataList(dictData);
            List<AddlbVo> objects = new ArrayList<>();
            for (SysDictData sysDictDatum : sysDictData) {
                AddlbVo addlbVo = new AddlbVo();
                String dictValue = sysDictDatum.getDictValue();
                addlbVo.setValue(dictValue);
                String dictLabel = sysDictDatum.getDictLabel();
                addlbVo.setLabel(dictLabel);
                objects.add(addlbVo);
            }
            //担保公司结果
            paramVo.setParam1(objects);
        }
        List<AddlbVo> sysUsers = sysRoleService.selectRolePermissionByroleName(cdlbxiangmujingli);
        Integer a = 0;
        for (AddlbVo sysUser : sysUsers) {
            if (sysUser == null) {
                a++;
            }
        }
        if (a == 0) {
            paramVo.setParam2(sysUsers);
        }
        List<AddlbVo> sysUsers1 = sysRoleService.selectRolePermissionByroleName(cdlbfengxianjingli);
        Integer a1 = 0;
        for (AddlbVo sysUser : sysUsers1) {
            if (sysUser == null) {
                a1++;
            }
        }
        if (a1 == 0) {
            paramVo.setParam3(sysUsers1);
        }
        List<AddlbVo> sysUsers2 = sysRoleService.selectRolePermissionByroleName(cdlbbangongshizhuren);
        Integer a2 = 0;
        for (AddlbVo sysUser : sysUsers2) {
            if (sysUser == null) {
                a2++;
            }
        }
        if (a2 == 0) {
            paramVo.setParam4(sysUsers2);
        }
        return AjaxResult.success(paramVo);
    }

    /**
     * 新增车贷绿本信息     360222200001163512
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "车贷绿本信息", businessType = BusinessType.INSERT)
    @PostMapping("/addllvbentianjia")
    @Transactional
    public AjaxResult addllvbentianjia(@RequestBody Param param) {
        Integer a = 0;

        for (tableData tableDatum : param.tableData) {
            a++;
            if (StringUtils.isEmpty(tableDatum.getClientCardId())) {
                return AjaxResult.error("序号为:" + a + "身份证号码不能为空");
            }
            if ((false == IDUtils.isIDNumber(tableDatum.getClientCardId()))) {
                return AjaxResult.error("序号为:" + a + "请输入正确的身份证号码");
            }
            if (StringUtils.isEmpty(tableDatum.getClientName())) {
                return AjaxResult.error("序号为:" + a + "客户名称不能为空");
            }
//            if (false == zhongwen(tableDatum.getClientName())) {
//                return AjaxResult.error("序号为:" + a + "客户名称为空");
//            }

            if (tableDatum.getMailDate() == null) {
                return AjaxResult.error("序号为:" + a + "邮寄日期不能为空");
            }
        }
        //出入库记录
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setProjectId(param.getId());
        cdlbInOutApply.setGarageState("02");//入库
        if (StringUtils.isNotEmpty(param.getRkremark()) && param.getRkremark().length() > 500) {
            return AjaxResult.error("申请说明最高为500字，请重新输入");
        }
        /*  cdlbInOutApply.setRemark(param.getRkremark());*/
        cdlbInOutApply.setStatus("0");
        int size = param.tableData.size();
        cdlbInOutApply.setCounts(Long.valueOf(size));
        /*出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回 20出库申请21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInOutApply.setApplyFlag(CdStatusList.LOGGING.getCode());
        cdlbInOutApply.setCreateBy(getLoginUser().getUsername());
        cdlbInOutApply.setUser10Id(getLoginUser().getUserId().toString());

        CdlbProject cdlbProject1 = cdlbProjectService.selectCdlbProjectById(param.getId());
        if (cdlbProject1 != null) {
            //项目经理  申请人           风险经理   审核人           办公室主任  登记人
            //项目经理  申请人
            cdlbInOutApply.setUser11Id(cdlbProject1.getPmIDs()); //入库
            cdlbInOutApply.setUser21Id(cdlbProject1.getPmIDs()); //出库
            //办公室主任  登记人
            cdlbInOutApply.setUser12Id(cdlbProject1.getDmIDs()); //入库
            cdlbInOutApply.setUser23Id(cdlbProject1.getDmIDs());//出库
            // 风险经理   审核人
            cdlbInOutApply.setUser22Id(cdlbProject1.getRmIDs());//出库


        }
        cdlbInOutApply.setUser10Time(new Date());
        cdlbInOutApply.setCreateTime(new Date());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApplyService.insertCdlbInOutApply(cdlbInOutApply);
        for (tableData tableDatum : param.tableData) {
            //绿本添加
            CdlbInfo cdlbInfo = new CdlbInfo();
            cdlbInfo.setProjectId(param.getId());
            cdlbInfo.setProjectInId(cdlbInOutApply.getId());
            cdlbInfo.setContractCode(tableDatum.getContractCode());
            cdlbInfo.setClientName(tableDatum.getClientName());
            cdlbInfo.setMailDate(tableDatum.getMailDate());
            cdlbInfo.setClientCardId(tableDatum.getClientCardId());
            cdlbInfo.setLbFlag(CdStatusList.LOGGING.getCode());
            cdlbInfo.setStatus("0");
            cdlbInfo.setCreateBy(getUsername());
            cdlbInfo.setCreateTime(new Date());
            cdlbInfo.setUpdateTime(new Date());
            cdlbInfoService.insertCdlbInfo(cdlbInfo);
            //绿本状态信息表     出入库状态  01出库，02入库
            setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "录入人", "录入日期", null, null, CdStatusList.LOGGING.getCode());
        }
        //车贷信息表动态流程
        setcdlbInfoDynamicBA(param.getId(), param.getRkremark(), CdStatusList.LOGGING.getCode(), "录入人", "录入日期", null, cdlbInOutApply.getId(), "02");
        return AjaxResult.success(cdlbInOutApply.getId());
    }

    /**
     * 新增车贷绿本信息     360222200001163512
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "车贷绿本信息关联申请", businessType = BusinessType.INSERT)
    @PostMapping("/addllvbentianjiasq")
    @Transactional
    public AjaxResult addllvbentianjiasq(@RequestBody Param param) {
        Integer a = 0;

        for (tableData tableDatum : param.tableData) {
            a++;
            if (StringUtils.isEmpty(tableDatum.getClientCardId())) {
                return AjaxResult.error("序号为:" + a + "身份证号码不能为空");
            }
            if ((false == IDUtils.isIDNumber(tableDatum.getClientCardId()))) {
                return AjaxResult.error("序号为:" + a + "请输入正确的身份证号码");
            }
            if (StringUtils.isEmpty(tableDatum.getClientName())) {
                return AjaxResult.error("序号为:" + a + "客户名称不能为空");
            }
//            if (false == zhongwen(tableDatum.getClientName())) {
//                return AjaxResult.error("序号为:" + a + "客户名称请输入2-20位汉字");
//            }

            if (tableDatum.getMailDate() == null) {
                return AjaxResult.error("序号为:" + a + "邮寄日期不能为空");
            }
        }
        //出入库记录

        CdlbInOutApply cdlbInOutApply = cdlbInOutApplyService.selectCdlbInOutApplyById(param.getId());
        Long projectId = cdlbInOutApply.getProjectId();
        String applyFlag = cdlbInOutApply.getApplyFlag();

        CdlbInOutApply cdlbInOutApply1 = new CdlbInOutApply();
        cdlbInOutApply1.setId(param.getId());
        long l = cdlbInOutApply.getCounts() + param.tableData.size();
        cdlbInOutApply1.setCounts(l);
        cdlbInOutApply1.setUpdateTime(new Date());
        cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply1);
        for (tableData tableDatum : param.tableData) {
            //绿本添加
            CdlbInfo cdlbInfo = new CdlbInfo();
            cdlbInfo.setProjectId(projectId);
            cdlbInfo.setProjectInId(param.getId());
            cdlbInfo.setContractCode(tableDatum.getContractCode());
            cdlbInfo.setClientName(tableDatum.getClientName());
            cdlbInfo.setMailDate(tableDatum.getMailDate());
            cdlbInfo.setClientCardId(tableDatum.getClientCardId());
            cdlbInfo.setLbFlag(applyFlag);
            cdlbInfo.setStatus("0");
            cdlbInfo.setCreateBy(getUsername());
            cdlbInfo.setCreateTime(new Date());
            cdlbInfo.setUpdateTime(new Date());
            cdlbInfoService.insertCdlbInfo(cdlbInfo);
            //绿本状态信息表     出入库状态  01出库，02入库
            setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "录入人", "录入日期", null, null, applyFlag);
        }
        return AjaxResult.success();
    }

    /**
     * 导入
     * 项目下的绿本
     *
     * @param file 导入文件
     * @throws Exception
     */
    @Log(title = "车贷绿本数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @Transactional
    public AjaxResult importData(MultipartFile file, Long updateId) throws Exception {
        ExcelUtil<CdlbInfo> util = new ExcelUtil<CdlbInfo>(CdlbInfo.class);
        List<CdlbInfo> mappingsList = util.importExcelcdlb(file.getInputStream());
        int size = mappingsList.size();
        Integer a = 0;
        for (CdlbInfo tableDatum : mappingsList) {
            a++;
            if (tableDatum == null) {
                return AjaxResult.error("上传失败");
            }

            if (StringUtils.isEmpty(tableDatum.getClientCardId())) {
                return AjaxResult.error("第（" + a + "）条数据身份证号码不能为空");
            }
            if ((false == IDUtils.isIDNumber(tableDatum.getClientCardId()))) {
                return AjaxResult.error("第（" + a + "）条数据请输入正确的身份证号码");
            }
            if (StringUtils.isEmpty(tableDatum.getClientName())) {
                return AjaxResult.error("第（" + a + "）条数据客户姓名不能为空");
            }
//            if (false == zhongwen(tableDatum.getClientName())) {
//                return AjaxResult.error("第（" + a + "）条数据客户姓名请输入2-20位汉字");
//            }

            if (tableDatum.getMailDate() == null) {
                return AjaxResult.error("第（" + a + "）条数据邮寄日期不能为空");
            }
        }


        //出入库记录
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setProjectId(updateId);
        cdlbInOutApply.setGarageState("02");//入库
        cdlbInOutApply.setUser10Id(getLoginUser().getUserId().toString());
        cdlbInOutApply.setUser10Time(new Date());

        CdlbProject cdlbProject1 = cdlbProjectService.selectCdlbProjectById(updateId);
        if (cdlbProject1 != null) {
            //项目经理  申请人           风险经理   审核人           办公室主任  登记人
            //项目经理  申请人
            cdlbInOutApply.setUser11Id(cdlbProject1.getPmIDs()); //入库
            cdlbInOutApply.setUser21Id(cdlbProject1.getPmIDs()); //出库
            //办公室主任  登记人
            cdlbInOutApply.setUser12Id(cdlbProject1.getDmIDs()); //入库
            cdlbInOutApply.setUser23Id(cdlbProject1.getDmIDs());//出库
            // 风险经理   审核人
            cdlbInOutApply.setUser22Id(cdlbProject1.getRmIDs());//出库


        }


        // cdlbInOutApply.setRemark(param.getRkremark());
        cdlbInOutApply.setStatus("0");
        cdlbInOutApply.setCounts(Long.valueOf(size));
        /*出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回 20出库申请21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInOutApply.setApplyFlag(CdStatusList.LOGGING.getCode());
        cdlbInOutApply.setCreateBy(getLoginUser().getUsername());
        cdlbInOutApply.setCreateTime(new Date());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApplyService.insertCdlbInOutApply(cdlbInOutApply);
        for (CdlbInfo tableDatum : mappingsList) {
            //绿本添加
            CdlbInfo cdlbInfo = new CdlbInfo();
            cdlbInfo.setProjectId(updateId);
            cdlbInfo.setProjectInId(cdlbInOutApply.getId());  //添加入库主键
            cdlbInfo.setContractCode(tableDatum.getContractCode());
            cdlbInfo.setClientName(tableDatum.getClientName());
            cdlbInfo.setMailDate(tableDatum.getMailDate());
            cdlbInfo.setClientCardId(tableDatum.getClientCardId());
            cdlbInfo.setLbFlag(CdStatusList.LOGGING.getCode());
            cdlbInfo.setStatus("0");
            cdlbInfo.setCreateBy(getUsername());
            cdlbInfo.setCreateTime(new Date());
            cdlbInfo.setUpdateTime(new Date());
            cdlbInfoService.insertCdlbInfo(cdlbInfo);

            //绿本状态信息表     出入库状态  01出库，02入库
            setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "录入人", "录入日期", null, null, CdStatusList.LOGGING.getCode());

        }
        //车贷信息表动态流程
        setcdlbInfoDynamicBA(updateId, null, CdStatusList.LOGGING.getCode(), "录入人", "录入日期", null, cdlbInOutApply.getId(), "02");
        return AjaxResult.success(size+"_"+cdlbInOutApply.getId());
    }

    /**
     * 绿本提交出库申请
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "绿本提交出库申请", businessType = BusinessType.INSERT)
    @PostMapping("/outWarehouse")
    @Transactional
    public AjaxResult outWarehouse(@RequestBody Param param) {
        Integer a = 0;
        //出入库记录
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setProjectId(param.getId());
        cdlbInOutApply.setGarageState("01");//（01出库，02入库）
        cdlbInOutApply.setWarehouseRemark(param.getRkremark());
        cdlbInOutApply.setRemark(param.getRkremark());
        CdlbProject cdlbProject1 = cdlbProjectService.selectCdlbProjectById(param.getId());
        if (cdlbProject1 != null) {
            //项目经理  申请人           风险经理   审核人           办公室主任  登记人
            //项目经理  申请人
            cdlbInOutApply.setUser11Id(cdlbProject1.getPmIDs()); //入库
            cdlbInOutApply.setUser21Id(cdlbProject1.getPmIDs()); //出库
            //办公室主任  登记人
            cdlbInOutApply.setUser12Id(cdlbProject1.getDmIDs()); //入库
            cdlbInOutApply.setUser23Id(cdlbProject1.getDmIDs());//出库
            // 风险经理   审核人
            cdlbInOutApply.setUser22Id(cdlbProject1.getRmIDs());//出库


        }
        cdlbInOutApply.setUser21Time(new Date());
        /* cdlbInOutApply.setRemark(param.getRkremark());*/
        cdlbInOutApply.setStatus("0");
        cdlbInOutApply.setCounts(param.counts);
        /*出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回                   20出库申请                        21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInOutApply.setApplyFlag(CdStatusList.GOWAREHOUSE.getCode());
        cdlbInOutApply.setCreateBy(getLoginUser().getUsername());
        cdlbInOutApply.setCreateTime(new Date());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApplyService.insertCdlbInOutApply(cdlbInOutApply);

        //添加通知
        CdlbInOutApply cdlbInOutApply1 = cdlbInOutApplyService.selectCdlbInOutApplyById(cdlbInOutApply.getId());
        if (cdlbInOutApply1 != null && StringUtils.isNotEmpty(cdlbInOutApply1.getUser22Id())) {
            String user22Id = cdlbInOutApply1.getUser22Id();
            int i = user22Id.indexOf(",");
            if (i != -1) {
                String[] split = user22Id.split(",");
                for (int j = 0; j < split.length; j++) {
                    insertNotify(cdlbInOutApply1.getId(), CdStatusList.GOWAREHOUSE.getCode(), Long.valueOf(split[j]), "车抵贷绿本出入库-出库申请待审核", "/cdlb/cdlbdsh");

                }
            }
            if (i == -1) {
                insertNotify(cdlbInOutApply1.getId(), CdStatusList.GOWAREHOUSE.getCode(), Long.valueOf(user22Id), "车抵贷绿本出入库-出库申请待审核", "/cdlb/cdlbdsh");
            }
        }

        for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {
            //修改借据状态为以出库
            CdlbLoanInfo cdlbLoanInfo = new CdlbLoanInfo();
            cdlbLoanInfo.setId(cdlbInfo1.getLoanId());
            cdlbLoanInfo.setCdlbRecord("Y");
            cdlbLoanInfoService.updateCdlbLoanInfo(cdlbLoanInfo);

            //修改绿本
            CdlbInfo cdlbInfo = new CdlbInfo();
            /* cdlbInfo.sdfg*/
            cdlbInfo.setId(cdlbInfo1.getId());
            cdlbInfo.setProjectOutId(cdlbInOutApply.getId());
            cdlbInfo.setLbFlag(CdStatusList.GOWAREHOUSE.getCode());
            if (cdlbInfo1.getLoanNo() != null) {
                cdlbInfo.setCdlbBinding("Y"); //是否绑定借据 Y是 N否
                /*cdlbInfo.setLoanNo(cdlbInfo1.getLoanNo());*/
                cdlbInfo.setLoanNo(cdlbInfo1.getLoanId().toString());
            }
            //添加子状态
            cdlbInfo.setChildFlag("01");
            cdlbInfo.setTemporaryFlag("01");
            cdlbInfoService.updateCdlbInfo(cdlbInfo);

            //绿本状态信息表     出入库状态  01出库，02入库

            setcdlbInfoDynamicAA(cdlbInfo.getId(), "01", "申请人", "申请日期", "出库申请说明", param.getRkremark(), CdStatusList.GOWAREHOUSE.getCode());
        }
        //车贷信息表动态流程
        setcdlbInfoDynamicBA(param.getId(), param.getRkremark(), CdStatusList.GOWAREHOUSE.getCode(), "申请人", "申请日期", "出库申请说明", cdlbInOutApply.getId(), "01");

        return AjaxResult.success();
    }

    /**
     * 绿本提交出库审核驳回
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "出库重新提交", businessType = BusinessType.INSERT)
    @PostMapping("/outWarehousechongxin")
    @Transactional
    public AjaxResult outWarehousechongxin(@RequestBody Param param) {
        Integer a = 0;
        //出入库记录
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setId(param.getId());
        cdlbInOutApply.setUser21Time(new Date());
        cdlbInOutApply.setWarehouseRemark(param.getRkremark());

        cdlbInOutApply.setCounts(param.counts);
        /*出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回                   20出库申请                        21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInOutApply.setApplyFlag(CdStatusList.GOWAREHOUSE.getCode());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);

        //添加通知
        CdlbInOutApply cdlbInOutApply1 = cdlbInOutApplyService.selectCdlbInOutApplyById(param.getId());
        if (cdlbInOutApply1 != null && StringUtils.isNotEmpty(cdlbInOutApply1.getUser22Id())) {
            String user22Id = cdlbInOutApply1.getUser22Id();
            int i = user22Id.indexOf(",");
            if (i != -1) {
                String[] split = user22Id.split(",");
                for (int j = 0; j < split.length; j++) {
                    insertNotify(cdlbInOutApply1.getId(), CdStatusList.GOWAREHOUSE.getCode(), Long.valueOf(split[j]), "车抵贷绿本出入库-出库申请待审核", "/cdlb/cdlbdsh");

                }
            }
            if (i == -1) {
                insertNotify(cdlbInOutApply1.getId(), CdStatusList.GOWAREHOUSE.getCode(), Long.valueOf(user22Id), "车抵贷绿本出入库-出库申请待审核", "/cdlb/cdlbdsh");
            }
        }

        Long projectId = null;
        for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {
            projectId = cdlbInfo1.getProjectId();
            //修改借据状态为以出库
/*
            CdlbLoanInfo cdlbLoanInfo = new CdlbLoanInfo();
            cdlbLoanInfo.setId(cdlbInfo1.getLoanId());
            cdlbLoanInfo.setCdlbRecord("Y");
            cdlbLoanInfoService.updateCdlbLoanInfo(cdlbLoanInfo);
*/
            //修改绿本
            CdlbInfo cdlbInfo = new CdlbInfo();
            /* cdlbInfo.sdfg*/
            cdlbInfo.setId(cdlbInfo1.getId());
            cdlbInfo.setLbFlag(CdStatusList.GOWAREHOUSE.getCode());
            //添加子状态
            cdlbInfo.setChildFlag("01");
            cdlbInfo.setTemporaryFlag("01");
            cdlbInfoService.updateCdlbInfo(cdlbInfo);
            //绿本状态信息表     出入库状态  01出库，02入库
            setcdlbInfoDynamicAA(cdlbInfo.getId(), "01", "申请人", "申请日期", "出库申请说明", param.getRkremark(), CdStatusList.GOWAREHOUSE.getCode());
        }

        //车贷信息表动态流程
        setcdlbInfoDynamicBA(projectId, param.getRkremark(), CdStatusList.GOWAREHOUSE.getCode(), "申请人", "申请日期", "出库申请说明", cdlbInOutApply.getId(), "01");

        return AjaxResult.success();
    }

    /**
     * 绿本提交出库审核驳回
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "绿本提交出库审核通过", businessType = BusinessType.INSERT)
    @PostMapping("/outshenHetongguo")
    @Transactional
    public AjaxResult outshenHetongguo(@RequestBody Param param) {
        Integer a = 0;
        //出入库记录
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setId(param.getId());
        cdlbInOutApply.setUser22Time(new Date());
        /*  cdlbInOutApply.setWarehouseRemark(param.getRkremark());*/

        cdlbInOutApply.setCounts(param.counts);
        /*出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回                   20出库申请                        21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInOutApply.setApplyFlag(CdStatusList.GOWAREHOUSEAPPLY.getCode());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);


        //添加通知
        CdlbInOutApply cdlbInOutApply1 = cdlbInOutApplyService.selectCdlbInOutApplyById(param.getId());
        if (cdlbInOutApply1 != null && StringUtils.isNotEmpty(cdlbInOutApply1.getUser23Id())) {
            String user23Id = cdlbInOutApply1.getUser23Id();
            int i = user23Id.indexOf(",");
            if (i != -1) {
                String[] split = user23Id.split(",");
                for (int j = 0; j < split.length; j++) {
                    insertNotify(cdlbInOutApply1.getId(), CdStatusList.GOWAREHOUSEAPPLY.getCode(), Long.valueOf(split[j]), "车抵贷绿本出入库-出库申请待登记", "/cdlb/cdlbdck");

                }
            }
            if (i == -1) {
                insertNotify(cdlbInOutApply1.getId(), CdStatusList.GOWAREHOUSEAPPLY.getCode(), Long.valueOf(user23Id), "车抵贷绿本出入库-出库申请待登记", "/cdlb/cdlbdck");
            }

        }
        Long projectId = null;
        for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {
            projectId = cdlbInfo1.getProjectId();
            //修改借据状态为以出库
/*
            CdlbLoanInfo cdlbLoanInfo = new CdlbLoanInfo();
            cdlbLoanInfo.setId(cdlbInfo1.getLoanId());
            cdlbLoanInfo.setCdlbRecord("Y");
            cdlbLoanInfoService.updateCdlbLoanInfo(cdlbLoanInfo);
*/
            //修改绿本
            CdlbInfo cdlbInfo = new CdlbInfo();
            /* cdlbInfo.sdfg*/
            cdlbInfo.setId(cdlbInfo1.getId());
            cdlbInfo.setLbFlag(CdStatusList.GOWAREHOUSEAPPLY.getCode());

            cdlbInfo.setChildFlag("01");
            cdlbInfo.setTemporaryFlag("01");
            cdlbInfoService.updateCdlbInfo(cdlbInfo);
            //绿本状态信息表     出入库状态  01出库，02入库
            setcdlbInfoDynamicAA(cdlbInfo.getId(), "01", "审核人", "审核日期", "审核意见", param.getRkremark(), CdStatusList.GOWAREHOUSEAPPLY.getCode());
        }

        //车贷信息表动态流程
        setcdlbInfoDynamicBA(projectId, param.getRkremark(), CdStatusList.GOWAREHOUSEAPPLY.getCode(), "审核人", "审核日期", "审核意见", cdlbInOutApply.getId(), "01");
        //代办通知事项  关闭
        CdlbInOutApply cdlbInOutApply2 = new CdlbInOutApply();
        cdlbInOutApply2.setId(cdlbInOutApply.getId());  //申请id
        cdlbInOutApply2.setApplyFlag(CdStatusList.GOWAREHOUSE.getCode()); // 申请状态
        updatetn(cdlbInOutApply2);

        return AjaxResult.success();
    }

    /**
     * 绿本提交出库审核驳回
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "绿本提交出库成功", businessType = BusinessType.INSERT)
    @PostMapping("/outok")
    @Transactional
    public AjaxResult outok(@RequestBody Param param) {
        Integer a = 0;
        //出入库记录
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setId(param.getId());
        cdlbInOutApply.setUser23Time(new Date());
        /*  cdlbInOutApply.setWarehouseRemark(param.getRkremark());*/

        cdlbInOutApply.setCounts(param.counts);
        /*出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回                   20出库申请                        21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInOutApply.setApplyFlag(CdStatusList.GOWAREHOUSEREGISTER.getCode());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);
        Long projectId = null;
        for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {
            projectId = cdlbInfo1.getProjectId();
            //修改借据状态为以出库
/*
            CdlbLoanInfo cdlbLoanInfo = new CdlbLoanInfo();
            cdlbLoanInfo.setId(cdlbInfo1.getLoanId());
            cdlbLoanInfo.setCdlbRecord("Y");
            cdlbLoanInfoService.updateCdlbLoanInfo(cdlbLoanInfo);
*/
            //修改绿本
            CdlbInfo cdlbInfo = new CdlbInfo();
            /* cdlbInfo.sdfg*/
            cdlbInfo.setId(cdlbInfo1.getId());
            cdlbInfo.setLbFlag(CdStatusList.GOWAREHOUSEREGISTER.getCode());
            cdlbInfoService.updateCdlbInfo(cdlbInfo);
            //绿本状态信息表     出入库状态  01出库，02入库
            setcdlbInfoDynamicAA(cdlbInfo.getId(), "01", "出库登记人", "登记日期", "出库办理说明", param.getRkremark(), CdStatusList.GOWAREHOUSEREGISTER.getCode());
        }

        //车贷信息表动态流程
        setcdlbInfoDynamicBA(projectId, param.getRkremark(), CdStatusList.GOWAREHOUSEREGISTER.getCode(), "出库登记人", "登记日期", "出库办理说明", cdlbInOutApply.getId(), "01");
        //代办通知事项  关闭
        CdlbInOutApply cdlbInOutApply1 = new CdlbInOutApply();
        cdlbInOutApply1.setId(cdlbInOutApply.getId());  //申请id
        cdlbInOutApply1.setApplyFlag(CdStatusList.GOWAREHOUSEAPPLY.getCode()); // 申请状态
        updatetn(cdlbInOutApply1);
        return AjaxResult.success();
    }

    /**
     * 绿本提交出库审核驳回
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "绿本提交出库审核驳回", businessType = BusinessType.INSERT)
    @PostMapping("/outshenHeBoHui")
    @Transactional
    public AjaxResult outshenHeBoHui(@RequestBody Param param) {
        Integer a = 0;
        //出入库记录
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setId(param.getId());
        /*    cdlbInOutApply.setUser22Time(new Date());*/


        cdlbInOutApply.setCounts(param.counts);
        /*出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回                   20出库申请                        21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInOutApply.setApplyFlag(CdStatusList.CGOBACK.getCode());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);

        //出库申请被驳回    添加通知
        CdlbProjectDynamic cdlbProjectDynamic = new CdlbProjectDynamic();
        cdlbProjectDynamic.setApplyId(param.getId());
        cdlbProjectDynamic.setApplyFlag(CdStatusList.GOWAREHOUSE.getCode());
        CdlbProjectDynamic projectDynamic = cdlbProjectDynamicService.selectlimit1(cdlbProjectDynamic);
        insertNotify(param.getId(), CdStatusList.CGOBACK.getCode(), Long.valueOf(projectDynamic.getOperId()), "车抵贷绿本出入库-出库申请被驳回", "/cdlb/cdlblbcksq");


        Long projectId = null;
        for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {
            projectId = cdlbInfo1.getProjectId();
            //修改借据状态为以出库
/*
            CdlbLoanInfo cdlbLoanInfo = new CdlbLoanInfo();
            cdlbLoanInfo.setId(cdlbInfo1.getLoanId());
            cdlbLoanInfo.setCdlbRecord("Y");
            cdlbLoanInfoService.updateCdlbLoanInfo(cdlbLoanInfo);
*/
            //修改绿本
            CdlbInfo cdlbInfo = new CdlbInfo();
            /* cdlbInfo.sdfg*/
            cdlbInfo.setId(cdlbInfo1.getId());
            cdlbInfo.setLbFlag(CdStatusList.CGOBACK.getCode());

            cdlbInfo.setChildFlag("01");
            cdlbInfo.setTemporaryFlag("01");
            cdlbInfoService.updateCdlbInfo(cdlbInfo);
            //绿本状态信息表     出入库状态  01出库，02入库
            setcdlbInfoDynamicAA(cdlbInfo.getId(), "01", "审核人", "驳回日期", "出库驳回原因", param.getRkremark(), CdStatusList.CGOBACK.getCode());
        }

        //车贷信息表动态流程
        setcdlbInfoDynamicBA(projectId, param.getRkremark(), CdStatusList.CGOBACK.getCode(), "审核人", "驳回日期", "出库驳回原因", cdlbInOutApply.getId(), "01");
        //代办通知事项  关闭
        CdlbInOutApply cdlbInOutApply1 = new CdlbInOutApply();
        cdlbInOutApply1.setId(cdlbInOutApply.getId());  //申请id
        cdlbInOutApply1.setApplyFlag(CdStatusList.GOWAREHOUSEAPPLY.getCode()); // 申请状态
        updatetn(cdlbInOutApply1);
        return AjaxResult.success();
    }

    /**
     * 绿本提交出库审核驳回
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "绿本提交出库出库驳回", businessType = BusinessType.INSERT)
    @PostMapping("/outchukuBH")
    @Transactional
    public AjaxResult outchukuBH(@RequestBody Param param) {
        Integer a = 0;
        //出入库记录
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setId(param.getId());
//        cdlbInOutApply.setUser22Time(new Date());

        cdlbInOutApply.setCounts(param.counts);
        /*出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回                   20出库申请                        21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInOutApply.setApplyFlag(CdStatusList.GOBACK.getCode());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);

        //出库申请被驳回  添加通知
        CdlbProjectDynamic cdlbProjectDynamic = new CdlbProjectDynamic();
        cdlbProjectDynamic.setApplyId(param.getId());
        cdlbProjectDynamic.setApplyFlag(CdStatusList.GOWAREHOUSE.getCode());
        CdlbProjectDynamic projectDynamic = cdlbProjectDynamicService.selectlimit1(cdlbProjectDynamic);
        insertNotify(param.getId(), CdStatusList.GOBACK.getCode(), Long.valueOf(projectDynamic.getOperId()), "车抵贷绿本出入库-出库审核被驳回", "/cdlb/cdlblbcksq");


        Long projectId = null;
        for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {
            projectId = cdlbInfo1.getProjectId();
            //修改借据状态为以出库
/*
            CdlbLoanInfo cdlbLoanInfo = new CdlbLoanInfo();
            cdlbLoanInfo.setId(cdlbInfo1.getLoanId());
            cdlbLoanInfo.setCdlbRecord("Y");
            cdlbLoanInfoService.updateCdlbLoanInfo(cdlbLoanInfo);
*/
            //修改绿本
            CdlbInfo cdlbInfo = new CdlbInfo();
            /* cdlbInfo.sdfg*/
            cdlbInfo.setId(cdlbInfo1.getId());
            cdlbInfo.setLbFlag(CdStatusList.GOBACK.getCode());

            cdlbInfo.setChildFlag("01");
            cdlbInfo.setTemporaryFlag("01");
            cdlbInfoService.updateCdlbInfo(cdlbInfo);
            //绿本状态信息表     出入库状态  01出库，02入库
            setcdlbInfoDynamicAA(cdlbInfo.getId(), "01", "出库登记人", "驳回日期", "审核意见", param.getRkremark(), CdStatusList.GOBACK.getCode());
        }

        //车贷信息表动态流程
        setcdlbInfoDynamicBA(projectId, param.getRkremark(), CdStatusList.GOBACK.getCode(), "出库登记人", "驳回日期", "审核意见", cdlbInOutApply.getId(), "01");
        //代办通知事项  关闭
        CdlbInOutApply cdlbInOutApply1 = new CdlbInOutApply();
        cdlbInOutApply1.setId(cdlbInOutApply.getId());  //申请id
        cdlbInOutApply1.setApplyFlag(CdStatusList.GOWAREHOUSEAPPLY.getCode()); // 申请状态
        updatetn(cdlbInOutApply1);
        return AjaxResult.success();
    }

    /**
     * 绿本提交入库申请
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "绿本提交入库申请", businessType = BusinessType.INSERT)
    @PostMapping("/enterInto")
    @Transactional
    public AjaxResult enterInto(@RequestBody Param param) {
        Integer a = 0;
        //出入库记录表
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setId(param.getId());
        //chu入库申请说明
        cdlbInOutApply.setWarehouseRemark(param.getRkremark());

        cdlbInOutApply.setRemark(param.getRkremark());
        cdlbInOutApply.setUser11Time(new Date());
        cdlbInOutApply.setCounts(param.counts);
        /*出入库审核状态标识：10入库录入              11入库申请                    12入库登记13入库完成 19入库驳回                   20出库申请                        21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInOutApply.setApplyFlag(CdStatusList.LOGGINGAPPLY.getCode());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApply.setUpdateBy(getLoginUser().getUser().getNickName());
        cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);
        //添加通知
        CdlbInOutApply cdlbInOutApply1 = cdlbInOutApplyService.selectCdlbInOutApplyById(param.getId());
        if (cdlbInOutApply1 != null && StringUtils.isNotEmpty(cdlbInOutApply1.getUser12Id())) {
            String user12Id = cdlbInOutApply1.getUser12Id();
            int i = user12Id.indexOf(",");
            if (i != -1) {
                String[] split = user12Id.split(",");
                for (int j = 0; j < split.length; j++) {
                    insertNotify(cdlbInOutApply1.getId(), CdStatusList.LOGGINGAPPLY.getCode(), Long.valueOf(split[j]), "车抵贷绿本出入库-入库申请待登记", "/cdlb/cdlbdrk");

                }
            }
            if (i == -1) {
                insertNotify(cdlbInOutApply1.getId(), CdStatusList.LOGGINGAPPLY.getCode(), Long.valueOf(user12Id), "车抵贷绿本出入库-入库申请待登记", "/cdlb/cdlbdrk");
            }
        }
        Long projectId = null;
        Long cdlbInfo1Id = null;
        Integer asd = 0;
        CdlbInfo cdlbInfo2 = new CdlbInfo();
        cdlbInfo2.setProjectInId(param.getId());
        cdlbInfo2.setStatus("0");
        List<CdlbInfo> cdlbInfos = cdlbInfoService.selectCdlbInfoList(cdlbInfo2);
        /*  for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {*/
        for (CdlbInfo cdlbInfo1 : cdlbInfos) {

            if (asd > 0) {
                if (!cdlbInfo1Id.equals(cdlbInfo1.getId())) {
                    projectId = cdlbInfo1.getProjectId();
                    //修改绿本
                    CdlbInfo cdlbInfo = new CdlbInfo();
                    cdlbInfo.setId(cdlbInfo1.getId());
                    cdlbInfo.setLbFlag(CdStatusList.LOGGINGAPPLY.getCode());
                    cdlbInfo.setChildFlag("01");
                    cdlbInfo.setTemporaryFlag("01");

                    cdlbInfoService.updateCdlbInfo(cdlbInfo);

                    //绿本状态信息表     出入库状态  01出库，02入库
                    setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "申请人", "申请日期", "入库申请说明", param.getRkremark(), CdStatusList.LOGGINGAPPLY.getCode());
                    cdlbInfo1Id = cdlbInfo1.getId();
                }
            } else {
                cdlbInfo1Id = cdlbInfo1.getId();
                asd++;
                projectId = cdlbInfo1.getProjectId();
                //修改绿本
                CdlbInfo cdlbInfo = new CdlbInfo();
                cdlbInfo.setId(cdlbInfo1.getId());
                cdlbInfo.setLbFlag(CdStatusList.LOGGINGAPPLY.getCode());

                cdlbInfo.setChildFlag("01");
                cdlbInfo.setTemporaryFlag("01");
                cdlbInfoService.updateCdlbInfo(cdlbInfo);

                //绿本状态信息表     出入库状态  01出库，02入库
                setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "申请人", "申请日期", "入库申请说明", param.getRkremark(), CdStatusList.LOGGINGAPPLY.getCode());
            }


        }
        //车贷信息表动态流程
        setcdlbInfoDynamicBA(projectId, param.getRkremark(), CdStatusList.LOGGINGAPPLY.getCode(), "申请人", "申请日期", "入库申请说明", cdlbInOutApply.getId(), "02");
        return AjaxResult.success();
    }

    /**
     * 绿本登记完成入库
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "绿本登记完成入库", businessType = BusinessType.INSERT)
    @PostMapping("/warehouseIntoOk")
    @Transactional
    public AjaxResult warehouseIntoOk(@RequestBody Param param) {
        Integer a = 0;
        //出入库记录表
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setId(param.getId());
        //chu入库申请说明
        cdlbInOutApply.setWarehouseRemark(param.getRkremark());

        cdlbInOutApply.setUser12Time(new Date());
        cdlbInOutApply.setCounts(param.counts);
        /*出入库审核状态标识：10入库录入              11入库申请                    12入库登记13入库完成 19入库驳回                   20出库申请                        21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInOutApply.setApplyFlag(CdStatusList.LOGGINGREGISTER.getCode());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApply.setUpdateBy(getLoginUser().getUser().getNickName());
        cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);


      /*  CdlbInOutApply cdlbInOutApply1 = cdlbInOutApplyService.selectCdlbInOutApplyById(param.getId());
        Long projectId1 = cdlbInOutApply1.getProjectId();
        CdlbProject cdlbProject1 = cdlbProjectService.selectCdlbProjectById(projectId1);*/
        Long projectId = null;
       /* for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {
            projectId = cdlbInfo1.getProjectId();
            //修改绿本
            CdlbInfo cdlbInfo = new CdlbInfo();
            cdlbInfo.setId(cdlbInfo1.getId());
            cdlbInfo.setLbFlag(CdStatusList.LOGGINGREGISTER.getCode());
            cdlbInfoService.updateCdlbInfo(cdlbInfo);
            //绿本状态信息表     出入库状态  01出库，02入库
            setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "登记人", "登记日期", "入库办理说明", param.getRkremark(), CdStatusList.LOGGINGREGISTER.getCode());
        }*/
        Long cdlbInfo1Id = null;
        Integer asd = 0;
        for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {

            if (asd > 0) {
                if (!cdlbInfo1Id.equals(cdlbInfo1.getId())) {
                    projectId = cdlbInfo1.getProjectId();
                    //修改绿本
                    CdlbInfo cdlbInfo = new CdlbInfo();
                    cdlbInfo.setId(cdlbInfo1.getId());
                    cdlbInfo.setLbFlag(CdStatusList.LOGGINGREGISTER.getCode());
                    cdlbInfo.setRegisterTime(new Date());
                    cdlbInfoService.updateCdlbInfo(cdlbInfo);
                    //绿本状态信息表     出入库状态  01出库，02入库
                    setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "入库登记人", "入库日期", "入库办理说明", param.getRkremark(), CdStatusList.LOGGINGREGISTER.getCode());
                    cdlbInfo1Id = cdlbInfo1.getId();
                }
            } else {
                cdlbInfo1Id = cdlbInfo1.getId();
                asd++;
                projectId = cdlbInfo1.getProjectId();
                //修改绿本
                CdlbInfo cdlbInfo = new CdlbInfo();
                cdlbInfo.setId(cdlbInfo1.getId());
                cdlbInfo.setLbFlag(CdStatusList.LOGGINGREGISTER.getCode());
                cdlbInfo.setRegisterTime(new Date());
                cdlbInfoService.updateCdlbInfo(cdlbInfo);
                //绿本状态信息表     出入库状态  01出库，02入库
                setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "入库登记人", "入库日期", "入库办理说明", param.getRkremark(), CdStatusList.LOGGINGREGISTER.getCode());
            }
        }
        //车贷信息表动态流程
        setcdlbInfoDynamicBA(projectId, param.getRkremark(), CdStatusList.LOGGINGREGISTER.getCode(), "入库登记人", "入库日期", "入库办理说明", cdlbInOutApply.getId(), "02");

        //代办通知事项  关闭
        CdlbInOutApply cdlbInOutApply1 = new CdlbInOutApply();
        cdlbInOutApply1.setId(cdlbInOutApply.getId());  //申请id
        cdlbInOutApply1.setApplyFlag(CdStatusList.LOGGINGAPPLY.getCode()); // 申请状态
        updatetn(cdlbInOutApply1);

        return AjaxResult.success();
    }

    /**
     * 绿本提交入库申请
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "绿本登记驳回", businessType = BusinessType.INSERT)
    @PostMapping("/warehouseIntoBack")
    @Transactional
    public AjaxResult warehouseIntoBack(@RequestBody Param param) {
        Integer a = 0;
        //出入库记录表
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        cdlbInOutApply.setId(param.getId());
        //chu入库申请说明
        cdlbInOutApply.setWarehouseRemark(param.getRkremark());

        cdlbInOutApply.setUser11Time(new Date());
        cdlbInOutApply.setCounts(param.counts);

        cdlbInOutApply.setApplyFlag(CdStatusList.LOGGINGOKBH.getCode());
        cdlbInOutApply.setUpdateTime(new Date());
        cdlbInOutApply.setUpdateBy(getLoginUser().getUser().getNickName());
        cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);

        //入库申请被驳回
        CdlbProjectDynamic cdlbProjectDynamic = new CdlbProjectDynamic();
        cdlbProjectDynamic.setApplyId(param.getId());
        cdlbProjectDynamic.setApplyFlag(CdStatusList.LOGGINGAPPLY.getCode());
        CdlbProjectDynamic projectDynamic = cdlbProjectDynamicService.selectlimit1(cdlbProjectDynamic);
        insertNotify(param.getId(), CdStatusList.LOGGINGOKBH.getCode(), Long.valueOf(projectDynamic.getOperId()), "车抵贷绿本出入库-入库申请被驳回", "/cdlb/cdlblbrksq");

        Long projectId = null;
/*        for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {
            projectId = cdlbInfo1.getProjectId();
            //修改绿本
            CdlbInfo cdlbInfo = new CdlbInfo();
            cdlbInfo.setId(cdlbInfo1.getId());
            cdlbInfo.setLbFlag(CdStatusList.LOGGINGOKBH.getCode());
            cdlbInfoService.updateCdlbInfo(cdlbInfo);
            //绿本状态信息表     出入库状态  01出库，02入库
            setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "驳回人", "驳回日期", "入库驳回原因", param.getRkremark(), CdStatusList.LOGGINGOKBH.getCode());
        }*/
        Long cdlbInfo1Id = null;
        Integer asd = 0;
        for (CdlbInfo cdlbInfo1 : param.cdlbInfos) {

            if (asd > 0) {
                if (!cdlbInfo1Id.equals(cdlbInfo1.getId())) {
                    projectId = cdlbInfo1.getProjectId();
                    //修改绿本
                    CdlbInfo cdlbInfo = new CdlbInfo();
                    cdlbInfo.setId(cdlbInfo1.getId());
                    cdlbInfo.setLbFlag(CdStatusList.LOGGINGOKBH.getCode());
                    cdlbInfoService.updateCdlbInfo(cdlbInfo);
                    //绿本状态信息表     出入库状态  01出库，02入库
                    setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "入库登记人", "驳回日期", "入库驳回", param.getRkremark(), CdStatusList.LOGGINGOKBH.getCode());
                    cdlbInfo1Id = cdlbInfo1.getId();
                }
            } else {
                cdlbInfo1Id = cdlbInfo1.getId();
                asd++;
                projectId = cdlbInfo1.getProjectId();
                //修改绿本
                CdlbInfo cdlbInfo = new CdlbInfo();
                cdlbInfo.setId(cdlbInfo1.getId());
                cdlbInfo.setLbFlag(CdStatusList.LOGGINGOKBH.getCode());
                cdlbInfoService.updateCdlbInfo(cdlbInfo);
                //绿本状态信息表     出入库状态  01出库，02入库
                setcdlbInfoDynamicAA(cdlbInfo.getId(), "02", "入库登记人", "驳回日期", "入库驳回", param.getRkremark(), CdStatusList.LOGGINGOKBH.getCode());
            }
        }


        //车贷信息表动态流程
        setcdlbInfoDynamicBA(projectId, param.getRkremark(), CdStatusList.LOGGINGOKBH.getCode(), "入库登记人", "驳回日期", "入库驳回", cdlbInOutApply.getId(), "02");
        //代办通知事项  关闭
        CdlbInOutApply cdlbInOutApply1 = new CdlbInOutApply();
        cdlbInOutApply1.setId(cdlbInOutApply.getId());  //申请id
        cdlbInOutApply1.setApplyFlag(CdStatusList.LOGGINGAPPLY.getCode()); // 申请状态
        updatetn(cdlbInOutApply1);
        return AjaxResult.success();
    }

    /**
     * 查询车贷绿本信息列表
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/list")
    public TableDataInfo list(CdlbInfo cdlbInfo) {
        /*  startPage();*/
        cdlbInfo.setStatus("0");
        List<CdlbInfo> list = cdlbInfoService.selectCdlbInfoList(cdlbInfo);
        /*List<CdlbInfo> list = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo);*/

        return getDataTable(list);
    }

    /**
     * 查询单条绿本信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AjaxResult list(@PathVariable("id") Long id) {

        CdlbInfo cdlbInfo = cdlbInfoService.selectCdlbInfoById(id);
        if (cdlbInfo != null && StringUtils.isNotEmpty(cdlbInfo.getCustNo())) {
            SysDictTypeData label = getLabel(guaranteeNameV, cdlbInfo.getCustNo());
            if (label != null && StringUtils.isNotEmpty(label.getDictName())) {
                cdlbInfo.setCustName(label.getDictLabel());
            }
        }
        return AjaxResult.success(cdlbInfo);
    }


    /**
     * 查询车贷绿本信息列表
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/leftlistLvbenchaxun")
    /*   public TableDataInfo leftlistLvbenchaxun(CdlbInfo cdlbInfo) {*/
    public TableDataInfo leftlistLvbenchaxun(CdlbInfo cdlbInfo, Integer as, Integer bs) {
        Integer ass = (as - 1) * bs;
        cdlbInfo.setBs(bs);
        cdlbInfo.setAs(ass);


        // [{value:"10",label:"待提交"},{value:"11",label:"待入库"},{value:"12",label:"已入库"},{value:"20",label:"待审核"},{value:"21",label:"待出库"},{value:"22",label:"已出库"}],

        /*cdlbInfo.setCdlbBinding("Y");*/
        cdlbInfo.setStatus("0");
        /* cdlbInfo.setStatusB("0");*/


        if (StringUtils.isNotEmpty(cdlbInfo.getLbFlag())) {
            ArrayList<String> objects = new ArrayList<>();
            objects.add(cdlbInfo.getLbFlag());//待出库
            cdlbInfo.setLbFlagList(objects);
        } else {
            ArrayList<String> objects = new ArrayList<>();
            /*objects.add("11");//待入库
            objects.add("12");//已入库
            objects.add("20");//待审核
            objects.add("21");//待出库*/
            /*这里查询不等于*/
            objects.add("10");//待提交
            objects.add("22");//已出库

            cdlbInfo.setNotlbFlagList(objects);
        }


        if (StringUtils.isNotEmpty(cdlbInfo.getIds())) {
            int length = cdlbInfo.getIds().length();
            String substring = cdlbInfo.getIds().substring(0, length - 1);
            cdlbInfo.setIds(substring);
        }
//        String username = getLoginUser().getUsername();                       cdlbxiangmujingli                             cdlbfengxianjingli                             cdlbbangongshizhuren
        boolean b = getLoginUser().getUser().getRoles().stream().anyMatch(t -> "cdlbxiangmujingli".equals(t.getRoleKey()) || "cdlbfengxianjingli".equals(t.getRoleKey()) || "cdlbbangongshizhuren".equals(t.getRoleKey()));
        boolean b1 = getLoginUser().getUser().getRoles().stream().anyMatch(t -> "admin".equals(t.getRoleKey())|| "cdlbzhglbgly".equals(t.getRoleKey()) || "cdlbyyglbgly".equals(t.getRoleKey()) || "cdlbfxjlbgly".equals(t.getRoleKey()));
        if (b1 == false && b==true) {
            cdlbInfo.setUserId(getLoginUser().getUserId());
        }
        if (b1 == false && b==false) {
            ArrayList<CdlbInfo> cdlbInfos = new ArrayList<>();
            return getDataTableByService(cdlbInfos, Long.valueOf(0));
        }

/*        String username = getLoginUser().getUsername();
        if (StringUtils.isNotEmpty(username)&&! username.equals("admin")){
            Long userId = getUserId();
            List<SysRole> sysRoles = sysRoleService.selectRolesByUserId(userId);
            if (CollectionUtils.isNotEmpty(sysRoles)){
                for (SysRole sysRole : sysRoles) {
                    if (sysRole.getRoleKey().equals("cdlbxiangmujingli")|| sysRole.getRoleKey().equals("cdlbfengxianjingli")||sysRole.getRoleKey().equals("cdlbbangongshizhuren")){
                        cdlbInfo.setUserId(userId);
                    }
                }
            }
        }*/


        Integer selectcounts = cdlbInfoService.selectcountsinfork(cdlbInfo);
        List<CdlbInfo> list = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo);
        List<String> longs = new ArrayList<>();
        Set<String> longs1 = new HashSet<>();
        Map<String, String> stringStringMap = custName();
        for (CdlbInfo info : list) {
            if (StringUtils.isNotEmpty(info.getCustNo())) {
              /*  SysDictTypeData label = getLabel(guaranteeNameV, info.getCustNo());
                if (label != null && StringUtils.isNotEmpty(label.getDictName())) {
                    info.setCustName(label.getDictLabel());
                }*/
                if (stringStringMap != null && StringUtils.isNotEmpty(stringStringMap.get(info.getCustNo()))) {
                    info.setCustName( stringStringMap.get(info.getCustNo()));
                }
            }
            longs.add(String.valueOf(info.getId()));
            longs1.add(String.valueOf(info.getId()));
        }
        Map<String, Integer> stringIntegerMap = new HashMap<>();
        for (String aLong : longs1) {
            Integer a = 0;
            for (String aLong1 : longs) {
                if (aLong.equals(aLong1)) {
                    a++;
                    stringIntegerMap.put(aLong, a);
                }
            }
        }
        for (CdlbInfo info : list) {
            for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

                String s = String.valueOf(info.getId());
                if (s.equals(entry.getKey())) {
                    if (entry.getValue() > 1) {
                        info.setRelevance("关联多个");
                        info.setRelevanceID(1l);
                    }
                    if (entry.getValue() == 1) {
                        if (info.getCdlbId() != null) {
                            info.setRelevance("成功");
                            info.setRelevanceID(2l);
                        } else {
                            info.setRelevance("未关联");
                            info.setRelevanceID(3l);
                        }
                    }
                }
            }
        }
        List<CdlbInfo> cdlbInfos = new ArrayList<>();
        /*  List<CdlbInfo> list1 = new ArrayList<>();*/
       /* if (CollectionUtils.isNotEmpty(list)) {
            //获取用户角色
            Long userId = getLoginUser().getUserId();
            CdlbProjectUser cdlbProjectUser = new CdlbProjectUser();
            cdlbProjectUser.setUserId(userId);
            List<CdlbProjectUser> cdlbProjectUsers = projectUserService.selectCdlbProjectUserList(cdlbProjectUser);
            if (CollectionUtils.isNotEmpty(cdlbProjectUsers)){
                for (CdlbProjectUser projectUser : cdlbProjectUsers) {
                  //获取项目id
                    Long projectId = projectUser.getProjectId();
                    //遍历集合
                    for (CdlbInfo info : list) {
                        if (info.getProjectId().equals(projectId)){
                            list1.add(info);
                        }
                    }
                }
            }else {
                list1=list;
            }


            cdlbInfos = list1.stream().sorted(Comparator.comparing(CdlbInfo::getId)).collect(Collectors.toList());//
        }*/
        cdlbInfos = list.stream().sorted(Comparator.comparing(CdlbInfo::getId)).collect(Collectors.toList());//
        /*   return getDataTable(cdlbInfos);*/
        return getDataTableByService(cdlbInfos, Long.valueOf(selectcounts));
    }

    /**
     * 查询车贷绿本信息列表
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/leftlistLvbenchaxunchuku")
    /* public TableDataInfo leftlistLvbenchaxunchuku(CdlbInfo cdlbInfo) {*/
    public TableDataInfo leftlistLvbenchaxunchuku(CdlbInfo cdlbInfo, Integer as, Integer bs) {
        Integer ass = (as - 1) * bs;
        cdlbInfo.setBs(bs);
        cdlbInfo.setAs(ass);


        // [{value:"10",label:"待提交"},{value:"11",label:"待入库"},{value:"12",label:"已入库"},{value:"20",label:"待审核"},{value:"21",label:"待出库"},{value:"22",label:"已出库"}],

        /*cdlbInfo.setCdlbBinding("Y");*/
        cdlbInfo.setStatus("0");
        /* cdlbInfo.setStatusB("0");*/


        if (StringUtils.isNotEmpty(cdlbInfo.getLbFlag())) {
            ArrayList<String> objects = new ArrayList<>();
            objects.add(cdlbInfo.getLbFlag());//待出库
            cdlbInfo.setLbFlagList(objects);
        } else {
            ArrayList<String> objects = new ArrayList<>();
            /*objects.add("11");//待入库
            objects.add("12");//已入库
            objects.add("20");//待审核
            objects.add("21");//待出库*/
            /*这里查询不等于*/
            objects.add("10");//待提交10 = {CdlbInfo@16098} "org.ruoyi.core.cdlb.domain.CdlbInfo@78bdf9fa[\r\n  id=506\r\n  projectId=13\r\n  projectInId=148\r\n  projectOutId=149\r\n  contractCode=435678765\r\n  clientName=阿斯蒂\r\n  clientCardId=360222200001163512\r\n  loanBinding=N\r\n  loanNo=b\r\n  mailDate=Fri Oct 28 00:00:00 CST 2022\r\n  lbFlag=22\r\n  status=0\r\n  createBy=<null>\r\n  createTime=Wed Apr 19 10:49:11 CST 2023\r\n  updateBy=<null>\r\n  updateTime=<null>\r\n  applyTime=Wed Apr 12 16:28:17 CST 2023\r\n  loanAmt=111.00\r\n  custNo=ZBGX\r\n]"... View
            objects.add("22");//已出库

            cdlbInfo.setNotlbFlagList(objects);
        }


        if (StringUtils.isNotEmpty(cdlbInfo.getIds())) {
            int length = cdlbInfo.getIds().length();
            String substring = cdlbInfo.getIds().substring(0, length - 1);
            cdlbInfo.setIds(substring);
        }

//        String username = getLoginUser().getUsername();                       cdlbxiangmujingli                             cdlbfengxianjingli                             cdlbbangongshizhuren
        boolean b = getLoginUser().getUser().getRoles().stream().anyMatch(t -> "cdlbxiangmujingli".equals(t.getRoleKey()) || "cdlbfengxianjingli".equals(t.getRoleKey()) || "cdlbbangongshizhuren".equals(t.getRoleKey()));
        boolean b1 = getLoginUser().getUser().getRoles().stream().anyMatch(t -> "admin".equals(t.getRoleKey())|| "cdlbzhglbgly".equals(t.getRoleKey()) || "cdlbyyglbgly".equals(t.getRoleKey()) || "cdlbfxjlbgly".equals(t.getRoleKey()));
        if (b1 == false && b==true) {
            cdlbInfo.setUserId(getLoginUser().getUserId());
        }
        if (b1 == false && b==false) {
            ArrayList<CdlbInfo> cdlbInfos = new ArrayList<>();
            return getDataTableByService(cdlbInfos, Long.valueOf(0));
        }
//        if (StringUtils.isNotEmpty(username)&&! username.equals("admin")) {
//            Long userId = getUserId();
//            List<SysRole> sysRoles = sysRoleService.selectRolesByUserId(userId);
//            if (CollectionUtils.isNotEmpty(sysRoles)) {
//                for (SysRole sysRole : sysRoles) {
//                    if (sysRole.getRoleKey().equals("cdlbxiangmujingli") || sysRole.getRoleKey().equals("cdlbfengxianjingli") || sysRole.getRoleKey().equals("cdlbbangongshizhuren")) {
//                        cdlbInfo.setUserId(userId);
//                    }
//                }
//            }
//        }

        Integer selectcounts = cdlbInfoService.selectcountsinfock(cdlbInfo);
        List<CdlbInfo> list = cdlbInfoService.selectCdlbInfoListJoinlianchu(cdlbInfo);
        List<String> longs = new ArrayList<>();
        Set<String> longs1 = new HashSet<>();
        Map<String, String> stringStringMap = custName();
        for (CdlbInfo info : list) {
            if (StringUtils.isNotEmpty(info.getCustNo())) {
              /*  SysDictTypeData label = getLabel(guaranteeNameV, info.getCustNo());
                if (label != null && StringUtils.isNotEmpty(label.getDictName())) {
                    info.setCustName(label.getDictLabel());
                }*/
                if (stringStringMap != null && StringUtils.isNotEmpty(stringStringMap.get(info.getCustNo()))) {
                    info.setCustName( stringStringMap.get(info.getCustNo()));
                }
            }
            longs.add(String.valueOf(info.getId()));
            longs1.add(String.valueOf(info.getId()));
        }
        Map<String, Integer> stringIntegerMap = new HashMap<>();
        for (String aLong : longs1) {
            Integer a = 0;
            for (String aLong1 : longs) {
                if (aLong.equals(aLong1)) {
                    a++;
                    stringIntegerMap.put(aLong, a);
                }
            }
        }
        for (CdlbInfo info : list) {
            for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

                String s = String.valueOf(info.getId());
                if (s.equals(entry.getKey())) {
                    if (entry.getValue() > 1) {
                        info.setRelevance("关联多个");
                        info.setRelevanceID(1l);
                    }
                    if (entry.getValue() == 1) {
                        if (info.getCdlbId() != null) {
                            info.setRelevance("成功");
                            info.setRelevanceID(2l);
                        } else {
                            info.setRelevance("未关联");
                            info.setRelevanceID(3l);
                        }
                    }
                }
            }
        }
        List<CdlbInfo> cdlbInfos = new ArrayList<>();
        /*List<CdlbInfo> list1 = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            //获取用户角色
            Long userId = getLoginUser().getUserId();
            CdlbProjectUser cdlbProjectUser = new CdlbProjectUser();
            cdlbProjectUser.setUserId(userId);
            List<CdlbProjectUser> cdlbProjectUsers = projectUserService.selectCdlbProjectUserList(cdlbProjectUser);
            if (CollectionUtils.isNotEmpty(cdlbProjectUsers)){
                for (CdlbProjectUser projectUser : cdlbProjectUsers) {
                    //获取项目id
                    Long projectId = projectUser.getProjectId();
                    //遍历集合
                    for (CdlbInfo info : list) {
                        if (info.getProjectId().equals(projectId)){
                            list1.add(info);
                        }
                    }
                }
            }else {
                list1=list;
            }
            List<CdlbInfo> uniqueByName = list1.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CdlbInfo::getId))), ArrayList::new));
            cdlbInfos = uniqueByName.stream().sorted(Comparator.comparing(CdlbInfo::getId)).collect(Collectors.toList());//
        }*/
        /*   return getDataTable(cdlbInfos);*/
        List<CdlbInfo> uniqueByName = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CdlbInfo::getId))), ArrayList::new));
        cdlbInfos = uniqueByName.stream().sorted(Comparator.comparing(CdlbInfo::getId)).collect(Collectors.toList());//
        return getDataTableByService(cdlbInfos, Long.valueOf(selectcounts));
    }

    /**
     * 查询车贷绿本信息列表
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/leftlist")
    public TableDataInfo leftlist(CdlbInfo cdlbInfo) {


        if (cdlbInfo.getAs() != null && cdlbInfo.getBs() != null) {
            Integer ass = (cdlbInfo.getAs() - 1) * cdlbInfo.getBs();
            cdlbInfo.setBs(cdlbInfo.getBs());
            cdlbInfo.setAs(ass);
        }
       /* startPage();*/
        /*cdlbInfo.setCdlbBinding("Y");*/
        cdlbInfo.setStatus("0");
        /* cdlbInfo.setStatusB("0");*/
 /*  /!*  待提交       待入库        入库完成                       待审核        待出库     出库完成
         10入库录入   11入库申请    12入库登记    13入库完成         20出库申请    21出库审核    22出库登记     23出库完成*!/
      optionszt: [{value:"",label:"全部"},{value:"11",label:"待入库"},{value:"12",label:"已入库"},{value:"20",label:"待审核"},{value:"21",label:"待出库"}],*/

           Integer aaa  = 0;
        if (StringUtils.isNotEmpty(cdlbInfo.getLbFlag())) {
            ArrayList<String> objects = new ArrayList<>();
            objects.add(cdlbInfo.getLbFlag());//待出库
            cdlbInfo.setLbFlagList(objects);
        } else {
            aaa++;
            ArrayList<String> objects = new ArrayList<>();
            objects.add("11");//待入库
            objects.add("12");//已入库
            objects.add("20");//待审核
            objects.add("21");//待出库
            cdlbInfo.setLbFlagList(objects);
        }
        if (StringUtils.isNotEmpty(cdlbInfo.getIds())) {
            int length = cdlbInfo.getIds().length();
            String substring = cdlbInfo.getIds().substring(0, length - 1);
            cdlbInfo.setIds(substring);
        }
        Integer selectcounts =0;
        Integer st = 0;
        for (String s : cdlbInfo.getLbFlagList()) {
            if (s.equals("11")||s.equals("12")){
               st++;
            }
        }
        if (st>0){
           selectcounts = cdlbInfoService.selectcountsinfork(cdlbInfo);
        } else {
            selectcounts = cdlbInfoService.selectcountsinfock(cdlbInfo);
        }

        List<CdlbInfo> list = new ArrayList<>();
        //此一处使用该方法
        if (aaa==0&&(cdlbInfo.getLbFlag().equals("20")||cdlbInfo.getLbFlag().equals("21"))){
            list = cdlbInfoService.selectCdlbInfoListJoinlianchuxiangqing(cdlbInfo);
        }else {
            list = cdlbInfoService.selectCdlbInfoListJoinlianxiangqing(cdlbInfo);
        }
        List<String> longs = new ArrayList<>();
        Set<String> longs1 = new HashSet<>();
        Map<String, String> stringStringMap = custName();
        String lbinfoid = "s";
        for (CdlbInfo info : list) {
            if (StringUtils.isNotEmpty(info.getCustNo())) {
               /* SysDictTypeData label = getLabel(guaranteeNameV, info.getCustNo());
                if (label != null && StringUtils.isNotEmpty(label.getDictName())) {
                    info.setCustName(label.getDictLabel());
                }*/
                if (stringStringMap != null && StringUtils.isNotEmpty(stringStringMap.get(info.getCustNo()))) {
                    info.setCustName( stringStringMap.get(info.getCustNo()));
                }
            }
            longs.add(String.valueOf(info.getId()));
            longs1.add(String.valueOf(info.getId()));
            String s = String.valueOf(info.getId());
            if (!lbinfoid.equals(s)){
                List<Paramtwo> strings = fileName(info.getId());
                info.setFileNameList(strings);
                lbinfoid =String.valueOf(info.getId());
            }

        }
        Map<String, Integer> stringIntegerMap = new HashMap<>();
        for (String aLong : longs1) {
            Integer a = 0;
            for (String aLong1 : longs) {
                if (aLong.equals(aLong1)) {
                    a++;
                    stringIntegerMap.put(aLong, a);
                }
            }
        }
        for (CdlbInfo info : list) {
            Integer integer = cdlbLoanInfoService.selectinfoIdcounts(info.getClientCardId());
            if (integer > 1) {
                info.setRelevance("关联多个");
                info.setRelevanceID(1l);
            }else if (integer== 1) {
                    info.setRelevance("成功");
                    info.setRelevanceID(2l);
                }else {
                    info.setRelevance("未关联");
                    info.setRelevanceID(3l);
                }

/*
            for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

                String s = String.valueOf(info.getId());
                if (s.equals(entry.getKey())) {
                    if (entry.getValue() > 1) {
                        info.setRelevance("关联多个");
                        info.setRelevanceID(1l);
                    }
                    if (entry.getValue() == 1) {
                        if (info.getCdlbId() != null) {
                            info.setRelevance("成功");
                            info.setRelevanceID(2l);
                        } else {
                            info.setRelevance("未关联");
                            info.setRelevanceID(3l);
                        }
                    }
                }
            }*/
        }
    /*    objects.add("11");//待入库
        objects.add("20");//待审核
        objects.add("21");//待出库
          objects.add("12");//已入库*/
        return getDataTableByService(list, Long.valueOf(selectcounts));
    }


    /**
     * 查询车贷绿本信息列表
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/leftlistsqa")
    public TableDataInfo leftlistsqa(CdlbInfo cdlbInfo, Integer as, Integer bs) {
        Integer ass = (as - 1) * bs;
        cdlbInfo.setBs(bs);
        cdlbInfo.setAs(ass);
        /*cdlbInfo.setCdlbBinding("Y");*/
        cdlbInfo.setStatus("0");
        if (StringUtils.isNotEmpty(cdlbInfo.getLbFlag())) {
            ArrayList<String> objects = new ArrayList<>();
            objects.add(cdlbInfo.getLbFlag());//待出库
            cdlbInfo.setLbFlagList(objects);
        }
        if (StringUtils.isNotEmpty(cdlbInfo.getIds())) {
            int length = cdlbInfo.getIds().length();
            String substring = cdlbInfo.getIds().substring(0, length - 1);
            cdlbInfo.setIds(substring);
        }
        // Integer selectcounts = cdlbInfoService.selectcounts(cdlbInfo.getProjectId());//入库完成12
        Integer selectcounts = cdlbInfoService.selectcountsinfork(cdlbInfo);
        List<CdlbInfo> list = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo);
        List<String> longs = new ArrayList<>();
        Set<String> longs1 = new HashSet<>();
        Map<String, String> stringStringMap = custName();
        String lbinfoid = "s";
        for (CdlbInfo info : list) {
            if (StringUtils.isNotEmpty(info.getCustNo())) {
               /* SysDictTypeData label = getLabel(guaranteeNameV, info.getCustNo());
                if (label != null && StringUtils.isNotEmpty(label.getDictName())) {
                    info.setCustName(label.getDictLabel());
                }*/
                if (stringStringMap != null && StringUtils.isNotEmpty(stringStringMap.get(info.getCustNo()))) {
                    info.setCustName( stringStringMap.get(info.getCustNo()));
                }
            }
            longs.add(String.valueOf(info.getId()));
            longs1.add(String.valueOf(info.getId()));
            String s = String.valueOf(info.getId());
            if (!lbinfoid.equals(s)){
                List<Paramtwo> strings = fileName(info.getId());
                info.setFileNameList(strings);
                lbinfoid =String.valueOf(info.getId());
            }

        }
        Map<String, Integer> stringIntegerMap = new HashMap<>();
        for (String aLong : longs1) {
            Integer a = 0;
            for (String aLong1 : longs) {
                if (aLong.equals(aLong1)) {
                    a++;
                    stringIntegerMap.put(aLong, a);
                }
            }
        }
        for (CdlbInfo info : list) {
            for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

                String s = String.valueOf(info.getId());
                if (s.equals(entry.getKey())) {
                    if (entry.getValue() > 1) {
                        info.setRelevance("关联多个");
                        info.setRelevanceID(1l);
                    }
                    if (entry.getValue() == 1) {
                        if (info.getCdlbId() != null) {
                            info.setRelevance("成功");
                            info.setRelevanceID(2l);
                        } else {
                            info.setRelevance("未关联");
                            info.setRelevanceID(3l);
                        }
                    }
                }
            }
        }
    /*    objects.add("11");//待入库
        objects.add("20");//待审核
        objects.add("21");//待出库
          objects.add("12");//已入库*/


        return getDataTableByService(list, Long.valueOf(selectcounts));
    }

    /**
     * 查询车贷绿本信息列表
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/leftlistb")
    public TableDataInfo leftlistb(CdlbInfo cdlbInfo) {
        if (cdlbInfo.getAs() != null && cdlbInfo.getBs() != null) {
            Integer ass = (cdlbInfo.getAs() - 1) * cdlbInfo.getBs();
            cdlbInfo.setBs(cdlbInfo.getBs());
            cdlbInfo.setAs(ass);
        }
        cdlbInfo.setStatus("0");
        if (StringUtils.isNotEmpty(cdlbInfo.getIds())) {
            int length = cdlbInfo.getIds().length();
            String substring = cdlbInfo.getIds().substring(0, length - 1);
            cdlbInfo.setIds(substring);
        }
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        if (cdlbInfo.getProjectInId() != null) {
            cdlbInOutApply = cdlbInOutApplyService.selectCdlbInOutApplyById(cdlbInfo.getProjectInId());
        }

        List<CdlbInfo> list = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo);
        // 2023-11-17 防止页面中的 pagesize 和total 相同 添加代码
        Long count = cdlbInfoService.selectCdlbInfoListJoinlianForCount(cdlbInfo);
        List<String> longs = new ArrayList<>();
        Set<String> longs1 = new HashSet<>();
        Map<String, String> stringStringMap = custName();
        String lbinfoid = "s";
        for (CdlbInfo info : list) {
            if (StringUtils.isNotEmpty(info.getCustNo())) {
               /* SysDictTypeData label = getLabel(guaranteeNameV, info.getCustNo());
                if (label != null && StringUtils.isNotEmpty(label.getDictName())) {
                    info.setCustName(label.getDictLabel());
                }*/
                if (stringStringMap != null && StringUtils.isNotEmpty(stringStringMap.get(info.getCustNo()))) {
                    info.setCustName( stringStringMap.get(info.getCustNo()));
                }
            }
            longs.add(String.valueOf(info.getId()));
            longs1.add(String.valueOf(info.getId()));
            String s = String.valueOf(info.getId());
            if (!lbinfoid.equals(s)){
                List<Paramtwo> strings = fileName(info.getId());
                info.setFileNameList(strings);
                lbinfoid =String.valueOf(info.getId());
            }
        }
        Map<String, Integer> stringIntegerMap = new HashMap<>();
        for (String aLong : longs1) {
            Integer a = 0;
            for (String aLong1 : longs) {
                if (aLong.equals(aLong1)) {
                    a++;
                    stringIntegerMap.put(aLong, a);
                }
            }
        }

        //默认选中
      /*  Set<String> longs11 = new HashSet<>();
        for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

            if (entry.getValue() > 1) {
                longs11.add(entry.getKey());
            }
        }

        Set<CdlbInfo> cdlbInfos1 = new HashSet<>();
        for (String s : longs11) {
            CdlbInfo cdlbInfo1 = new CdlbInfo();
            cdlbInfo1.setIsId(Long.valueOf(s));
            cdlbInfo1.setCdlbRecord("N");
            cdlbInfo1.setStatus("0");
            List<CdlbInfo> list3 = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo1);
            if (list3 != null && list3.size() == 1) {
                CdlbInfo cdlbInfo2 = list3.get(0);

                for (int i = 0; i <= list.size(); i++) {
                    i = 0;
                    if (list.get(i).getId().equals(cdlbInfo2.getId())) {
                        list.remove(i);
                    }
                    i = 0;
                       *//* if(list.get(i).equals("del"))
                            list.remove(i);*//*
                }
                cdlbInfo2.setRelevance("关联多个");
                cdlbInfo2.setRelevanceIDB(5l); //重新选择
                list.add(cdlbInfo2);
            }

        }*/

        String clientCardIds = "";
        String contractCodes = "";
        for (CdlbInfo info : list) {
            for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

                String s = String.valueOf(info.getId());
                if (s.equals(entry.getKey())) {
                    if (entry.getValue() > 1) {
                        info.setRelevance("关联多个");
                        info.setRelevanceID(1l);
                    }
                    if (entry.getValue() == 1) {
                        if (info.getCdlbId() != null) {
                            info.setRelevance("成功");
                            info.setRelevanceID(2l);
                        } else {
                            info.setRelevance("未关联");
                            info.setRelevanceID(3l);
                        }
                    }
                }
            }
            clientCardIds += info.getClientCardId() + ",";
            if (StringUtils.isNotEmpty(info.getContractCode())) {
                contractCodes += info.getContractCode() + ",";
            }
        }
        String applyFlag = cdlbInOutApply.getApplyFlag();
        //查看是否有重复
        List<CdlbInfo> cardIdCounts = new ArrayList<>();
        List<CdlbInfo> coCodes = new ArrayList<>();
        if (clientCardIds.length() > 0) {
            clientCardIds = clientCardIds.substring(0, clientCardIds.length() - 1);
            cardIdCounts = cdlbInfoService.selectClientCardIdCounts(clientCardIds, applyFlag);
        }

        if (contractCodes.length() > 0) {
            contractCodes = contractCodes.substring(0, contractCodes.length() - 1);
            coCodes = cdlbInfoService.selectcontractCodeCounts(contractCodes, applyFlag);
        }


        for (CdlbInfo info : list) {
            if (CollectionUtils.isNotEmpty(cardIdCounts)) {
                for (CdlbInfo cardIdCount : cardIdCounts) {
                    if (info.getClientCardId().equals(cardIdCount.getClientCardId()) && cardIdCount.getCounts() > 1) {
                        info.setClientCardIdQuantity(cardIdCount.getCounts());
                        break;
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(coCodes)) {
                for (CdlbInfo coCode : coCodes) {
                    if (info.getContractCode().equals(coCode.getContractCode()) && coCode.getCounts() > 1) {
                        info.setContractCodeQuantity(coCode.getCounts());
                        break;
                    }
                }
            }
        }
        List<CdlbInfo> collect12 = list.stream().sorted(Comparator.comparing(CdlbInfo::getId)).collect(Collectors.toList());

        return getDataTableForTotal(collect12,count);
    }

    /**
     * 出库查询绿本关联页
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/leftlistblblb")
    public TableDataInfo leftlistblblb(CdlbInfo cdlbInfo) {
        if (cdlbInfo.getAs() != null && cdlbInfo.getBs() != null) {
            Integer ass = (cdlbInfo.getAs() - 1) * cdlbInfo.getBs();
            cdlbInfo.setBs(cdlbInfo.getBs());
            cdlbInfo.setAs(ass);
        }
        cdlbInfo.setStatus("0");
        if (StringUtils.isNotEmpty(cdlbInfo.getIds())) {
            int length = cdlbInfo.getIds().length();
            String substring = cdlbInfo.getIds().substring(0, length - 1);
            cdlbInfo.setIds(substring);
        }
        List<CdlbInfo> list = cdlbInfoService.selectCdlbInfoListJoinlianchu(cdlbInfo);
        // 2023-11-17 防止页面中的 pagesize 和total 相同 添加代码
        long count = cdlbInfoService.selectCdlbInfoListJoinlianchuForCount(cdlbInfo);
        List<String> longs = new ArrayList<>();
        Set<String> longs1 = new HashSet<>();
        Map<String, String> stringStringMap = custName();
        String lbinfoid = "s";
        for (CdlbInfo info : list) {
            if (StringUtils.isNotEmpty(info.getCustNo())) {
               /* SysDictTypeData label = getLabel(guaranteeNameV, info.getCustNo());
                if (label != null && StringUtils.isNotEmpty(label.getDictName())) {
                    info.setCustName(label.getDictLabel());
                }*/
                if (stringStringMap != null && StringUtils.isNotEmpty(stringStringMap.get(info.getCustNo()))) {
                    info.setCustName( stringStringMap.get(info.getCustNo()));
                }
            }
            longs.add(String.valueOf(info.getId()));
            longs1.add(String.valueOf(info.getId()));
            String s = String.valueOf(info.getId());
            if (!lbinfoid.equals(s)){
                List<Paramtwo> strings = fileName(info.getId());
                info.setFileNameList(strings);
                lbinfoid =String.valueOf(info.getId());
            }
        }
        Map<String, Integer> stringIntegerMap = new HashMap<>();
        for (String aLong : longs1) {
            Integer a = 0;
            for (String aLong1 : longs) {
                if (aLong.equals(aLong1)) {
                    a++;
                    stringIntegerMap.put(aLong, a);
                }
            }
        }


        String clientCardIds = "";
        String contractCodes = "";
        for (CdlbInfo info : list) {
            for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

                String s = String.valueOf(info.getId());
                if (s.equals(entry.getKey())) {
                    if (entry.getValue() > 1) {
                        info.setRelevance("关联多个");
                        info.setRelevanceID(1l);
                    }
                    if (entry.getValue() == 1) {
                        if (info.getCdlbId() != null) {
                            info.setRelevance("成功");
                            info.setRelevanceID(2l);
                        } else {
                            info.setRelevance("未关联");
                            info.setRelevanceID(3l);
                        }
                    }
                }
            }
            clientCardIds += info.getClientCardId() + ",";
            if (StringUtils.isNotEmpty(info.getContractCode())) {
                contractCodes += info.getContractCode() + ",";
            }
        }


        List<CdlbInfo> collect12 = list.stream().sorted(Comparator.comparing(CdlbInfo::getId)).collect(Collectors.toList());

        return getDataTableForTotal(collect12,count);
    }

    /**
     * 查询车贷绿本信息列表
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/leftlistbgl")
    public TableDataInfo leftlistbgl(CdlbInfo cdlbInfo) {
        if (cdlbInfo.getAs() != null && cdlbInfo.getBs() != null) {
            Integer ass = (cdlbInfo.getAs() - 1) * cdlbInfo.getBs();
            cdlbInfo.setBs(cdlbInfo.getBs());
            cdlbInfo.setAs(ass);
        }
        cdlbInfo.setStatus("0");
        if (StringUtils.isNotEmpty(cdlbInfo.getIds())) {
            int length = cdlbInfo.getIds().length();
            String substring = cdlbInfo.getIds().substring(0, length - 1);
            cdlbInfo.setIds(substring);
        }
        CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
        if (cdlbInfo.getProjectInId() != null) {
            cdlbInOutApply = cdlbInOutApplyService.selectCdlbInOutApplyById(cdlbInfo.getProjectInId());
        }
        //  Integer selectcounts = cdlbInfoService.selectcountsruku(cdlbInfo.getProjectInId());//入库关联申请
        Integer selectcounts = cdlbInfoService.selectcountsinfork(cdlbInfo);
        List<CdlbInfo> list = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo);
        List<String> longs = new ArrayList<>();
        Set<String> longs1 = new HashSet<>();

        Map<String, String> stringStringMap = custName();
        String lbinfoid = "s";
        for (CdlbInfo info : list) {
            if (StringUtils.isNotEmpty(info.getCustNo())) {
              /*  SysDictTypeData label = getLabel(guaranteeNameV, info.getCustNo());
                if (label != null && StringUtils.isNotEmpty(label.getDictName())) {
                    info.setCustName(label.getDictLabel());
                }*/
                if (stringStringMap != null && StringUtils.isNotEmpty(stringStringMap.get(info.getCustNo()))) {
                    info.setCustName( stringStringMap.get(info.getCustNo()));
                }
            }
            longs.add(String.valueOf(info.getId()));
            longs1.add(String.valueOf(info.getId()));

            String s = String.valueOf(info.getId());
            if (!lbinfoid.equals(s)){
            List<Paramtwo> strings = fileName(info.getId());
            info.setFileNameList(strings);
                lbinfoid =String.valueOf(info.getId());
            }
        }
        Map<String, Integer> stringIntegerMap = new HashMap<>();
        for (String aLong : longs1) {
            Integer a = 0;
            for (String aLong1 : longs) {
                if (aLong.equals(aLong1)) {
                    a++;
                    stringIntegerMap.put(aLong, a);
                }
            }
        }

        //默认选中
       /* Set<String> longs11 = new HashSet<>();
        for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

            if (entry.getValue() > 1) {
                longs11.add(entry.getKey());
            }
        }

        Set<CdlbInfo> cdlbInfos1 = new HashSet<>();
        for (String s : longs11) {
            CdlbInfo cdlbInfo1 = new CdlbInfo();
            cdlbInfo1.setIsId(Long.valueOf(s));
            cdlbInfo1.setCdlbRecord("N");
            cdlbInfo1.setStatus("0");
            List<CdlbInfo> list3 = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo1);
            if (list3 != null && list3.size() == 1) {
                CdlbInfo cdlbInfo2 = list3.get(0);

                for (int i = 0; i <= list.size(); i++) {
                    i = 0;
                    if (list.get(i).getId().equals(cdlbInfo2.getId())) {
                        list.remove(i);
                    }
                    i = 0;
                       *//* if(list.get(i).equals("del"))
                            list.remove(i);*//*
                }
                cdlbInfo2.setRelevance("关联多个");
                cdlbInfo2.setRelevanceIDB(5l); //重新选择
                list.add(cdlbInfo2);
            }

        }*/

        String clientCardIds = "";
        String contractCodes = "";
        for (CdlbInfo info : list) {
            for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

                String s = String.valueOf(info.getId());
                if (s.equals(entry.getKey())) {
                    if (entry.getValue() > 1) {
                        info.setRelevance("关联多个");
                        info.setRelevanceID(1l);
                    }
                    if (entry.getValue() == 1) {
                        if (info.getCdlbId() != null) {
                            info.setRelevance("成功");
                            info.setRelevanceID(2l);
                        } else {
                            info.setRelevance("未关联");
                            info.setRelevanceID(3l);
                        }
                    }
                }
            }
            clientCardIds += info.getClientCardId() + ",";
            if (StringUtils.isNotEmpty(info.getContractCode())) {
                contractCodes += info.getContractCode() + ",";
            }
        }
        String applyFlag = cdlbInOutApply.getApplyFlag();
        //查看是否有重复
        List<CdlbInfo> cardIdCounts = new ArrayList<>();
        List<CdlbInfo> coCodes = new ArrayList<>();
        if (clientCardIds.length() > 0) {
            clientCardIds = clientCardIds.substring(0, clientCardIds.length() - 1);
            cardIdCounts = cdlbInfoService.selectClientCardIdCounts(clientCardIds, applyFlag);
        }

        if (contractCodes.length() > 0) {
            contractCodes = contractCodes.substring(0, contractCodes.length() - 1);
            coCodes = cdlbInfoService.selectcontractCodeCounts(contractCodes, applyFlag);
        }


        for (CdlbInfo info : list) {
            if (CollectionUtils.isNotEmpty(cardIdCounts)) {
                for (CdlbInfo cardIdCount : cardIdCounts) {
                    if (info.getClientCardId().equals(cardIdCount.getClientCardId()) && cardIdCount.getCounts() > 1) {
                        info.setClientCardIdQuantity(cardIdCount.getCounts());
                        break;
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(coCodes)) {
                for (CdlbInfo coCode : coCodes) {
                    if (info.getContractCode().equals(coCode.getContractCode()) && coCode.getCounts() > 1) {
                        info.setContractCodeQuantity(coCode.getCounts());
                        break;
                    }
                }
            }
        }
        /* List<CdlbInfo> collect12 = list.stream().sorted(Comparator.comparing(CdlbInfo::getId).reversed()).collect(Collectors.toList());*/

        /* return getDataTable(list);*/
        return getDataTableByService(list, Long.valueOf(selectcounts));
    }


    /**
     * 选择借据
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/listxvanzejiejv")
    public TableDataInfo listxvanzejiejv(CdlbInfo cdlbInfo) {

        cdlbInfo.setStatus("0");
        //选择借据
  /*      CdlbLoanInfo cdlbLoanInfo = new CdlbLoanInfo();
        cdlbLoanInfo.setId(cdlbInfo.getLoanId());
        cdlbLoanInfo.setCdlbBinding("Y");
        cdlbLoanInfo.setCdlbId(cdlbInfo.getCdlbId());
        int i = cdlbLoanInfoService.updateCdlbLoanInfo(cdlbLoanInfo);

*/

        if (StringUtils.isNotEmpty(cdlbInfo.getIds())) {
            int length = cdlbInfo.getIds().length();
            String substring = cdlbInfo.getIds().substring(0, length - 1);
            cdlbInfo.setIds(substring);
            String[] split = substring.split(",");
            Set<String> strings = new HashSet<>();
            for (int i = 0; i < split.length; i++) {
                strings.add(split[i]);
            }

        }
        if (StringUtils.isNotEmpty(cdlbInfo.getLoanNos())) {
            int length = cdlbInfo.getLoanNos().length();
            String substring = cdlbInfo.getLoanNos().substring(0, length - 1);
            cdlbInfo.setLoanNos(substring);
        }
        if (StringUtils.isNotEmpty(cdlbInfo.getLoanIds())) {
            int length = cdlbInfo.getLoanIds().length();
            String substring = cdlbInfo.getLoanIds().substring(0, length - 1);
            cdlbInfo.setLoanIds(substring);
        }
        List<CdlbInfo> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(cdlbInfo.getIds())) {
            List<CdlbInfo> list1 = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo);
            list.addAll(list1);
        }
        if (StringUtils.isNotEmpty(cdlbInfo.getLvbenandjiejvmap())){
        Map<String, Integer> map = (Map<String, Integer>) JSON.parse(cdlbInfo.getLvbenandjiejvmap());
        Set<Map.Entry<String, Integer>> en = map.entrySet();
        for (Map.Entry<String, Integer> entry : en) {
            cdlbInfo.setIds(null);
            cdlbInfo.setLoanNos(null);
            cdlbInfo.setLoanIds(null);
            /*  cdlbInfo.setLoanNonull("null");*/
            cdlbInfo.setCdlbId(Long.valueOf(entry.getKey()));
            cdlbInfo.setLoanId(Long.valueOf(entry.getValue()));
            List<CdlbInfo> list33 = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo);
            list.addAll(list33);
           /* String key=entry.getKey();
            Integer value=entry.getValue();*/

        }
        }
        Map<String, String> stringStringMap = custName();

        List<String> longs = new ArrayList<>();
        Set<String> longs1 = new HashSet<>();
        String lbinfoid = "s";
        for (CdlbInfo info : list) {
            if (StringUtils.isNotEmpty(info.getCustNo())) {
                /*SysDictTypeData label = getLabel(guaranteeNameV, info.getCustNo());
                if (label != null && StringUtils.isNotEmpty(label.getDictName())) {
                    info.setCustName(label.getDictLabel());
                }*/
                if (stringStringMap != null && StringUtils.isNotEmpty(stringStringMap.get(info.getCustNo()))) {
                    info.setCustName( stringStringMap.get(info.getCustNo()));
                }
            }
            longs.add(String.valueOf(info.getId()));
            longs1.add(String.valueOf(info.getId()));
            String s = String.valueOf(info.getId());
            if (!lbinfoid.equals(s)){
                List<Paramtwo> strings = fileName(info.getId());
                info.setFileNameList(strings);
                lbinfoid =String.valueOf(info.getId());
            }
        }
        Map<String, Integer> stringIntegerMap = new HashMap<>();
        for (String aLong : longs1) {
            Integer a = 0;
            for (String aLong1 : longs) {
                if (aLong.equals(aLong1)) {
                    a++;
                    stringIntegerMap.put(aLong, a);
                }
            }
        }

        for (CdlbInfo info : list) {
            for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

                String s = String.valueOf(info.getId());
                if (s.equals(entry.getKey())) {
                    if (entry.getValue() > 1) {
                        info.setRelevance("关联多个");
                        info.setRelevanceID(1l);
                    }
                    if (entry.getValue() == 1) {
                        if (info.getCdlbId() != null) {
                            info.setRelevance("成功");
                            info.setRelevanceID(2l);
                        } else {
                            info.setRelevance("未关联");
                            info.setRelevanceID(3l);
                        }
                    }
                }
            }
        }
    /*    objects.add("11");//待入库
        objects.add("20");//待审核
        objects.add("21");//待出库
          objects.add("12");//已入库*/

        List<CdlbInfo> collect12 = list.stream().sorted(Comparator.comparing(CdlbInfo::getId)).collect(Collectors.toList());
        return getDataTable(collect12);
    }

    /**
     * 查询车贷绿本信息列表
     */
    /*@PreAuthorize("@ss.hasPermi('system:info:list')")*/
    @GetMapping("/leftlisthb")
    public TableDataInfo leftlisthb(CdlbInfo cdlbInfo) {

        cdlbInfo.setStatus("0");

        if (StringUtils.isNotEmpty(cdlbInfo.getIds())) {
            int length = cdlbInfo.getIds().length();
            String substring = cdlbInfo.getIds().substring(0, length - 1);
            cdlbInfo.setIds(substring);
        }
        if (StringUtils.isNotEmpty(cdlbInfo.getLoanNos())) {
            int length = cdlbInfo.getLoanNos().length();
            String substring = cdlbInfo.getLoanNos().substring(0, length - 1);
            cdlbInfo.setLoanNos(substring);
        }
        if (StringUtils.isNotEmpty(cdlbInfo.getLoanIds())) {
            int length = cdlbInfo.getLoanIds().length();
            String substring = cdlbInfo.getLoanIds().substring(0, length - 1);
            cdlbInfo.setLoanIds(substring);
        }
        List<CdlbInfo> list = new ArrayList<>();
        List<CdlbInfo> list1 = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo);
        cdlbInfo.setLoanNos(null);
        cdlbInfo.setLoanIds(null);
        cdlbInfo.setLoanNonull("null");
        List<CdlbInfo> list2 = cdlbInfoService.selectCdlbInfoListJoinlian(cdlbInfo);
        list.addAll(list1);
        list.addAll(list2);
        List<String> longs = new ArrayList<>();
        Set<String> longs1 = new HashSet<>();
        Map<String, String> stringStringMap = custName();
        for (CdlbInfo info : list) {
            if (StringUtils.isNotEmpty(info.getCustNo())) {
            /*    SysDictTypeData label = getLabel(guaranteeNameV, info.getCustNo());
                if (label != null && StringUtils.isNotEmpty(label.getDictName())) {
                    info.setCustName(label.getDictLabel());
                }*/
                if (stringStringMap != null && StringUtils.isNotEmpty(stringStringMap.get(info.getCustNo()))) {
                    info.setCustName( stringStringMap.get(info.getCustNo()));
                }
            }
            longs.add(String.valueOf(info.getId()));
            longs1.add(String.valueOf(info.getId()));
        }
        Map<String, Integer> stringIntegerMap = new HashMap<>();
        for (String aLong : longs1) {
            Integer a = 0;
            for (String aLong1 : longs) {
                if (aLong.equals(aLong1)) {
                    a++;
                    stringIntegerMap.put(aLong, a);
                }
            }
        }

        for (CdlbInfo info : list) {
            for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {

                String s = String.valueOf(info.getId());
                if (s.equals(entry.getKey())) {
                    if (entry.getValue() > 1) {
                        info.setRelevance("关联多个");
                        info.setRelevanceID(1l);
                    }
                    if (entry.getValue() == 1) {
                        if (info.getCdlbId() != null) {
                            info.setRelevance("成功");
                            info.setRelevanceID(2l);
                        } else {
                            info.setRelevance("未关联");
                            info.setRelevanceID(3l);
                        }
                    }
                }
            }
        }
    /*    objects.add("11");//待入库
        objects.add("20");//待审核
        objects.add("21");//待出库
          objects.add("12");//已入库*/

        List<CdlbInfo> collect12 = list.stream().sorted(Comparator.comparing(CdlbInfo::getId).reversed()).collect(Collectors.toList());
        return getDataTable(collect12);
    }

    /**
     * 新增车贷绿本信息
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:add')")*/
    @Log(title = "车贷绿本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CdlbInfo cdlbInfo) {
        return toAjax(cdlbInfoService.insertCdlbInfo(cdlbInfo));
    }

    /**
     * 修改车贷绿本信息
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:edit')")*/
    @Log(title = "车贷绿本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CdlbInfo cdlbInfo) {
        Long id = cdlbInfo.getId();
        CdlbInfo cdlbInfo1 = cdlbInfoService.selectCdlbInfoById(id);
        //出入库记录 counts
        CdlbInOutApply cdlbInOutApply = cdlbInOutApplyService.selectCdlbInOutApplyById(cdlbInfo1.getProjectInId());
        long l = cdlbInOutApply.getCounts() - 1;
        CdlbInOutApply cdlbInOutApply1 = new CdlbInOutApply();
        cdlbInOutApply1.setId(cdlbInOutApply.getId());
        cdlbInOutApply1.setCounts(l);
        cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply1);
        return toAjax(cdlbInfoService.updateCdlbInfo(cdlbInfo));
    }

    /**
     * 修改申请状态
     */
    /* @PreAuthorize("@ss.hasPermi('system:info:edit')")*/
    @Log(title = "修改申请记录代办事项", businessType = BusinessType.UPDATE)
    @PutMapping("/updatetn")
    public AjaxResult updatetn(@RequestBody CdlbInOutApply cdlbInOutApply) {


        Long applyID = cdlbInOutApply.getId();  //申请id
        String ApplyFlag = cdlbInOutApply.getApplyFlag(); // 申请状态
        TopNotify topNotify1 = new TopNotify();
        topNotify1.setApplyId(applyID);
        topNotify1.setCdlbStatus(ApplyFlag);
        List<TopNotify> topNotifies = iTopNotifyService.selectTopNotifyListNotUser(topNotify1);
        if (CollectionUtils.isNotEmpty(topNotifies)) {
            TopNotify topNotify = new TopNotify();
            topNotify.setApplyId(applyID);
            topNotify.setCdlbStatus(ApplyFlag);
            topNotify.setNotifyType("0");
            topNotify.setViewFlag("1");
            topNotify.setUpdateTime(new Date());
            topNotify.setUpdateBy(getUsername());
            return AjaxResult.success(iTopNotifyService.updateTopNotifyIOA(topNotify));
        } else {
            return AjaxResult.success("暂无修改的事项");
        }
        /*     (notify_type = '0',view_flag = '1')*/
    }

    /**
     * 删除车贷绿本信息
     */
    /*  @PreAuthorize("@ss.hasPermi('system:info:remove')")*/
    @Log(title = "车贷绿本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(cdlbInfoService.deleteCdlbInfoByIds(ids));
    }


    /**
     * 查询标签
     *
     * @param assetNameV
     * @param assetName
     * @return
     */

    public SysDictTypeData getLabel(String assetNameV, String assetName) {
        SysDictTypeData dictType = new SysDictTypeData();
        dictType.setDictName(assetNameV);
        /*   dictType.setDictLabel(assetName);*/
        dictType.setDictValue(assetName);
        dictType.setStatus("0");
        dictType.setStatusd("0");
        SysDictTypeData assetNameLabel = dictTypeService.selectDictDictValuecd(dictType);
        if (assetNameLabel == null) {
            return null;
        } else {
            return assetNameLabel;
        }
    }

    /**
     * 是否为中文
     *
     * 应需求改为仅判断是否为空
     * @param args
     * @return
     */
    public boolean zhongwen(String args) {

//        String reg = "^[\\u4E00-\\u9FA5]{2,20}$";
//        Pattern pattern = Pattern.compile(reg);
//        Matcher matcher = pattern.matcher(args);
//        return matcher.matches();
        boolean b = true;
        if(null == args || args.equals("")){
            b = false;
        }
        return b;
    }


    /**
     * 绿本状态信息表
     * 入库相关申请状态流程
     *
     * @param id                   绿本表id
     * @param remark               入库申请说明
     * @param code                 绿本对应状态
     * @param caozuoren            操作人 ： 录入人   申请人  登记人
     * @param caozuofangshishijian 操作日期名称 ：录入日期   申请日期
     * @param caozuoshuoming       操作说明  ： 入库申请说明
     * @param garageState          出入库状态  01出库，02入库
     * @return
     */
    public int setcdlbInfoDynamicAA(Long id, String garageState, String caozuoren, String caozuofangshishijian, String caozuoshuoming, String remark, String code) {

        CdlbInfoDynamic cdlbInfoDynamic = new CdlbInfoDynamic();
        cdlbInfoDynamic.setInfoId(id);
        cdlbInfoDynamic.setGarageState(garageState);//（01出库，02入库）
        cdlbInfoDynamic.setDynamicTitle(caozuoren);
        cdlbInfoDynamic.setOperId(getLoginUser().getUserId());
        cdlbInfoDynamic.setOperName(getLoginUser().getUser().getNickName());
        cdlbInfoDynamic.setDynamicTimeTitle(caozuofangshishijian);
        cdlbInfoDynamic.setDynamicTime(new Date());
        cdlbInfoDynamic.setDynamicRemarkTitle(caozuoshuoming);
        cdlbInfoDynamic.setRemark(remark);
        /*出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回 20出库申请21出库审核22出库登记23出库完成29出库驳回*/
        cdlbInfoDynamic.setApplyFlag(code);
        return cdlbInfoDynamicService.insertCdlbInfoDynamic(cdlbInfoDynamic);
    }

    /**
     * //车贷绿本动态表
     * 出入库相关申请状态流程
     *
     * @param id                   绿本表id
     * @param remark               入库申请说明
     * @param code                 绿本对应状态
     * @param caozuoren            操作人 ： 录入人   申请人  登记人
     * @param caozuofangshishijian 操作日期名称 ：录入日期   申请日期
     * @param caozuoshuoming       操作说明  ： 入库申请说明
     *                             出入库状态  01出库，02入库
     * @param applyId              车贷申请表id
     * @return
     */
    public int setcdlbInfoDynamicBA(Long id, String remark, String code, String caozuoren, String caozuofangshishijian, String caozuoshuoming, Long applyId, String garageState) {

        //车贷动态表
        CdlbProjectDynamic cdlbProjectDynamic = new CdlbProjectDynamic();
        cdlbProjectDynamic.setProjectId(id);
        cdlbProjectDynamic.setDynamicTitle(caozuoren);
        cdlbProjectDynamic.setGarageState(garageState);//（01出库，02入库）
        cdlbProjectDynamic.setOperId(getLoginUser().getUserId());
        cdlbProjectDynamic.setOperName(getLoginUser().getUser().getNickName());
        cdlbProjectDynamic.setDynamicTimeTitle(caozuofangshishijian);
        cdlbProjectDynamic.setDynamicTime(new Date());
        cdlbProjectDynamic.setDynamicRemarkTitle(caozuoshuoming);
        cdlbProjectDynamic.setRemark(remark);
        /*出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回 20出库申请21出库审核22出库登记23出库完成29出库驳回*/
        cdlbProjectDynamic.setApplyFlag(code);
        cdlbProjectDynamic.setApplyId(applyId);
        return cdlbProjectDynamicService.insertCdlbProjectDynamic(cdlbProjectDynamic);

    }


    /**
     * @param applyid       申请id
     * @param ApplyState    申请状态
     * @param disposeUserID 用户id
     * @param NotifyMsg     通知说明
     * @param url           跳转地址
     * @return
     */
    public int insertNotify(Long applyid, String ApplyState, Long disposeUserID, String NotifyMsg, String url) {
        //代办通知
        TopNotify topNotify = new TopNotify();
        topNotify.setStatus("0");
        topNotify.setNotifyType("1");
        topNotify.setViewFlag("0");
        //对应用户
        topNotify.setDisposeUser(disposeUserID);
        topNotify.setNotifyMsg(NotifyMsg);
        topNotify.setCreateBy(getUsername());
        topNotify.setCreateTime(new Date());
        topNotify.setUpdateTime(new Date());
        topNotify.setButtonType("0");
        //必填
        topNotify.setProjectId(0L);   // 这里为申请id
        topNotify.setIncomeId(0l);


        topNotify.setApplyId(applyid);
        topNotify.setCdlbStatus(ApplyState);

        topNotify.setUrl(url);
        topNotify.setNotifyModule("车贷绿本");
        return iTopNotifyService.insertTopNotify(topNotify);
       /* //添加关联关系
        CdlbIoaTp cdlbIoaTp = new CdlbIoaTp();
        cdlbIoaTp.setApplyId(applyid);
        cdlbIoaTp.setNotifyId(topNotify.getId());
        cdlbIoaTp.setApplyState(ApplyState);
        return   cdlbIoaTpService.insertCdlbIoaTp(cdlbIoaTp);*/

    }
/*         topNotify.setNotifyType("1");
            topNotify.setPhaseStatus("0");
            topNotify.setViewFlag("0");*/
//    二期优化
//    详情导出


    /**
     * 入库详情导出
     *
     * @param response
     * @param cdlbInfo
     * @throws IOException
     */
    @Log(title = "车贷绿本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CdlbInfo cdlbInfo) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        cdlbInfo.setStatus("0");
        List<CdlbInfo> list = new ArrayList<>();
        if (cdlbInfo.getProjectId() != null) {
            ArrayList<String> objects = new ArrayList<>();
            objects.add("11");//待入库
            objects.add("12");//已入库
            objects.add("20");//待审核
            objects.add("21");//待出库
            cdlbInfo.setLbFlagList(objects);
            list = cdlbInfoService.selectCdlbInfoListxiangqing(cdlbInfo);
        }else {
            list = cdlbInfoService.selectCdlbInfoList(cdlbInfo);
        }
        List<List> exportData = new ArrayList<>();

        Map<String, String> sHashMap = custName();
       /* List<CdlbLoanInfo> cdlbLoanInfos1 = cdlbLoanInfoService.selectCdlbLoanInfos(cdlbLoanInfo);*/
        cdlbInfo.setAs(null);
        cdlbInfo.setBs(null);
        List<CdlbInfo> cdlbLoanInfos1 = cdlbInfoService.selectCdlbInfoListJoinlianxiangqing(cdlbInfo);
        List<CdlbInfo> cdlbLoanInfos2 = new ArrayList<>();
        if (cdlbInfo.getProjectId() != null) {
            cdlbLoanInfos2 = cdlbInfoService.selectCdlbInfoListJoinlianchuxiangqing(cdlbInfo);
        }
        for (CdlbInfo info : list) {
            info.getClientCardId();
            List<CdlbInfo> cdlbLoanInfos = new ArrayList<>();
            if (info.getLbFlag().equals("20")||info.getLbFlag().equals("21")){
                String s1 = info.getId().toString();
                List<CdlbInfo> collect = cdlbLoanInfos2.stream().filter(s -> s.getId().toString().equals(s1)).collect(Collectors.toList());
                CdlbInfo cdlbL = collect.get(0);
                cdlbLoanInfos.add(cdlbL);

            }else {
                cdlbLoanInfos = cdlbLoanInfos1.stream().filter(s -> s.getId().equals(info.getId())).collect(Collectors.toList());
            }

            if (CollectionUtils.isNotEmpty(cdlbLoanInfos)) {
                if (cdlbLoanInfos.size() > 1) {
                    info.setRelevance("关联多个");
                }
                if (cdlbLoanInfos.size() == 1) {
                    info.setRelevance("关联成功");
                }
            } else {
                info.setRelevance("未关联");
            }
            String aa = "";
            String bb = "";
            String cc = "";
            String dd = "";
            String ee = "";
            for (CdlbInfo loanInfo : cdlbLoanInfos) {
                if (StringUtils.isEmpty(loanInfo.getLoanNo())) {
                    aa += "" + "\n";
                } else {
                    aa += loanInfo.getLoanNo() + " \n";
                }
                if (loanInfo.getApplyTime() == null) {
                    bb += "" + "\n";
                } else {
                    bb += sdf.format(loanInfo.getApplyTime()) + "\n";
                }
                if (loanInfo.getLoanAmt() == null) {
                    cc += "" + "\n";
                } else {
                    cc += loanInfo.getLoanAmt().toString() + "\n";
                }

                if (StringUtils.isNotEmpty(loanInfo.getCustNo())) {
                    if (sHashMap != null && StringUtils.isNotEmpty(sHashMap.get(loanInfo.getCustNo()))) {
                        dd +=   sHashMap.get(loanInfo.getCustNo()) + "\n";
                    } else {
                        dd += "" + "\n";
                    }
                } else {
                    dd += "" + "\n";
                }
                if (loanInfo.getRegisterTime() == null) {
                    ee += "" + "\n";
                } else {
                    ee += sdf.format(loanInfo.getRegisterTime()) + "\n";
                }
            }

            if (StringUtils.isNotEmpty(aa)) {
                aa = aa.substring(0, aa.length() - 1);
            }
            if (StringUtils.isNotEmpty(bb)) {
                bb = bb.substring(0, bb.length() - 1);
            }
            if (StringUtils.isNotEmpty(cc)) {
                cc = cc.substring(0, cc.length() - 1);
            }
            if (StringUtils.isNotEmpty(dd)) {
                dd = dd.substring(0, dd.length() - 1);
            }
            if (StringUtils.isNotEmpty(ee)) {
                ee = ee.substring(0, ee.length() - 1);
            }

            List Id = new ArrayList();
            Id.add(info.getId());
            if (cdlbInfo.getProjectId() != null) {
                Id.add(info.getLbNumber());
            }
            Id.add(CdStatusList.getSymbolEnumInfo(info.getLbFlag()));
            Id.add(info.getContractCode());
            Id.add(info.getClientName());
            Id.add(info.getClientCardId());
            Id.add(sdf.format(info.getMailDate()));
            Id.add(info.getRelevance());
            Id.add(aa);
            Id.add(bb);
            Id.add(cc);
            Id.add(dd);
            Id.add(ee);
            exportData.add(Id);
        }
        List columns = new ArrayList<>();//标头
        columns.add("序号");
        if (cdlbInfo.getProjectId() != null) {
            columns.add("绿本编号");
        }
        columns.add("绿本状态");
        columns.add("合同编号");
        columns.add("客户姓名");
        columns.add("身份证号码");
        columns.add("邮寄日期");
        columns.add("关联借据情况");
        columns.add("车贷借据编号");
        columns.add("借款日期");
        columns.add("借款金额");
        columns.add("借据担保公司");
        columns.add("入库登记日期");
        String fileName = "";//文件名字
        String sheetName = "sheet1";//sheet名字
        List mergeIndex = new ArrayList();
        int flag = CdlbExcelUtils.exportToExcelForXlsx(response, exportData, fileName, sheetName, columns, mergeIndex);
        System.out.println(flag);

    }
    /**
     * 出库详情导出
     *
     * @param response
     * @param cdlbInfo
     * @throws IOException
     */
    @Log(title = "车贷绿本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportout")
    public void exportout(HttpServletResponse response, CdlbInfo cdlbInfo) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        cdlbInfo.setStatus("0");
        List<List> exportData = new ArrayList<>();

        Map<String, String> sHashMap = custName();
        cdlbInfo.setAs(null);
        cdlbInfo.setBs(null);

        List<CdlbInfo>  cdlbLoanInfos2 = cdlbInfoService.selectCdlbInfoListJoinlianchuxiangqing(cdlbInfo);

        for (CdlbInfo info : cdlbLoanInfos2) {
                if ( Objects.nonNull(info.getLoanId())) {
                    info.setRelevance("关联成功");
                } else {
                info.setRelevance("未关联");
            }
            String aa = "";
            String bb = "";
            String cc = "";
            String dd = "";
            String ee = "";

                if (StringUtils.isEmpty(info.getLoanNo())) {
                    aa += "" + "\n";
                } else {
                    aa += info.getLoanNo() + " \n";
                }
                if (info.getApplyTime() == null) {
                    bb += "" + "\n";
                } else {
                    bb += sdf.format(info.getApplyTime()) + "\n";
                }
                if (info.getLoanAmt() == null) {
                    cc += "" + "\n";
                } else {
                    cc += info.getLoanAmt().toString() + "\n";
                }

                if (StringUtils.isNotEmpty(info.getCustNo())) {
                    if (sHashMap != null && StringUtils.isNotEmpty(sHashMap.get(info.getCustNo()))) {
                        dd +=   sHashMap.get(info.getCustNo()) + "\n";
                    } else {
                        dd += "" + "\n";
                    }
                } else {
                    dd += "" + "\n";
                }
                if (info.getRegisterTime() == null) {
                    ee += "" + "\n";
                } else {
                    ee += sdf.format(info.getRegisterTime()) + "\n";
                }


            if (StringUtils.isNotEmpty(aa)) {
                aa = aa.substring(0, aa.length() - 1);
            }
            if (StringUtils.isNotEmpty(bb)) {
                bb = bb.substring(0, bb.length() - 1);
            }
            if (StringUtils.isNotEmpty(cc)) {
                cc = cc.substring(0, cc.length() - 1);
            }
            if (StringUtils.isNotEmpty(dd)) {
                dd = dd.substring(0, dd.length() - 1);
            }
            if (StringUtils.isNotEmpty(ee)) {
                ee = ee.substring(0, ee.length() - 1);
            }

            List Id = new ArrayList();
            Id.add(info.getId());
            if (cdlbInfo.getProjectId() != null) {
                Id.add(info.getLbNumber());
            }
            Id.add(CdStatusList.getSymbolEnumInfo(info.getLbFlag()));
            Id.add(info.getContractCode());
            Id.add(info.getClientName());
            Id.add(info.getClientCardId());
            Id.add(sdf.format(info.getMailDate()));
            Id.add(info.getRelevance());
            Id.add(aa);
            Id.add(bb);
            Id.add(cc);
            Id.add(dd);
            Id.add(ee);
            exportData.add(Id);
        }
        List columns = new ArrayList<>();//标头
        columns.add("序号");
        columns.add("绿本状态");
        columns.add("合同编号");
        columns.add("客户姓名");
        columns.add("身份证号码");
        columns.add("邮寄日期");
        columns.add("关联借据情况");
        columns.add("车贷借据编号");
        columns.add("借款日期");
        columns.add("借款金额");
        columns.add("借据担保公司");
        columns.add("入库登记日期");
        String fileName = "";//文件名字
        String sheetName = "sheet1";//sheet名字
        List mergeIndex = new ArrayList();
        int flag = CdlbExcelUtils.exportToExcelForXlsx(response, exportData, fileName, sheetName, columns, mergeIndex);
        System.out.println(flag);

    }

    /**
     * 修改车贷绿本信息子状态
     */
    @Log(title = "修改子状态", businessType = BusinessType.UPDATE)
    @PutMapping("/updateSubstate")
    public AjaxResult updateSubstate(@RequestBody CdlbInfo cdlbInfo) {
        cdlbInfo.setStatus("0");
        // 02 入库
        if (StringUtils.isNotEmpty(cdlbInfo.getGarageState()) && cdlbInfo.getGarageState().equals("02")) {
            if (StringUtils.isNotEmpty(cdlbInfo.getTemporaryFlag())) {
                // 子状态已审核 02
                if (cdlbInfo.getTemporaryFlag().equals("02")) {
                    CdlbInfo cdlbInfo1 = new CdlbInfo();
                    cdlbInfo1.setIdList(cdlbInfo.getIdList());
                    List<CdlbInfo> cdlbInfos = cdlbInfoService.selectCdlbInfoList(cdlbInfo1);
                    int aa = 0;
                    String oddNumbers = null;
                    for (CdlbInfo info : cdlbInfos) {
                        if (aa < 1) {
                            String s = cdlbInfoService.selectMaxlbNumber();
                            oddNumbers = GenerateOddNumbersUtil.getOddNumbers(s);
                            aa++;
                            info.setLbNumber(oddNumbers);
                        } else {
                            oddNumbers = GenerateOddNumbersUtil.getOddNumbers(oddNumbers);
                            info.setLbNumber(oddNumbers);
                        }
                        cdlbInfoService.updateCdlbInfo(info);
                    }
                } else {
                    cdlbInfo.setLbNumber("");
                }
            }
        }
        // add by niey 驳回操作，消除代办通知 20241202
        if (cdlbInfo.getTemporaryFlag().equals("03")) {
            Long infoId = cdlbInfo.getIdList().get(0);
            //根据车贷绿本子id查询出库记录主表id
            CdlbInfo cdlbInfoMsg= cdlbInfoService.selectCdlbInfoById(infoId);
            //消除代办
            TopNotify topNotify = new TopNotify();
            topNotify.setStatus("0");
            topNotify.setNotifyType("0");
            topNotify.setViewFlag("1");
            topNotify.setCdlbStatus("20");
            topNotify.setOaApplyId(cdlbInfoMsg.getProjectOutId());
            cdlbInOutApplyService.updateTopNotifyByApplyId(topNotify);
        }
        return toAjax(cdlbInfoService.updateCdlbInfotemporaryFlag(cdlbInfo));
    }

    /**
     * 修改车贷绿本信息子状态
     */

    @Log(title = "查询子状态")
    @GetMapping("/getSonState")
    public TableDataInfo getSonState(CdlbInfo cdlbInfo) {
        cdlbInfo.setStatus("0");
        List<CdlbInfo> sonState = cdlbInfoService.getSonState(cdlbInfo);
        return getDataTable(sonState);
    }

    @Log(title = "查询保存子状态")
    @GetMapping("/getSonStatesSave")
    public TableDataInfo getSonStatesSave(CdlbInfo cdlbInfo) {
        cdlbInfo.setStatus("0");
        List<CdlbInfo> sonState = cdlbInfoService.getSonStatesSave(cdlbInfo);
        return getDataTable(sonState);
    }

    @Log(title = "同步子状态")
    @PostMapping("/updatezFlags")
    public AjaxResult updatezFlags(@RequestBody CdlbInfo cdlbInfo) {
        cdlbInfo.setStatus("0");
        Integer sonState = cdlbInfoService.updatezFlags(cdlbInfo);
        cdlbInfo.setChildFlag("02");
        Integer sonState1 =   cdlbInfoService.updatelbnumber(cdlbInfo);
        return AjaxResult.success(sonState1);
    }

    @Log(title = "保存子状态")
    @PostMapping("/saveupdatezFlags")
    public AjaxResult saveupdatezFlags(@RequestBody CdlbInfo cdlbInfo) {
        cdlbInfo.setStatus("0");
        Integer sonState = cdlbInfoService.saveupdatezFlags(cdlbInfo);
        return AjaxResult.success(sonState);
    }
    //warehouseIntoOk 入库完成

    @Log(title = "绿本登记完成入库同时有驳回数据", businessType = BusinessType.INSERT)
    @PostMapping("/warehouseIntoOkTwo")
    @Transactional
    public AjaxResult warehouseIntoOkTwo(@RequestBody CdlbInfo cdlbInfo) {

        AjaxResult ajaxResult = AjaxResult.success();
        Param param = new Param();
        //申请记录id
        param.setId(cdlbInfo.getId());
        CdlbInfo cdlbInfo1 = new CdlbInfo();
        cdlbInfo1.setProjectInId(cdlbInfo.getId());
        cdlbInfo1.setStatus("0");
        //已审核
        int ysh = 0;
        cdlbInfo1.setChildFlag("02");
        List<CdlbInfo> cdlbInfos2 = cdlbInfoService.selectCdlbInfoList(cdlbInfo1);

        if (CollectionUtils.isNotEmpty(cdlbInfos2)) {
            ysh++;
            param.setCdlbInfos(cdlbInfos2);
            param.setCounts(Long.valueOf(cdlbInfos2.size()));
            param.setRkremark(cdlbInfo.getRkremark());
            ajaxResult = warehouseIntoOk(param);
        }
        //已驳回
        cdlbInfo1.setChildFlag("03");
        List<CdlbInfo> cdlbInfos3 = cdlbInfoService.selectCdlbInfoList(cdlbInfo1);
        if (CollectionUtils.isNotEmpty(cdlbInfos3)) {

            CdlbInOutApply cdlbInOutApply = cdlbInOutApplyService.selectCdlbInOutApplyByIdDto(cdlbInfo.getId());
            cdlbInOutApply.setId(null);
            cdlbInOutApplyService.insertCdlbInOutApply(cdlbInOutApply);

            CdlbProjectDynamic cdlbProjectDynamic = new CdlbProjectDynamic();
            cdlbProjectDynamic.setApplyId(cdlbInfo.getId());
            List<CdlbProjectDynamic> cdlbProjectDynamics = cdlbProjectDynamicService.selectCdlbProjectDynamicList(cdlbProjectDynamic);
            int i = cdlbProjectDynamics.size();
            int aa = 0;
            for (CdlbProjectDynamic projectDynamic : cdlbProjectDynamics) {
                aa++;
                if (ysh == 0) {
                    projectDynamic.setApplyId(cdlbInOutApply.getId());//复制数据，新的id
                    cdlbProjectDynamicService.insertCdlbProjectDynamic(projectDynamic);
                } else if (i > aa) {
                    projectDynamic.setApplyId(cdlbInOutApply.getId());
                    cdlbProjectDynamicService.insertCdlbProjectDynamic(projectDynamic);
                }
            }
            CdlbInfo cdlbInfo4 = new CdlbInfo();
            cdlbInfo4.setProjectInId(cdlbInfo.getId());
            cdlbInfo4.setProjectInIdDto(cdlbInOutApply.getId());
            //已审核
            cdlbInfo4.setChildFlag("03");
            cdlbInfoService.updateCdlbInfoByprojectInIddto(cdlbInfo4);
            ///申请记录id
            param.setId(cdlbInOutApply.getId());
            param.setCdlbInfos(cdlbInfos3);
            param.setCounts(Long.valueOf(cdlbInfos3.size()));
            param.setRkremark(cdlbInfo.getBremark());
            ajaxResult = warehouseIntoBack(param);
        }
        if (ysh == 0) {
            CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
            cdlbInOutApply.setId(cdlbInfo.getId());
            cdlbInOutApply.setStatus("1");
            cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);
        }
        return ajaxResult;
    }

    @Log(title = "出库审核有可能有驳回", businessType = BusinessType.INSERT)
    @PostMapping("/outshenHetongguoTwo")
    @Transactional
    public AjaxResult outshenHetongguoTwo(@RequestBody CdlbInfo cdlbInfo) {

        AjaxResult ajaxResult = AjaxResult.success();
        Param param = new Param();
        //申请记录id
        param.setId(cdlbInfo.getId());
        CdlbInfo cdlbInfo1 = new CdlbInfo();
        cdlbInfo1.setProjectOutId(cdlbInfo.getId());
        cdlbInfo1.setStatus("0");
        //已审核
        int ysh = 0;
        cdlbInfo1.setChildFlag("02");
        List<CdlbInfo> cdlbInfos2 = cdlbInfoService.selectCdlbInfoList(cdlbInfo1);
        if (CollectionUtils.isNotEmpty(cdlbInfos2)) {
            ysh++;
            param.setCdlbInfos(cdlbInfos2);
            param.setCounts(Long.valueOf(cdlbInfos2.size()));
            param.setRkremark(cdlbInfo.getRkremark());
            ajaxResult = outshenHetongguo(param);
        }
        //已驳回
        cdlbInfo1.setChildFlag("03");
        List<CdlbInfo> cdlbInfos3 = cdlbInfoService.selectCdlbInfoList(cdlbInfo1);
        if (CollectionUtils.isNotEmpty(cdlbInfos3)) {

            CdlbInOutApply cdlbInOutApply = cdlbInOutApplyService.selectCdlbInOutApplyByIdDto(cdlbInfo.getId());
            cdlbInOutApply.setId(null);
            cdlbInOutApplyService.insertCdlbInOutApply(cdlbInOutApply);

            CdlbProjectDynamic cdlbProjectDynamic = new CdlbProjectDynamic();
            cdlbProjectDynamic.setApplyId(cdlbInfo.getId());
            List<CdlbProjectDynamic> cdlbProjectDynamics = cdlbProjectDynamicService.selectCdlbProjectDynamicList(cdlbProjectDynamic);
            int i = cdlbProjectDynamics.size();
            int aa = 0;
            for (CdlbProjectDynamic projectDynamic : cdlbProjectDynamics) {
                aa++;
                if (ysh == 0) {
                    projectDynamic.setApplyId(cdlbInOutApply.getId());//复制数据，新的id
                    cdlbProjectDynamicService.insertCdlbProjectDynamic(projectDynamic);
                } else if (i > aa) {
                    projectDynamic.setApplyId(cdlbInOutApply.getId());
                    cdlbProjectDynamicService.insertCdlbProjectDynamic(projectDynamic);
                }
            }
            CdlbInfo cdlbInfo4 = new CdlbInfo();
            cdlbInfo4.setProjectOutId(cdlbInfo.getId());
            cdlbInfo4.setProjectOutIdDto(cdlbInOutApply.getId());
            //已驳回
            cdlbInfo4.setChildFlag("03");
            cdlbInfoService.updateCdlbInfoByprojectInIddto(cdlbInfo4);
            ///申请记录id
            param.setId(cdlbInOutApply.getId());
            param.setCdlbInfos(cdlbInfos3);
            param.setCounts(Long.valueOf(cdlbInfos3.size()));
            param.setRkremark(cdlbInfo.getBremark());
            ajaxResult = outshenHeBoHui(param);
        }
        if (ysh == 0) {
            CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
            cdlbInOutApply.setId(cdlbInfo.getId());
            cdlbInOutApply.setStatus("1");
            cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);
        }
        return ajaxResult;
    }

    @Log(title = "出库成功有可能有驳回", businessType = BusinessType.INSERT)
    @PostMapping("/outokTwo")
    @Transactional
    public AjaxResult outokTwo(@RequestBody CdlbInfo cdlbInfo) {

        AjaxResult ajaxResult = AjaxResult.success();
        Param param = new Param();
        //申请记录id
        param.setId(cdlbInfo.getId());
        CdlbInfo cdlbInfo1 = new CdlbInfo();
        cdlbInfo1.setProjectOutId(cdlbInfo.getId());
        cdlbInfo1.setStatus("0");
        //已审核
        int ysh = 0;
        cdlbInfo1.setChildFlag("02");
        List<CdlbInfo> cdlbInfos2 = cdlbInfoService.selectCdlbInfoList(cdlbInfo1);
        if (CollectionUtils.isNotEmpty(cdlbInfos2)) {
            ysh++;
            param.setCdlbInfos(cdlbInfos2);
            param.setCounts(Long.valueOf(cdlbInfos2.size()));
            param.setRkremark(cdlbInfo.getRkremark());
            ajaxResult = outok(param);
        }
        //已驳回
        cdlbInfo1.setChildFlag("03");
        List<CdlbInfo> cdlbInfos3 = cdlbInfoService.selectCdlbInfoList(cdlbInfo1);
        if (CollectionUtils.isNotEmpty(cdlbInfos3)) {

            CdlbInOutApply cdlbInOutApply = cdlbInOutApplyService.selectCdlbInOutApplyByIdDto(cdlbInfo.getId());
            cdlbInOutApply.setId(null);
            cdlbInOutApplyService.insertCdlbInOutApply(cdlbInOutApply);

            CdlbProjectDynamic cdlbProjectDynamic = new CdlbProjectDynamic();
            cdlbProjectDynamic.setApplyId(cdlbInfo.getId());
            List<CdlbProjectDynamic> cdlbProjectDynamics = cdlbProjectDynamicService.selectCdlbProjectDynamicList(cdlbProjectDynamic);
            int i = cdlbProjectDynamics.size();
            int aa = 0;
            for (CdlbProjectDynamic projectDynamic : cdlbProjectDynamics) {
                aa++;
                if (ysh == 0) {
                    projectDynamic.setApplyId(cdlbInOutApply.getId());//复制数据，新的id
                    cdlbProjectDynamicService.insertCdlbProjectDynamic(projectDynamic);
                } else if (i > aa) {
                    projectDynamic.setApplyId(cdlbInOutApply.getId());
                    cdlbProjectDynamicService.insertCdlbProjectDynamic(projectDynamic);
                }
            }
            CdlbInfo cdlbInfo4 = new CdlbInfo();
            cdlbInfo4.setProjectOutId(cdlbInfo.getId());
            cdlbInfo4.setProjectOutIdDto(cdlbInOutApply.getId());
            //已驳回
            cdlbInfo4.setChildFlag("03");
            cdlbInfoService.updateCdlbInfoByprojectInIddto(cdlbInfo4);
            ///申请记录id
            param.setId(cdlbInOutApply.getId());
            param.setCdlbInfos(cdlbInfos3);
            param.setCounts(Long.valueOf(cdlbInfos3.size()));
            param.setRkremark(cdlbInfo.getBremark());
            ajaxResult = outchukuBH(param);
        }
        if (ysh == 0) {
            CdlbInOutApply cdlbInOutApply = new CdlbInOutApply();
            cdlbInOutApply.setId(cdlbInfo.getId());
            cdlbInOutApply.setStatus("1");
            cdlbInOutApplyService.updateCdlbInOutApply(cdlbInOutApply);
        }
        return ajaxResult;
    }

    /**
     *   获取担保公司名称
     * @return
     */
    public  Map<String,String> custName(){

        SysDictTypeData dictType = new SysDictTypeData();
        dictType.setDictName(guaranteeNameV);
        /*   dictType.setDictLabel(assetName);*/
        dictType.setDictValue(null);
        dictType.setStatus("0");
        dictType.setStatusd("0");
        List<SysDictTypeData> sysDictTypeData = dictTypeService.selectDictDictValuecds(dictType);
        HashMap<String, String> sHashMap = new HashMap<>();
        for (SysDictTypeData sysDictTypeDatum : sysDictTypeData) {
            sHashMap.put(sysDictTypeDatum.getDictValue(),sysDictTypeDatum.getDictLabel());
        }
        return sHashMap;
       /*
         Map<String, String> sHashMap = custName();
        if (sHashMap != null && StringUtils.isNotEmpty(sHashMap.get(info.getCustNo()))) {
            info.setCustName( sHashMap.get(info.getCustNo()));
        }*/

    }

    /**
     *上传
     * 项目下的绿本
     *
     * @param  导入文件
     * @throws Exception
     */
    @Log(title = "上传图片pdf", businessType = BusinessType.IMPORT)
    @PostMapping("/importpicture/{updateId}")
    @Transactional
    public AjaxResult importpicture( List<MultipartFile> file,
                                     @PathVariable("updateId") Long updateId) throws IOException {
        Integer total = 20;
        Integer size = file.size();
        Integer size1 = 0;
        CdlbFiles cdlbFiles1 = new CdlbFiles();
        cdlbFiles1.setCdlbInfoId(updateId);
        List<CdlbFiles> listqw = cdlbFilesService.selectCdlbFilesList(cdlbFiles1);
        if (CollectionUtils.isNotEmpty(listqw)){
             size1 = listqw.size();
        }
        int i1 = total - size1;
        if (i1>0){
            int i = i1 - size;
            if (i<0){
               return AjaxResult.success("附件最大数量限制20");
            }
        }else {
            return AjaxResult.success("附件最大数量限制20");
        }
        for (MultipartFile filea :file) {
            // 获取文件原本的名字
            String originName = filea.getOriginalFilename();
            // 判断文件是否是pdf文件
            Set<String> set = new HashSet<>();
            set.add(".pdf");
            set.add(".jpg");
            set.add(".jpeg");
            set.add(".gif");
            set.add(".png");
            // 取出文件的后缀
            int count = 0;
            for(int i = 0; i < originName.length(); i++){
                if(originName.charAt(i) == '.'){
                    count = i;
                    break;
                }
            }
            String endName = originName.substring(count); //取出文件类型
            if(!set.contains(endName)){
                return  AjaxResult.error("上传的文件类型错误,支持.JPG、.JPEG、 .GIF、 .PNG、 .PDF格式");
            } }
        for (MultipartFile multipartFile : file) {
            // 获取文件原本的名字
            String originName = multipartFile.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.CDLB_SYSTEM, multipartFile);
            CdlbFiles cdlbFiles = new CdlbFiles();
            cdlbFiles.setCdlbInfoId(updateId);
            cdlbFiles.setUrl(url);
            cdlbFiles.setName(originName);
            cdlbFiles.setStatus("0");
            cdlbFiles.setCreateBy(getUsername());
            cdlbFilesService.insertCdlbFiles(cdlbFiles);
        }


        return AjaxResult.success(1);
    }

    /**
     *   获取文件名称集合
     * @param infoId
     * @return
     */
         public   List<Paramtwo> fileName(Long infoId){
            List<Paramtwo> strings = new ArrayList<>();

             CdlbFiles cdlbFiles = new CdlbFiles();
                cdlbFiles.setCdlbInfoId(infoId);
                 cdlbFiles.setStatus("0");
             List<CdlbFiles> cdlbFiles1 = cdlbFilesService.selectCdlbFilesList(cdlbFiles);
             for (CdlbFiles files : cdlbFiles1) {
                 Paramtwo paramtwo = new Paramtwo();
                 paramtwo.setC(files.getId());
                 paramtwo.setB(files.getName());
                 strings.add(paramtwo);
             }
             return strings;
       }
}

/*
@Log(title = "绿本提交出库审核驳回", businessType = BusinessType.INSERT)
@PostMapping("/outshenHeBoHui")

@Log(title = "绿本提交出库出库驳回", businessType = BusinessType.INSERT)
@PostMapping("/outchukuBH")

  @Log(title = "绿本提交出库成功", businessType = BusinessType.INSERT)
    @PostMapping("/outok")
*/
