package org.ruoyi.core.cdlb.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 申请记录关联代办事项对象 cdlb_ioa_tp
 * 
 * <AUTHOR>
 * @date 2023-04-14
 */
public class CdlbIoaTp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 申请表id */
    @Excel(name = "申请表id")
    private Long applyId;
    @Excel(name = "申请状态")
    private String applyState;

    /** 代办事项id */
    @Excel(name = "代办事项id")
    private Long notifyId;

    public String getApplyState() {
        return applyState;
    }

    public void setApplyState(String applyState) {
        this.applyState = applyState;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setApplyId(Long applyId) 
    {
        this.applyId = applyId;
    }

    public Long getApplyId() 
    {
        return applyId;
    }
    public void setNotifyId(Long notifyId) 
    {
        this.notifyId = notifyId;
    }

    public Long getNotifyId() 
    {
        return notifyId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyId", getApplyId())
            .append("notifyId", getNotifyId())
            .toString();
    }
}
