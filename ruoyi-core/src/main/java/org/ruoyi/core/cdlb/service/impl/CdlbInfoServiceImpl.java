package org.ruoyi.core.cdlb.service.impl;


import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.cdlb.domain.CdlbInfo;
import org.ruoyi.core.cdlb.mapper.CdlbInfoMapper;
import org.ruoyi.core.cdlb.mapper.CdlbLoanInfoMapper;
import org.ruoyi.core.cdlb.service.ICdlbInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车贷绿本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-09
 */
@Service
public class CdlbInfoServiceImpl implements ICdlbInfoService {
    @Autowired
    private CdlbInfoMapper cdlbInfoMapper;
    @Autowired
    private CdlbLoanInfoMapper cdlbLoanInfoMapper;

    /**
     * 查询车贷绿本信息
     *
     * @param id 车贷绿本信息主键
     * @return 车贷绿本信息
     */
    @Override
    public CdlbInfo selectCdlbInfoById(Long id) {
        return cdlbInfoMapper.selectCdlbInfoById(id);
    }

    /**
     * 查询车贷绿本信息列表
     *
     * @param cdlbInfo 车贷绿本信息
     * @return 车贷绿本信息
     */
    @Override
    public List<CdlbInfo> selectCdlbInfoList(CdlbInfo cdlbInfo) {
        List<CdlbInfo> cdlbInfos = cdlbInfoMapper.selectCdlbInfoList(cdlbInfo);
        return cdlbInfos;
    }    @Override
    public List<CdlbInfo> selectCdlbInfoListxiangqing(CdlbInfo cdlbInfo) {
        List<CdlbInfo> cdlbInfos = cdlbInfoMapper.selectCdlbInfoListxiangqing(cdlbInfo);
        return cdlbInfos;
    }

    @Override
    public List<CdlbInfo> selectCdlbInfoListJoinlian(CdlbInfo cdlbInfo) {

        List<CdlbInfo> cdlbInfos = cdlbInfoMapper.selectCdlbInfoListJoinlian(cdlbInfo);
        return cdlbInfos;
    }    @Override
    public List<CdlbInfo> selectCdlbInfoListJoinlianxiangqing(CdlbInfo cdlbInfo) {

        List<CdlbInfo> cdlbInfos = cdlbInfoMapper.selectCdlbInfoListJoinlianxiangqing(cdlbInfo);
        return cdlbInfos;
    }
    @Override
    public List<CdlbInfo> selectCdlbInfoListJoinlianchu(CdlbInfo cdlbInfo) {

        List<CdlbInfo> cdlbInfos = cdlbInfoMapper.selectCdlbInfoListJoinlianchu(cdlbInfo);
        return cdlbInfos;
    } @Override
    public long selectCdlbInfoListJoinlianchuForCount(CdlbInfo cdlbInfo) {

        return cdlbInfoMapper.selectCdlbInfoListJoinlianchuForCount(cdlbInfo);

    }    @Override
    public List<CdlbInfo> selectCdlbInfoListJoinlianchuxiangqing(CdlbInfo cdlbInfo) {

        List<CdlbInfo> cdlbInfos = cdlbInfoMapper.selectCdlbInfoListJoinlianchuxiangqing(cdlbInfo);
        return cdlbInfos;
    }
    @Override
    public List<CdlbInfo> selecInfoListstate(String id) {
        List<CdlbInfo> cdlbInfos = cdlbInfoMapper.selecInfoListstate(id);
        return cdlbInfos;
    }

    /**
     * 新增车贷绿本信息
     *
     * @param cdlbInfo 车贷绿本信息
     * @return 结果
     */
    @Override
    public int insertCdlbInfo(CdlbInfo cdlbInfo) {
       /* cdlbInfo.setCreateTime(DateUtils.getNowDate());*/
        return cdlbInfoMapper.insertCdlbInfo(cdlbInfo);
    }

    /**
     * 修改车贷绿本信息
     *
     * @param cdlbInfo 车贷绿本信息
     * @return 结果
     */
    @Override
    public int updateCdlbInfo(CdlbInfo cdlbInfo) {
        cdlbInfo.setUpdateTime(DateUtils.getNowDate());
        return cdlbInfoMapper.updateCdlbInfo(cdlbInfo);
    }    @Override
    public int updateCdlbInfoByprojectInId(CdlbInfo cdlbInfo) {
        cdlbInfo.setUpdateTime(DateUtils.getNowDate());
        return cdlbInfoMapper.updateCdlbInfoByprojectInId(cdlbInfo);
    }
    @Override
    public int updateCdlbInfoByprojectInIddto(CdlbInfo cdlbInfo) {
        cdlbInfo.setUpdateTime(DateUtils.getNowDate());
        return cdlbInfoMapper.updateCdlbInfoByprojectInIddto(cdlbInfo);
    }
    @Override
    public int updateCdlbInfoByprojectoutId(CdlbInfo cdlbInfo) {
        cdlbInfo.setUpdateTime(DateUtils.getNowDate());
        return cdlbInfoMapper.updateCdlbInfoByprojectoutId(cdlbInfo);
    }

    /**
     * 批量删除车贷绿本信息
     *
     * @param ids 需要删除的车贷绿本信息主键
     * @return 结果
     */
    @Override
    public int deleteCdlbInfoByIds(Long[] ids) {
        return cdlbInfoMapper.deleteCdlbInfoByIds(ids);
    }

    /**
     * 删除车贷绿本信息信息
     *
     * @param id 车贷绿本信息主键
     * @return 结果
     */
    @Override
    public int deleteCdlbInfoById(Long id) {
        return cdlbInfoMapper.deleteCdlbInfoById(id);
    }


    @Override
    public Integer selectcounts(Long id) {
        return cdlbInfoMapper.selectcounts(id);
    }
    @Override
    public Integer selectcounts4(CdlbInfo cdlbInfo) {
        return cdlbInfoMapper.selectcounts4(cdlbInfo);
    }    @Override
    public Integer selectcountsruku(Long id) {
        return cdlbInfoMapper.selectcountsruku(id);
    }
    @Override
    public  List<CdlbInfo>  selectClientCardIdCounts(String clientCardIds,String applyFlag) {
        return cdlbInfoMapper.selectClientCardIdCounts(clientCardIds,applyFlag);
    }   @Override
    public  List<CdlbInfo>  selectcontractCodeCounts(String contractCodes,String applyFlag) {
        return cdlbInfoMapper.selectcontractCodeCounts(contractCodes,applyFlag);
    }



    @Override
    public Integer selectcountsinfork(CdlbInfo cdlbInfo) {
        return cdlbInfoMapper.selectcountsinfork(cdlbInfo);
    }
    @Override
    public Integer selectcountsinfock(CdlbInfo cdlbInfo) {
        return cdlbInfoMapper.selectcountsinfock(cdlbInfo);
    }

    @Override
    public Integer updateCdlbInfotemporaryFlag(CdlbInfo cdlbInfo) {
        return cdlbInfoMapper.updateCdlbInfotemporaryFlag(cdlbInfo);
    }
    @Override
    public Integer updatezFlags(CdlbInfo cdlbInfo) {
        return cdlbInfoMapper.updatezFlags(cdlbInfo);
    }    @Override
    public Integer saveupdatezFlags(CdlbInfo cdlbInfo) {
        return cdlbInfoMapper.saveupdatezFlags(cdlbInfo);
    }

    @Override
    public String selectMaxlbNumber() {
        return cdlbInfoMapper.selectMaxlbNumber();

    }

    @Override
    public Integer updatelbnumber(CdlbInfo cdlbInfo) {

        return cdlbInfoMapper.updatelbnumber();

    }

    @Override
    public Long selectCdlbInfoListJoinlianForCount(CdlbInfo cdlbInfo) {
        return cdlbInfoMapper.selectCdlbInfoListJoinlianForCount(cdlbInfo);
    }

    @Override
    public List<CdlbInfo> getSonState(CdlbInfo cdlbInfo) {
        return cdlbInfoMapper.getSonState(cdlbInfo);

    }   @Override
    public List<CdlbInfo> getSonStatesSave(CdlbInfo cdlbInfo) {
        return cdlbInfoMapper.getSonStatesSave(cdlbInfo);

    }


}