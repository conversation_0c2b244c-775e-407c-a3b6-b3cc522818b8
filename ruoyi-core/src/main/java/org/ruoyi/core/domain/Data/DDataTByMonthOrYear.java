package org.ruoyi.core.domain.Data;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 运营情况月（年）报表通用对象
 *
 * <AUTHOR>
 * @date 2023-02-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DDataTByMonthOrYear extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 外部系统平台编码 */
    private String platformNo;

    /** 担保公司编码 */
    private String custNo;

    /** 合作方编码 */
    private String partnerNo;

    /** 资金方编码 */
    private String fundNo;

    /** 产品编码 */
    private String productNo;

    /** 是否映射成功（Y映射成功N映射失败） */
    private String isMapping;

    /** 数据统计时间 */
    @Excel(name = "日期")
    private String reconDate;

    /** 历史累计-累计贷款本金余额（元） */
    @Excel(name = "贷款余额")
    private BigDecimal totalBalanceAmount;

    /** 历史累计-贷款笔数（笔） */
    @Excel(name = "贷款笔数")
    private Long totalCount;

    /** 当期新增-新增贷款本金（元） */
    @Excel(name = "新增贷款本金")
    private BigDecimal addAmount;

    /** 当期新增-新增贷款笔数（笔） */
    @Excel(name = "新增贷款笔数")
    private Long addCount;

    /** 历史累计-累计贷款本金（元） */
    @Excel(name = "累计贷款本金")
    private BigDecimal totalAmount;

    @Excel(name = "累计解保笔数")
    private Long solutionStroke;

    /** 当期新增还款本金 */
    @Excel(name = "新增还款本金")
    private BigDecimal addRepayPrintAmount;

    /** 当期新增还款笔数 */
    @Excel(name = "新增还款笔数")
    private Long addRepayCount;

    /** 累计还款本金 */
    @Excel(name = "累计还款本金")
    private BigDecimal totalRepayPrintAmount;

    /** 累计还款笔数 */
    @Excel(name = "累计还款笔数")
    private Long totalRepayCount;

    List<String> dateRange;

    private Map<String, List<String>> moreSearchMap;

    private String moreSearch;
}
