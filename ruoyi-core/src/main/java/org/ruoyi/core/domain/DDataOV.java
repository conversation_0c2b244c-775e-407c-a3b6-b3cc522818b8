package org.ruoyi.core.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 外部系统平台运营情况数据对象 d_data
 * 
 * <AUTHOR>
 * @date 2022-10-28
 */
public class DDataOV extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 外部系统平台编码 */
    @Excel(name = "外部系统平台编码",dictType = "platform_no")
    private String platformNo;

    /** 担保公司编码 */
    @Excel(name = "担保公司编码",dictType = "cust_no")
    private String custNo;

    /** 合作方编码 */
    @Excel(name = "合作方编码",dictType = "partner_no")
    private String partnerNo;

    /** 资金方编码 */
    @Excel(name = "资金方编码",dictType = "fund_no")
    private String fundNo;

    /** 产品编码 */
    @Excel(name = "产品编码",dictType = "product_no")
    private String productNo;

    /** 是否为画像数据（Y是 N否） */
    @Excel(name = "是否为画像数据", readConverterExp = "Y=是,N=否")
    private String isPortrayal;

//    /** 画像类型 */
//    @Excel(name = "画像类型")
//    private String portrayalType;
//
//    /** 画像编码 */
//    @Excel(name = "画像编码")
//    private String portrayalNo;

    /** 数据统计时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据统计时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reconDate;

    /** 历史累计-数据起算日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "历史累计-数据起算日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date totalBeginDate;

    /** 历史累计-统计截止日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "历史累计-统计截止日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date totalEndDate;

    /** 历史累计-贷款笔数（笔） */
    @Excel(name = "历史累计-贷款笔数")
    private Long totalCount;

    /** 历史累计-贷款人数（人） */
    @Excel(name = "历史累计-贷款人数")
    private Long totalHumanCount;



    /** 历史累计-累计贷款本金（元） */
    @Excel(name = "历史累计-累计贷款本金")
    private BigDecimal totalAmount;

    /** 累计前期服务费 */
    @Excel(name = "累计前期服务费")
    private BigDecimal totalActServiceAmount;

    /** 累计前期担保费 */
    @Excel(name = "累计前期担保费")
    private BigDecimal totalActGuaranteeAmount;

    /** 累计前期保证金 */
    @Excel(name = "累计前期保证金")
    private BigDecimal totalActMarginAmount;

    /** 累计前期代偿金 */
    @Excel(name = "累计前期代偿金")
    private BigDecimal totalActCompensateAmount;

    /** 历史累计-贷款平均IRR（%） */
    @Excel(name = "历史累计-贷款平均IRR")
    private BigDecimal totalAverageIrr;

    /** 历史累计-平均贷款期限（月） */
    @Excel(name = "历史累计-平均贷款期限")
    private BigDecimal totalAverageTerm;

    /** 历史累计-累计贷款本金余额（元） */
    @Excel(name = "历史累计-累计贷款本金余额")
    private BigDecimal totalBalanceAmount;

    /** 历史累计-还款计划贷款本金余额 */
    @Excel(name = "历史累计-还款计划贷款本金余额")
    private BigDecimal totalPlanBalanceAmt;

    /** 历史累计-资金贷款本金余额（元） */
    @Excel(name = "历史累计-资金贷款本金余额")
    private BigDecimal totalFundBalanceAmt;

    /** 历史累计-还款计划资金贷款本金余额（元） */
    @Excel(name = "历史累计-还款计划资金贷款本金余额")
    private BigDecimal totalPlanFundBalanceAmt;

    /** 历史累计-还款计划全部应还-全部已还（元） */
    @Excel(name = "历史累计-还款计划全部应还-全部已还")
    private BigDecimal totalBalanceAmt;

    /** 历史累计-逾期总金额（元） */
    @Excel(name = "历史累计-逾期总金额")
    private BigDecimal overdueTotalAmt;

    /** 历史累计-未结清贷款笔数（笔） */
    @Excel(name = "历史累计-未结清贷款笔数")
    private Long totalUnclearedCount;

    /** 历史累计-代偿笔数（笔） */
    @Excel(name = "历史累计-代偿笔数")
    private Long totalCompensateCount;

    /** 历史累计-代偿总额（元） */
    @Excel(name = "历史累计-代偿总额")
    private BigDecimal totalCompensateAmount;

    /** 历史累计-代偿总本金 */
    @Excel(name = "历史累计-代偿总本金")
    private BigDecimal totalCompensatePrintAmount;

    /** 历史累计-代偿利息（元） */
    @Excel(name = "历史累计-代偿利息")
    private BigDecimal totalCompensateIntAmount;

    /** 历史累计-代偿罚息（元） */
    @Excel(name = "历史累计-代偿罚息")
    private BigDecimal totalCompensateOintAmount;

    /** 历史累计-代偿服务费（元） */
    @Excel(name = "历史累计-代偿服务费")
    private BigDecimal totalCompensateServiceAmount;

    /** 历史累计-代偿担保费（元） */
    @Excel(name = "历史累计-代偿担保费")
    private BigDecimal totalCompensateGuaranteeAmount;

    /** 历史累计-代偿保证金（元） */
    @Excel(name = "历史累计-代偿保证金")
    private BigDecimal totalCompensateMarginAmount;

    /** 历史累计-代偿代偿金（元） */
    @Excel(name = "历史累计-代偿代偿金")
    private BigDecimal totalCompensateCompensateAmount;

    /** 历史累计-代偿逾期违约金（元） */
    @Excel(name = "历史累计-代偿逾期违约金")
    private BigDecimal totalCompensateDefineAmount;

    /** 历史累计-代偿提前还款违约金（元） */
    @Excel(name = "历史累计-代偿提前还款违约金")
    private BigDecimal totalCompensateAdvDefineAmount;

    /** 历史累计-代偿担保费罚息（元） */
    @Excel(name = "历史累计-代偿担保费罚息")
    private BigDecimal totalCompensateOguaranteeAmount;

    /** 累计还款笔数 */
    @Excel(name = "累计还款笔数")
    private Long totalRepayCount;

    /** 累计还款本金 */
    @Excel(name = "累计还款本金")
    private BigDecimal totalRepayPrintAmount;

    /** 累计还款利息 */
    @Excel(name = "累计还款利息")
    private BigDecimal totalRepayIntAmount;

    /** 累计还款罚息 */
    @Excel(name = "累计还款罚息")
    private BigDecimal totalRepayOintAmount;

    /** 累计还款服务费 */
    @Excel(name = "累计还款服务费")
    private BigDecimal totalRepayServiceAmount;

    /** 累计还款担保费 */
    @Excel(name = "累计还款担保费")
    private BigDecimal totalRepayGuaranteeAmount;

    /** 累计还款担保费罚息（元） */
    @Excel(name = "累计还款担保费罚息")
    private BigDecimal totalRepayOguaranteeAmount;

    /** 累计还款保证金 */
    @Excel(name = "累计还款保证金")
    private BigDecimal totalRepayMarginAmount;

    /** 累计还款代偿金 */
    @Excel(name = "累计还款代偿金")
    private BigDecimal totalRepayCompensateAmount;

    /** 累计还款逾期违约金 */
    @Excel(name = "累计还款逾期违约金")
    private BigDecimal totalRepayDefineAmount;

    /** 累计还款提前还款违约金 */
    @Excel(name = "累计还款提前还款违约金")
    private BigDecimal totalRepayAdvDefineAmount;

    /** 累计减免笔数 */
    @Excel(name = "累计减免笔数")
    private Long totalReduceCount;

    /** 累计减免本金 */
    @Excel(name = "累计减免本金")
    private BigDecimal totalReducePrintAmount;

    /** 累计减免利息 */
    @Excel(name = "累计减免利息")
    private BigDecimal totalReduceIntAmount;

    /** 累计减免罚息 */
    @Excel(name = "累计减免罚息")
    private BigDecimal totalReduceOintAmount;

    /** 累计减免服务费 */
    @Excel(name = "累计减免服务费")
    private BigDecimal totalReduceServiceAmount;

    /** 累计减免担保费 */
    @Excel(name = "累计减免担保费")
    private BigDecimal totalReduceGuaranteeAmount;

    /** 累计减免担保费罚息（元） */
    @Excel(name = "累计减免担保费罚息")
    private BigDecimal totalReduceOguaranteeAmount;

    /** 累计减免保证金 */
    @Excel(name = "累计减免保证金")
    private BigDecimal totalReduceMarginAmount;

    /** 累计减免代偿金 */
    @Excel(name = "累计减免代偿金")
    private BigDecimal totalReduceCompensateAmount;

    /** 累计减免逾期违约金 */
    @Excel(name = "累计减免逾期违约金")
    private BigDecimal totalReduceDefineAmount;

    /** 累计减免提前还款违约金 */
    @Excel(name = "累计减免提前还款违约金")
    private BigDecimal totalReduceAdvDefineAmount;

    /** 当期新增-初始起算日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "当期新增-初始起算日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addBeginDate;

    /** 当期新增-统计截止日 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "当期新增-统计截止日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addEndDate;

    /** 当期新增-新增贷款笔数（笔） */
    @Excel(name = "当期新增-新增贷款笔数")
    private Long addCount;

    /** 当期新增-新增贷款人数（人） */
    @Excel(name = "当期新增-新增贷款人数")
    private Long addHumanCount;

    /** 当期新增-新增贷款本金（元） */
    @Excel(name = "当期新增-新增贷款本金")
    private BigDecimal addAmount;

    /** 当期新增前期服务费 */
    @Excel(name = "当期新增前期服务费")
    private BigDecimal addActServiceAmount;

    /** 当期新增前期担保费 */
    @Excel(name = "当期新增前期担保费")
    private BigDecimal addActGuaranteeAmount;

    /** 当期新增前期保证金 */
    @Excel(name = "当期新增前期保证金")
    private BigDecimal addActMarginAmount;

    /** 当期新增前期代偿金 */
    @Excel(name = "当期新增前期代偿金")
    private BigDecimal addActCompensateAmount;

    /** 当期新增-新增贷款本金余额（元） */
    @Excel(name = "当期新增-新增贷款本金余额")
    private BigDecimal addEalanceAmount;

    /** 当期新增-贷款金额中位数（元） */
    @Excel(name = "当期新增-贷款金额中位数")
    private BigDecimal addMedianAmount;

    /** 当期新增-贷款平均IRR（%） */
    @Excel(name = "当期新增-贷款平均IRR")
    private BigDecimal addAverageIrr;

    /** 当期新增-平均贷款期限（月） */
    @Excel(name = "当期新增-平均贷款期限")
    private BigDecimal addAverageTerm;

    /** 当期新增-代偿笔数（笔） */
    @Excel(name = "当期新增-代偿笔数")
    private Long addCompensateCount;

    /** 当期新增-代偿总金额（元） */
    @Excel(name = "当期新增-代偿总金额")
    private BigDecimal addCompensateAmount;

    /** 当期新增-代偿本金（元） */
    @Excel(name = "当期新增-代偿本金")
    private BigDecimal addCompensatePrintAmount;

    /** 当期新增-代偿利息（元） */
    @Excel(name = "当期新增-代偿利息")
    private BigDecimal addCompensateIntAmount;

    /** 当期新增-代偿罚息（元） */
    @Excel(name = "当期新增-代偿罚息")
    private BigDecimal addCompensateOintAmount;

    /** 当期新增-代偿服务费（元） */
    @Excel(name = "当期新增-代偿服务费")
    private BigDecimal addCompensateServiceAmount;

    /** 当期新增-代偿担保费（元） */
    @Excel(name = "当期新增-代偿担保费")
    private BigDecimal addCompensateGuaranteeAmount;

    /** 当期新增-代偿保证金（元） */
    @Excel(name = "当期新增-代偿保证金")
    private BigDecimal addCompensateMarginAmount;

    /** 当期新增-代偿代偿金（元） */
    @Excel(name = "当期新增-代偿代偿金")
    private BigDecimal addCompensateCompensateAmount;

    /** 当期新增-代偿逾期违约金（元） */
    @Excel(name = "当期新增-代偿逾期违约金")
    private BigDecimal addCompensateDefineAmount;

    /** 当期新增-代偿提前还款违约金（元） */
    @Excel(name = "当期新增-代偿提前还款违约金")
    private BigDecimal addCompensateAdvDefineAmount;

    /** 当期新增-代偿担保费罚息（元） */
    @Excel(name = "当期新增-代偿担保费罚息")
    private BigDecimal addCompensateOguaranteeAmount;

    /** 当期新增还款笔数 */
    @Excel(name = "当期新增还款笔数")
    private Long addRepayCount;

    /** 当期新增还款本金 */
    @Excel(name = "当期新增还款本金")
    private BigDecimal addRepayPrintAmount;

    /** 当期新增还款利息 */
    @Excel(name = "当期新增还款利息")
    private BigDecimal addRepayIntAmount;

    /** 当期新增还款罚息 */
    @Excel(name = "当期新增还款罚息")
    private BigDecimal addRepayOintAmount;

    /** 当期新增还款服务费 */
    @Excel(name = "当期新增还款服务费")
    private BigDecimal addRepayServiceAmount;

    /** 当期新增还款担保费 */
    @Excel(name = "当期新增还款担保费")
    private BigDecimal addRepayGuaranteeAmount;

    /** 当期新增-还款担保费罚息（元） */
    @Excel(name = "当期新增-还款担保费罚息")
    private BigDecimal addRepayOguaranteeAmount;

    /** 当期新增还款保证金 */
    @Excel(name = "当期新增还款保证金")
    private BigDecimal addRepayMarginAmount;

    /** 当期新增还款代偿金 */
    @Excel(name = "当期新增还款代偿金")
    private BigDecimal addRepayCompensateAmount;

    /** 当期新增还款逾期违约金 */
    @Excel(name = "当期新增还款逾期违约金")
    private BigDecimal addRepayDefineAmount;

    /** 当期新增还款提前还款违约金 */
    @Excel(name = "当期新增还款提前还款违约金")
    private BigDecimal addRepayAdvDefineAmount;

    /** 当期新增减免笔数 */
    @Excel(name = "当期新增减免笔数")
    private Long addReduceCount;

    /** 当期新增减免本金 */
    @Excel(name = "当期新增减免本金")
    private BigDecimal addReducePrintAmount;

    /** 当期新增减免利息 */
    @Excel(name = "当期新增减免利息")
    private BigDecimal addReduceIntAmount;

    /** 当期新增减免罚息 */
    @Excel(name = "当期新增减免罚息")
    private BigDecimal addReduceOintAmount;

    /** 当期新增减免服务费 */
    @Excel(name = "当期新增减免服务费")
    private BigDecimal addReduceServiceAmount;

    /** 当期新增减免担保费 */
    @Excel(name = "当期新增减免担保费")
    private BigDecimal addReduceGuaranteeAmount;

    /** 当期新增-还款减免担保费罚息（元） */
    @Excel(name = "当期新增-还款减免担保费罚息")
    private BigDecimal addReduceOguaranteeAmount;

    /** 当期新增减免保证金 */
    @Excel(name = "当期新增减免保证金")
    private BigDecimal addReduceMarginAmount;

    /** 当期新增减免代偿金 */
    @Excel(name = "当期新增减免代偿金")
    private BigDecimal addReduceCompensateAmount;

    /** 当期新增减免逾期违约金 */
    @Excel(name = "当期新增减免逾期违约金")
    private BigDecimal addReduceDefineAmount;

    /** 当期新增减免提前还款违约金 */
    @Excel(name = "当期新增减免提前还款违约金")
    private BigDecimal addReduceAdvDefineAmount;

    /** 当期新增-追偿还款笔数 */
    @Excel(name = "当期新增-追偿还款笔数")
    private Integer addRepay8Count;

    /** 当期新增-追偿还款总金额（元） */
    @Excel(name = "当期新增-追偿还款总金额")
    private BigDecimal addRepay8Amount;

    /** 当期新增-追偿还款本金（元） */
    @Excel(name = "当期新增-追偿还款本金")
    private BigDecimal addRepay8PrinAmount;

    /** 当期新增-追偿还款利息（元） */
    @Excel(name = "当期新增-追偿还款利息")
    private BigDecimal addRepay8IntAmount;

    /** 当期新增-追偿还款罚息（元） */
    @Excel(name = "当期新增-追偿还款罚息")
    private BigDecimal addRepay8OintAmount;

    /** 当期新增-追偿还款服务费（元） */
    @Excel(name = "当期新增-追偿还款服务费")
    private BigDecimal addRepay8ServiceAmount;

    /** 当期新增-追偿还款担保费（元） */
    @Excel(name = "当期新增-追偿还款担保费")
    private BigDecimal addRepay8GuaranteeAmount;

    /** 当期新增-追偿还款保证金（元） */
    @Excel(name = "当期新增-追偿还款保证金")
    private BigDecimal addRepay8MarginAmount;

    /** 当期新增-追偿还款代偿金（元） */
    @Excel(name = "当期新增-追偿还款代偿金")
    private BigDecimal addRepay8CompensateAmount;

    /** 当期新增-追偿还款逾期违约金（元） */
    @Excel(name = "当期新增-追偿还款逾期违约金")
    private BigDecimal addRepay8DefineAmount;

    /** 当期新增-追偿还款提前还款违约金（元） */
    @Excel(name = "当期新增-追偿还款提前还款违约金")
    private BigDecimal addRepay8AdvDefineAmount;

    /** 当期新增-追偿还款担保费罚息（元） */
    @Excel(name = "当期新增-追偿还款担保费罚息")
    private BigDecimal addRepay8OguaranteeAmount;

    /** 当期新增-代偿还款减免总金额（元） */
    @Excel(name = "当期新增-代偿还款减免总金额")
    private BigDecimal addReduce7Amount;

    /** 当期新增-代偿还款减免本金（元） */
    @Excel(name = "当期新增-代偿还款减免本金")
    private BigDecimal addReduce7PrinAmount;

    /** 当期新增-代偿还款减免利息（元） */
    @Excel(name = "当期新增-代偿还款减免利息")
    private BigDecimal addReduce7IntAmount;

    /** 当期新增-代偿还款减免罚息（元） */
    @Excel(name = "当期新增-代偿还款减免罚息")
    private BigDecimal addReduce7OintAmount;

    /** 当期新增-代偿还款减免服务费（元） */
    @Excel(name = "当期新增-代偿还款减免服务费")
    private BigDecimal addReduce7ServiceAmount;

    /** 当期新增-代偿还款减免担保费（元） */
    @Excel(name = "当期新增-代偿还款减免担保费")
    private BigDecimal addReduce7GuaranteeAmount;

    /** 当期新增-代偿还款减免担保费罚息（元） */
    @Excel(name = "当期新增-代偿还款减免担保费罚息")
    private BigDecimal addReduce7OguaranteeAmount;

    /** 当期新增-代偿还款减免保证金（元） */
    @Excel(name = "当期新增-代偿还款减免保证金")
    private BigDecimal addReduce7MarginAmount;

    /** 当期新增-代偿还款减免代偿金（元） */
    @Excel(name = "当期新增-代偿还款减免代偿金")
    private BigDecimal addReduce7CompensateAmount;

    /** 当期新增-代偿还款减免逾期违约金（元） */
    @Excel(name = "当期新增-代偿还款减免逾期违约金")
    private BigDecimal addReduce7DefineAmount;

    /** 当期新增-代偿还款减免提前还款违约金（元） */
    @Excel(name = "当期新增-代偿还款减免提前还款违约金")
    private BigDecimal addReduce7AdvDefineAmount;

    /** 当期新增-追偿还款减免总金额（元） */
    @Excel(name = "当期新增-追偿还款减免总金额")
    private BigDecimal addReduce8Amount;

    /** 当期新增-追偿还款减免本金（元） */
    @Excel(name = "当期新增-追偿还款减免本金")
    private BigDecimal addReduce8PrinAmount;

    /** 当期新增-追偿还款减免利息（元） */
    @Excel(name = "当期新增-追偿还款减免利息")
    private BigDecimal addReduce8IntAmount;

    /** 当期新增-追偿还款减免罚息（元） */
    @Excel(name = "当期新增-追偿还款减免罚息")
    private BigDecimal addReduce8OintAmount;

    /** 当期新增-追偿还款减免服务费（元） */
    @Excel(name = "当期新增-追偿还款减免服务费")
    private BigDecimal addReduce8ServiceAmount;

    /** 当期新增-追偿还款减免担保费（元） */
    @Excel(name = "当期新增-追偿还款减免担保费")
    private BigDecimal addReduce8GuaranteeAmount;

    /** 当期新增-追偿还款减免担保费罚息（元） */
    @Excel(name = "当期新增-追偿还款减免担保费罚息")
    private BigDecimal addReduce8OguaranteeAmount;

    /** 当期新增-追偿还款减免保证金（元） */
    @Excel(name = "当期新增-追偿还款减免保证金")
    private BigDecimal addReduce8MarginAmount;

    /** 当期新增-追偿还款减免代偿金（元） */
    @Excel(name = "当期新增-追偿还款减免代偿金")
    private BigDecimal addReduce8CompensateAmount;

    /** 当期新增-追偿还款减免逾期违约金（元） */
    @Excel(name = "当期新增-追偿还款减免逾期违约金")
    private BigDecimal addReduce8DefineAmount;

    /** 当期新增-追偿还款减免提前还款违约金（元） */
    @Excel(name = "当期新增-追偿还款减免提前还款违约金")
    private BigDecimal addReduce8AdvDefineAmount;

    /** 状态（0正常 1停用） */
//    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 是否映射成功（Y映射成功N映射失败） */
    @Excel(name = "是否映射成功", readConverterExp = "Y=映射成功,N映射失败")
    private String isMapping;

    /** 资方在贷笔数 */
    @Excel(name = "资方在贷笔数")
    private Long fundZdCount;
    /** 用户未结清笔数 */
    @Excel(name = "用户未结清笔数")
    private Long userWjqCount;

    private String betweenDate;
    private String endDate;

    List<String> dateRange;

    public List<String> getDateRange() {
        return dateRange;
    }

    public void setDateRange(List<String> dateRange) {
        this.dateRange = dateRange;
    }

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPlatformNo(String platformNo) 
    {
        this.platformNo = platformNo;
    }

    public String getPlatformNo() 
    {
        return platformNo;
    }
    public void setCustNo(String custNo) 
    {
        this.custNo = custNo;
    }

    public String getCustNo() 
    {
        return custNo;
    }
    public void setPartnerNo(String partnerNo) 
    {
        this.partnerNo = partnerNo;
    }

    public String getPartnerNo() 
    {
        return partnerNo;
    }
    public void setFundNo(String fundNo) 
    {
        this.fundNo = fundNo;
    }

    public String getFundNo() 
    {
        return fundNo;
    }
    public void setProductNo(String productNo) 
    {
        this.productNo = productNo;
    }

    public String getProductNo() 
    {
        return productNo;
    }
    public void setIsPortrayal(String isPortrayal) 
    {
        this.isPortrayal = isPortrayal;
    }

    public String getIsPortrayal() 
    {
        return isPortrayal;
    }
//    public void setPortrayalType(String portrayalType)
//    {
//        this.portrayalType = portrayalType;
//    }
//
//    public String getPortrayalType()
//    {
//        return portrayalType;
//    }
//    public void setPortrayalNo(String portrayalNo)
//    {
//        this.portrayalNo = portrayalNo;
//    }
//
//    public String getPortrayalNo()
//    {
//        return portrayalNo;
//    }
    public void setReconDate(Date reconDate) 
    {
        this.reconDate = reconDate;
    }

    public Date getReconDate() 
    {
        return reconDate;
    }
    public void setTotalBeginDate(Date totalBeginDate) 
    {
        this.totalBeginDate = totalBeginDate;
    }

    public Date getTotalBeginDate() 
    {
        return totalBeginDate;
    }
    public void setTotalEndDate(Date totalEndDate) 
    {
        this.totalEndDate = totalEndDate;
    }

    public Date getTotalEndDate() 
    {
        return totalEndDate;
    }
    public void setTotalCount(Long totalCount) 
    {
        this.totalCount = totalCount;
    }

    public Long getTotalCount() 
    {
        return totalCount;
    }
    public void setTotalHumanCount(Long totalHumanCount) 
    {
        this.totalHumanCount = totalHumanCount;
    }

    public Long getTotalHumanCount() 
    {
        return totalHumanCount;
    }
    public void setTotalAmount(BigDecimal totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() 
    {
        return totalAmount;
    }
    public void setTotalActServiceAmount(BigDecimal totalActServiceAmount) 
    {
        this.totalActServiceAmount = totalActServiceAmount;
    }

    public BigDecimal getTotalActServiceAmount() 
    {
        return totalActServiceAmount;
    }
    public void setTotalActGuaranteeAmount(BigDecimal totalActGuaranteeAmount) 
    {
        this.totalActGuaranteeAmount = totalActGuaranteeAmount;
    }

    public BigDecimal getTotalActGuaranteeAmount() 
    {
        return totalActGuaranteeAmount;
    }
    public void setTotalActMarginAmount(BigDecimal totalActMarginAmount) 
    {
        this.totalActMarginAmount = totalActMarginAmount;
    }

    public BigDecimal getTotalActMarginAmount() 
    {
        return totalActMarginAmount;
    }
    public void setTotalActCompensateAmount(BigDecimal totalActCompensateAmount) 
    {
        this.totalActCompensateAmount = totalActCompensateAmount;
    }

    public BigDecimal getTotalActCompensateAmount() 
    {
        return totalActCompensateAmount;
    }
    public void setTotalAverageIrr(BigDecimal totalAverageIrr) 
    {
        this.totalAverageIrr = totalAverageIrr;
    }

    public BigDecimal getTotalAverageIrr() 
    {
        return totalAverageIrr;
    }
    public void setTotalAverageTerm(BigDecimal totalAverageTerm) 
    {
        this.totalAverageTerm = totalAverageTerm;
    }

    public BigDecimal getTotalAverageTerm() 
    {
        return totalAverageTerm;
    }
    public void setTotalBalanceAmount(BigDecimal totalBalanceAmount) 
    {
        this.totalBalanceAmount = totalBalanceAmount;
    }

    public BigDecimal getTotalBalanceAmount() 
    {
        return totalBalanceAmount;
    }
    public void setTotalPlanBalanceAmt(BigDecimal totalPlanBalanceAmt) 
    {
        this.totalPlanBalanceAmt = totalPlanBalanceAmt;
    }

    public BigDecimal getTotalPlanBalanceAmt() 
    {
        return totalPlanBalanceAmt;
    }
    public void setTotalFundBalanceAmt(BigDecimal totalFundBalanceAmt) 
    {
        this.totalFundBalanceAmt = totalFundBalanceAmt;
    }

    public BigDecimal getTotalFundBalanceAmt() 
    {
        return totalFundBalanceAmt;
    }
    public void setTotalPlanFundBalanceAmt(BigDecimal totalPlanFundBalanceAmt) 
    {
        this.totalPlanFundBalanceAmt = totalPlanFundBalanceAmt;
    }

    public BigDecimal getTotalPlanFundBalanceAmt() 
    {
        return totalPlanFundBalanceAmt;
    }
    public void setTotalBalanceAmt(BigDecimal totalBalanceAmt) 
    {
        this.totalBalanceAmt = totalBalanceAmt;
    }

    public BigDecimal getTotalBalanceAmt() 
    {
        return totalBalanceAmt;
    }
    public void setOverdueTotalAmt(BigDecimal overdueTotalAmt) 
    {
        this.overdueTotalAmt = overdueTotalAmt;
    }

    public BigDecimal getOverdueTotalAmt() 
    {
        return overdueTotalAmt;
    }
    public void setTotalUnclearedCount(Long totalUnclearedCount) 
    {
        this.totalUnclearedCount = totalUnclearedCount;
    }

    public Long getTotalUnclearedCount() 
    {
        return totalUnclearedCount;
    }
    public void setTotalCompensateCount(Long totalCompensateCount) 
    {
        this.totalCompensateCount = totalCompensateCount;
    }

    public Long getTotalCompensateCount() 
    {
        return totalCompensateCount;
    }
    public void setTotalCompensateAmount(BigDecimal totalCompensateAmount) 
    {
        this.totalCompensateAmount = totalCompensateAmount;
    }

    public BigDecimal getTotalCompensateAmount() 
    {
        return totalCompensateAmount;
    }
    public void setTotalCompensatePrintAmount(BigDecimal totalCompensatePrintAmount) 
    {
        this.totalCompensatePrintAmount = totalCompensatePrintAmount;
    }

    public BigDecimal getTotalCompensatePrintAmount() 
    {
        return totalCompensatePrintAmount;
    }
    public void setTotalCompensateIntAmount(BigDecimal totalCompensateIntAmount) 
    {
        this.totalCompensateIntAmount = totalCompensateIntAmount;
    }

    public BigDecimal getTotalCompensateIntAmount() 
    {
        return totalCompensateIntAmount;
    }
    public void setTotalCompensateOintAmount(BigDecimal totalCompensateOintAmount) 
    {
        this.totalCompensateOintAmount = totalCompensateOintAmount;
    }

    public BigDecimal getTotalCompensateOintAmount() 
    {
        return totalCompensateOintAmount;
    }
    public void setTotalCompensateServiceAmount(BigDecimal totalCompensateServiceAmount) 
    {
        this.totalCompensateServiceAmount = totalCompensateServiceAmount;
    }

    public BigDecimal getTotalCompensateServiceAmount() 
    {
        return totalCompensateServiceAmount;
    }
    public void setTotalCompensateGuaranteeAmount(BigDecimal totalCompensateGuaranteeAmount) 
    {
        this.totalCompensateGuaranteeAmount = totalCompensateGuaranteeAmount;
    }

    public BigDecimal getTotalCompensateGuaranteeAmount() 
    {
        return totalCompensateGuaranteeAmount;
    }
    public void setTotalCompensateMarginAmount(BigDecimal totalCompensateMarginAmount) 
    {
        this.totalCompensateMarginAmount = totalCompensateMarginAmount;
    }

    public BigDecimal getTotalCompensateMarginAmount() 
    {
        return totalCompensateMarginAmount;
    }
    public void setTotalCompensateCompensateAmount(BigDecimal totalCompensateCompensateAmount) 
    {
        this.totalCompensateCompensateAmount = totalCompensateCompensateAmount;
    }

    public BigDecimal getTotalCompensateCompensateAmount() 
    {
        return totalCompensateCompensateAmount;
    }
    public void setTotalCompensateDefineAmount(BigDecimal totalCompensateDefineAmount) 
    {
        this.totalCompensateDefineAmount = totalCompensateDefineAmount;
    }

    public BigDecimal getTotalCompensateDefineAmount() 
    {
        return totalCompensateDefineAmount;
    }
    public void setTotalCompensateAdvDefineAmount(BigDecimal totalCompensateAdvDefineAmount) 
    {
        this.totalCompensateAdvDefineAmount = totalCompensateAdvDefineAmount;
    }

    public BigDecimal getTotalCompensateAdvDefineAmount() 
    {
        return totalCompensateAdvDefineAmount;
    }
    public void setTotalCompensateOguaranteeAmount(BigDecimal totalCompensateOguaranteeAmount) 
    {
        this.totalCompensateOguaranteeAmount = totalCompensateOguaranteeAmount;
    }

    public BigDecimal getTotalCompensateOguaranteeAmount() 
    {
        return totalCompensateOguaranteeAmount;
    }
    public void setTotalRepayCount(Long totalRepayCount) 
    {
        this.totalRepayCount = totalRepayCount;
    }

    public Long getTotalRepayCount() 
    {
        return totalRepayCount;
    }
    public void setTotalRepayPrintAmount(BigDecimal totalRepayPrintAmount) 
    {
        this.totalRepayPrintAmount = totalRepayPrintAmount;
    }

    public BigDecimal getTotalRepayPrintAmount() 
    {
        return totalRepayPrintAmount;
    }
    public void setTotalRepayIntAmount(BigDecimal totalRepayIntAmount) 
    {
        this.totalRepayIntAmount = totalRepayIntAmount;
    }

    public BigDecimal getTotalRepayIntAmount() 
    {
        return totalRepayIntAmount;
    }
    public void setTotalRepayOintAmount(BigDecimal totalRepayOintAmount) 
    {
        this.totalRepayOintAmount = totalRepayOintAmount;
    }

    public BigDecimal getTotalRepayOintAmount() 
    {
        return totalRepayOintAmount;
    }
    public void setTotalRepayServiceAmount(BigDecimal totalRepayServiceAmount) 
    {
        this.totalRepayServiceAmount = totalRepayServiceAmount;
    }

    public BigDecimal getTotalRepayServiceAmount() 
    {
        return totalRepayServiceAmount;
    }
    public void setTotalRepayGuaranteeAmount(BigDecimal totalRepayGuaranteeAmount) 
    {
        this.totalRepayGuaranteeAmount = totalRepayGuaranteeAmount;
    }

    public BigDecimal getTotalRepayGuaranteeAmount() 
    {
        return totalRepayGuaranteeAmount;
    }
    public void setTotalRepayOguaranteeAmount(BigDecimal totalRepayOguaranteeAmount) 
    {
        this.totalRepayOguaranteeAmount = totalRepayOguaranteeAmount;
    }

    public BigDecimal getTotalRepayOguaranteeAmount() 
    {
        return totalRepayOguaranteeAmount;
    }
    public void setTotalRepayMarginAmount(BigDecimal totalRepayMarginAmount) 
    {
        this.totalRepayMarginAmount = totalRepayMarginAmount;
    }

    public BigDecimal getTotalRepayMarginAmount() 
    {
        return totalRepayMarginAmount;
    }
    public void setTotalRepayCompensateAmount(BigDecimal totalRepayCompensateAmount) 
    {
        this.totalRepayCompensateAmount = totalRepayCompensateAmount;
    }

    public BigDecimal getTotalRepayCompensateAmount() 
    {
        return totalRepayCompensateAmount;
    }
    public void setTotalRepayDefineAmount(BigDecimal totalRepayDefineAmount) 
    {
        this.totalRepayDefineAmount = totalRepayDefineAmount;
    }

    public BigDecimal getTotalRepayDefineAmount() 
    {
        return totalRepayDefineAmount;
    }
    public void setTotalRepayAdvDefineAmount(BigDecimal totalRepayAdvDefineAmount) 
    {
        this.totalRepayAdvDefineAmount = totalRepayAdvDefineAmount;
    }

    public BigDecimal getTotalRepayAdvDefineAmount() 
    {
        return totalRepayAdvDefineAmount;
    }
    public void setTotalReduceCount(Long totalReduceCount) 
    {
        this.totalReduceCount = totalReduceCount;
    }

    public Long getTotalReduceCount() 
    {
        return totalReduceCount;
    }
    public void setTotalReducePrintAmount(BigDecimal totalReducePrintAmount) 
    {
        this.totalReducePrintAmount = totalReducePrintAmount;
    }

    public BigDecimal getTotalReducePrintAmount() 
    {
        return totalReducePrintAmount;
    }
    public void setTotalReduceIntAmount(BigDecimal totalReduceIntAmount) 
    {
        this.totalReduceIntAmount = totalReduceIntAmount;
    }

    public BigDecimal getTotalReduceIntAmount() 
    {
        return totalReduceIntAmount;
    }
    public void setTotalReduceOintAmount(BigDecimal totalReduceOintAmount) 
    {
        this.totalReduceOintAmount = totalReduceOintAmount;
    }

    public BigDecimal getTotalReduceOintAmount() 
    {
        return totalReduceOintAmount;
    }
    public void setTotalReduceServiceAmount(BigDecimal totalReduceServiceAmount) 
    {
        this.totalReduceServiceAmount = totalReduceServiceAmount;
    }

    public BigDecimal getTotalReduceServiceAmount() 
    {
        return totalReduceServiceAmount;
    }
    public void setTotalReduceGuaranteeAmount(BigDecimal totalReduceGuaranteeAmount) 
    {
        this.totalReduceGuaranteeAmount = totalReduceGuaranteeAmount;
    }

    public BigDecimal getTotalReduceGuaranteeAmount() 
    {
        return totalReduceGuaranteeAmount;
    }
    public void setTotalReduceOguaranteeAmount(BigDecimal totalReduceOguaranteeAmount) 
    {
        this.totalReduceOguaranteeAmount = totalReduceOguaranteeAmount;
    }

    public BigDecimal getTotalReduceOguaranteeAmount() 
    {
        return totalReduceOguaranteeAmount;
    }
    public void setTotalReduceMarginAmount(BigDecimal totalReduceMarginAmount) 
    {
        this.totalReduceMarginAmount = totalReduceMarginAmount;
    }

    public BigDecimal getTotalReduceMarginAmount() 
    {
        return totalReduceMarginAmount;
    }
    public void setTotalReduceCompensateAmount(BigDecimal totalReduceCompensateAmount) 
    {
        this.totalReduceCompensateAmount = totalReduceCompensateAmount;
    }

    public BigDecimal getTotalReduceCompensateAmount() 
    {
        return totalReduceCompensateAmount;
    }
    public void setTotalReduceDefineAmount(BigDecimal totalReduceDefineAmount) 
    {
        this.totalReduceDefineAmount = totalReduceDefineAmount;
    }

    public BigDecimal getTotalReduceDefineAmount() 
    {
        return totalReduceDefineAmount;
    }
    public void setTotalReduceAdvDefineAmount(BigDecimal totalReduceAdvDefineAmount) 
    {
        this.totalReduceAdvDefineAmount = totalReduceAdvDefineAmount;
    }

    public BigDecimal getTotalReduceAdvDefineAmount() 
    {
        return totalReduceAdvDefineAmount;
    }
    public void setAddBeginDate(Date addBeginDate) 
    {
        this.addBeginDate = addBeginDate;
    }

    public Date getAddBeginDate() 
    {
        return addBeginDate;
    }
    public void setAddEndDate(Date addEndDate) 
    {
        this.addEndDate = addEndDate;
    }

    public Date getAddEndDate() 
    {
        return addEndDate;
    }
    public void setAddCount(Long addCount) 
    {
        this.addCount = addCount;
    }

    public Long getAddCount() 
    {
        return addCount;
    }
    public void setAddHumanCount(Long addHumanCount) 
    {
        this.addHumanCount = addHumanCount;
    }

    public Long getAddHumanCount() 
    {
        return addHumanCount;
    }
    public void setAddAmount(BigDecimal addAmount) 
    {
        this.addAmount = addAmount;
    }

    public BigDecimal getAddAmount() 
    {
        return addAmount;
    }
    public void setAddActServiceAmount(BigDecimal addActServiceAmount) 
    {
        this.addActServiceAmount = addActServiceAmount;
    }

    public BigDecimal getAddActServiceAmount() 
    {
        return addActServiceAmount;
    }
    public void setAddActGuaranteeAmount(BigDecimal addActGuaranteeAmount) 
    {
        this.addActGuaranteeAmount = addActGuaranteeAmount;
    }

    public BigDecimal getAddActGuaranteeAmount() 
    {
        return addActGuaranteeAmount;
    }
    public void setAddActMarginAmount(BigDecimal addActMarginAmount) 
    {
        this.addActMarginAmount = addActMarginAmount;
    }

    public BigDecimal getAddActMarginAmount() 
    {
        return addActMarginAmount;
    }
    public void setAddActCompensateAmount(BigDecimal addActCompensateAmount) 
    {
        this.addActCompensateAmount = addActCompensateAmount;
    }

    public BigDecimal getAddActCompensateAmount() 
    {
        return addActCompensateAmount;
    }
    public void setAddEalanceAmount(BigDecimal addEalanceAmount) 
    {
        this.addEalanceAmount = addEalanceAmount;
    }

    public BigDecimal getAddEalanceAmount() 
    {
        return addEalanceAmount;
    }
    public void setAddMedianAmount(BigDecimal addMedianAmount) 
    {
        this.addMedianAmount = addMedianAmount;
    }

    public BigDecimal getAddMedianAmount() 
    {
        return addMedianAmount;
    }
    public void setAddAverageIrr(BigDecimal addAverageIrr) 
    {
        this.addAverageIrr = addAverageIrr;
    }

    public BigDecimal getAddAverageIrr() 
    {
        return addAverageIrr;
    }
    public void setAddAverageTerm(BigDecimal addAverageTerm) 
    {
        this.addAverageTerm = addAverageTerm;
    }

    public BigDecimal getAddAverageTerm() 
    {
        return addAverageTerm;
    }
    public void setAddCompensateCount(Long addCompensateCount) 
    {
        this.addCompensateCount = addCompensateCount;
    }

    public Long getAddCompensateCount() 
    {
        return addCompensateCount;
    }
    public void setAddCompensateAmount(BigDecimal addCompensateAmount) 
    {
        this.addCompensateAmount = addCompensateAmount;
    }

    public BigDecimal getAddCompensateAmount() 
    {
        return addCompensateAmount;
    }
    public void setAddCompensatePrintAmount(BigDecimal addCompensatePrintAmount) 
    {
        this.addCompensatePrintAmount = addCompensatePrintAmount;
    }

    public BigDecimal getAddCompensatePrintAmount() 
    {
        return addCompensatePrintAmount;
    }
    public void setAddCompensateIntAmount(BigDecimal addCompensateIntAmount) 
    {
        this.addCompensateIntAmount = addCompensateIntAmount;
    }

    public BigDecimal getAddCompensateIntAmount() 
    {
        return addCompensateIntAmount;
    }
    public void setAddCompensateOintAmount(BigDecimal addCompensateOintAmount) 
    {
        this.addCompensateOintAmount = addCompensateOintAmount;
    }

    public BigDecimal getAddCompensateOintAmount() 
    {
        return addCompensateOintAmount;
    }
    public void setAddCompensateServiceAmount(BigDecimal addCompensateServiceAmount) 
    {
        this.addCompensateServiceAmount = addCompensateServiceAmount;
    }

    public BigDecimal getAddCompensateServiceAmount() 
    {
        return addCompensateServiceAmount;
    }
    public void setAddCompensateGuaranteeAmount(BigDecimal addCompensateGuaranteeAmount) 
    {
        this.addCompensateGuaranteeAmount = addCompensateGuaranteeAmount;
    }

    public BigDecimal getAddCompensateGuaranteeAmount() 
    {
        return addCompensateGuaranteeAmount;
    }
    public void setAddCompensateMarginAmount(BigDecimal addCompensateMarginAmount) 
    {
        this.addCompensateMarginAmount = addCompensateMarginAmount;
    }

    public BigDecimal getAddCompensateMarginAmount() 
    {
        return addCompensateMarginAmount;
    }
    public void setAddCompensateCompensateAmount(BigDecimal addCompensateCompensateAmount) 
    {
        this.addCompensateCompensateAmount = addCompensateCompensateAmount;
    }

    public BigDecimal getAddCompensateCompensateAmount() 
    {
        return addCompensateCompensateAmount;
    }
    public void setAddCompensateDefineAmount(BigDecimal addCompensateDefineAmount) 
    {
        this.addCompensateDefineAmount = addCompensateDefineAmount;
    }

    public BigDecimal getAddCompensateDefineAmount() 
    {
        return addCompensateDefineAmount;
    }
    public void setAddCompensateAdvDefineAmount(BigDecimal addCompensateAdvDefineAmount) 
    {
        this.addCompensateAdvDefineAmount = addCompensateAdvDefineAmount;
    }

    public BigDecimal getAddCompensateAdvDefineAmount() 
    {
        return addCompensateAdvDefineAmount;
    }
    public void setAddCompensateOguaranteeAmount(BigDecimal addCompensateOguaranteeAmount) 
    {
        this.addCompensateOguaranteeAmount = addCompensateOguaranteeAmount;
    }

    public BigDecimal getAddCompensateOguaranteeAmount() 
    {
        return addCompensateOguaranteeAmount;
    }
    public void setAddRepayCount(Long addRepayCount) 
    {
        this.addRepayCount = addRepayCount;
    }

    public Long getAddRepayCount() 
    {
        return addRepayCount;
    }
    public void setAddRepayPrintAmount(BigDecimal addRepayPrintAmount) 
    {
        this.addRepayPrintAmount = addRepayPrintAmount;
    }

    public BigDecimal getAddRepayPrintAmount() 
    {
        return addRepayPrintAmount;
    }
    public void setAddRepayIntAmount(BigDecimal addRepayIntAmount) 
    {
        this.addRepayIntAmount = addRepayIntAmount;
    }

    public BigDecimal getAddRepayIntAmount() 
    {
        return addRepayIntAmount;
    }
    public void setAddRepayOintAmount(BigDecimal addRepayOintAmount) 
    {
        this.addRepayOintAmount = addRepayOintAmount;
    }

    public BigDecimal getAddRepayOintAmount() 
    {
        return addRepayOintAmount;
    }
    public void setAddRepayServiceAmount(BigDecimal addRepayServiceAmount) 
    {
        this.addRepayServiceAmount = addRepayServiceAmount;
    }

    public BigDecimal getAddRepayServiceAmount() 
    {
        return addRepayServiceAmount;
    }
    public void setAddRepayGuaranteeAmount(BigDecimal addRepayGuaranteeAmount) 
    {
        this.addRepayGuaranteeAmount = addRepayGuaranteeAmount;
    }

    public BigDecimal getAddRepayGuaranteeAmount() 
    {
        return addRepayGuaranteeAmount;
    }
    public void setAddRepayOguaranteeAmount(BigDecimal addRepayOguaranteeAmount) 
    {
        this.addRepayOguaranteeAmount = addRepayOguaranteeAmount;
    }

    public BigDecimal getAddRepayOguaranteeAmount() 
    {
        return addRepayOguaranteeAmount;
    }
    public void setAddRepayMarginAmount(BigDecimal addRepayMarginAmount) 
    {
        this.addRepayMarginAmount = addRepayMarginAmount;
    }

    public BigDecimal getAddRepayMarginAmount() 
    {
        return addRepayMarginAmount;
    }
    public void setAddRepayCompensateAmount(BigDecimal addRepayCompensateAmount) 
    {
        this.addRepayCompensateAmount = addRepayCompensateAmount;
    }

    public BigDecimal getAddRepayCompensateAmount() 
    {
        return addRepayCompensateAmount;
    }
    public void setAddRepayDefineAmount(BigDecimal addRepayDefineAmount) 
    {
        this.addRepayDefineAmount = addRepayDefineAmount;
    }

    public BigDecimal getAddRepayDefineAmount() 
    {
        return addRepayDefineAmount;
    }
    public void setAddRepayAdvDefineAmount(BigDecimal addRepayAdvDefineAmount) 
    {
        this.addRepayAdvDefineAmount = addRepayAdvDefineAmount;
    }

    public BigDecimal getAddRepayAdvDefineAmount() 
    {
        return addRepayAdvDefineAmount;
    }
    public void setAddReduceCount(Long addReduceCount) 
    {
        this.addReduceCount = addReduceCount;
    }

    public Long getAddReduceCount() 
    {
        return addReduceCount;
    }
    public void setAddReducePrintAmount(BigDecimal addReducePrintAmount) 
    {
        this.addReducePrintAmount = addReducePrintAmount;
    }

    public BigDecimal getAddReducePrintAmount() 
    {
        return addReducePrintAmount;
    }
    public void setAddReduceIntAmount(BigDecimal addReduceIntAmount) 
    {
        this.addReduceIntAmount = addReduceIntAmount;
    }

    public BigDecimal getAddReduceIntAmount() 
    {
        return addReduceIntAmount;
    }
    public void setAddReduceOintAmount(BigDecimal addReduceOintAmount) 
    {
        this.addReduceOintAmount = addReduceOintAmount;
    }

    public BigDecimal getAddReduceOintAmount() 
    {
        return addReduceOintAmount;
    }
    public void setAddReduceServiceAmount(BigDecimal addReduceServiceAmount) 
    {
        this.addReduceServiceAmount = addReduceServiceAmount;
    }

    public BigDecimal getAddReduceServiceAmount() 
    {
        return addReduceServiceAmount;
    }
    public void setAddReduceGuaranteeAmount(BigDecimal addReduceGuaranteeAmount) 
    {
        this.addReduceGuaranteeAmount = addReduceGuaranteeAmount;
    }

    public BigDecimal getAddReduceGuaranteeAmount() 
    {
        return addReduceGuaranteeAmount;
    }
    public void setAddReduceOguaranteeAmount(BigDecimal addReduceOguaranteeAmount) 
    {
        this.addReduceOguaranteeAmount = addReduceOguaranteeAmount;
    }

    public BigDecimal getAddReduceOguaranteeAmount() 
    {
        return addReduceOguaranteeAmount;
    }
    public void setAddReduceMarginAmount(BigDecimal addReduceMarginAmount) 
    {
        this.addReduceMarginAmount = addReduceMarginAmount;
    }

    public BigDecimal getAddReduceMarginAmount() 
    {
        return addReduceMarginAmount;
    }
    public void setAddReduceCompensateAmount(BigDecimal addReduceCompensateAmount) 
    {
        this.addReduceCompensateAmount = addReduceCompensateAmount;
    }

    public BigDecimal getAddReduceCompensateAmount() 
    {
        return addReduceCompensateAmount;
    }
    public void setAddReduceDefineAmount(BigDecimal addReduceDefineAmount) 
    {
        this.addReduceDefineAmount = addReduceDefineAmount;
    }

    public BigDecimal getAddReduceDefineAmount() 
    {
        return addReduceDefineAmount;
    }
    public void setAddReduceAdvDefineAmount(BigDecimal addReduceAdvDefineAmount) 
    {
        this.addReduceAdvDefineAmount = addReduceAdvDefineAmount;
    }

    public BigDecimal getAddReduceAdvDefineAmount() 
    {
        return addReduceAdvDefineAmount;
    }
    public void setAddRepay8Count(Integer addRepay8Count) 
    {
        this.addRepay8Count = addRepay8Count;
    }

    public Integer getAddRepay8Count() 
    {
        return addRepay8Count;
    }
    public void setAddRepay8Amount(BigDecimal addRepay8Amount) 
    {
        this.addRepay8Amount = addRepay8Amount;
    }

    public BigDecimal getAddRepay8Amount() 
    {
        return addRepay8Amount;
    }
    public void setAddRepay8PrinAmount(BigDecimal addRepay8PrinAmount) 
    {
        this.addRepay8PrinAmount = addRepay8PrinAmount;
    }

    public BigDecimal getAddRepay8PrinAmount() 
    {
        return addRepay8PrinAmount;
    }
    public void setAddRepay8IntAmount(BigDecimal addRepay8IntAmount) 
    {
        this.addRepay8IntAmount = addRepay8IntAmount;
    }

    public BigDecimal getAddRepay8IntAmount() 
    {
        return addRepay8IntAmount;
    }
    public void setAddRepay8OintAmount(BigDecimal addRepay8OintAmount) 
    {
        this.addRepay8OintAmount = addRepay8OintAmount;
    }

    public BigDecimal getAddRepay8OintAmount() 
    {
        return addRepay8OintAmount;
    }
    public void setAddRepay8ServiceAmount(BigDecimal addRepay8ServiceAmount) 
    {
        this.addRepay8ServiceAmount = addRepay8ServiceAmount;
    }

    public BigDecimal getAddRepay8ServiceAmount() 
    {
        return addRepay8ServiceAmount;
    }
    public void setAddRepay8GuaranteeAmount(BigDecimal addRepay8GuaranteeAmount) 
    {
        this.addRepay8GuaranteeAmount = addRepay8GuaranteeAmount;
    }

    public BigDecimal getAddRepay8GuaranteeAmount() 
    {
        return addRepay8GuaranteeAmount;
    }
    public void setAddRepay8MarginAmount(BigDecimal addRepay8MarginAmount) 
    {
        this.addRepay8MarginAmount = addRepay8MarginAmount;
    }

    public BigDecimal getAddRepay8MarginAmount() 
    {
        return addRepay8MarginAmount;
    }
    public void setAddRepay8CompensateAmount(BigDecimal addRepay8CompensateAmount) 
    {
        this.addRepay8CompensateAmount = addRepay8CompensateAmount;
    }

    public BigDecimal getAddRepay8CompensateAmount() 
    {
        return addRepay8CompensateAmount;
    }
    public void setAddRepay8DefineAmount(BigDecimal addRepay8DefineAmount) 
    {
        this.addRepay8DefineAmount = addRepay8DefineAmount;
    }

    public BigDecimal getAddRepay8DefineAmount() 
    {
        return addRepay8DefineAmount;
    }
    public void setAddRepay8AdvDefineAmount(BigDecimal addRepay8AdvDefineAmount) 
    {
        this.addRepay8AdvDefineAmount = addRepay8AdvDefineAmount;
    }

    public BigDecimal getAddRepay8AdvDefineAmount() 
    {
        return addRepay8AdvDefineAmount;
    }
    public void setAddRepay8OguaranteeAmount(BigDecimal addRepay8OguaranteeAmount) 
    {
        this.addRepay8OguaranteeAmount = addRepay8OguaranteeAmount;
    }

    public BigDecimal getAddRepay8OguaranteeAmount() 
    {
        return addRepay8OguaranteeAmount;
    }
    public void setAddReduce7Amount(BigDecimal addReduce7Amount) 
    {
        this.addReduce7Amount = addReduce7Amount;
    }

    public BigDecimal getAddReduce7Amount() 
    {
        return addReduce7Amount;
    }
    public void setAddReduce7PrinAmount(BigDecimal addReduce7PrinAmount) 
    {
        this.addReduce7PrinAmount = addReduce7PrinAmount;
    }

    public BigDecimal getAddReduce7PrinAmount() 
    {
        return addReduce7PrinAmount;
    }
    public void setAddReduce7IntAmount(BigDecimal addReduce7IntAmount) 
    {
        this.addReduce7IntAmount = addReduce7IntAmount;
    }

    public BigDecimal getAddReduce7IntAmount() 
    {
        return addReduce7IntAmount;
    }
    public void setAddReduce7OintAmount(BigDecimal addReduce7OintAmount) 
    {
        this.addReduce7OintAmount = addReduce7OintAmount;
    }

    public BigDecimal getAddReduce7OintAmount() 
    {
        return addReduce7OintAmount;
    }
    public void setAddReduce7ServiceAmount(BigDecimal addReduce7ServiceAmount) 
    {
        this.addReduce7ServiceAmount = addReduce7ServiceAmount;
    }

    public BigDecimal getAddReduce7ServiceAmount() 
    {
        return addReduce7ServiceAmount;
    }
    public void setAddReduce7GuaranteeAmount(BigDecimal addReduce7GuaranteeAmount) 
    {
        this.addReduce7GuaranteeAmount = addReduce7GuaranteeAmount;
    }

    public BigDecimal getAddReduce7GuaranteeAmount() 
    {
        return addReduce7GuaranteeAmount;
    }
    public void setAddReduce7OguaranteeAmount(BigDecimal addReduce7OguaranteeAmount) 
    {
        this.addReduce7OguaranteeAmount = addReduce7OguaranteeAmount;
    }

    public BigDecimal getAddReduce7OguaranteeAmount() 
    {
        return addReduce7OguaranteeAmount;
    }
    public void setAddReduce7MarginAmount(BigDecimal addReduce7MarginAmount) 
    {
        this.addReduce7MarginAmount = addReduce7MarginAmount;
    }

    public BigDecimal getAddReduce7MarginAmount() 
    {
        return addReduce7MarginAmount;
    }
    public void setAddReduce7CompensateAmount(BigDecimal addReduce7CompensateAmount) 
    {
        this.addReduce7CompensateAmount = addReduce7CompensateAmount;
    }

    public BigDecimal getAddReduce7CompensateAmount() 
    {
        return addReduce7CompensateAmount;
    }
    public void setAddReduce7DefineAmount(BigDecimal addReduce7DefineAmount) 
    {
        this.addReduce7DefineAmount = addReduce7DefineAmount;
    }

    public BigDecimal getAddReduce7DefineAmount() 
    {
        return addReduce7DefineAmount;
    }
    public void setAddReduce7AdvDefineAmount(BigDecimal addReduce7AdvDefineAmount) 
    {
        this.addReduce7AdvDefineAmount = addReduce7AdvDefineAmount;
    }

    public BigDecimal getAddReduce7AdvDefineAmount() 
    {
        return addReduce7AdvDefineAmount;
    }
    public void setAddReduce8Amount(BigDecimal addReduce8Amount) 
    {
        this.addReduce8Amount = addReduce8Amount;
    }

    public BigDecimal getAddReduce8Amount() 
    {
        return addReduce8Amount;
    }
    public void setAddReduce8PrinAmount(BigDecimal addReduce8PrinAmount) 
    {
        this.addReduce8PrinAmount = addReduce8PrinAmount;
    }

    public BigDecimal getAddReduce8PrinAmount() 
    {
        return addReduce8PrinAmount;
    }
    public void setAddReduce8IntAmount(BigDecimal addReduce8IntAmount) 
    {
        this.addReduce8IntAmount = addReduce8IntAmount;
    }

    public BigDecimal getAddReduce8IntAmount() 
    {
        return addReduce8IntAmount;
    }
    public void setAddReduce8OintAmount(BigDecimal addReduce8OintAmount) 
    {
        this.addReduce8OintAmount = addReduce8OintAmount;
    }

    public BigDecimal getAddReduce8OintAmount() 
    {
        return addReduce8OintAmount;
    }
    public void setAddReduce8ServiceAmount(BigDecimal addReduce8ServiceAmount) 
    {
        this.addReduce8ServiceAmount = addReduce8ServiceAmount;
    }

    public BigDecimal getAddReduce8ServiceAmount() 
    {
        return addReduce8ServiceAmount;
    }
    public void setAddReduce8GuaranteeAmount(BigDecimal addReduce8GuaranteeAmount) 
    {
        this.addReduce8GuaranteeAmount = addReduce8GuaranteeAmount;
    }

    public BigDecimal getAddReduce8GuaranteeAmount() 
    {
        return addReduce8GuaranteeAmount;
    }
    public void setAddReduce8OguaranteeAmount(BigDecimal addReduce8OguaranteeAmount) 
    {
        this.addReduce8OguaranteeAmount = addReduce8OguaranteeAmount;
    }

    public BigDecimal getAddReduce8OguaranteeAmount() 
    {
        return addReduce8OguaranteeAmount;
    }
    public void setAddReduce8MarginAmount(BigDecimal addReduce8MarginAmount) 
    {
        this.addReduce8MarginAmount = addReduce8MarginAmount;
    }

    public BigDecimal getAddReduce8MarginAmount() 
    {
        return addReduce8MarginAmount;
    }
    public void setAddReduce8CompensateAmount(BigDecimal addReduce8CompensateAmount) 
    {
        this.addReduce8CompensateAmount = addReduce8CompensateAmount;
    }

    public BigDecimal getAddReduce8CompensateAmount() 
    {
        return addReduce8CompensateAmount;
    }
    public void setAddReduce8DefineAmount(BigDecimal addReduce8DefineAmount) 
    {
        this.addReduce8DefineAmount = addReduce8DefineAmount;
    }

    public BigDecimal getAddReduce8DefineAmount() 
    {
        return addReduce8DefineAmount;
    }
    public void setAddReduce8AdvDefineAmount(BigDecimal addReduce8AdvDefineAmount) 
    {
        this.addReduce8AdvDefineAmount = addReduce8AdvDefineAmount;
    }

    public BigDecimal getAddReduce8AdvDefineAmount() 
    {
        return addReduce8AdvDefineAmount;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setIsMapping(String isMapping) 
    {
        this.isMapping = isMapping;
    }

    public String getIsMapping() 
    {
        return isMapping;
    }

    public Long getFundZdCount() {
        return fundZdCount;
    }

    public void setFundZdCount(Long fundZdCount) {
        this.fundZdCount = fundZdCount;
    }

    public Long getUserWjqCount() {
        return userWjqCount;
    }

    public void setUserWjqCount(Long userWjqCount) {
        this.userWjqCount = userWjqCount;
    }

    public String getBetweenDate() {
        return betweenDate;
    }

    public void setBetweenDate(String betweenDate) {
        this.betweenDate = betweenDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return "DData{" +
                "id=" + id +
                ", platformNo='" + platformNo + '\'' +
                ", custNo='" + custNo + '\'' +
                ", partnerNo='" + partnerNo + '\'' +
                ", fundNo='" + fundNo + '\'' +
                ", productNo='" + productNo + '\'' +
                ", isPortrayal='" + isPortrayal + '\'' +
//                ", portrayalType='" + portrayalType + '\'' +
//                ", portrayalNo='" + portrayalNo + '\'' +
                ", reconDate=" + reconDate +
                ", totalBeginDate=" + totalBeginDate +
                ", totalEndDate=" + totalEndDate +
                ", totalCount=" + totalCount +
                ", totalHumanCount=" + totalHumanCount +
                ", totalAmount=" + totalAmount +
                ", totalActServiceAmount=" + totalActServiceAmount +
                ", totalActGuaranteeAmount=" + totalActGuaranteeAmount +
                ", totalActMarginAmount=" + totalActMarginAmount +
                ", totalActCompensateAmount=" + totalActCompensateAmount +
                ", totalAverageIrr=" + totalAverageIrr +
                ", totalAverageTerm=" + totalAverageTerm +
                ", totalBalanceAmount=" + totalBalanceAmount +
                ", totalPlanBalanceAmt=" + totalPlanBalanceAmt +
                ", totalFundBalanceAmt=" + totalFundBalanceAmt +
                ", totalPlanFundBalanceAmt=" + totalPlanFundBalanceAmt +
                ", totalBalanceAmt=" + totalBalanceAmt +
                ", overdueTotalAmt=" + overdueTotalAmt +
                ", totalUnclearedCount=" + totalUnclearedCount +
                ", totalCompensateCount=" + totalCompensateCount +
                ", totalCompensateAmount=" + totalCompensateAmount +
                ", totalCompensatePrintAmount=" + totalCompensatePrintAmount +
                ", totalCompensateIntAmount=" + totalCompensateIntAmount +
                ", totalCompensateOintAmount=" + totalCompensateOintAmount +
                ", totalCompensateServiceAmount=" + totalCompensateServiceAmount +
                ", totalCompensateGuaranteeAmount=" + totalCompensateGuaranteeAmount +
                ", totalCompensateMarginAmount=" + totalCompensateMarginAmount +
                ", totalCompensateCompensateAmount=" + totalCompensateCompensateAmount +
                ", totalCompensateDefineAmount=" + totalCompensateDefineAmount +
                ", totalCompensateAdvDefineAmount=" + totalCompensateAdvDefineAmount +
                ", totalCompensateOguaranteeAmount=" + totalCompensateOguaranteeAmount +
                ", totalRepayCount=" + totalRepayCount +
                ", totalRepayPrintAmount=" + totalRepayPrintAmount +
                ", totalRepayIntAmount=" + totalRepayIntAmount +
                ", totalRepayOintAmount=" + totalRepayOintAmount +
                ", totalRepayServiceAmount=" + totalRepayServiceAmount +
                ", totalRepayGuaranteeAmount=" + totalRepayGuaranteeAmount +
                ", totalRepayOguaranteeAmount=" + totalRepayOguaranteeAmount +
                ", totalRepayMarginAmount=" + totalRepayMarginAmount +
                ", totalRepayCompensateAmount=" + totalRepayCompensateAmount +
                ", totalRepayDefineAmount=" + totalRepayDefineAmount +
                ", totalRepayAdvDefineAmount=" + totalRepayAdvDefineAmount +
                ", totalReduceCount=" + totalReduceCount +
                ", totalReducePrintAmount=" + totalReducePrintAmount +
                ", totalReduceIntAmount=" + totalReduceIntAmount +
                ", totalReduceOintAmount=" + totalReduceOintAmount +
                ", totalReduceServiceAmount=" + totalReduceServiceAmount +
                ", totalReduceGuaranteeAmount=" + totalReduceGuaranteeAmount +
                ", totalReduceOguaranteeAmount=" + totalReduceOguaranteeAmount +
                ", totalReduceMarginAmount=" + totalReduceMarginAmount +
                ", totalReduceCompensateAmount=" + totalReduceCompensateAmount +
                ", totalReduceDefineAmount=" + totalReduceDefineAmount +
                ", totalReduceAdvDefineAmount=" + totalReduceAdvDefineAmount +
                ", addBeginDate=" + addBeginDate +
                ", addEndDate=" + addEndDate +
                ", addCount=" + addCount +
                ", addHumanCount=" + addHumanCount +
                ", addAmount=" + addAmount +
                ", addActServiceAmount=" + addActServiceAmount +
                ", addActGuaranteeAmount=" + addActGuaranteeAmount +
                ", addActMarginAmount=" + addActMarginAmount +
                ", addActCompensateAmount=" + addActCompensateAmount +
                ", addEalanceAmount=" + addEalanceAmount +
                ", addMedianAmount=" + addMedianAmount +
                ", addAverageIrr=" + addAverageIrr +
                ", addAverageTerm=" + addAverageTerm +
                ", addCompensateCount=" + addCompensateCount +
                ", addCompensateAmount=" + addCompensateAmount +
                ", addCompensatePrintAmount=" + addCompensatePrintAmount +
                ", addCompensateIntAmount=" + addCompensateIntAmount +
                ", addCompensateOintAmount=" + addCompensateOintAmount +
                ", addCompensateServiceAmount=" + addCompensateServiceAmount +
                ", addCompensateGuaranteeAmount=" + addCompensateGuaranteeAmount +
                ", addCompensateMarginAmount=" + addCompensateMarginAmount +
                ", addCompensateCompensateAmount=" + addCompensateCompensateAmount +
                ", addCompensateDefineAmount=" + addCompensateDefineAmount +
                ", addCompensateAdvDefineAmount=" + addCompensateAdvDefineAmount +
                ", addCompensateOguaranteeAmount=" + addCompensateOguaranteeAmount +
                ", addRepayCount=" + addRepayCount +
                ", addRepayPrintAmount=" + addRepayPrintAmount +
                ", addRepayIntAmount=" + addRepayIntAmount +
                ", addRepayOintAmount=" + addRepayOintAmount +
                ", addRepayServiceAmount=" + addRepayServiceAmount +
                ", addRepayGuaranteeAmount=" + addRepayGuaranteeAmount +
                ", addRepayOguaranteeAmount=" + addRepayOguaranteeAmount +
                ", addRepayMarginAmount=" + addRepayMarginAmount +
                ", addRepayCompensateAmount=" + addRepayCompensateAmount +
                ", addRepayDefineAmount=" + addRepayDefineAmount +
                ", addRepayAdvDefineAmount=" + addRepayAdvDefineAmount +
                ", addReduceCount=" + addReduceCount +
                ", addReducePrintAmount=" + addReducePrintAmount +
                ", addReduceIntAmount=" + addReduceIntAmount +
                ", addReduceOintAmount=" + addReduceOintAmount +
                ", addReduceServiceAmount=" + addReduceServiceAmount +
                ", addReduceGuaranteeAmount=" + addReduceGuaranteeAmount +
                ", addReduceOguaranteeAmount=" + addReduceOguaranteeAmount +
                ", addReduceMarginAmount=" + addReduceMarginAmount +
                ", addReduceCompensateAmount=" + addReduceCompensateAmount +
                ", addReduceDefineAmount=" + addReduceDefineAmount +
                ", addReduceAdvDefineAmount=" + addReduceAdvDefineAmount +
                ", addRepay8Count=" + addRepay8Count +
                ", addRepay8Amount=" + addRepay8Amount +
                ", addRepay8PrinAmount=" + addRepay8PrinAmount +
                ", addRepay8IntAmount=" + addRepay8IntAmount +
                ", addRepay8OintAmount=" + addRepay8OintAmount +
                ", addRepay8ServiceAmount=" + addRepay8ServiceAmount +
                ", addRepay8GuaranteeAmount=" + addRepay8GuaranteeAmount +
                ", addRepay8MarginAmount=" + addRepay8MarginAmount +
                ", addRepay8CompensateAmount=" + addRepay8CompensateAmount +
                ", addRepay8DefineAmount=" + addRepay8DefineAmount +
                ", addRepay8AdvDefineAmount=" + addRepay8AdvDefineAmount +
                ", addRepay8OguaranteeAmount=" + addRepay8OguaranteeAmount +
                ", addReduce7Amount=" + addReduce7Amount +
                ", addReduce7PrinAmount=" + addReduce7PrinAmount +
                ", addReduce7IntAmount=" + addReduce7IntAmount +
                ", addReduce7OintAmount=" + addReduce7OintAmount +
                ", addReduce7ServiceAmount=" + addReduce7ServiceAmount +
                ", addReduce7GuaranteeAmount=" + addReduce7GuaranteeAmount +
                ", addReduce7OguaranteeAmount=" + addReduce7OguaranteeAmount +
                ", addReduce7MarginAmount=" + addReduce7MarginAmount +
                ", addReduce7CompensateAmount=" + addReduce7CompensateAmount +
                ", addReduce7DefineAmount=" + addReduce7DefineAmount +
                ", addReduce7AdvDefineAmount=" + addReduce7AdvDefineAmount +
                ", addReduce8Amount=" + addReduce8Amount +
                ", addReduce8PrinAmount=" + addReduce8PrinAmount +
                ", addReduce8IntAmount=" + addReduce8IntAmount +
                ", addReduce8OintAmount=" + addReduce8OintAmount +
                ", addReduce8ServiceAmount=" + addReduce8ServiceAmount +
                ", addReduce8GuaranteeAmount=" + addReduce8GuaranteeAmount +
                ", addReduce8OguaranteeAmount=" + addReduce8OguaranteeAmount +
                ", addReduce8MarginAmount=" + addReduce8MarginAmount +
                ", addReduce8CompensateAmount=" + addReduce8CompensateAmount +
                ", addReduce8DefineAmount=" + addReduce8DefineAmount +
                ", addReduce8AdvDefineAmount=" + addReduce8AdvDefineAmount +
                ", status='" + status + '\'' +
                ", isMapping='" + isMapping + '\'' +
                ", fundZdCount=" + fundZdCount +
                ", userWjqCount=" + userWjqCount +
                ", dateRange=" + dateRange +
                '}';
    }
}
