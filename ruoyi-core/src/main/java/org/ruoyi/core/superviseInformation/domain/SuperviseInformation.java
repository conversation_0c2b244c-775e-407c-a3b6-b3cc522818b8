package org.ruoyi.core.superviseInformation.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 资料对象 zl_information
 *
 * <AUTHOR>
 * @date 2023-11-14
 *
 */
@Data
public class SuperviseInformation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 资料名称 */
    @Excel(name = "资料名称")
    @NotNull(message = "资料名称不能为空")
    private String informationName;

    /** 目录id */
    //@Excel(name = "目录id")
    @NotNull(message = "目录不能为空")
    private Long catalogueId;

    /** 系统资料编号 */
    @Excel(name = "系统资料编号")
    private String informationSystemCode;

    /** 资料月份 */
    @Excel(name = "资料所属月份")
    private String informationMonth;

    /** 资料年度 */
    @Excel(name = "资料年度")
    @NotNull(message = "资料年度不能为空")
    private String informationYear;

    /** 资料编号 */
    @Excel(name = "资料编号")
    private String informationCode;

    /** 保存状态(0 非永久  1永久) */
    //@Excel(name = "保存状态")
    @NotNull(message = "保存状态不能为空")
    private String saveFlag;

    /** 保管开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "保管开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date saveStartTime;

    /** 保管结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "保管结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date saveEndTime;

    /** 审核状态( 1 审核通过，2 未审核 3 审核不通过 4 审核中) */
    //@Excel(name = "审核状态( 1 审核通过 ，2 未审核 3 审核不通过 4审核中)")
    private String auditState;

    /** 提交状态 ( 0 未提交，1 已提交) */
    //@Excel(name = "提交状态")
    private String submitState;

    /** 文件路径 */
    //@Excel(name = "文件路径")
    @NotNull(message = "附件不能为空")
    private String fileUrl;

    @Excel(name = "文件名称")
    @NotNull(message = "附件不能为空")
    private String fileName;

    @Excel(name = "目录名称")
    private String catalogueName;

    @Excel(name = "审核状态")
    private String auditStateName;

    @Excel(name = "目录结构")
    private String structure;

    /** 是否为临时文件 (0.是 1.否) */
    //@Excel(name = "是否为临时文件 (0.是 1.否)")
    //private String isTemporary;

    /** 授权部门 */
    private Long auDeptId;

    /** 授权人 */
    private Long auUserId;

    /** 岗位 */
    private List<Long> auPostIds;

    /** 授权时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date impowerTime;

    /** 是否废弃 0.是 1.否 */
    private String isAbandoned;

    /** 是否删除 0.是 1.否 */
    private String isDelete;

    /** 合作公司 */
    //@Excel(name = "合作公司")
    private Long cooperationCompany;

    /** 合作项目 */
    //@Excel(name = "合作项目")
    private Long cooperationProject;
    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("informationName", getInformationName())
            .append("catalogueId", getCatalogueId())
            .append("informationSystemCode", getInformationSystemCode())
            .append("informationYear", getInformationYear())
            .append("informationCode", getInformationCode())
            .append("saveFlag", getSaveFlag())
            .append("saveStartTime", getSaveStartTime())
            .append("saveEndTime", getSaveEndTime())
            .append("auditState", getAuditState())
            .append("submitState", getSubmitState())
            .append("fileUrl", getFileUrl())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();

    }

}
