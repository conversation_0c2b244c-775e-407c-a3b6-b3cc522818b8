package org.ruoyi.core.superviseInformation.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue;
import org.ruoyi.core.superviseInformation.domain.vo.SuperviseInformationCatalogueVO;

import java.util.List;
import java.util.Set;

/**
 * 资料目录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-10
 *
 */
@Mapper
public interface SuperviseInformationCatalogueMapper
{

    /**
     * 查询资料目录
     *
     * @param id 资料目录主键
     * @return 资料目录
     */
    public SuperviseInformationCatalogue selectInformationCatalogueById(Long id);

    public SuperviseInformationCatalogueVO selectInformationCatalogueVOById(Long id);

    /**
     * 查询资料目录列表
     *
     * @param informationCatalogue 资料目录
     * @return 资料目录集合
     */
    public List<SuperviseInformationCatalogueVO> selectInformationCatalogueList(SuperviseInformationCatalogueVO informationCatalogue);

    /**
     * 查看父级权限
     *
     * @param informationCatalogue 资料目录
     * @return 结果
     */
    public List<SuperviseInformationCatalogueVO> selectCatalogueListOfAuthority(SuperviseInformationCatalogue informationCatalogue);

    /**
     * 新增资料目录
     *
     * @param informationCatalogue 资料目录
     * @return 结果
     */
    public int insertInformationCatalogue(SuperviseInformationCatalogue informationCatalogue);

    /**
     * 修改资料目录
     *
     * @param informationCatalogue 资料目录
     * @return 结果
     */
    public int updateInformationCatalogue(SuperviseInformationCatalogue informationCatalogue);

    /**
     * 删除资料目录
     *
     * @param id 资料目录主键
     * @return 结果
     */
    public int deleteInformationCatalogueById(Long id);

    /**
     * 批量删除资料目录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInformationCatalogueByIds(Long[] ids);

    /**
     * 获取当天创建次数
     * @param createTime
     */
    public int getCountByCreateTime(String createTime);

    public List<SuperviseInformationCatalogue> selectInformationCatalogueByIds(@Param("catalogueIds") Set<Long> catalogueIds);

    /**
     * 根据上级id查询列表
     *
     * @param informationCatalogue
     * @return 资料目录
     */
    public List<SuperviseInformationCatalogue> selectInformationCatalogueByParentId(SuperviseInformationCatalogue informationCatalogue);

    public List<SuperviseInformationCatalogueVO> selectInformationCatalogueVOList();

    //public List<SuperviseInformationCatalogueVO> selectInformationCatalogueListOfAuthority(SuperviseInformationCatalogue informationCatalogueVO);

    int getCountByCatalogueParentId(Long[] catalogueIds);

    List<SuperviseInformationCatalogue> selectInformationCatalogues();

    public SuperviseInformationCatalogue selectBHTCatalogue();

}
