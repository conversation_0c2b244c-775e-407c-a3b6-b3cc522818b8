package org.ruoyi.core.controller.guadata;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.domain.DData;
import org.ruoyi.core.domain.Data.DDataT;
import org.ruoyi.core.domain.Data.DDataTByMonthOrYear;
import org.ruoyi.core.service.DDataService;
import org.ruoyi.core.service.impl.SysSelectDataRefServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/system/aData")
public class DataController extends BaseController {
    @Autowired
    private DDataService dDataService;

    @Autowired
    private SysSelectDataRefServiceImpl sysSelectDataRefService;

    /**
     * 运营情况日报表列表查询
     */
    @GetMapping("/list2")
    public TableDataInfo list2(DDataT dDataT)
    {
        DData dData = new DData();
        dData.setPlatformNo(dDataT.getPlatformNo());
        dData.setCustNo(dDataT.getCustNo());
        dData.setPartnerNo(dDataT.getPartnerNo());
        dData.setFundNo(dDataT.getFundNo());
        dData.setProductNo(dDataT.getProductNo());
        dData.setMoreSearch(dDataT.getMoreSearch());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            dDataT.setProductNo(productCode);
        }
        startPage();
        List<DDataT> list = dDataService.queryByPaget(dDataT);
        return getDataTable(list);
    }

    /**
     * 运营情况月报表列表查询
     */
    @GetMapping("/list3")
    public TableDataInfo list3(DDataTByMonthOrYear obj)
    {
        DData dData = new DData();
        dData.setPlatformNo(obj.getPlatformNo());
        dData.setCustNo(obj.getCustNo());
        dData.setPartnerNo(obj.getPartnerNo());
        dData.setFundNo(obj.getFundNo());
        dData.setProductNo(obj.getProductNo());
        dData.setMoreSearch(obj.getMoreSearch());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            obj.setProductNo(productCode);
        }
        startPage();
        List<DDataTByMonthOrYear> list = dDataService.queryMonthByPaget(obj);
        return getDataTable(list);
    }

    /**
     * 运营情况年报表列表查询
     */
    @GetMapping("/list4")
    public TableDataInfo list4(DDataTByMonthOrYear obj)
    {
        DData dData = new DData();
        dData.setPlatformNo(obj.getPlatformNo());
        dData.setCustNo(obj.getCustNo());
        dData.setPartnerNo(obj.getPartnerNo());
        dData.setFundNo(obj.getFundNo());
        dData.setProductNo(obj.getProductNo());
        dData.setMoreSearch(obj.getMoreSearch());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            obj.setProductNo(productCode);
        }
        startPage();
        List<DDataTByMonthOrYear> list = dDataService.queryYearByPaget(obj);
        return getDataTable(list);
    }

    /**
     * 导出 - 运营情况日报表
     */
    @PreAuthorize("@ss.hasPermi('data:external:export2')")
    @Log(title = "导出运营情况日报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export2")
    public void export(HttpServletResponse response, DDataT dDataT)
    {
        DData dData = new DData();
        dData.setPlatformNo(dDataT.getPlatformNo());
        dData.setCustNo(dDataT.getCustNo());
        dData.setPartnerNo(dDataT.getPartnerNo());
        dData.setFundNo(dDataT.getFundNo());
        dData.setProductNo(dDataT.getProductNo());
        dData.setMoreSearch(dDataT.getMoreSearch());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            dDataT.setProductNo(productCode);
        }
        List<DDataT> list = dDataService.exportData2(dDataT);
        ExcelUtil<DDataT> util = new ExcelUtil<DDataT>(DDataT.class);
        util.exportExcel(response, list, "外部系统平台运营情况数据数据");
    }

    /**
     * 导出 - 运营情况月报表
     */
    @PreAuthorize("@ss.hasPermi('data:external:export3')")
    @Log(title = "导出运营情况月报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export3")
    public void export3(HttpServletResponse response)
    {
        DDataTByMonthOrYear dDataTByMonthOrYear = new DDataTByMonthOrYear();
        DData dData = new DData();
        dData.setPlatformNo(dDataTByMonthOrYear.getPlatformNo());
        dData.setCustNo(dDataTByMonthOrYear.getCustNo());
        dData.setPartnerNo(dDataTByMonthOrYear.getPartnerNo());
        dData.setFundNo(dDataTByMonthOrYear.getFundNo());
        dData.setProductNo(dDataTByMonthOrYear.getProductNo());
        dData.setMoreSearch(dDataTByMonthOrYear.getMoreSearch());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            dDataTByMonthOrYear.setProductNo(productCode);
        }
        List<DDataTByMonthOrYear> list = dDataService.queryMonthByPaget(dDataTByMonthOrYear);
        ExcelUtil<DDataTByMonthOrYear> util = new ExcelUtil<DDataTByMonthOrYear>(DDataTByMonthOrYear.class);
        util.exportExcel(response, list, "运营情况月报表");
    }

    /**
     * 导出 - 运营情况年报表
     */
    @PreAuthorize("@ss.hasPermi('data:external:export4')")
    @Log(title = "导出运营情况年报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export4")
    public void export4(HttpServletResponse response)
    {
        DDataTByMonthOrYear dDataTByMonthOrYear = new DDataTByMonthOrYear();
        DData dData = new DData();
        dData.setPlatformNo(dDataTByMonthOrYear.getPlatformNo());
        dData.setCustNo(dDataTByMonthOrYear.getCustNo());
        dData.setPartnerNo(dDataTByMonthOrYear.getPartnerNo());
        dData.setFundNo(dDataTByMonthOrYear.getFundNo());
        dData.setProductNo(dDataTByMonthOrYear.getProductNo());
        dData.setMoreSearch(dDataTByMonthOrYear.getMoreSearch());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            dDataTByMonthOrYear.setProductNo(productCode);
        }
        List<DDataTByMonthOrYear> list = dDataService.queryYearByPaget(dDataTByMonthOrYear);
        ExcelUtil<DDataTByMonthOrYear> util = new ExcelUtil<DDataTByMonthOrYear>(DDataTByMonthOrYear.class);
        util.exportExcel(response, list, "运营情况年报表");
    }
}
