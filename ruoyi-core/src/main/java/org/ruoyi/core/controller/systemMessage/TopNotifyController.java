package org.ruoyi.core.controller.systemMessage;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.ruoyi.core.cwproject.service.ITopNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通知待办信息Controller
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@RestController
@RequestMapping("/system/notify")
public class TopNotifyController extends BaseController
{
    @Autowired
    private ITopNotifyService topNotifyService;

    /**
     * 查询通知待办信息列表
     */
//    @PreAuthorize("@ss.hasPermi('system:notify:list')")
    @GetMapping("/list")
    public TableDataInfo list(TopNotify topNotify)
    {
        //startPage();
        LoginUser loginUser = getLoginUser();
        List<TopNotify> list = topNotifyService.selectTopNotifyList(topNotify,loginUser);
        return getDataTable(list);
    }
    /**
     * 查询通知已完成待办信息列表
     */
//    @PreAuthorize("@ss.hasPermi('system:notify:list')")
    @GetMapping("/accomplishList")
    public TableDataInfo accomplishList(TopNotify topNotify)
    {
        startPage();
        LoginUser loginUser = getLoginUser();
        List<TopNotify> list = topNotifyService.selectAccompLishList(topNotify,loginUser);
        return getDataTable(list);
    }

//    /**
//     * 导出通知待办信息列表
//     */
//    @PreAuthorize("@ss.hasPermi('system:notify:export')")
//    @Log(title = "通知待办信息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, TopNotify topNotify)
//    {
//        List<TopNotify> list = topNotifyService.selectTopNotifyList(topNotify);
//        ExcelUtil<TopNotify> util = new ExcelUtil<TopNotify>(TopNotify.class);
//        util.exportExcel(response, list, "通知待办信息数据");
//    }

    /**
     * 获取通知待办信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:notify:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(topNotifyService.selectTopNotifyById(id));
    }

    /**
     * 新增通知待办信息
     */
    @PreAuthorize("@ss.hasPermi('system:notify:add')")
    @Log(title = "通知待办信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TopNotify topNotify)
    {
        return toAjax(topNotifyService.insertTopNotify(topNotify));
    }

    /**
     * 修改通知待办信息
     */
//    @PreAuthorize("@ss.hasPermi('system:notify:edit')")
    @Log(title = "通知待办信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TopNotify topNotify)
    {
        return toAjax(topNotifyService.updateTopNotify(topNotify));
    }


    /**
     * 删除通知待办信息
     */
    @PreAuthorize("@ss.hasPermi('system:notify:remove')")
    @Log(title = "通知待办信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(topNotifyService.deleteTopNotifyByIds(ids));
    }

    /**
     * 根据流程id查询通知代办信息
     * 使用于一个流程只发送一次通知
     */
    @PostMapping("/getNotifyByProcessId")
    public AjaxResult getNotifyInfo(@RequestBody TopNotify topNotify){
        return AjaxResult.success(topNotifyService.selectTopNotifyInfoByProcessId(topNotify));
    }

    @GetMapping("/updateNotifyStatus")
    public void updateNotify(TopNotify topNotify){
        topNotifyService.updateCuiShenStatus(topNotify);
    }
}
