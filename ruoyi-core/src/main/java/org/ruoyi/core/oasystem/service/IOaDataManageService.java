package org.ruoyi.core.oasystem.service;

import org.ruoyi.core.oasystem.domain.OaDataManage;

import java.util.List;

/**
 * 数据集管理Service接口
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
public interface IOaDataManageService
{
    /**
     * 查询数据集管理
     *
     * @param id 数据集管理主键
     * @return 数据集管理
     */
    public OaDataManage selectOaDataManageById(Long id);

    /**
     * 查询数据集管理列表
     *
     * @param oaDataManage 数据集管理
     * @return 数据集管理集合
     */
    public List<OaDataManage> selectOaDataManageList(OaDataManage oaDataManage);

    /**
     * 新增数据集管理
     *
     * @param oaDataManage 数据集管理
     * @return 结果
     */
    public int insertOaDataManage(OaDataManage oaDataManage);

    /**
     * 修改数据集管理
     *
     * @param oaDataManage 数据集管理
     * @return 结果
     */
    public int updateOaDataManage(OaDataManage oaDataManage);

    /**
     * 批量删除数据集管理
     *
     * @param ids 需要删除的数据集管理主键集合
     * @return 结果
     */
    public int deleteOaDataManageByIds(Long[] ids);

    /**
     * 删除数据集管理信息
     *
     * @param id 数据集管理主键
     * @return 结果
     */
    public int deleteOaDataManageById(Long id);

    /**
     * 根据第一层目录编码查询数据集字典
     * @param firstDataCode
     * @return
     */
    List<OaDataManage> selectDataManageListByCode(String firstDataCode);

    /**
    * 根据第一层目录编码查询数据集字典 并拼接
    * @param firstDataCode
    * @return
    */
    public List<OaDataManage> selectSplicingListByCode(String firstDataCode);
}
