package org.ruoyi.core.oasystem.service.impl;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.oasystem.domain.OaDocumentMonitor;
import org.ruoyi.core.oasystem.mapper.OaDocumentMonitorMapper;
import org.ruoyi.core.oasystem.service.IOaDocumentMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-03
 */
@Service
public class OaDocumentMonitorServiceImpl implements IOaDocumentMonitorService
{
    @Autowired
    private OaDocumentMonitorMapper oaDocumentMonitorMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public OaDocumentMonitor selectOaDocumentMonitorById(Long id)
    {
        return oaDocumentMonitorMapper.selectOaDocumentMonitorById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaDocumentMonitor 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<OaDocumentMonitor> selectOaDocumentMonitorList(OaDocumentMonitor oaDocumentMonitor)
    {
        return oaDocumentMonitorMapper.selectOaDocumentMonitorList(oaDocumentMonitor);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param oaDocumentMonitor 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertOaDocumentMonitor(OaDocumentMonitor oaDocumentMonitor)
    {
        oaDocumentMonitor.setCreateTime(DateUtils.getNowDate());
        return oaDocumentMonitorMapper.insertOaDocumentMonitor(oaDocumentMonitor);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaDocumentMonitor 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateOaDocumentMonitor(OaDocumentMonitor oaDocumentMonitor)
    {
        oaDocumentMonitor.setUpdateTime(DateUtils.getNowDate());
        return oaDocumentMonitorMapper.updateOaDocumentMonitor(oaDocumentMonitor);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaDocumentMonitorByIds(Long[] ids)
    {
        return oaDocumentMonitorMapper.deleteOaDocumentMonitorByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaDocumentMonitorById(Long id)
    {
        return oaDocumentMonitorMapper.deleteOaDocumentMonitorById(id);
    }

    @Override
    public List<Map<String, Object>> queryComList() {

        return oaDocumentMonitorMapper.queryComList();
    }
}
