package org.ruoyi.core.oasystem.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaProcessTemplateUser;

import java.util.List;

/**
 * OA系统-流程模板成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-10
 */
public interface OaProcessTemplateUserMapper 
{
    /**
     * 查询OA系统-流程模板成员
     * 
     * @param templateId OA系统-流程模板成员主键
     * @return OA系统-流程模板成员
     */
    public List<OaProcessTemplateUser> selectByTemplateId(Long templateId);

    /**
     * <AUTHOR>
     * @Description 批量新增
     * @Date 2023/10/10 15:40
     * @Param [oaProcessTemplateUserList]
     * @return int
     **/
    int batchAddOaProcessTemplateUser(@Param("list") List<OaProcessTemplateUser> oaProcessTemplateUserList);
}
