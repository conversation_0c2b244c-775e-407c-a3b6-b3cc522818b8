package org.ruoyi.core.oasystem.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.oasystem.domain.*;
import org.ruoyi.core.oasystem.domain.dto.FlowUserAndNotificationDto;
import org.ruoyi.core.oasystem.domain.vo.FlowPrintHistoryVo;
import org.ruoyi.core.oasystem.domain.vo.MyUseulTemplVo;
import org.ruoyi.core.oasystem.domain.vo.ProcessTemplateVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * OA系统-流程模板Service接口
 *
 * <AUTHOR>
 * @date 2023-07-25
 */
public interface IOaProcessTemplateService
{
    /**
     * 查询OA系统-流程模板主
     *
     * @param id OA系统-流程模板主主键
     * @param status 流程状态查询模板，status == 4,代表已驳回，查询模板历史表
     * @return OA系统-流程模板主
     */
    public ProcessTemplateVo selectOaProcessTemplateById(Long id,String status);

    /**
     * 查询OA系统-流程模板主列表
     *
     * @param oaProcessTemplate OA系统-流程模板主
     * @return OA系统-流程模板主集合
     */
    public List<OaProcessTemplate> selectOaProcessTemplateList(OaProcessTemplate oaProcessTemplate);

    /**
     * 新增OA系统-流程模板主
     *
     * @param oaProcessTemplate OA系统-流程模板主
     * @return 结果
     */
    public int insertOaProcessTemplate(OaProcessTemplate oaProcessTemplate, LoginUser loginUser);

    /**
     * 修改OA系统-流程模板主
     *
     * @param oaProcessTemplate OA系统-流程模板主
     * @return 结果
     */
    public int updateOaProcessTemplate(OaProcessTemplate oaProcessTemplate, LoginUser loginUser);

    /**
     * 批量删除OA系统-流程模板主
     *
     * @param ids 需要删除的OA系统-流程模板主主键集合
     * @return 结果
     */
    public int deleteOaProcessTemplateByIds(Long[] ids);

    /**
     * 删除OA系统-流程模板主信息
     *
     * @param id OA系统-流程模板主主键
     * @return 结果
     */
    public int deleteOaProcessTemplateById(Long id);

    int updateEnable(OaProcessTemplate oaProcessTemplate, LoginUser loginUser);

    /**
     * 通知方式里的对本流程启用状态修改
     */
    int changeNotificationProcessEnable(OaProcessTemplateNotification oaProcessTemplateNotification, LoginUser loginUser);

    /**
     * 查找用户列表
     */
    List<SysUserForOaProcessTemplate> selectUserListForOaProcessTemplate(SysUser user);

    /**
     * 查找全局参数列表
     */
    List<OaProcessTemplateGlobalSetting> selectOaProcessTemplateGlobalSetting();

    /**
     * 全局参数设置 - 修改状态
     */
    int updateGlobalSettingEnableStatus(OaProcessTemplateGlobalSetting oaProcessTemplateGlobalSetting, LoginUser loginUser);

    /**
     * 获取流程模板的修改记录
     */
    List<OaProcessTemplateUpdateInfo> selectOaProcessTemplateUpdateInfoList(OaProcessTemplate oaProcessTemplate);

    /**
     * 获取我的常用流程
     */
    Map<String, Object> selectMyUsualProcessList(MyUsualProcess myUsualProcess, LoginUser loginUser);

    /**
     * 获取公司列表
     */
    List<Map<String, Object>> companyList(LoginUser loginUser);

    public Integer getCompanyId(Long classId);


    List<Map<String, Object>> allCompanyList();
    /**
     * 查找用户列表 - 发起流程用
     */
    List<SysUserForOaProcessTemplate> selectUserListForOaProcessTemplateForFlow(SysUser user, Long templateId);

    /**
     * 流程发起后，新增可查阅用户列表
     */
    int flowAddUserListAndNotification(FlowUserAndNotificationDto flowUserAndNotificationDto, LoginUser loginUser);

    /**
     * 根据businessId查询发起表单的可阅览人和代办通知方式
     */
    Map<String, Object> flowUserListInfoAndNotificationInfo(String businessId);

    /**
     * 查询全局参数配置
     */
    Map<String, Object> getGlobalSettingStatus();

    /**
     * 根据businessId查询打印记录
     */
    List<FlowPrintHistoryVo> flowPrintHistory(String businessId);

    /**
     * 根据businessId新增打印记录
     */
    int addPrintHistory(String businessId, LoginUser loginUser);

    /**
     * 更新流程属性（可阅览人 和 代办通知方式）
     */
    int flowUpdateUserListAndNotification(FlowUserAndNotificationDto flowUserAndNotificationDto, LoginUser loginUser);

    public List<OaProcessTemplate> queryOaProcessTemplateList(OaProcessTemplate oaProcessTemplate);

    List<OaProcessTemplate> queryTemplByRole(OaProcessTemplate oaProcessTemplate,LoginUser loginUser);

    List<Map<String, Object>> getTabsCompanyList(LoginUser loginUser);

    void addMyUseualTempl(MyUseulTemplVo myUseulTemplVo, LoginUser loginUser);

    List<OaProcessTemplate> queryTemplateListByClass(OaProcessTemplate oaProcessTemplate);

    List<Map<String, Object>> getAllCompany();

    List<OaProcessTemplate> queryTemplateListByCompanyId(OaProcessTemplate oaProcessTemplate);

    //审批时上传文件
    Map<String, Object> uploadFile(MultipartFile file, String stepId, String businessId, LoginUser loginUser, String soleFlag, String commitStatus);

    //审批时删除文件
    AjaxResult deleteFileByUrl(Map<String, String> obj);

    //根据id删除我的流程中的某个流程
    AjaxResult deleteMyUsualProcessById(Long id, LoginUser loginUser);

    OaProcessTemplate getTemplateByModuleType(OaProcessTemplate oaProcessTemplate);

    OaProcessTemplate queryTemplateByOaModuleTypeCompanyId(OaProcessTemplate oaProcessTemplate);

    OaProcessTemplate queryTemplateByProcIdAndCompanyId(OaProcessTemplate oaProcessTemplate);

    /**
     * 通过父模板ID查询OA系统-流程主模板最新模板ID
     *
     * @param parentId OA系统-流程模板主主键
     * @return OA系统-流程模板主
     */
     Long selectTemplateIdByParentId(Long parentId);

    Map<String, Object> getDataByTemplName(OaProcessTemplate oaProcessTemplate, LoginUser loginUser);

    List<Map<String, Object>> getCheckTableField(OaFormField oaFormField);
}
