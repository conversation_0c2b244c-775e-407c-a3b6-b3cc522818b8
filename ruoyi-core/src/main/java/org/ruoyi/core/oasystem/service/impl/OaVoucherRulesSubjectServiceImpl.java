package org.ruoyi.core.oasystem.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.oasystem.domain.OaVoucherRulesSubject;
import org.ruoyi.core.oasystem.mapper.OaVoucherRulesSubjectMapper;
import org.ruoyi.core.oasystem.service.IOaVoucherRulesSubjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
@Service
public class OaVoucherRulesSubjectServiceImpl implements IOaVoucherRulesSubjectService
{
    @Autowired
    private OaVoucherRulesSubjectMapper oaVoucherRulesSubjectMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public OaVoucherRulesSubject selectOaVoucherRulesSubjectById(Long id)
    {
        return oaVoucherRulesSubjectMapper.selectOaVoucherRulesSubjectById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaVoucherRulesSubject 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<OaVoucherRulesSubject> selectOaVoucherRulesSubjectList(OaVoucherRulesSubject oaVoucherRulesSubject)
    {
        return oaVoucherRulesSubjectMapper.selectOaVoucherRulesSubjectList(oaVoucherRulesSubject);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param oaVoucherRulesSubject 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertOaVoucherRulesSubject(OaVoucherRulesSubject oaVoucherRulesSubject)
    {
        oaVoucherRulesSubject.setCreateTime(DateUtils.getNowDate());
        return oaVoucherRulesSubjectMapper.insertOaVoucherRulesSubject(oaVoucherRulesSubject);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaVoucherRulesSubject 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateOaVoucherRulesSubject(OaVoucherRulesSubject oaVoucherRulesSubject)
    {
        oaVoucherRulesSubject.setUpdateTime(DateUtils.getNowDate());
        return oaVoucherRulesSubjectMapper.updateOaVoucherRulesSubject(oaVoucherRulesSubject);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaVoucherRulesSubjectByIds(Long[] ids)
    {
        return oaVoucherRulesSubjectMapper.deleteOaVoucherRulesSubjectByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaVoucherRulesSubjectById(Long id)
    {
        return oaVoucherRulesSubjectMapper.deleteOaVoucherRulesSubjectById(id);
    }
}
