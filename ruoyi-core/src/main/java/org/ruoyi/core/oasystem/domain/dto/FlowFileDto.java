package org.ruoyi.core.oasystem.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 审批流程文件对象
 *
 * @Description
 * @<PERSON><PERSON>
 * @Date 2023/9/22 13:43
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class FlowFileDto {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String businessId;

    private String stepId;

    private String soleFlag;

    private String fileName;

    private String url;

    private Long uploadUserId;

    private String createBy;

    private Date createTime;

    private String updateBy;

    private Date updateTime;

    private String commitStatus;
}
