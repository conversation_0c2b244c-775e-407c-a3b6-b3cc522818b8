package org.ruoyi.core.oasystem.controller;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.oasystem.domain.OaDataManage;
import org.ruoyi.core.oasystem.service.IOaDataManageService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.List;

/**
 * 数据集管理Controller
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@RestController
@RequestMapping("/systemData/manage")
public class OaDataManageController extends BaseController
{
    @Autowired
    private IOaDataManageService oaDataManageService;

    /**
     * 查询数据集管理列表
     */
    // @PreAuthorize("@ss.hasPermi('oa:dataSet:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody OaDataManage oaDataManage)
    {
        startPage();
        List<OaDataManage> list = oaDataManageService.selectOaDataManageList(oaDataManage);
        return getDataTable(list);
    }

    /**
     * 导出数据集管理列表
     */
    @Log(title = "数据集管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaDataManage oaDataManage)
    {
        List<OaDataManage> list = oaDataManageService.selectOaDataManageList(oaDataManage);
        ExcelUtil<OaDataManage> util = new ExcelUtil<OaDataManage>(OaDataManage.class);
        util.exportExcel(response, list, "数据集管理数据");
    }

    /**
     * 获取数据集管理详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(oaDataManageService.selectOaDataManageById(id));
    }

    /**
     * 新增数据集管理
     */
    @Log(title = "数据集管理", businessType = BusinessType.INSERT)
    @PreAuthorize("@ss.hasPermi('oa:dataSet:add')")
    @PostMapping
    public AjaxResult add(@RequestBody OaDataManage oaDataManage)
    {
        return toAjax(oaDataManageService.insertOaDataManage(oaDataManage));
    }

    /**
     * 修改数据集管理
     */
    @Log(title = "数据集管理", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('oa:dataSet:update')")
    @PutMapping
    public AjaxResult edit(@RequestBody OaDataManage oaDataManage)
    {
        return toAjax(oaDataManageService.updateOaDataManage(oaDataManage));
    }

    /**
     * 批量删除数据集管理
     */
    @Log(title = "数据集管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(oaDataManageService.deleteOaDataManageByIds(ids));
    }

    /**
     * 删除数据集管理
     */
    @Log(title = "数据集管理", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('oa:dataSet:delete')")
	@DeleteMapping("/deleteData/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(oaDataManageService.deleteOaDataManageById(id));
    }

    /**
     * 查询数据集管理列表
     */
    @GetMapping("/dataManageList")
    public TableDataInfo getDataManageList(@RequestParam(value = "firstDataCode", required = false) String firstDataCode)
    {
        startPage();
        List<OaDataManage> list = oaDataManageService.selectDataManageListByCode(firstDataCode);
        return getDataTable(list);
    }

    @GetMapping("/splicingListByCode")
    public TableDataInfo selectSplicingListByCode(@RequestParam(value = "firstDataCode", required = false) String firstDataCode)
    {
        startPage();
        List<OaDataManage> list = oaDataManageService.selectSplicingListByCode(firstDataCode);
        return getDataTable(list);
    }
}
