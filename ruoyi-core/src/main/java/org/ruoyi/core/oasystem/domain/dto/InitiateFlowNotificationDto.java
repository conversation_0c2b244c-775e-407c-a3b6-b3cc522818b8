package org.ruoyi.core.oasystem.domain.dto;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 流程发起后，新增用户列表
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class InitiateFlowNotificationDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long id;

    /** 流程模板id */
    private Long templateId;

    /** 流程发起后的主键 */
    private String procFormDataId;

    /** 通知途径 0-企业微信 1-邮件 */
    private String notificationType;

    /** 启用状态 0-启用 1关闭 */
    private String processEnable;
}
