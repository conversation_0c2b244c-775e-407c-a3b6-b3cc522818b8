package org.ruoyi.core.oasystem.mapper;

import java.util.List;
import org.ruoyi.core.oasystem.domain.SysVersionMaintain;

/**
 * 版本维护Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
public interface SysVersionMaintainMapper 
{
    /**
     * 查询版本维护
     * 
     * @param id 版本维护主键
     * @return 版本维护
     */
    public SysVersionMaintain selectSysVersionMaintainById(Long id);

    /**
     * 查询版本维护列表
     * 
     * @param sysVersionMaintain 版本维护
     * @return 版本维护集合
     */
    public List<SysVersionMaintain> selectSysVersionMaintainList(SysVersionMaintain sysVersionMaintain);

    /**
     * 新增版本维护
     * 
     * @param sysVersionMaintain 版本维护
     * @return 结果
     */
    public int insertSysVersionMaintain(SysVersionMaintain sysVersionMaintain);

    /**
     * 修改版本维护
     * 
     * @param sysVersionMaintain 版本维护
     * @return 结果
     */
    public int updateSysVersionMaintain(SysVersionMaintain sysVersionMaintain);

    /**
     * 删除版本维护
     * 
     * @param id 版本维护主键
     * @return 结果
     */
    public int deleteSysVersionMaintainById(Long id);

    /**
     * 批量删除版本维护
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysVersionMaintainByIds(Long[] ids);

    /**
     * 查询版本信息
     * @return
     */
    SysVersionMaintain queryVersionInfo();

}
