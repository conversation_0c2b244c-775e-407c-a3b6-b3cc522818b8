package org.ruoyi.core.oasystem.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.oasystem.domain.OaProjectPayerAssociation;
import org.ruoyi.core.oasystem.mapper.OaProjectPayerAssociationMapper;
import org.ruoyi.core.oasystem.service.IOaProjectPayerAssociationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-06
 */
@Service
public class OaProjectPayerAssociationServiceImpl implements IOaProjectPayerAssociationService
{
    @Autowired
    private OaProjectPayerAssociationMapper oaProjectPayerAssociationMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public OaProjectPayerAssociation selectOaProjectPayerAssociationById(Long id)
    {
        return oaProjectPayerAssociationMapper.selectOaProjectPayerAssociationById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaProjectPayerAssociation 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<OaProjectPayerAssociation> selectOaProjectPayerAssociationList(OaProjectPayerAssociation oaProjectPayerAssociation)
    {
        return oaProjectPayerAssociationMapper.selectOaProjectPayerAssociationList(oaProjectPayerAssociation);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param oaProjectPayerAssociation 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertOaProjectPayerAssociation(OaProjectPayerAssociation oaProjectPayerAssociation)
    {
        oaProjectPayerAssociation.setCreateTime(DateUtils.getNowDate());
        return oaProjectPayerAssociationMapper.insertOaProjectPayerAssociation(oaProjectPayerAssociation);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaProjectPayerAssociation 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateOaProjectPayerAssociation(OaProjectPayerAssociation oaProjectPayerAssociation)
    {
        oaProjectPayerAssociation.setUpdateTime(DateUtils.getNowDate());
        return oaProjectPayerAssociationMapper.updateOaProjectPayerAssociation(oaProjectPayerAssociation);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaProjectPayerAssociationByIds(Long[] ids)
    {
        return oaProjectPayerAssociationMapper.deleteOaProjectPayerAssociationByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaProjectPayerAssociationById(Long id)
    {
        return oaProjectPayerAssociationMapper.deleteOaProjectPayerAssociationById(id);
    }
}
