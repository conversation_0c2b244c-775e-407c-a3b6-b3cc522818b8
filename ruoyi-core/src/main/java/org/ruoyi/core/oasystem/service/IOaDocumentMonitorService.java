package org.ruoyi.core.oasystem.service;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.oasystem.domain.OaDocumentMonitor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IOaDocumentMonitorService.java
 * @Description TODO
 * @createTime 2023年07月03日 16:24:00
 */
public interface IOaDocumentMonitorService {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaDocumentMonitor selectOaDocumentMonitorById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param oaDocumentMonitor 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<OaDocumentMonitor> selectOaDocumentMonitorList(OaDocumentMonitor oaDocumentMonitor);
    /**
     * 新增【请填写功能名称】
     *
     * @param oaDocumentMonitor 【请填写功能名称】
     * @return 结果
     */
    public int insertOaDocumentMonitor(OaDocumentMonitor oaDocumentMonitor);
    /**
     * 修改【请填写功能名称】
     *
     * @param oaDocumentMonitor 【请填写功能名称】
     * @return 结果
     */
    public int updateOaDocumentMonitor(OaDocumentMonitor oaDocumentMonitor);
    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaDocumentMonitorByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaDocumentMonitorById(Long id);

    List<Map<String, Object>> queryComList();
}
