package org.ruoyi.core.oasystem.domain;

import com.ruoyi.common.core.domain.entity.SysDept;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * OA系统-流程分类对象 oa_process_classification
 * 
 * <AUTHOR>
 * @date 2023-08-07
 */
public class OaProcessClassification extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 父id */
    @Excel(name = "父id")
    private Long parentId;

    /** 祖级列表 */
    @Excel(name = "祖级列表")
    private String ancestors;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer orderNum;

    private Integer companyId;

    /** 是否是公司（0是 1不是） */
    @Excel(name = "是否是公司", readConverterExp = "0=是,1=不是")
    private String isCompany;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /** 子部门 */
    private List<OaProcessClassification> children = new ArrayList<OaProcessClassification>();

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setAncestors(String ancestors) 
    {
        this.ancestors = ancestors;
    }

    public void setIsCompany(String isCompany)
    {
        this.isCompany = isCompany;
    }

    public String getIsCompany()
    {
        return isCompany;
    }
    public String getAncestors() 
    {
        return ancestors;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public List<OaProcessClassification> getChildren() {
        return children;
    }

    public void setChildren(List<OaProcessClassification> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return "OaProcessClassification{" +
                "id=" + id +
                ", parentId=" + parentId +
                ", ancestors='" + ancestors + '\'' +
                ", name='" + name + '\'' +
                ", orderNum=" + orderNum +
                ", companyId=" + companyId +
                ", isCompany='" + isCompany + '\'' +
                ", status='" + status + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", children=" + children +
                '}';
    }
}
