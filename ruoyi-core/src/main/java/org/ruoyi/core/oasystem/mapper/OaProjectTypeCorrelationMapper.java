package org.ruoyi.core.oasystem.mapper;

import org.ruoyi.core.oasystem.domain.OaProjectTypeCorrelation;
import org.ruoyi.core.oasystem.domain.vo.OaProjectTypeCorrelationVo;

import java.util.List;

/**
 * 项目类型与功能关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface OaProjectTypeCorrelationMapper
{
    /**
     * 查询项目类型与功能关联
     *
     * @param id 项目类型与功能关联主键
     * @return 项目类型与功能关联
     */
    public OaProjectTypeCorrelationVo selectOaProjectTypeCorrelationById(Long id);

    public OaProjectTypeCorrelationVo selectOaProjectTypeCorrelationByProjectPortfolioCode(String projectPortfolioCode);
    /**
     * 查询项目类型与功能关联列表
     *
     * @param oaProjectTypeCorrelation 项目类型与功能关联
     * @return 项目类型与功能关联集合
     */
    public List<OaProjectTypeCorrelationVo> selectOaProjectTypeCorrelationList(OaProjectTypeCorrelation oaProjectTypeCorrelation);

    /**
     * 新增项目类型与功能关联
     *
     * @param oaProjectTypeCorrelation 项目类型与功能关联
     * @return 结果
     */
    public int insertOaProjectTypeCorrelation(OaProjectTypeCorrelation oaProjectTypeCorrelation);

    /**
     * 修改项目类型与功能关联
     *
     * @param oaProjectTypeCorrelation 项目类型与功能关联
     * @return 结果
     */
    public int updateOaProjectTypeCorrelation(OaProjectTypeCorrelation oaProjectTypeCorrelation);

    /**
     * 删除项目类型与功能关联
     *
     * @param id 项目类型与功能关联主键
     * @return 结果
     */
    public int deleteOaProjectTypeCorrelationById(Long id);

    /**
     * 批量删除项目类型与功能关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaProjectTypeCorrelationByIds(Long[] ids);
}
