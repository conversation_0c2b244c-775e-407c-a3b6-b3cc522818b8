package org.ruoyi.core.oasystem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * OA编辑审批功能通用编辑审批记录表 - 实体类
 *
 * @Description
 * <AUTHOR>
 * @Date 2023/12/28 16:31
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaEditApproveGeneralityEditRecords extends BaseEntity {
    private static final long serialVersionUID = 1L;

    //主键
    private Long id;

    //代表的相关功能。1付款人配置，2收款人配置，3项目与流程关联，4项目名称配置，9收/付款人配置
    private String oaApplyType;

    //OA功能相关的申请id
    private Long oaApplyId;

    //记录表修改前id
    private Long oaApplyRecordsOldId;

    //记录表修改后id
    private Long oaApplyRecordsNewId;

    //编辑人员id
    private Long editUserId;

    //编辑时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date editTime;

    //修改说明
    private String editInfo;

    //审核人id
    private Long checkUserId;

    //审批时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    //审核状态，0待业务审核，1待财务审核
    private String checkStatus;

    //状态：0正常 1禁用（禁用状态说明本流程已经通过审核了）
    private String status;

    //驳回标识，0通过，1驳回
    private String rejectFlag;

    //驳回原因，当驳回标识为1时，本字段不能为空
    private String checkRejectInfo;

    //是否知悉，0未知悉，1已知悉
    private String confirmFlag;

    //责任人是否知悉，0未知悉，1已知悉（项目名称配置用）
    private String responsibilityConfirmFlag;



    //联表展示数据
    //记录表修改前数据
    private String oaApplyRecordsOldData;

    //记录表修改后数据
    private String oaApplyRecordsNewData;

    //编辑人员姓名
    private String editUserNickName;

    //审批人姓名
    private String checkUserNickName;

    //流程关联id
    private String processId;

    //删除标识;0-正常 1-删除
    private String delFlag;

    //操作的类型;0-新增,1-修改,2-删除
    private String editType;
}
