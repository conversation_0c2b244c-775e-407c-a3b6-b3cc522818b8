package org.ruoyi.core.oasystem.controller;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserPost;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.mapper.SysCompanyMapper;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.oasystem.domain.OaTrader;
import org.ruoyi.core.oasystem.domain.bo.OaTraderBo;
import org.ruoyi.core.oasystem.domain.bo.UpdateNewOaTraderBo;
import org.ruoyi.core.oasystem.domain.vo.OaEditApproveGeneralityEditRecordsVo;
import org.ruoyi.core.oasystem.domain.vo.OaTraderVo;
import org.ruoyi.core.oasystem.service.IOaCommonService;
import org.ruoyi.core.oasystem.service.IOaTraderService;
import org.ruoyi.core.oasystem.service.IOaVoucherRulesMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2023-06-28
 */
@RestController
@RequestMapping("/oasystem/trader")
public class OaTraderController extends BaseController
{
    @Autowired
    private IOaTraderService oaTraderService;

    @Autowired
    private IOaCommonService oaCommonService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IOaVoucherRulesMainService oaVoucherRulesMainService;

    @Autowired
    private SysCompanyMapper sysCompanyMapper;

    /**
     * 查询【请填写功能名称】列表
     */
    //@PreAuthorize("@ss.hasPermi('system:trader:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaTrader oaTrader, String selectType)
    {
//        //todo 暂时加的假参数，后续这个参数前端补 selectType--->传过来的视图类型
//        if (selectType == null) {
//            selectType = "0";
//        }
//        LoginUser loginUser = getLoginUser();
//        //进行公司的过滤
//        Map<String, Object> paramMap = oaCommonService.selectListBeforeParam(loginUser, selectType, "0".equals(oaTrader.getTraderType())?"1":"2");
//        //权限控制，主要是过滤公司
//        List<Long> companyIdList = (List<Long>) paramMap.get("companyIdList");
//        //过滤视图
//        List<Long> filterOaApplyId = null;
//        if (!"0".equals(selectType)) {
//            filterOaApplyId = (List<Long>) paramMap.get("filterOaApplyId");
//        }
        //先处理一下传过来要查询的交易人类型是否是多条件搜索
        String traderType = oaTrader.getTraderType();
        if (traderType != null) {
            if (traderType.contains(",")) {
                oaTrader.setTraderTypeList(Arrays.stream(traderType.split(",")).collect(Collectors.toList()));
                oaTrader.setTraderType(null);
            }
        }
        //找用户可以看到的公司id集合
        Long userId = getLoginUser().getUserId();
        SysUser sysUser = userService.selectUserById(userId);
        List<Long> companyIdList = sysUser.getUserPostList().stream().map(SysUserPost::getUnitId).collect(Collectors.toList());
        //找是否开启了OA流程审核  之前的入参  付款人是2，收款人是3。收付款人两个页面合并之后，收/付款人改为2
        String isEnable = oaVoucherRulesMainService.selectIsEnableStatusByProjectType("2");
        startPage();
        List<OaTraderVo> list = oaTraderService.selectOaTraderListNew(oaTrader, companyIdList, isEnable);
//        List<OaTraderVo> list = oaTraderService.selectOaTraderListByParam(oaTrader, selectType, companyIdList, filterOaApplyId);
//        Long total = null;
//        if ("0".equals(selectType) || selectType == null) {
//            total = oaTraderService.selectOaTraderListTotal(oaTrader, companyIdList);
//        } else {
//            //只有在查询我的审批或者我的提交时，会调用本方法。此时，selectType要么是1要么是2，loginUser绝对不会为空
//            if (list.size() > 0) {
//                total = oaTraderService.selectOaTraderListTotalByOaTraderAndCompanyIdListAndFilterOaApplyIdList(oaTrader, companyIdList, filterOaApplyId);
//            } else {
//                total = 0L;
//            }
//        }
//        return getDataTableForTotal(list, total);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    //@PreAuthorize("@ss.hasPermi('system:trader:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaTrader oaTrader, String selectType)
    {
        LoginUser loginUser = getLoginUser();
        //进行公司的过滤
        Map<String, Object> paramMap = oaCommonService.selectListBeforeParam(loginUser, selectType, "1");
        //权限控制，主要是过滤公司
        List<Long> companyIdList = (List<Long>) paramMap.get("companyIdList");
        //过滤视图
        List<Long> filterOaApplyId = null;
        if (!"0".equals(selectType)) {
            filterOaApplyId = (List<Long>) paramMap.get("filterOaApplyId");
        }
        List<OaTraderVo> list = oaTraderService.selectOaTraderListByParam(oaTrader, selectType, companyIdList, filterOaApplyId);
        ExcelUtil<OaTraderVo> util = new ExcelUtil<OaTraderVo>(OaTraderVo.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:trader:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(oaTraderService.selectOaTraderById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    // @PreAuthorize("@ss.hasPermi('system:trader:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaTrader oaTrader)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(oaTraderService.insertOaTrader(oaTrader,loginUser));
    }

    /**
     * 新增收付款人（新逻辑，不参与OA流程审批）
     */
    @PostMapping("/addNew")
    public AjaxResult addNew(@RequestBody OaTrader oaTrader)
    {
        String userNameTrim = oaTrader.getUserName().trim();
        String bankOfDepositTrim = oaTrader.getBankOfDeposit().trim();
        String accountNumberTrim = oaTrader.getAccountNumber().trim();
        String abbreviationTrim = oaTrader.getAbbreviation().trim();
        oaTrader.setUserName(userNameTrim);
        oaTrader.setBankOfDeposit(bankOfDepositTrim);
        oaTrader.setAccountNumber(accountNumberTrim);
        oaTrader.setAbbreviation(abbreviationTrim);
        LoginUser loginUser = getLoginUser();
        return toAjax(oaTraderService.insertOaTraderNew(oaTrader, loginUser));
    }

    /**
     * 编辑收付款人（新逻辑，不参与OA流程审批）
     */
    @PutMapping("/editNew")
    public AjaxResult editNew(@RequestBody UpdateNewOaTraderBo updateNewOaTraderBo)
    {
        OaTrader newOaTrader = updateNewOaTraderBo.getNewOaTrader();
        String userNameTrim = newOaTrader.getUserName().trim();
        String bankOfDepositTrim = newOaTrader.getBankOfDeposit().trim();
        String accountNumberTrim = newOaTrader.getAccountNumber().trim();
        String abbreviationTrim = newOaTrader.getAbbreviation().trim();
        newOaTrader.setUserName(userNameTrim);
        newOaTrader.setBankOfDeposit(bankOfDepositTrim);
        newOaTrader.setAccountNumber(accountNumberTrim);
        newOaTrader.setAbbreviation(abbreviationTrim);
        LoginUser loginUser = getLoginUser();
        return toAjax(oaTraderService.updateOaTraderNew(updateNewOaTraderBo, loginUser));
    }

    /**
     * 修改【请填写功能名称】
     */
    // @PreAuthorize("@ss.hasPermi('system:trader:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaTrader oaTrader)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(oaTraderService.updateOaTrader(oaTrader,loginUser));
    }

    /**
     * 删除【请填写功能名称】
     */
    // @PreAuthorize("@ss.hasPermi('system:trader:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(oaTraderService.deleteOaTraderByIds(ids));
    }


    /**
     * 状态修改
     */
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody OaTrader user)
    {
        user.setUpdateBy(getUsername());
        LoginUser loginUser = getLoginUser();
        return toAjax(oaTraderService.updateEnable(user,loginUser));
    }

    /**
     * 查询是否有重复
     */
    @PostMapping("/checkRepeat")
    public Map<String,Object> checkOaTraderRepeat(@RequestBody OaTrader oaTrader)
    {
        String userNameTrim = oaTrader.getUserName().trim();
        String bankOfDepositTrim = oaTrader.getBankOfDeposit().trim();
        String accountNumberTrim = oaTrader.getAccountNumber().trim();
        String abbreviationTrim = oaTrader.getAbbreviation().trim();
        oaTrader.setUserName(userNameTrim);
        oaTrader.setBankOfDeposit(bankOfDepositTrim);
        oaTrader.setAccountNumber(accountNumberTrim);
        oaTrader.setAbbreviation(abbreviationTrim);
       return oaTraderService.checkOaTrader(oaTrader);
    }


    @GetMapping("/CollectionList")
    public TableDataInfo getCollectionList()
    {
        OaTrader oaTrader = new OaTrader();
        oaTrader.setTraderType("1");
        List<OaTrader> list = oaTraderService.selectOaTraderList(oaTrader);
        return getDataTable(list);
    }



    @GetMapping("/selectList")
    public List<Map<String, Object>> getselectList(OaTrader oaTrader)
    {
        List<Map<String, Object>> dataByParams = oaTraderService.getDataByParams(oaTrader);
        return dataByParams;
    }

    /**
     * 每次进行增删改操作，进行留痕记录
     */
    @PostMapping("/addNewEditInfo")
    public AjaxResult addNewEditInfo(@RequestBody OaTraderBo oaTraderBo) {
        String userNameTrim = oaTraderBo.getUserName().trim();
        String bankOfDepositTrim = oaTraderBo.getBankOfDeposit().trim();
        String accountNumberTrim = oaTraderBo.getAccountNumber().trim();
        String abbreviationTrim = oaTraderBo.getAbbreviation().trim();
        oaTraderBo.setUserName(userNameTrim);
        oaTraderBo.setBankOfDeposit(bankOfDepositTrim);
        oaTraderBo.setAccountNumber(accountNumberTrim);
        oaTraderBo.setAbbreviation(abbreviationTrim);
        LoginUser loginUser = getLoginUser();
        //老逻辑
//        oaCommonService.addNewEditInfo(oaTraderBo, loginUser);
        PageHelper.clearPage();
        return toAjax(oaCommonService.addNewEditInfoNew(oaTraderBo, loginUser));
    }

    /**
     * OA流程处理完之后，调用此接口
     * checkFlag 1-通过 2-驳回
     */
    @PostMapping("/oaCheckHandle")
    public AjaxResult oaChekHandle(@RequestBody OaTraderBo oaTraderBo) {
        LoginUser loginUser = getLoginUser();
        PageHelper.clearPage();
        //先查对应用到的id
        return toAjax(oaCommonService.oaChekHandle(oaTraderBo.getProcessId(), oaTraderBo.getCheckFlag()));
    }

    /**
     * 点击审核，出现的具体信息
     */
    @GetMapping("/detail")
    public AjaxResult detail(String oaApplyType, Long oaApplyId)
    {
        OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo = oaCommonService.selectOaEditApproveGeneralityEditRecordsDetailByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
        return AjaxResult.success(oaEditApproveGeneralityEditRecordsVo);
    }

    /**
     * 审批
     */
    @PutMapping("/check")
    public AjaxResult check(@RequestBody OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(oaCommonService.checkOaEditApproveGenerality(oaEditApproveGeneralityEditRecordsVo, loginUser));
    }

    /**
     * 知悉操作
     */
    @PutMapping("/confirm")
    public AjaxResult confirm(@RequestBody OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(oaCommonService.confirmOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecordsVo, loginUser));
    }

    /**
     * 查询编辑记录
     */
    @GetMapping("/selectEditRecord")
    public AjaxResult selectEditRecord(Long oaApplyId)
    {
        //老逻辑
//        List<OaEditApproveGeneralityEditRecordsVo> oaVoucherRulesEditRecordsVos = oaCommonService.selectEditRecordByOaVoucherRulesMain(oaApplyType, oaApplyId);
        startPage();
        List<OaEditApproveGeneralityEditRecordsVo> oaVoucherRulesEditRecordsVos = oaCommonService.selectEditRecordByOaVoucherRulesMainNew(oaApplyId);
        return AjaxResult.success(oaVoucherRulesEditRecordsVos);
    }

    /**
     * 视图显示的条数
     */
    @GetMapping("/viewCount")
    public AjaxResult viewCount(OaTrader oaTrader)
    {
        LoginUser loginUser = getLoginUser();
        //查全部的条数
        Map<String, Object> paramMap = oaCommonService.selectListBeforeParam(loginUser, "0", "0".equals(oaTrader.getTraderType())?"1":"2");
        List<Long> companyIdList = (List<Long>) paramMap.get("companyIdList");
        Long allCount = oaTraderService.selectOaTraderListTotal(oaTrader, companyIdList);
        if (allCount == null) {
            allCount = 0L;
        }
        //查待我审核的条数
        Map<String, Object> paramMap1 = oaCommonService.selectListBeforeParam(loginUser, "1", "0".equals(oaTrader.getTraderType())?"1":"2");
        List<Long> companyIdList1 = (List<Long>) paramMap1.get("companyIdList");
        List<Long> filterOaApplyId1 = (List<Long>) paramMap1.get("filterOaApplyId");
        Long myCheckCount = null;
        if (filterOaApplyId1 != null) {
            myCheckCount = oaTraderService.selectOaTraderListTotalByOaTraderAndCompanyIdListAndFilterOaApplyIdList(oaTrader, companyIdList1, filterOaApplyId1);
        }
        if (myCheckCount == null) {
            myCheckCount = 0L;
        }
        //查我的提交的条数
        Map<String, Object> paramMap2 = oaCommonService.selectListBeforeParam(loginUser, "2", "0".equals(oaTrader.getTraderType())?"1":"2");
        List<Long> companyIdList2 = (List<Long>) paramMap2.get("companyIdList");
        List<Long> filterOaApplyId2 = (List<Long>) paramMap2.get("filterOaApplyId");
        Long mySubmitCount = null;
        if (filterOaApplyId2 != null) {
            mySubmitCount = oaTraderService.selectOaTraderListTotalByOaTraderAndCompanyIdListAndFilterOaApplyIdList(oaTrader, companyIdList2, filterOaApplyId2);
        }
        if (mySubmitCount == null) {
            mySubmitCount = 0L;
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("allCount", allCount);
        returnMap.put("myCheckCount", myCheckCount);
        returnMap.put("mySubmitCount", mySubmitCount);
        return AjaxResult.success(returnMap);
    }

    /**
     * 所属账套下拉框
     */
    @GetMapping("/selectAccountInfo")
    public AjaxResult selectAccountInfo(String queryAllFlag)
    {
        if (queryAllFlag == null) {
            Long userId = getLoginUser().getUserId();
            SysUser sysUser = userService.selectUserById(userId);
            List<Long> companyIdList = sysUser.getUserPostList().stream().map(SysUserPost::getUnitId).collect(Collectors.toList());
            return AjaxResult.success(oaTraderService.selectAccountInfo(companyIdList, queryAllFlag));
        } else {
            return AjaxResult.success(oaTraderService.selectAccountInfo(null, queryAllFlag));
        }
    }

    /**
     * 公司下拉框
     */
    @GetMapping("/selectCompanyInfo")
    public AjaxResult selectCompanyInfo(String queryAllFlag)
    {
        Long userId = getLoginUser().getUserId();
        if (queryAllFlag == null) {
            SysUser sysUser = userService.selectUserById(userId);
            List<Map<String, Object>> collect = sysUser.getUserPostList().stream().map(t -> {
                Map<String, Object> map = new HashMap<>();
                map.put("companyId", t.getUnitId());
                map.put("name", t.getUnitName());
                return map;
            }).collect(Collectors.toList());
            return AjaxResult.success(collect.stream().distinct().collect(Collectors.toList()));
        } else {
            SysCompanyVo sysCompanyVo = new SysCompanyVo();
            sysCompanyVo.setIsInside("1");
            List<SysCompanyVo> sysCompanyVoList = sysCompanyMapper.selectSysCompanyList(sysCompanyVo);
            List<Map<String, Object>> collect = sysCompanyVoList.stream().map(t -> {
                Map<String, Object> map = new HashMap<>();
                map.put("companyId", t.getId());
                map.put("name", t.getCompanyName());
                return map;
            }).collect(Collectors.toList());
            return AjaxResult.success(collect.stream().distinct().collect(Collectors.toList()));
        }
    }

    /**
     * 获取新旧Data字符
     */
    @GetMapping("/getDataByProcessId")
    public AjaxResult getDataByProcessId(String processId)
    {
        return AjaxResult.success(oaCommonService.getDataByProcessId(processId));
    }

    /**
     * 废弃收付款人审批流程
     */
    @PostMapping("/abandonedProd")
    public AjaxResult abandonedProd(@RequestBody OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(oaCommonService.abandonedProd(oaEditApproveGeneralityEditRecordsVo, loginUser));
    }
}
