package org.ruoyi.core.oasystem.service;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.oasystem.domain.OaVoucherRulesEditRecords;
import org.ruoyi.core.oasystem.domain.OaVoucherRulesMain;
import org.ruoyi.core.oasystem.domain.bo.OaVoucherMainBo;
import org.ruoyi.core.oasystem.domain.vo.OaVoucherMainVo;
import org.ruoyi.core.oasystem.domain.vo.OaVoucherRulesEditRecordsVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IOaVoucherRulesMainService.java
 * @Description TODO
 * @createTime 2023年11月09日 14:15:00
 */
public interface IOaVoucherRulesMainService {

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaVoucherRulesMain selectOaVoucherRulesMainById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param oaVoucherRulesMain 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<OaVoucherMainVo> selectOaVoucherRulesMainList(OaVoucherRulesMain oaVoucherRulesMain, String selectType, List<Long> companyIdList, List<Long> filterOaVoucherRulesMainId);

    /**
     * 新增【请填写功能名称】
     *
     * @param oaVoucherRulesMain 【请填写功能名称】
     * @return 结果
     */
    public int insertOaVoucherRulesMain(OaVoucherRulesMain oaVoucherRulesMain);

    /**
     * 修改【请填写功能名称】
     *
     * @param oaVoucherRulesMain 【请填写功能名称】
     * @return 结果
     */
    public int updateOaVoucherRulesMain(OaVoucherRulesMain oaVoucherRulesMain);
    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaVoucherRulesMainByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaVoucherRulesMainById(Long id);



    public int addOrUpdateData(OaVoucherMainVo oaVoucherMainVo, String mainStatus);

    public OaVoucherMainVo getDataByMainId(Long id);

    Long selectTotal(OaVoucherRulesMain oaVoucherRulesMain);

    Map<String, Object> queryRulesNum(OaVoucherRulesMain oaVoucherRulesMain);

    int addNewEditInfo(OaVoucherMainBo oaVoucherMainBo, LoginUser loginUser);

    //记账凭证 - 点击审核，出现的具体信息
    OaVoucherRulesEditRecordsVo selectOaVoucherRulesEditRecordsDetailByOaVoucherRulesMain(Long oaVoucherRulesMainId);

    //记账凭证 - 审批
    int checkOaVoucherRules(OaVoucherRulesEditRecords oaVoucherRulesEditRecords, LoginUser loginUser);

    //记账凭证 - 知悉操作
    int confirmOaVoucherRules(OaVoucherRulesEditRecords oaVoucherRulesEditRecords, LoginUser loginUser);

    //查找记账凭证规则主表总条数
    Long selectOaVoucherRulesMainListTotal(OaVoucherRulesMain oaVoucherRulesMain, List<Long> companyIdList);

    //查找记账凭证规则主表总条数，加入新的查询条件。主要是查询我的审批和我的提交视图的信息
    Long selectOaVoucherRulesMainListTotalByOaVoucherRulesMainAndCompanyIdList(OaVoucherRulesMain oaVoucherRulesMain, List<Long> companyIdList, List<Long> filterOaVoucherRulesMainId);

    //记账凭证规则查询列表前的前置查询参数
    Map<String, Object> selectOaVoucherRulesMainListBeforeParam(OaVoucherRulesMain oaVoucherRulesMain, LoginUser loginUser, String selectType);

    //查询每个功能是否开启了需要编辑审核
    String selectIsEnableStatusByProjectType(String projectType);

    //查询编辑记录
    List<OaVoucherRulesEditRecordsVo> selectEditRecordByOaVoucherRulesMain(Long oaVoucherRulesMainId);

    //更改需要编辑审核 状态
    int changeEditExamine(String projectType, String isEnable, LoginUser loginUser);

    //视图显示的条数
    Map<String, Object> viewCount(OaVoucherRulesMain oaVoucherRulesMain, LoginUser loginUser);

//    //查找所有代我审核的列表
//    List<OaVoucherMainVo> selectWaitMyApproval(OaVoucherRulesMain oaVoucherRulesMain, LoginUser loginUser);
}
