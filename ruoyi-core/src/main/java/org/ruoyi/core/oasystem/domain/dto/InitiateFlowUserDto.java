package org.ruoyi.core.oasystem.domain.dto;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 流程发起后，新增用户列表
 *
 * <AUTHOR>
 * @date 2023-08-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class InitiateFlowUserDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 流程发起后的主键 */
    private String procFormDataId;

    /** 用户id */
    private Long userId;

    /** 状态；0-正常，1-禁用 */
    private String status;

    /** 用户类型；0-自定义，1-模板导入 */
    private String userType;
}
