package org.ruoyi.core.oasystem.mapper;

import java.util.List;
import org.ruoyi.core.oasystem.domain.OaTraderDynamic;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-28
 */
public interface OaTraderDynamicMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaTraderDynamic selectOaTraderDynamicById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaTraderDynamic 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<OaTraderDynamic> selectOaTraderDynamicList(OaTraderDynamic oaTraderDynamic);

    /**
     * 新增【请填写功能名称】
     * 
     * @param oaTraderDynamic 【请填写功能名称】
     * @return 结果
     */
    public int insertOaTraderDynamic(OaTraderDynamic oaTraderDynamic);

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaTraderDynamic 【请填写功能名称】
     * @return 结果
     */
    public int updateOaTraderDynamic(OaTraderDynamic oaTraderDynamic);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaTraderDynamicById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaTraderDynamicByIds(Long[] ids);

    int deleteByOaTraderId(Long[] ids);
}
