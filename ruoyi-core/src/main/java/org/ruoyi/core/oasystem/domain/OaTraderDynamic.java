package org.ruoyi.core.oasystem.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 oa_trader_dynamic
 * 
 * <AUTHOR>
 * @date 2023-06-28
 */
public class OaTraderDynamic extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 交易人表id */
    @Excel(name = "交易人表id")
    private Long oaTraderId;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date operationTime;

    /** 操作内容 */
    @Excel(name = "操作内容")
    private String operationContent;

    /** 操作人id */
    @Excel(name = "操作人id")
    private Long operationBrId;

    /** 操作人名称 */
    @Excel(name = "操作人名称")
    private String operationBr;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOaTraderId(Long oaTraderId) 
    {
        this.oaTraderId = oaTraderId;
    }

    public Long getOaTraderId() 
    {
        return oaTraderId;
    }
    public void setOperationTime(Date operationTime) 
    {
        this.operationTime = operationTime;
    }

    public Date getOperationTime() 
    {
        return operationTime;
    }
    public void setOperationContent(String operationContent) 
    {
        this.operationContent = operationContent;
    }

    public String getOperationContent() 
    {
        return operationContent;
    }
    public void setOperationBrId(Long operationBrId) 
    {
        this.operationBrId = operationBrId;
    }

    public Long getOperationBrId() 
    {
        return operationBrId;
    }
    public void setOperationBr(String operationBr) 
    {
        this.operationBr = operationBr;
    }

    public String getOperationBr() 
    {
        return operationBr;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("oaTraderId", getOaTraderId())
            .append("operationTime", getOperationTime())
            .append("operationContent", getOperationContent())
            .append("operationBrId", getOperationBrId())
            .append("operationBr", getOperationBr())
            .toString();
    }
}
