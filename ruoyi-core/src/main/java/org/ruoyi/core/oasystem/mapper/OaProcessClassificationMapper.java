package org.ruoyi.core.oasystem.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaProcessClassification;

import java.util.List;

/**
 * OA系统-流程分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-07
 */
public interface OaProcessClassificationMapper 
{
    /**
     * 查询OA系统-流程分类
     * 
     * @param id OA系统-流程分类主键
     * @return OA系统-流程分类
     */
    public OaProcessClassification selectOaProcessClassificationById(Long id);

    /**
     * 查询OA系统-流程分类列表
     * 
     * @param oaProcessClassification OA系统-流程分类
     * @return OA系统-流程分类集合
     */
    public List<OaProcessClassification> selectOaProcessClassificationList(OaProcessClassification oaProcessClassification);


    public List<OaProcessClassification> selectOaProcessClassificationNoParentsList(OaProcessClassification oaProcessClassification);

    /**
     * 新增OA系统-流程分类
     * 
     * @param oaProcessClassification OA系统-流程分类
     * @return 结果
     */
    public int insertOaProcessClassification(OaProcessClassification oaProcessClassification);

    /**
     * 修改OA系统-流程分类
     * 
     * @param oaProcessClassification OA系统-流程分类
     * @return 结果
     */
    public int updateOaProcessClassification(OaProcessClassification oaProcessClassification);

    /**
     * 删除OA系统-流程分类
     * 
     * @param id OA系统-流程分类主键
     * @return 结果
     */
    public int deleteOaProcessClassificationById(Long id);

    /**
     * 批量删除OA系统-流程分类
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaProcessClassificationByIds(Long[] ids);

    OaProcessClassification checkDeptNameUnique(@Param("name") String name, @Param("parentId") Long parentId);

    int selectNormalChildrenDeptById(@Param("id") Long id);

    int hasChildByDeptId(Long ids);

    int checkDeptExistUser(Long ids);

    List<OaProcessClassification> queryAllCompany(@Param("roleList") List<Long> roleList,@Param("ancestors")String ancestors,@Param("parentId")Long parentId);

    List<OaProcessClassification> queryDataById(@Param("split") List<String> split);

    OaProcessClassification queryDataByCompanyId(@Param("classificationId") Long classificationId);

    //根据节点父ID和公司ID，找到所有父ID下的所有儿子节点
    List<OaProcessClassification> selectOaProcessClassificationByParentIdAndCompanyId(@Param("parentId") Long parentId, @Param("companyId") Long companyId);

    OaProcessClassification getCompanyDataBy(@Param("split") List<String> split);

    //根据用户查找新权限所涉及的公司id（OA流程发起权限会查看具体的信息）
    List<Long> selectNewAuthorityCompany(@Param("userId") Long userId);

    List<OaProcessClassification> selectOaProcessClassificationNoParentsListFroNewAuthority(@Param("oaProcessClassification") OaProcessClassification oaProcessClassification, @Param("companyList") List<Long> companyList);

    List<OaProcessClassification> queryAllCompany2(@Param("ancestors")String ancestors,@Param("parentId")Long parentId);
}
