package org.ruoyi.core.oasystem.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.oasystem.domain.OaProjectFlowMain;
import org.ruoyi.core.oasystem.domain.OaProjectFlowUtil;
import org.ruoyi.core.oasystem.domain.bo.OaProjectFlowMainBo;
import org.ruoyi.core.oasystem.domain.vo.OaEditApproveGeneralityEditRecordsVo;
import org.ruoyi.core.oasystem.service.IOaCommonService;
import org.ruoyi.core.oasystem.service.IOaProjectFlowMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2023-07-06
 */
@RestController
@RequestMapping("/oasystem/projectFlowMain")
public class OaProjectFlowMainController extends BaseController
{
    @Autowired
    private IOaProjectFlowMainService oaProjectFlowMainService;

    @Autowired
    private IOaCommonService oaCommonService;

    /**
     * 查询【请填写功能名称】列表
     */
    // @PreAuthorize("@ss.hasPermi('system:main:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaProjectFlowMain oaProjectFlowMain, String selectType)
    {
        //todo 暂时加的假参数，后续这个参数前端补 selectType--->传过来的视图类型
        if (selectType == null) {
            selectType = "0";
        }
        LoginUser loginUser = getLoginUser();
        //进行公司的过滤
        Map<String, Object> paramMap = oaCommonService.selectListBeforeParam(loginUser, selectType, "3");
        //权限控制，主要是过滤公司
        List<Long> companyIdList = (List<Long>) paramMap.get("companyIdList");
        //过滤视图
        List<Long> filterOaApplyId = null;
        if (!"0".equals(selectType)) {
            filterOaApplyId = (List<Long>) paramMap.get("filterOaApplyId");
        }
        startPage();
//        List<OaProjectFlowMain> list = oaProjectFlowMainService.selectOaProjectFlowMainList(oaProjectFlowMain);
        List<Map<String, Object>> maps = oaProjectFlowMainService.selectOaProjectList(oaProjectFlowMain, selectType, companyIdList, filterOaApplyId);
//       Long total = oaProjectFlowMainService.getTotal(oaProjectFlowMain);
        Long total = null;
        if ("0".equals(selectType) || selectType == null) {
            total = oaProjectFlowMainService.getTotalByCompanyIdList(oaProjectFlowMain, companyIdList);
        } else {
            //只有在查询我的审批或者我的提交时，会调用本方法。此时，selectType要么是1要么是2，loginUser绝对不会为空
            if (maps.size() > 0) {
                total = oaProjectFlowMainService.getTotalByCompanyIdListAndFilterOaApplyId(oaProjectFlowMain, companyIdList, filterOaApplyId);
            } else {
                total = 0L;
            }
        }
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setTotal(total);
        tableDataInfo.setRows(maps);
        tableDataInfo.setCode(200);
        tableDataInfo.setMsg("查询成功！");
        return tableDataInfo;
    }

    /**
     * 导出【请填写功能名称】列表
     */
    //@PreAuthorize("@ss.hasPermi('system:main:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaProjectFlowMain oaProjectFlowMain)
    {
        List<OaProjectFlowMain> list = oaProjectFlowMainService.selectOaProjectFlowMainList(oaProjectFlowMain);
        ExcelUtil<OaProjectFlowMain> util = new ExcelUtil<OaProjectFlowMain>(OaProjectFlowMain.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:main:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(oaProjectFlowMainService.selectOaProjectFlowMainById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    //@PreAuthorize("@ss.hasPermi('system:main:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaProjectFlowMain oaProjectFlowMain)
    {
        return toAjax(oaProjectFlowMainService.insertOaProjectFlowMain(oaProjectFlowMain));
    }

    /**
     * 修改【请填写功能名称】
     */
    // @PreAuthorize("@ss.hasPermi('system:main:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaProjectFlowMain oaProjectFlowMain)
    {
        return toAjax(oaProjectFlowMainService.updateOaProjectFlowMain(oaProjectFlowMain));
    }

    /**
     * 删除【请填写功能名称】
     */
    // @PreAuthorize("@ss.hasPermi('system:main:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(oaProjectFlowMainService.deleteOaProjectFlowMainByIds(ids));
    }

    /**
     * 项目与流程关联删除
     */
    @Log(title = "项目与流程关联删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable Long id)
    {
        return toAjax(oaProjectFlowMainService.deleteOaProjectFlowMainById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
//    @PreAuthorize("@ss.hasPermi('system:main:add')")
    @PostMapping("/addProFlowMain")
    public AjaxResult addProFlow(@RequestBody OaProjectFlowUtil oaProjectFlowUtil)
    {
        LoginUser loginUser = getLoginUser();
        int  i = oaProjectFlowMainService.addProjectAndFlow(oaProjectFlowUtil,loginUser);
        return toAjax(1);
    }

    /**
     * 获取【请填写功能名称】详细信息
     */

    @GetMapping(value = "/datadetils/{id}")
    public AjaxResult getdataInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(oaProjectFlowMainService.getOaProjectFlowMainById(id));
    }


    /**
     * 获取【请填写功能名称】详细信息
     */

    @GetMapping(value = "/getUpdatedatad/{id}")
    public AjaxResult getUpdatedatad(@PathVariable("id") Long id)
    {
        return AjaxResult.success(oaProjectFlowMainService.getupDataById(id));
    }

    /**
     * 每次进行增删改操作，进行留痕记录
     */
    @PostMapping("/addNewEditInfo")
    public AjaxResult addNewEditInfo(@RequestBody OaProjectFlowMainBo oaProjectFlowMainBo) {
        LoginUser loginUser = getLoginUser();
        return toAjax(oaCommonService.addNewEditInfoOfOaProjectFlow(oaProjectFlowMainBo, loginUser));
    }

    /**
     * 点击审核，出现的具体信息
     */
    @GetMapping("/detail")
    public AjaxResult detail(String oaApplyType, Long oaApplyId)
    {
        OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo = oaCommonService.selectOaEditApproveGeneralityEditRecordsDetailByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
        return AjaxResult.success(oaEditApproveGeneralityEditRecordsVo);
    }

    /**
     * 审批
     */
    @PutMapping("/check")
    public AjaxResult check(@RequestBody OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(oaCommonService.checkOaEditApproveGeneralityOfOaProjectFlow(oaEditApproveGeneralityEditRecordsVo, loginUser));
    }

    /**
     * 知悉操作
     */
    @PutMapping("/confirm")
    public AjaxResult confirm(@RequestBody OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(oaCommonService.confirmOaEditApproveGeneralityEditRecordsOfOaProjectFlow(oaEditApproveGeneralityEditRecordsVo, loginUser));
    }

    @GetMapping("/viewCount")
    public AjaxResult viewCount(OaProjectFlowMain oaProjectFlowMain)
    {
        LoginUser loginUser = getLoginUser();
        //查全部的条数
        Map<String, Object> paramMap = oaCommonService.selectListBeforeParam(loginUser, "0", "3");
        List<Long> companyIdList = (List<Long>) paramMap.get("companyIdList");
        Long allCount = oaProjectFlowMainService.getTotalByCompanyIdList(oaProjectFlowMain, companyIdList);
        if (allCount == null) {
            allCount = 0L;
        }
        //查待我审核的条数
        Map<String, Object> paramMap1 = oaCommonService.selectListBeforeParam(loginUser, "1", "3");
        List<Long> companyIdList1 = (List<Long>) paramMap1.get("companyIdList");
        List<Long> filterOaApplyId1 = (List<Long>) paramMap1.get("filterOaApplyId");
        Long myCheckCount = null;
        if (filterOaApplyId1 != null) {
            myCheckCount = oaProjectFlowMainService.getTotalByCompanyIdListAndFilterOaApplyId(oaProjectFlowMain, companyIdList1, filterOaApplyId1);
        }
        if (myCheckCount == null) {
            myCheckCount = 0L;
        }
        //查我的提交的条数
        Map<String, Object> paramMap2 = oaCommonService.selectListBeforeParam(loginUser, "2", "3");
        List<Long> companyIdList2 = (List<Long>) paramMap2.get("companyIdList");
        List<Long> filterOaApplyId2 = (List<Long>) paramMap2.get("filterOaApplyId");
        Long mySubmitCount = null;
        if (filterOaApplyId2 != null) {
            mySubmitCount = oaProjectFlowMainService.getTotalByCompanyIdListAndFilterOaApplyId(oaProjectFlowMain, companyIdList2, filterOaApplyId2);
        }
        if (mySubmitCount == null) {
            mySubmitCount = 0L;
        }
        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put("allCount", allCount);
        returnMap.put("myCheckCount", myCheckCount);
        returnMap.put("mySubmitCount", mySubmitCount);
        return AjaxResult.success(returnMap);
    }
}
