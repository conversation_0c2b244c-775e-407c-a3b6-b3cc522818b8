package org.ruoyi.core.oasystem.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * OA系统-流程模板修订记录对象 oa_process_template_update_info
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaProcessTemplateUpdateInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 模板id */
    @Excel(name = "模板id")
    private Long templateId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 说明信息 */
    @Excel(name = "说明信息")
    private String information;

    /** 用户姓名 */
    @Excel(name = "用户姓名")
    private String nickName;
}