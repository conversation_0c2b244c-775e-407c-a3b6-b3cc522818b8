package org.ruoyi.core.oasystem.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaVoucherRulesEditRecords;
import org.ruoyi.core.oasystem.domain.vo.OaVoucherRulesEditRecordsVo;

import java.util.List;

/**
 * 记账凭证规则编辑审批记录表 Mapper接口
 * 
 * <AUTHOR>
 * @date 2023/12/13 14:30
 */
public interface OaVoucherRulesEditRecordsMapper
{
    /**
     * 根据记账凭证主表id查找最新的对应编辑审批记录
     *
     * @param oaVoucherRulesMainId 记账凭证主表id
     * @return 最新的审批记录
     */
    public OaVoucherRulesEditRecords selectNewOaVoucherRulesEditRecordsByOaVoucherRulesMainId(@Param("oaVoucherRulesMainId") Long oaVoucherRulesMainId, @Param("confirmFlag") String confirmFlag);

    /**
     * 新增记账凭证规则编辑审批记录
     *
     * @param oaVoucherRulesEditRecords 记账凭证规则编辑审批记录 - 实体类
     * @return
     */
    public int insertOaVoucherRulesEditRecords(OaVoucherRulesEditRecords oaVoucherRulesEditRecords);

    /**
     * 更新记账凭证规则编辑审批记录
     *
     * @param oaVoucherRulesEditRecords 记账凭证规则编辑审批记录 - 实体类
     * @return 结果
     */
    public int updateOaVoucherRulesEditRecords(OaVoucherRulesEditRecords oaVoucherRulesEditRecords);

    /**
     * 根据记账凭证规则主表id删除记账凭证规则编辑审批记录
     *
     * @param oaVoucherRulesMainId 记账凭证规则编主表id
     * @return 结果
     */
    int deleteOaVoucherRulesEditRecordsByOaVoucherRulesMainId(Long oaVoucherRulesMainId);

    /**
     * 根据记账凭证规则主表id查询所有记账凭证规则编辑审批记录（除了新增编辑记录）
     *
     * @param oaVoucherRulesMainId 记账凭证规则编主表id
     * @return 结果
     */
    List<OaVoucherRulesEditRecordsVo> selectEditRecordByOaVoucherRulesMain(Long oaVoucherRulesMainId);

    /**
     * 根据记账凭证规则主表id查询所有记账凭证规则编辑审批记录
     *
     * @param oaVoucherRulesMainId 记账凭证规则编主表id
     * @return 结果
     */
    List<OaVoucherRulesEditRecordsVo> selectAllEditRecordByOaVoucherRulesMain(Long oaVoucherRulesMainId);
}
