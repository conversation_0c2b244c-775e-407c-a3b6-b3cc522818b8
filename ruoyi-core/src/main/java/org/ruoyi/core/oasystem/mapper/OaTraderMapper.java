package org.ruoyi.core.oasystem.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaTrader;
import org.ruoyi.core.oasystem.domain.vo.OaTraderVo;

import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-28
 */
public interface OaTraderMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaTrader selectOaTraderById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaTrader 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<OaTrader> selectOaTraderList(OaTrader oaTrader);

    /**
     * 新增【请填写功能名称】
     * 
     * @param oaTrader 【请填写功能名称】
     * @return 结果
     */
    public int insertOaTrader(OaTrader oaTrader);

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaTrader 【请填写功能名称】
     * @return 结果
     */
    public int updateOaTrader(OaTrader oaTrader);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaTraderById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaTraderByIds(Long[] ids);

    OaTrader getDataByAccNum(@Param("num") String num,@Param("traderType") String traderType);

    List<Map<String,Object>> queryDataByParams(OaTrader oaTrader);

    List<OaTrader> selectOaTraderListByIdList(@Param("oaTraderIdList") List<Long> oaTraderIdList);

    //根据各种过滤后的参数条件，查询列表
    List<OaTrader> selectOaTraderListByOaTraderAndFilterOaApplyIdAndCompanyIdList(@Param("oaTrader") OaTrader oaTrader, @Param("companyIdList") List<Long> companyIdList, @Param("filterOaApplyId") List<Long> filterOaApplyId);

    //根据传入的数组，查询集合
    List<OaTrader> selectOaTraderListByIds(Long[] ids);

    //查询总条数
    Long selectOaTraderListTotal(@Param("oaTrader") OaTrader oaTrader, @Param("companyIdList") List<Long> companyIdList);

    //根据过滤条件查询总条数
    Long selectOaTraderListTotalByOaTraderAndCompanyIdListAndFilterOaApplyIdList(@Param("oaTrader") OaTrader oaTrader, @Param("companyIdList") List<Long> companyIdList, @Param("filterOaApplyId") List<Long> filterOaApplyId);

    //通过项目的id，查找对应的返费公司有哪些
    List<OaTraderVo> selectOaTraderListByOaProjectDeployId(List<Long> oaProjectDeployIdList);

    //通过页面输入的参数和用户可以看到的权限，查看最新的收付款人(不需要OA审核的查询情况)
    List<OaTraderVo> selectOaTraderListByCompanyIdList(@Param("oaTrader") OaTrader oaTrader, @Param("companyIdList") List<Long> companyIdList, @Param("isEnable") String isEnable);

    List<OaTrader> selectOaTrader(OaTrader oaTrader);

//    //通过页面输入的参数和用户可以看到的权限，查看最新的收付款人(需要OA审核的查询情况)
//    List<OaTraderVo> selectOaTraderListByCompanyIdListByOaCheck(@Param("oaTrader") OaTrader oaTrader, @Param("companyIdList") List<Long> companyIdList);
}
