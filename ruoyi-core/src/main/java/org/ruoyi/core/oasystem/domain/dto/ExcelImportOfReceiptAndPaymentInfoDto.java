package org.ruoyi.core.oasystem.domain.dto;

import lombok.*;

/**
 * 项目信息 - 项目信息费信息 excel导入接受对象
 *
 * <AUTHOR>
 * @date 2025-2-5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class ExcelImportOfReceiptAndPaymentInfoDto {
    private static final long serialVersionUID = 1L;

    //事项
    private String itemName;

    //项目名称
    private String projectName;

    //收款人姓名
    private String traderTypeOneAccountName;

    //收款人开户行
    private String traderTypeOneBankOfDeposit;

    //收款人账号
    private String traderTypeOneAccountNumber;

    //收款人是内部/外部公司
    private String traderTypeOneTraderType;

    //付款人姓名
    private String traderTypeZeroAccountName;

    //付款人开户行
    private String traderTypeZeroBankOfDeposit;

    //付款人账号
    private String traderTypeZeroAccountNumber;

    //付款人是内部/外部公司
    private String traderTypeZeroTraderType;
}