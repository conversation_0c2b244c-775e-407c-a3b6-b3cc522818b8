package org.ruoyi.core.debtConversion.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.ruoyi.core.debtConversion.domain.DimBaseProdInfo;

import java.util.List;

/**
 * 产品信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@DataSource(DataSourceType.DEBT)
public interface DimBaseProdInfoMapper
{
    /**
     * 查询产品信息
     *
     * @param id 产品信息主键
     * @return 产品信息
     */
    public DimBaseProdInfo selectDimBaseProdInfoById(Long id);

    /**
     * 查询产品信息列表
     *
     * @param dimBaseProdInfo 产品信息
     * @return 产品信息集合
     */
    public List<DimBaseProdInfo> selectDimBaseProdInfoList(DimBaseProdInfo dimBaseProdInfo);

    /**
     * 新增产品信息
     *
     * @param dimBaseProdInfo 产品信息
     * @return 结果
     */
    public int insertDimBaseProdInfo(DimBaseProdInfo dimBaseProdInfo);

    /**
     * 修改产品信息
     *
     * @param dimBaseProdInfo 产品信息
     * @return 结果
     */
    public int updateDimBaseProdInfo(DimBaseProdInfo dimBaseProdInfo);

    /**
     * 删除产品信息
     *
     * @param id 产品信息主键
     * @return 结果
     */
    public int deleteDimBaseProdInfoById(Long id);

    /**
     * 批量删除产品信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDimBaseProdInfoByIds(Long[] ids);
}
