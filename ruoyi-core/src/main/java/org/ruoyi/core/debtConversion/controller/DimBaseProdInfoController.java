package org.ruoyi.core.debtConversion.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.debtConversion.domain.DimBaseProdInfo;
import org.ruoyi.core.debtConversion.service.IDimBaseProdInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 产品信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/dim/base/prod/info")
public class DimBaseProdInfoController extends BaseController
{
    @Autowired
    private IDimBaseProdInfoService dimBaseProdInfoService;

    /**
     * 查询产品信息列表
     */
    //@PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(DimBaseProdInfo dimBaseProdInfo)
    {
        startPage();
        List<DimBaseProdInfo> list = dimBaseProdInfoService.selectDimBaseProdInfoList(dimBaseProdInfo);
        return getDataTable(list);
    }

    /**
     * 导出产品信息列表
     */
    //@PreAuthorize("@ss.hasPermi('system:info:export')")
    @Log(title = "产品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DimBaseProdInfo dimBaseProdInfo)
    {
        List<DimBaseProdInfo> list = dimBaseProdInfoService.selectDimBaseProdInfoList(dimBaseProdInfo);
        ExcelUtil<DimBaseProdInfo> util = new ExcelUtil<DimBaseProdInfo>(DimBaseProdInfo.class);
        util.exportExcel(response, list, "产品信息数据");
    }

    /**
     * 获取产品信息详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(dimBaseProdInfoService.selectDimBaseProdInfoById(id));
    }

    /**
     * 新增产品信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "产品信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DimBaseProdInfo dimBaseProdInfo)
    {
        return toAjax(dimBaseProdInfoService.insertDimBaseProdInfo(dimBaseProdInfo));
    }

    /**
     * 修改产品信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:edit')")
    @Log(title = "产品信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DimBaseProdInfo dimBaseProdInfo)
    {
        return toAjax(dimBaseProdInfoService.updateDimBaseProdInfo(dimBaseProdInfo));
    }

    /**
     * 删除产品信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:remove')")
    @Log(title = "产品信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dimBaseProdInfoService.deleteDimBaseProdInfoByIds(ids));
    }
}
