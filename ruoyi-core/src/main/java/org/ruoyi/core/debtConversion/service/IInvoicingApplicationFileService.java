package org.ruoyi.core.debtConversion.service;

import org.ruoyi.core.debtConversion.domain.InvoicingApplicationFile;
import org.ruoyi.core.debtConversion.domain.vo.DebtConversionImport;
import org.ruoyi.core.debtConversion.domain.vo.InvoicingApplicationFileVo;
import org.ruoyi.core.debtConversion.domain.vo.InvoicingApplicationImport;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 开票申请文件Service接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface IInvoicingApplicationFileService
{
    /**
     * 查询开票申请文件
     *
     * @param id 开票申请文件主键
     * @return 开票申请文件
     */
    public InvoicingApplicationFile selectInvoicingApplicationFileById(Long id);

    /**
     * 查询开票申请文件列表
     *
     * @param invoicingApplicationFile 开票申请文件
     * @return 开票申请文件集合
     */
    public List<InvoicingApplicationFileVo> selectInvoicingApplicationFileList(InvoicingApplicationFileVo invoicingApplicationFile);

    /**
     * 新增开票申请文件
     *
     * @param invoicingApplicationFile 开票申请文件
     * @return 结果
     */
    public int insertInvoicingApplicationFile(InvoicingApplicationFile invoicingApplicationFile);

    /**
     * 修改开票申请文件
     *
     * @param invoicingApplicationFile 开票申请文件
     * @return 结果
     */
    public int updateInvoicingApplicationFile(InvoicingApplicationFile invoicingApplicationFile);

    /**
     * 批量删除开票申请文件
     *
     * @param ids 需要删除的开票申请文件主键集合
     * @return 结果
     */
    public int deleteInvoicingApplicationFileByIds(Long[] ids);

    /**
     * 删除开票申请文件信息
     *
     * @param id 开票申请文件主键
     * @return 结果
     */
    public int deleteInvoicingApplicationFileById(Long id);

    /**
     * 校验开票申请文件信息
     *
     * @param invoicingApplicationImportList
     * @return 结果
     */
    public Map<String,List<InvoicingApplicationImport>> importDataCheck(List<InvoicingApplicationImport> invoicingApplicationImportList);

    /**
     * 数据校验成功 批量新增
     * @param invoicingApplicationFile
     * @return
     */
    public int importData(InvoicingApplicationFileVo invoicingApplicationFile);
}
