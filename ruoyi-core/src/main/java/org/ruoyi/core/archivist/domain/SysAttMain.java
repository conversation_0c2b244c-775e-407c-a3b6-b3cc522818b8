package org.ruoyi.core.archivist.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 sys_att_main
 *
 * <AUTHOR>
 * @date 2024-01-11
 */
public class SysAttMain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdData;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdModelId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdModelName;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdKey;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdTempKey;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdFileName;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdContentType;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdAttType;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date docCreateTime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdFilePath;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdPersonId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date fdLastOpenTime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long fdSize;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdAttLocation;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long fdOrder;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdCreatorId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long downloadSum;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdFileId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long fdBorrowCount;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdOriginId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long fdVersion;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String fdUploaderId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date fdUploadTime;

    public void setFdId(String fdId)
    {
        this.fdId = fdId;
    }

    public String getFdId()
    {
        return fdId;
    }
    public void setFdData(String fdData)
    {
        this.fdData = fdData;
    }

    public String getFdData()
    {
        return fdData;
    }
    public void setFdModelId(String fdModelId)
    {
        this.fdModelId = fdModelId;
    }

    public String getFdModelId()
    {
        return fdModelId;
    }
    public void setFdModelName(String fdModelName)
    {
        this.fdModelName = fdModelName;
    }

    public String getFdModelName()
    {
        return fdModelName;
    }
    public void setFdKey(String fdKey)
    {
        this.fdKey = fdKey;
    }

    public String getFdKey()
    {
        return fdKey;
    }
    public void setFdTempKey(String fdTempKey)
    {
        this.fdTempKey = fdTempKey;
    }

    public String getFdTempKey()
    {
        return fdTempKey;
    }
    public void setFdFileName(String fdFileName)
    {
        this.fdFileName = fdFileName;
    }

    public String getFdFileName()
    {
        return fdFileName;
    }
    public void setFdContentType(String fdContentType)
    {
        this.fdContentType = fdContentType;
    }

    public String getFdContentType()
    {
        return fdContentType;
    }
    public void setFdAttType(String fdAttType)
    {
        this.fdAttType = fdAttType;
    }

    public String getFdAttType()
    {
        return fdAttType;
    }
    public void setDocCreateTime(Date docCreateTime)
    {
        this.docCreateTime = docCreateTime;
    }

    public Date getDocCreateTime()
    {
        return docCreateTime;
    }
    public void setFdFilePath(String fdFilePath)
    {
        this.fdFilePath = fdFilePath;
    }

    public String getFdFilePath()
    {
        return fdFilePath;
    }
    public void setFdPersonId(String fdPersonId)
    {
        this.fdPersonId = fdPersonId;
    }

    public String getFdPersonId()
    {
        return fdPersonId;
    }
    public void setFdLastOpenTime(Date fdLastOpenTime)
    {
        this.fdLastOpenTime = fdLastOpenTime;
    }

    public Date getFdLastOpenTime()
    {
        return fdLastOpenTime;
    }
    public void setFdSize(Long fdSize)
    {
        this.fdSize = fdSize;
    }

    public Long getFdSize()
    {
        return fdSize;
    }
    public void setFdAttLocation(String fdAttLocation)
    {
        this.fdAttLocation = fdAttLocation;
    }

    public String getFdAttLocation()
    {
        return fdAttLocation;
    }
    public void setFdOrder(Long fdOrder)
    {
        this.fdOrder = fdOrder;
    }

    public Long getFdOrder()
    {
        return fdOrder;
    }
    public void setFdCreatorId(String fdCreatorId)
    {
        this.fdCreatorId = fdCreatorId;
    }

    public String getFdCreatorId()
    {
        return fdCreatorId;
    }
    public void setDownloadSum(Long downloadSum)
    {
        this.downloadSum = downloadSum;
    }

    public Long getDownloadSum()
    {
        return downloadSum;
    }
    public void setFdFileId(String fdFileId)
    {
        this.fdFileId = fdFileId;
    }

    public String getFdFileId()
    {
        return fdFileId;
    }
    public void setFdBorrowCount(Long fdBorrowCount)
    {
        this.fdBorrowCount = fdBorrowCount;
    }

    public Long getFdBorrowCount()
    {
        return fdBorrowCount;
    }
    public void setFdOriginId(String fdOriginId)
    {
        this.fdOriginId = fdOriginId;
    }

    public String getFdOriginId()
    {
        return fdOriginId;
    }
    public void setFdVersion(Long fdVersion)
    {
        this.fdVersion = fdVersion;
    }

    public Long getFdVersion()
    {
        return fdVersion;
    }
    public void setFdUploaderId(String fdUploaderId)
    {
        this.fdUploaderId = fdUploaderId;
    }

    public String getFdUploaderId()
    {
        return fdUploaderId;
    }
    public void setFdUploadTime(Date fdUploadTime)
    {
        this.fdUploadTime = fdUploadTime;
    }

    public Date getFdUploadTime()
    {
        return fdUploadTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("fdId", getFdId())
                .append("fdData", getFdData())
                .append("fdModelId", getFdModelId())
                .append("fdModelName", getFdModelName())
                .append("fdKey", getFdKey())
                .append("fdTempKey", getFdTempKey())
                .append("fdFileName", getFdFileName())
                .append("fdContentType", getFdContentType())
                .append("fdAttType", getFdAttType())
                .append("docCreateTime", getDocCreateTime())
                .append("fdFilePath", getFdFilePath())
                .append("fdPersonId", getFdPersonId())
                .append("fdLastOpenTime", getFdLastOpenTime())
                .append("fdSize", getFdSize())
                .append("fdAttLocation", getFdAttLocation())
                .append("fdOrder", getFdOrder())
                .append("fdCreatorId", getFdCreatorId())
                .append("downloadSum", getDownloadSum())
                .append("fdFileId", getFdFileId())
                .append("fdBorrowCount", getFdBorrowCount())
                .append("fdOriginId", getFdOriginId())
                .append("fdVersion", getFdVersion())
                .append("fdUploaderId", getFdUploaderId())
                .append("fdUploadTime", getFdUploadTime())
                .toString();
    }
}