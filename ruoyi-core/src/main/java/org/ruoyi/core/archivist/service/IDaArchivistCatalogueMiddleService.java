package org.ruoyi.core.archivist.service;

import org.ruoyi.core.archivist.domain.DaArchivistCatalogueMiddle;
import org.ruoyi.core.archivist.domain.DaArchivistMain;
import org.ruoyi.core.archivist.domain.vo.DaArchivistCatalogueMiddleVo;
import org.ruoyi.core.archivist.domain.vo.DaArchivistCatalogueVo;

import java.util.List;

public interface IDaArchivistCatalogueMiddleService {

    /**
     * 查询详细
     *
     * @param archivistId 档案id
     * @return 档案-目录中间表
     */
    public DaArchivistCatalogueMiddle selectDaArchivistCatalogueMiddleByArchivistId(String archivistId);

    /**
     * 查询列表
     *
     * @param daArchivistCatalogueMiddle 档案-目录中间表对象
     * @return 档案-目录中间集合
     */
    public List<DaArchivistCatalogueMiddle> selectDaArchivistCatalogueMiddleList(DaArchivistCatalogueMiddle daArchivistCatalogueMiddle);

    /**
     * 新增
     *
     * @param daArchivistCatalogueMiddle 档案-目录中间表
     * @return 结果
     */
    public int insertDaArchivistCatalogueMiddle(DaArchivistCatalogueMiddle daArchivistCatalogueMiddle);

    /**
     * 修改
     *
     * @param daArchivistCatalogueMiddle 档案-目录中间表对象
     * @return 结果
     */
    public int updateDaArchivistCatalogueMiddle(DaArchivistCatalogueMiddle daArchivistCatalogueMiddle);

    /**
     * 批量删除
     *
     * @param archivistIds 需要删除的档案id集合
     * @return 结果
     */
    public int deleteDaArchivistCatalogueMiddleByArchivistIds(String[] archivistIds);

    /**
     * 删除
     *
     * @param archivistId 档案表id
     * @return 结果
     */
    public int deleteDaArchivistCatalogueMiddleByArchivistId(String archivistId);

    /**
     * 根据目录id查询档案集合
     *
     * @param catalogueId 目录id
     * @return 档案-目录中间
     */
    public List<DaArchivistCatalogueMiddle> selectDaArchivistCatalogueMiddleByCatalogueId(Long catalogueId);

    /**
     * 档案移动/复制
     * @param daArchivistCatalogueMiddleVo 档案-目录中间表vo
     * @return 结果
     */
    public int archivistMoveOrCopy(DaArchivistCatalogueMiddleVo daArchivistCatalogueMiddleVo);

    /**
     * 根据档案id查询目录集合
     *
     * @param archivistId 档案id
     * @return 档案-目录中间
     */
    public List<DaArchivistCatalogueVo> selectMiddleListByArchivistId(String archivistId);

}
