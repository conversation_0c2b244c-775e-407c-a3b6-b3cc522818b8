package org.ruoyi.core.archivist.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.*;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.AuthRoleEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.vo.DaArchivistCataloguevVO;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.mapper.SysCompanyMapper;
import com.ruoyi.system.service.ISysCompanyService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import com.ruoyi.system.service.impl.SysCompanyServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.formula.functions.Count;
import org.ruoyi.core.archivist.constant.NumStrConstant;
import org.ruoyi.core.archivist.domain.DaArchivistCatalogueMiddle;
import org.ruoyi.core.archivist.domain.DaArchivistProjectMiddle;
import org.ruoyi.core.archivist.domain.DaIsArchivistMiddle;
import org.ruoyi.core.archivist.domain.vo.*;
import org.ruoyi.core.archivist.mapper.DaIsArchivistMiddleMapper;
import org.ruoyi.core.archivist.service.*;
import org.ruoyi.core.oasystem.domain.BusinessDataRecord;
import org.ruoyi.core.oasystem.mapper.BusinessDataRecordMapper;
import org.ruoyi.core.xmglproject.domain.AuthDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.archivist.mapper.DaArchivistCatalogueMapper;
import org.ruoyi.core.archivist.domain.DaArchivistCatalogue;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 归档目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-28
 */
@Service
public class DaArchivistCatalogueServiceImpl implements IDaArchivistCatalogueService
{
    @Autowired
    private DaArchivistCatalogueMapper daArchivistCatalogueMapper;

    @Autowired
    private IDaArchivistMainService daArchivistMainService;

    @Autowired
    private IDaArchivistCatalogueMiddleService daArchivistCatalogueMiddleService;

    @Autowired
    private IDaArchivistProjectMiddleService daArchivistProjectMiddleService;

    @Autowired
    private IDaIsArchivistMiddleService daIsArchivistMiddleService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityServiceImpl;

    @Autowired
    private DaIsArchivistMiddleMapper daIsArchivistMiddleMapper;

    @Autowired
    private ISysDictDataService sysDictDataService;

    @Autowired
    private ISysCompanyService sysCompanyService;

    /**
     * 查询归档目录
     *
     * @param id 归档目录主键
     * @return 归档目录
     */
    @Override
    public DaArchivistCatalogueVo selectDaArchivistCatalogueById(Long id)
    {
        return daArchivistCatalogueMapper.selectDaArchivistCatalogueById(id);
    }

    /**
     * 查询归档目录列表
     *
     * @param daArchivistCatalogue 归档目录
     * @return 归档目录
     */
    @Override
    public List<DaArchivistCatalogueVo> selectDaArchivistCatalogueList(DaArchivistCatalogueVo daArchivistCatalogue) {
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        List<DaArchivistCatalogueVo>  archivistCatalogueVoList= new ArrayList<>();
        Set<Long> longSet = new HashSet<>();
        // daArchivistCatalogue.setUserId(loginUser.getUserId());

        List<DaArchivistCatalogueVo> haveAuthorityCatalogueList = new ArrayList<>();
        // 新权限->获取用户有哪些公司的档案管理权限 add by niey
        List<Long> authorityCompanyIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.ARCHIVES.getCode());
        if (!org.springframework.util.CollectionUtils.isEmpty(authorityCompanyIds)){
            daArchivistCatalogue.setUnitIdList(authorityCompanyIds);
        }

        // 新权限->获取当前登录用户的项目名称权限 add by niey 20241104
        /*List<DaArchivistProjectMiddle> archivistProjectMiddleList = new ArrayList<>();
        //获取有权限的项目id
        List<Long> loginUserHaveAuthorityDeployIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.PROJNAME.getCode());
        if (!org.springframework.util.CollectionUtils.isEmpty(loginUserHaveAuthorityDeployIds)) {
            //根据项目id查询档案
            archivistProjectMiddleList = daArchivistProjectMiddleService.selectDaArchivistProjectMiddleListByDeployProjectIds(loginUserHaveAuthorityDeployIds,daArchivistCatalogue.getPertainArchivist());
            List<String> middleList = archivistProjectMiddleList.stream().map(DaArchivistProjectMiddle::getArchivistId).collect(Collectors.toList());
            //根据档案id查询对应的所属目录
            List<DaArchivistCatalogueVo> archivistCatalogueVoList = daArchivistCatalogueMapper.queryCatalogueListByArchivistIds(middleList);
            //获取上级目录且重组集合
            haveAuthorityCatalogueList = this.getChildCatalogueList(archivistCatalogueVoList, haveAuthorityCatalogueList);
            if (!CollectionUtils.isEmpty(haveAuthorityCatalogueList)){
                cataLogueIdSet = haveAuthorityCatalogueList.stream().map(DaArchivistCatalogueVo::getId).collect(Collectors.toSet());
                daArchivistCatalogue.setCatalogueIdSet(cataLogueIdSet);
            }
        }*/

        //设置归档流程发起人
        daArchivistCatalogue.setFlowInitiator(loginUser.getUserName());
        //获取当前用户发的归档流程的所属目录
        // 新权限->查询归档流程发起人是当前登录用户的证照目录 add by niey
        List<DaArchivistCatalogueVo> loginUserCreateCatalogueList = daArchivistCatalogueMapper.selectCatalogueListByUserId(loginUser.getUserName(), daArchivistCatalogue.getPertainArchivist());
        if (!CollectionUtils.isEmpty(loginUserCreateCatalogueList)){
            //获取上级目录
            archivistCatalogueVoList = this.getChildCatalogueList(loginUserCreateCatalogueList, archivistCatalogueVoList);

            if (!CollectionUtils.isEmpty(archivistCatalogueVoList)){
                for (DaArchivistCatalogueVo catalogueVo : archivistCatalogueVoList) {
                    if (!authorityCompanyIds.contains(catalogueVo.getOrgId())){
                        longSet.add(catalogueVo.getId());
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(longSet)){
                daArchivistCatalogue.setCatalogueIdSet(longSet);
            }else {
                //取id
                Set<Long> collect = archivistCatalogueVoList.stream().map(DaArchivistCatalogueVo::getId).collect(Collectors.toSet());
                daArchivistCatalogue.setCatalogueIdSet(collect);
            }
        }
        if (CollectionUtils.isEmpty(loginUserCreateCatalogueList) && CollectionUtils.isEmpty(authorityCompanyIds)){
            return new ArrayList<>();
        }
        if(null != daArchivistCatalogue.getPageNum() && null != daArchivistCatalogue.getPageSize()){
            PageHelper.startPage(daArchivistCatalogue.getPageNum(), daArchivistCatalogue.getPageSize());
        }
        return daArchivistCatalogueMapper.selectDaArchivistCatalogueListByUnitId(daArchivistCatalogue);
    }

    /**
     * 新增归档目录
     *
     * @param daArchivistCatalogue 归档目录
     * @return 结果
     */
    @Override
    public int insertDaArchivistCatalogue(DaArchivistCatalogue daArchivistCatalogue)
    {
        daArchivistCatalogue.setCreateTime(DateUtils.getNowDate());
        daArchivistCatalogue.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        //系统编号逻辑
        int count = getCountByCreateTime(DateUtils.getDate()) + 1;
        String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
        daArchivistCatalogue.setCatalogueSystemCode("ML" + createTimeNum + String.format("%03d", count));
        return daArchivistCatalogueMapper.insertDaArchivistCatalogue(daArchivistCatalogue);
    }

    /**
     * 修改归档目录
     *
     * @param daArchivistCatalogue 归档目录
     * @return 结果
     */
    @Override
    public int updateDaArchivistCatalogue(DaArchivistCatalogue daArchivistCatalogue)
    {
        daArchivistCatalogue.setUpdateTime(DateUtils.getNowDate());
        daArchivistCatalogue.setUpdateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        Long catalogueId = daArchivistCatalogue.getId();
        //判断是否为一级目录，初始化数据不允许修改
        DaArchivistCatalogueVo daArchivistCatalogueVo = this.selectDaArchivistCatalogueById(catalogueId);
        if (daArchivistCatalogueVo.getParentId() == null){
            throw new ServiceException("一级目录不允许修改，详情请联系管理员！");
        }
        return daArchivistCatalogueMapper.updateDaArchivistCatalogue(daArchivistCatalogue);
    }

    /**
     * 批量删除归档目录
     *
     * @param ids 需要删除的归档目录主键
     * @return 结果
     */
    @Override
    public int deleteDaArchivistCatalogueByIds(Long[] ids)
    {
        return daArchivistCatalogueMapper.deleteDaArchivistCatalogueByIds(ids);
    }

    /**
     * 删除归档目录信息
     *
     * @param id 归档目录主键
     * @return 结果
     */
    @Override
    public int deleteDaArchivistCatalogueById(Long id)
    {
        //判断是否为第一级目录
        DaArchivistCatalogueVo daArchivistCatalogueVo = daArchivistCatalogueMapper.selectDaArchivistCatalogueById(id);
        if (daArchivistCatalogueVo.getParentId() == null ){
            throw new ServiceException("该目录为一级目录，无法进行删除!");
        }else if (daArchivistCatalogueVo.getParentId() != null){
            //判断是否存在子目录
            List<DaArchivistCatalogueVo> daArchivistCatalogueVos = daArchivistCatalogueMapper.selectTowGradeByParentId(id);
            if (daArchivistCatalogueVos.size() > 0){
                throw new ServiceException("该目录存在子目录，无法进行删除!");
            }
            //判断目录下是否存在数据
            List<DaArchivistCatalogueMiddle> daArchivistCatalogueMiddles = daArchivistCatalogueMiddleService.selectDaArchivistCatalogueMiddleByCatalogueId(id);
            if (daArchivistCatalogueMiddles.size() > 0 ){
                throw new ServiceException("该目录已存在归档文件，无法进行删除!");
            }
        }
        return daArchivistCatalogueMapper.deleteDaArchivistCatalogueById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult createCatalogueSave(DaArchivistMainVo daArchivistMainVo) {
        String mainId = "";
        //  获取‘归档设置’表单数据
        String bizType = daArchivistMainVo.getBizType();
        DaArchivistCatalogueVo daArchivistCatalogueVo = daArchivistMainVo.getDaArchivistCatalogueVo();
        if (ObjectUtils.isEmpty(daArchivistCatalogueVo)){
            throw new ServiceException("表单内容为空，请确认后重试！");
        }
        // 获取‘所属目录’字符串
        String catalogueName = daArchivistCatalogueVo.getCatalogueName();
        //根据流程id获取流程发起人
        DaProcFormDataVo procFormDataVo = daArchivistCatalogueMapper.selectFormDataByBusinessId(daArchivistMainVo.getPertainFlowId());
        if (ObjectUtils.isNotEmpty(procFormDataVo)) {
            SysUser sysUser = sysUserService.selectUserById(procFormDataVo.getUserId());
            if (!Objects.isNull(sysUser)) {
                daArchivistMainVo.setFlowInitiator(sysUser.getUserName());
            }
        }
        // 档案详情入库
        daArchivistMainVo.setPertainLib(daArchivistCatalogueVo.getPertainLib());
        daArchivistMainVo.setInitiateTime(DateUtils.getNowDate());
        // 系统编号逻辑
        int count = daArchivistMainService.getCountByCreateTime(DateUtils.getDate()) + 1;
        String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
        if (bizType.equals(NumStrConstant.ARCHIVIST_HT)){
            daArchivistMainVo.setArchivistCode("HTDA" + createTimeNum + String.format("%03d", count));
        }else {
            daArchivistMainVo.setArchivistCode("JCDA" + createTimeNum + String.format("%03d", count));
        }
        daArchivistMainVo.setArchivistTime(DateUtils.getNowDate());
        //档案入库
        daArchivistMainService.insertArchivistMainVo(daArchivistMainVo);
        mainId = daArchivistMainVo.getId();
        Long catalogueId = 0L;
        DaArchivistCatalogueVo vos = daArchivistCatalogueMapper.selectCompanyInfoById(daArchivistCatalogueVo.getOrgId(), daArchivistMainVo.getBizType());
        // 所属目录根据 ‘/’分割
        String[] splitCatalogueName = catalogueName.split("/");
        // 循环判断是否需要创建目录
        for (String splitName : splitCatalogueName) {
            splitName = vos.getCatalogueName() + ">" +  splitName ;
            // 取每个所属项目字段，判断是否包含‘>’符号
            if (splitName.contains(">")) {

                // 根据">"分割字符串
                String[] catalogueNameSplit = splitName.split(">");
                //判断是否存在相同字符目录
                for (int i = 0; i < catalogueNameSplit.length - 1; i++) {
                    for (int j = i + 1; j < catalogueNameSplit.length; j++) {
                        if (catalogueNameSplit[0].equals(catalogueNameSplit[1])) {
                            // if (i == 0 && catalogueNameSplit[i].equals(catalogueNameSplit[j]) && j == 1) {
                            continue;
                        }else if (catalogueNameSplit[i].equals(catalogueNameSplit[j])) {
                            throw new ServiceException("【所属目录】存在重复目录，请修改后重试！");
                        }
                    }
                }
                for (int i = 0; i < catalogueNameSplit.length; i++) {
                    DaArchivistCatalogueVo vo = null;
                    if (i == 0){
                        vo = daArchivistCatalogueMapper.queryCatalogueByName(catalogueNameSplit[i],bizType);
                    }else if (i == 1){
                        if (catalogueNameSplit[i].equals(catalogueNameSplit[i - 1])){
                            continue;
                        }
                        vo = daArchivistCatalogueMapper.queryCatalogueByNameParentId(catalogueNameSplit[i],bizType,vos.getId());
                        if (!ObjectUtils.isEmpty(vo)){
                            catalogueId = vo.getId();
                        }
                    }else {
                        if (catalogueId != 0){
                            vo = daArchivistCatalogueMapper.queryCatalogueByNameParentId(catalogueNameSplit[i],bizType,catalogueId);
                            if(vo != null && vo.getId() != null){
                                catalogueId = vo.getId();
                            }
                        }else {
                            vo = daArchivistCatalogueMapper.queryCatalogueByName(catalogueNameSplit[i],bizType);
                        }
                    }
                    // 不存在
                    if (ObjectUtils.isEmpty(vo)) {
                        // 创建目录
                        DaArchivistCatalogue archivistCatalogue = new DaArchivistCatalogue();
                        archivistCatalogue.setCatalogueName(catalogueNameSplit[i]);//目录名称
                        Long orgId = daArchivistCatalogueVo.getOrgId();
                        DaArchivistCatalogueVo voo = daArchivistCatalogueMapper.queryCatalogueByNameAndCode(catalogueNameSplit[i - 1], bizType, orgId);
                        archivistCatalogue.setParentId(voo.getId());// 上级目录id
                        archivistCatalogue.setDeptId(daArchivistCatalogueVo.getDeptId());// 所属部门
                        archivistCatalogue.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());// 创建人
                        archivistCatalogue.setPertainArchivist(daArchivistMainVo.getBizType());// 所属档案库
                        archivistCatalogue.setCreateTime(DateUtils.getNowDate());// 创建时间
                        archivistCatalogue.setOrgId(voo.getOrgId());// 所属公司
                        // 系统编号逻辑
                        int counts = getCountByCreateTime(DateUtils.getDate()) + 1;
                        String createTimeNums = DateUtils.dateTimeNow("yyyyMMdd");
                        archivistCatalogue.setCatalogueSystemCode("ML" + createTimeNums + String.format("%03d", counts));
                        daArchivistCatalogueMapper.insertDaArchivistCatalogue(archivistCatalogue);// 数据落地
                        catalogueId = archivistCatalogue.getId();
                        if (i == catalogueNameSplit.length - 1){
                            // 插入档案-目录中间表
                            DaArchivistCatalogueMiddle archivistCatalogueMiddle = new DaArchivistCatalogueMiddle();
                            archivistCatalogueMiddle.setArchivistId(mainId);// 档案id
                            archivistCatalogueMiddle.setCatalogueId(catalogueId);// 所属目录id
                            daArchivistCatalogueMiddleService.insertDaArchivistCatalogueMiddle(archivistCatalogueMiddle);
                            // 插入档案-项目中间表
                            if (daArchivistCatalogueVo.getProjectId() != null) {
                                Long[] projectId = daArchivistCatalogueVo.getProjectId();
                                for (Long id : projectId) {
                                    DaArchivistProjectMiddle projectMiddle = new DaArchivistProjectMiddle();
                                    projectMiddle.setArchivistId(mainId);
                                    projectMiddle.setProjectId(id);
                                    daArchivistProjectMiddleService.insertDaArchivistProjectMiddle(projectMiddle);
                                }
                            }
                            // 修改流程是否归档中间表数据为已归档
                            DaIsArchivistMiddle daIsArchivistMiddle = new DaIsArchivistMiddle();
                            daIsArchivistMiddle.setIsArchivist(NumStrConstant.STRING_0);
                            daIsArchivistMiddle.setArchivistId(mainId);
                            daIsArchivistMiddle.setFlowId(daArchivistMainVo.getPertainFlowId());
                            daIsArchivistMiddleService.updateDaIsArchivistMiddle(daIsArchivistMiddle);
                        }else {
                            continue;
                        }
                    } else {
                        // 第一级存在,取数组的第一个数据，直接跳过此次循环
                        if (i != catalogueNameSplit.length - 1) {
                            continue;
                        } else {
                            // 插入档案-目录中间表
                            DaArchivistCatalogueMiddle archivistCatalogueMiddle = new DaArchivistCatalogueMiddle();
                            archivistCatalogueMiddle.setArchivistId(mainId);// 档案id
                            archivistCatalogueMiddle.setCatalogueId(vo.getId());// 所属目录id
                            daArchivistCatalogueMiddleService.insertDaArchivistCatalogueMiddle(archivistCatalogueMiddle);
                            // 插入档案-项目中间表
                            Long[] projectId = daArchivistCatalogueVo.getProjectId();
                            if (projectId != null) {
                                for (Long id : projectId) {
                                    DaArchivistProjectMiddle projectMiddle = new DaArchivistProjectMiddle();
                                    projectMiddle.setArchivistId(mainId);
                                    projectMiddle.setProjectId(id);
                                    daArchivistProjectMiddleService.insertDaArchivistProjectMiddle(projectMiddle);
                                }
                            }
                            // 修改流程是否归档中间表数据为已归档
                            DaIsArchivistMiddle daIsArchivistMiddle = new DaIsArchivistMiddle();
                            daIsArchivistMiddle.setIsArchivist(NumStrConstant.STRING_0);
                            daIsArchivistMiddle.setArchivistId(mainId);
                            daIsArchivistMiddle.setFlowId(daArchivistMainVo.getPertainFlowId());
                            daIsArchivistMiddleService.updateDaIsArchivistMiddle(daIsArchivistMiddle);
                        }
                    }
                }
            } else {
                // 判断是否存在下级目录
                DaArchivistCatalogueVo vo = daArchivistCatalogueMapper.queryCatalogueByName(splitName,bizType);
                if (ObjectUtils.isNotEmpty(vo)) {
                    // 插入档案-目录中间表
                    DaArchivistCatalogueMiddle archivistCatalogueMiddle = new DaArchivistCatalogueMiddle();
                    archivistCatalogueMiddle.setArchivistId(mainId);// 档案id
                    archivistCatalogueMiddle.setCatalogueId(vo.getId());// 所属目录id
                    daArchivistCatalogueMiddleService.insertDaArchivistCatalogueMiddle(archivistCatalogueMiddle);

                    // 插入档案-项目中间表
                    Long[] projectId = daArchivistCatalogueVo.getProjectId();
                    if (projectId != null) {
                        for (Long id : projectId) {
                            DaArchivistProjectMiddle projectMiddle = new DaArchivistProjectMiddle();
                            projectMiddle.setArchivistId(mainId);
                            projectMiddle.setProjectId(id);
                            daArchivistProjectMiddleService.insertDaArchivistProjectMiddle(projectMiddle);
                        }
                    }
                    // 修改流程是否归档中间表数据为已归档
                    DaIsArchivistMiddle daIsArchivistMiddle = new DaIsArchivistMiddle();
                    daIsArchivistMiddle.setIsArchivist(NumStrConstant.STRING_0);
                    daIsArchivistMiddle.setArchivistId(mainId);
                    daIsArchivistMiddle.setFlowId(daArchivistMainVo.getPertainFlowId());
                    daIsArchivistMiddle.setUpdateBy(SecurityUtils.getLoginUser().getUser().getUserName());
                    daIsArchivistMiddleService.updateDaIsArchivistMiddle(daIsArchivistMiddle);
                } else {
                    // 不存在则创建
                    // 创建目录
                    DaArchivistCatalogue archivistCatalogue = new DaArchivistCatalogue();
                    archivistCatalogue.setCatalogueName(splitName);// 目录名称
                    archivistCatalogue.setParentId(daArchivistCatalogueVo.getOrgId());// 上级目录id
                    archivistCatalogue.setDeptId(daArchivistCatalogueVo.getDeptId());// 所属部门
                    archivistCatalogue.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());// 创建人
                    archivistCatalogue.setCreateTime(DateUtils.getNowDate());// 创建时间
                    archivistCatalogue.setOrgId(daArchivistCatalogueVo.getOrgId());// 所属公司
                    archivistCatalogue.setPertainArchivist(daArchivistMainVo.getBizType());
                    // 系统编号逻辑
                    int counts = getCountByCreateTime(DateUtils.getDate()) + 1;
                    String createTimeNums = DateUtils.dateTimeNow("yyyyMMdd");
                    archivistCatalogue.setCatalogueSystemCode("ML" + createTimeNums + String.format("%03d", counts));
                    daArchivistCatalogueMapper.insertDaArchivistCatalogue(archivistCatalogue);// 数据落地
                    catalogueId = archivistCatalogue.getId();

                    // 插入档案-目录中间表
                    DaArchivistCatalogueMiddle archivistCatalogueMiddle = new DaArchivistCatalogueMiddle();
                    archivistCatalogueMiddle.setArchivistId(mainId);// 档案id
                    archivistCatalogueMiddle.setCatalogueId(catalogueId);// 所属目录id
                    daArchivistCatalogueMiddleService.insertDaArchivistCatalogueMiddle(archivistCatalogueMiddle);

                    // 插入档案-项目中间表
                    Long[] projectId = daArchivistCatalogueVo.getProjectId();
                    if (projectId != null) {
                        for (Long id : projectId) {
                            DaArchivistProjectMiddle projectMiddle = new DaArchivistProjectMiddle();
                            projectMiddle.setArchivistId(mainId);
                            projectMiddle.setProjectId(id);
                            daArchivistProjectMiddleService.insertDaArchivistProjectMiddle(projectMiddle);
                        }
                    }
                    // 修改流程是否归档中间表数据为已归档
                    DaIsArchivistMiddle daIsArchivistMiddle = new DaIsArchivistMiddle();
                    daIsArchivistMiddle.setIsArchivist(NumStrConstant.STRING_0);
                    daIsArchivistMiddle.setArchivistId(mainId);
                    daIsArchivistMiddle.setFlowId(daArchivistMainVo.getPertainFlowId());
                    daIsArchivistMiddleService.updateDaIsArchivistMiddle(daIsArchivistMiddle);

                }
            }
        }
        return AjaxResult.success(mainId);
    }

    /**
     * 获取档案目录树形列表
     * @param pertainArchivist 所属档案库
     * @return
     */
    @Override
    public List<DaArchivistCatalogueVo> getTreeList(String pertainArchivist) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<DaArchivistCatalogueVo>  archivistCatalogueVoList= new ArrayList<>();
        List<Long> catalogueIdList = new ArrayList<>();
        List<DaArchivistCatalogueVo> loginCompanyList = new ArrayList<>();
        // 新权限->获取用户有哪些公司的档案管理权限 add by niey
        List<Long> authorityCompanyIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(user.getUserId(), AuthModuleEnum.ARCHIVES.getCode());
        //根据公司id查询此公司下有哪些档案目录
        if(!CollectionUtils.isEmpty(authorityCompanyIds)){
            loginCompanyList = daArchivistCatalogueMapper.selectDaArchivistCatalogueVoList(pertainArchivist,authorityCompanyIds);
            if (!CollectionUtils.isEmpty(loginCompanyList)){
                //不为空，取目录id集合
                List<Long> catalogueIds = loginCompanyList.stream().map(DaArchivistCatalogueVo::getId).collect(Collectors.toList());
                //添加到新集合，统一处理
                catalogueIdList.addAll(catalogueIds);
            }
        }

        // 新权限->查询归档流程发起人是当前登录用户的证照目录 add by niey
        List<DaArchivistCatalogueVo> loginUserCreateCatalogueList = daArchivistCatalogueMapper.selectCatalogueListByUserId(user.getUserName(), pertainArchivist);
        if (!CollectionUtils.isEmpty(loginUserCreateCatalogueList)){
            //获取上级目录
            archivistCatalogueVoList = this.getChildCatalogueList(loginUserCreateCatalogueList, archivistCatalogueVoList);
            //取id
            List<Long> loginUserCreateCatalogueCollect = archivistCatalogueVoList.stream().map(DaArchivistCatalogueVo::getId).collect(Collectors.toList());
            //添加到新集合，统一处理
            catalogueIdList.addAll(loginUserCreateCatalogueCollect);
        }

        // 新权限->查询当前用户有哪些项目名称权限和项目对应的档案目录 add by niey
        /*List<Long> loginUserHaveProjectDeployIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeByUserIdAndModuleType(user.getUserId(), AuthModuleEnum.PROJNAME.getCode());
        if (!CollectionUtils.isEmpty(loginUserHaveProjectDeployIds)){
            //根据项目名称id集合查询项目目录信息
            List<DaArchivistCatalogueVo> loginUserHaveProjectCatalogueList = daArchivistCatalogueMapper.selectDaArchivistCatalogueListByDeployProjectIds(loginUserHaveProjectDeployIds,pertainArchivist);
            if (!CollectionUtils.isEmpty(loginUserHaveProjectCatalogueList)){
                //获取上级目录
                archivistCatalogueVoList = this.getChildCatalogueList(loginUserCreateCatalogueList, archivistCatalogueVoList);
                //取id
                List<Long> loginUserHaveProjectCatalogueCollect = archivistCatalogueVoList.stream().map(DaArchivistCatalogueVo::getId).collect(Collectors.toList());
                //添加到新集合，统一处理
                catalogueIdList.addAll(loginUserHaveProjectCatalogueCollect);
            }
        }*/

        //去重
        Set<Long> catalogueIds = new HashSet<>(catalogueIdList);
        if (CollectionUtils.isEmpty(catalogueIds)){
            return new ArrayList<>();
        }

        //为空返回空集合，不为空则根据目录id查询目录
        List<DaArchivistCatalogueVo> daArchivistCatalogues = daArchivistCatalogueMapper.selectCatalogueListByIds(catalogueIds);

        // 父级，用于存放最终结果
        List<DaArchivistCatalogueVo> fp = new ArrayList<>();
        // 筛选出的子集
        List<DaArchivistCatalogueVo> fpson = new ArrayList<>();
        // 先提取出顶级目录和子集
        for (int i = 0; i < daArchivistCatalogues.size(); i++) {
            if (daArchivistCatalogues.get(i).getParentId() == null || "".equals(daArchivistCatalogues.get(i).getParentId())) {
                fp.add(daArchivistCatalogues.get(i));
            } else {
                fpson.add(daArchivistCatalogues.get(i));
            }
        }
        // 从顶级目录开始，递归穿插数据
        for (int i = 0; i < fp.size(); i++) {
            getChildData(fp.get(i), fpson);
        }
        return fp;
    }

    /**
     * 获取当前目录和当前目录的上级、上上级...
     * @param loginUserCatalogueList
     */
    private List<DaArchivistCatalogueVo> getChildCatalogueList(List<DaArchivistCatalogueVo> loginUserCatalogueList, List<DaArchivistCatalogueVo> archivistCatalogueVoList) {
        if (!CollectionUtils.isEmpty(loginUserCatalogueList)){
            for (DaArchivistCatalogueVo daArchivistCatalogueVo : loginUserCatalogueList) {
                if (null != daArchivistCatalogueVo.getParentId()){
                    getParentInfo(daArchivistCatalogueVo,archivistCatalogueVoList);
                }else {
                    archivistCatalogueVoList.add(daArchivistCatalogueVo);
                }
            }
        }
        return archivistCatalogueVoList;
    }

    /**
     * 查询上级
     * @param daArchivistCatalogueVo
     * @param archivistCatalogueVoList
     */
    private List<DaArchivistCatalogueVo> getParentInfo(DaArchivistCatalogueVo daArchivistCatalogueVo, List<DaArchivistCatalogueVo> archivistCatalogueVoList) {
        if (null != daArchivistCatalogueVo.getParentId()){
            archivistCatalogueVoList.add(daArchivistCatalogueVo);
            DaArchivistCatalogueVo catalogueVo = daArchivistCatalogueMapper.selectCatalogueInfoByParentId(daArchivistCatalogueVo.getParentId());
            archivistCatalogueVoList.add(catalogueVo);
            getParentInfo(catalogueVo,archivistCatalogueVoList);
        }
        return archivistCatalogueVoList;
    }

    /**
     * @param fp    父集
     * @param fpson 子集
     */
    private void getChildData(DaArchivistCatalogueVo fp, List<DaArchivistCatalogueVo> fpson) {
        List<DaArchivistCatalogueVo> stessoLive = new ArrayList<>();
        for (int j = 0; j < fpson.size(); j++) {
            // 如果是其子类，则存储在子类的list中，循环完统一set
            if (fpson.get(j).getParentId().equals(fp.getId())) {
                stessoLive.add(fpson.get(j));
                getChildData(fpson.get(j), fpson);
            }
        }
        // 设置子数据
        fp.setFPiattaformas(stessoLive);
    }

    @Override
    public int getCountByCreateTime(String createTime)
    {
        return daArchivistCatalogueMapper.getCountByCreateTime(createTime);
    }

    /**
     * 根据部门id查询所属公司
     * @param deptId
     * @return
     */
    @Override
    public SysDept selectCompanyInfoByDeptId(Long deptId) {
        return daArchivistCatalogueMapper.selectCompanyInfoByDeptId(deptId);
    }

    @Override
    public DaProjectVo splitJsonStringToQueryProject(DaArchivistJsonProjectVo daArchivistJsonProjectVo) {
        if (daArchivistJsonProjectVo.getProjectJs().equals("") && !daArchivistJsonProjectVo.getProjectJs().contains("list")
                && !daArchivistJsonProjectVo.getProjectJs().contains("projectList")){
            return new DaProjectVo();
        }
        String projectJs = daArchivistJsonProjectVo.getProjectJs();
        JSONObject projectJson = JSONObject.parseObject(projectJs);
        JSONArray list = projectJson.getJSONArray("list");
        DaProjectVo daProjectVo = new DaProjectVo();
        if (list == null || list.equals("")){
            return new DaProjectVo();
        }
        for (Object o : list) {
            String js = String.valueOf(o);
            if (js.contains("projectList")) {

                int i = js.indexOf("key");
                String substring = js.substring(i);
                int i1 = substring.indexOf("}");
                String projectJsons = substring.substring(6, i1-1);
                // 根据select_标识查询流程表中项目
                String flowId = daArchivistJsonProjectVo.getFlowId();
                // 根据流程id查询表单记录表
                DaProcFormDataVo daProcFormDataVo = daArchivistCatalogueMapper.selectFormDataByBusinessId(flowId);
                String data = daProcFormDataVo.getData();
                int i2 = data.indexOf(projectJsons);
                if (i2 < 0){
                    return new DaProjectVo();
                }
                String substring1 = data.substring(i2);
                int i6 = substring1.indexOf(":");
                String substring5 = substring1.substring(i6);
                int i7 = substring5.indexOf("\"");
                String substring6 = substring5.substring(1, i7-1);
                String[] split = new String[32];
                if (substring6.contains("[") && substring6.contains("]")){
                    int i3 = substring1.indexOf("[");
                    String substring2 = substring1.substring(i3);
                    int i4 = substring2.indexOf("]");
                    String substring3 = substring2.substring(1, i4);
                    System.out.println(substring2);
                    split = substring3.split(",");
                }else {
                    split = substring6.split(",");
                }
                //项目数组
                daProjectVo.setProjectArray( split);
                StringBuffer buffer = new StringBuffer();
                for (String id : split) {
                    // 根据项目id查询项目名称和id
                    DaProjectVo projectVo = daArchivistCatalogueMapper.selectPorjectMsgByProjectId(id);
                    if (!Objects.isNull(projectVo)){
                        //拼接字符串
                        buffer.append(projectVo.getProjectName() + "/");
                    }
                }
                String bufferStr = buffer.toString();
                int i5 = bufferStr.lastIndexOf("/");
                if(!StringUtils.isEmpty(bufferStr)){
                    String substring4 = bufferStr.substring(0, i5);
                    daProjectVo.setProjectName(substring4);
                }else {
                    daProjectVo.setProjectName("");
                }
            }else {
                continue;
            }
        }
        return daProjectVo;
    }

    /**
     * 根据公司id查询部门树
     * @param companyId 公司id
     * @return
     */
    @Override
    public List<SysDeptVo> getDeptTreeList(Long companyId) {
        List<SysDeptVo> sysDeptList = daArchivistCatalogueMapper.selectSysDeptList(companyId);
        // 父级，用于存放最终结果
        List<SysDeptVo> fp = new ArrayList<>();
        // 筛选出的子集
        List<SysDeptVo> fpson = new ArrayList<>();
        // 先提取出顶级目录和子集
        for (int i = 0; i < sysDeptList.size(); i++) {
            if (sysDeptList.get(i).getParentId().longValue() == 0 || "0".equals(sysDeptList.get(i).getParentId())) {
                fp.add(sysDeptList.get(i));
            } else {
                fpson.add(sysDeptList.get(i));
            }
        }
        // 从顶级目录开始，递归穿插数据
        for (int i = 0; i < fp.size(); i++) {
            getChildData(fp.get(i), fpson);
        }
        return fp;
    }

    /**
     * @param fp    父集
     * @param fpson 子集
     */
    private void getChildData(SysDeptVo fp, List<SysDeptVo> fpson) {
        List<SysDeptVo> stessoLive = new ArrayList<>();
        for (int j = 0; j < fpson.size(); j++) {
            // 如果是其子类，则存储在子类的list中，循环完统一set
            if (fpson.get(j).getParentId().equals(fp.getDeptId())) {
                stessoLive.add(fpson.get(j));
                getChildData(fpson.get(j), fpson);
            }
        }
        // 设置子数据
        fp.setFPiattaformas(stessoLive);
    }

    /**
     * 根据当前登录用户的deptId查询全量公司信息
     * @return
     */
    @Override
    public List<SysUnit> getAllCompany() {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        //根据用户id查询岗位
        List<SysPostAO> userPosts = daArchivistCatalogueMapper.selectAllCompanyMessage(userId);
        //去除null数据
        userPosts.removeAll(Collections.singleton(null));
        List<SysPostAO> list = new ArrayList<>();
        if ( userPosts != null && userPosts.size() > 0 ){
            for (SysPostAO post : userPosts) {
                //是否存在'dangan'权限标识
                if (post.getPostCode().contains(NumStrConstant.DANGAN_PRE)){
                    list.add(post);
                }
            }
        }else {
            List<SysUnit> unitList = new ArrayList<>();
            return unitList;
        }
        //根据用户岗位查询公司
        List<SysUnit> unitList = new ArrayList<>();
        for (SysPostAO sysPost : list) {
            //根据用户所属岗位查询所属公司集合
            if (sysPost.getDeptId() != null) {
                SysUnit sysUnit = daArchivistCatalogueMapper.selectDeptById(sysPost.getDeptId());
                if (sysUnit != null && ObjectUtils.isNotEmpty(sysUnit) ) {
                    unitList.add(sysUnit);
                }
            }
        }
        return unitList;
    }

    /**
     * 查询当前用户有哪些公司的档案管理员权限
     * @return
     */
    @Override
    public Map<String,Object> getUserAuthCompany(Long companyId) {
        Boolean flag = false;
        Map<String,Object> compMap = new HashMap<>();
        Set<Long> companyIdList = new HashSet<>();
        List<SysCompanyVo> sysCompanyVoList = new ArrayList<>();
        List<SysCompanyVo> authSysCompanyVoList = new ArrayList<>();
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        List<SysDictData> sysDictData = sysDictDataService.selectDictLabelByType("company_type");
        if (!CollectionUtils.isEmpty(sysDictData)){
            for (SysDictData sysDictDatum : sysDictData) {
                if(sysDictDatum.getDictLabel().contains("担保")){
                    SysCompanyVo sysCompany = new SysCompanyVo();
                    sysCompany.setCompanyTypeCode(sysDictDatum.getDictCode());
                    sysCompanyVoList = sysCompanyService.selectSysCompanyList(sysCompany);
                    break;
                }
            }
        }

        //先判断是否是初始化所有公司权限
        List<AuthDetailVo> userGLYAuthList = daArchivistCatalogueMapper.selectLoginUserCompanyDAGLYAuth(userId, AuthModuleEnum.ARCHIVES.getCode(), AuthRoleEnum.DAGLY1.getCode());
        for (AuthDetailVo detailVo : userGLYAuthList) {
            //根据主表id查询附表
            List<AuthDetail> authDetailList = daIsArchivistMiddleMapper.selectAuthFlowInfoByAuthMainId(detailVo.getAuthMainId());
            //如果为空，则证明是初始化权限，只在主表存在，附表没有对应的数据
            if (org.springframework.util.CollectionUtils.isEmpty(authDetailList)) {
                flag = true;
                //返回所有担保公司信息
                compMap.put("flag",flag);
                compMap.put("companyList",sysCompanyVoList);
                return compMap;
            }else {
                for (AuthDetail authDetail : authDetailList) {
                    if (authDetail.getStatus().equals("0")) {
                        companyIdList.add(authDetail.getThirdTableId());
                        if (companyId != null && authDetail.getThirdTableId().toString().equals(companyId.toString())){
                            flag = true;
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(companyIdList)) {
                authSysCompanyVoList = daIsArchivistMiddleMapper.selectCompanyInfoListByIds(companyIdList);
            }
        }
        compMap.put("flag",flag);
        compMap.put("companyList",authSysCompanyVoList);
        return compMap;
    }
}
