package org.ruoyi.core.archivist.service;

import org.ruoyi.core.archivist.domain.DaArchivistFiles;
import org.ruoyi.core.archivist.domain.SysAttFile;
import org.ruoyi.core.archivist.domain.SysAttMain;

import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
public interface IDaArchivistFilesService {

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public DaArchivistFiles selectDaArchivistFilesById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param daArchivistFiles 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<DaArchivistFiles> selectDaArchivistFilesList(DaArchivistFiles daArchivistFiles);

    /**
     * 新增【请填写功能名称】
     *
     * @param daArchivistFiles 【请填写功能名称】
     * @return 结果
     */
    public int insertDaArchivistFiles(DaArchivistFiles daArchivistFiles);

    /**
     * 修改【请填写功能名称】
     *
     * @param daArchivistFiles 【请填写功能名称】
     * @return 结果
     */
    public int updateDaArchivistFiles(DaArchivistFiles daArchivistFiles);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteDaArchivistFilesByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteDaArchivistFilesById(Long id);

    List<DaArchivistFiles> selectDaArchivistFilesListByFlowId(String pertainFlowId);

    /**
     * 新增【请填写功能名称】
     *
     * @param daArchivistFiles 【请填写功能名称】
     * @return 结果
     */
    public int addDaArchivistFiles(DaArchivistFiles daArchivistFiles);

    /**
     * 根据档案id查询附件信息
     * @param id
     * @return
     */
    List<SysAttMain> selectDRSysAttMainMessageById(String id);

    /**
     * 根据附件id查询历史数据导入附件
     * @param fdFileId
     * @return
     */
    SysAttFile selectDRSysAttFileMessageByFileId(String fdFileId);
}
