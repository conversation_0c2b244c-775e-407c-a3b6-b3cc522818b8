package org.ruoyi.core.archivist.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.common.core.domain.entity.*;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.ruoyi.core.archivist.constant.NumStrConstant;
import org.ruoyi.core.archivist.domain.*;
import org.ruoyi.core.archivist.domain.vo.DaArchivistCatalogueVo;
import org.ruoyi.core.archivist.domain.vo.DaArchivistMainVo;
import org.ruoyi.core.archivist.domain.vo.OaProjectDeployVo;
import org.ruoyi.core.archivist.domain.vo.SysAttFileVo;
import org.ruoyi.core.archivist.mapper.DaArchivistCatalogueMapper;
import org.ruoyi.core.archivist.mapper.DaArchivistMainMapper;
import org.ruoyi.core.archivist.service.*;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 档案详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-28
 */
@Service
public class DaArchivistMainServiceImpl implements IDaArchivistMainService {
    private static final Logger log = LoggerFactory.getLogger(DaArchivistMainServiceImpl.class);

    @Autowired
    private DaArchivistMainMapper daArchivistMainMapper;

    @Autowired
    private IDaArchivistCatalogueService daArchivistCatalogueService;

    @Autowired
    private IDaArchivistCatalogueMiddleService daArchivistCatalogueMiddleService;

    @Autowired
    private IDaArchivistProjectMiddleService daArchivistProjectMiddleService;

    @Autowired
    private IDaArchivistFilesService daArchivistFilesService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private DaArchivistCatalogueMapper daArchivistCatalogueMapper;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityServiceImpl;

    @Autowired
    private ISysDeptService sysDeptService;

    /**
     * 查询档案详情
     *
     * @param id 档案详情主键
     * @return 档案详情
     */
    @Override
    public DaArchivistMain selectDaArchivistMainById(String id) {
        DaArchivistMain daArchivistMain = daArchivistMainMapper.selectDaArchivistMainById(id);
        // 所属卷库
        if (daArchivistMain.getPertainLib() != null && !daArchivistMain.getPertainLib().equals("")) {
            String lib = dictDataService.selectDictLabel(NumStrConstant.DICT_ARCHIVIST_MANAGE_VOLUME, daArchivistMain.getPertainLib());
            daArchivistMain.setPertainLib(lib);
        }
        // 档案来源
        if (daArchivistMain.getArchivistSourceType() != null && !daArchivistMain.getArchivistSourceType().equals("")) {
            String source = dictDataService.selectDictLabel(NumStrConstant.DICT_ARCHIVIST_SOURCE, daArchivistMain.getArchivistSourceType());
            daArchivistMain.setArchivistSource(source);
        }
        // 借阅状态
        if (daArchivistMain.getBorrowType() != null && !daArchivistMain.getBorrowType().equals("")) {
            String borrow = dictDataService.selectDictLabel(NumStrConstant.DICT_ARCHIVIST_BORROW_TYPE, daArchivistMain.getBorrowType());
            daArchivistMain.setBorrowType(borrow);
        }
        // 所属档案库
        if (daArchivistMain.getPertainArchivist() != null && !daArchivistMain.getPertainArchivist().equals("")) {
            String pertain = dictDataService.selectDictLabel(NumStrConstant.DICT_ARCHIVIST_PERTAIN, daArchivistMain.getPertainArchivist());
            daArchivistMain.setPertainArchivist(pertain);
        }
        // 档案状态
        if (daArchivistMain.getArchivistType() != null && !daArchivistMain.getArchivistType().equals("")) {
            String type = dictDataService.selectDictLabel(NumStrConstant.DICT_ARCHIVIST_TYPE, daArchivistMain.getArchivistType());
            daArchivistMain.setArchivistType(type);
        }

        // 查询中间表，返回档案所在目录的集合
        List<DaArchivistCatalogueVo> daArchivistCatalogueVos = daArchivistCatalogueMiddleService.selectMiddleListByArchivistId(id);
        StringBuffer buffer = new StringBuffer();
        for (DaArchivistCatalogueVo daArchivistCatalogueVo : daArchivistCatalogueVos) {
            buffer.append(daArchivistCatalogueVo.getCatalogueName() + "/");
        }
        if (buffer.toString().contains("/")) {
            int i = buffer.lastIndexOf("/");
            String substring = buffer.substring(0, i);
            daArchivistMain.setCatalogueName(substring);
        } else {
            daArchivistMain.setCatalogueName(buffer.toString());
        }
        // 查询中间表，返回项目集合
        List<OaProjectDeployVo> projectList = daArchivistProjectMiddleService.selectDaArchivistProjectMiddleByArchivistId(id);
        // 查询所属公司、所属部门
        DaArchivistMain main = daArchivistMainMapper.selectCompanyNameAndDeptNameByMainId(id);
        daArchivistMain.setPertainCompanyName(main.getPertainCompanyName());
        daArchivistMain.setPertainDeptName(main.getPertainDeptName());
        daArchivistMain.setArchivistCatalogueVoList(daArchivistCatalogueVos);
        if (projectList != null && projectList.size() > 0) {
            daArchivistMain.setCwProjectList(projectList);
        }
        // 查询流程附件表，获取流程中上传的附件信息
        if (daArchivistMain.getPertainFlowId() != null && !daArchivistMain.getPertainFlowId().equals("")) {
            // 查询终稿扫描件
            List<DaArchivistFiles> fileList = daArchivistFilesService.selectDaArchivistFilesListByFlowId(daArchivistMain.getPertainFlowId());
            if (fileList.size() > 0 && !CollectionUtils.isEmpty(fileList)) {
                daArchivistMain.setDaArchivistFileList(fileList);
            } else {
                daArchivistMain.setDaArchivistFileList(new ArrayList<>());
            }
        }
        // 历史数据附件
        if (!daArchivistMain.getArchivistSourceType().equals("") && daArchivistMain.getArchivistSourceType().equals(NumStrConstant.DA_ARCHIVIST_SOURCE_TYPE_DR)) {
            List<SysAttMain> attMainList = daArchivistFilesService.selectDRSysAttMainMessageById(id);
            List<SysAttFileVo> voList = new ArrayList<>();
            if (attMainList != null && attMainList.size() > 0) {
                for (SysAttMain sysAttMain : attMainList) {
                    String fileName = sysAttMain.getFdFileName();
                    SysAttFile attFile = daArchivistFilesService.selectDRSysAttFileMessageByFileId(sysAttMain.getFdFileId());
                    SysAttFileVo sysAttFileVo = new SysAttFileVo();
                    sysAttFileVo.setFdId(attFile.getFdId());
                    sysAttFileVo.setFilePath(attFile.getFdFilePath());
                    sysAttFileVo.setFileName(fileName);
                    voList.add(sysAttFileVo);
                }
            }
            daArchivistMain.setSysAttFileVoList(voList);
        }
        return daArchivistMain;
    }

    /**
     * 查询档案列表
     *
     * @param daArchivistMain 档案详情
     * @return 档案详情
     */
    @Override
    public List<DaArchivistMain> selectDaArchivistMainList(DaArchivistMain daArchivistMain) {
        Set<Long> unitList = new HashSet<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<DaArchivistMain> daArchivistMains = new ArrayList<>();
        if (Objects.isNull(user)){
            return daArchivistMains;
        }

        // 获取用户有哪些公司的档案管理权限
        List<Long> authorityCompanyIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(user.getUserId(), AuthModuleEnum.ARCHIVES.getCode());
        if (!CollectionUtils.isEmpty(authorityCompanyIds)){
            Set<Long> authorityCompanyIdSet = authorityCompanyIds.stream().collect(Collectors.toSet());
            daArchivistMain.setUnitIdList(authorityCompanyIdSet);
        }
        /*//新权限->获取当前登录用户的项目名称权限 add by niey 20241104
        List<DaArchivistProjectMiddle> archivistProjectMiddleList = new ArrayList<>();
        //新权限->查询用户有哪些项目权限  add by niey 20241104
        List<Long> loginUserHaveAuthorityDeployIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeByUserIdAndModuleType(user.getUserId(), AuthModuleEnum.PROJNAME.getCode());
        if (!CollectionUtils.isEmpty(loginUserHaveAuthorityDeployIds)){
            //根据项目名称id查询此项目对应档案类型的档案
            archivistProjectMiddleList = daArchivistProjectMiddleService.selectDaArchivistProjectMiddleListByDeployProjectIds(loginUserHaveAuthorityDeployIds,daArchivistMain.getPertainArchivist());
            List<String> middleList = archivistProjectMiddleList.stream().map(DaArchivistProjectMiddle::getArchivistId).collect(Collectors.toList());
            daArchivistMain.setProjectArchivistIdList(middleList);
        }*/

        // 拆分json 获取档案id
        if (daArchivistMain.getExportList() != null && !daArchivistMain.getExportList().equals("")) {
            List<DaArchivistMain> strings = this.CfJsonString(daArchivistMain.getExportList());
            if (strings != null && strings.size() > 0) {
                daArchivistMain.setExportJsList(strings);
            }
        }
        if (daArchivistMain.getPageNum() != null && daArchivistMain.getPageSize() != null) {
            PageHelper.startPage(daArchivistMain.getPageNum(), daArchivistMain.getPageSize());
        }
        //新权限->流程发起人  add by niey 20241104
        daArchivistMain.setFlowInitiator(user.getUserName());
        daArchivistMains = daArchivistMainMapper.selectDaArchivistMainList(daArchivistMain);
        normalDictType(daArchivistMains);
        // normalProjectMessage(daArchivistMains); //档案关联的项目
        return daArchivistMains;
    }

    /**
     * 拆分导出接口中前端传递的json
     */
    public List<DaArchivistMain> CfJsonString(String exportJs) {
        List<DaArchivistMain> newList = new ArrayList<>();
        if (exportJs != null) {
            newList = JSONObject.parseArray(exportJs, DaArchivistMain.class);
        }
        return newList;
    }


    /**
     * 新增档案详情
     *
     * @param daArchivistMain 档案详情
     * @return 结果
     */
    @Override
    public int insertDaArchivistMain(DaArchivistMain daArchivistMain) {
        daArchivistMain.setCreateTime(DateUtils.getNowDate());
        daArchivistMain.setId(UUID.randomUUID().toString());
        return daArchivistMainMapper.insertDaArchivistMain(daArchivistMain);
    }

    /**
     * 修改档案详情
     *
     * @param daArchivistMain 档案详情
     * @return 结果
     */
    @Override
    public int updateDaArchivistMain(DaArchivistMain daArchivistMain) {
        daArchivistMain.setUpdateTime(DateUtils.getNowDate());
        return daArchivistMainMapper.updateDaArchivistMain(daArchivistMain);
    }

    /**
     * 批量删除档案详情
     *
     * @param ids 需要删除的档案详情主键
     * @return 结果
     */
    @Override
    public int deleteDaArchivistMainByIds(String[] ids) {
        return daArchivistMainMapper.deleteDaArchivistMainByIds(ids);
    }

    /**
     * 删除档案详情信息
     *
     * @param id 档案详情主键
     * @return 结果
     */
    @Override
    public int deleteDaArchivistMainById(String id) {
        return daArchivistMainMapper.deleteDaArchivistMainById(id);
    }

    /**
     * 新增档案详情
     *
     * @param daArchivistMainVo 档案详情
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertArchivistMainVo(DaArchivistMainVo daArchivistMainVo) {
        daArchivistMainVo.setCreateTime(DateUtils.getNowDate());
        daArchivistMainVo.setId(IdUtils.fastSimpleUUID());
        daArchivistMainVo.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        daArchivistMainVo.setArchivistBy(SecurityUtils.getLoginUser().getUser().getUserName());

        return daArchivistMainMapper.insertArchivistMainVo(daArchivistMainVo);
    }

    @Override
    public List<DaArchivistMainVo> getArchivistData(Long catalogueId) {
        return daArchivistMainMapper.getArchivistData(catalogueId);
    }

    /**
     * 档案列表询筛选数据总条数，做分页的total
     *
     * @param daArchivistMain
     * @return
     */
    @Override
    public List<DaArchivistMain> selectDaArchivistMainListCount(DaArchivistMain daArchivistMain) {
        Set<Long> unitList = new HashSet<>();
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        List<DaArchivistMain> countList = new ArrayList<>();

        // 根据岗位id查询岗位,如果不是档案管理员角色，返回空集合
        // 根据用户id查询岗位的所属公司
        PageHelper.clearPage();
        List<SysUnit> companyList = daArchivistCatalogueMapper.selectUserAreaCompany(userId);
        // 去除null数据
        companyList.removeAll(Collections.singleton(null));
        Boolean flag = false;
        if (companyList != null && companyList.size() > 0) {
            unitList = companyList.stream().map(e -> e.getUnitId()).collect(Collectors.toSet());

            daArchivistMain.setUnitIdList(unitList);
            flag = true;
        }
        if (flag) {
            // 拆分json 获取档案id 导出
            List<DaArchivistMain> strings = this.CfJsonString(daArchivistMain.getExportList());
            if (strings != null && strings.size() > 0) {
                daArchivistMain.setExportJsList(strings);
            }
            if (daArchivistMain.getCatalogueId() != null) {
                // 获取数据总数做分页
                daArchivistMain.setPageNum(null);
                daArchivistMain.setPageSize(null);
                countList = daArchivistMainMapper.selectDaArchivistMainListCount(daArchivistMain);
            } else {
                daArchivistMain.setPageNum(null);
                daArchivistMain.setPageSize(null);
                countList = daArchivistMainMapper.selectDaArchivistMainListCount(daArchivistMain);
            }
        }
        return countList;
    }


    @Override
    public SysAttMain selectFileMsgByFilePath(String key) {
        return daArchivistMainMapper.queryFileMsgByFilePath(key);
    }

    /**
     * 查询数据字典
     *
     * @param daArchivistMains
     */
    public void normalDictType(List<DaArchivistMain> daArchivistMains) {
        if (CollectionUtils.isEmpty(daArchivistMains)) {
            return;
        }
        List<SysDictData> ssjkList = dictDataService.selectDictLabelByType(NumStrConstant.DICT_ARCHIVIST_MANAGE_VOLUME);
        List<SysDictData> dalyList = dictDataService.selectDictLabelByType(NumStrConstant.DICT_ARCHIVIST_SOURCE);
        List<SysDictData> jyztList = dictDataService.selectDictLabelByType(NumStrConstant.DICT_ARCHIVIST_BORROW_TYPE);
        List<SysDictData> ssdaList = dictDataService.selectDictLabelByType(NumStrConstant.DICT_ARCHIVIST_PERTAIN);
        List<SysDictData> daztList = dictDataService.selectDictLabelByType(NumStrConstant.DICT_ARCHIVIST_TYPE);

        daArchivistMains.forEach(b -> {
            // 所属卷库
            String lib = "";
            // 档案来源
            String source = "";
            // 借阅状态
            String borrow = "";
            // 所属档案库
            String pertain = "";
            // 档案状态
            String type = "";

            // 所属卷库
            if (b.getPertainLib() != null && !CollectionUtils.isEmpty(ssjkList)) {
                Optional<SysDictData> ssjkDescO = ssjkList.stream()
                        .filter(bean -> b.getPertainLib().toString().equals(bean.getDictValue()))
                        .findFirst();
                lib = ssjkDescO.isPresent() ? ssjkDescO.get().getDictLabel() : null;
                b.setPertainLib(lib);
            }
            // 档案来源
            if (b.getArchivistSource() != null && !CollectionUtils.isEmpty(dalyList)) {
                Optional<SysDictData> dalyDescO = dalyList.stream()
                        .filter(bean -> b.getArchivistSource().toString().equals(bean.getDictValue()))
                        .findFirst();
                source = dalyDescO.isPresent() ? dalyDescO.get().getDictLabel() : null;
                b.setArchivistSource(source);
            }
            // 借阅状态
            if (b.getBorrowType() != null && !CollectionUtils.isEmpty(jyztList)) {
                Optional<SysDictData> jyztDescO = jyztList.stream()
                        .filter(bean -> b.getBorrowType().toString().equals(bean.getDictValue()))
                        .findFirst();
                borrow = jyztDescO.isPresent() ? jyztDescO.get().getDictLabel() : null;
                b.setBorrowType(borrow);
            }
            // 所属档案库
            if (b.getPertainArchivist() != null && !CollectionUtils.isEmpty(ssdaList)) {
                Optional<SysDictData> ssdaDescO = ssdaList.stream()
                        .filter(bean -> b.getPertainArchivist().toString().equals(bean.getDictValue()))
                        .findFirst();
                pertain = ssdaDescO.isPresent() ? ssdaDescO.get().getDictLabel() : null;
                b.setPertainArchivist(pertain);
            }
            // 档案状态
            if (b.getArchivistType() != null && !CollectionUtils.isEmpty(daztList)) {
                Optional<SysDictData> daztDescO = daztList.stream()
                        .filter(bean -> b.getArchivistType().toString().equals(bean.getDictValue()))
                        .findFirst();
                type = daztDescO.isPresent() ? daztDescO.get().getDictLabel() : null;
                b.setArchivistType(type);
            }
        });
    }

    /**
     * 根据档案查询关联的项目
     *
     * @param daArchivistMains
     */
    public void normalProjectMessage(List<DaArchivistMain> daArchivistMains) {
        if (CollectionUtils.isEmpty(daArchivistMains)) {
            return;
        }
        // 档案关联的项目
        List<OaProjectDeployVo> projectList = daArchivistProjectMiddleService.selectDaArchivistProjectMiddleByArchivistIdList(daArchivistMains);
        if (projectList != null && projectList.size() > 0) {
            daArchivistMains.forEach(b -> {
                if (b.getId() != null && !CollectionUtils.isEmpty(projectList)) {

                    List<OaProjectDeployVo> oaProjectDeploy = projectList.stream()
                            .filter(bean -> b.getId().equals(bean.getArchivistId()))
                            .collect(Collectors.toList());
                    b.setCwProjectList(oaProjectDeploy == null || oaProjectDeploy.size() == 0 ? new ArrayList<>() : oaProjectDeploy);
                }
            });
        }
    }

    @Override
    public int getCountByCreateTime(String createTime) {
        return daArchivistMainMapper.getCountByCreateTime(createTime);
    }

    @Override
    public int updateCode() {
        // 获取数据集为流程归档、合同档案的数据
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat upTime = new SimpleDateFormat("yyyyMMdd");
        List<String> yijing = new ArrayList<>();
        List<DaArchivistMain> htMainList = daArchivistMainMapper.queryHTDAInfoList("HT");
        for (DaArchivistMain daArchivistMain : htMainList) {
            Date archivistTime = daArchivistMain.getArchivistTime();
            Boolean flag = false;
            // 年月日格式的时间
            String format = sdf.format(archivistTime);
            List<DaArchivistMain> equalsList = daArchivistMainMapper.selectEqualDaByArchivistTime("HT", format);
            // 如果时间和上次的一致，则跳过不更新
            if (yijing.size() > 0 && !yijing.isEmpty()) {
                flag = yijing.contains(format);
            }
            if (!flag && daArchivistMain.getArchivistCode().contains("ML")) {
                yijing.add(format);
                if (!equalsList.isEmpty() && equalsList.size() > 0) {
                    List<String> stringList = equalsList.stream().map(DaArchivistMain::getArchivistCode)
                            .collect(Collectors.toList());
                    long count = stringList.stream().distinct().count();
                    if (stringList.size() == count) {
                        // 没有重复数据，则单独修改当前数据
                        // 判断是否是ML开头的旧数据，如果是就修改
                        if (daArchivistMain.getArchivistCode().contains("ML")) {
                            String newCode = daArchivistMain.getArchivistCode().substring(2);
                            newCode = "HTDA" + newCode;
                            DaArchivistMain daMain = new DaArchivistMain();
                            daMain.setId(daArchivistMain.getId());
                            daMain.setArchivistCode(newCode);
                            daArchivistMainMapper.updateDaArchivistMain(daMain);
                        }
                    } else {
                        Integer number = 0;
                        for (DaArchivistMain main : equalsList) {
                            number++;
                            String upTimeToBe = upTime.format(main.getArchivistTime());

                            DaArchivistMain daMain = new DaArchivistMain();
                            daMain.setId(main.getId());
                            if (number < 10) {
                                daMain.setArchivistCode("HTDA" + upTimeToBe + "00" + number);
                            } else if (number >= 10) {
                                daMain.setArchivistCode("HTDA" + upTimeToBe + "0" + number);
                            }
                            daArchivistMainMapper.updateDaArchivistMain(daMain);
                        }
                    }
                }
            }
        }
        List<String> JCXZYiJing = new ArrayList<>();
        ////获取数据集为流程归档、基础行政档案的数据
        List<DaArchivistMain> jcxzMainList = daArchivistMainMapper.queryHTDAInfoList("JCXZ");
        for (DaArchivistMain daArchivistMain : jcxzMainList) {
            Boolean flag = false;
            Date archivistTime = daArchivistMain.getArchivistTime();
            // 年月日格式的时间
            String format = sdf.format(archivistTime);
            List<DaArchivistMain> equalsList = daArchivistMainMapper.selectEqualDaByArchivistTime("JCXZ", format);
            // 如果时间和上次的一致，则跳过不更新
            if (JCXZYiJing.size() > 0 && !JCXZYiJing.isEmpty()) {
                flag = JCXZYiJing.contains(format);
            }
            if (!flag || daArchivistMain.getArchivistCode().contains("ML")) {
                JCXZYiJing.add(format);
                if (!equalsList.isEmpty() && equalsList.size() > 0) {
                    List<String> stringList = equalsList.stream().map(DaArchivistMain::getArchivistCode)
                            .collect(Collectors.toList());
                    long count = stringList.stream().distinct().count();
                    if (stringList.size() == count) {
                        System.out.println("没有重复元素");
                        // 没有重复编码，则判断是否是有‘ML’的旧数据，如果是，改成新编码
                        if (daArchivistMain.getArchivistCode().contains("ML")) {
                            String newCode = daArchivistMain.getArchivistCode().substring(2);
                            newCode = "JCDA" + newCode;
                            DaArchivistMain daMain = new DaArchivistMain();
                            daMain.setId(daArchivistMain.getId());
                            daMain.setArchivistCode(newCode);
                            daArchivistMainMapper.updateDaArchivistMain(daMain);
                        }
                    } else {
                        Integer number = 0;
                        for (DaArchivistMain main : equalsList) {
                            number++;
                            String upTimeToBe = upTime.format(main.getArchivistTime());
                            DaArchivistMain daMain = new DaArchivistMain();
                            daMain.setId(main.getId());
                            if (number <= 9) {
                                daMain.setArchivistCode("JCDA" + upTimeToBe + "00" + number);
                            } else if (number >= 10) {
                                daMain.setArchivistCode("JCDA" + upTimeToBe + "0" + number);
                            }
                            daArchivistMainMapper.updateDaArchivistMain(daMain);
                        }
                    }
                }
            }
        }
        return 1;
    }

    /**
     * 查询当前用户有哪些公司的档案查看或管理员权限以及流程发起人能看到的档案所属公司权限
     * @return
     */
    @Override
    public AjaxResult getCompanyAndDeptTreeList(String pertainArchivist) {
        AjaxResult ajax = new AjaxResult();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<Long> collect = new ArrayList<>();
        List<TreeSelect> treeSelectsDept = new ArrayList<>();
        //授权公司部门树
        List<TreeSelect> treeSelectAuthDept = new ArrayList<>();
        List<SysCompany> companyList = new ArrayList<>();
        // 新权限->获取用户有哪些公司的档案管理模块权限
        List<Long> authorityCompanyIds = newAuthorityServiceImpl.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(user.getUserId(), AuthModuleEnum.ARCHIVES.getCode());

        // 新权限->查询归档流程发起人是当前登录用户的档案的所属公司
        List<DaArchivistCatalogueVo> loginUserCreateCatalogueList = daArchivistCatalogueMapper.selectCatalogueListByUserId(user.getUserName(), pertainArchivist);
        if (!CollectionUtils.isEmpty(loginUserCreateCatalogueList)){
            collect = loginUserCreateCatalogueList.stream().map(DaArchivistCatalogueVo::getOrgId).collect(Collectors.toList());
        }
        //参数合并
        collect.addAll(authorityCompanyIds);
        //去重后的公司id集合
        Set<Long> heaveCompanyIds = new HashSet<>(collect);

        /**根据公司id查询当前用户通过通用授权获取到的有权限的公司，以及归档后的流程发起人是当前用户的档案的所属公司信息 */
        //先查询公司信息
        if (!CollectionUtils.isEmpty(heaveCompanyIds)) {
            companyList = daArchivistMainMapper.selectCompanyInfoByCompanyIds(heaveCompanyIds);
        }
        ajax.put("companyList",companyList);

        //再根据公司信息筛选公司的部门信息
        if (!CollectionUtils.isEmpty(companyList)){
            List<SysDept> deptList = daArchivistCatalogueMapper.selectDeptListByCompanyIds(heaveCompanyIds);
            treeSelectsDept = sysDeptService.buildDeptTreeSelect(deptList);
        }
        ajax.put("deptTreeList",treeSelectsDept);

        //获取档案用户有仅仅只是通过通用授权获取到的有权限的公司的部门
        if (!CollectionUtils.isEmpty(authorityCompanyIds)){
            Set<Long> companyIdSet = new HashSet<>(authorityCompanyIds);
            List<SysDept> authorityDeptList = daArchivistCatalogueMapper.selectDeptListByCompanyIds(companyIdSet);
            treeSelectAuthDept = sysDeptService.buildDeptTreeSelect(authorityDeptList);
        }
        ajax.put("authDeptTreeList",treeSelectAuthDept);

        return ajax;
    }
}
