package org.ruoyi.core.archivist.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.archivist.domain.DaArchivistMain;
import org.ruoyi.core.archivist.domain.DaArchivistProjectMiddle;
import org.ruoyi.core.archivist.domain.vo.OaProjectDeployVo;

/**
 * 档案-项目中间Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-28
 */
public interface DaArchivistProjectMiddleMapper
{
    /**
     * 查询档案-项目中间
     *
     * @param projectId 档案-项目中间主键
     * @return 档案-项目中间
     */
    public DaArchivistProjectMiddle selectDaArchivistProjectMiddleByProjectId(Long projectId);

    /**
     * 查询档案-项目中间列表
     *
     * @param daArchivistProjectMiddle 档案-项目中间
     * @return 档案-项目中间集合
     */
    public List<DaArchivistProjectMiddle> selectDaArchivistProjectMiddleList(DaArchivistProjectMiddle daArchivistProjectMiddle);

    /**
     * 新增档案-项目中间
     *
     * @param daArchivistProjectMiddle 档案-项目中间
     * @return 结果
     */
    public int insertDaArchivistProjectMiddle(DaArchivistProjectMiddle daArchivistProjectMiddle);

    /**
     * 修改档案-项目中间
     *
     * @param daArchivistProjectMiddle 档案-项目中间
     * @return 结果
     */
    public int updateDaArchivistProjectMiddle(DaArchivistProjectMiddle daArchivistProjectMiddle);

    /**
     * 删除档案-项目中间
     *
     * @param projectId 档案-项目中间主键
     * @return 结果
     */
    public int deleteDaArchivistProjectMiddleByProjectId(Long projectId);

    /**
     * 批量删除档案-项目中间
     *
     * @param projectIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDaArchivistProjectMiddleByProjectIds(Long[] projectIds);

    /**
     * 根据档案id查询项目集合
     * @param id
     * @return
     */
    List<OaProjectDeployVo> selectDaArchivistProjectMiddleByArchivistId(String id);

    /**
     * 根据档案id集合查询项目名称
     * @param daArchivistMains
     * @return
     */
    List<OaProjectDeployVo> queryDaArchivistProjectMiddleByArchivistIdList(@Param("daArchivistMains") List<DaArchivistMain> daArchivistMains);

    /**
     * 根据项目名称id查询档案信息
     */
    public List<DaArchivistProjectMiddle> queryDaArchivistProjectMiddleListByDeployProjectIds(@Param("deployProjectIds")List<Long> deployProjectIds, @Param("pertainArchivist")String pertainArchivist);
}
