package org.ruoyi.core.archivist.service;
import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.archivist.domain.DaArchivistMain;
import org.ruoyi.core.archivist.domain.SysAttMain;
import org.ruoyi.core.archivist.domain.vo.DaArchivistMainVo;

/**
 * 归档详情Service接口
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
public interface IDaArchivistMainService {
    /**
     * 查询归档详情
     *
     * @param id 归档详情主键
     * @return 归档详情
     */
    public DaArchivistMain selectDaArchivistMainById(String id);

    /**
     * 查询归档详情列表
     *
     * @param daArchivistMain 归档详情
     * @return 归档详情集合
     */
    public List<DaArchivistMain> selectDaArchivistMainList(DaArchivistMain daArchivistMain);

    /**
     * 新增归档详情
     *
     * @param daArchivistMain 归档详情
     * @return 结果
     */
    public int insertDaArchivistMain(DaArchivistMain daArchivistMain);

    /**
     * 修改归档详情
     *
     * @param daArchivistMain 归档详情
     * @return 结果
     */
    public int updateDaArchivistMain(DaArchivistMain daArchivistMain);

    /**
     * 批量删除归档详情
     *
     * @param ids 需要删除的归档详情主键集合
     * @return 结果
     */
    public int deleteDaArchivistMainByIds(String[] ids);

    /**
     * 删除归档详情信息
     *
     * @param id 归档详情主键
     * @return 结果
     */
    public int deleteDaArchivistMainById(String id);

    public int insertArchivistMainVo(DaArchivistMainVo daArchivistMainVo);

    /**
     * 根据目录id查询档案信息
     * @param catalogueId
     * @return
     */
    public List<DaArchivistMainVo> getArchivistData(Long catalogueId);

    /**
     * 查询筛选数据总条数，做分页的total
     * @param daArchivistMain
     * @return
     */
    List<DaArchivistMain> selectDaArchivistMainListCount(DaArchivistMain daArchivistMain);

    /**
     * 根据历史数据附件路径查找附件类型
     * @param key
     * @return
     */
    SysAttMain selectFileMsgByFilePath(String key);

    /**
     * 档案系统编号自动生成规则
     * @param createTime
     * @return
     */
    int getCountByCreateTime(String createTime);

    /**
     * 修复档案编号重复问题
     * @return
     */
    int updateCode();

    /**
     * 获取当前用户有哪些公司的档案权限
     * @return
     */
    AjaxResult getCompanyAndDeptTreeList(String pertainArchivist);

}
