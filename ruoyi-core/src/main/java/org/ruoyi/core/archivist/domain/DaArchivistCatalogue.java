package org.ruoyi.core.archivist.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotNull;

/**
 * 归档目录对象 da_archivist_catalogue
 * 
 * <AUTHOR>
 * @date 2023-11-28
 */
public class DaArchivistCatalogue extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 目录名称 */
    @Excel(name = "目录名称")
    private String catalogueName;

    /** 上级目录id */
    @Excel(name = "上级目录id")
    private Long parentId;

    private String parentName;

    /** 系统目录编号 */
    @Excel(name = "系统目录编号")
    private String catalogueSystemCode;

    /** 目录编号 */
    @Excel(name = "目录编号")
    private String catalogueCode;

    /** 所属公司 */
    @Excel(name = "所属公司")
    private Long orgId;
    private String orgName;

    /** 所属部门 */
    @Excel(name = "所属部门")
    private Long deptId;

    private String deptName;

    /** 所属档案库(HT:合同) */
    @Excel(name = "所属档案库(HT:合同)")
    private String pertainArchivist;

    /** 显示排序 */
    @Excel(name = "显示排序")
    private Long orderNum;

    /** 创建人 */
    private String createBy;

    private Integer pageNum;

    private Integer pageSize;

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCatalogueName(String catalogueName) 
    {
        this.catalogueName = catalogueName;
    }

    public String getCatalogueName() 
    {
        return catalogueName;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setCatalogueSystemCode(String catalogueSystemCode) 
    {
        this.catalogueSystemCode = catalogueSystemCode;
    }

    public String getCatalogueSystemCode() 
    {
        return catalogueSystemCode;
    }
    public void setCatalogueCode(String catalogueCode) 
    {
        this.catalogueCode = catalogueCode;
    }

    public String getCatalogueCode() 
    {
        return catalogueCode;
    }
    public void setOrgId(Long orgId) 
    {
        this.orgId = orgId;
    }

    public Long getOrgId() 
    {
        return orgId;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setPertainArchivist(String pertainArchivist) 
    {
        this.pertainArchivist = pertainArchivist;
    }

    public String getPertainArchivist() 
    {
        return pertainArchivist;
    }
    public void setOrderNum(Long orderNum) 
    {
        this.orderNum = orderNum;
    }
    @NotNull(message = "排序号不能为空！")
    public Long getOrderNum()
    {
        return orderNum;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("catalogueName", getCatalogueName())
            .append("parentId", getParentId())
            .append("catalogueSystemCode", getCatalogueSystemCode())
            .append("catalogueCode", getCatalogueCode())
            .append("orgId", getOrgId())
            .append("deptId", getDeptId())
            .append("pertainArchivist", getPertainArchivist())
            .append("orderNum", getOrderNum())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
