package org.ruoyi.core.badsystem.service.impl;

import org.ruoyi.core.badsystem.domain.FinancialSettlementPayment;
import org.ruoyi.core.badsystem.mapper.FinancialSettlementPaymentMapper;
import org.ruoyi.core.badsystem.service.IFinancialSettlementPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 不良系统-财务结算单-付款明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Service
public class FinancialSettlementPaymentServiceImpl implements IFinancialSettlementPaymentService
{
    @Autowired
    private FinancialSettlementPaymentMapper financialSettlementPaymentMapper;

    /**
     * 查询不良系统-财务结算单-付款明细
     *
     * @param id 不良系统-财务结算单-付款明细主键
     * @return 不良系统-财务结算单-付款明细
     */
    @Override
    public FinancialSettlementPayment selectFinancialSettlementPaymentById(Long id)
    {
        return financialSettlementPaymentMapper.selectFinancialSettlementPaymentById(id);
    }

    /**
     * 查询不良系统-财务结算单-付款明细列表
     *
     * @param financialSettlementPayment 不良系统-财务结算单-付款明细
     * @return 不良系统-财务结算单-付款明细
     */
    @Override
    public List<FinancialSettlementPayment> selectFinancialSettlementPaymentList(FinancialSettlementPayment financialSettlementPayment)
    {
        return financialSettlementPaymentMapper.selectFinancialSettlementPaymentList(financialSettlementPayment);
    }

    /**
     * 新增不良系统-财务结算单-付款明细
     *
     * @param financialSettlementPayment 不良系统-财务结算单-付款明细
     * @return 结果
     */
    @Override
    public int insertFinancialSettlementPayment(FinancialSettlementPayment financialSettlementPayment)
    {
        return financialSettlementPaymentMapper.insertFinancialSettlementPayment(financialSettlementPayment);
    }

    /**
     * 修改不良系统-财务结算单-付款明细
     *
     * @param financialSettlementPayment 不良系统-财务结算单-付款明细
     * @return 结果
     */
    @Override
    public int updateFinancialSettlementPayment(FinancialSettlementPayment financialSettlementPayment)
    {
        return financialSettlementPaymentMapper.updateFinancialSettlementPayment(financialSettlementPayment);
    }

    /**
     * 批量删除不良系统-财务结算单-付款明细
     *
     * @param ids 需要删除的不良系统-财务结算单-付款明细主键
     * @return 结果
     */
    @Override
    public int deleteFinancialSettlementPaymentByIds(Long[] ids)
    {
        return financialSettlementPaymentMapper.deleteFinancialSettlementPaymentByIds(ids);
    }

    /**
     * 删除不良系统-财务结算单-付款明细信息
     *
     * @param id 不良系统-财务结算单-付款明细主键
     * @return 结果
     */
    @Override
    public int deleteFinancialSettlementPaymentById(Long id)
    {
        return financialSettlementPaymentMapper.deleteFinancialSettlementPaymentById(id);
    }


    /**
     * 批量新增不良系统-财务结算单-付款明细
     *
     * @param financialSettlementPayment 不良系统-财务结算单-付款明细
     * @return 结果
     */
    @Override
    public int batchFinancialSettlementPayment(List<FinancialSettlementPayment> financialSettlementPayment){
        return financialSettlementPaymentMapper.batchFinancialSettlementPayment(financialSettlementPayment);
    }

    /**
     * 根据关联财务结算单 id   删除不良系统-财务结算单-付款明细
     *
     * @param financialSettlementId 不良系统-财务结算单-付款明细主键
     * @return 结果
     */
    @Override
    public int deleteFinancialSettlementPaymentByFinancialSettlementId(Long financialSettlementId){
        return financialSettlementPaymentMapper.deleteFinancialSettlementPaymentByFinancialSettlementId(financialSettlementId);
    }
}
