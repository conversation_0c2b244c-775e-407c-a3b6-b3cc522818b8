package org.ruoyi.core.badsystem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 不良系统-渠道业务对账单-委后还款明细对象 bl_channel_business_reconciliation_repayment
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ChannelBusinessReconciliationRepayment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 业务对账单 id */
    //@Excel(name = "业务对账单 id")
    private Long businessReconciliationId;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNumber;

    /** 借据号 */
    @Excel(name = "借据号")
    private String promissoryNoteNumber;

    /** 还款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "还款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date repaymentTime;

    /** 委后还款金额 */
    @Excel(name = "委后还款金额")
    private BigDecimal repaymentAmount;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("businessReconciliationId", getBusinessReconciliationId())
            .append("batchNumber", getBatchNumber())
            .append("promissoryNoteNumber", getPromissoryNoteNumber())
            .append("repaymentTime", getRepaymentTime())
            .append("repaymentAmount", getRepaymentAmount())
            .toString();
    }
}
