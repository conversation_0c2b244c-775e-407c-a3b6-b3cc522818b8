package org.ruoyi.core.badsystem.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.badsystem.domain.AssetManagement;
import org.ruoyi.core.badsystem.domain.vo.AssetManagementVo;
import org.ruoyi.core.badsystem.domain.vo.OutsourcedProjectVo;
import org.ruoyi.core.badsystem.service.IAssetManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 不良系统-资产管理Controller
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@RestController
@RequestMapping("/asset/management")
public class AssetManagementController extends BaseController
{
    @Autowired
    private IAssetManagementService assetManagementService;

    /**
     * 查询不良系统-资产管理列表
     */
    //@PreAuthorize("@ss.hasPermi('system:management:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssetManagementVo assetManagement)
    {
        startPage();
        List<AssetManagementVo> list = assetManagementService.selectAssetManagementList(assetManagement);
        return getDataTable(list);
    }

    /**
     * 导出不良系统-资产管理列表
     */
    //@PreAuthorize("@ss.hasPermi('system:management:export')")
    @Log(title = "不良系统-资产管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssetManagementVo assetManagement)
    {
        List<AssetManagementVo> list = assetManagementService.selectAssetManagementList(assetManagement);
        ExcelUtil<AssetManagementVo> util = new ExcelUtil<AssetManagementVo>(AssetManagementVo.class);
        util.exportExcel(response, list, "不良系统-资产管理数据");
    }

    /**
     * 获取不良系统-资产管理详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:management:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(assetManagementService.selectAssetManagementById(id));
    }

    /**
     * 新增不良系统-资产管理
     */
    //@PreAuthorize("@ss.hasPermi('system:management:add')")
    @Log(title = "不良系统-资产管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssetManagement assetManagement)
    {
        return toAjax(assetManagementService.insertAssetManagement(assetManagement));
    }

    /**
     * 修改不良系统-资产管理
     */
    //@PreAuthorize("@ss.hasPermi('system:management:edit')")
    @Log(title = "不良系统-资产管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AssetManagement assetManagement)
    {
        return toAjax(assetManagementService.updateAssetManagement(assetManagement));
    }

    /**
     * 删除不良系统-资产管理
     */
    //@PreAuthorize("@ss.hasPermi('system:management:remove')")
    @Log(title = "不良系统-资产管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(assetManagementService.deleteAssetManagementByIds(ids));
    }


    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AssetManagementVo> util = new ExcelUtil<AssetManagementVo>(AssetManagementVo.class);
        util.importTemplateExcel(response, "借款人信息模板");
    }


    /**
     * 借款人信息导入
     *
     * @param file 导入文件
     * @throws Exception
     */
    @Log(title = "借款人信息导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<AssetManagementVo> util = new ExcelUtil<AssetManagementVo>(AssetManagementVo.class);
        List<AssetManagementVo> assetManagementList = util.importExcel(file.getInputStream());
        if(assetManagementList == null || assetManagementList.isEmpty()){
            throw new ServiceException("至少导入一条数据");
        }
        assetManagementService.importData(assetManagementList);
        return AjaxResult.success();
    }

    /**
     * 资产管理-案件信息
     * @param assetManagement
     * @return
     */
    @GetMapping("/getCaseInformation")
    public TableDataInfo getCaseInformation(AssetManagementVo assetManagement)
    {
        startPage();
        List<OutsourcedProjectVo> list = assetManagementService.getOutsourcedProject(assetManagement);
        return getDataTable(list);
    }
}
