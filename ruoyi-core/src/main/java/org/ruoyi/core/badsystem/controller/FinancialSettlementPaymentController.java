package org.ruoyi.core.badsystem.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.badsystem.domain.FinancialSettlementPayment;
import org.ruoyi.core.badsystem.service.IFinancialSettlementPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 不良系统-财务结算单-付款明细Controller
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@RestController
@RequestMapping("/financial/settlement/payment")
public class FinancialSettlementPaymentController extends BaseController
{
    @Autowired
    private IFinancialSettlementPaymentService financialSettlementPaymentService;

    /**
     * 查询不良系统-财务结算单-付款明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:payment:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinancialSettlementPayment financialSettlementPayment)
    {
        startPage();
        List<FinancialSettlementPayment> list = financialSettlementPaymentService.selectFinancialSettlementPaymentList(financialSettlementPayment);
        return getDataTable(list);
    }

    /**
     * 导出不良系统-财务结算单-付款明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:payment:export')")
    @Log(title = "不良系统-财务结算单-付款明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinancialSettlementPayment financialSettlementPayment)
    {
        List<FinancialSettlementPayment> list = financialSettlementPaymentService.selectFinancialSettlementPaymentList(financialSettlementPayment);
        ExcelUtil<FinancialSettlementPayment> util = new ExcelUtil<FinancialSettlementPayment>(FinancialSettlementPayment.class);
        util.exportExcel(response, list, "不良系统-财务结算单-付款明细数据");
    }

    /**
     * 获取不良系统-财务结算单-付款明细详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:payment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(financialSettlementPaymentService.selectFinancialSettlementPaymentById(id));
    }

    /**
     * 新增不良系统-财务结算单-付款明细
     */
    //@PreAuthorize("@ss.hasPermi('system:payment:add')")
    @Log(title = "不良系统-财务结算单-付款明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinancialSettlementPayment financialSettlementPayment)
    {
        return toAjax(financialSettlementPaymentService.insertFinancialSettlementPayment(financialSettlementPayment));
    }

    /**
     * 修改不良系统-财务结算单-付款明细
     */
    //@PreAuthorize("@ss.hasPermi('system:payment:edit')")
    @Log(title = "不良系统-财务结算单-付款明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinancialSettlementPayment financialSettlementPayment)
    {
        return toAjax(financialSettlementPaymentService.updateFinancialSettlementPayment(financialSettlementPayment));
    }

    /**
     * 删除不良系统-财务结算单-付款明细
     */
    //@PreAuthorize("@ss.hasPermi('system:payment:remove')")
    @Log(title = "不良系统-财务结算单-付款明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(financialSettlementPaymentService.deleteFinancialSettlementPaymentByIds(ids));
    }
}
