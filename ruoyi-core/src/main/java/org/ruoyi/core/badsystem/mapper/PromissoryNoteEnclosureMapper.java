package org.ruoyi.core.badsystem.mapper;

import org.ruoyi.core.badsystem.domain.PromissoryNoteEnclosure;

import java.util.List;

/**
 * 不良系统-借据-附件Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface PromissoryNoteEnclosureMapper
{
    /**
     * 查询不良系统-借据-附件
     *
     * @param id 不良系统-借据-附件主键
     * @return 不良系统-借据-附件
     */
    public PromissoryNoteEnclosure selectPromissoryNoteEnclosureById(Long id);

    /**
     * 查询不良系统-借据-附件列表
     *
     * @param promissoryNoteEnclosure 不良系统-借据-附件
     * @return 不良系统-借据-附件集合
     */
    public List<PromissoryNoteEnclosure> selectPromissoryNoteEnclosureList(PromissoryNoteEnclosure promissoryNoteEnclosure);

    /**
     * 新增不良系统-借据-附件
     *
     * @param promissoryNoteEnclosure 不良系统-借据-附件
     * @return 结果
     */
    public int insertPromissoryNoteEnclosure(PromissoryNoteEnclosure promissoryNoteEnclosure);

    /**
     * 修改不良系统-借据-附件
     *
     * @param promissoryNoteEnclosure 不良系统-借据-附件
     * @return 结果
     */
    public int updatePromissoryNoteEnclosure(PromissoryNoteEnclosure promissoryNoteEnclosure);

    /**
     * 删除不良系统-借据-附件
     *
     * @param id 不良系统-借据-附件主键
     * @return 结果
     */
    public int deletePromissoryNoteEnclosureById(Long id);

    /**
     * 批量删除不良系统-借据-附件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePromissoryNoteEnclosureByIds(Long[] ids);


    /**
     * 批量新增不良系统-借据-附件
     *
     * @param promissoryNoteEnclosure 不良系统-借据-附件
     * @return 结果
     */
    public int batchPromissoryNoteEnclosure(List<PromissoryNoteEnclosure> promissoryNoteEnclosure);
}
