package org.ruoyi.core.badsystem.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.badsystem.domain.ChannelBusinessReconciliationRepayment;
import org.ruoyi.core.badsystem.service.IChannelBusinessReconciliationRepaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 不良系统-渠道业务对账单-委后还款明细Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/channel/business/reconciliation/repayment")
public class ChannelBusinessReconciliationRepaymentController extends BaseController
{
    @Autowired
    private IChannelBusinessReconciliationRepaymentService channelBusinessReconciliationRepaymentService;

    /**
     * 查询不良系统-渠道业务对账单-委后还款明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:repayment:list')")
    @GetMapping("/list")
    public TableDataInfo list(ChannelBusinessReconciliationRepayment channelBusinessReconciliationRepayment)
    {
        startPage();
        List<ChannelBusinessReconciliationRepayment> list = channelBusinessReconciliationRepaymentService.selectChannelBusinessReconciliationRepaymentList(channelBusinessReconciliationRepayment);
        return getDataTable(list);
    }

    /**
     * 导出不良系统-渠道业务对账单-委后还款明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:repayment:export')")
    @Log(title = "不良系统-渠道业务对账单-委后还款明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ChannelBusinessReconciliationRepayment channelBusinessReconciliationRepayment)
    {
        List<ChannelBusinessReconciliationRepayment> list = channelBusinessReconciliationRepaymentService.selectChannelBusinessReconciliationRepaymentList(channelBusinessReconciliationRepayment);
        ExcelUtil<ChannelBusinessReconciliationRepayment> util = new ExcelUtil<ChannelBusinessReconciliationRepayment>(ChannelBusinessReconciliationRepayment.class);
        util.exportExcel(response, list, "不良系统-渠道业务对账单-委后还款明细数据");
    }

    /**
     * 获取不良系统-渠道业务对账单-委后还款明细详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:repayment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(channelBusinessReconciliationRepaymentService.selectChannelBusinessReconciliationRepaymentById(id));
    }

    /**
     * 新增不良系统-渠道业务对账单-委后还款明细
     */
    //@PreAuthorize("@ss.hasPermi('system:repayment:add')")
    @Log(title = "不良系统-渠道业务对账单-委后还款明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ChannelBusinessReconciliationRepayment channelBusinessReconciliationRepayment)
    {
        return toAjax(channelBusinessReconciliationRepaymentService.insertChannelBusinessReconciliationRepayment(channelBusinessReconciliationRepayment));
    }

    /**
     * 修改不良系统-渠道业务对账单-委后还款明细
     */
    //@PreAuthorize("@ss.hasPermi('system:repayment:edit')")
    @Log(title = "不良系统-渠道业务对账单-委后还款明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ChannelBusinessReconciliationRepayment channelBusinessReconciliationRepayment)
    {
        return toAjax(channelBusinessReconciliationRepaymentService.updateChannelBusinessReconciliationRepayment(channelBusinessReconciliationRepayment));
    }

    /**
     * 删除不良系统-渠道业务对账单-委后还款明细
     */
    //@PreAuthorize("@ss.hasPermi('system:repayment:remove')")
    @Log(title = "不良系统-渠道业务对账单-委后还款明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(channelBusinessReconciliationRepaymentService.deleteChannelBusinessReconciliationRepaymentByIds(ids));
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ChannelBusinessReconciliationRepayment> util = new ExcelUtil<ChannelBusinessReconciliationRepayment>(ChannelBusinessReconciliationRepayment.class);
        util.importTemplateExcel(response, "不良系统-渠道业务对账单-委后还款明细");
    }

    /**
     * 结算账号明细导入
     *
     * @param file 导入文件
     * @throws Exception
     */
    @Log(title = "不良系统-渠道业务对账单-委后还款明细", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<ChannelBusinessReconciliationRepayment> util = new ExcelUtil<ChannelBusinessReconciliationRepayment>(ChannelBusinessReconciliationRepayment.class);
        List<ChannelBusinessReconciliationRepayment> list = util.importExcel(file.getInputStream());
        return AjaxResult.success("导入成功",list);
    }
}
