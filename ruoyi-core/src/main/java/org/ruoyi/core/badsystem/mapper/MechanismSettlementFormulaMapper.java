package org.ruoyi.core.badsystem.mapper;

import org.ruoyi.core.badsystem.domain.MechanismSettlementFormula;

import java.util.List;


/**
 * 机构-结算公式明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface MechanismSettlementFormulaMapper
{
    /**
     * 查询机构-结算公式明细
     *
     * @param id 机构-结算公式明细主键
     * @return 机构-结算公式明细
     */
    public MechanismSettlementFormula selectMechanismSettlementFormulaById(Long id);

    /**
     * 查询机构-结算公式明细列表
     *
     * @param mechanismSettlementFormula 机构-结算公式明细
     * @return 机构-结算公式明细集合
     */
    public List<MechanismSettlementFormula> selectMechanismSettlementFormulaList(MechanismSettlementFormula mechanismSettlementFormula);

    /**
     * 新增机构-结算公式明细
     *
     * @param mechanismSettlementFormula 机构-结算公式明细
     * @return 结果
     */
    public int insertMechanismSettlementFormula(MechanismSettlementFormula mechanismSettlementFormula);

    /**
     * 修改机构-结算公式明细
     *
     * @param mechanismSettlementFormula 机构-结算公式明细
     * @return 结果
     */
    public int updateMechanismSettlementFormula(MechanismSettlementFormula mechanismSettlementFormula);

    /**
     * 删除机构-结算公式明细
     *
     * @param id 机构-结算公式明细主键
     * @return 结果
     */
    public int deleteMechanismSettlementFormulaById(Long id);

    /**
     * 批量删除机构-结算公式明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMechanismSettlementFormulaByIds(Long[] ids);

    /**
     * 批量新增机构-结算公式明细
     *
     * @param  mechanismSettlementFormulaList
     * @return 结果
     */
    public int batchMechanismSettlementFormula(List<MechanismSettlementFormula> mechanismSettlementFormulaList);

    public int deleteByMechanismId(Long mechanismId);
}
