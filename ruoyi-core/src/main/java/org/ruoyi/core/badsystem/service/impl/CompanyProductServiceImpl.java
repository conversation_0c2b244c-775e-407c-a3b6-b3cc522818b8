package org.ruoyi.core.badsystem.service.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.badsystem.domain.vo.CompanyProductVo;
import org.ruoyi.core.badsystem.mapper.CompanyProductMapper;
import org.ruoyi.core.badsystem.service.ICompanyProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 不良系统-公司产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class CompanyProductServiceImpl implements ICompanyProductService
{
    @Autowired
    private CompanyProductMapper companyProductMapper;
    @Autowired
    private CompanyProductInterestServiceImpl companyProductInterestService;
    /**
     * 查询不良系统-公司产品
     *
     * @param id 不良系统-公司产品主键
     * @return 不良系统-公司产品
     */
    @Override
    public CompanyProductVo selectCompanyProductById(Long id)
    {
        return companyProductMapper.selectCompanyProductById(id);
    }

    /**
     * 查询不良系统-公司产品列表
     *
     * @param companyProduct 不良系统-公司产品
     * @return 不良系统-公司产品
     */
    @Override
    public List<CompanyProductVo> selectCompanyProductList(CompanyProductVo companyProduct)
    {
        return companyProductMapper.selectCompanyProductList(companyProduct);
    }

    /**
     * 新增不良系统-公司产品
     *
     * @param companyProduct 不良系统-公司产品
     * @return 结果
     */
    @Override
    @Transactional
    public int insertCompanyProduct(CompanyProductVo companyProduct)
    {
        companyProduct.setCreateTime(DateUtils.getNowDate());
        companyProduct.setCreateBy(getUsername());
        int i = companyProductMapper.checkVersion(companyProduct);
        if (i > 0) {
            throw new ServiceException("产品名称-版本重复、该公司已有对应产品。");
        }
        companyProductMapper.insertCompanyProduct(companyProduct);

        if(companyProduct.getCompanyProductInterestList() != null && !companyProduct.getCompanyProductInterestList().isEmpty()){
            companyProduct.getCompanyProductInterestList().forEach(companyProductInterest -> {
                companyProductInterest.setCompanyProductId(companyProduct.getId());
            });
            companyProductInterestService.batchCompanyProductInterest(companyProduct.getCompanyProductInterestList());
        }
        return 1;
    }

    /**
     * 修改不良系统-公司产品
     *
     * @param companyProduct 不良系统-公司产品
     * @return 结果
     */
    @Override
    public int updateCompanyProduct(CompanyProductVo companyProduct)
    {
        int i = companyProductMapper.checkVersion(companyProduct);
        if (i > 0) {
            throw new ServiceException("产品名称-版本重复、该公司已有对应产品。");
        }
        companyProductInterestService.deleteCompanyProductInterestByCompanyProductId(companyProduct.getId());

        if(companyProduct.getCompanyProductInterestList() != null && !companyProduct.getCompanyProductInterestList().isEmpty()){
            companyProduct.getCompanyProductInterestList().forEach(companyProductInterest -> {
                companyProductInterest.setCompanyProductId(companyProduct.getId());
            });
            companyProductInterestService.batchCompanyProductInterest(companyProduct.getCompanyProductInterestList());
        }

        companyProduct.setUpdateTime(DateUtils.getNowDate());
        companyProduct.setUpdateBy(getUsername());
        return companyProductMapper.updateCompanyProduct(companyProduct);
    }

    /**
     * 批量删除不良系统-公司产品
     *
     * @param ids 需要删除的不良系统-公司产品主键
     * @return 结果
     */
    @Override
    public int deleteCompanyProductByIds(Long[] ids)
    {
        return companyProductMapper.deleteCompanyProductByIds(ids);
    }

    /**
     * 删除不良系统-公司产品信息
     *
     * @param id 不良系统-公司产品主键
     * @return 结果
     */
    @Override
    public int deleteCompanyProductById(Long id)
    {
        return companyProductMapper.deleteCompanyProductById(id);
    }
}
