package org.ruoyi.core.badsystem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 不良系统-委外方案-分案策略对象 bl_outsourced_project_strategy
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Data
public class OutsourcedProjectStrategy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 委外分案 */
    //@Excel(name = "委外分案")
    private Long outsourcedProjectId;

    /** 案件范围 */
    @Excel(name = "案件范围" ,
            readConverterExp  = "m1=m1, m2=m2, m3=m3, m4=m4, m5=m5, m6=m6, m7=m7, m8=m8, m9=m9, m10=m10, m11=m11, m12=m12, m13-24=m13-24, m25-36=m25-36")
    private String caseRangeString;

    private List<String> caseRange;

    /** 数量 */
    @Excel(name = "数量" , cellType = Excel.ColumnType.NUMERIC)
    private Long quantity;

    /** 金额 */
    @Excel(name = "金额" , cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal amountMoney;

    /** 分配规则 (1.随机；2.逾期开始日早到晚；3.逾期开始日晚到早) */
    @Excel(name = "分配规则 (1.随机；2.逾期开始日早到晚；3.逾期开始日晚到早)" , readConverterExp  = "1=随机, 2=逾期开始日早到晚, 3=逾期开始日晚到早")
    private String allocationRules;

    /** 客户逾期后是否发生还款 */
    @Excel(name = "客户逾期后是否发生还款" , readConverterExp  = "1=是, 2=否")
    private String isRepayment;

    /** 逾期开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "逾期开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date overdueStart;

    /** 逾期截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "逾期截止日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date overdueEnd;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("outsourcedProjectId", getOutsourcedProjectId())
            .append("caserange", getCaseRange())
            .append("quantity", getQuantity())
            .append("amountMoney", getAmountMoney())
            .append("allocationRules", getAllocationRules())
            .append("isRepayment", getIsRepayment())
            .append("overdueStart", getOverdueStart())
            .append("overdueEnd", getOverdueEnd())
            .toString();
    }
}
