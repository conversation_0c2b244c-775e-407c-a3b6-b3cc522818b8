package org.ruoyi.core.badsystem.mapper;

import org.ruoyi.core.badsystem.domain.OutsourcedRepaymentDetail;
import org.ruoyi.core.badsystem.domain.vo.OutsourcedRepaymentDetailVo;

import java.util.List;
import java.util.Set;

/**
 * 不良系统-委外还款明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
public interface OutsourcedRepaymentDetailMapper
{
    /**
     * 查询不良系统-委外还款明细
     *
     * @param id 不良系统-委外还款明细主键
     * @return 不良系统-委外还款明细
     */
    public OutsourcedRepaymentDetail selectOutsourcedRepaymentDetailById(Long id);

    /**
     * 查询不良系统-委外还款明细列表
     *
     * @param outsourcedRepaymentDetail 不良系统-委外还款明细
     * @return 不良系统-委外还款明细集合
     */
    public List<OutsourcedRepaymentDetailVo> selectOutsourcedRepaymentDetailList(OutsourcedRepaymentDetail outsourcedRepaymentDetail);

    /**
     * 新增不良系统-委外还款明细
     *
     * @param outsourcedRepaymentDetail 不良系统-委外还款明细
     * @return 结果
     */
    public int insertOutsourcedRepaymentDetail(OutsourcedRepaymentDetail outsourcedRepaymentDetail);

    /**
     * 修改不良系统-委外还款明细
     *
     * @param outsourcedRepaymentDetail 不良系统-委外还款明细
     * @return 结果
     */
    public int updateOutsourcedRepaymentDetail(OutsourcedRepaymentDetail outsourcedRepaymentDetail);

    /**
     * 删除不良系统-委外还款明细
     *
     * @param id 不良系统-委外还款明细主键
     * @return 结果
     */
    public int deleteOutsourcedRepaymentDetailById(Long id);

    /**
     * 批量删除不良系统-委外还款明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOutsourcedRepaymentDetailByIds(Long[] ids);


    public int batchOutsourcedRepaymentDetail(List<OutsourcedRepaymentDetailVo> outsourcedRepaymentDetail);

    public List<OutsourcedRepaymentDetail> selectOutsourcedRepaymentDetailListByLoanBills(List<String> loanBillList);

}
