package org.ruoyi.core.badsystem.service;

import org.ruoyi.core.badsystem.domain.vo.CompanyProductVo;

import java.util.List;

/**
 * 不良系统-公司产品Service接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface ICompanyProductService
{
    /**
     * 查询不良系统-公司产品
     *
     * @param id 不良系统-公司产品主键
     * @return 不良系统-公司产品
     */
    public CompanyProductVo selectCompanyProductById(Long id);

    /**
     * 查询不良系统-公司产品列表
     *
     * @param companyProduct 不良系统-公司产品
     * @return 不良系统-公司产品集合
     */
    public List<CompanyProductVo> selectCompanyProductList(CompanyProductVo companyProduct);

    /**
     * 新增不良系统-公司产品
     *
     * @param companyProduct 不良系统-公司产品
     * @return 结果
     */
    public int insertCompanyProduct(CompanyProductVo companyProduct);

    /**
     * 修改不良系统-公司产品
     *
     * @param companyProduct 不良系统-公司产品
     * @return 结果
     */
    public int updateCompanyProduct(CompanyProductVo companyProduct);

    /**
     * 批量删除不良系统-公司产品
     *
     * @param ids 需要删除的不良系统-公司产品主键集合
     * @return 结果
     */
    public int deleteCompanyProductByIds(Long[] ids);

    /**
     * 删除不良系统-公司产品信息
     *
     * @param id 不良系统-公司产品主键
     * @return 结果
     */
    public int deleteCompanyProductById(Long id);
}
