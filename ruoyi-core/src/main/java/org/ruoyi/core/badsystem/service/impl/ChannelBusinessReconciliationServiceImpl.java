package org.ruoyi.core.badsystem.service.impl;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.badsystem.domain.ChannelBusinessReconciliation;
import org.ruoyi.core.badsystem.domain.ChannelBusinessReconciliationCollection;
import org.ruoyi.core.badsystem.domain.ChannelBusinessReconciliationRepayment;
import org.ruoyi.core.badsystem.domain.vo.ChannelBusinessReconciliationVo;
import org.ruoyi.core.badsystem.domain.vo.OutsourcedProjectVo;
import org.ruoyi.core.badsystem.mapper.ChannelBusinessReconciliationMapper;
import org.ruoyi.core.badsystem.service.IChannelBusinessReconciliationCollectionService;
import org.ruoyi.core.badsystem.service.IChannelBusinessReconciliationRepaymentService;
import org.ruoyi.core.badsystem.service.IChannelBusinessReconciliationService;
import org.ruoyi.core.badsystem.service.IOutsourcedProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;
import static org.ruoyi.core.dm.service.impl.DmAqaVintageAnalysisServiceImpl.convertToUnderscore;

/**
 * 不良系统-渠道业务对账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class ChannelBusinessReconciliationServiceImpl implements IChannelBusinessReconciliationService
{
    @Autowired
    private ChannelBusinessReconciliationMapper channelBusinessReconciliationMapper;
    @Autowired
    private IChannelBusinessReconciliationCollectionService collectionService;
    @Autowired
    private IChannelBusinessReconciliationRepaymentService repaymentService;
    @Autowired
    private IOutsourcedProjectService outsourcedProjectService;

    /**
     * 查询不良系统-渠道业务对账单
     *
     * @param id 不良系统-渠道业务对账单主键
     * @return 不良系统-渠道业务对账单
     */
    @Override
    public ChannelBusinessReconciliationVo selectChannelBusinessReconciliationById(Long id)
    {
        return channelBusinessReconciliationMapper.selectChannelBusinessReconciliationById(id);
    }

    /**
     * 查询不良系统-渠道业务对账单列表
     *
     * @param channelBusinessReconciliation 不良系统-渠道业务对账单
     * @return 不良系统-渠道业务对账单
     */
    @Override
    public List<ChannelBusinessReconciliationVo> selectChannelBusinessReconciliationList(ChannelBusinessReconciliationVo channelBusinessReconciliation)
    {
        return channelBusinessReconciliationMapper.selectChannelBusinessReconciliationList(channelBusinessReconciliation);
    }

    @Override
    public List<ChannelBusinessReconciliationVo> postList(ChannelBusinessReconciliationVo channelBusinessReconciliation)
    {
        if(channelBusinessReconciliation.getSortMap() != null){
            Map<String, String> underscoreMap = new HashMap<>();
            for (String key : channelBusinessReconciliation.getSortMap().keySet()) {
                String newKey = convertToUnderscore(key);
                underscoreMap.put(newKey, channelBusinessReconciliation.getSortMap().get(key));
            }
            channelBusinessReconciliation.setSortMap(underscoreMap);
        }
        return channelBusinessReconciliationMapper.postList(channelBusinessReconciliation);
    }

    /**
     * 新增不良系统-渠道业务对账单
     *
     * @param channelBusinessReconciliation 不良系统-渠道业务对账单
     * @return 结果
     */
    @Override
    public int insertChannelBusinessReconciliation(ChannelBusinessReconciliationVo channelBusinessReconciliation)
    {
        channelBusinessReconciliation.setCreateTime(DateUtils.getNowDate());
        channelBusinessReconciliation.setCreateBy(getUsername());
        int i = channelBusinessReconciliation.getCount();
        int j = 0;
        try{
            i++;
            int count = channelBusinessReconciliationMapper.getCountByCreateTime(DateUtils.getDate()) + i;
            String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
            channelBusinessReconciliation.setReconciliationCode(createTimeNum + String.format("%03d", count));

            //批量新增不良系统-渠道业务对账单-财务收款单   计算财务收款总额
            if (channelBusinessReconciliation.getCollectionList() != null && !channelBusinessReconciliation.getCollectionList().isEmpty()) {
                BigDecimal totalAmountCollected = channelBusinessReconciliation.getCollectionList().stream()
                        .map(ChannelBusinessReconciliationCollection::getAmountCollected)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                channelBusinessReconciliation.setTotalAmountCollected(totalAmountCollected);
            } else {
                channelBusinessReconciliation.setTotalAmountCollected(BigDecimal.ZERO);
            }

            //批量新增不良系统-渠道业务对账单-委后还款明细对象  计算平台核销金额
            if (channelBusinessReconciliation.getRepaymentList() != null && !channelBusinessReconciliation.getRepaymentList().isEmpty()) {
                BigDecimal totalRepaymentAmount = channelBusinessReconciliation.getRepaymentList().stream()
                        .map(ChannelBusinessReconciliationRepayment::getRepaymentAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                channelBusinessReconciliation.setTotalRepaymentAmount(totalRepaymentAmount);
            } else {
                channelBusinessReconciliation.setTotalRepaymentAmount(BigDecimal.ZERO);
            }
            channelBusinessReconciliation.setDifferenceAmount(channelBusinessReconciliation.getTotalAmountCollected().subtract(channelBusinessReconciliation.getTotalRepaymentAmount()));

            // 判断 differenceAmount 是否为 0
            if (channelBusinessReconciliation.getDifferenceAmount().compareTo(BigDecimal.ZERO) == 0) {
                //差价为 0 是校验差异状态为处理完成
                channelBusinessReconciliation.setVerifyDifferenceStatus("2");
            } {
                //差价不为 0 是校验差异状态为处理中
                channelBusinessReconciliation.setVerifyDifferenceStatus("1");
            }
            if((channelBusinessReconciliation.getCollectionList() == null || channelBusinessReconciliation.getCollectionList().isEmpty())
              && (channelBusinessReconciliation.getRepaymentList() == null || channelBusinessReconciliation.getRepaymentList().isEmpty())
            ){
                channelBusinessReconciliation.setVerifyDifferenceStatus("3");
            }

            j = channelBusinessReconciliationMapper.insertChannelBusinessReconciliation(channelBusinessReconciliation);

            //批量新增不良系统-渠道业务对账单-财务收款单
            if (channelBusinessReconciliation.getCollectionList() != null && !channelBusinessReconciliation.getCollectionList().isEmpty()) {
                for (ChannelBusinessReconciliationCollection collection : channelBusinessReconciliation.getCollectionList()) {
                    collection.setBusinessReconciliationId(channelBusinessReconciliation.getId());
                }
                collectionService.batchChannelBusinessReconciliationCollection(channelBusinessReconciliation.getCollectionList());
            }

            //批量新增不良系统-渠道业务对账单-委后还款明细对象
            if (channelBusinessReconciliation.getRepaymentList() != null && !channelBusinessReconciliation.getRepaymentList().isEmpty()) {
                for (ChannelBusinessReconciliationRepayment repayment : channelBusinessReconciliation.getRepaymentList()) {
                    repayment.setBusinessReconciliationId(channelBusinessReconciliation.getId());
                }
                repaymentService.batchChannelBusinessReconciliationRepayment(channelBusinessReconciliation.getRepaymentList());
            }

        } catch ( DataAccessException e){
            if (e.getCause() instanceof SQLException) {
                SQLException sqlException = (SQLException) e.getCause();
                // 根据数据库的错误代码判断是否是违反唯一约束的错误
                if (sqlException.getErrorCode() == 1062) {
                    channelBusinessReconciliation.setReconciliationCode(null);
                    channelBusinessReconciliation.setCount(i);
                    insertChannelBusinessReconciliation(channelBusinessReconciliation);
                }
            }
        }

        return j;
    }

    /**
     * 修改不良系统-渠道业务对账单
     *
     * @param channelBusinessReconciliation 不良系统-渠道业务对账单
     * @return 结果
     */
    @Override
    public int updateChannelBusinessReconciliation(ChannelBusinessReconciliation channelBusinessReconciliation)
    {
        if("2".equals(channelBusinessReconciliation.getStatus())){
            ChannelBusinessReconciliationVo reconciliation = selectChannelBusinessReconciliationById(channelBusinessReconciliation.getId());
            if(!reconciliation.getRepaymentList().isEmpty()){
                List<String> batchNumberList = reconciliation.getRepaymentList().stream().map(ChannelBusinessReconciliationRepayment::getBatchNumber).collect(Collectors.toList());
                OutsourcedProjectVo outsourcedProjectVo = new OutsourcedProjectVo();
                outsourcedProjectVo.setUpdateBy(getUsername());
                outsourcedProjectVo.setUpdateTime(DateUtils.getNowDate());
                outsourcedProjectVo.setOutsourcedProjectNumberList(batchNumberList);
                outsourcedProjectService.updateOutsourcedProjectByReconciliation(outsourcedProjectVo);
            }
        }
        channelBusinessReconciliation.setUpdateTime(DateUtils.getNowDate());
        return channelBusinessReconciliationMapper.updateChannelBusinessReconciliation(channelBusinessReconciliation);
    }

    /**
     * 批量删除不良系统-渠道业务对账单
     *
     * @param ids 需要删除的不良系统-渠道业务对账单主键
     * @return 结果
     */
    @Override
    public int deleteChannelBusinessReconciliationByIds(Long[] ids)
    {
        return channelBusinessReconciliationMapper.deleteChannelBusinessReconciliationByIds(ids);
    }

    /**
     * 删除不良系统-渠道业务对账单信息
     *
     * @param id 不良系统-渠道业务对账单主键
     * @return 结果
     */
    @Override
    public int deleteChannelBusinessReconciliationById(Long id)
    {
        return channelBusinessReconciliationMapper.deleteChannelBusinessReconciliationById(id);
    }

    /**
     * 查询不良系统-渠道业务对账单
     *
     * @param processId 不良系统-渠道业务对账单流程 id
     * @return 不良系统-渠道业务对账单
     */
    @Override
    public ChannelBusinessReconciliationVo selectChannelBusinessReconciliationByProcessId(String processId){
        return channelBusinessReconciliationMapper.selectChannelBusinessReconciliationByProcessId(processId);
    }
}
