package org.ruoyi.core.badsystem.service.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.badsystem.domain.OutsourcedRepaymentDetail;
import org.ruoyi.core.badsystem.domain.vo.MechanismVo;
import org.ruoyi.core.badsystem.domain.vo.OutsourcedRepaymentDetailVo;
import org.ruoyi.core.badsystem.mapper.OutsourcedRepaymentDetailMapper;
import org.ruoyi.core.badsystem.service.IMechanismService;
import org.ruoyi.core.badsystem.service.IOutsourcedRepaymentDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 不良系统-委外还款明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Service
public class OutsourcedRepaymentDetailServiceImpl implements IOutsourcedRepaymentDetailService
{
    @Autowired
    private OutsourcedRepaymentDetailMapper outsourcedRepaymentDetailMapper;
    @Autowired
    private IMechanismService mechanismService;
    /**
     * 查询不良系统-委外还款明细
     *
     * @param id 不良系统-委外还款明细主键
     * @return 不良系统-委外还款明细
     */
    @Override
    public OutsourcedRepaymentDetail selectOutsourcedRepaymentDetailById(Long id)
    {
        return outsourcedRepaymentDetailMapper.selectOutsourcedRepaymentDetailById(id);
    }

    /**
     * 查询不良系统-委外还款明细列表
     *
     * @param outsourcedRepaymentDetail 不良系统-委外还款明细
     * @return 不良系统-委外还款明细
     */
    @Override
    public List<OutsourcedRepaymentDetailVo> selectOutsourcedRepaymentDetailList(OutsourcedRepaymentDetail outsourcedRepaymentDetail)
    {
        return outsourcedRepaymentDetailMapper.selectOutsourcedRepaymentDetailList(outsourcedRepaymentDetail);
    }

    /**
     * 新增不良系统-委外还款明细
     *
     * @param outsourcedRepaymentDetail 不良系统-委外还款明细
     * @return 结果
     */
    @Override
    public int insertOutsourcedRepaymentDetail(OutsourcedRepaymentDetail outsourcedRepaymentDetail)
    {
        outsourcedRepaymentDetail.setCreateTime(DateUtils.getNowDate());
        return outsourcedRepaymentDetailMapper.insertOutsourcedRepaymentDetail(outsourcedRepaymentDetail);
    }

    /**
     * 修改不良系统-委外还款明细
     *
     * @param outsourcedRepaymentDetail 不良系统-委外还款明细
     * @return 结果
     */
    @Override
    public int updateOutsourcedRepaymentDetail(OutsourcedRepaymentDetail outsourcedRepaymentDetail)
    {
        outsourcedRepaymentDetail.setUpdateTime(DateUtils.getNowDate());
        return outsourcedRepaymentDetailMapper.updateOutsourcedRepaymentDetail(outsourcedRepaymentDetail);
    }

    /**
     * 批量删除不良系统-委外还款明细
     *
     * @param ids 需要删除的不良系统-委外还款明细主键
     * @return 结果
     */
    @Override
    public int deleteOutsourcedRepaymentDetailByIds(Long[] ids)
    {
        return outsourcedRepaymentDetailMapper.deleteOutsourcedRepaymentDetailByIds(ids);
    }

    /**
     * 删除不良系统-委外还款明细信息
     *
     * @param id 不良系统-委外还款明细主键
     * @return 结果
     */
    @Override
    public int deleteOutsourcedRepaymentDetailById(Long id)
    {
        return outsourcedRepaymentDetailMapper.deleteOutsourcedRepaymentDetailById(id);
    }

    @Override
    public String importData(List<OutsourcedRepaymentDetailVo> outsourcedRepaymentDetailVos){
        int i = 0;
        for (OutsourcedRepaymentDetailVo vo : outsourcedRepaymentDetailVos) {
            i++;

            if(vo.getLoanBill().isEmpty()){
                throw new ServiceException("第"+ i +"行数据,借款单格式不正确");
            }

            if(vo.getOutsourcedProjectNumber().isEmpty()){
                throw new ServiceException("第"+ i +"行数据,分案批次格式不正确");
            }

            if(vo.getBorrowerName().isEmpty()){
                throw new ServiceException("第"+ i +"行数据,借款人姓名格式不正确");
            }

            if(vo.getPhoneNumber().isEmpty()){
                throw new ServiceException("第"+ i +"行数据,联系人电话格式不正确");
            }

            if(vo.getRepaymentAmount() == null){
                throw new ServiceException("第"+ i +"行数据,还款金额格式不正确");
            }

            if(vo.getRepaymentTime() == null){
                throw new ServiceException("第"+ i +"行数据,还款时间格式不正确");
            }

            if(vo.getSerialNumber().isEmpty()){
                throw new ServiceException("第"+ i +"行数据,流水号格式不正确");
            }

            if(vo.getTrusteeInstitutionName().isEmpty()){
                throw new ServiceException("第"+ i +"行数据,受托机构格式不正确");
            }

            if(vo.getCheckStatus().isEmpty()){
                throw new ServiceException("第"+ i +"行数据,核对状态格式不正确");
            }

            if(vo.getServiceAmount() == null){
                throw new ServiceException("第"+ i +"行数据,服务费用金额格式不正确");
            }
        }

        //查询表格是否有重复数据
        // 存储已出现的唯一组合（loanBill, lorrowerName, phoneNumber）
        Set<String> seen = new HashSet<>();
        // 存储重复数据的索引
        List<Integer> duplicateIndexes = new ArrayList<>();

        for (int p = 0; p < outsourcedRepaymentDetailVos.size(); p++) {
            OutsourcedRepaymentDetailVo vo = outsourcedRepaymentDetailVos.get(p);
            // 拼接这三个字段为一个字符串作为唯一标识
            String key = vo.getLoanBill()
                    + "-" + vo.getBorrowerName()
                    + "-" + vo.getPhoneNumber()
                    + "-" + vo.getRepaymentAmount()
                    + "-" + vo.getSerialNumber()
                    + "-" + vo.getRepaymentTime();

            if (seen.contains(key)) {
                // 如果集合中已经包含这个组合，则是重复的
                duplicateIndexes.add(p + 1);
            } else {
                // 如果是第一次出现，将其添加到集合中
                seen.add(key);
            }
        }

        // 输出重复数据的索引
        if (!duplicateIndexes.isEmpty()){
            throw new ServiceException("重复数据的行号: " + duplicateIndexes);
        }

        //查询数据库中是否存在重复数据
        List<String> loanBills = outsourcedRepaymentDetailVos.stream().map(OutsourcedRepaymentDetailVo::getLoanBill).distinct().collect(Collectors.toList());
        List<OutsourcedRepaymentDetail> detailList = outsourcedRepaymentDetailMapper.selectOutsourcedRepaymentDetailListByLoanBills(loanBills);
        //借款单、姓名、手机号、身份证号、还款金额、流水号、还款时间 同时相同才算重复
        int k = 0;
        for (OutsourcedRepaymentDetailVo vo : outsourcedRepaymentDetailVos) {
            k++;
            for (OutsourcedRepaymentDetail detail : detailList) {
                if(vo.getLoanBill().equals(detail.getLoanBill())
                        && vo.getBorrowerName().equals(detail.getBorrowerName())
                        && vo.getPhoneNumber().equals(detail.getPhoneNumber())
                        && vo.getRepaymentAmount().compareTo(detail.getRepaymentAmount()) == 0
                        && vo.getSerialNumber().equals(detail.getSerialNumber())
                        && vo.getRepaymentTime().equals(detail.getRepaymentTime())
                ){
                    throw new ServiceException("第"+ k +"行数据,重复请检查!");
                }
            }
        }
        //查询受托机构信息
        List<String> trusteeInstitutionNameList= outsourcedRepaymentDetailVos.stream().map(OutsourcedRepaymentDetailVo::getTrusteeInstitutionName).distinct().collect(Collectors.toList());
        MechanismVo mechanismVo = new MechanismVo();
        mechanismVo.setMechanismNameList(trusteeInstitutionNameList);
        Map<String, Long> echanismMap = mechanismService.selectMechanismList(mechanismVo).stream()
                .collect(Collectors.toMap(
                        MechanismVo::getMechanismName, MechanismVo::getId ,
                        (existing, replacement) -> existing // 如果发生键冲突，保留第一个值
                ));
        int j = 0;
        for (OutsourcedRepaymentDetailVo vo : outsourcedRepaymentDetailVos) {
            if (echanismMap.containsKey(vo.getTrusteeInstitutionName())) {
                vo.setTrusteeInstitutionId(echanismMap.get(vo.getTrusteeInstitutionName()));
            } else {
                throw new ServiceException("第"+ j +"行数据,受托机构不存在");
            }
            vo.setCreateBy(getUsername());
        }

        outsourcedRepaymentDetailMapper.batchOutsourcedRepaymentDetail(outsourcedRepaymentDetailVos);

        return "导入成功";
    }
}
