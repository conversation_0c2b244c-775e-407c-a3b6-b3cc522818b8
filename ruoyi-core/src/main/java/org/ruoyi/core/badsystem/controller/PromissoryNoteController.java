package org.ruoyi.core.badsystem.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.badsystem.domain.PromissoryNote;
import org.ruoyi.core.badsystem.domain.vo.PromissoryNoteVo;
import org.ruoyi.core.badsystem.service.IPromissoryNoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 不良系统-借据Controller
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@RestController
@RequestMapping("/promissory/note")
public class PromissoryNoteController extends BaseController
{
    @Autowired
    private IPromissoryNoteService promissoryNoteService;

    /**
     * 查询不良系统-借据列表
     */
    //@PreAuthorize("@ss.hasPermi('system:note:list')")
    @GetMapping("/list")
    public TableDataInfo list(PromissoryNoteVo promissoryNote)
    {
        startPage();
        List<PromissoryNoteVo> list = promissoryNoteService.selectPromissoryNoteList(promissoryNote);
        return getDataTable(list);
    }

    /**
     * 导出不良系统-借据列表
     */
    //@PreAuthorize("@ss.hasPermi('system:note:export')")
    @Log(title = "不良系统-借据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PromissoryNoteVo promissoryNote)
    {
        List<PromissoryNoteVo> list = promissoryNoteService.selectPromissoryNoteList(promissoryNote);
        ExcelUtil<PromissoryNoteVo> util = new ExcelUtil<PromissoryNoteVo>(PromissoryNoteVo.class);
        util.exportExcel(response, list, "不良系统-借据数据");
    }

    /**
     * 获取不良系统-借据详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:note:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(promissoryNoteService.selectPromissoryNoteById(id));
    }

    /**
     * 新增不良系统-借据
     */
    //@PreAuthorize("@ss.hasPermi('system:note:add')")
    @Log(title = "不良系统-借据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PromissoryNoteVo promissoryNote)
    {
        return toAjax(promissoryNoteService.insertPromissoryNote(promissoryNote));
    }

    /**
     * 修改不良系统-借据
     */
    //@PreAuthorize("@ss.hasPermi('system:note:edit')")
    @Log(title = "不良系统-借据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PromissoryNote promissoryNote)
    {
        return toAjax(promissoryNoteService.updatePromissoryNote(promissoryNote));
    }

    /**
     * 删除不良系统-借据
     */
    //@PreAuthorize("@ss.hasPermi('system:note:remove')")
    @Log(title = "不良系统-借据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(promissoryNoteService.deletePromissoryNoteByIds(ids));
    }
}
