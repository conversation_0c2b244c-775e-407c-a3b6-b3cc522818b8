package org.ruoyi.core.badsystem.mapper;

import org.ruoyi.core.badsystem.domain.FinancialSettlementReconciliation;

import java.util.List;

/**
 * 不良系统-财务结算单-业务对账单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface FinancialSettlementReconciliationMapper
{
    /**
     * 查询不良系统-财务结算单-业务对账单
     *
     * @param id 不良系统-财务结算单-业务对账单主键
     * @return 不良系统-财务结算单-业务对账单
     */
    public FinancialSettlementReconciliation selectFinancialSettlementReconciliationById(Long id);

    /**
     * 查询不良系统-财务结算单-业务对账单列表
     *
     * @param financialSettlementReconciliation 不良系统-财务结算单-业务对账单
     * @return 不良系统-财务结算单-业务对账单集合
     */
    public List<FinancialSettlementReconciliation> selectFinancialSettlementReconciliationList(FinancialSettlementReconciliation financialSettlementReconciliation);

    /**
     * 新增不良系统-财务结算单-业务对账单
     *
     * @param financialSettlementReconciliation 不良系统-财务结算单-业务对账单
     * @return 结果
     */
    public int insertFinancialSettlementReconciliation(FinancialSettlementReconciliation financialSettlementReconciliation);

    /**
     * 修改不良系统-财务结算单-业务对账单
     *
     * @param financialSettlementReconciliation 不良系统-财务结算单-业务对账单
     * @return 结果
     */
    public int updateFinancialSettlementReconciliation(FinancialSettlementReconciliation financialSettlementReconciliation);

    /**
     * 删除不良系统-财务结算单-业务对账单
     *
     * @param id 不良系统-财务结算单-业务对账单主键
     * @return 结果
     */
    public int deleteFinancialSettlementReconciliationById(Long id);

    /**
     * 批量删除不良系统-财务结算单-业务对账单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinancialSettlementReconciliationByIds(Long[] ids);

    /**
     * 批量新增不良系统-财务结算单-业务对账单
     *
     * @param financialSettlementReconciliation 不良系统-财务结算单-业务对账单
     * @return 结果
     */
    public int batchFinancialSettlementReconciliation(List<FinancialSettlementReconciliation> financialSettlementReconciliation);

    /**
     * 根据关联财务结算单 id   删除不良系统-财务结算单-业务对账单
     *
     * @param financialSettlementId 不良系统-财务结算单-付款明细主键
     * @return 结果
     */
    public int deleteFinancialSettlementReconciliationByFinancialSettlementId(Long financialSettlementId);
}
