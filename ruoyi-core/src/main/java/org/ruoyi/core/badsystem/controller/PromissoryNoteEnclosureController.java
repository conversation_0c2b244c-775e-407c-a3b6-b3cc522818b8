package org.ruoyi.core.badsystem.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.badsystem.domain.PromissoryNoteEnclosure;
import org.ruoyi.core.badsystem.service.IPromissoryNoteEnclosureService;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 不良系统-借据-附件Controller
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@RestController
@RequestMapping("/promissory/note/enclosure")
public class PromissoryNoteEnclosureController extends BaseController
{
    @Autowired
    private IPromissoryNoteEnclosureService promissoryNoteEnclosureService;

    /**
     * 查询不良系统-借据-附件列表
     */
    //@PreAuthorize("@ss.hasPermi('system:enclosure:list')")
    @GetMapping("/list")
    public TableDataInfo list(PromissoryNoteEnclosure promissoryNoteEnclosure)
    {
        startPage();
        List<PromissoryNoteEnclosure> list = promissoryNoteEnclosureService.selectPromissoryNoteEnclosureList(promissoryNoteEnclosure);
        return getDataTable(list);
    }

    /**
     * 导出不良系统-借据-附件列表
     */
    //@PreAuthorize("@ss.hasPermi('system:enclosure:export')")
    @Log(title = "不良系统-借据-附件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PromissoryNoteEnclosure promissoryNoteEnclosure)
    {
        List<PromissoryNoteEnclosure> list = promissoryNoteEnclosureService.selectPromissoryNoteEnclosureList(promissoryNoteEnclosure);
        ExcelUtil<PromissoryNoteEnclosure> util = new ExcelUtil<PromissoryNoteEnclosure>(PromissoryNoteEnclosure.class);
        util.exportExcel(response, list, "不良系统-借据-附件数据");
    }

    /**
     * 获取不良系统-借据-附件详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:enclosure:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(promissoryNoteEnclosureService.selectPromissoryNoteEnclosureById(id));
    }

    /**
     * 新增不良系统-借据-附件
     */
    //@PreAuthorize("@ss.hasPermi('system:enclosure:add')")
    @Log(title = "不良系统-借据-附件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PromissoryNoteEnclosure promissoryNoteEnclosure)
    {
        return toAjax(promissoryNoteEnclosureService.insertPromissoryNoteEnclosure(promissoryNoteEnclosure));
    }

    /**
     * 修改不良系统-借据-附件
     */
    //@PreAuthorize("@ss.hasPermi('system:enclosure:edit')")
    @Log(title = "不良系统-借据-附件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PromissoryNoteEnclosure promissoryNoteEnclosure)
    {
        return toAjax(promissoryNoteEnclosureService.updatePromissoryNoteEnclosure(promissoryNoteEnclosure));
    }

    /**
     * 删除不良系统-借据-附件
     */
    //@PreAuthorize("@ss.hasPermi('system:enclosure:remove')")
    @Log(title = "不良系统-借据-附件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(promissoryNoteEnclosureService.deletePromissoryNoteEnclosureByIds(ids));
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PromissoryNoteEnclosure> util = new ExcelUtil<PromissoryNoteEnclosure>(PromissoryNoteEnclosure.class);
        util.importTemplateExcel(response, "不良系统-借据-附件");
    }

    /**
     * 结算账号明细导入
     *
     * @param file 导入文件
     * @throws Exception
     */
    @Log(title = "不良系统-借据-附件导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<PromissoryNoteEnclosure> util = new ExcelUtil<PromissoryNoteEnclosure>(PromissoryNoteEnclosure.class);
        List<PromissoryNoteEnclosure> list = util.importExcel(file.getInputStream());
        if (list == null || list.isEmpty()) {
            throw new ServiceException("导入数据为空");
        }
        String name = file.getOriginalFilename();
        String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.BAD_SYSTEM, file);


        Map<String,Object> data = new HashMap<>();
        data.put("promissoryNoteEnclosureList",list);

        data.put("importNumber",list.size());
        data.put("fileName",name);
        data.put("fileUrl",url);
        data.put("importPrincipalInterest",new BigDecimal(BigInteger.ZERO));
        data.put("importAmount",new BigDecimal(BigInteger.ZERO));
        data.put("importInterest",new BigDecimal(BigInteger.ZERO));
        if (!list.isEmpty()){
            BigDecimal totalRemainingDue = list.stream()
                    .map(p -> p.getRemainingDue() == null ? BigDecimal.ZERO : p.getRemainingDue())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            data.put("importPrincipalInterest",totalRemainingDue);

            BigDecimal totalRemainingPrincipal = list.stream()
                    .map(p -> p.getRemainingPrincipal() == null ? BigDecimal.ZERO : p.getRemainingPrincipal())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            data.put("importAmount",totalRemainingPrincipal);

            BigDecimal totalLoanInterest = list.stream()
                    .map(p -> p.getLoanInterest() == null ? BigDecimal.ZERO : p.getLoanInterest())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            data.put("importInterest",totalLoanInterest);
        }
        return AjaxResult.success("导入成功",data);
    }
}
