package org.ruoyi.core.badsystem.service.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.badsystem.domain.FinancialSettlement;
import org.ruoyi.core.badsystem.domain.OutsourcedProject;
import org.ruoyi.core.badsystem.domain.vo.FinancialSettlementVo;
import org.ruoyi.core.badsystem.domain.vo.MechanismImport;
import org.ruoyi.core.badsystem.domain.vo.MechanismVo;
import org.ruoyi.core.badsystem.domain.vo.OutsourcedProjectVo;
import org.ruoyi.core.badsystem.mapper.MechanismMapper;
import org.ruoyi.core.badsystem.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 机构Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class MechanismServiceImpl implements IMechanismService
{
    @Autowired
    private MechanismMapper mechanismMapper;
    @Autowired
    private IMechanismSettlementFormulaService mechanismSettlementFormulaService;
    @Autowired
    private IMechanismFinanceAccountService mechanismFinanceAccountService;
    @Autowired
    private IMechanismContactsService mechanismContactsService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IOutsourcedProjectService outsourcedProjectService;
    @Autowired
    private IFinancialSettlementService financialSettlementService;

    /**
     * 查询机构
     *
     * @param id 机构主键
     * @return 机构
     */
    @Override
    public MechanismVo selectMechanismById(Long id)
    {
        MechanismVo mechanism = mechanismMapper.selectMechanismById(id);
        List<Long> businessManager = mechanism.getBusinessManager();
        if(businessManager != null && !businessManager.isEmpty()){
            List<SysUser> sysUsers = userService.selectUserByUserIds(businessManager);
            mechanism.setBusinessManagerList(sysUsers);
        }
        OutsourcedProject outsourcedProject = new OutsourcedProject();
        outsourcedProject.setCreditorInstitutionsId(id);
        List<OutsourcedProjectVo> outsourcedProjectVos = outsourcedProjectService.selectOutsourcedProjectList(outsourcedProject);
        if (outsourcedProjectVos != null && !outsourcedProjectVos.isEmpty()) {
            BigDecimal totalAmount = outsourcedProjectVos.stream()
                    .map(OutsourcedProjectVo::getRealTotalAmountMoney)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            mechanism.setTotalOutsourcedPrincipal(totalAmount);
        } else {
            mechanism.setTotalOutsourcedPrincipal(BigDecimal.ZERO);
        }

        FinancialSettlement financialSettlement = new FinancialSettlement();
        financialSettlement.setSettlementEntity(id);
        List<FinancialSettlementVo> financialSettlementVos = financialSettlementService.selectFinancialSettlementList(financialSettlement);
        if (financialSettlementVos != null && !financialSettlementVos.isEmpty()) {
            BigDecimal financialReceiptsAmount = financialSettlementVos.stream()
                    .filter(Objects::nonNull)
                    .map(vo -> Optional.ofNullable(vo.getFinancialReceiptsAmount()).orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            mechanism.setTotalRepaymentAmount(financialReceiptsAmount);
        } else {
            mechanism.setTotalRepaymentAmount(BigDecimal.ZERO);
        }

        return mechanism;
    }

    /**
     * 查询机构列表
     *
     * @param mechanism 机构
     * @return 机构
     */
    @Override
    public List<MechanismVo> selectMechanismList(MechanismVo mechanism)
    {
        return mechanismMapper.selectMechanismList(mechanism);
    }

    /**
     * 新增机构
     *
     * @param mechanism 机构
     * @return 结果
     */
    @Override
    public int insertMechanism(MechanismVo mechanism)
    {
        mechanism.setCreateBy(getUsername());
        mechanism.setCreateTime(DateUtils.getNowDate());
        int i = mechanism.getCount();
        int j = 0;
        try {
            i++;
            int count = mechanismMapper.getCountByCreateTime(DateUtils.getDate()) + i;
            String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
            mechanism.setMechanismCode(createTimeNum + String.format("%03d", count));

            j = mechanismMapper.insertMechanism(mechanism);
            if (mechanism.getMechanismSettlementFormulaList() != null && !mechanism.getMechanismSettlementFormulaList().isEmpty()){
                mechanism.getMechanismSettlementFormulaList().forEach( vo -> {
                        vo.setMechanismId(mechanism.getId());
                    }
                );
                mechanismSettlementFormulaService.batchMechanismSettlementFormula(mechanism.getMechanismSettlementFormulaList());
            }

            if (mechanism.getMechanismFinanceAccountList() != null && !mechanism.getMechanismFinanceAccountList().isEmpty()){
                mechanism.getMechanismFinanceAccountList().forEach( vo -> {
                            vo.setMechanismId(mechanism.getId());
                        }
                );
                mechanismFinanceAccountService.batchMechanismFinanceAccount(mechanism.getMechanismFinanceAccountList());
            }

            if (mechanism.getMechanismContactsList() != null && !mechanism.getMechanismContactsList().isEmpty()){
                mechanism.getMechanismContactsList().forEach( vo -> {
                            vo.setMechanismId(mechanism.getId());
                        }
                );
                mechanismContactsService.batchMechanismContacts(mechanism.getMechanismContactsList());
            }
        } catch (DataAccessException e){
            if (e.getCause() instanceof SQLException) {
                SQLException sqlException = (SQLException) e.getCause();
                // 根据数据库的错误代码判断是否是违反唯一约束的错误
                if (sqlException.getErrorCode() == 1062) {
                    mechanism.setMechanismCode(null);
                    mechanism.setCount(i);
                    insertMechanism(mechanism);
                }
            }
        }
        return j;
    }

    /**
     * 修改机构
     *
     * @param mechanism 机构
     * @return 结果
     */
    @Override
    public int updateMechanism(MechanismVo mechanism)
    {
        mechanismSettlementFormulaService.deleteByMechanismId(mechanism.getId());
        if (mechanism.getMechanismSettlementFormulaList() != null && !mechanism.getMechanismSettlementFormulaList().isEmpty()){
            mechanism.getMechanismSettlementFormulaList().forEach( vo -> {
                        vo.setMechanismId(mechanism.getId());
                    }
            );
            mechanismSettlementFormulaService.batchMechanismSettlementFormula(mechanism.getMechanismSettlementFormulaList());
        }

        mechanismFinanceAccountService.deleteByMechanismId(mechanism.getId());
        if (mechanism.getMechanismFinanceAccountList() != null && !mechanism.getMechanismFinanceAccountList().isEmpty()){
            mechanism.getMechanismFinanceAccountList().forEach( vo -> {
                        vo.setMechanismId(mechanism.getId());
                    }
            );
            mechanismFinanceAccountService.batchMechanismFinanceAccount(mechanism.getMechanismFinanceAccountList());
        }

        mechanismContactsService.deleteByMechanismId(mechanism.getId());
        if (mechanism.getMechanismContactsList() != null && !mechanism.getMechanismContactsList().isEmpty()){
            mechanism.getMechanismContactsList().forEach( vo -> {
                        vo.setMechanismId(mechanism.getId());
                    }
            );
            mechanismContactsService.batchMechanismContacts(mechanism.getMechanismContactsList());
        }

        mechanism.setUpdateBy(getUsername());
        mechanism.setUpdateTime(DateUtils.getNowDate());
        return mechanismMapper.updateMechanism(mechanism);
    }

    /**
     * 批量删除机构
     *
     * @param ids 需要删除的机构主键
     * @return 结果
     */
    @Override
    public int deleteMechanismByIds(Long[] ids)
    {
        return mechanismMapper.deleteMechanismByIds(ids);
    }

    /**
     * 删除机构信息
     *
     * @param id 机构主键
     * @return 结果
     */
    @Override
    public int deleteMechanismById(Long id)
    {
        return mechanismMapper.deleteMechanismById(id);
    }


    /**
     * 修改机构-开启状态
     *
     * @param mechanism 机构
     * @return 结果
     */
    @Override
    public int updateMechanismCollaborationStatus(MechanismVo mechanism)
    {
        //根据传来的开启状态来修改相反的数值
        if (mechanism.getCollaborationStatus().equals("1")){
            mechanism.setCollaborationStatus("2");
        } else {
            mechanism.setCollaborationStatus("1");
        }

        mechanism.setUpdateBy(getUsername());
        mechanism.setUpdateTime(DateUtils.getNowDate());
        return mechanismMapper.updateMechanism(mechanism);
    }


    /**
     * 修改机构-关联流程
     *
     * @param mechanism 机构
     * @return 结果
     */
    @Override
    public int processMechanismVo(MechanismVo mechanism)
    {
        mechanism.setUpdateBy(getUsername());
        mechanism.setUpdateTime(DateUtils.getNowDate());
        return mechanismMapper.updateMechanism(mechanism);
    }

    @Override
    public MechanismVo selectMechanismByProcessId(String processId){
        return mechanismMapper.selectMechanismByProcessId(processId);
    }

    @Override
    public Map<String,List<MechanismImport>> importDataCheck(List<MechanismImport> mechanismImportList){
        // 检查空值
        List<MechanismImport> emptyList = mechanismImportList.stream()
                .filter(item -> item.getMechanismName() == null || item.getMechanismName().trim().isEmpty())
                .collect(Collectors.toList());
        if (!emptyList.isEmpty()) {
            throw new RuntimeException("存在机构名称为空的记录");
        }
        // 检查导入列表中重复
        Map<String, Long> nameCountMap = mechanismImportList.stream()
                .collect(Collectors.groupingBy(MechanismImport::getMechanismName, Collectors.counting()));

        List<MechanismImport> repeatList = mechanismImportList.stream()
                .filter(item -> nameCountMap.get(item.getMechanismName()) > 1)
                .collect(Collectors.toList());
        if (!repeatList.isEmpty()) {
            throw new RuntimeException("存在重复的机构名称: " +
                    repeatList.stream().map(MechanismImport::getMechanismName).distinct().collect(Collectors.joining(",")));
        }

        HashMap<String,List<MechanismImport>> map = new HashMap<>();
        //未识别成功数据
        List<MechanismImport> duplicateList = new ArrayList<>();

        List<String> nameList = mechanismImportList.stream().map(MechanismImport::getMechanismName).collect(Collectors.toList());

        MechanismVo mechanism = new MechanismVo();
        mechanism.setMechanismNameList(nameList);
        List<MechanismVo> mechanismVos = mechanismMapper.selectMechanismList(mechanism);
        //重复的机构名称全称
        List<String> mechanismVoNameList = mechanismVos.stream().map(MechanismVo::getMechanismName).collect(Collectors.toList());

        // 筛选出MechanismName重复的数据 加入未识别list中
        duplicateList.addAll(mechanismImportList.stream()
                        .filter(item -> mechanismVoNameList.contains(item.getMechanismName()))
                        .collect(Collectors.toList()));
        mechanismImportList.removeAll(duplicateList);

        String[] type = {"债权机构","受托方","委托方","渠道"};
        String[] mode = {"催收/调解","法诉","保全"};
        SysUser sysUser = new SysUser();
        sysUser.setStatus("0");
        List<SysUser> sysUsers = userService.selectUserList(sysUser);
        String[] nickNames = sysUsers.stream().map(SysUser::getNickName).toArray(String[]::new);
        mechanismImportList.forEach(item -> {
            String mechanismDisposalMode = item.getMechanismDisposalMode();
            if (mechanismDisposalMode != null && !mechanismDisposalMode.isEmpty()) {
                String[] modes = mechanismDisposalMode.split(",");
                for (String m : modes) {
                    if (!Arrays.asList(mode).contains(m.trim())) {
                        duplicateList.add(item);
                        return; // 只要有一个不合法就添加到duplicateList并跳出循环
                    }
                }
            }

            String mechanismType = item.getMechanismType();
            if (mechanismType != null && !mechanismType.isEmpty()) {
                String[] types = mechanismType.split(",");
                for (String m : types) {
                    if (!Arrays.asList(type).contains(m.trim())) {
                        duplicateList.add(item);
                        return; // 只要有一个不合法就添加到duplicateList并跳出循环
                    }
                }
            }
            String businessManager = item.getBusinessManager();
            if (businessManager != null && !businessManager.isEmpty()) {
                String[] businessManagers = businessManager.split(",");
                for (String m : businessManagers) {
                    if (!Arrays.asList(nickNames).contains(m.trim())) {
                        duplicateList.add(item);
                        return; // 只要有一个不合法就添加到duplicateList并跳出循环
                    }
                }
            }
        });
        map.put("duplicateList", duplicateList);
        //剔除未识别数据
        mechanismImportList.removeAll(duplicateList);
        map.put("successList", mechanismImportList);
        return map;
    }


    @Override
    public String importData(List<MechanismImport> mechanismImportList){
        HashMap<String,Integer> typeMap = new HashMap<>();
        typeMap.put("债权机构",1);
        typeMap.put("受托方",2);
        typeMap.put("委托方",3);
        typeMap.put("渠道",4);
        HashMap<String,Integer> modeMap = new HashMap<>();
        modeMap.put("催收/调解",1);
        modeMap.put("法诉",2);
        modeMap.put("保全",3);
        SysUser sysUser = new SysUser();
        sysUser.setStatus("0");
        List<SysUser> sysUsers = userService.selectUserList(sysUser);
        Map<String, Long> userMap = sysUsers.stream()
                .collect(Collectors.toMap(
                        SysUser::getNickName,
                        SysUser::getUserId,
                        (existing, replacement) -> existing // 如果有重复key，保留已存在的
                ));
        List<MechanismVo> mechanismVos = new ArrayList<>();
        int count = mechanismMapper.getCountByCreateTime(DateUtils.getDate());
        String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
        AtomicInteger i = new AtomicInteger();
        mechanismImportList.forEach(item -> {
            MechanismVo mechanismVo = new MechanismVo();
            mechanismVo.setMechanismName(item.getMechanismName());
            mechanismVo.setMechanismShortName(item.getMechanismShortName());
            mechanismVo.setJoinTime(item.getJoinTime());
            mechanismVo.setSignEndTime(item.getSignEndTime());
            i.getAndIncrement();
            mechanismVo.setMechanismCode(createTimeNum + String.format("%03d", count + i.get()));

            String mechanismType = item.getMechanismType();
            if (mechanismType!= null &&!mechanismType.isEmpty()) {
                String[] types = mechanismType.split(",");
                List<Integer> typeList = new ArrayList<>();
                for (String type : types) {
                    typeList.add(typeMap.get(type));
                }
                mechanismVo.setMechanismType(typeList);
            }

            String mechanismDisposalMode = item.getMechanismDisposalMode();
            if (mechanismDisposalMode!= null &&!mechanismDisposalMode.isEmpty()) {
                String[] modes = mechanismDisposalMode.split(",");
                List<Integer> modeList = new ArrayList<>();
                for (String mode : modes) {
                    modeList.add(modeMap.get(mode));
                }
                mechanismVo.setMechanismDisposalMode(modeList);
            }

            String businessManager = item.getBusinessManager();
            if (businessManager!= null &&!businessManager.isEmpty()) {
                String[] managers = businessManager.split(",");
                List<Long> idList = new ArrayList<>();
                for (String manager : managers) {
                    idList.add(userMap.get(manager));
                }
                mechanismVo.setBusinessManager(idList);
            }
            mechanismVo.setCreateBy(getUsername());
            mechanismVo.setCreateTime(DateUtils.getNowDate());

            mechanismVos.add(mechanismVo);
        });
        mechanismMapper.batchMechanism(mechanismVos);
        return "导入成功";
    }


}
