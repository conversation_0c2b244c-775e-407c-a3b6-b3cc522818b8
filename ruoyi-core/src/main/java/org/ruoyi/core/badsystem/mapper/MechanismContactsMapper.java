package org.ruoyi.core.badsystem.mapper;

import org.ruoyi.core.badsystem.domain.MechanismContacts;

import java.util.List;

/**
 * 机构-联系人Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface MechanismContactsMapper
{
    /**
     * 查询机构-联系人
     *
     * @param id 机构-联系人主键
     * @return 机构-联系人
     */
    public MechanismContacts selectMechanismContactsById(Long id);

    /**
     * 查询机构-联系人列表
     *
     * @param mechanismContacts 机构-联系人
     * @return 机构-联系人集合
     */
    public List<MechanismContacts> selectMechanismContactsList(MechanismContacts mechanismContacts);

    /**
     * 新增机构-联系人
     *
     * @param mechanismContacts 机构-联系人
     * @return 结果
     */
    public int insertMechanismContacts(MechanismContacts mechanismContacts);

    /**
     * 修改机构-联系人
     *
     * @param mechanismContacts 机构-联系人
     * @return 结果
     */
    public int updateMechanismContacts(MechanismContacts mechanismContacts);

    /**
     * 删除机构-联系人
     *
     * @param id 机构-联系人主键
     * @return 结果
     */
    public int deleteMechanismContactsById(Long id);

    /**
     * 批量删除机构-联系人
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMechanismContactsByIds(Long[] ids);


    /**
     * 批量新增机构-联系人
     *
     * @param  mechanismContactsList
     * @return 结果
     */
    public int batchMechanismContacts(List<MechanismContacts> mechanismContactsList);


    public int deleteByMechanismId(Long mechanismId);
}
