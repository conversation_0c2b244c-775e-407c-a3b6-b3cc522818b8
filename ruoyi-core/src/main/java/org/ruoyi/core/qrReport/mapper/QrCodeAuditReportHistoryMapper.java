package org.ruoyi.core.qrReport.mapper;

import org.ruoyi.core.qrReport.domain.QrCodeAuditReportHistory;

import java.util.List;

/**
 * 审计报告对比Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
public interface QrCodeAuditReportHistoryMapper 
{
    /**
     * 查询审计报告对比
     * 
     * @param id 审计报告对比主键
     * @return 审计报告对比
     */
    public QrCodeAuditReportHistory selectQrCodeAuditReportHistoryById(Long id);

    /**
     * 查询审计报告对比列表
     * 
     * @param qrCodeAuditReportHistory 审计报告对比
     * @return 审计报告对比集合
     */
    public List<QrCodeAuditReportHistory> selectQrCodeAuditReportHistoryList(QrCodeAuditReportHistory qrCodeAuditReportHistory);

    /**
     * 新增审计报告对比
     * 
     * @param qrCodeAuditReportHistory 审计报告对比
     * @return 结果
     */
    public int insertQrCodeAuditReportHistory(QrCodeAuditReportHistory qrCodeAuditReportHistory);

    /**
     * 修改审计报告对比
     * 
     * @param qrCodeAuditReportHistory 审计报告对比
     * @return 结果
     */
    public int updateQrCodeAuditReportHistory(QrCodeAuditReportHistory qrCodeAuditReportHistory);

    /**
     * 删除审计报告对比
     * 
     * @param id 审计报告对比主键
     * @return 结果
     */
    public int deleteQrCodeAuditReportHistoryById(Long id);

    /**
     * 批量删除审计报告对比
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQrCodeAuditReportHistoryByIds(Long[] ids);
}
