package org.ruoyi.core.qrReport.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 审计报告对比对象 qr_code_audit_report_history
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
public class QrCodeAuditReportHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 关联id */
    @Excel(name = "关联id")
    private Long relationId;

    /** 关联类型(1审计报告) */
    @Excel(name = "关联类型(1审计报告)")
    private String relationType;

    /** 记录修改前json */
    @Excel(name = "记录修改前json")
    private String oldJsonDate;

    /** 记录修改后json */
    @Excel(name = "记录修改后json")
    private String newJsonDate;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setRelationId(Long relationId) 
    {
        this.relationId = relationId;
    }

    public Long getRelationId() 
    {
        return relationId;
    }
    public void setRelationType(String relationType) 
    {
        this.relationType = relationType;
    }

    public String getRelationType() 
    {
        return relationType;
    }
    public void setOldJsonDate(String oldJsonDate) 
    {
        this.oldJsonDate = oldJsonDate;
    }

    public String getOldJsonDate() 
    {
        return oldJsonDate;
    }
    public void setNewJsonDate(String newJsonDate) 
    {
        this.newJsonDate = newJsonDate;
    }

    public String getNewJsonDate() 
    {
        return newJsonDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("relationId", getRelationId())
            .append("relationType", getRelationType())
            .append("oldJsonDate", getOldJsonDate())
            .append("newJsonDate", getNewJsonDate())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}
