package org.ruoyi.core.qrReport.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.qrReport.domain.QrCodeAuditReportHistory;
import org.ruoyi.core.qrReport.mapper.QrCodeAuditReportHistoryMapper;
import org.ruoyi.core.qrReport.service.IQrCodeAuditReportHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 审计报告对比Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
@Service
public class QrCodeAuditReportHistoryServiceImpl implements IQrCodeAuditReportHistoryService
{
    @Autowired
    private QrCodeAuditReportHistoryMapper qrCodeAuditReportHistoryMapper;

    /**
     * 查询审计报告对比
     * 
     * @param id 审计报告对比主键
     * @return 审计报告对比
     */
    @Override
    public QrCodeAuditReportHistory selectQrCodeAuditReportHistoryById(Long id)
    {
        return qrCodeAuditReportHistoryMapper.selectQrCodeAuditReportHistoryById(id);
    }

    /**
     * 查询审计报告对比列表
     * 
     * @param qrCodeAuditReportHistory 审计报告对比
     * @return 审计报告对比
     */
    @Override
    public List<QrCodeAuditReportHistory> selectQrCodeAuditReportHistoryList(QrCodeAuditReportHistory qrCodeAuditReportHistory)
    {
        return qrCodeAuditReportHistoryMapper.selectQrCodeAuditReportHistoryList(qrCodeAuditReportHistory);
    }

    /**
     * 新增审计报告对比
     * 
     * @param qrCodeAuditReportHistory 审计报告对比
     * @return 结果
     */
    @Override
    public int insertQrCodeAuditReportHistory(QrCodeAuditReportHistory qrCodeAuditReportHistory)
    {
        qrCodeAuditReportHistory.setCreateTime(DateUtils.getNowDate());
        return qrCodeAuditReportHistoryMapper.insertQrCodeAuditReportHistory(qrCodeAuditReportHistory);
    }

    /**
     * 修改审计报告对比
     * 
     * @param qrCodeAuditReportHistory 审计报告对比
     * @return 结果
     */
    @Override
    public int updateQrCodeAuditReportHistory(QrCodeAuditReportHistory qrCodeAuditReportHistory)
    {
        return qrCodeAuditReportHistoryMapper.updateQrCodeAuditReportHistory(qrCodeAuditReportHistory);
    }

    /**
     * 批量删除审计报告对比
     * 
     * @param ids 需要删除的审计报告对比主键
     * @return 结果
     */
    @Override
    public int deleteQrCodeAuditReportHistoryByIds(Long[] ids)
    {
        return qrCodeAuditReportHistoryMapper.deleteQrCodeAuditReportHistoryByIds(ids);
    }

    /**
     * 删除审计报告对比信息
     * 
     * @param id 审计报告对比主键
     * @return 结果
     */
    @Override
    public int deleteQrCodeAuditReportHistoryById(Long id)
    {
        return qrCodeAuditReportHistoryMapper.deleteQrCodeAuditReportHistoryById(id);
    }
}
