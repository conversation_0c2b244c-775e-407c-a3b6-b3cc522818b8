package org.ruoyi.core.kaoqin.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.ruoyi.core.kaoqin.domain.vo.DayLogListVo;
import org.ruoyi.core.kaoqin.service.IKqCalendarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.kaoqin.mapper.KqCalendarMapper;
import org.ruoyi.core.kaoqin.domain.KqCalendar;import org.springframework.transaction.annotation.Transactional;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 考勤日历Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@Service
public class KqCalendarServiceImpl implements IKqCalendarService
{
    @Autowired
    private KqCalendarMapper kqCalendarMapper;

    /**
     * 查询考勤日历
     *
     * @param id 考勤日历主键
     * @return 考勤日历
     */
    @Override
    public KqCalendar selectKqCalendarById(Long id)
    {
        return kqCalendarMapper.selectKqCalendarById(id);
    }

    /**
     * 查询考勤日历列表
     *
     * @param kqCalendar 考勤日历
     * @return 考勤日历
     */
    @Override
    @Transactional
    public List<KqCalendar> selectKqCalendarList(KqCalendar kqCalendar)
    {
        if(kqCalendar.getYear() == null) {
            throw new ServiceException("查询年份不能为空");
        }
        List<KqCalendar> kqCalendars = kqCalendarMapper.selectKqCalendarList(kqCalendar);
        if (kqCalendars.isEmpty()) {
            JSONObject holidayJSON = getHolidayJSON(kqCalendar.getYear());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            JSONObject holidayObject = holidayJSON.getJSONObject("holiday");
            if (!holidayObject.isEmpty()){
            kqCalendarMapper.insertKqCalendars(kqCalendar);

                for (String key : holidayObject.keySet()) {
                    JSONObject obj = holidayObject.getJSONObject(key);
                   // String name = obj.getString("name");
                    boolean isholiday = obj.getBooleanValue("holiday");
                    String date = obj.getString("date");
                    KqCalendar newHoliday = new KqCalendar();
                    //newHoliday.setDateexplain(name);
                    newHoliday.setHoliday(isholiday ? "1" : "2");
                    newHoliday.setCreateTime(DateUtils.getNowDate());
                    newHoliday.setCreateBy("admin");
                    try {
                        Date dateHoliday = sdf.parse(date);
                        newHoliday.setDate(dateHoliday);
                    } catch (ParseException e){
                        throw new RuntimeException("日期类型转换失败" );
                    }
                    kqCalendarMapper.updateKqCalendarByDate(newHoliday);
                }
                kqCalendars = kqCalendarMapper.selectKqCalendarList(kqCalendar);
                return kqCalendars;
            } else {
                throw new ServiceException(kqCalendar.getYear() + "年度法定节假日还未生成");
            }
        }
        return kqCalendars;
    }
    @Override
    public Long selectKqCalendarCount(KqCalendar kqCalendar){
        return kqCalendarMapper.selectKqCalendarCount(kqCalendar);
    }

    @Override
    public List<DayLogListVo> selectKqCalendarListForMonth(DayLogListVo dayLogListVo){
        return kqCalendarMapper.selectKqCalendarListForMonth(dayLogListVo);
    }

    @Override
    public List<DayLogListVo> selectKqCalendarListForMonthDesc(DayLogListVo dayLogListVo){
        return kqCalendarMapper.selectKqCalendarListForMonthDesc(dayLogListVo);
    }

    /**
     * 新增考勤日历
     *
     * @param kqCalendar 考勤日历
     * @return 结果
     */
    @Override
    public int insertKqCalendar(KqCalendar kqCalendar)
    {
        kqCalendar.setCreateTime(DateUtils.getNowDate());
        return kqCalendarMapper.insertKqCalendar(kqCalendar);
    }

    /**
     * 修改考勤日历
     *
     * @param kqCalendar 考勤日历
     * @return 结果
     */
    @Override
    public int updateKqCalendar(KqCalendar kqCalendar)
    {
        kqCalendar.setUpdateTime(DateUtils.getNowDate());
        return kqCalendarMapper.updateKqCalendar(kqCalendar);
    }

    @Override
    public int insertKqCalendars(KqCalendar kqCalendar){
        return kqCalendarMapper.insertKqCalendars(kqCalendar);
    }

    /**
     * 批量删除考勤日历
     *
     * @param ids 需要删除的考勤日历主键
     * @return 结果
     */
    @Override
    public int deleteKqCalendarByIds(Long[] ids)
    {
        return kqCalendarMapper.deleteKqCalendarByIds(ids);
    }

    /**
     * 删除考勤日历信息
     *
     * @param id 考勤日历主键
     * @return 结果
     */
    @Override
    public int deleteKqCalendarById(Long id)
    {
        return kqCalendarMapper.deleteKqCalendarById(id);
    }

    public int replaceKqCalendarBatch(List<KqCalendar> kqCalendarList){
        if(kqCalendarList.isEmpty()){
            throw new ServiceException("提交数据未发生改变");
        }
        kqCalendarList.forEach(vo ->{
            vo.setUpdateBy(getUsername());
            vo.setUpdateTime(new Date());
        });
        return kqCalendarMapper.replaceKqCalendarBatch(kqCalendarList);
    }

    /**
     * 通过API获取法定节假日
     * @param year
     * @return
     */
    public JSONObject getHolidayJSON(String year) {
        HttpClient client = HttpClients.createDefault();
        // 要调用的接口方法
        String url = "https://timor.tech/api/holiday/year/" + year +"/";
        HttpGet httpGet=new HttpGet(url);
        JSONObject jsonObject = null;
        try {
            HttpResponse res = client.execute(httpGet);
            if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                // 返回json格式：
                jsonObject = JSONObject.parseObject(EntityUtils.toString(res.getEntity()));
            }
        } catch (Exception e) {
            System.out.println("服务间接口调用出错！");
            e.printStackTrace();
        }
        return jsonObject;
    }
}
