package org.ruoyi.core.kaoqin.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import org.ruoyi.core.kaoqin.domain.DayLog;
import org.ruoyi.core.kaoqin.domain.vo.DayLogExport;
import org.ruoyi.core.kaoqin.domain.vo.DayLogListVo;
import org.ruoyi.core.kaoqin.domain.vo.LogVo;

import java.util.List;

/**
 * 日志填报Service接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
public interface IDayLogService
{
    /**
     * 查询日志填报
     *
     * @param id 日志填报主键
     * @return 日志填报
     */
    public DayLog selectDayLogById(Long id);

    /**
     * 查询日志填报列表
     *
     * @param dayLog 日志填报
     * @return 日志填报集合
     */
    public List<DayLog> selectDayLogList(DayLog dayLog);

    /**
     * 新增日志填报
     *
     * @param dayLog 日志填报
     * @return 结果
     */
    public int insertDayLog(DayLog dayLog);

    /**
     * 修改日志填报
     *
     * @param dayLog 日志填报
     * @return 结果
     */
    public int updateDayLog(DayLog dayLog);

    /**
     * 批量删除日志填报
     *
     * @param ids 需要删除的日志填报主键集合
     * @return 结果
     */
    public int deleteDayLogByIds(Long[] ids);

    /**
     * 删除日志填报信息
     *
     * @param id 日志填报主键
     * @return 结果
     */
    public int deleteDayLogById(Long id);

    public int saveDayLogList(List<DayLog> dayLogList);

    public int commintDayLogList(List<DayLog> dayLogList);

    public LogVo getDayLogList(DayLogListVo dayLog);

    public List<DayLogListVo> getDayLogListOther(DayLogListVo dayLog);

    public List<DayLogExport> getDayLogListOtherExport(DayLogListVo dayLog);

    public List<DayLogListVo> getDayLogListOfLeader(DayLogListVo dayLog);

    public List<SysUser> selectUserList(SysUser user);
}
