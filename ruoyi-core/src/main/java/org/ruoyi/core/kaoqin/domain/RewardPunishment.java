package org.ruoyi.core.kaoqin.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 奖惩对象 kq_reward_punishment
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Data
public class RewardPunishment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    //@Excel(name = "用户id")
    private Long userId;

    /** 奖惩编码 */
    @Excel(name = "奖惩编码",sort = 2)
    private String rewardPunishmentCode;

    /** 奖惩类型 1 奖励 2 惩罚 */
    @Excel(name = "奖惩类型", readConverterExp = "1=奖励,2=惩罚",sort = 4)
    private String type;

    /** 奖惩措施 1 金额 2 物品 */
    //@Excel(name = "奖惩措施 1 金额 2 物品")
    private String measure;

    /** 金额 */
    //@Excel(name = "金额")
    private BigDecimal amount;

    /** 物品名称 */
    //@Excel(name = "物品名称")
    private String itemName;

    /** 物品数量 */
    //@Excel(name = "物品数量")
    private Long itemNum;

    /** 奖惩是由 */
    @Excel(name = "奖惩是由",sort = 6)
    private String reason;

    /** 状态 1 待审批 2 审批中 3 审批通过 4 废弃 */
    @Excel(name = "审批状态" , readConverterExp = "1=待审批,2=审批中,3=审批通过,4=审核不通过",sort = 9)
    private String status;

    /** 生效状态 0 生效  1 废弃 */
    //@Excel(name = "生效状态 0 生效  1 废弃 2 处理中")
    private String effective;

    /** 审批完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审批完成时间", width = 30, dateFormat = "yyyy-MM-dd",sort = 10)
    private Date auditCompletionTime;

    /** 删除标志（0代表存在 1代表删除） */
    //@Excel(name = "删除标志", readConverterExp = "0=代表存在,1=代表删除")
    private String isDelete;

    private String processId;

    /** 废弃原因 */
    //@Excel(name = "废弃原因")
    private String voidReason;

    /** 废弃时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    //@Excel(name = "废弃时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date voidTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间",sort =  8, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
