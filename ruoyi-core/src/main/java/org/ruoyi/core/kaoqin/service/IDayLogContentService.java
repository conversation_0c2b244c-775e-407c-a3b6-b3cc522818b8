package org.ruoyi.core.kaoqin.service;

import org.ruoyi.core.kaoqin.domain.DayLogContent;

import java.util.List;

/**
 * 用户日报-内容Service接口
 *
 * <AUTHOR>
 * @date 2025-02-18
 */
public interface IDayLogContentService
{
    /**
     * 查询用户日报-内容
     *
     * @param id 用户日报-内容主键
     * @return 用户日报-内容
     */
    public DayLogContent selectDayLogContentById(Long id);

    /**
     * 查询用户日报-内容列表
     *
     * @param dayLogContent 用户日报-内容
     * @return 用户日报-内容集合
     */
    public List<DayLogContent> selectDayLogContentList(DayLogContent dayLogContent);

    /**
     * 新增用户日报-内容
     *
     * @param dayLogContent 用户日报-内容
     * @return 结果
     */
    public int insertDayLogContent(DayLogContent dayLogContent);

    /**
     * 修改用户日报-内容
     *
     * @param dayLogContent 用户日报-内容
     * @return 结果
     */
    public int updateDayLogContent(DayLogContent dayLogContent);

    /**
     * 批量删除用户日报-内容
     *
     * @param ids 需要删除的用户日报-内容主键集合
     * @return 结果
     */
    public int deleteDayLogContentByIds(Long[] ids);

    /**
     * 删除用户日报-内容信息
     *
     * @param id 用户日报-内容主键
     * @return 结果
     */
    public int deleteDayLogContentById(Long id);

    /**
     * 批量新增用户日报-内容
     * @param dayLogContent 日报内容集合
     * @return 结果
     */
    public int insertdayLogContentBatch(List<DayLogContent> dayLogContent);

    /**
     * 根据主表id删除用户日报-内容
     *
     * @param mainId 日报主键
     * @return 结果
     */
    public int deleteDayLogContentByMainId(Long mainId);
}
