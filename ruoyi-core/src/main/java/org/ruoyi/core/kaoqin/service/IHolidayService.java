package org.ruoyi.core.kaoqin.service;

import org.ruoyi.core.kaoqin.domain.Holiday;

import java.util.List;

/**
 * 法定节假日Service接口
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
public interface IHolidayService
{
    /**
     * 查询法定节假日
     *
     * @param id 法定节假日主键
     * @return 法定节假日
     */
    public Holiday selectHolidayById(String id);

    /**
     * 查询法定节假日列表
     *
     * @param holiday 法定节假日
     * @return 法定节假日集合
     */
    public List<Holiday> selectHolidayList(Holiday holiday);

    /**
     * 新增法定节假日
     *
     * @param holiday 法定节假日
     * @return 结果
     */
    public int insertHoliday(Holiday holiday);

    /**
     * 修改法定节假日
     *
     * @param holiday 法定节假日
     * @return 结果
     */
    public int updateHoliday(Holiday holiday);

    /**
     * 批量删除法定节假日
     *
     * @param ids 需要删除的法定节假日主键集合
     * @return 结果
     */
    public int deleteHolidayByIds(String[] ids);

    /**
     * 删除法定节假日信息
     *
     * @param id 法定节假日主键
     * @return 结果
     */
    public int deleteHolidayById(String id);

    public List<Holiday> getHolidayList();
}
