package org.ruoyi.core.kaoqin.service.impl;

import java.util.List;

import org.ruoyi.core.kaoqin.domain.DayLogContent;
import org.ruoyi.core.kaoqin.mapper.DayLogContentMapper;
import org.ruoyi.core.kaoqin.service.IDayLogContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
/**
 * 用户日报-内容Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-18
 */
@Service
public class DayLogContentServiceImpl implements IDayLogContentService
{
    @Autowired
    private DayLogContentMapper dayLogContentMapper;

    /**
     * 查询用户日报-内容
     *
     * @param id 用户日报-内容主键
     * @return 用户日报-内容
     */
    @Override
    public DayLogContent selectDayLogContentById(Long id)
    {
        return dayLogContentMapper.selectDayLogContentById(id);
    }

    /**
     * 查询用户日报-内容列表
     *
     * @param dayLogContent 用户日报-内容
     * @return 用户日报-内容
     */
    @Override
    public List<DayLogContent> selectDayLogContentList(DayLogContent dayLogContent)
    {
        return dayLogContentMapper.selectDayLogContentList(dayLogContent);
    }

    /**
     * 新增用户日报-内容
     *
     * @param dayLogContent 用户日报-内容
     * @return 结果
     */
    @Override
    public int insertDayLogContent(DayLogContent dayLogContent)
    {
        return dayLogContentMapper.insertDayLogContent(dayLogContent);
    }

    /**
     * 修改用户日报-内容
     *
     * @param dayLogContent 用户日报-内容
     * @return 结果
     */
    @Override
    public int updateDayLogContent(DayLogContent dayLogContent)
    {
        return dayLogContentMapper.updateDayLogContent(dayLogContent);
    }

    /**
     * 批量删除用户日报-内容
     *
     * @param ids 需要删除的用户日报-内容主键
     * @return 结果
     */
    @Override
    public int deleteDayLogContentByIds(Long[] ids)
    {
        return dayLogContentMapper.deleteDayLogContentByIds(ids);
    }

    /**
     * 删除用户日报-内容信息
     *
     * @param id 用户日报-内容主键
     * @return 结果
     */
    @Override
    public int deleteDayLogContentById(Long id)
    {
        return dayLogContentMapper.deleteDayLogContentById(id);
    }

    /**
     * 批量新增用户日报-内容
     *
     * @param dayLogContent 日报内容集合
     * @return 结果
     */
    @Override
    public int insertdayLogContentBatch(List<DayLogContent> dayLogContent) {
        return dayLogContentMapper.insertdayLogContentBatch(dayLogContent);
    }

    /**
     * 根据主表id删除用户日报-内容
     *
     * @param mainId 日报主键
     * @return 结果
     */
    @Override
    public int deleteDayLogContentByMainId(Long mainId){
        return dayLogContentMapper.deleteDayLogContentByMainId(mainId);
    }
}
