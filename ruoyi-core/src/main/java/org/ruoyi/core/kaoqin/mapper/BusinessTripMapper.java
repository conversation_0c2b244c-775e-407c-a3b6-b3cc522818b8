package org.ruoyi.core.kaoqin.mapper;

import org.ruoyi.core.kaoqin.domain.BusinessTrip;
import org.ruoyi.core.kaoqin.domain.vo.BusinessTripVo;

import java.util.List;

/**
 * 出差申请Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-28
 */
public interface BusinessTripMapper
{
    /**
     * 查询出差申请
     *
     * @param id 出差申请主键
     * @return 出差申请
     */
    public BusinessTripVo selectBusinessTripById(Long id);

    /**
     * 查询出差申请列表
     *
     * @param businessTrip 出差申请
     * @return 出差申请集合
     */
    public List<BusinessTripVo> selectBusinessTripList(BusinessTripVo businessTrip);

    public List<BusinessTripVo> selectBusinessTripListExport(BusinessTripVo businessTrip);
    /**
     * 新增出差申请
     *
     * @param businessTrip 出差申请
     * @return 结果
     */
    public int insertBusinessTrip(BusinessTrip businessTrip);

    /**
     * 修改出差申请
     *
     * @param businessTrip 出差申请
     * @return 结果
     */
    public int updateBusinessTrip(BusinessTrip businessTrip);

    /**
     * 删除出差申请
     *
     * @param id 出差申请主键
     * @return 结果
     */
    public int deleteBusinessTripById(Long id);

    /**
     * 批量删除出差申请
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBusinessTripByIds(Long[] ids);

    int getCountByCreateTime(String createTime);

    public int voidBusinessTrip(BusinessTrip businessTrip);

    public BusinessTripVo selectBusinessTripByHandleId(Long id);
}
