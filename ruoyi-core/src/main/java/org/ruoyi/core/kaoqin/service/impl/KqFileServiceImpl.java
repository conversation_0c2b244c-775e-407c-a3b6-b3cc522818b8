package org.ruoyi.core.kaoqin.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.kaoqin.domain.KqFile;
import org.ruoyi.core.kaoqin.mapper.KqFileMapper;
import org.ruoyi.core.kaoqin.service.IKqFileService;
import org.ruoyi.core.personnel.domain.vo.PersonnelFileVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 考勤管理-文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Service
public class KqFileServiceImpl implements IKqFileService
{
    @Autowired
    private KqFileMapper kqFileMapper;

    /**
     * 查询考勤管理-文件
     *
     * @param id 考勤管理-文件主键
     * @return 考勤管理-文件
     */
    @Override
    public KqFile selectKqFileById(Long id)
    {
        return kqFileMapper.selectKqFileById(id);
    }

    /**
     * 查询考勤管理-文件列表
     *
     * @param kqFile 考勤管理-文件
     * @return 考勤管理-文件
     */
    @Override
    public List<KqFile> selectKqFileList(KqFile kqFile)
    {
        return kqFileMapper.selectKqFileList(kqFile);
    }

    /**
     * 新增考勤管理-文件
     *
     * @param kqFile 考勤管理-文件
     * @return 结果
     */
    @Override
    public int insertKqFile(KqFile kqFile)
    {
        kqFile.setCreateTime(DateUtils.getNowDate());
        return kqFileMapper.insertKqFile(kqFile);
    }

    /**
     * 修改考勤管理-文件
     *
     * @param kqFile 考勤管理-文件
     * @return 结果
     */
    @Override
    public int updateKqFile(KqFile kqFile)
    {
        kqFile.setUpdateTime(DateUtils.getNowDate());
        return kqFileMapper.updateKqFile(kqFile);
    }

    /**
     * 批量删除考勤管理-文件
     *
     * @param ids 需要删除的考勤管理-文件主键
     * @return 结果
     */
    @Override
    public int deleteKqFileByIds(Long[] ids)
    {
        return kqFileMapper.deleteKqFileByIds(ids);
    }

    /**
     * 删除考勤管理-文件信息
     *
     * @param id 考勤管理-文件主键
     * @return 结果
     */
    @Override
    public int deleteKqFileById(Long id)
    {
        return kqFileMapper.deleteKqFileById(id);
    }

    @Override
    public int insertKqFiles(List<KqFile> files) {
        return kqFileMapper.insertKqFiles(files);
    }

    @Override
    public int deleteByCorrelationId(KqFile file) {
        return kqFileMapper.deleteByCorrelationId(file);
    }

    @Override
    public int correlationFile(KqFile file){
        return kqFileMapper.correlationFile(file);
    }
}
