package org.ruoyi.core.kaoqin.mapper;

import org.ruoyi.core.kaoqin.domain.DayLogGroup;

import java.util.List;

/**
 * 日志查询-快速分组列Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface DayLogGroupMapper
{
    /**
     * 查询日志查询-快速分组列
     *
     * @param id 日志查询-快速分组列主键
     * @return 日志查询-快速分组列
     */
    public DayLogGroup selectDayLogGroupById(Long id);

    /**
     * 查询日志查询-快速分组列列表
     *
     * @param dayLogGroup 日志查询-快速分组列
     * @return 日志查询-快速分组列集合
     */
    public List<DayLogGroup> selectDayLogGroupList(DayLogGroup dayLogGroup);

    public List<DayLogGroup> getDayLogGroupList(DayLogGroup dayLogGroup);

    /**
     * 新增日志查询-快速分组列
     *
     * @param dayLogGroup 日志查询-快速分组列
     * @return 结果
     */
    public int insertDayLogGroup(DayLogGroup dayLogGroup);

    /**
     * 修改日志查询-快速分组列
     *
     * @param dayLogGroup 日志查询-快速分组列
     * @return 结果
     */
    public int updateDayLogGroup(DayLogGroup dayLogGroup);

    /**
     * 删除日志查询-快速分组列
     *
     * @param id 日志查询-快速分组列主键
     * @return 结果
     */
    public int deleteDayLogGroupById(Long id);

    /**
     * 批量删除日志查询-快速分组列
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDayLogGroupByIds(Long[] ids);
}
