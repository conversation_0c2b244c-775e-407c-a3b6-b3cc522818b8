package org.ruoyi.core.kaoqin.mapper;

import org.ruoyi.core.kaoqin.domain.RewardPunishment;
import org.ruoyi.core.kaoqin.domain.vo.RewardPunishmentVo;

import java.util.List;

/**
 * 奖惩Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface RewardPunishmentMapper
{
    /**
     * 查询奖惩
     *
     * @param id 奖惩主键
     * @return 奖惩
     */
    public RewardPunishmentVo selectRewardPunishmentById(Long id);

    /**
     * 查询奖惩列表
     *
     * @param rewardPunishment 奖惩
     * @return 奖惩集合
     */
    public List<RewardPunishmentVo> selectRewardPunishmentList(RewardPunishmentVo rewardPunishment);

    /**
     * 新增奖惩
     *
     * @param rewardPunishment 奖惩
     * @return 结果
     */
    public int insertRewardPunishment(RewardPunishment rewardPunishment);

    /**
     * 修改奖惩
     *
     * @param rewardPunishment 奖惩
     * @return 结果
     */
    public int updateRewardPunishment(RewardPunishment rewardPunishment);

    /**
     * 删除奖惩
     *
     * @param id 奖惩主键
     * @return 结果
     */
    public int deleteRewardPunishmentById(Long id);

    /**
     * 批量删除奖惩
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRewardPunishmentByIds(Long[] ids);

    int getCountByCreateTimeOfReward(String createTime);

    int getCountByCreateTimeOfPunishment(String createTime);

    int voidRewardPunishment(RewardPunishment rewardPunishment);

    public int processRewardPunishment(RewardPunishmentVo rewardPunishment);

    public int passRewardPunishment(RewardPunishmentVo rewardPunishment);

    public int unpassRewardPunishment(RewardPunishment rewardPunishment);

    public RewardPunishmentVo selectRewardPunishmentByHandleId(Long id);

}
