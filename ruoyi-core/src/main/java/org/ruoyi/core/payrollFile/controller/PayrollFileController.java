package org.ruoyi.core.payrollFile.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;
import org.ruoyi.core.payrollFile.domain.PayrollExcel;
import org.ruoyi.core.payrollFile.domain.PayrollFile;
import org.ruoyi.core.payrollFile.domain.PayrollProcess;
import org.ruoyi.core.payrollFile.service.IPayrollFileService;
import org.ruoyi.core.personnel.domain.PersonnelArchives;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;

/**
 * 薪资档案Controller
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@RestController
@RequestMapping("/payrollFile/payrollFile")
public class PayrollFileController extends BaseController
{
    @Autowired
    private IPayrollFileService payrollFileService;

    /**
     * 查询薪资档案列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PayrollFile payrollFile)
    {
        startPage();
        List<PayrollFile> list = payrollFileService.selectPayrollFileList(payrollFile);
        return getDataTable(list);
    }

    /**
     * 导出薪资档案列表
     */
    @Log(title = "薪资档案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PayrollFile payrollFile)
    {
        List<PayrollFile> list = payrollFileService.selectPayrollFileList(payrollFile);
        ExcelUtil<PayrollFile> util = new ExcelUtil<PayrollFile>(PayrollFile.class);
        util.exportExcel(response, list, "薪资档案数据");
    }

    /**
     * 获取薪资档案详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(payrollFileService.selectPayrollFileById(id));
    }

    /**
     * 薪资档案关联审批流程
     */
    @PostMapping("/initiateProcess")
    public AjaxResult initiateProcess(@RequestBody PayrollProcess payrollProcess)
    {
        return toAjax(payrollFileService.initiateProcess(payrollProcess));
    }

    /**
     * 通过新建立的子表id修改状态，并重新关联主表单据(用于更新薪资档案子表)
     */
    @GetMapping(value = "/setBillData")
    public AjaxResult setBillData(PayrollProcess pay){
        return AjaxResult.success(payrollFileService.setBillData(pay));
    }

    /**
     * 通过提取流程信息，展示提醒内容
     */
    @GetMapping(value = "/getDataToFlow/{flowId}")
    public AjaxResult getDataToFlow(@PathVariable("flowId") String flowId)
    {
        return AjaxResult.success(payrollFileService.getDataToFlow(flowId));
    }

    /**
     * 获取薪资调整记录
     */
    @GetMapping(value = "/tx/{id}")
    public AjaxResult getSalaryAdjustment(@PathVariable("id") String id)
    {
        return AjaxResult.success(payrollFileService.getSalaryAdjustment(id));
    }

    /**
     * 新增薪资档案
     */
    @Log(title = "薪资档案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PayrollFile payrollFile)
    {
        return toAjax(payrollFileService.insertPayrollFile(payrollFile));
    }

    /**
     * 批量新增薪资档案
     */
    @PostMapping("insert")
    public AjaxResult adds(@RequestBody List<PayrollFile> payrollFile)
    {
        return toAjax(payrollFileService.insertPayrollFiles(payrollFile));
    }

    /**
     * 修改薪资档案
     */
    @Log(title = "薪资档案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PayrollFile payrollFile)
    {
        return toAjax(payrollFileService.updatePayrollFile(payrollFile));
    }

    /**
     * 删除薪资档案
     */
    @Log(title = "薪资档案", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return AjaxResult.success(payrollFileService.deletePayrollFileByIds(ids));
    }

    /**
     * 导入薪资档案
     * @param file 导入文件
     * @throws Exception
     */
    @Log(title = "导入薪资档案", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<PayrollFile> util = new ExcelUtil<PayrollFile>(PayrollFile.class);
        List<PayrollFile> payrollFileList = util.importExcel(file.getInputStream());
        String message = payrollFileService.importData(payrollFileList);
        return AjaxResult.success(message);
    }

    /**
     * 下载薪资档案模板
     */
    @PostMapping("/singleOwn")
    public void getAutoList(HttpServletResponse response) throws Exception {
        ExcelUtil<PayrollExcel> util = new ExcelUtil<PayrollExcel>(PayrollExcel.class);
        util.importTemplateExcel(response, "薪资档案导入模板");
    }

    /**
     * 获取薪资档案流程
     * @param oaProcessTemplate 流程模板信息
     * @return 薪资档案流程
     */
    @Log(title = "薪资流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getInformationUsedFlow")
    public AjaxResult getPayrollFileFlow(OaProcessTemplate oaProcessTemplate) {
        return payrollFileService.getPayrollFileFlow(oaProcessTemplate);
    }

    /**
     * 获取薪资档案详细信息
     */
    @GetMapping(value = "/checkPayrollFileByIdCard/{idCard}")
    public AjaxResult checkPayrollFileByIdCard(@PathVariable("idCard") String idCard)
    {
        return AjaxResult.success(payrollFileService.checkPayrollFileByIdCard(idCard));
    }
}
