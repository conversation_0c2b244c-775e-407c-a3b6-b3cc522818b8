package org.ruoyi.core.lyx.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 lyx_day_vouchar
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
public class LyxDayVouchar extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 收款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date collectionDate;

    /** 凭证生成日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "凭证生成日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date proofCreateTime;

    /** 制单人 */
    @Excel(name = "制单人")
    private String documenter;

    /** 摘要 */
    @Excel(name = "摘要")
    private String dayAbstract;

    /** 应收账款中信大厦 */
    @Excel(name = "应收账款中信大厦")
    private BigDecimal yszkZhongxin;

    /** 现金 */
    @Excel(name = "现金")
    private BigDecimal cash;

    /** 银行存款-史总卡到账 */
    @Excel(name = "银行存款-史总卡到账")
    private BigDecimal yhckSzkdz;

    /** 银行存款-民生银行POS */
    @Excel(name = "银行存款-民生银行POS")
    private BigDecimal yhckMsyhpos;

    /** 财务费用-手续费 */
    @Excel(name = "财务费用-手续费")
    private BigDecimal cwfySxf;

    /** 借方合计 */
    @Excel(name = "借方合计")
    private BigDecimal borrowTotal;

    /** 饭卡收入 */
    @Excel(name = "饭卡收入")
    private BigDecimal fankaTotal;

    /** 餐费收入 */
    @Excel(name = "餐费收入")
    private BigDecimal canfeiTotal;

    /** 贷方合计 */
    @Excel(name = "贷方合计")
    private BigDecimal loanTotal;

    /** 每日台账表id */
    @Excel(name = "每日台账表id")
    private Long dayCheckId;

    private Long voucherId;

    private String selectMonth;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setProofCreateTime(Date proofCreateTime) 
    {
        this.proofCreateTime = proofCreateTime;
    }

    public Date getProofCreateTime() 
    {
        return proofCreateTime;
    }
    public void setDocumenter(String documenter) 
    {
        this.documenter = documenter;
    }

    public String getDocumenter() 
    {
        return documenter;
    }

    public void setYszkZhongxin(BigDecimal yszkZhongxin) 
    {
        this.yszkZhongxin = yszkZhongxin;
    }

    public BigDecimal getYszkZhongxin() 
    {
        return yszkZhongxin;
    }
    public void setCash(BigDecimal cash) 
    {
        this.cash = cash;
    }

    public BigDecimal getCash() 
    {
        return cash;
    }
    public void setYhckSzkdz(BigDecimal yhckSzkdz) 
    {
        this.yhckSzkdz = yhckSzkdz;
    }

    public BigDecimal getYhckSzkdz() 
    {
        return yhckSzkdz;
    }
    public void setYhckMsyhpos(BigDecimal yhckMsyhpos) 
    {
        this.yhckMsyhpos = yhckMsyhpos;
    }

    public BigDecimal getYhckMsyhpos() 
    {
        return yhckMsyhpos;
    }
    public void setCwfySxf(BigDecimal cwfySxf) 
    {
        this.cwfySxf = cwfySxf;
    }

    public BigDecimal getCwfySxf() 
    {
        return cwfySxf;
    }
    public void setBorrowTotal(BigDecimal borrowTotal) 
    {
        this.borrowTotal = borrowTotal;
    }

    public BigDecimal getBorrowTotal() 
    {
        return borrowTotal;
    }
    public void setFankaTotal(BigDecimal fankaTotal) 
    {
        this.fankaTotal = fankaTotal;
    }

    public BigDecimal getFankaTotal() 
    {
        return fankaTotal;
    }
    public void setCanfeiTotal(BigDecimal canfeiTotal) 
    {
        this.canfeiTotal = canfeiTotal;
    }

    public BigDecimal getCanfeiTotal() 
    {
        return canfeiTotal;
    }
    public void setLoanTotal(BigDecimal loanTotal) 
    {
        this.loanTotal = loanTotal;
    }

    public BigDecimal getLoanTotal() 
    {
        return loanTotal;
    }
    public void setDayCheckId(Long dayCheckId) 
    {
        this.dayCheckId = dayCheckId;
    }

    public Long getDayCheckId() 
    {
        return dayCheckId;
    }

    public Date getCollectionDate() {
        return collectionDate;
    }

    public void setCollectionDate(Date collectionDate) {
        this.collectionDate = collectionDate;
    }

    public String getDayAbstract() {
        return dayAbstract;
    }

    public void setDayAbstract(String dayAbstract) {
        this.dayAbstract = dayAbstract;
    }

    public Long getVoucherId() {
        return voucherId;
    }

    public void setVoucherId(Long voucherId) {
        this.voucherId = voucherId;
    }

    public String getSelectMonth() {
        return selectMonth;
    }

    public void setSelectMonth(String selectMonth) {
        this.selectMonth = selectMonth;
    }

    @Override
    public String toString() {
        return "LyxDayVouchar{" +
                "id=" + id +
                ", collectionDate=" + collectionDate +
                ", proofCreateTime=" + proofCreateTime +
                ", documenter='" + documenter + '\'' +
                ", dayAbstract='" + dayAbstract + '\'' +
                ", yszkZhongxin=" + yszkZhongxin +
                ", cash=" + cash +
                ", yhckSzkdz=" + yhckSzkdz +
                ", yhckMsyhpos=" + yhckMsyhpos +
                ", cwfySxf=" + cwfySxf +
                ", borrowTotal=" + borrowTotal +
                ", fankaTotal=" + fankaTotal +
                ", canfeiTotal=" + canfeiTotal +
                ", loanTotal=" + loanTotal +
                ", dayCheckId=" + dayCheckId +
                ", voucherId=" + voucherId +
                ", selectMonth='" + selectMonth + '\'' +
                '}';
    }
}
