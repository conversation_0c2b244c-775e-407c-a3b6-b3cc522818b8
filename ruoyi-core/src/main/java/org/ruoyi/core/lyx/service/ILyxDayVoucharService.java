package org.ruoyi.core.lyx.service;

import org.ruoyi.core.lyx.domain.DayVoucharVo;
import org.ruoyi.core.lyx.domain.LyxDayCheck;
import org.ruoyi.core.lyx.domain.LyxDayVouchar;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ILyxDayVoucharService.java
 * @Description TODO
 * @createTime 2024年03月14日 15:57:00
 */
public interface ILyxDayVoucharService {

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public LyxDayVouchar selectLyxDayVoucharById(Long id);
    /**
     * 查询【请填写功能名称】列表
     *
     * @param lyxDayVouchar 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<LyxDayVouchar> selectLyxDayVoucharList(LyxDayVouchar lyxDayVouchar);
    /**
     * 新增【请填写功能名称】
     *
     * @param lyxDayVouchar 【请填写功能名称】
     * @return 结果
     */
    public int insertLyxDayVouchar(LyxDayVouchar lyxDayVouchar);
    /**
     * 修改【请填写功能名称】
     *
     * @param lyxDayVouchar 【请填写功能名称】
     * @return 结果
     */
    public int updateLyxDayVouchar(LyxDayVouchar lyxDayVouchar);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxDayVoucharByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxDayVoucharById(Long id);

    Map<String, Object> createDayVouchar(DayVoucharVo dayVoucharVo);

    Map<String, Object> queryDayVoucharData(LyxDayVouchar lyxDayVouchar);
}
