package org.ruoyi.core.lyx.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.lyx.domain.LyxMeituanWithdraw;
import org.ruoyi.core.lyx.service.ILyxMeituanWithdrawService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2024-03-13
 */
@RestController
@RequestMapping("/lyxSystem/meituanWithdraw")
public class LyxMeituanWithdrawController extends BaseController
{
    @Autowired
    private ILyxMeituanWithdrawService lyxMeituanWithdrawService;

    /**
     * 查询【请填写功能名称】列表
     */
//    @PreAuthorize("@ss.hasPermi('system:withdraw:list')")
    @GetMapping("/list")
    public TableDataInfo list(LyxMeituanWithdraw lyxMeituanWithdraw)
    {
        startPage();
        List<LyxMeituanWithdraw> list = lyxMeituanWithdrawService.selectLyxMeituanWithdrawList(lyxMeituanWithdraw);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
//    @PreAuthorize("@ss.hasPermi('system:withdraw:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LyxMeituanWithdraw lyxMeituanWithdraw)
    {
        List<LyxMeituanWithdraw> list = lyxMeituanWithdrawService.selectLyxMeituanWithdrawList(lyxMeituanWithdraw);
        ExcelUtil<LyxMeituanWithdraw> util = new ExcelUtil<LyxMeituanWithdraw>(LyxMeituanWithdraw.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:withdraw:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(lyxMeituanWithdrawService.selectLyxMeituanWithdrawById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
//    @PreAuthorize("@ss.hasPermi('system:withdraw:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LyxMeituanWithdraw lyxMeituanWithdraw)
    {
        return toAjax(lyxMeituanWithdrawService.insertLyxMeituanWithdraw(lyxMeituanWithdraw));
    }

    /**
     * 修改【请填写功能名称】
     */
//    @PreAuthorize("@ss.hasPermi('system:withdraw:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LyxMeituanWithdraw lyxMeituanWithdraw)
    {
        return toAjax(lyxMeituanWithdrawService.updateLyxMeituanWithdraw(lyxMeituanWithdraw));
    }

    /**
     * 删除【请填写功能名称】
     */
//    @PreAuthorize("@ss.hasPermi('system:withdraw:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lyxMeituanWithdrawService.deleteLyxMeituanWithdrawByIds(ids));
    }


    @PostMapping("/insertOrUpdate")
    public Map<String,Object> saveOrUpdate(@RequestBody LyxMeituanWithdraw lyxMeituanWithdraw)
    {
        LoginUser loginUser = getLoginUser();
        return lyxMeituanWithdrawService.saveOrUpdate(lyxMeituanWithdraw,loginUser);
    }

    /**
     * 查询美团记账金额
     * @param lyxMeituanWithdraw
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/getMTTallyAmt")
    public Map<String,Object> getMTTallyAmt(LyxMeituanWithdraw lyxMeituanWithdraw)
    {
        return  lyxMeituanWithdrawService.queryMTTallyAmt(lyxMeituanWithdraw);
    }



    @PostMapping("/createMTVouchar")
    public Map<String,Object> createMTVouchar(@RequestBody LyxMeituanWithdraw lyxMeituanWithdraw)
    {
        return  lyxMeituanWithdrawService.createMTVouchar(lyxMeituanWithdraw);
    }


    /**
     * 查询美团记账金额
     * @param lyxMeituanWithdraw
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @GetMapping("/getAmtTotal")
    public Map<String,Object> getAmtTotal(LyxMeituanWithdraw lyxMeituanWithdraw)
    {
        return  lyxMeituanWithdrawService.queryAmtTotal(lyxMeituanWithdraw);
    }






}
