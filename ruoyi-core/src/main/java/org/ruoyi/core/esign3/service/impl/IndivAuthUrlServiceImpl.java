package org.ruoyi.core.esign3.service.impl;

import org.ruoyi.core.esign3.bean.ContextInfo;
import org.ruoyi.core.esign3.bean.IndivInfo;
import org.ruoyi.core.esign3.exception.DefineException;
import org.ruoyi.core.esign3.factory.base.PsnIdentityVerify;
import org.ruoyi.core.esign3.factory.indivIdentity.IndivAuthUrl;
import org.ruoyi.core.esign3.factory.response.indivIdentity.IndivAuthUrlResponse;
import org.ruoyi.core.esign3.model.ConfigParams;
import org.ruoyi.core.esign3.service.IndivAuthUrlService;
import org.ruoyi.core.esign3.util.FactoryInitializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 获取个人核身认证地址服务实现类
 */
@Service
public class IndivAuthUrlServiceImpl implements IndivAuthUrlService {

    @Autowired
    private FactoryInitializer factoryInitializer;

    /**
     * 获取个人核身认证地址
     * @param authType 指定默认认证类型
     * @param availableAuthTypes 指定页面显示认证方式
     * @param authAdvancedEnabled 是否使用详情版
     * @param contextInfo 上下文信息
     * @param indivInfo 个人基本信息
     * @param configParams 配置参数
     * @return 认证地址响应
     * @throws DefineException 自定义异常
     */
    @Override
    public IndivAuthUrlResponse getIndivAuthUrl(String authType,
                                              List<String> availableAuthTypes,
                                              List<String> authAdvancedEnabled,
                                              ContextInfo contextInfo,
                                              IndivInfo indivInfo,
                                              ConfigParams configParams) throws DefineException {
        // 确保Factory类已初始化
        factoryInitializer.ensureInitialized();

        IndivAuthUrl indivAuthUrl = PsnIdentityVerify.indivAuthUrl(
            authType,
            availableAuthTypes,
            authAdvancedEnabled,
            contextInfo,
            indivInfo,
            configParams
        );

        IndivAuthUrlResponse response = indivAuthUrl.execute();
        return response;
    }
}
