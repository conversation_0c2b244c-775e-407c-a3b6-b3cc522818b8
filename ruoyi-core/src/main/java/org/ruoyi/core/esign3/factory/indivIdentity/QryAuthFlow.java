package org.ruoyi.core.esign3.factory.indivIdentity;

import org.ruoyi.core.esign3.enums.RequestType;
import org.ruoyi.core.esign3.exception.DefineException;
import org.ruoyi.core.esign3.factory.request.Request;
import org.ruoyi.core.esign3.factory.response.indivIdentity.QryAuthFlowResponse;

/**
 * 查询认证授权流程详情
 * <AUTHOR>
 * @date 2025/05/06
 */
public class QryAuthFlow extends Request<QryAuthFlowResponse> {

    private String authFlowId;

    /**
     * 构造方法
     * @param authFlowId 认证授权流程ID
     */
    public QryAuthFlow(String authFlowId) {
        this.authFlowId = authFlowId;
    }

    @Override
    public void build() {
        // 设置请求方法和路径
        this.setUrl("/v3/auth-flow/" + authFlowId);
        this.setRequestType(RequestType.GET);
    }
}
