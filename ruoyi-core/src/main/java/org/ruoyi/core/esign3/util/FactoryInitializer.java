package org.ruoyi.core.esign3.util;

import org.ruoyi.core.esign3.factory.Factory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * E签宝工厂类初始化工具
 * 用于确保Factory类在使用前被正确初始化
 */
@Component
public class FactoryInitializer {

    @Value("${esign.api_host}")
    private String apiHost;

    @Value("${esign.project_id}")
    private String projectId;

    @Value("${esign.project_secret}")
    private String projectSecret;
    
    /**
     * 确保Factory类已初始化
     * 如果未初始化，则使用配置文件中的值进行初始化
     */
    public void ensureInitialized() {
        if (Factory.getProject_id() == null || Factory.getProject_scert() == null || Factory.getHost() == null) {
            System.out.println("=== FactoryInitializer: Factory未初始化，进行初始化 ===");
            System.out.println("apiHost: " + apiHost);
            System.out.println("projectId: " + projectId);
            System.out.println("projectSecret: " + projectSecret);
            Factory.init(apiHost, projectId, projectSecret);
            Factory.setDebug(true);
            System.out.println("=== FactoryInitializer: Factory初始化完成 ===");
        } else {
            System.out.println("=== FactoryInitializer: Factory已初始化 ===");
            System.out.println("Factory.getProject_id(): " + Factory.getProject_id());
            System.out.println("Factory.getProject_scert(): " + Factory.getProject_scert());
            System.out.println("Factory.getHost(): " + Factory.getHost());
        }
    }
}
