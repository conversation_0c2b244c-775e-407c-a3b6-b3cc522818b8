package org.ruoyi.core.OASettingUp.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class NoaFlow {

    /** 流程ID */
    @Excel(name = "流程ID")
    private String flowId;

    /**
     * 流程名称
     */
    private String flowName;

    /** 节点ID */
    @Excel(name = "节点ID")
    private String nodeId;

    /**
     * 动态表单ID
     */
    private String formId;

    /**
     * 模板流程绑定的流程全称id
     */
    private String flowFullId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 流程创建人
     */
    private String createUser;

    /**
     * 流程创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 节点审核通过时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date auditTime;

    /**
     * 节点审核人
     */
    private String auditUser;

    private String parentId;

    private String ancestors;

    private String parentName;

    private String className;

    private Boolean nameFlag;

    private List<NoaFlow> nodes;

}
