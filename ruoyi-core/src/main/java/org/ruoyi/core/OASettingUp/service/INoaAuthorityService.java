package org.ruoyi.core.OASettingUp.service;

import org.ruoyi.core.OASettingUp.domain.NoaAuthority;
import org.ruoyi.core.OASettingUp.domain.NoaFlow;
import org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind;

import java.util.List;

/**
 * authorityService接口
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
public interface INoaAuthorityService
{
    /**
     * 查询authority
     *
     * @param flowId authority主键
     * @return authority
     */
    public NoaAuthority selectNoaAuthorityByFlowId(String flowId);

    /**
     * 查询authority列表
     *
     * @param noaAuthority authority
     * @return authority集合
     */
    public List<NoaAuthority> selectNoaAuthorityList(NoaAuthority noaAuthority);

    /**
     * 新增authority
     *
     * @param noaAuthority authority
     * @return 结果
     */
    public int insertNoaAuthority(NoaAuthority noaAuthority);

    /**
     * 修改authority
     *
     * @param noaAuthority authority
     * @return 结果
     */
    public int updateNoaAuthority(NoaAuthority noaAuthority);

    /**
     * 批量删除authority
     *
     * @param flowIds 需要删除的authority主键集合
     * @return 结果
     */
    public int deleteNoaAuthorityByFlowIds(String[] flowIds);

    /**
     * 删除authority信息
     *
     * @param flowId authority主键
     * @return 结果
     */
    public int deleteNoaAuthorityByFlowId(String flowId);

    /**
     * 获取所有流程及节点详情
     * @return  NoaFlow
     */
    List<NoaFlow> getFlow(String flowName);

    /**
     * 根据流程全量Id获取流程节点信息
     * @param flowFullId
     * @return
     */
    List<NoaFlow> getNodeToFlow(String flowFullId);

    int insertNoaAuthoritys(List<NoaAuthority> authorits);

    int deleteAuthorityById(String id);

    List<NoaAuthority> selectNoaAuthorityListToRemind(List<NoaWorkflowRemind> noaWorkflowReminds);

    List<NoaAuthority> selectNoaAuthorityByRemind(NoaWorkflowRemind returnDate);
}
