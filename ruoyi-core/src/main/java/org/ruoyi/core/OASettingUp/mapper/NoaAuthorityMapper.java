package org.ruoyi.core.OASettingUp.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.OASettingUp.domain.NoaAuthority;
import org.ruoyi.core.OASettingUp.domain.NoaFlow;
import org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind;

import java.util.List;

/**
 * authorityMapper接口
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
@Mapper
public interface NoaAuthorityMapper
{
    /**
     * 查询authority
     *
     * @param flowId authority主键
     * @return authority
     */
    public

    NoaAuthority selectNoaAuthorityByFlowId(String flowId);

    /**
     * 查询authority列表
     *
     * @param noaAuthority authority
     * @return authority集合
     */
    public List<NoaAuthority> selectNoaAuthorityList(NoaAuthority noaAuthority);

    /**
     * 新增authority
     *
     * @param noaAuthority authority
     * @return 结果
     */
    public int insertNoaAuthority(NoaAuthority noaAuthority);

    /**
     * 修改authority
     *
     * @param noaAuthority authority
     * @return 结果
     */
    public int updateNoaAuthority(NoaAuthority noaAuthority);

    /**
     * 删除authority
     *
     * @param flowId authority主键
     * @return 结果
     */
    public int deleteNoaAuthorityByFlowId(String flowId);

    /**
     * 批量删除authority
     *
     * @param flowIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNoaAuthorityByFlowIds(String[] flowIds);

    List<NoaFlow> selectFlow(String flowName);

    int insertNoaAuthoritys( @Param("authorits") List<NoaAuthority> authorits);

    int deleteAuthorityById(String remindId);

    List<NoaAuthority> selectNoaAuthorityListToRemind(@Param("workFlowReminds") List<NoaWorkflowRemind> noaWorkflowReminds);

    List<NoaAuthority> selectNoaAuthorityByRemind(NoaWorkflowRemind returnDate);

    List<NoaFlow> selectParentClass(@Param("noaFlows") List<NoaFlow> noaFlows);
}
