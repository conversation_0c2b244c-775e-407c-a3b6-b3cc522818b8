package org.ruoyi.core.xmglproject.mapper;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.AuthMain;
import com.ruoyi.system.domain.SysPost;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.xmglproject.domain.*;

import javax.xml.crypto.Data;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface XmglProjectMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public XmglProject selectXmglProjectById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param xmglProject 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<XmglProject> selectXmglProjectList(XmglProject xmglProject);

    /**
     * 新增【请填写功能名称】
     * 
     * @param xmglProject 【请填写功能名称】
     * @return 结果
     */
    public int insertXmglProject(XmglProject xmglProject);

    /**
     * 修改【请填写功能名称】
     * 
     * @param xmglProject 【请填写功能名称】
     * @return 结果
     */
    public int updateXmglProject(XmglProject xmglProject);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteXmglProjectById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXmglProjectByIds(Long[] ids);

    /**
     * 查询资金方数据
     * @return
     */
    List<Map<String, Object>> getFundList();
    /**
     * 查询资产方数据
     * @return
     */
    List<Map<String, Object>> getProductList();

    /**
     * 查询资金方数据
     * @return
     */
    List<Map<String, Object>> getFundShortList();
    /**
     * 查询资产方数据
     * @return
     */
    List<Map<String, Object>> getProductShortList();


    /**
     * 负责人idList
     * @return
     */
    List<Map<String, Object>> getUserList();

    /**
     * 获取已经到期的项目
     *
     * @return {@link List}<{@link XmglProject}>
     */
    List<XmglProject> getOverduePro();

    /**
     * 得到渠道方列表
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> getChannelSideList();

    /**
     * 根据公共方法获取到的有权限的项目id查询项目信息集合
     * @return
     */
    List<OaProjectDeploy> selectProjectList(@Param("authIds") List<Long> authIds);

    /**
     * 新增立项项目时根据项目名称查重
     * @param projectName
     * @return
     */
    int queryIsRepeatByProjectName(String projectName);

    /**
     * 新增立项项目-内部渠道方信息
     * @param xmglProjectChannel
     * @return
     */
    int insertProjectChannelInfo(XmglProjectChannel xmglProjectChannel);

    List<SysDept> selectLoginUserDept(@Param("userId") Long userId, @Param("deptName")String deptName);

    List<SysUser> selectPrincipalByProjectId(Long projectId);

    /**
     * 根据项目id查询当前项目的业务负责人
     * @param projectId
     * @return
     */
    List<SysUser> selectEachDimensionAuth(Long projectId);

    /**
     * 新增立项项目与项目名称关联关系表
     * @param xmglDeployProject
     * @return
     */
    int insertDeployProjectInfo(XmglDeployProject xmglDeployProject);

    /**
     * 查询所有不是'新增立项审核'状态的项目id
     * @param authProjectIds
     * @return
     */
    List<Long> selectNoXZLXSHStatusProject(@Param("authProjectIds") List<Long> authProjectIds);

    /**
     * 查询当前用户是否是所有项目的业务管理员或某些项目的业务管理员角色
     * @param moduleCode
     * @param roleCode
     * @param userId
     */
    List<AuthDetailVo> selectYWManagerAndCreateByProjects(@Param("moduleCode") String moduleCode, @Param("roleCode") String roleCode, @Param("userId") Long userId);

    /**
     * 获取当前登录用户创建的'新增立项审核'状态中的项目id
     * @param nickName
     * @return
     */
    List<Long> selectXZLXSHStatusByCreateBy(String nickName);

    /**
     * 定时任务
     * 查询不是'已上线/已终止/'新增立项审核中/已启用/未解锁的所有项目信息
     * @param dateTime
     * @return
     */
    List<XmglProject> selectXmglProjectTaskList(String dateTime);

    /**
     * 定时任务
     * 查询不是'已上线/已终止/'新增立项审核中/已启用/已解锁/未认领的所有项目信息
     * @param alreadyDateStr
     * @return
     */
    List<XmglProject> selectXmglProjectTaskListAlready(String alreadyDateStr);

    SysUser dirtyDataProcessing(Long userId);

    /**
     * 根据用户id查询该用户是否有此项目名称的业务负责人角色
     * @param
     * @param userId
     */
    AuthDetailVo selectAuthDeployInfoByModuleTypeAndUserId(@Param("roleType") String roleType, @Param("userId") Long userId, @Param("deployId") Long deployId);

    /**
     * 根据auth_detail权限附表id删除用户权限
     * @param authDetailId
     */
    void deleteAuthMainDetailInfoByDetailId(Long authDetailId);

    /**
     * 查询项目名称下拉框列表
     * @param oaProjectDeploy
     * @return
     */
    List<OaProjectDeploy> newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdList(@Param("oaProjectDeploy") OaProjectDeploy oaProjectDeploy);

    /**
     * 项目汇总
     * @param xmglProject
     * @return
     */
    List<XmglProjectSummary> selectXmglProjectSummaryList(XmglProject xmglProject);

    OaProjectDeploy selectDeployInfoByProjectName(String projectName);

    /**
     * 根据用户昵称查询用户信息
     * @param nickName
     * @return
     */
    SysUser selectUserInfoByNickName(String nickName);

    /**
     * 获取当前登录用户主岗位
     * @param userId
     * @return
     */
    SysPost selectLoginUserHomePost(Long userId);

    /**
     * 根据项目名称查重
     * @param projectName
     * @return
     */
    XmglProject selectXmglProjectByProjectName(String projectName);

    /**
     * 查询所有存量数据的项目负责人
     * @return
     */
    List<XmglDeployProject> selectInventoryUsers(Date nowDate);

    /**
     * 根据立项项目id查询业务负责人
     * @param projectId
     * @return
     */
    List<XmglProjectUser> selectProjectUserListByProjectId(Long projectId);

    /**
     * 重新初始化内部渠道方权限
     * @param date
     * @return
     */
    List<XmglProject> selectXmglProjectListByTime(Date date);

    /**
     * 根据权限主表查询附表信息
     * @param authMainId
     * @return
     */
    List<AuthDetailVo> selectDeployIdInfoByAuthMainId(Long authMainId);

    /**
     * 查询所有新增立项审核中状态的立项项目信息
     * @return
     */
    List<XmglProject> selectNewAuditProjectInfo();

    /**
     * 根据项目名称id查询立项状态为'新增立项审核中'状态的立项项目信息
     * @param deployIds
     * @return
     */
    List<XmglProject> selectNewAuditProjectInfoByDeployIds(@Param("deployIds") List<Long> deployIds);
}
