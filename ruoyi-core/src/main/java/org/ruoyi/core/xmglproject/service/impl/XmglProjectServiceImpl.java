package org.ruoyi.core.xmglproject.service.impl;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.*;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.dto.AuthorizedFeatureDetailDTO;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserPostMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysOperLogService;
import com.ruoyi.system.service.ISysPostService;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.checkerframework.checker.units.qual.K;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.ruoyi.core.cwproject.service.ITopNotifyService;
import org.ruoyi.core.oasystem.domain.*;
import org.ruoyi.core.oasystem.domain.vo.OaProjectDeployVo1;
import org.ruoyi.core.oasystem.mapper.OaDataManageMapper;
import org.ruoyi.core.oasystem.mapper.OaEditApproveGeneralityUserMapper;
import org.ruoyi.core.oasystem.mapper.ProjectCompanyRelevanceMapper;
import org.ruoyi.core.oasystem.mapper.ProjectTypeRelevanceMapper;
import org.ruoyi.core.oasystem.service.IOaDataManageService;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.mapper.PersonnelArchivesMapper;
import org.ruoyi.core.xmglproject.constant.XmglProjectEnum;
import org.ruoyi.core.xmglproject.domain.*;
import org.ruoyi.core.xmglproject.mapper.*;
import org.ruoyi.core.xmglproject.service.IXmgAddTemporarilyService;
import org.ruoyi.core.xmglproject.service.IXmglProjectDeployService;
import org.ruoyi.core.xmglproject.service.IXmglProjectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ParseException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.xml.crypto.Data;

import static org.ruoyi.core.oasystem.service.impl.OaProjectNameRuleServiceImpl.getDataNameWithParents;

/**
 * 项目立项管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class XmglProjectServiceImpl implements IXmglProjectService {

    public static final String PERMISSION_NEVER_EXPIRE = "9999-12-31 23:59:59";

    private static Logger logger = LoggerFactory.getLogger(XmglProjectServiceImpl.class);
    @Autowired
    private XmglProjectMapper xmglProjectMapper;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    @Autowired
    private XmglContactWayMapper xmglContactWayMapper;

    @Autowired
    private XmglDynamicMapper dynamicMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private XmglProjectUserMapper xmglProjectUserMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private XmglProjectChannelMapper projectChannelMapper;

    @Autowired
    private IXmglProjectDeployService xmglProjectDeployService;

    @Autowired
    private NewAuthorityServiceImpl getNewAuthorityServiceImpl;

    @Autowired
    private XmglRelevanceServiceImpl xmglRelevanceServiceImpl;

    @Autowired
    private ITopNotifyService topNotifyService;

    @Autowired
    private OaEditApproveGeneralityUserMapper oaEditApproveGeneralityUserMapper;

    @Autowired
    private ISysOperLogService sysOperLogService;

    @Autowired
    private IXmgAddTemporarilyService addTemporarilyService;

    @Autowired
    private XmglFlowRelationMapper xmglFlowRelationMapper;

    @Autowired
    private ISysPostService iSysPostService;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityServiceImpl;

    @Autowired
    private ProjectTypeRelevanceMapper projectTypeRelevanceMapper;

    @Autowired
    private ProjectCompanyRelevanceMapper projectCompanyRelevanceMapper;

    @Autowired
    private XmglChannelUserMapper xmglChannelUserMapper;

    @Autowired
    private SysUserPostMapper sysUserPostMapper;

    @Autowired
    private PersonnelArchivesMapper personnelArchivesMapper;

    @Autowired
    private OaDataManageMapper oaDataManageService;
    @Autowired
    private XmglAddTemporarilyMapper xmglAddTemporarilyMapper;

    /**
     * 查询项目立项管理详情
     *
     * @param id 项目立项管理主键
     * @return 项目立项管理
     */
    @Override
    public AddprojectVo selectXmglProjectById(Long id, LoginUser loginUser) {
        Long userId = SecurityUtils.getLoginUser().getUserId();

        AddprojectVo addprojectVo = new AddprojectVo();
        Boolean busPrincipalFlag = false;
        Boolean proPrincipalFlag = false;// 项目负责人
        Boolean busAdministratorFlag = false;// 业务管理员
        Boolean interiorChannelFlag = false;// 内部渠道方
        Boolean readOnly = false;// 查看权限
        // Boolean projectType = false;
        // Boolean businessType = false;
        XmglProject xmglProject = xmglProjectMapper.selectXmglProjectById(id);

        // 先根据新的项目id获取关联项目名称模块的项目id
        XmglDeployProject xmglDeployProject = new XmglDeployProject();
        XmglDeployProject deployProjectInfo = new XmglDeployProject();
        deployProjectInfo = xmglProjectDeployService.selectXmglProjectDeployByProjectId(id);
        if (Objects.isNull(deployProjectInfo)) {
            // XmglDeployProject deployProject = new XmglDeployProject();
            deployProjectInfo.setDeployId(-1L);
            // BeanUtil.copyProperties(deployProject, deployProjectInfo);
        }
        // 获取项目详情
        XmglPostRoleFlag xmglPostRoleFlag = new XmglPostRoleFlag();
        // 担保公司
//        List<XmglProjectCompanyRelevance> custList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("0", id);
//        xmglProject.setCustList(custList);

        // 资产方
//        List<XmglProjectCompanyRelevance> partnerList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("1", id);
//        xmglProject.setPartnerList(partnerList);

        // 资金方
//        List<XmglProjectCompanyRelevance> fundList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("2", id);
//        xmglProject.setFundList(fundList);

        // 资金方
        List<XmglProjectCompanyRelevance> otherUnitList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("3", id);
        xmglProject.setOtherUnitList(otherUnitList);

        Map<String, List<XmglProjectCompanyRelevance>> tableList = xmglRelevanceServiceImpl.queryDataObjectByProjectId(id).stream()
                .filter(pr -> !pr.getUnitType().equals("3"))
                .collect(Collectors.groupingBy(pr -> pr.getUnitType() + "List"));
        if (!tableList.isEmpty()) {
            tableList = this.repliceTpyeMap(tableList);
        }
        xmglProject.setTableList(tableList);

/*        // 项目类型
        List<XmglProjectTypeRelevance> projectTypeData = xmglRelevanceServiceImpl.queryProjectObject("0", id);
        xmglProject.setXmglProjectTypeList(projectTypeData);

        // 业务类型
        List<XmglProjectTypeRelevance> businessTypeData = xmglRelevanceServiceImpl.queryProjectObject("1", id);
        xmglProject.setXmglBusinessTypeList(businessTypeData);
*/
        List<XmglProjectTypeRelevance> typeData = xmglRelevanceServiceImpl.getTypeByProjectId(id);
        List<XmglProjectTypeRelevance> businessTypeData = typeData.stream().filter(vo -> "1".equals(vo.getDataType())).collect(Collectors.toList());
        // 拼接前端需要的一二级typeName
        if (businessTypeData != null && !businessTypeData.isEmpty()) {
            Map<Long, OaDataManage> businessType = oaDataManageService.selectDataManageListByCode("business_type").stream()
                    .filter(vo -> vo.getParentId() != null) // 过滤最上一级节点
                    .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
            for (XmglProjectTypeRelevance businessTypeRe : businessTypeData) {
                String dataNameWithParents = getDataNameWithParents(businessTypeRe.getTypeId(), businessType);
                if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                    dataNameWithParents = dataNameWithParents.substring(1);
                }
                businessTypeRe.setTypeName(dataNameWithParents);
            }
        }
        xmglProject.setXmglBusinessTypeList(businessTypeData);


        List<XmglProjectTypeRelevance> projectTypeData = typeData.stream().filter(vo -> "0".equals(vo.getDataType())).collect(Collectors.toList());
        if (projectTypeData != null && !projectTypeData.isEmpty()) {
            Map<Long, OaDataManage> businessType = oaDataManageService.selectDataManageListByCode("project_type").stream()
                    .filter(vo -> vo.getParentId() != null) // 过滤最上一级节点
                    .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
            for (XmglProjectTypeRelevance businessTypeRe : projectTypeData) {
                String dataNameWithParents = getDataNameWithParents(businessTypeRe.getTypeId(), businessType);
                if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                    dataNameWithParents = dataNameWithParents.substring(1);
                }
                businessTypeRe.setTypeName(dataNameWithParents);
            }
        }
        xmglProject.setXmglProjectTypeList(projectTypeData);

        // 获取当前登陆人在该项目中的通用授权的权限类型(项目负责人/业务管理员/查看权限)
        // 查询当前用户在'项目名称'模块是否是该项目的业务责任人，如果是项目负责人或业务责任人，则该用户看不到'认领项目'按钮
        /*OaEditApproveGeneralityEditRecords editRecordsList = xmglProjectDeployService.selectProjectYWPerson(loginUser.getUserId(), xmglDeployProject.getDeployId());
        if (!Objects.isNull(editRecordsList)) {
            // 业务负责人
            busPrincipalFlag = "true";
        }*/
        // 查询当前用户在‘通用授权’中是担任了什么角色(项目负责人/业务管理员/查看权限)
        // List<AuthMain> authMains = xmglProjectDeployService.selectXmglProjectDeployInfoByDeployId(loginUser.getUserId(), deployProjectInfo.getDeployId());
        List<AuthorizedFeatureDetailDTO> projectRoleList = newAuthorityServiceImpl.getProjectRoleByOaProjectDeployIdAndModuleType(deployProjectInfo.getDeployId(), AuthModuleEnum.PROJSETUP.getCode());
        // 项目负责人
        List<Object> proPrincipalUserld = projectRoleList.stream().filter(t -> "7".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
        proPrincipalUserld.removeAll(Collections.singleton(null));
        if (!CollectionUtils.isEmpty(proPrincipalUserld)) {
            List<Long> featureUserIds = proPrincipalUserld.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
            if (featureUserIds.contains(userId)) {
                proPrincipalFlag = true;
            }
        }
        // 业务管理员
        List<Object> busAdministratorUserId = projectRoleList.stream().filter(t -> "10".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
        busAdministratorUserId.removeAll(Collections.singleton(null));
        if (!CollectionUtils.isEmpty(busAdministratorUserId)) {
            List<Long> featureUserIds = busAdministratorUserId.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
            if (featureUserIds.contains(userId)) {
                busAdministratorFlag = true;
            }
        }
        // 查看权限
        List<Object> readOnlyUserId = projectRoleList.stream().filter(t -> "88".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
        readOnlyUserId.removeAll(Collections.singleton(null));
        if (!CollectionUtils.isEmpty(readOnlyUserId)) {
            List<Long> featureUserIds = readOnlyUserId.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
            if (featureUserIds.contains(userId)) {
                readOnly = true;
            }
        }
        /*if (!CollectionUtils.isEmpty(authMains)) {
            for (AuthMain authMain : authMains) {
                // 项目负责人
                if (authMain.getRoleType().equals(AuthRoleEnum.XMGL1.getCode())) {
                    proPrincipalFlag = true;
                }
                // 业务管理员
                if (authMain.getRoleType().equals(AuthRoleEnum.XMGL2.getCode())) {
                    busAdministratorFlag = true;
                }
                // 仅查看权限
                if (authMain.getRoleType().equals(AuthRoleEnum.XMGL3.getCode())) {
                    readOnly = true;
                }
            }
        }*/
        // xmglProject.setProjectType(projectType);
        // xmglProject.setBusinessType(businessType);
        xmglProject.setDeployId(deployProjectInfo.getDeployId());// 项目名称模块的项目id

        // 渠道方类型
        String channelType = xmglProject.getChannelType();
        if (null != channelType && !channelType.equals("") && channelType.equals("1")) {
            // 内部渠道方集合
            List<XmglProjectChannel> xmglProjectChannels = projectChannelMapper.selectXmglProjectChannelListByProjectId(id);

            xmglProject.setProjectChannelList(xmglProjectChannels);
            // 判断当前登录用户是否是内部渠道方，项目的渠道方如果是内部，则内部渠道方用户可以发布项目动态，不能更新项目进度，不能看到认领项目按钮
            List<Long> collect = xmglProjectChannels.stream().map(XmglProjectChannel::getChannelId).collect(Collectors.toList());
            if (collect.contains(loginUser.getUserId())) {
                interiorChannelFlag = true;
            }
        }
        // 项目负责人
        List<XmglProjectUser> xmglProjectUsers = xmglProjectUserMapper.selectXmglProjectUserListByProjectId(id);
        xmglProject.setProjectPrincipalList(xmglProjectUsers);// 项目负责人集合
        List<Long> collect = xmglProjectUsers.stream().map(XmglProjectUser::getUserId).collect(Collectors.toList());
        boolean contains = collect.contains(loginUser.getUserId());
        if (contains) {
            proPrincipalFlag = true;
        }
        xmglPostRoleFlag.setBusPrincipalFlag(busPrincipalFlag);// 业务负责人
        xmglPostRoleFlag.setProPrincipalFlag(proPrincipalFlag);// 项目负责人
        xmglPostRoleFlag.setBusAdministratorFlag(busAdministratorFlag);// 业务管理员
        xmglPostRoleFlag.setReadAuth(readOnly);// 仅查看权限
        xmglPostRoleFlag.setInteriorChannelFlag(interiorChannelFlag);

        if (busPrincipalFlag || proPrincipalFlag || busAdministratorFlag) {
            // 项目动态
            List<XmglDynamic> xmglDynamics = dynamicMapper.selectXmglDynamicListByProjectId(xmglProject.getId());
            addprojectVo.setDynamicList(xmglDynamics);
        }

        // 项目联系人
        /**
         * 业务管理员、项目负责人可以看到所有人录入的联系人信息，即当前的和历史其他用户录入的
         * 渠道方只能看到当前的联系方式信息，可能是项目负责人录的，也可能是自己录的
         * 查看权限用户如果之前自己作为录入人曾经录入过（担任负责人或渠道方可以录入），则可以看到所有自己录入的；否则普通查看权限用户看不到任何联系方式信息
         */
        // 如果当前用户是业务管理员或项目负责人(业务负责人)，则查看所有联系人信息,或者当前用户既是业务管理员或项目负责人(业务负责人)，又是渠道方用户，也查看所有联系方式
        List<XmglContactWay> contactWayList = new ArrayList<>();
        if (busAdministratorFlag || proPrincipalFlag || busAdministratorFlag && interiorChannelFlag || readOnly) {
            // 查看所有联系方式(即当前的和历史其他用户录入的)
            contactWayList = xmglContactWayMapper.selectContactWayListByProjectId("all", id);
        } else if (interiorChannelFlag) {
            // 如果当前用户是渠道方，只能看到当前的联系方式信息，可能是项目负责人录的，也可能是自己录的
            contactWayList = xmglContactWayMapper.selectContactWayListByProjectId("channel", id);
        } else if (readOnly) {
            // 看权限用户如果之前自己作为录入人曾经录入过（担任负责人或渠道方可以录入），则可以看到所有自己录入的；否则普通查看权限用户看不到任何联系方式信息
            contactWayList = xmglContactWayMapper.selectContactWayByLoginUserId(loginUser.getUserId(), id);
        }
        addprojectVo.setProjectForm(xmglProject);
        addprojectVo.setUserContactWayList(contactWayList);
        addprojectVo.setXmglPostRoleFlag(xmglPostRoleFlag);
        return addprojectVo;
    }

    /**
     * 选择没有认领xmgl项目id
     * 如果不是通过认领项目进来则去关联表查询当前负责人的信息id
     * 未用到此方法
     *
     * @param id        id
     * @param loginUser 登录用户
     * @return {@link AddprojectVo}
     */
    @Override
    public AddprojectVo selectNoRenLingXmglProjectById(Long id, LoginUser loginUser) {
        AddprojectVo addprojectVo = new AddprojectVo();
        XmglProject xmglProject = xmglProjectMapper.selectXmglProjectById(id);


//        XmglContactWay xmglContactWay =  xmglContactWayMapper.selectByProIdAndUserId(id,loginUser.getUserId());
        // 通过负责人id和项目id查询联系方式id
        XmglProjectUser xmglProjectUser = new XmglProjectUser();
        xmglProjectUser.setProjectId(xmglProject.getId());
        xmglProjectUser.setUserId(xmglProject.getUserId());
        List<XmglProjectUser> xmglProjectUsers = xmglProjectUserMapper.selectXmglProjectUserList(xmglProjectUser);
        XmglContactWay xmglContactWay = new XmglContactWay();
        if (xmglProjectUsers.size() > 0) {
            Long contactId = xmglProjectUsers.get(0).getContactId();
            xmglContactWay = xmglContactWayMapper.selectXmglContactWayById(contactId);
        } else {
            xmglContactWay = xmglContactWayMapper.selectByProIdAndUserId(xmglProject.getId(), loginUser.getUserId());
        }

        // 去关系表查询
        addprojectVo.setProjectForm(xmglProject);
        addprojectVo.setUserform(xmglContactWay);

        return addprojectVo;
    }

    /**
     * 查询项目立项管理列表
     *
     * @param xmglProject 项目立项管理
     * @return 【项目立项管理
     */
    @Override
    public List<XmglProject> selectXmglProjectList(XmglProject xmglProject, LoginUser loginUser) {
        List<XmglProject> xmglProjects = new ArrayList<>();
        List<Long> projectIds = new ArrayList<>();
        xmglProject.setStatus("0");
        List<Long> newAuditIds = new ArrayList<>();
        // 获取当前登录用户有项目任何一种角色权限的项目(项目负责人、业务管理员、查看权限)
        PageHelper.clearPage();
        List<Long> authProjectIds = getNewAuthorityServiceImpl.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.PROJSETUP.getCode());
        /**
         * 权限：
         * 用户可见的项目，由通用授权功能确定。共有3种角色：项目负责人(业务负责人)、业务管理员、查看权限。在列表中用户能看到有项目任何一种角色权限的项目
         * 运营部岗位或渠道部岗位或各地CEO岗位的用户，能看到【新增项目】【认领项目】按钮
         * 新增立项审核中状态的项目，仅申请人和业务管理员可见
         */
        // 查询所有当前用户有查看权限，且不是'新增立项审核中状态'的项目名称id
        if (!CollectionUtils.isEmpty(authProjectIds)) {
            projectIds = xmglProjectMapper.selectNoXZLXSHStatusProject(authProjectIds);
        }

        // 查询当前用户是否是所有项目的业务管理员或某些项目的业务管理员角色
        List<AuthDetailVo> ywProjectIds = xmglProjectMapper.selectYWManagerAndCreateByProjects(AuthModuleEnum.PROJSETUP.getCode(), AuthRoleEnum.XMGL2.getCode(), loginUser.getUserId());
        List<Long> deployIds = new ArrayList<>();
        Boolean flag = false;
        // 根据权限主表id查询附表信息
        if (!CollectionUtils.isEmpty(ywProjectIds)) {
            for (AuthDetailVo authDetailVo : ywProjectIds) {
                if (flag) {
                    break;
                }
                List<AuthDetailVo> detailVoList = xmglProjectMapper.selectDeployIdInfoByAuthMainId(authDetailVo.getAuthMainId());
                // 不为空，则是通过通用授权给的权限，需查询当前用户有哪些项目的业务管理员权限
                if (!CollectionUtils.isEmpty(detailVoList)) {
                    for (AuthDetailVo detailVo : detailVoList) {
                        if (detailVo.getStatus().equals("0")) {
                            deployIds.add(detailVo.getThirdTableId());
                        }
                    }
                } else {
                    // 为空，则证明是初始化数据
                    flag = true;
                }
            }
        }
        // 是初始化权限，查询新增立项审核中的立项项目
        List<XmglProject> newAuditProject = new ArrayList<>();
        if (flag) {
            newAuditProject = xmglProjectMapper.selectNewAuditProjectInfo();
            if (!CollectionUtils.isEmpty(newAuditProject)) {
                newAuditIds = newAuditProject.stream().map(XmglProject::getId).collect(Collectors.toList());
            }
        } else {
            // 不是初始换权限，判断是否为空，不为空则查询对应的立项项目信息
            if (!CollectionUtils.isEmpty(deployIds)) {
                newAuditProject = xmglProjectMapper.selectNewAuditProjectInfoByDeployIds(deployIds);
                if (!CollectionUtils.isEmpty(newAuditProject)) {
                    newAuditIds = newAuditProject.stream().map(XmglProject::getId).collect(Collectors.toList());
                }
            }
        }
        // 获取当前用户创建的'新增立项审核'状态的项目名称id
        List<Long> loginCreate = xmglProjectMapper.selectXZLXSHStatusByCreateBy(loginUser.getUser().getNickName());
        // set去重
        Set<Long> set = new HashSet<>();
        set.addAll(projectIds);
        set.addAll(newAuditIds);
        set.addAll(loginCreate);

        // 第一次进入页面，表中无数据，则直接返回
        if (CollectionUtils.isEmpty(authProjectIds) || CollectionUtils.isEmpty(set)) {
            return xmglProjects;
        }

        // 传参立项项目id查询立项项目信息集合
        xmglProject.setDeployIds(new ArrayList<>(set));

        // 查看所有立项项目列表
        if (xmglProject.getScope().equals("all")) {
            if (null != xmglProject.getPageNum() && null != xmglProject.getPageSize()) {
                PageHelper.startPage(xmglProject.getPageNum(), xmglProject.getPageSize());
            }
            xmglProjects = xmglProjectMapper.selectXmglProjectList(xmglProject);
            xmglProjects.removeAll(Collections.singleton(null));
        } else {
            // 只查看本人负责的项目
            // xmglProject.setChannelType("1");// 渠道方类型
            Long principalListId = xmglProject.getUserId();// 业务负责人id
            if (null != principalListId) {
                xmglProject.setPrincipalId(principalListId);
            }
            xmglProject.setUserId(loginUser.getUserId());// 当前登陆人id
            xmglProject.setChannelSideAcc(loginUser.getUser().getUserId().toString());// 内部渠道方
            if (null != xmglProject.getPageNum() && null != xmglProject.getPageSize()) {
                PageHelper.startPage(xmglProject.getPageNum(), xmglProject.getPageSize());
            }
            xmglProjects = xmglProjectMapper.selectXmglProjectList(xmglProject);
            xmglProjects.removeAll(Collections.singleton(null));
        }
        // 组装枚举
        if (!CollectionUtils.isEmpty(xmglProjects) && xmglProjects.size() > 0) {
            assembleList(xmglProjects);
        }
        if (xmglProjects.isEmpty()) {
            return xmglProjects;
        }

        List<Long> xmglProjectIds = xmglProjects.stream().map(XmglProject::getId).collect(Collectors.toList());
        List<XmglProjectTypeRelevance> typeByProjectIds = xmglRelevanceServiceImpl.getTypeByProjectIds(xmglProjectIds);
        // 业务类型 改 产品类型
        Map<Long, List<XmglProjectTypeRelevance>> businessTypeMap = typeByProjectIds.stream().filter(vo -> "1".equals(vo.getDataType()))
                .collect(Collectors.groupingBy(XmglProjectTypeRelevance::getProjectId));
        //
        Map<Long, List<XmglProjectTypeRelevance>> projectTypeMap = typeByProjectIds.stream().filter(vo -> "0".equals(vo.getDataType()))
                .collect(Collectors.groupingBy(XmglProjectTypeRelevance::getProjectId));

        for (XmglProject deployVo1 : xmglProjects) {
            List<XmglProjectTypeRelevance> businessTypeData = businessTypeMap.get(deployVo1.getId());
            if (businessTypeData != null && !businessTypeData.isEmpty()) {
                Map<Long, OaDataManage> businessType = oaDataManageService.selectDataManageListByCode("business_type").stream()
                        .filter(vo -> vo.getParentId() != null) // 过滤最上一级节点
                        .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
                for (XmglProjectTypeRelevance businessTypeRe : businessTypeData) {
                    String dataNameWithParents = getDataNameWithParents(businessTypeRe.getTypeId(), businessType);
                    if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                        dataNameWithParents = dataNameWithParents.substring(1);
                    }
                    businessTypeRe.setTypeName(dataNameWithParents);
                }
            }
            deployVo1.setXmglBusinessTypeList(businessTypeData);

            List<XmglProjectTypeRelevance> projectTypeData = projectTypeMap.get(deployVo1.getId());
            if (projectTypeData != null && !projectTypeData.isEmpty()) {
                Map<Long, OaDataManage> projectType = oaDataManageService.selectDataManageListByCode("project_type").stream()
                        .filter(vo -> vo.getParentId() != null) // 过滤最上一级节点
                        .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
                for (XmglProjectTypeRelevance projectTypeRe : projectTypeData) {
                    String dataNameWithParents = getDataNameWithParents(projectTypeRe.getTypeId(), projectType);
                    if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                        dataNameWithParents = dataNameWithParents.substring(1);
                    }
                    projectTypeRe.setTypeName(dataNameWithParents);
                }
            }
            deployVo1.setXmglProjectTypeList(projectTypeData);
        }

        return xmglProjects;
    }

    /**
     * 项目立项列表组装枚举
     *
     * @param xmglProjects
     * @return
     */
    private List<XmglProject> assembleList(List<XmglProject> xmglProjects) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        xmglProjects.forEach(x -> {
            // 进度状态
            String proStatus = XmglProjectEnum.getName(x.getProjectStatus());
            x.setProjectStatusLabel(proStatus);
            // 锁定状态
            /**
             * 项目状态已上线 锁定状态显示为：-                         YSX
             * 项目状态已被终止  展示进度条图形；锁定状态为-              YZZ
             * 有人认领了项目，等待OA流程审核  展示进度条；锁定状态为-     RLXMSH
             * 申请项目延期，等待OA流程审核 展示进度条图形；锁定状态为-    YQXMSH
             * 申请终止项目，等待OA流程审核 展示进度条图形；锁定状态为-    ZZXMSH
             * 用户提交新增项目申请，等待OA流程审核 不展示进度条和解锁倒计时及锁定状态  XZXMSH
             */
            if (x.getProjectStatus().equals(XmglProjectEnum.getCode("已上线")) || x.getProjectStatus().equals(XmglProjectEnum.getCode("已终止"))
                    || x.getProjectStatus().equals(XmglProjectEnum.getCode("认领项目审核中")) || x.getProjectStatus().equals(XmglProjectEnum.getCode("延期项目审核中"))
                    || x.getProjectStatus().equals(XmglProjectEnum.getCode("终止项目审核中")) || x.getProjectStatus().equals(XmglProjectEnum.getCode("新增立项审核中"))) {
                x.setLockStatus("-");
                x.setUnlockDay("-");
            }

            // 项目负责人
            if (null == x.getProjectPrincipal() || x.getProjectPrincipal().equals("")) {
                x.setProjectPrincipal("");
            }

            // 状态
            if (!x.getScheduleStatus().equals("-")) {
                String scheduleStatus = XmglProjectEnum.getName(x.getScheduleStatus());
                x.setScheduleStatusLabel(scheduleStatus);
            }

            // 判断已锁定状态下，解锁倒计时是否是负数
            if (x.getUnlockDay().contains("-") && x.getUnlockDay().length() > 1) {
                String unlockDay = x.getUnlockDay().substring(x.getUnlockDay().indexOf("-") + 1);
                x.setUnlockDay(unlockDay);
            }
            Boolean busPrincipalFlag = false;
            Boolean proPrincipalFlag = false;
            Boolean busAdministratorFlag = false;
            Boolean interiorChannelFlag = false;
            Boolean readOnly = false;
            // String projectType = "";
            // String businessType = "";
            ///查询当前用户在'项目名称'模块是否是该项目的业务责任人或项目负责人，如果是项目负责人或业务责任人，则该用户看不到'认领项目'按钮
            OaEditApproveGeneralityEditRecords editRecordsList = xmglProjectDeployService.selectProjectYWPerson(loginUser.getUserId(), x.getDeployId());
            if (!Objects.isNull(editRecordsList)) {
                // 业务负责人
                busPrincipalFlag = true;
            }
            // 查询当前用户在‘授权’中是担任了什么角色(项目负责人/业务管理员/查看权限)
            // List<AuthMain> authMains = xmglProjectDeployService.selectXmglProjectDeployInfoByDeployId(loginUser.getUserId(), x.getDeployId());
            List<AuthorizedFeatureDetailDTO> projectRoleList = newAuthorityServiceImpl.getProjectRoleByOaProjectDeployIdAndModuleType(x.getDeployId(), AuthModuleEnum.PROJSETUP.getCode());
            // 项目负责人
            List<Object> proPrincipalUserld = projectRoleList.stream().filter(t -> "7".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
            proPrincipalUserld.removeAll(Collections.singleton(null));
            if (!CollectionUtils.isEmpty(proPrincipalUserld)) {
                List<Long> featureUserIds = proPrincipalUserld.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
                if (featureUserIds.contains(loginUser.getUserId())) {
                    proPrincipalFlag = true;
                }
            }

            // 业务管理员
            List<Object> busAdministratorUserId = projectRoleList.stream().filter(t -> "10".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
            busAdministratorUserId.removeAll(Collections.singleton(null));
            if (!CollectionUtils.isEmpty(busAdministratorUserId)) {
                List<Long> featureUserIds = busAdministratorUserId.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
                if (featureUserIds.contains(loginUser.getUserId())) {
                    busAdministratorFlag = true;
                }
            }
            // 查看权限
            List<Object> readOnlyUserId = projectRoleList.stream().filter(t -> "88".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
            readOnlyUserId.removeAll(Collections.singleton(null));
            if (!CollectionUtils.isEmpty(readOnlyUserId)) {
                List<Long> featureUserIds = readOnlyUserId.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
                if (featureUserIds.contains(loginUser.getUserId())) {
                    readOnly = true;
                }
            }
            // 渠道方类型
            String channelType = "";
            if (null != x.getChannelType()) {
                channelType = x.getChannelType();
            }
            if (!channelType.equals("") && channelType.equals("1")) {
                // 内部渠道方集合
                List<XmglProjectChannel> xmglProjectChannels = projectChannelMapper.selectXmglProjectChannelListByProjectId(x.getId());
                x.setProjectChannelList(xmglProjectChannels);
                // 判断当前登录用户是否是内部渠道方，项目的渠道方如果是内部，则内部渠道方用户可以发布项目动态，不能更新项目进度，不能看到认领项目按钮
                for (XmglProjectChannel xmglProjectChannel : xmglProjectChannels) {
                    if (loginUser.getUserId() == xmglProjectChannel.getChannelId()) {
                        interiorChannelFlag = true;
                    }
                }
            }
            // 担保公司
            /*List<XmglProjectCompanyRelevance> custList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("0", x.getId());
            x.setCustList(custList);

            // 资产方
            List<XmglProjectCompanyRelevance> partnerList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("1", x.getId());
            x.setPartnerList(partnerList);

            // 资金方
            List<XmglProjectCompanyRelevance> fundList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("2", x.getId());
            x.setFundList(fundList);*/

            // 业务类型 改为 产品分类
//            List<XmglProjectTypeRelevance> businessTypeData = xmglRelevanceServiceImpl.queryProjectObject("1", x.getId());
//            String businessType = "";
//            for (XmglProjectTypeRelevance businessTypeDatum : businessTypeData) {
//                businessType = businessType + businessTypeDatum.getTypeName() + ",";
//            }
//            if (StringUtils.isNotEmpty(businessType)) {
//                businessType = businessType.substring(0, businessType.lastIndexOf(","));
//            }
//            x.setBusinessType(businessType);

            // 权限标识
            XmglPostRoleFlag xmglPostRoleFlag = new XmglPostRoleFlag();
            xmglPostRoleFlag.setBusPrincipalFlag(busPrincipalFlag);
            xmglPostRoleFlag.setProPrincipalFlag(proPrincipalFlag);
            xmglPostRoleFlag.setBusAdministratorFlag(busAdministratorFlag);
            xmglPostRoleFlag.setReadAuth(readOnly);
            xmglPostRoleFlag.setInteriorChannelFlag(interiorChannelFlag);
            x.setPostRoleFlag(xmglPostRoleFlag);
        });
        return xmglProjects;
    }

    public void chexkOverTimeProject(LoginUser loginUser) {
        // 先清除全部的

        Collection<String> keys = SpringUtils.getBean(RedisCache.class).keys("XMGLOVERTIME_" + "*");
        SpringUtils.getBean(RedisCache.class).deleteObject(keys);

        // 添加redis证明今天执行过
        String s = DateUtils.dateTime();
        SpringUtils.getBean(RedisCache.class).setCacheObject("XMGLOVERTIME_" + s, true);

        // 查找数据中是否有过期天数为0的数据
        List<XmglProject> overdueData = xmglProjectMapper.getOverduePro();

        for (XmglProject overdueDatum : overdueData) {
            overdueDatum.setLockStatus("1");
            if (overdueDatum.getClaimed() == null || overdueDatum.getClaimed().equals("")) {
                overdueDatum.setClaimed("1");// 未认领
            }
            // 插入动态表
            String dynamicMyg = "项目进度未更新超过90天，已解锁，可被他人认领";
            this.insertDynamic(overdueDatum.getId(), "0", dynamicMyg, loginUser.getUserId());
            xmglProjectMapper.updateXmglProject(overdueDatum);
        }
    }

    /**
     * 新增立项项目
     *
     * @param xmglProject 项目立项管理
     * @return 结果
     */
    @Override
    public Map<String, Object> insertXmglProject(AddprojectVo xmglProject, LoginUser loginUser) {
        HashMap<String, Object> returnMap = null;
        XmglProject projectForm = null;
        String errorMessage = "";
        try {
            logger.info("新增立项项目数据表入库操作..开始");
            returnMap = new HashMap<>();
            projectForm = xmglProject.getProjectForm();
            // 项目名称id
            Long deployId = projectForm.getDeployId();

            // List<XmglProject> xmglProjects = this.checkAgain(projectForm,loginUser);
            // List<XmglProject> xmglProjects = new ArrayList<>();
            // if(xmglProjects.size() == 0){

            // 计算90天后的时间（新增审批通过之后自动更新为当前时间）
            Date date30 = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 90L);
            // Date date30 = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 1L);
            Date nowDate = DateUtils.getNowDate();
            logger.info("新增方法设置对象参数的字段默认值..");
            projectForm.setCreateTime(nowDate);// 创建时间
            // projectForm.setBeginTime(nowDate);// 锁定开始时间
            projectForm.setOverTime(date30);// 锁定结束时间(表中此字段不能为空，后期在审批通过之后，重新设定锁定结束时间)
            projectForm.setClaimed("1");// 1未认领
            /** 新权限改造后，以下字段不需要再传值，此处防止不报错，设置默认值 开始 */
            projectForm.setUserId(loginUser.getUserId());// 责任人id字段在新权限未用到，此处设置成当前审批用户id
            projectForm.setBusinessType("-");
            projectForm.setFundShortName("-");
            projectForm.setFundFullName("-");
            projectForm.setCustFullName("-");
            projectForm.setCustShortName("-");
            projectForm.setProductFullName("-");
            projectForm.setProductShortName("-");
            projectForm.setCreateBr(loginUser.getUser().getNickName());
            if (projectForm.getChannelSide().equals("") && projectForm.getChannelSide() == null) {
                projectForm.setChannelSide("");
            }
            /** 新权限改造后，以上字段不需要再传值，此处防止不报错，设置默认值 结束 */
            projectForm.setProjectStatus("6");// 新增项目审核中
            projectForm.setUserId(loginUser.getUserId());
            logger.info("立项项目数据入库" + projectForm.toString());
            xmglProjectMapper.insertXmglProject(projectForm);

            // 资产方/资金方/担保公司/项目类型/业务类型关联表
            this.insertOtherInfo(projectForm);

            // 新增联系方式
            Long projectId = projectForm.getId();
            XmglContactWay userForm = xmglProject.getUserform();
            userForm.setProjectId(projectId);
            userForm.setUserId(loginUser.getUserId());// 录入人id
            userForm.setCreateTime(nowDate);
            userForm.setCreateBr(loginUser.getUser().getNickName());
            userForm.setFundDept(userForm.getFundDept());
            userForm.setProductDept(userForm.getProductDept());
            logger.info("新增联系方式数据入库" + userForm.toString());
            xmglContactWayMapper.insertXmglContactWay(userForm);

            // 选择的是内部渠道方
            if (xmglProject.getProjectForm().getChannelType().equals("1")) {
                List<Long> channelForm = xmglProject.getProjectForm().getChannelForm();
                if (!CollectionUtils.isEmpty(channelForm) && channelForm.size() > 0) {
                    for (Long aLong : channelForm) {
                        XmglProjectChannel channel = new XmglProjectChannel();
                        channel.setProjectId(projectForm.getId());
                        channel.setChannelId(aLong);
                        channel.setContactId(userForm.getId());
                        channel.setCreateBy(loginUser.getUsername());
                        channel.setCreateTime(DateUtils.getNowDate());
                        SysUser sysUser = sysUserMapper.selectUserById(aLong);
                        channel.setNickName(sysUser.getNickName());
                        logger.info("新增内部渠道方数据入库" + channel.toString());
                        xmglProjectMapper.insertProjectChannelInfo(channel);
                        /**
                         * 内部渠道方授权授权业务相关模块
                         * 如果内部渠道方用户在该模块没有任何一种权限，默认授予用户此模块的查看权限
                         * 如果该用户已在此模块有其他任何一种权限，则不做处理
                         */
                        this.getAuthority(aLong, deployId, userForm.getUserId());

                        // 查询当前新的内部渠道方的上级、上上级、祖级
                        List<Long> parentUserIds = new ArrayList<>();
                        parentUserIds = this.getParentUser(sysUser.getUserName(), parentUserIds);
                        if (!CollectionUtils.isEmpty(parentUserIds)) {
                            for (Long userId : parentUserIds) {
                                //查询用户是否有此模块的项目权限
                                List<String> userRoleByUserIdAndOaProjectDeployIdAndModuleType = newAuthorityServiceImpl.getUserRoleByUserIdAndOaProjectDeployIdAndModuleType(userId, deployId, AuthModuleEnum.PROJSETUP.getCode());
                                //为空则授权
                                if (CollectionUtils.isEmpty(userRoleByUserIdAndOaProjectDeployIdAndModuleType)){
                                    insertAuthAndDetail(userId, deployId, loginUser.getUserId(), AuthRoleEnum.XMGL3.getCode(), AuthModuleEnum.PROJSETUP.getCode());
                                }
                            }
                        }
                    }
                }
            }

            // 通用授权-项目详情页的项目名称和立项项目授权
            String moduleType1 = "";
            String roleType1 = "";
            String roleType2 = "";
            String moduleType2 = "";
            List<Map<String, Object>> moduleRoleType = new ArrayList<>();
            Map<String, Object> map1 = new HashMap<>();
            Map<String, Object> map2 = new HashMap<>();
            map1.put("moduleType1", AuthModuleEnum.PROJSETUP.getCode());
            map1.put("roleType1", AuthRoleEnum.XMGL1.getCode());
            moduleRoleType.add(map1);
            map2.put("moduleType2", AuthModuleEnum.PROJNAME.getCode());
            map2.put("roleType2", AuthRoleEnum.COMMON.getCode());
            moduleRoleType.add(map2);
            // 新增项目负责人
            List<Long> projectUser = projectForm.getProjectUser();
            if (!CollectionUtils.isEmpty(projectUser) && projectUser.size() > 0) {
                for (Long aLong : projectUser) {
                    XmglProjectUser xmglProjectUser = new XmglProjectUser();
                    SysUser userInfo = sysUserMapper.selectUserById(aLong);
                    xmglProjectUser.setProjectId(projectId);
                    xmglProjectUser.setUserId(aLong);
                    xmglProjectUser.setNickName(userInfo.getNickName());
                    xmglProjectUser.setContactId(userForm.getId());
                    xmglProjectUser.setUserFlag("0");
                    xmglProjectUser.setStatus("0");
                    xmglProjectUser.setCreateBy(loginUser.getUsername());
                    xmglProjectUser.setCreateTime(DateUtils.getNowDate());
                    logger.info("新增项目负责人数据入库" + xmglProjectUser.toString());
                    xmglProjectUserMapper.insertXmglProjectUser(xmglProjectUser);
                    /** 【业务信息配置-项目名称】中，如果新的项目负责人还没有本项目名称的权限，则项目负责人将拥有项目名称的权限。如果在其他维度已经授权拥有了该项目权限，则不处理 */
                    List<AuthorizedFeatureDetailDTO> projectRoleList = newAuthorityServiceImpl.getProjectRoleByOaProjectDeployIdAndModuleType(deployId, AuthModuleEnum.PROJSETUP.getCode());
                    List<Object> proPrincipalUserld = projectRoleList.stream().filter(t -> "7".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
                    proPrincipalUserld.removeAll(Collections.singleton(null));
                    if (CollectionUtils.isEmpty(proPrincipalUserld)) {
                        List<Long> featureUserIds = proPrincipalUserld.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
                        // 如果项目负责人没有权限，则授权
                        logger.info("如果项目负责人没有权限，则授权");
                        if (!featureUserIds.contains(aLong)) {

                            for (Map<String, Object> typeMap : moduleRoleType) {
                                if (null != typeMap.get("moduleType1") && typeMap.get("moduleType1").equals("PROJSETUP")) {
                                    moduleType1 = typeMap.get("moduleType1").toString();
                                    roleType1 = typeMap.get("roleType1").toString();
                                    this.insertAuthAndDetail(aLong, deployId, userForm.getUserId(), roleType1, moduleType1);
                                } else if (null != typeMap.get("moduleType2") && typeMap.get("moduleType2").equals("PROJNAME")) {
                                    moduleType2 = typeMap.get("moduleType2").toString();
                                    roleType2 = typeMap.get("roleType2").toString();
                                    this.insertAuthAndDetail(aLong, deployId, userForm.getUserId(), roleType2, moduleType2);
                                }
                            }
                        }
                    }
                    // 查询当前新的项目负责人的上级、上上级、祖级
                    List<Long> parentUserIds = new ArrayList<>();
                    parentUserIds = this.getParentUser(userInfo.getUserName(), parentUserIds);
                    if (!CollectionUtils.isEmpty(parentUserIds)) {
                        for (Long userId : parentUserIds) {
                            //查询用户是否有此模块的项目权限
                            List<String> userRoleByUserIdAndOaProjectDeployIdAndModuleType = newAuthorityServiceImpl.getUserRoleByUserIdAndOaProjectDeployIdAndModuleType(userId, deployId, AuthModuleEnum.PROJSETUP.getCode());
                            //为空则授权
                            if (CollectionUtils.isEmpty(userRoleByUserIdAndOaProjectDeployIdAndModuleType)){
                                insertAuthAndDetail(userId, deployId, loginUser.getUserId(), roleType1, moduleType1);
                            }
                        }
                    }
                }
                // 项目名称模块同样新增业务负责人
                OaEditApproveGeneralityUser financialStaffUser = new OaEditApproveGeneralityUser();
                // oaApplyType为4代表事项目名称配置功能
                financialStaffUser.setOaApplyType("4");
                financialStaffUser.setOaApplyId(projectForm.getDeployId());
                financialStaffUser.setUserFlag("1");
                financialStaffUser.setStatus("0");
                String nickName = sysUserMapper.selectUserById(userForm.getUserId()).getNickName();
                financialStaffUser.setCreateBy(nickName);
                financialStaffUser.setCreateTime(nowDate);
                financialStaffUser.setUpdateBy(nickName);
                financialStaffUser.setUpdateTime(nowDate);
                logger.info("新增项目时，项目名称模块同样新增业务负责人数据入库" + financialStaffUser.toString());
                oaEditApproveGeneralityUserMapper.deleteDataByAppIdAndUserType("4", projectForm.getDeployId(), "1");
                if (!CollectionUtils.isEmpty(projectUser) && projectUser.size() > 0) {
                    for (Long aLong : projectUser) {
                        financialStaffUser.setUserId(aLong);
                        logger.info("新增OA编辑审批用户,oa_edit_approve_generality_user表中插入项目负责人数据");
                        oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
                    }
                }
            }
            // 立项项目与项目名称关联关系表
            XmglDeployProject xmglDeployProject = new XmglDeployProject();
            xmglDeployProject.setDeployId(deployId);
            xmglDeployProject.setProjectId(projectId);
            xmglDeployProject.setCreateBy(loginUser.getUsername());
            xmglDeployProject.setCreateTime(DateUtils.getNowDate());
            xmglProjectMapper.insertDeployProjectInfo(xmglDeployProject);
        } catch (Exception e) {
            errorMessage = e.getMessage();
            e.printStackTrace();
        } finally {
            returnMap.put("code", 1);
            returnMap.put("projectId", projectForm.getId());
            String operMessage = "新增立项项目【" + projectForm.getProjectName() + "】,并提交审核";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJSETUP.getCode(), "", operMessage, 1, errorMessage, "");
        }
        return returnMap;
    }

    /**
     * 修改项目立项管理
     *
     * @param xmglProject 项目立项管理
     * @return 结果
     */
    @Override
    public int updateXmglProject(XmglProject xmglProject) {
        xmglProject.setUpdateTime(DateUtils.getNowDate());
        xmglProject.setUpdateBy(SecurityUtils.getUsername());
        xmglProject.setUpdateBr(SecurityUtils.getLoginUser().getUser().getNickName());
        return xmglProjectMapper.updateXmglProject(xmglProject);
    }

    /**
     * 批量删除项目立项管理
     *
     * @param ids 需要删除的项目立项管理主键
     * @return 结果
     */
    @Override
    public int deleteXmglProjectByIds(Long[] ids) {
        return xmglProjectMapper.deleteXmglProjectByIds(ids);
    }

    /**
     * 删除项目立项管理信息
     *
     * @param id 项目立项管理主键
     * @return 结果
     */
    @Override
    public int deleteXmglProjectById(Long id) {
        return xmglProjectMapper.deleteXmglProjectById(id);
    }

    /**
     * 获取下拉框数据
     *
     * @return
     */
    @Override
    public Map<String, Object> selectDataList() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        HashMap<String, Object> returnMap = new HashMap<>();
        HashMap<String, Object> addMap = new HashMap<>();
        addMap.put("label", "全部");
        addMap.put("value", "");
        // 获取项目负责人
        List<Map<String, Object>> userList = xmglProjectMapper.getUserList();
        // 查询渠道方下拉框列表
        Set<String> without = new HashSet<>();// 外部渠道方姓名
        Map<String, Object> interMap = new HashMap<>();// 内部渠道方map集合
        List<Map<String, Object>> interiorChannel = new ArrayList<>();// 内部渠道方
        List<String> nickNameList = new ArrayList<>();
        // 按照权限查询当前用户能看到的项目
        XmglProject xmglProject = new XmglProject();
        xmglProject.setScope("all");
        // 获取用户有哪些业务配置-项目名称的项目id
        List<Long> authProjectIds = getNewAuthorityServiceImpl.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.PROJSETUP.getCode());
        if (!CollectionUtils.isEmpty(authProjectIds)) {
            // 根据项目id查询对应的立项项目列表
            List<XmglProject> projects = xmglProjectDeployService.selectProjectInfoListByDeployIds(authProjectIds);

            if (!CollectionUtils.isEmpty(projects)) {
                for (XmglProject project : projects) {
                    if (null != project.getChannelType() && !project.getChannelType().equals("") && project.getChannelType().equals("1")) {
                        // 获取各个项目的内部渠道方用户集合
                        List<XmglProjectChannel> projectChannelList = projectChannelMapper.selectXmglProjectChannelListByProjectId(project.getId());
                        if (!CollectionUtils.isEmpty(projectChannelList)) {
                            Set<XmglProjectChannel> collect = projectChannelList.stream().collect(Collectors.toSet());
                            for (XmglProjectChannel projectChannel : collect) {
                                if (!nickNameList.contains(projectChannel.getNickName())) {
                                    Map<String, Object> channleMap = new HashMap<>();
                                    channleMap.put("label", projectChannel.getNickName());
                                    channleMap.put("value", projectChannel.getChannelId());
                                    nickNameList.add(projectChannel.getNickName());// 用来判断是否有重复的渠道方
                                    interiorChannel.add(channleMap);// 渠道方下拉框列表集合
                                    interMap.put(projectChannel.getNickName(), projectChannel.getChannelId());
                                }
                            }
                        }
                    } else if (null != project.getChannelType() && !project.getChannelType().equals("") && project.getChannelType().equals("2")) {
                        if (null != project.getChannelSide() && !project.getChannelSide().equals("")) {
                            if (!nickNameList.contains(project.getChannelSide())) {
                                nickNameList.add(project.getChannelSide());// 用来判断是否有重复的渠道方
                                without.add(project.getChannelSide());// 外部渠道方集合
                                Map<String, Object> channleMap = new HashMap<>();
                                channleMap.put("label", project.getChannelSide());
                                channleMap.put("value", project.getChannelSide());
                                interiorChannel.add(channleMap);// 渠道方下拉框列表集合
                            }
                        }
                    }
                }
            }
        }

        // 进度状态
        List<Map<String, Object>> list = XmglProjectEnum.getList();
        returnMap.put("projectStatus", list);

        // 当前登录用户是否是运营部岗位标识
        List<SysDept> operationDept = xmglProjectMapper.selectLoginUserDept(SecurityUtils.getLoginUser().getUserId(), "运营");
        Boolean operationFlag = false;
        if (!CollectionUtils.isEmpty(operationDept) && operationDept.size() > 0) {
            operationFlag = true;
        }
        // 当前登录用户是否是渠道部岗位标识
        List<SysDept> channelDept = xmglProjectMapper.selectLoginUserDept(SecurityUtils.getLoginUser().getUserId(), "渠道");
        Boolean channelFlag = false;
        if (!CollectionUtils.isEmpty(channelDept) && channelDept.size() > 0) {
            channelFlag = true;
        }
        // 当前用户是否是ceo岗位标识
        Boolean ceoFlag = false;
        List<SysDept> ceoList = xmglProjectMapper.selectLoginUserDept(SecurityUtils.getLoginUser().getUserId(), "ceo");
        List<SysPost> sysPosts = sysUserPostMapper.queryPostListByUserId(SecurityUtils.getLoginUser().getUserId());
        if (!CollectionUtils.isEmpty(sysPosts)) {
            for (SysPost sysPost : sysPosts) {
                if (sysPost.getPostCode().contains("ceo")) {
                    ceoFlag = true;
                }
            }
        }
        if (!CollectionUtils.isEmpty(ceoList) && ceoList.size() > 0) {
            ceoFlag = true;
        }

        // 锁定状态
        List<Map<String, Object>> lists = new ArrayList<>();
        Map<String, Object> lockStatus = new HashMap<>();
        lockStatus.put("0", "未锁定");
        lockStatus.put("1", "已锁定");
        // lists.add(addMap);
        lists.add(lockStatus);

        returnMap.put("isCeo", ceoFlag);// ceo岗位
        returnMap.put("isOperation", operationFlag);// 运营部岗位
        returnMap.put("isChannel", channelFlag);// 渠道部岗位
        returnMap.put("userList", userList);// 项目负责人
        returnMap.put("channelSideList", interiorChannel);// 渠道方下拉框列表
        returnMap.put("lockStatus", lists);// 锁定状态
        returnMap.put("channelList", without);// 外部渠道方


        return returnMap;
    }

    /**
     * 此方法未用到
     *
     * @return
     */
    @Override
    public Map<String, Object> selectAddDataList() {
        HashMap<String, Object> returnMap = new HashMap<>();
        // 获取资金方全称数据
        List<Map<String, Object>> fundFullDataList = xmglProjectMapper.getFundList();
        List<Map<String, Object>> fundShortDataList = xmglProjectMapper.getFundShortList();
        // 获取资产方全称数据
        List<Map<String, Object>> productFullDataList = xmglProjectMapper.getProductList();
        List<Map<String, Object>> productShortDataList = xmglProjectMapper.getProductShortList();
        // 获取担保公司数据
        List<Map<String, Object>> xmglMappingData = sysDictDataMapper.getXmglAllMappingData();
        // 获取渠道方
        List<Map<String, Object>> channelSide = xmglProjectMapper.getChannelSideList();

        returnMap.put("fundFullList", fundFullDataList);
        returnMap.put("productFullList", productFullDataList);
        returnMap.put("fundShortList", fundShortDataList);
        returnMap.put("productShortList", productShortDataList);
        returnMap.put("custFullList", xmglMappingData);
        returnMap.put("channelSideList", channelSide);
        return returnMap;
    }

    /**
     * 终止项目
     *
     * @param xmglProject xmgl项目
     * @param loginUser   登录用户
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    public Map<String, Object> terminateProject(XmglProject xmglProject, LoginUser loginUser) {
        HashMap<String, Object> returnHashMap = null;
        boolean b = false;
        int i = 0;
        String errorMsg = "";
        try {
            returnHashMap = new HashMap<>();
            b = false;
            xmglProject.setUpdateTime(DateUtils.getNowDate());
            xmglProject.setUpdateBr(loginUser.getUser().getNickName());
            xmglProject.setProjectStatus("12");
            i = xmglProjectMapper.updateXmglProject(xmglProject);
            // 更新项目名称-立项项目表状态为已终止
            XmglDeployProject xmglDeployProject = new XmglDeployProject();
            xmglDeployProject.setProjectId(xmglProject.getId());
            xmglDeployProject.setStatus("2");
            xmglDeployProject.setUpdateBy(loginUser.getUsername());
            xmglDeployProject.setUpdateTime(DateUtils.getNowDate());
            xmglProjectDeployService.updateProjectDeployStatus(xmglDeployProject);
        } catch (Exception e) {
            errorMsg = e.getMessage();
            e.printStackTrace();
        } finally {
            String operMessage = "";
            if (StringUtils.isNotEmpty(errorMsg)) {
                operMessage = "【" + xmglProject.getProjectName() + "】申请终止项目失败！";
            } else {
                operMessage = "审批通过，【" + xmglProject.getProjectName() + "】项目已被终止！";
            }
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), "", operMessage, 1, errorMsg, "");
            if (i > 0) {
                b = true;
            }
            returnHashMap.put("isok", b);
        }
        return returnHashMap;
    }


    /**
     * 项目上线
     * (暂未用到)
     *
     * @param xmglProject xmgl项目
     * @param loginUser   登录用户
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    public Map<String, Object> updateXmglProjectUp(XmglProject xmglProject, LoginUser loginUser) {
        HashMap<String, Object> returnHashMap = new HashMap<>();
        boolean b = false;
        xmglProject.setUpdateTime(DateUtils.getNowDate());
        xmglProject.setUpdateBr(loginUser.getUser().getNickName());
        xmglProject.setProjectStatus("5");
        xmglProject.setScheduleStatus("5");
        int i = xmglProjectMapper.updateXmglProject(xmglProject);
        String dynamicMsg = "项目已上线";
        this.insertDynamic(xmglProject.getId(), "0", dynamicMsg, loginUser.getUserId());
        if (i > 0) {
            b = true;
        }
        returnHashMap.put("isok", b);
        return returnHashMap;
    }

    /**
     * 修改立项项目信息
     *
     * @param xmglProject xmgl项目
     * @param loginUser   登录用户
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    public Map<String, Object> updateProAndUserAndDynamic(AddprojectVo xmglProject, LoginUser loginUser) {
        HashMap<String, Object> returnMap = new HashMap<>();
        // 传过来的项目信息
        XmglProject projectForm = xmglProject.getProjectForm();
        // 传过来的user信息
        XmglContactWay userform = xmglProject.getUserform();
        int i = this.updateProjectMethods(projectForm, userform, loginUser);
        returnMap.put("code", i);
        returnMap.put("projectId", projectForm.getId());
        return returnMap;

    }

    /**
     * 认领立项项目
     *
     * @param addprojectVo xmgl项目
     * @param loginUser    登录用户
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    public Map<String, Object> updateClaimProject(AddprojectVo addprojectVo, LoginUser loginUser) {
        HashMap<String, Object> returnHashMap = new HashMap<>();
        boolean b = false;
        /**
         * 认领项目：
         * 项目负责人：
         * 只能选择运营部岗位的用户，可选多人
         * 如果认领人是运营部岗位用户，则此处显示认领人本人姓名，可以继续选择他人，可以删除自己
         * 如果认领人是渠道部员工岗位，则此处为空
         * 渠道方：
         * 只能选择渠道部岗位的用户，则此处为空
         * 如果认领人是运营部岗位的用户，则此处默认为空
         * 如果认领人是渠道部员工岗位，则此处自动选择内部，内部用户为认领人本人。可以继续增加或删除人员，可以删除自己
         * 联系方式
         * 认领人需要输入一套新的联系方式信息
         * 资产方与资金方，至少有一方的姓名+电话信息是有输入内容的
         *
         * 项目认领成功后：
         *          * 认领后，项目进度将重置，从对接中开始；原项目负责人角色身份被删除
         *          * 【业务信息配置-项目名称】中，业务责任人也同步替换为此项目负责人，原业务责任人删除
         *          * 【业务信息配置-项目名称】中，如果新的项目负责人还没有本项目名称的权限，则项目负责人将拥有项目名称的权限。如果负责人在其他维度已经被授权拥有了该项目名称权限，则不用处理
         *          * 认领后项目进度重置为对接中（第1个进度），重新开始90天倒计时。
         */
        // 项目信息
        XmglProject projectForm = addprojectVo.getProjectForm();
        // 联系人信息
        XmglContactWay userform = addprojectVo.getUserform();

        // 获取认领前的渠道方类型
        XmglProject project = xmglProjectMapper.selectXmglProjectById(projectForm.getId());

        Long deployId = projectForm.getDeployId();

        // 将新增时录入的联系人改为旧联系人
        xmglContactWayMapper.updateContactWayStatusByProjectId(projectForm.getId());
        // 插入新录入的联系人信息
        userform.setProjectId(projectForm.getId());
        userform.setUserId(userform.getUserId());// 录入人id
        userform.setCreateTime(DateUtils.getNowDate());
        userform.setCreateBr(loginUser.getUser().getNickName());
        xmglContactWayMapper.insertXmglContactWay(userform);

        String channelType = projectForm.getChannelType();// 渠道方类型(1内部渠道方 2外部渠道方 )

        // 如果认领前的渠道方类型是内部渠道方，则删除权限表中该用户对该项目关联的项目名称权限
        if (project.getChannelType().equals("1")) {
            // 获取当前项目最新的渠道方
            List<XmglProjectChannel> channelList = projectChannelMapper.selectXmglProjectChannelListByProjectId(projectForm.getId());
            if (!CollectionUtils.isEmpty(channelList)) {
                // 清除权限
                this.cancelAuthority(channelList, deployId);
            }
        }

        // 将之前录入的渠道方改成旧渠道方
        // xmglContactWayMapper.updateXmglContactWayByProjectId(projectForm.getId());
        projectChannelMapper.updateStatusByProjectId(projectForm.getId());
        if (channelType.equals("1")) {
            // 1是内部渠道方，插入新数据
            List<XmglProjectChannel> list = new ArrayList<>();
            List<Long> channelForm = projectForm.getChannelForm();
            for (Long aLong : channelForm) {
                XmglProjectChannel projectChannel = new XmglProjectChannel();
                projectChannel.setChannelId(aLong);
                projectChannel.setProjectId(projectForm.getId());
                projectChannel.setContactId(userform.getId());
                projectChannel.setStatus("0");
                projectChannel.setCreateBy(loginUser.getUsername());
                projectChannel.setCreateTime(DateUtils.getNowDate());
                SysUser sysUser = sysUserMapper.selectUserById(aLong);
                projectChannel.setNickName(sysUser.getNickName());
                list.add(projectChannel);

                /**
                 * 内部渠道方授权授权业务相关模块
                 * 如果内部渠道方用户在该模块没有任何一种权限，默认授予用户此模块的查看权限
                 * 如果该用户已在此模块有其他任何一种权限，则不做处理
                 */
                this.getAuthority(aLong, deployId, userform.getUserId());

                /*
                 *   如果该渠道方有该项目的任何一种权限，则不做处理
                 *   如果内部渠道方用户没有这个项目的任何一种角色权限(业务管理员、项目负责人、查看权限)，默认给该用户'查看权限'
                 */
                // List<AuthorizedFeatureDetailDTO> projectRoleList = newAuthorityServiceImpl.getProjectRoleByOaProjectDeployIdAndModuleType(deployId, AuthModuleEnum.PROJNAME.getCode());
                // List<Object> proPrincipalUserld = projectRoleList.stream().filter(t -> "88".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(bb -> bb.get("featureUserId")).collect(Collectors.toList());
                // List<Object> proPrincipalUserld1 = projectRoleList.stream().filter(t -> "7".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(bb -> bb.get("featureUserId")).collect(Collectors.toList());
                // List<Object> proPrincipalUserld2 = projectRoleList.stream().filter(t -> "10".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(bb -> bb.get("featureUserId")).collect(Collectors.toList());
                // proPrincipalUserld.removeAll(Collections.singleton(null));
                // proPrincipalUserld1.removeAll(Collections.singleton(null));
                // proPrincipalUserld2.removeAll(Collections.singleton(null));
                // if (!CollectionUtils.isEmpty(proPrincipalUserld) || !CollectionUtils.isEmpty(proPrincipalUserld1) || !CollectionUtils.isEmpty(proPrincipalUserld2)){
                //    List<Long> featureUserIds = proPrincipalUserld.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
                //    //如果渠道方没有三种权限中的任何一种，则授权查看权限
                //    if (!featureUserIds.contains(aLong) && !proPrincipalUserld1.contains(aLong) && !proPrincipalUserld2.contains(aLong)){
                //        this.insertAuthAndDetail(aLong,deployId,userform.getUserId(),AuthRoleEnum.XMGL3.getCode());
                //    }
                //}

                // 查询当前新的内部渠道方的上级、上上级、祖级
                List<Long> parentUserIds = new ArrayList<>();
                parentUserIds = this.getParentUser(sysUser.getUserName(), parentUserIds);
                if (!CollectionUtils.isEmpty(parentUserIds)) {
                    for (Long userId : parentUserIds) {
                        //查询用户是否有此模块的项目权限
                        List<String> userRoleByUserIdAndOaProjectDeployIdAndModuleType = newAuthorityServiceImpl.getUserRoleByUserIdAndOaProjectDeployIdAndModuleType(userId, deployId, AuthModuleEnum.PROJSETUP.getCode());
                        //为空则授权
                        if (CollectionUtils.isEmpty(userRoleByUserIdAndOaProjectDeployIdAndModuleType)){
                            insertAuthAndDetail(userId, deployId, loginUser.getUserId(), AuthRoleEnum.XMGL3.getCode(), AuthModuleEnum.PROJSETUP.getCode());
                        }
                    }
                }
            }
            projectChannelMapper.insertXmglProjectChannelList(list);
        }
        // 认领后，项目进度将重置，从对接中开始,恢复90天倒计时，且项目状态改为已锁定
        projectForm.setProjectStatus("1");
        projectForm.setScheduleStatus("1");
        projectForm.setLockStatus("0");
        projectForm.setClaimed("2");// 认领状态 1未认领 2已认领
        projectForm.setUserId(projectForm.getClaimantId());// 认领申请人(提交认领申请时，此字段保存申请人id)

        // 审批通过后调用此接口的时间，就是锁定开始时间
        projectForm.setBeginTime(DateUtils.getNowDate());
        // 计算90天后的时间
        Date date90 = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 90L);
        // Date date90 = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 1L);
        // 重新插入到期时间
        projectForm.setOverTime(date90);
        projectForm.setUpdateStatusTime(DateUtils.getNowDate());
        int i = xmglProjectMapper.updateXmglProject(projectForm);

        // 获取项目的当前项目负责人,将状态改为失效
        List<XmglProjectUser> oldProjectUserList = xmglProjectUserMapper.selectXmglProjectUserListByProjectId(projectForm.getId());
        List<XmglProjectChannel> channelList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(oldProjectUserList)) {
            // 循环获取项目负责人权限表数据
            for (XmglProjectUser xmglProjectUser : oldProjectUserList) {
                // 循环获取当前项目的项目负责人
                XmglProjectChannel userIdDo = new XmglProjectChannel();
                userIdDo.setChannelId(xmglProjectUser.getUserId());
                channelList.add(userIdDo);
            }
            // 取消旧的项目负责人权限时也取消其下级授权
            cancelAuthority(channelList, deployId);
        }

        // 根据立项项目id先将新增时录入的负责人信息改成旧负责人
        xmglProjectUserMapper.updateXmglProjectUserByProjectId(projectForm.getId());
        // 通用授权-项目详情页的项目名称和立项项目授权
        String moduleType1 = "";
        String roleType1 = "";
        String roleType2 = "";
        String moduleType2 = "";
        List<Map<String, Object>> moduleRoleType = new ArrayList<>();
        Map<String, Object> map1 = new HashMap<>();
        Map<String, Object> map2 = new HashMap<>();
        map1.put("moduleType1", AuthModuleEnum.PROJSETUP.getCode());
        map1.put("roleType1", AuthRoleEnum.XMGL1.getCode());
        moduleRoleType.add(map1);
        map2.put("moduleType2", AuthModuleEnum.PROJNAME.getCode());
        map2.put("roleType2", AuthRoleEnum.COMMON.getCode());
        moduleRoleType.add(map2);
        // 插入新负责人信息
        List<Long> projectUser = projectForm.getProjectUser();
        List<XmglProjectUser> projectUserList = new ArrayList<>();
        for (Long aLong : projectUser) {
            XmglProjectUser xmglProjectUser = new XmglProjectUser();
            xmglProjectUser.setUserId(aLong);
            SysUser sysUser = sysUserMapper.selectUserById(aLong);
            xmglProjectUser.setNickName(sysUser.getNickName());
            xmglProjectUser.setProjectId(projectForm.getId());
            xmglProjectUser.setUserFlag("0");
            xmglProjectUser.setStatus("0");
            xmglProjectUser.setContactId(userform.getId());// 联系人表主键id
            xmglProjectUser.setCreateTime(DateUtils.getNowDate());
            xmglProjectUser.setCreateBy(loginUser.getUsername());
            projectUserList.add(xmglProjectUser);
            /** 【业务信息配置-项目名称】中，如果新的项目负责人还没有本项目名称的权限，则项目负责人将拥有项目名称的权限。如果在其他维度已经授权拥有了该项目权限，则不处理 */
            List<AuthorizedFeatureDetailDTO> projectRoleList = newAuthorityServiceImpl.getProjectRoleByOaProjectDeployIdAndModuleType(deployId, AuthModuleEnum.PROJSETUP.getCode());
            // 项目负责人
            List<Object> proPrincipalUserld = projectRoleList.stream().filter(t -> "7".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(c -> c.get("featureUserId")).collect(Collectors.toList());
            proPrincipalUserld.removeAll(Collections.singleton(null));
            if (!CollectionUtils.isEmpty(proPrincipalUserld)) {
                List<Long> featureUserIds = proPrincipalUserld.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
                // 如果项目负责人没有权限，则授权
                if (!featureUserIds.contains(aLong)) {
                    for (Map<String, Object> typeMap : moduleRoleType) {
                        if (null != typeMap.get("moduleType1") && typeMap.get("moduleType1").equals("PROJSETUP")) {
                            moduleType1 = typeMap.get("moduleType1").toString();
                            roleType1 = typeMap.get("roleType1").toString();
                            this.insertAuthAndDetail(aLong, deployId, aLong, roleType1, moduleType1);
                        } else if (null != typeMap.get("moduleType2") && typeMap.get("moduleType2").equals("PROJNAME")) {
                            moduleType2 = typeMap.get("moduleType2").toString();
                            roleType2 = typeMap.get("roleType2").toString();
                            this.insertAuthAndDetail(aLong, deployId, aLong, roleType2, moduleType2);
                        }
                    }
                }
            }

            // 查询当前新的项目负责人的上级、上上级、祖级
            List<Long> parentUserIds = new ArrayList<>();
            parentUserIds = this.getParentUser(sysUser.getUserName(), parentUserIds);
            if (!CollectionUtils.isEmpty(parentUserIds)) {
                for (Long userId : parentUserIds) {
                    //查询用户是否有此模块的项目权限
                    List<String> userRoleByUserIdAndOaProjectDeployIdAndModuleType = newAuthorityServiceImpl.getUserRoleByUserIdAndOaProjectDeployIdAndModuleType(userId, deployId, AuthModuleEnum.PROJSETUP.getCode());
                    //为空则授权
                    if (CollectionUtils.isEmpty(userRoleByUserIdAndOaProjectDeployIdAndModuleType)){
                        insertAuthAndDetail(userId, deployId, loginUser.getUserId(), roleType1, moduleType1);
                    }
                }
            }
        }
        // 新项目负责人入库
        xmglProjectUserMapper.insertXmglProjectUserList(projectUserList);

        //【业务信息配置-项目名称】中，业务责任人也同步替换为此项目负责人，原业务责任人删除
        oaEditApproveGeneralityUserMapper.deleteDataByAppIdAndUserType("4", deployId, "1");// 删除旧业务负责人
        // 添加新业务负责人
        OaEditApproveGeneralityUser financialStaffUser = new OaEditApproveGeneralityUser();
        // oaApplyType为4代表事项目名称配置功能
        financialStaffUser.setOaApplyType("4");
        financialStaffUser.setOaApplyId(deployId);
        financialStaffUser.setUserFlag("1");
        financialStaffUser.setStatus("0");
        financialStaffUser.setCreateBy(loginUser.getUser().getNickName());
        financialStaffUser.setCreateTime(new Date());
        financialStaffUser.setUpdateBy(loginUser.getUser().getNickName());
        financialStaffUser.setUpdateTime(new Date());
        for (Long userId : projectUser) {
            // 用户为业务
            financialStaffUser.setUserId(userId);
            oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
        }

        // 生成项目动态
        SysUser user = sysUserMapper.selectUserById(userform.getUserId());
        String operMessage = "审批通过，[" + projectForm.getProjectName() + "]已被用户[" + user.getNickName() + "]认领";
        sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJSETUP.getCode(), FunctionNodeEnum.INSPROJSETUP.getCode(), operMessage, 1, "", "");
        if (i > 0) {
            b = true;
        }
        returnHashMap.put("isok", b);
        return returnHashMap;
    }

    @Override
    public Map<String, Object> getDetails(Long id, LoginUser loginUser) {

        HashMap<String, Object> returnHashMap = new HashMap<>();
        List<XmglContactWay> xmglContactWays = new ArrayList<>();

        // 判断是否是管理员角色
        boolean b = false;
        // 判断当前登录人权限如果是管理员A则不需要传登陆人id查询联系方式
        List<SysRole> roles = loginUser.getUser().getRoles();
        for (SysRole role : roles) {
            if (role.getRoleKey().equals("XMGLROLEA")) {
                b = true;
            }
        }

        if (b) {
            xmglContactWays = xmglContactWayMapper.selectuserList(id, null);
        } else {
            // 查关系表中的联系信息id
            XmglProjectUser xmglProjectUser = xmglProjectUserMapper.queryByProIdAndUserId(id, loginUser.getUserId());
            if (null != xmglProjectUser) {
                XmglContactWay xmglContactWay = xmglContactWayMapper.selectXmglContactWayById(xmglProjectUser.getContactId());
                xmglContactWays.add(xmglContactWay);
            }

//            xmglContactWays = xmglContactWayMapper.selectuserList(id, loginUser.getUserId());
        }
        XmglProject xmglProject = xmglProjectMapper.selectXmglProjectById(id);
        XmglDynamic xmglDynamic = new XmglDynamic();
        xmglDynamic.setProjectId(id);
        xmglDynamic.setStatus("0");
        List<XmglDynamic> xmglDynamics = dynamicMapper.selectXmglDynamicList(xmglDynamic);
        returnHashMap.put("projectForm", xmglProject);
        returnHashMap.put("userForm", xmglContactWays);
        returnHashMap.put("dynamics", xmglDynamics);

        return returnHashMap;
    }

    @Override
    public int deleteDynamic(XmglDynamic xmglDynamic, LoginUser loginUser) {
        Date nowDate = DateUtils.getNowDate();
        XmglDynamic queryMmglDynamic = dynamicMapper.selectXmglDynamicById(xmglDynamic.getId());
        // 修改状态为禁用状态
        xmglDynamic.setStatus("1");
        xmglDynamic.setUpdateBr(loginUser.getUser().getNickName());
        xmglDynamic.setUpdateTime(nowDate);

        // 新增动态
        String dynamicMsg = "删除已发布动态";
        this.insertDynamic(queryMmglDynamic.getProjectId(), "0", dynamicMsg, loginUser.getUserId());

        return dynamicMapper.updateXmglDynamic(xmglDynamic);

    }

    @Override
    public int deleteproject(XmglProject xmglProject, LoginUser loginUser) {
        Date nowDate = DateUtils.getNowDate();
        xmglProject.setStatus("1");
        xmglProject.setUpdateTime(nowDate);
        xmglProject.setUpdateBr(loginUser.getUser().getNickName());


        // 新增动态
        String dynamicMsg = "删除项目";
        this.insertDynamic(xmglProject.getId(), "0", dynamicMsg, loginUser.getUserId());

        return xmglProjectMapper.updateXmglProject(xmglProject);

    }

    @Override
    public Map<String, Object> getLoginUserIsAdmin(LoginUser loginUser) {
        HashMap<String, Object> returnMap = new HashMap<>();
        // 判断是否是管理员角色
        boolean b = false;
        // 判断当前登录人权限如果是管理员A则不需要传登陆人id查询联系方式
        List<SysRole> roles = loginUser.getUser().getRoles();
        for (SysRole role : roles) {
            if (role.getRoleKey().equals("XMGLROLEA") || role.getRoleKey().equals("admin")) {
                b = true;
            }
        }
        returnMap.put("isAdmin", b);
        return returnMap;
    }

    @Override
    public Map<String, Object> addProjectDynamic(AddprojectVo xmglProject, LoginUser loginUser) {
        HashMap<String, Object> returnMap = new HashMap<>();
        Long id = xmglProject.getProjectForm().getId();
        if (!StringUtils.equals("", xmglProject.getDynamicMsg())) {
            this.insertDynamic(id, "1", xmglProject.getDynamicMsg(), loginUser.getUserId());
        }
        returnMap.put("code", 1);
        returnMap.put("projectId", id);
        return returnMap;
    }

    @Override
    public List<Map<String, Object>> getUserList() {
        // 获取 XMGLROLEA XMGLROLEC角色的用户
        List<Map<String, Object>> xmglRoleKeyList = sysUserRoleMapper.getXMGLRoleKeyList();
        return xmglRoleKeyList;
    }

    /**
     * 变更项目负责人
     *
     * @param loginUser
     * @param xmglProjectUser
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> updateprincipalData(LoginUser loginUser, XmglProjectUser xmglProjectUser) {
        HashMap<String, Object> returnMap = new HashMap<>();
        Long nextId = 0L;
        // 项目id
        Long projectId = xmglProjectUser.getProjectId();
        // 项目名称模块的项目id
        Long deployId = xmglProjectUser.getDeployId();

        // 查询该项目现有的项目负责人集合
        List<XmglProjectUser> projectUserList = xmglProjectUserMapper.selectXmglProjectUserListByProjectId(projectId);
        // 获取负责人对应的联系人表主键id集合
        if (!CollectionUtils.isEmpty(projectUserList)) {
            Set<Long> collect = projectUserList.stream().map(XmglProjectUser::getContactId).collect(Collectors.toSet());
            nextId = collect.iterator().next();
        }

        // 获取项目的当前项目负责人
        List<XmglProjectUser> oldProjectUserList = xmglProjectUserMapper.selectXmglProjectUserListByProjectId(projectId);
        List<XmglProjectChannel> channelList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(oldProjectUserList)) {
            // 循环获取项目负责人权限表数据
            for (XmglProjectUser projectUser : oldProjectUserList) {
                // 循环获取当前项目的项目负责人
                XmglProjectChannel userIdDo = new XmglProjectChannel();
                userIdDo.setChannelId(projectUser.getUserId());
                channelList.add(userIdDo);
            }
            // 取消旧的项目负责人权限时也取消其下级授权
            cancelAuthority(channelList, deployId);
        }

        // 将现有的项目负责人修改成旧负责人
        xmglProjectUserMapper.updateXmglProjectUserByProjectId(projectId);

        /** 新项目负责人入库 */
        // 组装通用授权-立项项目管理和项目名称页签的授权数据
        String moduleType1 = "";
        String roleType1 = "";
        String roleType2 = "";
        String moduleType2 = "";
        List<Map<String, Object>> moduleRoleType = new ArrayList<>();
        Map<String, Object> map1 = new HashMap<>();
        Map<String, Object> map2 = new HashMap<>();
        map1.put("moduleType1", AuthModuleEnum.PROJSETUP.getCode());
        map1.put("roleType1", AuthRoleEnum.XMGL1.getCode());
        moduleRoleType.add(map1);
        map2.put("moduleType2", AuthModuleEnum.PROJNAME.getCode());
        map2.put("roleType2", AuthRoleEnum.COMMON.getCode());
        moduleRoleType.add(map2);
        // 新的项目负责人id集合
        List<Long> newUserIds = xmglProjectUser.getNewUserIds();
        List<XmglProjectUser> xmglProjectUserList = new ArrayList<>();
        for (Long newUserId : newUserIds) {
            XmglProjectUser projectUser = new XmglProjectUser();
            SysUser sysUser = sysUserMapper.selectUserById(newUserId);
            projectUser.setUserId(newUserId);
            projectUser.setNickName(sysUser.getNickName());
            projectUser.setProjectId(xmglProjectUser.getProjectId());
            projectUser.setUserFlag("0");
            projectUser.setStatus("0");
            projectUser.setContactId(nextId);// 联系人表主键id
            projectUser.setCreateTime(DateUtils.getNowDate());
            projectUser.setCreateBy(loginUser.getUsername());
            xmglProjectUserList.add(projectUser);
            /** 【业务信息配置-项目名称】中，如果新的项目负责人还没有本项目名称的权限，则项目负责人将拥有项目名称的权限。如果在其他维度已经授权拥有了该项目权限，则不处理 */
            List<AuthorizedFeatureDetailDTO> projectRoleList = newAuthorityServiceImpl.getProjectRoleByOaProjectDeployIdAndModuleType(deployId, AuthModuleEnum.PROJSETUP.getCode());
            // 项目负责人
            List<Object> proPrincipalUserld = projectRoleList.stream().filter(t -> "7".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(c -> c.get("featureUserId")).collect(Collectors.toList());
            proPrincipalUserld.removeAll(Collections.singleton(null));
            if (!CollectionUtils.isEmpty(proPrincipalUserld)) {
                List<Long> featureUserIds = proPrincipalUserld.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
                // 如果项目负责人没有权限，则授权
                if (!featureUserIds.contains(newUserId)) {

                    for (Map<String, Object> typeMap : moduleRoleType) {
                        if (null != typeMap.get("moduleType1") && typeMap.get("moduleType1").equals("PROJSETUP")) {
                            moduleType1 = typeMap.get("moduleType1").toString();
                            roleType1 = typeMap.get("roleType1").toString();
                            this.insertAuthAndDetail(newUserId, deployId, loginUser.getUserId(), roleType1, moduleType1);
                        } else if (null != typeMap.get("moduleType2") && typeMap.get("moduleType2").equals("PROJNAME")) {
                            moduleType2 = typeMap.get("moduleType2").toString();
                            roleType2 = typeMap.get("roleType2").toString();
                            this.insertAuthAndDetail(newUserId, deployId, loginUser.getUserId(), roleType2, moduleType2);
                        }
                    }
                }
            }
            // 查询当前新的项目负责人的上级、上上级、祖级
            List<Long> parentUserIds = new ArrayList<>();
            parentUserIds = this.getParentUser(sysUser.getUserName(), parentUserIds);
            if (!CollectionUtils.isEmpty(parentUserIds)) {
                for (Long userId : parentUserIds) {
                    //查询用户是否有此模块的项目权限
                    List<String> userRoleByUserIdAndOaProjectDeployIdAndModuleType = newAuthorityServiceImpl.getUserRoleByUserIdAndOaProjectDeployIdAndModuleType(userId, deployId, AuthModuleEnum.PROJSETUP.getCode());
                    //为空则授权
                    if (CollectionUtils.isEmpty(userRoleByUserIdAndOaProjectDeployIdAndModuleType)){
                        insertAuthAndDetail(userId, deployId, loginUser.getUserId(), roleType1, moduleType1);
                    }
                }
            }
        }
        xmglProjectUserMapper.insertXmglProjectUserList(xmglProjectUserList);
        // 删除项目名称模块配置的旧业务负责人
        oaEditApproveGeneralityUserMapper.deleteDataByAppIdAndUserType("4", deployId, "1");
        // 添加新业务负责人
        OaEditApproveGeneralityUser financialStaffUser = new OaEditApproveGeneralityUser();
        // oaApplyType为4代表事项目名称配置功能
        financialStaffUser.setOaApplyType("4");
        financialStaffUser.setOaApplyId(deployId);
        financialStaffUser.setUserFlag("1");
        financialStaffUser.setStatus("0");
        financialStaffUser.setCreateBy(loginUser.getUser().getNickName());
        financialStaffUser.setCreateTime(new Date());
        financialStaffUser.setUpdateBy(loginUser.getUser().getNickName());
        financialStaffUser.setUpdateTime(new Date());
        for (Long userId : newUserIds) {
            financialStaffUser.setUserId(userId);
            oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
        }

        // 组装项目动态
        String oldUsers = "";
        String subOldUsers = "";
        // 旧项目负责人不为空，组装通知内容。
        if (!CollectionUtils.isEmpty(projectUserList)) {
            for (XmglProjectUser projectUser : projectUserList) {
                SysUser sysUser = sysUserMapper.selectUserById(projectUser.getUserId());
                oldUsers = oldUsers + sysUser.getNickName() + ",";
            }
        }
        if (null != oldUsers && !oldUsers.equals("")) {
            subOldUsers = oldUsers.substring(0, oldUsers.lastIndexOf(","));
        }
        String newUsers = "";
        for (Long newUserId : newUserIds) {
            SysUser sysUser = sysUserMapper.selectUserById(newUserId);
            newUsers = newUsers + sysUser.getNickName() + ",";
        }
        String dynamic = "";
        String subNewUsers = newUsers.substring(0, newUsers.lastIndexOf(","));
        if (!subOldUsers.equals("")) {
            dynamic = "项目负责人由【" + subOldUsers + "】修改为【" + subNewUsers + "】";
        } else {
            dynamic = "项目负责人修改为【" + subNewUsers + "】";
        }

        // 添加动态
        int i = this.insertDynamic(xmglProjectUser.getProjectId(), "1", dynamic, loginUser.getUserId());

        // 发送代办通知
        this.sendNotify(projectUserList, xmglProjectUser);

        if (i > 0) {
            returnMap.put("isok", true);
        } else {
            returnMap.put("isok", false);
        }
        return returnMap;
    }

    /**
     * 递归查询用户的上级
     *
     * @param userName
     * @return
     */
    private List<Long> getParentUser(String userName, List<Long> parentUserIds) {

        if (StringUtils.isNotEmpty(userName)) {
            PersonnelArchivesVo personnelArchivesVo = personnelArchivesMapper.queryParentPersonnelBySysName(userName);
            if (!Objects.isNull(personnelArchivesVo)) {
                Long directSuperior = personnelArchivesVo.getDirectSuperior();
                if (null != directSuperior){
                    SysUser sysUser = sysUserMapper.selectUserById(directSuperior);
                    parentUserIds.add(sysUser.getUserId());
                    getParentUser(sysUser.getUserName(), parentUserIds);
                }
            }
        }
        return parentUserIds;
    }

    /**
     * 变更负责人时，给新项目负责人和旧项目负责人发送通知代办
     *
     * @param projectUserList 项目现有的项目负责人集合
     * @param xmglProjectUser 项目信息
     */
    private void sendNotify(List<XmglProjectUser> projectUserList, XmglProjectUser xmglProjectUser) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<Long> newUserIds = xmglProjectUser.getNewUserIds();
        String newUserName = "";
        Long projectId = 0L;
        if (null != xmglProjectUser.getProjectId()) {
            projectId = xmglProjectUser.getProjectId();
        }
        // 给新负责人发通知
        if (!CollectionUtils.isEmpty(newUserIds) && newUserIds.size() > 0) {
            for (Long newUserId : newUserIds) {
                SysUser sysUser = sysUserMapper.selectUserById(newUserId);
                newUserName = newUserName + sysUser.getNickName() + ",";
                // 组装通知消息
                TopNotify topNotify = new TopNotify();
                topNotify.setNotifyModule("项目立项管理");
                topNotify.setNotifyType("0");
                topNotify.setOaNotifyType("5");
                topNotify.setOaApplyId(projectId);
                // 查询项目信息
                String message = "";
                XmglProject project = xmglProjectMapper.selectXmglProjectById(projectId);
                if (null != project.getProjectName() && !project.getProjectName().equals("")) {
                    message = "[" + user.getNickName() + "]已指定您作为项目[" + project.getProjectName() + "]的项目负责人";
                }

                topNotify.setNotifyMsg(message);
                topNotify.setDisposeUser(newUserId);
                topNotify.setUrl("/xmgl/project/updateprincipal");
                topNotify.setProjectId(-1L);
                topNotify.setIncomeId(-1L);
                topNotify.setViewFlag("0");
                topNotify.setCreateBy(user.getNickName());
                topNotify.setCreateTime(DateUtils.getNowDate());
                topNotify.setUpdateBy(user.getNickName());
                topNotify.setUpdateTime(DateUtils.getNowDate());
                topNotifyService.insertTopNotify(topNotify);
            }
        }
        // 截取新负责人姓名
        String newName = newUserName.substring(0, newUserName.lastIndexOf(","));
        // 给旧负责人发通知
        String message = "";
        if (!CollectionUtils.isEmpty(projectUserList)) {
            for (XmglProjectUser projectUser : projectUserList) {
                // 组装通知消息
                TopNotify topNotify = new TopNotify();
                topNotify.setNotifyModule("项目立项管理");
                topNotify.setNotifyType("0");
                topNotify.setOaNotifyType("5");
                topNotify.setOaApplyId(projectId);
                // 查询项目信息
                XmglProject project = xmglProjectMapper.selectXmglProjectById(projectUser.getProjectId());
                message = "[" + user.getNickName() + "]已将项目[" + project.getProjectName() + "]变更项目负责人为[" + newName + "]";
                topNotify.setNotifyMsg(message);
                topNotify.setDisposeUser(projectUser.getUserId());
                topNotify.setUrl("/xmgl/project/updateprincipal");
                topNotify.setProjectId(-1L);
                topNotify.setIncomeId(-1L);
                topNotify.setViewFlag("0");
                topNotify.setCreateBy(user.getNickName());
                topNotify.setCreateTime(DateUtils.getNowDate());
                topNotify.setUpdateBy(user.getNickName());
                topNotify.setUpdateTime(DateUtils.getNowDate());
                topNotifyService.insertTopNotify(topNotify);
            }
        }
        sysOperLogService.insertOperLogMessage(AuthModuleEnum.USERRE.getCode(), "", message, 1, "", "");

    }

    @Override
    public Map<String, Object> isOldPrincipalData(XmglProjectUser xmglProjectUser, LoginUser loginUser) {
        HashMap<String, Object> returnMap = new HashMap<>();
        // 通过项目id和当前登录人id判断当前这个人是否是新的负责人
        XmglProjectUser xmglProjectUser2 = new XmglProjectUser();
        xmglProjectUser2.setProjectId(xmglProjectUser.getProjectId());
        xmglProjectUser2.setUserId(loginUser.getUserId());

        List<XmglProjectUser> xmglProjectUsers = xmglProjectUserMapper.selectXmglProjectUserList(xmglProjectUser2);

//        XmglProjectUser xmglProjectUser1 = xmglProjectUserMapper.queryByProIdAndUserId(xmglProjectUser.getProjectId(),loginUser.getUserId());
        if (xmglProjectUsers.size() == 0 || xmglProjectUsers.get(0).getUserFlag().equals("0")) {
            returnMap.put("isNew", false);
        } else if (xmglProjectUsers.get(0).getUserFlag().equals("1")) {
            returnMap.put("isNew", true);
        }


        return returnMap;
    }

    /**
     * 更新旧的项目负责人
     * 未使用
     *
     * @param xmglProjectUser xmgl项目用户
     */
    public Long updatePrincipalOld(XmglProjectUser xmglProjectUser, LoginUser loginUser) {

        // 根据项目id 和 负责人角色为新负责人为条件在关系表查询 如果查询不到则根据项目id查询当前项目管理表的数据插入一条为 就负责人的数据
        XmglProject xmglProject = xmglProjectMapper.selectXmglProjectById(xmglProjectUser.getProjectId());
        // 得到联系方式id 通过负责人id和项目id
        XmglContactWay xmglContactWay = xmglContactWayMapper.queryByUserIdAndPid(xmglProject.getUserId(), xmglProject.getId());
        // 直接根据项目id查询出当前负责人id 然后根据当前负责人id和项目id查询关系表 如果有数据 则把项目角色更改为旧负责人 如果没有则新增一条就负责人
        XmglProjectUser xmglProjectUser3 = xmglProjectUserMapper.queryByProIdAndUserId(xmglProject.getId(), xmglProject.getUserId());
        // 证明没有数据  新增一条当前项目负责人为旧负责人的数据 ，在添加一条新负责人的数据 并且旧负责人的联系方式id的所属人id改为新负责人的id
        Date nowDate = DateUtils.getNowDate();
        if (null == xmglProjectUser3) {
            // 新增以当前项目负责人为旧负责人的数据
            XmglProjectUser xmglProjectUser1 = new XmglProjectUser();
            xmglProjectUser1.setProjectId(xmglProjectUser.getProjectId());
            xmglProjectUser1.setUserFlag("1");
            xmglProjectUser1.setUserId(xmglProject.getUserId());
            XmglContactWay xmglContactWay1 = new XmglContactWay();
            if (null == xmglContactWay) {

                xmglContactWay1.setProjectId(xmglProject.getId());
                xmglContactWay1.setUserId(xmglProject.getUserId());
                xmglContactWay1.setCreateTime(nowDate);
                xmglContactWay1.setUpdateTime(nowDate);
                xmglContactWay1.setCreateBr(loginUser.getUser().getNickName());
                xmglContactWayMapper.insertXmglContactWay(xmglContactWay1);
            }
            if (null == xmglContactWay) {
                xmglProjectUser1.setContactId(xmglContactWay1.getId());
            } else {
                xmglProjectUser1.setContactId(xmglContactWay.getId());
            }
            xmglProjectUser1.setCreateBy(loginUser.getUser().getNickName());
            xmglProjectUser1.setCreateTime(nowDate);
            xmglProjectUser1.setUpdateTime(nowDate);
            xmglProjectUserMapper.insertXmglProjectUser(xmglProjectUser1);

        }
        // 如果不为null证明有值 则修改负责人为旧负责人
        else {
            xmglProjectUser3.setUserFlag("1");
            xmglProjectUser3.setUpdateTime(nowDate);
            xmglProjectUserMapper.updateXmglProjectUser(xmglProjectUser3);
        }
        // 添加或者修改新负责人
        // 根据新负责人id和项目id去关系表查询，查询到了则证明之前这个人是旧负责人 状态改为新负责人就行 如果没查到则新增一条为新负责人
        XmglProjectUser xmglProjectUser1 = xmglProjectUserMapper.queryByProIdAndUserId(xmglProject.getId(), xmglProjectUser.getUserId());
        if (null == xmglProjectUser1) {
            // 新增以当前项目负责人为新负责人的数据
            XmglProjectUser xmglProjectUser2 = new XmglProjectUser();
            xmglProjectUser2.setProjectId(xmglProjectUser.getProjectId());
            xmglProjectUser2.setUserFlag("0");
            xmglProjectUser2.setUserId(xmglProjectUser.getUserId());
            // 联系方式id
            XmglContactWay xmglContactWay1 = new XmglContactWay();
            // 如果之前的负责人没有联系方式则新增一条联系方式
            if (null == xmglContactWay) {

                xmglContactWay1.setProjectId(xmglProject.getId());
                xmglContactWay1.setUserId(xmglProject.getUserId());
                xmglContactWay1.setCreateTime(nowDate);
                xmglContactWay1.setUpdateTime(nowDate);
                xmglContactWay1.setCreateBr(loginUser.getUser().getNickName());
                xmglContactWayMapper.insertXmglContactWay(xmglContactWay1);
            }
            if (null == xmglContactWay) {
                xmglProjectUser2.setContactId(xmglContactWay1.getId());
            } else {
                xmglProjectUser2.setContactId(xmglContactWay.getId());
            }
            xmglProjectUser2.setCreateBy(loginUser.getUser().getNickName());
            xmglProjectUser2.setCreateTime(nowDate);
            xmglProjectUser2.setUpdateTime(nowDate);
            xmglProjectUserMapper.insertXmglProjectUser(xmglProjectUser2);

        } else {
            xmglProjectUser1.setUserFlag("0");
            xmglProjectUser1.setUpdateTime(nowDate);
            xmglProjectUserMapper.updateXmglProjectUser(xmglProjectUser1);
        }


        return xmglProject.getUserId();
    }

    /**
     * 插入动态
     *
     * @param projectId   项目id
     * @param dynamicType 动态类型
     * @param dynamicMsg  动态信息
     * @param userId      用户id
     * @return int
     */
    public int insertDynamic(Long projectId, String dynamicType, String dynamicMsg, Long userId) {
        SysUser user = sysUserMapper.selectUserById(userId);
        Date nowDate = DateUtils.getNowDate();
        XmglDynamic xmglDynamic = new XmglDynamic();
        xmglDynamic.setProjectId(projectId);
        xmglDynamic.setDynamicType(dynamicType);
        xmglDynamic.setUserId(userId);
        xmglDynamic.setUserName(user.getNickName());
        xmglDynamic.setDynamicTime(nowDate);
        xmglDynamic.setDynamicMsg(dynamicMsg);
        xmglDynamic.setCreateBr(user.getNickName());
        xmglDynamic.setCreateTime(nowDate);
        xmglDynamic.setUpdateTime(nowDate);
        return dynamicMapper.insertXmglDynamic(xmglDynamic);
    }

    /**
     * 项目动态
     *
     * @param updateProject
     * @param noUpdateProject
     * @param oldUserData
     * @param newUserData
     * @return
     */
    public String generateDynamic(XmglProject updateProject, XmglProject noUpdateProject, XmglContactWay oldUserData, XmglContactWay newUserData) {
        String dynamicMsg = "修改项目：";
        SimpleDateFormat sp = new SimpleDateFormat("yyyy-MM-dd");
        // 判断项目进度
        if (!StringUtils.equals(updateProject.getScheduleStatus(), noUpdateProject.getScheduleStatus())) {
            dynamicMsg = dynamicMsg + "项目进度由”" + XmglProjectEnum.getName("SS" + noUpdateProject.getScheduleStatus()) + "“修改为“" + XmglProjectEnum.getName("SS" + updateProject.getScheduleStatus()) + "”，";
        }
        // 判断项目状态
        if (!StringUtils.equals(updateProject.getProjectStatus(), noUpdateProject.getProjectStatus())) {
            dynamicMsg = dynamicMsg + "项目状态由”" + XmglProjectEnum.getName(noUpdateProject.getProjectStatus()) + "“修改为“" + XmglProjectEnum.getName(updateProject.getProjectStatus()) + "”，";
        }
        // 判断立项时间
        if (!StringUtils.equals(updateProject.getProjectDate().toString(), noUpdateProject.getProjectDate().toString())) {
            dynamicMsg = dynamicMsg + "立项时间由”" + sp.format(noUpdateProject.getProjectDate()) + "“修改为“" + sp.format(updateProject.getProjectDate()) + "”，";
        }
        // 判断渠道方
        if (!StringUtils.equals(updateProject.getChannelSide().toString(), noUpdateProject.getChannelType().toString())) {
            String newType = "";
            String oldType = "";
            String subName = "";
            if (updateProject.getChannelSide().equals("1")) {
                newType = "内部渠道方";
                oldType = "外部渠道方";
                List<Long> projectUser = updateProject.getProjectUser();
                List<SysUser> userList = sysUserMapper.selectUserByUserId(projectUser);
                List<String> collect = userList.stream().map(SysUser::getNickName).collect(Collectors.toList());
                String name = "";
                for (String ss : collect) {
                    name = "【" + ss + "】" + ",";
                }
                subName = name.substring(name.lastIndexOf(","));
                dynamicMsg = dynamicMsg + "渠道方由" + oldType + "修改为" + newType + subName + "，";
            } else {
                newType = "外部渠道方";
                oldType = "内部渠道方";
                String channelSide = updateProject.getChannelSide();
                dynamicMsg = dynamicMsg + "渠道方由" + oldType + "修改为" + newType + "【" + channelSide + "】，";
            }
        }
        return dynamicMsg;

    }

    /**
     * 修改立项项目
     *
     * @param projectForm
     * @param userform
     * @param loginUser
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateProjectMethods(XmglProject projectForm, XmglContactWay userform, LoginUser loginUser) {
        XmglProject xmglProject = xmglProjectMapper.selectXmglProjectById(projectForm.getId());
        if (xmglProject.getProjectStatus().equals(XmglProjectEnum.XZXMSH.getCode())) {
            throw new ServiceException("项目处于新增项目审核中，暂时无法修改，待审批通过后重试！");
        }
        Long deployId = 0L;
        // 获取立项项目关联的项目名称id
        XmglDeployProject xmglDeployProject = xmglProjectDeployService.selectBeingProjectDeployInfoByProjectId(xmglProject.getId());
        if (!Objects.isNull(xmglDeployProject)) {
            deployId = xmglDeployProject.getDeployId();
        }
        Date nowDate = DateUtils.getNowDate();
        // 更新时间和更新人
        projectForm.setUpdateTime(nowDate);
        projectForm.setUpdateBr(loginUser.getUser().getNickName());
        projectForm.setProjectStatus(projectForm.getScheduleStatus());
        // 立项项目id
        Long id = projectForm.getId();
        /**
         * 新权限改造：
         * 旧版本是当前项目变更点击保存按钮后，详情页数据不会发生变化，只有在用户对该项目进行'发布项目动态'之后，此时该项目详情页的数据才会发生变化
         * 新版本要做成对项目进行变更后，点击提交按钮走审批流程，审批流程通过之后，立马对旧数据进行修改展示，此时发不发布项目项目动态不影响项目变化。
         */

        /**如果通过修改立项信息修改了联系人信息，录入人不改变
         如果有多套联系方式信息，修改立项信息功能只能修改当前的联系信息
         */
        userform.setUpdateTime(DateUtils.getNowDate());
        userform.setUpdateBr(loginUser.getUser().getNickName());
        xmglContactWayMapper.updateXmglContactWay(userform);

        // 如果修改前的渠道方类型是内部渠道方，则删除权限表中该用户对该项目关联的项目名称权限
        if (xmglProject.getChannelType().equals("1")) {
            // 获取当前项目最新的渠道方
            List<XmglProjectChannel> channelList = projectChannelMapper.selectXmglProjectChannelListByProjectId(id);
            if (!CollectionUtils.isEmpty(channelList)) {
                // 清除权限
                this.cancelAuthority(channelList, deployId);

                // 循环获取项目负责人权限表数据
                // for (XmglProjectChannel projectChannel : channelList) {
                //    AuthDetailVo authDetailVo = xmglProjectMapper.selectAuthDeployInfoByModuleTypeAndUserId(AuthRoleEnum.XMGL1.getCode(), projectChannel.getChannelId(), deployId);
                //    if (!Objects.isNull(authDetailVo)) {
                //        /** 删除权限表旧项目负责人权限 */
                //        xmglProjectMapper.deleteAuthMainDetailInfoByDetailId(authDetailVo.getAuthDetailId());
                //    }
                //}
            }
        }

        // 将之前录入的渠道方改成旧渠道方
        // xmglContactWayMapper.updateXmglContactWayByProjectId(id);
        projectChannelMapper.updateStatusByProjectId(projectForm.getId());

        // 内部渠道方
        if (projectForm.getChannelType().equals("1")) {
            // 添加新的渠道方
            List<Long> channelForm = projectForm.getChannelForm();
            for (Long aLong : channelForm) {
                XmglProjectChannel xmglProjectChannel = new XmglProjectChannel();
                xmglProjectChannel.setProjectId(id);
                xmglProjectChannel.setChannelId(aLong);
                xmglProjectChannel.setContactId(userform.getId());
                xmglProjectChannel.setCreateTime(DateUtils.getNowDate());
                xmglProjectChannel.setCreateBy(loginUser.getUser().getUserName());
                xmglProjectChannel.setStatus("0");
                SysUser sysUser = sysUserMapper.selectUserById(aLong);
                xmglProjectChannel.setNickName(sysUser.getNickName());
                projectChannelMapper.insertXmglProjectChannel(xmglProjectChannel);

                /**
                 * 内部渠道方授权授权业务相关模块
                 * 如果内部渠道方用户在该模块没有任何一种权限，默认授予用户此模块的查看权限
                 * 如果该用户已在此模块有其他任何一种权限，则不做处理
                 */
                this.getAuthority(aLong, deployId, userform.getUserId());


                /*
                 *   如果该渠道方有该项目的任何一种权限，则不做处理
                 *   如果内部渠道方用户没有这个项目的任何一种角色权限(业务管理员、项目负责人、查看权限)，默认给该用户'查看权限'
                 */
                // List<AuthorizedFeatureDetailDTO> projectRoleList = newAuthorityServiceImpl.getProjectRoleByOaProjectDeployIdAndModuleType(deployId, AuthModuleEnum.PROJNAME.getCode());
                // List<Object> proPrincipalUserld = projectRoleList.stream().filter(t -> "88".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
                // List<Object> proPrincipalUserld1 = projectRoleList.stream().filter(t -> "7".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
                // List<Object> proPrincipalUserld2 = projectRoleList.stream().filter(t -> "10".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
                // proPrincipalUserld.removeAll(Collections.singleton(null));
                // proPrincipalUserld1.removeAll(Collections.singleton(null));
                // proPrincipalUserld2.removeAll(Collections.singleton(null));
                // if (!CollectionUtils.isEmpty(proPrincipalUserld)){
                //    List<Long> featureUserIds = proPrincipalUserld.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
                //    //如果渠道方没有三种权限中的任何一种，则授权查看权限
                //    if (!featureUserIds.contains(aLong) && !proPrincipalUserld1.contains(aLong) && !proPrincipalUserld2.contains(aLong)){
                //        this.insertAuthAndDetail(aLong,deployId,userform.getUserId(),AuthRoleEnum.XMGL3.getCode());
                //    }
                //}
                // 查询当前新的内部渠道方的上级、上上级、祖级
                List<Long> parentUserIds = new ArrayList<>();
                parentUserIds = this.getParentUser(sysUser.getUserName(), parentUserIds);
                if (!CollectionUtils.isEmpty(parentUserIds)) {
                    for (Long userId : parentUserIds) {
                        //查询用户是否有此模块的项目权限
                        List<String> userRoleByUserIdAndOaProjectDeployIdAndModuleType = newAuthorityServiceImpl.getUserRoleByUserIdAndOaProjectDeployIdAndModuleType(userId, deployId, AuthModuleEnum.PROJSETUP.getCode());
                        //为空则授权
                        if (CollectionUtils.isEmpty(userRoleByUserIdAndOaProjectDeployIdAndModuleType)){
                            insertAuthAndDetail(userId, deployId, loginUser.getUserId(), AuthRoleEnum.XMGL3.getCode(), AuthModuleEnum.PROJSETUP.getCode());
                        }
                    }
                }
            }
            projectForm.setChannelSide("");
        }
        // 新项目负责人 注意！！！！！：原型中修改操作不允许修改项目负责人，以下代码是备用代码
        // List<Long> projectUser = projectForm.getProjectUser();
        //// 先将现有的项目负责人更新成旧负责人
        // xmglProjectUserMapper.updateXmglProjectUserByProjectId(id);
        //// 添加新的项目负责人
        // if (!CollectionUtils.isEmpty(projectUser) && projectUser.size() > 0) {
        //    for (Long aLong : projectUser) {
        //        XmglProjectUser xmglProjectUser = new XmglProjectUser();
        //        xmglProjectUser.setProjectId(id);
        //        xmglProjectUser.setUserId(aLong);
        //        xmglProjectUser.setContactId(userform.getId());
        //        xmglProjectUser.setUserFlag("0");
        //        xmglProjectUser.setStatus("0");
        //        xmglProjectUser.setCreateBy(loginUser.getUsername());
        //        xmglProjectUser.setCreateTime(DateUtils.getNowDate());
        //        xmglProjectUserMapper.insertXmglProjectUser(xmglProjectUser);
        //    }
        //}
        // 如果本次新增立项项目时未创建新的项目名称或公司或公司类型，项目负责人没有该项目的权限，则重新授权
        // 注意：修改立项项目信息不允许修改项目负责人，以下代码是备用代码。
        // for (Long aLong : projectUser) {
        //     //根据用户id查询该用户是否有此项目名称的业务负责人角色
        //     AuthMain authMain = xmglProjectMapper.selectAuthDeployInfoByModuleTypeAndUserId(AuthRoleEnum.XMGL1.getCode(), aLong, projectForm.getDeployId());
        //     //为空，则证明该用户在通用授权中没有该项目权限
        //     if (ObjectUtils.isEmpty(authMain)){
        //         this.insertAuthAndDetail(aLong,projectForm.getDeployId(),projectForm.getUserId());
        //     }
        // }
        // 更新项目
        return xmglProjectMapper.updateXmglProject(projectForm);
    }

    /**
     * 通用授权入库
     * 新增/修改/变更负责人或渠道方时，判断新的项目负责人是否有该项目的负责人角色，没有则插入权限表
     *
     * @param principalUserId 项目负责人id
     * @param deployId        项目名称id
     * @param createUserId    创建人/申请人id
     */
    private void insertAuthAndDetail(Long principalUserId, Long deployId, Long createUserId, String roleType, String moduleType) {
        logger.info("新增/修改/变更负责人或渠道方时，判断新的项目负责人是否有该项目的负责人角色，没有则插入权限表");
        SysUser sysUser = sysUserMapper.selectUserById(createUserId);
        // 获取主岗位或兼任岗位
        Long homePost = sysUserPostMapper.selectUserPostUserId(createUserId);
        // 获取主岗位信息或兼任岗位信息
        SysPost sysPostToAgency = iSysPostService.selectPostById(homePost);
        AuthMain authMain = new AuthMain();
        authMain.setThirdType("1");
        authMain.setThirdId(principalUserId);
        authMain.setModuleType(moduleType);
        authMain.setRoleType(roleType);
        authMain.setPermissionScope(AuthPermissionEnum.PROJ4.getCode());
        authMain.setPermissionType("0");
        // 在设置权限时间时使用常量
        try {
            logger.info("内部渠道方设置权限表中到期日为：" + PERMISSION_NEVER_EXPIRE + "，转换为YYYY_MM_DD_HHMMSS格式后为：" + DateUtils.parse(PERMISSION_NEVER_EXPIRE, DateUtils.DateFormat.YYYY_MM_DD_HHMMSS));
            authMain.setPermissionTime(DateUtils.parse(PERMISSION_NEVER_EXPIRE, DateUtils.DateFormat.YYYY_MM_DD_HHMMSS));
        } catch (ParseException e) {
            // 处理日期解析异常，例如记录日志或抛出自定义异常
            logger.error("解析权限过期时间失败", e);
            throw new RuntimeException("解析权限过期时间失败", e);
        }
        // authMain.setPermissionTime(DateUtils.parse("9999-12-31 23:59:59", DateUtils.DateFormat.YYYY_MM_DD_HHMMSS));
        authMain.setStatus("0");
        authMain.setSendNotify("-1");
        authMain.setCreateId(createUserId);
        authMain.setCreateBy(sysUser.getUserName());
        authMain.setCreateTime(DateUtils.getNowDate());
        authMain.setCreateUnitId(sysPostToAgency.getUnitId() == null ? -1L : sysPostToAgency.getUnitId());
        authMain.setCreateDeptId(sysPostToAgency.getDeptId() == null ? -1L : sysPostToAgency.getDeptId());

        authMain.setUpdateBy(sysUser.getUserName());
        authMain.setUpdateTime(DateUtils.getNowDate());
        authMain.setUpdateId(createUserId);
        authMain.setUpdateUnitId(sysPostToAgency.getUnitId() == null ? -1L : sysPostToAgency.getUnitId());
        authMain.setUpdateDeptId(sysPostToAgency.getDeptId() == null ? -1L : sysPostToAgency.getDeptId());
        // 插入权限主表
        xmglFlowRelationMapper.insertAuthMain(authMain);
        Long id = authMain.getId();

        // 插入附表
        AuthDetail authDetail = new AuthDetail();
        authDetail.setAuthMainId(id);
        authDetail.setThirdTableName("oa_project_deploy");
        authDetail.setThirdTableAliasName("proj");
        authDetail.setThirdTableId(deployId);
        authDetail.setStatus("0");
        authDetail.setCreateId(createUserId);
        authDetail.setCreateBy(sysUser.getUserName());
        authDetail.setCreateTime(DateUtils.getNowDate());
        authDetail.setCreateUnitId(sysPostToAgency.getUnitId() == null ? -1L : sysPostToAgency.getUnitId());
        authDetail.setCreateDeptId(sysPostToAgency.getDeptId() == null ? -1L : sysPostToAgency.getDeptId());
        authDetail.setUpdateTime(DateUtils.getNowDate());
        authDetail.setUpdateId(createUserId);
        authDetail.setUpdateBy(sysUser.getUserName());
        authDetail.setUpdateUnitId(sysPostToAgency.getUnitId() == null ? -1L : sysPostToAgency.getUnitId());
        authDetail.setUpdateDeptId(sysPostToAgency.getDeptId() == null ? -1L : sysPostToAgency.getDeptId());
        xmglFlowRelationMapper.insertAuthDteail(authDetail);
    }

    public List<XmglProject> checkAgain(XmglProject xmglProject, LoginUser loginUser) {

        XmglProject queryxmglProject = new XmglProject();
        // 业务类型
        // queryxmglProject.setBusinessType(xmglProject.getBusinessType());
        // 资金方全称
        // queryxmglProject.setFundFullName(xmglProject.getFundFullName());
        // 资产方全称
        // if(null!=xmglProject.getProductFullName()){
        //    queryxmglProject.setProductFullName(xmglProject.getProductFullName());
        //}
        // 担保公司全称
        // queryxmglProject.setCustFullName(xmglProject.getCustFullName());
        // 项目名称
        queryxmglProject.setProjectName(xmglProject.getProjectName());


        return this.selectXmglProjectList(queryxmglProject, loginUser);

    }

    /**
     * 获取用户在【业务信息配置-项目名称】中有项目名称权限的项目信息
     *
     * @return
     */
    @Override
    public List<OaProjectDeploy> selectRelevanceProject() {
        List<OaProjectDeploy> oaProjectDeploys = new ArrayList<>();
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setSelectType("0");
        oaProjectDeploys = xmglProjectMapper.newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdList(oaProjectDeploy);
        return oaProjectDeploys;
    }

    /**
     * 新增立项项目时根据项目名称查重
     *
     * @param projectName
     * @return
     */
    @Override
    public Boolean queryIsRepeatByProjectName(String projectName) {
        Boolean flag = false;
        int size = xmglProjectMapper.queryIsRepeatByProjectName(projectName);
        if (size <= 0) {
            flag = true;
        }
        return flag;
    }

    /**
     * 根据项目id查询当前项目业务负责人
     *
     * @param projectId
     * @return
     */
    @Override
    public Set<SysUser> queryPrincipalByProjectId(Long projectId) {
        Set<SysUser> userSet = new HashSet<>();
        // 项目名称授权的业务负责人
        List<SysUser> userList = xmglProjectMapper.selectPrincipalByProjectId(projectId);
        // 各维度授权的业务负责人
        List<SysUser> eachAuth = xmglProjectMapper.selectEachDimensionAuth(projectId);
        userSet.addAll(userList);
        userSet.addAll(eachAuth);
        userSet.removeAll(Collections.singleton(null));
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(userSet) && userSet.size() > 0) {
            return userSet;
        }
        return userSet;
    }

    @Override
    public int updateXmglProjectAnyStatus(XmglProject xmglProjectDepoly) {
        int result = 0;
        XmglProject projectInfo = new XmglProject();
        String projectStatus = xmglProjectDepoly.getProjectStatus();
        if (null == xmglProjectDepoly.getDeployId()) {
            return result;
        }
        if (null == projectStatus || projectStatus.equals("")) {
            return result;
        }
        // 先根据传过来的项目名称id查询是否在项目立项-项目名称管理表中有对应关系
        XmglDeployProject deployProject = xmglProjectDeployService.selectXmglProjectDeployByDeployId(xmglProjectDepoly.getDeployId());
        if (Objects.isNull(deployProject)) {
            return result;
        }
        // 根据关联关系信息找到项目立项表信息
        projectInfo = xmglProjectMapper.selectXmglProjectById(deployProject.getProjectId());
        // 修改项目属性(11 修改项目属性)
        if (projectStatus.equals(XmglProjectEnum.XGXMSH.getCode())) {
            // 当立项项目状态不是'已终止'或'已上线'状态时，同步更新
            if (!projectInfo.getProjectStatus().equals(XmglProjectEnum.YZZ.getCode())
                    && !projectInfo.getProjectStatus().equals(XmglProjectEnum.YSX.getCode())) {
                XmglProject project = new XmglProject();
                // 修改项目名称审核通过后，恢复立项项目状态
                project.setProjectStatus(projectInfo.getScheduleStatus());
                project.setId(projectInfo.getId());
                if (!StringUtils.equals(projectInfo.getProjectName(), xmglProjectDepoly.getProjectName())) {
                    project.setProjectName(xmglProjectDepoly.getProjectName());
                }

                result = xmglProjectMapper.updateXmglProject(project);
            }
        }
        return result;
    }

    /**
     * 延期项目(修改锁定到期时间)
     *
     * @param xmglProject
     * @return
     */
    @Override
    public int projectPostponeAudit(XmglProject xmglProject) {
        int result = 0;
        XmglProject project = null;
        String format = null;
        String errorMsg = "";
        try {
            result = 0;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
            // 如果是锁定状态，只更新锁定结束时间 0已锁定
            project = xmglProjectMapper.selectXmglProjectById(xmglProject.getId());
            format = sdf.format(project.getOverTime());
            if (project.getLockStatus().equals("0")) {
                XmglProject xmgl = new XmglProject();
                xmgl.setOverTime(xmglProject.getOverTime());
                xmgl.setId(xmglProject.getId());
                xmglProject.setProjectStatus(project.getScheduleStatus());
                result = xmglProjectMapper.updateXmglProject(xmglProject);
            } else {
                // 如果是1未锁定状态，更新锁定开始时间和锁定结束时间，且将锁定状态修改为已锁定(锁定结束时间由前端传过来)
                xmglProject.setBeginTime(DateUtils.getNowDate());
                xmglProject.setLockStatus("0");
                xmglProject.setProjectStatus(project.getScheduleStatus());
                result = xmglProjectMapper.updateXmglProject(xmglProject);
            }
        } catch (Exception e) {
            errorMsg = e.getMessage();
            e.printStackTrace();
        } finally {
            String dynamicMsg = "";
            if (StringUtils.isNotEmpty(errorMsg)) {
                dynamicMsg = "【" + project.getProjectName() + "】项目的锁定到期时间延期至" + format + "失败！";
            } else {
                dynamicMsg = "审批通过，【" + project.getProjectName() + "】项目的锁定到期时间延期至" + format;
            }
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJSETUP.getCode(), "", dynamicMsg, 1, errorMsg, "");
        }
        return result;
    }


    /**
     * 项目立项新增插入关联表
     *
     * @param xmglProject
     */
    public void insertOtherInfo(XmglProject xmglProject) {
        logger.info("新增立项项目时插入关联表信息...");
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 项目类型
        List<XmglProjectTypeRelevance> projectTypeList = xmglProject.getXmglProjectTypeList();
        if (null != projectTypeList && projectTypeList.size() > 0) {
            for (XmglProjectTypeRelevance projectTypeRelevance : projectTypeList) {
                projectTypeRelevance.setProjectId(xmglProject.getId());
                projectTypeRelevance.setDeployId(xmglProject.getDeployId());
                projectTypeRelevance.setCreateBy(user.getNickName());
                projectTypeRelevance.setStatus("0");
                projectTypeRelevance.setCreateTime(DateUtils.getNowDate());
            }
            logger.info("插入项目类型关联表..");
            xmglRelevanceServiceImpl.insertProjectTypeRelevanceList(projectTypeList);
        }

        // 业务类型
        List<XmglProjectTypeRelevance> businessTypeList = xmglProject.getXmglBusinessTypeList();
        if (null != businessTypeList && businessTypeList.size() > 0) {
            for (XmglProjectTypeRelevance projectTypeRelevance : businessTypeList) {
                projectTypeRelevance.setProjectId(xmglProject.getId());
                projectTypeRelevance.setDeployId(xmglProject.getDeployId());
                projectTypeRelevance.setCreateBy(user.getNickName());
                projectTypeRelevance.setStatus("0");
                projectTypeRelevance.setCreateTime(DateUtils.getNowDate());
            }
            logger.info("插入业务类型关联表..");
            xmglRelevanceServiceImpl.insertProjectTypeRelevanceList(businessTypeList);
        }

        // 动态新增公司类型
        Map<String, List<XmglProjectCompanyRelevance>> tableList = xmglProject.getTableList();
        if (tableList != null && !tableList.isEmpty()) {
            tableList = repliceMap(tableList);
            List<XmglProjectCompanyRelevance> mergedList = new ArrayList<>();
            for (Map.Entry<String, List<XmglProjectCompanyRelevance>> entry : tableList.entrySet()) {
                String key = entry.getKey();
                List<XmglProjectCompanyRelevance> projectList = entry.getValue();
                String unitType = key.replace("List", "");
                // 设置 unitType 并合并到一个列表
                for (XmglProjectCompanyRelevance project : projectList) {
                    project.setUnitType(unitType);  // 设置 unitType
                    project.setProjectId(xmglProject.getId());
                    project.setDeployId(xmglProject.getDeployId());
                    project.setCreateBy(user.getNickName());
                    project.setStatus("0");
                    project.setCreateTime(DateUtils.getNowDate());
                    mergedList.add(project);  // 添加到合并后的列表
                }
            }
            xmglRelevanceServiceImpl.insertProjectCompanyRelevanceList(mergedList);
            logger.info("插入公司关联表..");
        }
        // 担保公司
//        List<XmglProjectCompanyRelevance> custList = xmglProject.getCustList();
//        if (custList.size() > 0) {
//            for (XmglProjectCompanyRelevance projectCompanyRelevance : custList) {
//                String proportion = "";
//                if (null != projectCompanyRelevance.getProportion() && !projectCompanyRelevance.getProportion().equals("") && projectCompanyRelevance.getProportion().contains("%")) {
//                    proportion = projectCompanyRelevance.getProportion().substring(0, projectCompanyRelevance.getProportion().lastIndexOf("%"));
//                    projectCompanyRelevance.setProportion(proportion);
//                }
//                projectCompanyRelevance.setProjectId(xmglProject.getId());
//                projectCompanyRelevance.setDeployId(xmglProject.getDeployId());
//                projectCompanyRelevance.setCreateBy(user.getNickName());
//                projectCompanyRelevance.setStatus("0");
//                projectCompanyRelevance.setCreateTime(DateUtils.getNowDate());
//            }
//            xmglRelevanceServiceImpl.insertProjectCompanyRelevanceList(custList);
//        }
        // 其他公司
        List<XmglProjectCompanyRelevance> otherUnitList = xmglProject.getOtherUnitList();
        if (null != otherUnitList && otherUnitList.size() > 0) {
            for (XmglProjectCompanyRelevance projectCompanyRelevance : otherUnitList) {
                String proportion = "";
                if (null != projectCompanyRelevance.getProportion() && !projectCompanyRelevance.getProportion().equals("") && projectCompanyRelevance.getProportion().contains("%")) {
                    proportion = projectCompanyRelevance.getProportion().substring(0, projectCompanyRelevance.getProportion().lastIndexOf("%"));
                    projectCompanyRelevance.setProportion(proportion);
                }
                projectCompanyRelevance.setProjectId(xmglProject.getId());
                projectCompanyRelevance.setDeployId(xmglProject.getDeployId());
                projectCompanyRelevance.setCreateBy(user.getNickName());
                projectCompanyRelevance.setUnitType("3");
                projectCompanyRelevance.setStatus("0");
                projectCompanyRelevance.setCreateTime(DateUtils.getNowDate());
            }
            logger.info("插入其他公司关联表..");
            xmglRelevanceServiceImpl.insertProjectCompanyRelevanceList(otherUnitList);
        }
        // 资产方
//        List<XmglProjectCompanyRelevance> partnerList = xmglProject.getPartnerList();
//        if (partnerList.size() > 0) {
//            for (XmglProjectCompanyRelevance projectCompanyRelevance : partnerList) {
//                String proportion = "";
//                if (null != projectCompanyRelevance.getProportion() && !projectCompanyRelevance.getProportion().equals("") && projectCompanyRelevance.getProportion().contains("%")) {
//                    proportion = projectCompanyRelevance.getProportion().substring(0, projectCompanyRelevance.getProportion().lastIndexOf("%"));
//                    projectCompanyRelevance.setProportion(proportion);
//                }
//                projectCompanyRelevance.setProjectId(xmglProject.getId());
//                projectCompanyRelevance.setDeployId(xmglProject.getDeployId());
//                projectCompanyRelevance.setCreateBy(user.getNickName());
//                projectCompanyRelevance.setStatus("0");
//                projectCompanyRelevance.setCreateTime(DateUtils.getNowDate());
//            }
//            xmglRelevanceServiceImpl.insertProjectCompanyRelevanceList(partnerList);
//        }

        // 资金方
//        List<XmglProjectCompanyRelevance> fundList = xmglProject.getFundList();
//        if (fundList.size() > 0) {
//            for (XmglProjectCompanyRelevance projectCompanyRelevance : fundList) {
//                String proportion = "";
//                if (null != projectCompanyRelevance.getProportion() && !projectCompanyRelevance.getProportion().equals("") && projectCompanyRelevance.getProportion().contains("%")) {
//                    proportion = projectCompanyRelevance.getProportion().substring(0, projectCompanyRelevance.getProportion().lastIndexOf("%"));
//                    projectCompanyRelevance.setProportion(proportion);
//                }
//                projectCompanyRelevance.setProjectId(xmglProject.getId());
//                projectCompanyRelevance.setDeployId(xmglProject.getDeployId());
//                projectCompanyRelevance.setCreateBy(user.getNickName());
//                projectCompanyRelevance.setStatus("0");
//                projectCompanyRelevance.setCreateTime(DateUtils.getNowDate());
//            }
//            xmglRelevanceServiceImpl.insertProjectCompanyRelevanceList(fundList);
//        }
    }

    /**
     * 更新项目进度
     *
     * @param addprojectVo
     * @return
     */
    @Override
    public AjaxResult updateProjectProgress(AddprojectVo addprojectVo) {
        LoginUser loginUser = null;
        XmglProject projectForm = null;// 项目信息
        XmglProject project = null;
        String oldName = null;
        String newName = null;
        String dynamicMsg = null;// 更新说明
        String errorMsg = "";
        int i = 0;
        try {
            /**
             * 更新进度后，将重新开始解锁倒计时。如果项目已解锁，将被重新锁定
             * 如果项目进度由待上会更新为了合同签署，但此时项目名称中仍然存在暂不确定的公司，则再次弹窗确认，此时无法完成提交
             * 如果项目进度由待上会更新为了合同签署，但此时项目名称中项目类型未填，则再次弹窗确认，此时无法完成提交
             * 如果项目进度由待上会更新为了合同签署，但此时项目名称中业务类型未填，则再次弹窗确认，此时无法完成提交
             * 如果项目进度由合同签署更新为了已上线，则点击提交后再次弹窗确认，项目状态变为：已上线，项目锁定状态为：-
             * 自动按规则生成一条项目动态
             */
            loginUser = SecurityUtils.getLoginUser();
            projectForm = addprojectVo.getProjectForm();
            // 根据id查询旧进度
            project = xmglProjectMapper.selectXmglProjectById(projectForm.getId());

            /**
             * 如果项目进度由待上会更新为了合同签署，但此时项目名称中仍然存在暂不确定的公司，则再次弹窗确认，此时无法完成提交
             * 如果项目进度由待上会更新为了合同签署，但此时项目名称中项目类型未填，则再次弹窗确认，此时无法完成提交
             * 如果项目进度由待上会更新为了合同签署，但此时项目名称中业务类型未填，则再次弹窗确认，此时无法完成提交
             */
            if (projectForm.getScheduleStatus().equals(XmglProjectEnum.HTQS.getCode())) {
                // 担保公司
                List<XmglProjectCompanyRelevance> custList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("0", projectForm.getId());
                // 资产方
                List<XmglProjectCompanyRelevance> partnerList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("1", projectForm.getId());
                // 资金方
                List<XmglProjectCompanyRelevance> fundList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("2", projectForm.getId());

                boolean cust = custList.stream().anyMatch(c -> c.getUnitId() == 0L);
                boolean partner = partnerList.stream().anyMatch(p -> p.getUnitId() == 0L);
                boolean fund = fundList.stream().anyMatch(f -> f.getUnitId() == 0L);
                if (cust || partner || fund) {
                    return AjaxResult.error("本项目的项目名称中存在暂不确定的公司，请使用 [修改项目属性] 功能，选定公司名称，再更新项目进度");
                }

                // 项目类型
                List<XmglProjectTypeRelevance> projectTypeData = xmglRelevanceServiceImpl.queryProjectObject("0", projectForm.getId());
                if (CollectionUtils.isEmpty(projectTypeData)) {
                    return AjaxResult.error("本项目尚未确认 [项目类型]，请使用 [修改项目属性] 功能，选择项目类型，再更新项目进度");
                }

                // 业务类型
                List<XmglProjectTypeRelevance> businessTypeData = xmglRelevanceServiceImpl.queryProjectObject("1", projectForm.getId());
                if (CollectionUtils.isEmpty(businessTypeData)) {
                    return AjaxResult.error("本项目尚未确认 [业务类型]，请使用 [修改项目属性] 功能，选择业务类型，再更新项目进度");
                }
            }
            // 如果项目进度由合同签署更新为了已上线，则点击提交后再次弹窗确认，项目状态变为：已上线，项目锁定状态为：-
            if (projectForm.getScheduleStatus().equals(XmglProjectEnum.YSX.getCode())) {
                projectForm.setLockStatus("-");
                projectForm.setProjectStatus("5");
                projectForm.setScheduleStatus("5");
            }
            // 最近的进度
            String scheduleStatus = projectForm.getScheduleStatus();
            // 根据枚举值查询旧的项目进度
            oldName = XmglProjectEnum.getName(project.getScheduleStatus());
            // 根据枚举值查询最新的项目进度
            newName = XmglProjectEnum.getName(scheduleStatus);

            dynamicMsg = addprojectVo.getDynamicMsg();
            // 重新开始解锁倒计时。如果项目已解锁，将被重新锁定
            Date date90 = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 90L);
            // Date date90 = new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 1L);
            projectForm.setBeginTime(DateUtils.getNowDate());// 锁定开始时间
            projectForm.setOverTime(date90);// 锁定结束时间
            projectForm.setLockStatus("0");// 锁定状态
            projectForm.setProjectStatus(projectForm.getScheduleStatus());// 项目进度状态
            projectForm.setUpdateStatusTime(DateUtils.getNowDate());// 最后一次更新进度时间
        } catch (Exception e) {
            errorMsg = e.getMessage();
            e.printStackTrace();
        } finally {
            String dynamicInfo = "";
            if (StringUtils.isNotEmpty(errorMsg)) {
                dynamicInfo = "项目进度由[" + oldName + "]更新为[" + newName + "]，更新失败！";// 项目动态字符串
            } else {
                // 生成项目动态
                dynamicInfo = "项目进度由[" + oldName + "]更新为[" + newName + "]，更新说明：" + dynamicMsg;// 项目动态字符串
            }
            this.insertDynamic(project.getId(), "1", dynamicInfo, loginUser.getUserId());
            i = xmglProjectMapper.updateXmglProject(projectForm);
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.PROJNAME.getCode(), "", dynamicInfo, 1, errorMsg, "");
        }
        return AjaxResult.success(i);
    }

    /**
     * 项目名称模块业务负责人脏数据处理
     *
     * @param yewuList
     * @return
     */
    @Override
    public List<SysUser> dirtyDataProcessing(List<OaEditApproveGeneralityUser> yewuList) {
        List<SysUser> userList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(yewuList)) {
            for (OaEditApproveGeneralityUser oaEditApproveGeneralityUser : yewuList) {
                // 查询已授权的业务负责人是否是运营岗位的用户
                SysUser user = xmglProjectMapper.dirtyDataProcessing(oaEditApproveGeneralityUser.getUserId());
                if (!Objects.isNull(user)) {
                    userList.add(user);
                }
            }
        }
        return userList;
    }

    /**
     * 在【业务信息配置-项目名称】中当业务管理员修改了业务责任人，则在项目立项管理中，项目负责人也同步变更，他们是同一人
     *
     * @param financialStaffUser
     * @param financialStaffList
     */
    @Override
    public void updateBusinessUser(OaEditApproveGeneralityUser financialStaffUser, List<Long> financialStaffList) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long oaApplyId = financialStaffUser.getOaApplyId();
        // 根据项目名称模块的项目id查询关联的立项项目id
        XmglDeployProject xmglDeployProject = xmglProjectDeployService.selectXmglProjectDeployByDeployId(oaApplyId);
        if (Objects.isNull(xmglDeployProject)) {
            return;
        }
        Long projectId = xmglDeployProject.getProjectId();
        // 获取最新的联系人信息
        XmglContactWay contactWay = xmglContactWayMapper.selectNewXmglContactWayByProjectId(projectId);
        // 将现有的项目负责人修改成旧负责人
        xmglProjectUserMapper.updateXmglProjectUserByProjectId(projectId);
        // 插入新的项目负责人
        List<XmglProjectUser> xmglProjectUserList = new ArrayList<>();
        for (Long newUserId : financialStaffList) {
            XmglProjectUser projectUser = new XmglProjectUser();
            SysUser sysUser = sysUserMapper.selectUserById(newUserId);
            projectUser.setUserId(newUserId);
            projectUser.setNickName(sysUser.getNickName());
            projectUser.setProjectId(projectId);
            projectUser.setUserFlag("0");
            projectUser.setStatus("0");
            projectUser.setContactId(contactWay.getId());// 联系人表主键id
            projectUser.setCreateTime(DateUtils.getNowDate());
            projectUser.setCreateBy(loginUser.getUsername());
            xmglProjectUserList.add(projectUser);
        }
        if (!CollectionUtils.isEmpty(xmglProjectUserList)) {
            xmglProjectUserMapper.insertXmglProjectUserList(xmglProjectUserList);
        }
    }

    /**
     * 用户点击取消按钮，或关闭窗口页签，默认不发起审批流程
     * 此时删除该用户所有新创建的临时数据
     *
     * @param addUuid
     * @return
     */
    @Override
    public int deleteTemporarilyInfo(String addUuid) {
        logger.info("当前登录用户(" + SecurityUtils.getLoginUser().getUser().getNickName() + ")点击取消按钮或关闭窗口页签，默认不发起审批流程,自动调用 deleteTemporarily 接口，逻辑删除数据，开始");
        // String addUuid = addTemporarily.getAddUuid();
        // uuid不为空，证明有新增的公司或项目或公司类型
        if (null != addUuid && StringUtils.isNotEmpty(addUuid)) {
            List<Long> deployIdList = new ArrayList<>();
            List<Long> companyIdList = new ArrayList<>();
            List<Long> companyTypeList = new ArrayList<>();
            // 根据统一uuid查询同一批次新增的数据
            logger.info("根据统一uuid查询同一批次新增的数据,uuid={}", addUuid);
            List<XmglAddTemporarily> xmglAddTemporarilies = addTemporarilyService.selectXmglAddTemporarilyByAddUuid(addUuid);
            // 循环组装list
            for (XmglAddTemporarily temporarily : xmglAddTemporarilies) {
                // 项目
                if (temporarily.getAddType().equals("1")) {
                    deployIdList.add(temporarily.getDeployTabId());
                }
                // 公司
                if (temporarily.getAddType().equals("2")) {
                    companyIdList.add(temporarily.getCompanyTabId());
                }
                // 公司类型
                if (temporarily.getAddType().equals("3")) {
                    companyTypeList.add(temporarily.getCompanyTypeTabId());
                }
            }
            /** 删除项目信息 */
            // 根据本次新增的项目名称id查询立项项目(正常情况下只会有一条)
            List<XmglDeployProject> deployProjectList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(deployIdList)) {
                logger.info("获取本次新增的项目名称id,deployIdList={},", deployIdList);
                deployProjectList = addTemporarilyService.selectXmglProjectInfoListByDeployIds(deployIdList);
            }

            if (!CollectionUtils.isEmpty(deployProjectList) && deployProjectList.size() > 0) {
                logger.info("有正常的立项和项目信息的关联关系，则根据立项项目id查询新增类型的流程信息,包括新增的项目信息，公司信息，公司类型信息,流程信息等...");
                // 有正常的关联关系，则根据立项项目id查询新增类型的流程信息
                for (XmglDeployProject xmglDeployProject : deployProjectList) {
                    logger.info("有正常的关联关系，则根据立项项目id查询新增类型的流程信息,projectId={},", deployProjectList.get(0).getProjectId());
                    Long projectId = xmglDeployProject.getProjectId();
                    Long deployId = xmglDeployProject.getDeployId();
                    // 查询流程信息
                    XmglFlowRelation flowRelationInfo = xmglFlowRelationMapper.getFlowRelationInfoByProjectIdAndFlowClassify(projectId, "6");
                    // 不为空，证明本次新增立项项目有使用到新增的项目名称信息
                    if (!Objects.isNull(flowRelationInfo)) {
                        // 将本次新增的并且使用到的项目名称从临时表中去除
                        deployIdList.remove(deployId);
                    }
                    // 根据新增的项目id集合查询数据
                    if (!CollectionUtils.isEmpty(deployIdList) && deployIdList.size() > 0) {
                        List<OaProjectDeploy> deployList = addTemporarilyService.selectOaProjectDeployInfoById(deployIdList);
                        List<Long> deployIds = new ArrayList<>();
                        if (!CollectionUtils.isEmpty(deployList) && deployList.size() > 0) {
                            for (OaProjectDeploy oaProjectDeploy : deployList) {
                                if (oaProjectDeploy.getCheckStatus().equals("0")) {
                                    // oaProjectDeployService.deleteOaProjectDeployById(oaProjectDeploy.getId());
                                    deployIds.add(oaProjectDeploy.getId());
                                }
                            }
                            if (!CollectionUtils.isEmpty(deployIds)) {
                                logger.info("本次逻辑删除的项目信息,deployIds={},", deployIds);
                                addTemporarilyService.deleteOaProjectDeployByIds(deployIds);
                                /** 删除授权信息 */
                                logger.info("删除项目授权信息");
                                // 首先根据项目id查询授权明细表
                                List<AuthDetail> authDetailList = addTemporarilyService.selectAuthDetailByDeployId(deployIds, "1");
                                // 根据授权明细表id集合删除授权明细
                                if (!CollectionUtils.isEmpty(authDetailList) && authDetailList.size() > 0) {
                                    List<Long> authIds = authDetailList.stream().map(AuthDetail::getId).collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(authIds)) {
                                        logger.info("本次逻辑删除的【项目】授权明细表数据信息,authIds={},", authIds);
                                        xmglAddTemporarilyMapper.deleteAuthDetailByIds(authIds);
                                    }

                                    // 获取权限主表id集合
                                    List<Long> authMainIds = authDetailList.stream().map(AuthDetail::getAuthMainId).distinct().collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(authMainIds)) {
                                        logger.info("本次逻辑删除的【项目】授权主表数据信息,authMainIds={},", authMainIds);
                                        xmglAddTemporarilyMapper.deleteAuthMainByIds(authMainIds);
                                    }
                                }
                            }
                        }
                    }

                    /** 删除公司信息 */
                    // 获取本次新增立项项目时使用到的公司信息
                    List<Long> custList1 = new ArrayList<>();// 担保公司id集合
                    List<Long> partnerList1 = new ArrayList<>();// 资产方
                    List<Long> fundList1 = new ArrayList<>();// 资金方
                    List<Long> otherUnitList1 = new ArrayList<>();// 其他类型的公司id
                    List<Long> allList = new ArrayList<>();// 以上四种公司类型id集合
                    // 担保公司
                    List<XmglProjectCompanyRelevance> custList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("0", deployId);
                    if (!CollectionUtils.isEmpty(custList) && custList.size() > 0) {
                        custList1 = custList.stream().map(XmglProjectCompanyRelevance::getId).collect(Collectors.toList());
                        allList.addAll(custList1);
                    }

                    // 资产方
                    List<XmglProjectCompanyRelevance> partnerList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("1", deployId);
                    if (!CollectionUtils.isEmpty(partnerList) && partnerList.size() > 0) {
                        partnerList1 = partnerList.stream().map(XmglProjectCompanyRelevance::getId).collect(Collectors.toList());
                        allList.addAll(partnerList1);
                    }
                    // 资金方
                    List<XmglProjectCompanyRelevance> fundList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("2", deployId);
                    if (!CollectionUtils.isEmpty(fundList) && fundList.size() > 0) {
                        fundList1 = fundList.stream().map(XmglProjectCompanyRelevance::getId).collect(Collectors.toList());
                        allList.addAll(fundList1);
                    }
                    // 其他
                    List<XmglProjectCompanyRelevance> otherUnitList = xmglRelevanceServiceImpl.queryDataObjectByTypeAndId("3", deployId);
                    if (!CollectionUtils.isEmpty(fundList) && fundList.size() > 0) {
                        otherUnitList1 = fundList.stream().map(XmglProjectCompanyRelevance::getId).collect(Collectors.toList());
                        allList.addAll(otherUnitList1);
                    }
                    // 从新增临时表中删除本次用到的且包含公司id的数据
                    companyIdList.removeAll(allList);
                    // 根据新增的公司id集合查询数据
                    if (!CollectionUtils.isEmpty(companyIdList) && companyIdList.size() > 0) {
                        List<SysCompanyVo> companyList = addTemporarilyService.selectSysCompanyInfoListById(companyIdList);
                        List<Long> companyIds = new ArrayList<>();
                        if (!CollectionUtils.isEmpty(companyList) && companyList.size() > 0) {
                            for (SysCompanyVo sysCompany : companyList) {
                                if (sysCompany.getCheckStatus().equals("0")) {
                                    // sysCompanyService.deleteSysCompanyById(sysCompany.getId());
                                    companyIds.add(sysCompany.getId());
                                }
                            }
                            if (!CollectionUtils.isEmpty(companyIds)) {
                                logger.info("逻辑删除的公司信息,companyIds={},", companyIds);
                                addTemporarilyService.deleteCompanyInfoByCompanyIds(companyIds);
                                /** 删除授权信息 */
                                logger.info("删除公司授权信息");
                                // 首先根据项目id查询授权明细表
                                List<AuthDetail> authDetailList = addTemporarilyService.selectAuthDetailByDeployId(companyIds, "2");
                                // 根据授权明细表id集合删除授权明细
                                if (!CollectionUtils.isEmpty(authDetailList) && authDetailList.size() > 0) {
                                    List<Long> authIds = authDetailList.stream().map(AuthDetail::getId).collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(authIds)) {
                                        logger.info("本次逻辑删除的【公司】授权明细表数据信息,authIds={},", authIds);
                                        xmglAddTemporarilyMapper.deleteAuthDetailByIds(authIds);
                                    }

                                    // 获取权限主表id集合
                                    List<Long> authMainIds = authDetailList.stream().map(AuthDetail::getAuthMainId).distinct().collect(Collectors.toList());
                                    if (!CollectionUtils.isEmpty(authMainIds)) {
                                        logger.info("本次逻辑删除的【公司】授权主表数据信息,authMainIds={},", authMainIds);
                                        xmglAddTemporarilyMapper.deleteAuthMainByIds(authMainIds);
                                    }
                                }
                            }
                        }
                    }

                    /** 删除公司类型 */
                    // 获取本次新增立项项目使用到的所有公司的所有公司类型
                    if (!CollectionUtils.isEmpty(allList) && allList.size() > 0) {
                        // 根据公司id集合查询公司的所有类型dict_code集合
                        List<Long> dictCodeList = addTemporarilyService.selectProjectCompanyRelevanceListByIds(allList, deployId);
                        companyTypeList.removeAll(dictCodeList);
                    }
                    if (!CollectionUtils.isEmpty(companyTypeList) && companyTypeList.size() > 0) {
                        // 删除公司类型
                        logger.info("逻辑删除的公司类型信息,companyTypeList={},", companyTypeList);
                        addTemporarilyService.deleteCompanyTypeByIds(companyTypeList);
                    }
                }
                /** 没有关联流程 直接全部删除 */
            } else {
                logger.info("本次新增的立项项目没有关联流程，或用户(" + SecurityUtils.getLoginUser().getUser().getNickName() + ")还没有提交流程，直接点击取消或关闭页签，直接删除本次新增的所有数据");
                // 根据新增的项目id集合查询数据
                if (!CollectionUtils.isEmpty(deployIdList) && deployIdList.size() > 0) {
                    List<OaProjectDeploy> deployList = addTemporarilyService.selectOaProjectDeployInfoById(deployIdList);
                    List<Long> deployIds = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(deployList) && deployList.size() > 0) {
                        for (OaProjectDeploy oaProjectDeploy : deployList) {
                            if (oaProjectDeploy.getCheckStatus().equals("0")) {
                                // oaProjectDeployService.deleteOaProjectDeployById(oaProjectDeploy.getId());
                                deployIds.add(oaProjectDeploy.getId());
                            }
                        }
                        if (!CollectionUtils.isEmpty(deployIds)) {
                            logger.info("本次逻辑删除的项目信息,deployIds={},", deployIds);
                            addTemporarilyService.deleteOaProjectDeployByIds(deployIds);
                        }
                    }
                }
                // 根据新增的公司id集合查询数据
                if (!CollectionUtils.isEmpty(companyIdList) && companyIdList.size() > 0) {
                    List<SysCompanyVo> companyList = addTemporarilyService.selectSysCompanyInfoListById(companyIdList);
                    List<Long> companyIds = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(companyList) && companyList.size() > 0) {
                        for (SysCompanyVo sysCompany : companyList) {
                            if (sysCompany.getCheckStatus().equals("0")) {
                                // sysCompanyService.deleteSysCompanyById(sysCompany.getId());
                                companyIds.add(sysCompany.getId());
                            }
                        }
                        if (!CollectionUtils.isEmpty(companyIds)) {
                            logger.info("逻辑删除的公司信息,companyIds={},", companyIds);
                            addTemporarilyService.deleteCompanyInfoByCompanyIds(companyIds);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(companyTypeList) && companyTypeList.size() > 0) {
                    // 删除公司类型
                    logger.info("逻辑删除的公司类型信息,companyTypeList={},", companyTypeList);
                    addTemporarilyService.deleteCompanyTypeByIds(companyTypeList);
                }
            }
        }
        return 1;
    }

    /**
     * 项目汇总
     *
     * @param xmglProject
     * @return
     */
    @Override
    public List<XmglProjectSummary> getProjectSummaryList(XmglProject xmglProject) {
        List<XmglProjectSummary> xmglProjectSummaries = new ArrayList<>();
        if (xmglProject.getPageNum() != null && xmglProject.getPageSize() != null) {
            PageHelper.startPage(xmglProject.getPageNum(), xmglProject.getPageSize());
        }
        xmglProjectSummaries = xmglProjectMapper.selectXmglProjectSummaryList(xmglProject);

        // 为空返回直接空集合
        if (CollectionUtils.isEmpty(xmglProjectSummaries)) {
            return new ArrayList<>();
        }
        // 不为空
        for (XmglProjectSummary projectSummary : xmglProjectSummaries) {
            // 进度状态
            String proStatus = XmglProjectEnum.getName(projectSummary.getProjectStatus());
            projectSummary.setProjectStatusLabel(proStatus);
        }

        List<Long> xmglProjectIds = xmglProjectSummaries.stream().map(XmglProjectSummary::getId).collect(Collectors.toList());
        List<XmglProjectTypeRelevance> typeByProjectIds = xmglRelevanceServiceImpl.getTypeByProjectIds(xmglProjectIds);
        // 业务类型 改 产品类型
        Map<Long, List<XmglProjectTypeRelevance>> businessTypeMap = typeByProjectIds.stream().filter(vo -> "1".equals(vo.getDataType()))
                .collect(Collectors.groupingBy(XmglProjectTypeRelevance::getProjectId));
        //
        Map<Long, List<XmglProjectTypeRelevance>> projectTypeMap = typeByProjectIds.stream().filter(vo -> "0".equals(vo.getDataType()))
                .collect(Collectors.groupingBy(XmglProjectTypeRelevance::getProjectId));

        for (XmglProjectSummary deployVo1 : xmglProjectSummaries) {
            List<XmglProjectTypeRelevance> businessTypeData = businessTypeMap.get(deployVo1.getId());
            if (businessTypeData != null && !businessTypeData.isEmpty()) {
                Map<Long, OaDataManage> businessType = oaDataManageService.selectDataManageListByCode("business_type").stream()
                        .filter(vo -> vo.getParentId() != null) // 过滤最上一级节点
                        .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
                for (XmglProjectTypeRelevance businessTypeRe : businessTypeData) {
                    String dataNameWithParents = getDataNameWithParents(businessTypeRe.getTypeId(), businessType);
                    if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                        dataNameWithParents = dataNameWithParents.substring(1);
                    }
                    businessTypeRe.setTypeName(dataNameWithParents);
                }
            }
            deployVo1.setXmglBusinessTypeList(businessTypeData);

            List<XmglProjectTypeRelevance> projectTypeData = projectTypeMap.get(deployVo1.getId());
            if (projectTypeData != null && !projectTypeData.isEmpty()) {
                Map<Long, OaDataManage> projectType = oaDataManageService.selectDataManageListByCode("project_type").stream()
                        .filter(vo -> vo.getParentId() != null) // 过滤最上一级节点
                        .collect(Collectors.toMap(OaDataManage::getId, oaDataManage -> oaDataManage));
                for (XmglProjectTypeRelevance projectTypeRe : projectTypeData) {
                    String dataNameWithParents = getDataNameWithParents(projectTypeRe.getTypeId(), projectType);
                    if (dataNameWithParents != null && dataNameWithParents.length() > 1) {
                        dataNameWithParents = dataNameWithParents.substring(1);
                    }
                    projectTypeRe.setTypeName(dataNameWithParents);
                }
            }
            deployVo1.setXmglProjectTypeList(projectTypeData);
        }

        return xmglProjectSummaries;
    }

    /**
     * 存量数据导入
     *
     * @param importProjectVo
     * @return
     */
    @Override
    public String importFileData(List<ImportProjectVo> importProjectVo) {
        int count = 0;
        String message = "";
        XmglProject xmglProject = new XmglProject();
        if (CollectionUtils.isEmpty(importProjectVo)) {
            message = "请检查文件是否为空";
            return message;
        }
        // 循环处理
        for (int i = 0; i <= importProjectVo.size(); i++) {
            // 项目名称查重
            XmglProject project = xmglProjectMapper.selectXmglProjectByProjectName(importProjectVo.get(i).getProjectName());
            if (!Objects.isNull(project)) {
                continue;
            }
            // 先根据项目名称字段查询业务信息配置中是否存在项目
            OaProjectDeploy oaProjectDeploy = xmglProjectMapper.selectDeployInfoByProjectName(importProjectVo.get(i).getProjectName());
            // 不为空，证明存量数据可以与业务信息配置中的项目匹配上
            if (!Objects.isNull(oaProjectDeploy)) {
                count++;
                ImportProjectVo projectVo = importProjectVo.get(i);
                // 设置默认值
                /** 新权限改造后，以下字段不需要再传值，此处防止不报错，设置默认值 开始 */
                projectVo.setUserId(0L);// 责任人id字段在新权限未用到，此处设置成当前审批用户id
                projectVo.setBusinessType("-");
                projectVo.setFundShortName("-");
                projectVo.setFundFullName("-");
                projectVo.setCustFullName("-");
                projectVo.setCustShortName("-");
                projectVo.setProductFullName("-");
                projectVo.setProductShortName("-");
                projectVo.setCreateBr("admin");
                projectVo.setBusinessType("-");
                projectVo.setCreateTime(DateUtils.getNowDate());
                // 存量数据只有前五种状态，此处进度状态和项目状态保持一致
                projectVo.setProjectStatus(projectVo.getScheduleStatus());

                // 解锁倒计时
                if (projectVo.getLockStatus().equals("0")) {
                    Date date = this.getDate(projectVo.getToUnlockDay());
                    projectVo.setOverTime(date);
                } else {
                    projectVo.setOverTime(DateUtils.getNowDate());
                }

                // 渠道方
                String channelSide = "";
                if (null != projectVo.getChannelSide()) {
                    channelSide = projectVo.getChannelSide();
                }
                // 渠道方类型
                String channelType = "";
                if (null != projectVo.getChannelType()) {
                    channelType = projectVo.getChannelType();
                }
                // 如果是内部渠道方
                if (null != channelType && channelType.equals("1")) {
                    // 入库后取到立项项目id
                    BeanUtil.copyProperties(projectVo, xmglProject);
                    // 内部渠道方在xmgl_project表中渠道方字段没有值，外部渠道方才有
                    xmglProject.setChannelSide("");
                    xmglProjectMapper.insertXmglProject(xmglProject);
                    // 分割渠道方名称
                    String[] split = channelSide.split(",");
                    for (String name : split) {
                        SysUser sysUser = xmglProjectMapper.selectUserInfoByNickName(name);
                        if (!Objects.isNull(sysUser)) {
                            XmglProjectChannel projectChannel = new XmglProjectChannel();
                            projectChannel.setProjectId(xmglProject.getId());
                            projectChannel.setChannelId(sysUser.getUserId());
                            projectChannel.setStatus("1");
                            projectChannel.setNickName(name);
                            projectChannel.setCreateBy("admin");
                            projectChannel.setCreateTime(DateUtils.getNowDate());
                            xmglProjectMapper.insertProjectChannelInfo(projectChannel);
                            /**
                             * 内部渠道方授权
                             */
                            this.getAuthority(sysUser.getUserId(), oaProjectDeploy.getId(), SecurityUtils.getLoginUser().getUserId());
                        }
                    }
                } else {
                    if (!channelSide.equals("") && channelSide != null) {
                        projectVo.setChannelSide(channelSide);
                    } else {
                        projectVo.setChannelSide("");
                    }
                    // 入库后取到立项项目id
                    BeanUtil.copyProperties(projectVo, xmglProject);
                    xmglProject.setCreateTime(DateUtils.getNowDate());
                    xmglProjectMapper.insertXmglProject(xmglProject);
                }

                // 项目负责人
                String projectPrincipal = "";
                if (null != projectVo.getProjectPrincipal()) {
                    projectPrincipal = projectVo.getProjectPrincipal();
                }
                if (StringUtils.isNotEmpty(projectPrincipal)) {
                    // 分割项目负责人
                    String[] split = projectPrincipal.split(",");
                    for (String name : split) {
                        XmglProjectUser xmglProjectUser = new XmglProjectUser();
                        SysUser sysUser = xmglProjectMapper.selectUserInfoByNickName(name);
                        if (!Objects.isNull(sysUser)) {
                            xmglProjectUser.setProjectId(xmglProject.getId());
                            xmglProjectUser.setUserId(sysUser.getUserId());
                            xmglProjectUser.setNickName(sysUser.getNickName());
                            // xmglProjectUser.setContactId(userform.getId());
                            xmglProjectUser.setUserFlag("0");
                            xmglProjectUser.setStatus("0");
                            xmglProjectUser.setCreateBy("admin");
                            xmglProjectUser.setCreateTime(DateUtils.getNowDate());
                            xmglProjectUserMapper.insertXmglProjectUser(xmglProjectUser);

                            /** 【业务信息配置-项目名称】中，如果新的项目负责人还没有本项目名称的权限，则项目负责人将拥有项目名称的权限。如果在其他维度已经授权拥有了该项目权限，则不处理 */

                            // 查询授权信息
                            List<AuthDetailVo> authMainList = projectChannelMapper.selectAuthorityInfo("PROJSETUP", "XMGL1", oaProjectDeploy.getId(), sysUser.getUserId());
                            // 为空则标识没有权限
                            if (CollectionUtils.isEmpty(authMainList)) {
                                this.insertAuthAndDetail(sysUser.getUserId(), oaProjectDeploy.getId(), SecurityUtils.getLoginUser().getUserId(), "XMGL1", "PROJSETUP");
                            }

                            // List<AuthorizedFeatureDetailDTO> projectRoleList = newAuthorityServiceImpl.getProjectRoleByOaProjectDeployIdAndModuleType(oaProjectDeploy.getId(), AuthModuleEnum.PROJNAME.getCode());
                            // List<Object> proPrincipalUserld = projectRoleList.stream().filter(t -> "7".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("featureUserId")).collect(Collectors.toList());
                            // proPrincipalUserld.removeAll(Collections.singleton(null));
                            // if (!CollectionUtils.isEmpty(proPrincipalUserld)) {
                            //    List<Long> featureUserIds = proPrincipalUserld.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
                            //    //如果项目负责人没有权限，则授权
                            //    if (!featureUserIds.contains(sysUser.getUserId())) {
                            //        this.insertAuthAndDetail(sysUser.getUserId(), oaProjectDeploy.getId(), SecurityUtils.getLoginUser().getUserId(), AuthRoleEnum.XMGL1.getCode(), AuthModuleEnum.PROJNAME.getCode());
                            //    }
                            //}
                        }
                    }
                }
                // 插入立项项目-项目名称关联表
                XmglDeployProject xmglDeployProject = new XmglDeployProject();
                xmglDeployProject.setDeployId(oaProjectDeploy.getId());
                xmglDeployProject.setProjectId(xmglProject.getId());
                xmglDeployProject.setCreateBy("admin");
                xmglDeployProject.setCreateTime(DateUtils.getNowDate());
                // 如果是已停用项目，则设置关联关系为已终止状态
                if (importProjectVo.get(i).getScheduleStatus().equals("12")) {
                    xmglDeployProject.setStatus("2");
                }
                xmglProjectMapper.insertDeployProjectInfo(xmglDeployProject);

                // 项目类型
                List<ProjectTypeRelevance> projectTypeData = projectTypeRelevanceMapper.queryProjectObject("0", oaProjectDeploy.getId());
                if (null != projectTypeData && projectTypeData.size() > 0) {
                    List<XmglProjectTypeRelevance> projectTypeRelevanceList = new ArrayList<>();
                    for (ProjectTypeRelevance projectTypeRelevance : projectTypeData) {
                        XmglProjectTypeRelevance typeRelevance = new XmglProjectTypeRelevance();
                        BeanUtil.copyProperties(projectTypeRelevance, typeRelevance);
                        // 重新赋值
                        typeRelevance.setProjectId(xmglProject.getId());
                        typeRelevance.setDeployId(oaProjectDeploy.getId());
                        typeRelevance.setCreateBy(projectTypeRelevance.getCreateBy());
                        typeRelevance.setStatus("0");
                        typeRelevance.setCreateTime(DateUtils.getNowDate());
                        projectTypeRelevanceList.add(typeRelevance);
                    }
                    xmglRelevanceServiceImpl.insertProjectTypeRelevanceList(projectTypeRelevanceList);
                }

                // 业务类型
                List<ProjectTypeRelevance> businessTypeData = projectTypeRelevanceMapper.queryProjectObject("1", oaProjectDeploy.getId());
                if (null != businessTypeData && businessTypeData.size() > 0) {
                    List<XmglProjectTypeRelevance> projectTypeRelevanceList = new ArrayList<>();
                    for (ProjectTypeRelevance projectTypeRelevance : businessTypeData) {
                        XmglProjectTypeRelevance typeRelevance = new XmglProjectTypeRelevance();
                        BeanUtil.copyProperties(projectTypeRelevance, typeRelevance);
                        // 重新赋值
                        typeRelevance.setProjectId(xmglProject.getId());
                        typeRelevance.setDeployId(oaProjectDeploy.getId());
                        typeRelevance.setCreateBy(projectTypeRelevance.getCreateBy());
                        typeRelevance.setStatus("0");
                        typeRelevance.setCreateTime(DateUtils.getNowDate());
                        projectTypeRelevanceList.add(typeRelevance);
                    }
                    xmglRelevanceServiceImpl.insertProjectTypeRelevanceList(projectTypeRelevanceList);
                }

                // 担保公司
                List<ProjectCompanyRelevance> custList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("0", oaProjectDeploy.getId());
                if (custList.size() > 0) {
                    List<XmglProjectCompanyRelevance> projectCompanyRelevanceList = new ArrayList<>();
                    for (ProjectCompanyRelevance projectCompanyRelevance : custList) {
                        XmglProjectCompanyRelevance companyRelevance = new XmglProjectCompanyRelevance();
                        BeanUtil.copyProperties(projectCompanyRelevance, companyRelevance);
                        // 重新赋值
                        companyRelevance.setProjectId(xmglProject.getId());
                        companyRelevance.setDeployId(oaProjectDeploy.getId());
                        companyRelevance.setCreateBy(projectCompanyRelevance.getCreateBy());
                        companyRelevance.setStatus("0");
                        companyRelevance.setCreateTime(DateUtils.getNowDate());
                        projectCompanyRelevanceList.add(companyRelevance);
                    }
                    xmglRelevanceServiceImpl.insertProjectCompanyRelevanceList(projectCompanyRelevanceList);
                }

                // 资产方
                List<ProjectCompanyRelevance> partnerList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("1", oaProjectDeploy.getId());
                if (partnerList.size() > 0) {
                    List<XmglProjectCompanyRelevance> projectCompanyRelevanceList = new ArrayList<>();
                    for (ProjectCompanyRelevance projectCompanyRelevance : partnerList) {
                        XmglProjectCompanyRelevance companyRelevance = new XmglProjectCompanyRelevance();
                        BeanUtil.copyProperties(projectCompanyRelevance, companyRelevance);
                        // 重新赋值
                        companyRelevance.setProjectId(xmglProject.getId());
                        companyRelevance.setDeployId(oaProjectDeploy.getId());
                        companyRelevance.setCreateBy(projectCompanyRelevance.getCreateBy());
                        companyRelevance.setStatus("0");
                        companyRelevance.setCreateTime(DateUtils.getNowDate());
                        projectCompanyRelevanceList.add(companyRelevance);
                    }
                    xmglRelevanceServiceImpl.insertProjectCompanyRelevanceList(projectCompanyRelevanceList);
                }

                // 资金方
                List<ProjectCompanyRelevance> fundList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("2", oaProjectDeploy.getId());
                if (fundList.size() > 0) {
                    List<XmglProjectCompanyRelevance> projectCompanyRelevanceList = new ArrayList<>();
                    for (ProjectCompanyRelevance projectCompanyRelevance : fundList) {
                        XmglProjectCompanyRelevance companyRelevance = new XmglProjectCompanyRelevance();
                        BeanUtil.copyProperties(projectCompanyRelevance, companyRelevance);
                        // 重新赋值
                        companyRelevance.setProjectId(xmglProject.getId());
                        companyRelevance.setDeployId(oaProjectDeploy.getId());
                        companyRelevance.setCreateBy(projectCompanyRelevance.getCreateBy());
                        companyRelevance.setStatus("0");
                        companyRelevance.setCreateTime(DateUtils.getNowDate());
                        projectCompanyRelevanceList.add(companyRelevance);
                    }
                    xmglRelevanceServiceImpl.insertProjectCompanyRelevanceList(projectCompanyRelevanceList);
                }

                // 其他
                List<ProjectCompanyRelevance> otherUnitList = projectCompanyRelevanceMapper.queryDataObjectByTypeAndId("3", oaProjectDeploy.getId());
                if (otherUnitList.size() > 0) {
                    List<XmglProjectCompanyRelevance> projectCompanyRelevanceList = new ArrayList<>();
                    for (ProjectCompanyRelevance projectCompanyRelevance : otherUnitList) {
                        XmglProjectCompanyRelevance companyRelevance = new XmglProjectCompanyRelevance();
                        BeanUtil.copyProperties(projectCompanyRelevance, companyRelevance);
                        // 重新赋值
                        companyRelevance.setProjectId(xmglProject.getId());
                        companyRelevance.setDeployId(oaProjectDeploy.getId());
                        companyRelevance.setCreateBy(projectCompanyRelevance.getCreateBy());
                        companyRelevance.setStatus("0");
                        companyRelevance.setCreateTime(DateUtils.getNowDate());
                        projectCompanyRelevanceList.add(companyRelevance);
                    }
                    xmglRelevanceServiceImpl.insertProjectCompanyRelevanceList(projectCompanyRelevanceList);
                }
            }
        }
        message = "导入成功！共【" + count + "】条";
        return message;
    }

    /**
     * 授权项目名称-业务负责人
     *
     * @return
     */
    @Override
    public int initProjectPersonCharge() {
        int i = 0;
        Date date30 = new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000 * 3L);
        // 查询所有存量数据的项目负责人
        List<XmglDeployProject> deployProjectList = xmglProjectMapper.selectInventoryUsers(date30);
        if (!CollectionUtils.isEmpty(deployProjectList)) {
            for (XmglDeployProject deployProject : deployProjectList) {
                // 删除旧的业务负责人
                oaEditApproveGeneralityUserMapper.deleteDataByAppIdAndUserType("4", deployProject.getDeployId(), "1");

                // 根据立项项目id查询业务负责人
                List<XmglProjectUser> projectUserList = xmglProjectMapper.selectProjectUserListByProjectId(deployProject.getProjectId());
                // 项目名称模块同样新增业务负责人
                OaEditApproveGeneralityUser financialStaffUser = new OaEditApproveGeneralityUser();
                // oaApplyType为4代表事项目名称配置功能
                financialStaffUser.setOaApplyType("4");
                financialStaffUser.setOaApplyId(deployProject.getDeployId());
                financialStaffUser.setUserFlag("1");
                financialStaffUser.setStatus("0");
                String nickName = sysUserMapper.selectUserById(SecurityUtils.getUserId()).getNickName();
                financialStaffUser.setCreateBy(nickName);
                financialStaffUser.setCreateTime(DateUtils.getNowDate());
                financialStaffUser.setUpdateBy(nickName);
                financialStaffUser.setUpdateTime(DateUtils.getNowDate());
                // 循环
                if (!CollectionUtils.isEmpty(projectUserList) && projectUserList.size() > 0) {
                    for (XmglProjectUser userList : projectUserList) {
                        i = i + 1;
                        financialStaffUser.setUserId(userList.getUserId());
                        oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
                    }
                }
            }
        }
        return i;
    }

    /**
     * 获取当前日期前n天或后n天的日期
     *
     * @param number
     * @return
     */
    private Date getDate(Integer number) {
        // 获取 Calendar 实例
        Calendar calendar = Calendar.getInstance();
        // 设置为当前日期
        calendar.setTime(new Date());
        // 将日期减去一天
        calendar.add(Calendar.DATE, number);
        // 获取前一天日期
        Date date = calendar.getTime();
        return date;
    }

    /**
     * 内部渠道方授权授权业务相关模块
     * 如果内部渠道方用户在该模块没有任何一种权限，默认授予用户此模块的查看权限
     * 如果该用户已在此模块有其他任何一种权限，则不做处理
     *
     * @param userId   用户id
     * @param deployId 业务项目名称id
     * @param createId 授权人id(立项项目的创建人id)
     */
    public void getAuthority(Long userId, Long deployId, Long createId) {
        List<AuthTemplateDetail> authTemplateDetailList = new ArrayList<>();
        List<Map<String, Object>> list = AuthTemplateEnum.getList();
        // 查询模板
        AuthTemplate authTemplate = projectChannelMapper.selectAuthTemplate("xmgl", "XMGL");
        if (Objects.isNull(authTemplate)) {
            return;
        }
        authTemplateDetailList = projectChannelMapper.selectAuthTemplateDetail(authTemplate.getId());
        for (AuthTemplateDetail authTemplateDetail : authTemplateDetailList) {
            Boolean flag = false;
            // 根据类型和项目id查询用户在该模块下是否有权限
            for (Map<String, Object> tempEnum : list) {
                if (tempEnum.get("tempCode").equals(authTemplateDetail.getPermissionRule())) {
                    // 角色标识
                    String roleCode = (String) tempEnum.get("roleCode");
                    // 查询authMain主表id
                    List<AuthDetailVo> authMainList = projectChannelMapper.selectAuthorityInfo(authTemplateDetail.getModuleType(), roleCode, deployId, userId);
                    // 为空则标识没有权限
                    if (CollectionUtils.isEmpty(authMainList)) {
                        this.insertAuthAndDetail(userId, deployId, createId, roleCode, authTemplateDetail.getModuleType());
                    }
                }
            }
        }
    }

    /**
     * 更换内部渠道方或项目负责人时，取消老的渠道方用户或项目负责人权限及其下级授过权的数据
     *
     * @param channelList 要去除权限的内部渠道方集合
     * @param deployId    关联的业务信息中的项目id
     */
    public void cancelAuthority(List<XmglProjectChannel> channelList, Long deployId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<AuthTemplateDetail> authTemplateDetailList = new ArrayList<>();
        List<Map<String, Object>> list = AuthTemplateEnum.getList();

        // 查询模板
        AuthTemplate authTemplate = projectChannelMapper.selectAuthTemplate("xmgl", "XMGL");
        if (Objects.isNull(authTemplate)) {
            return;
        }
        // 内部渠道方/项目负责人集合
        if (CollectionUtils.isEmpty(channelList)) {
            return;
        }
        authTemplateDetailList = projectChannelMapper.selectAuthTemplateDetail(authTemplate.getId());

        // 获取内部渠道方/项目负责人的userName
        List<SysUser> userList = projectChannelMapper.selectUserInfoByUserIdList(channelList);

        // 获取各个内部渠道方/项目负责人的所有下级，如果当前渠道方/项目负责人有给下级授权，则取消下级的授权
        List<Long> userIdList = new ArrayList<>();
        // 获取渠道方用户集合中所有下属
        for (SysUser sysUser : userList) {
            PersonnelArchivesVo personnelArchivesVo = personnelArchivesMapper.queryPersonnelOrganization(sysUser.getUserName());
            if (!Objects.isNull(personnelArchivesVo)) {
                List<Long> collect = new ArrayList<>();
                List<PersonnelArchivesVo> result = new ArrayList<>();
                // 获取所有下级的id
                List<PersonnelArchivesVo> subordinatesUserIdList = this.collectSubordinates(personnelArchivesVo, result);
                // 取用户id
                collect = subordinatesUserIdList.stream().map(PersonnelArchivesVo::getUserId).collect(Collectors.toList());
                userIdList.addAll(collect);
            }
        }

        // 各个渠道方/项目负责人及其所有下属id集合
        for (Long userId : userIdList) {
            // 遍历模板
            for (AuthTemplateDetail authTemplateDetail : authTemplateDetailList) {
                // 获取模板中每个模块的标识
                for (Map<String, Object> tempEnum : list) {
                    if (tempEnum.get("tempCode").equals(authTemplateDetail.getPermissionRule())) {
                        // 角色标识
                        String roleCode = (String) tempEnum.get("roleCode");
                        // 查询authMain主表id
                        List<AuthMain> authMainList = projectChannelMapper.selectAuthMainInfo(authTemplateDetail.getModuleType(), roleCode, deployId, userId);
                        if (!CollectionUtils.isEmpty(authMainList)) {
                            List<Long> collect = authMainList.stream().map(AuthMain::getId).collect(Collectors.toList());
                            // 根据权限主表id批量更新状态为1已失效
                            projectChannelMapper.updateAuthMainStatusByMainId(collect, loginUser.getUsername());
                            // 批量更新权限副表状态为1已失效
                            projectChannelMapper.updateAuthDetailStatusByMainId(collect, loginUser.getUsername());
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取自己和所有下级的id
     */
    private List<PersonnelArchivesVo> collectSubordinates(PersonnelArchivesVo current, List<PersonnelArchivesVo> result) {
        // 将当前对象添加到结果列表中
        result.add(current);

        // 递归处理下属集合
        if (current.getSubordinateList() != null) {
            for (PersonnelArchivesVo subordinate : current.getSubordinateList()) {
                collectSubordinates(subordinate, result);
            }
        }
        return result;
    }

    /**
     * 内部渠道方授权
     *
     * @return
     */
    @Override
    public int initProjectChannel() {
        int result = 0;
        Date date = this.getDate(-13);
        List<XmglProject> projects = xmglProjectMapper.selectXmglProjectListByTime(date);
        for (XmglProject project : projects) {
            // 查询内部渠道方集合
            List<XmglProjectChannel> xmglProjectChannels = projectChannelMapper.selectXmglProjectChannelListByProjectId(project.getId());
            // 查询立项项目-项目名称对应关系
            XmglDeployProject deployProject = xmglProjectDeployService.selectXmglProjectDeployByProjectId(project.getId());
            if (!Objects.isNull(deployProject)) {
                if (!CollectionUtils.isEmpty(xmglProjectChannels)) {
                    // 循环获取每个内部渠道方
                    for (XmglProjectChannel xmglProjectChannel : xmglProjectChannels) {
                        getAuthority(xmglProjectChannel.getChannelId(), deployProject.getDeployId(), SecurityUtils.getLoginUser().getUserId());
                        result = result + 1;
                    }
                }
            }
        }
        return result;
    }

    /**
     * 立项项目列表导出
     *
     * @param xmglProject
     * @param loginUser
     * @return
     */
    @Override
    public List<XmglProject> selectXmglProjectListExport(XmglProject xmglProject, LoginUser loginUser) {
        List<XmglProject> xmglProjects = new ArrayList<>();
        xmglProject.setStatus("0");
        String s = DateUtils.dateTime();
        List<Long> newAuditIds = new ArrayList<>();
        // 获取当前登录用户有项目任何一种角色权限的项目(项目负责人、业务管理员、查看权限)
        PageHelper.clearPage();
        List<Long> authProjectIds = getNewAuthorityServiceImpl.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.PROJSETUP.getCode());
        if (CollectionUtils.isEmpty(authProjectIds)) {
            return xmglProjects;
        }
        /**
         * 权限：
         * 用户可见的项目，由通用授权功能确定。共有3种角色：项目负责人(业务负责人)、业务管理员、查看权限。在列表中用户能看到有项目任何一种角色权限的项目
         * 运营部岗位或渠道部岗位或各地CEO岗位的用户，能看到【新增项目】【认领项目】按钮
         * 新增立项审核中状态的项目，仅申请人和业务管理员可见
         */
        // 查询所有当前用户有查看权限，且不是'新增立项审核中状态'的项目名称id
        List<Long> projectIds = xmglProjectMapper.selectNoXZLXSHStatusProject(authProjectIds);
        // 查询业务管理员可见且状态为'新增立项审核'的项目名称id

        // 查询当前用户是否是所有项目的业务管理员或某些项目的业务管理员角色
        List<AuthDetailVo> ywProjectIds = xmglProjectMapper.selectYWManagerAndCreateByProjects(AuthModuleEnum.PROJSETUP.getCode(), AuthRoleEnum.XMGL2.getCode(), loginUser.getUserId());
        List<Long> deployIds = new ArrayList<>();
        Boolean flag = false;
        // 根据权限主表id查询附表信息
        if (!CollectionUtils.isEmpty(ywProjectIds)) {
            for (AuthDetailVo authDetailVo : ywProjectIds) {
                if (flag) {
                    break;
                }
                List<AuthDetailVo> detailVoList = xmglProjectMapper.selectDeployIdInfoByAuthMainId(authDetailVo.getAuthMainId());
                // 不为空，则是通过通用授权给的权限，需查询当前用户有哪些项目的业务管理员权限
                if (!CollectionUtils.isEmpty(detailVoList)) {
                    for (AuthDetailVo detailVo : detailVoList) {
                        if (detailVo.getStatus().equals("0")) {
                            deployIds.add(detailVo.getThirdTableId());
                        }
                    }
                } else {
                    // 为空，则证明是初始化数据
                    flag = true;
                }
            }
        }
        // 是初始化权限，查询新增立项审核中的立项项目
        List<XmglProject> newAuditProject = new ArrayList<>();
        if (flag) {
            newAuditProject = xmglProjectMapper.selectNewAuditProjectInfo();
            if (!CollectionUtils.isEmpty(newAuditProject)) {
                newAuditIds = newAuditProject.stream().map(XmglProject::getId).collect(Collectors.toList());
            }
        } else {
            // 不是初始换权限，判断是否为空，不为空则查询对应的立项项目信息
            if (!CollectionUtils.isEmpty(deployIds)) {
                newAuditProject = xmglProjectMapper.selectNewAuditProjectInfoByDeployIds(deployIds);
                if (!CollectionUtils.isEmpty(newAuditProject)) {
                    newAuditIds = newAuditProject.stream().map(XmglProject::getId).collect(Collectors.toList());
                }
            }
        }

        // 获取当前用户创建的'新增立项审核'状态的项目名称id
        List<Long> loginCreate = xmglProjectMapper.selectXZLXSHStatusByCreateBy(loginUser.getUser().getNickName());
        // set去重
        Set<Long> set = new HashSet<>();
        set.addAll(projectIds);
        set.addAll(newAuditIds);
        set.addAll(loginCreate);
        // 传参立项项目id查询立项项目信息集合
        xmglProject.setDeployIds(new ArrayList<>(set));
        // 第一次进入页面，表中无数据，则直接返回
        if (CollectionUtils.isEmpty(set)) {
            return xmglProjects;
        }
        // 查看所有立项项目列表
        if (xmglProject.getScope().equals("all")) {
            if (null != xmglProject.getPageNum() && null != xmglProject.getPageSize()) {
                PageHelper.startPage(xmglProject.getPageNum(), xmglProject.getPageSize());
            }
            xmglProjects = xmglProjectMapper.selectXmglProjectList(xmglProject);
            xmglProjects.removeAll(Collections.singleton(null));
        } else {
            // 只查看本人负责的项目
            // xmglProject.setChannelType("1");// 渠道方类型
            Long principalListId = xmglProject.getUserId();// 业务负责人id
            if (null != principalListId) {
                xmglProject.setPrincipalId(principalListId);
            }
            xmglProject.setUserId(loginUser.getUserId());// 当前登陆人id
            xmglProject.setChannelSideAcc(loginUser.getUser().getUserId().toString());// 内部渠道方
            if (null != xmglProject.getPageNum() && null != xmglProject.getPageSize()) {
                PageHelper.startPage(xmglProject.getPageNum(), xmglProject.getPageSize());
            }
            xmglProjects = xmglProjectMapper.selectXmglProjectList(xmglProject);
            xmglProjects.removeAll(Collections.singleton(null));
        }
        // 组装枚举
        if (!CollectionUtils.isEmpty(xmglProjects) && xmglProjects.size() > 0) {
            assembleListExport(xmglProjects);
        }
        return xmglProjects;
    }

    /**
     * 项目立项列表组装枚举
     *
     * @param xmglProjects
     * @return
     */
    private List<XmglProject> assembleListExport(List<XmglProject> xmglProjects) {
        xmglProjects.forEach(x -> {
            // 进度状态
            String proStatus = XmglProjectEnum.getName(x.getProjectStatus());
            x.setProjectStatusLabel(proStatus);
            // 锁定状态
            /**
             * 项目状态已上线 锁定状态显示为：-                         YSX
             * 项目状态已被终止  展示进度条图形；锁定状态为-              YZZ
             * 有人认领了项目，等待OA流程审核  展示进度条；锁定状态为-     RLXMSH
             * 申请项目延期，等待OA流程审核 展示进度条图形；锁定状态为-    YQXMSH
             * 申请终止项目，等待OA流程审核 展示进度条图形；锁定状态为-    ZZXMSH
             * 用户提交新增项目申请，等待OA流程审核 不展示进度条和解锁倒计时及锁定状态  XZXMSH
             */
            if (x.getProjectStatus().equals(XmglProjectEnum.getCode("已上线")) || x.getProjectStatus().equals(XmglProjectEnum.getCode("已终止"))
                    || x.getProjectStatus().equals(XmglProjectEnum.getCode("认领项目审核中")) || x.getProjectStatus().equals(XmglProjectEnum.getCode("延期项目审核中"))
                    || x.getProjectStatus().equals(XmglProjectEnum.getCode("终止项目审核中")) || x.getProjectStatus().equals(XmglProjectEnum.getCode("新增立项审核中"))) {
                x.setLockStatus("-");
                x.setUnlockDay("-");
            }

            // 项目负责人
            if (null == x.getProjectPrincipal() || x.getProjectPrincipal().equals("")) {
                x.setProjectPrincipal("");
            }

            // 状态
            if (!x.getScheduleStatus().equals("-")) {
                String scheduleStatus = XmglProjectEnum.getName(x.getScheduleStatus());
                x.setScheduleStatusLabel(scheduleStatus);
            }
            // 渠道方类型
            String channelType = "";
            if (null != x.getChannelType()) {
                channelType = x.getChannelType();
            }
            if (!channelType.equals("") && channelType.equals("1")) {
                // 内部渠道方集合
                List<XmglProjectChannel> xmglProjectChannels = projectChannelMapper.selectXmglProjectChannelListByProjectId(x.getId());
                x.setProjectChannelList(xmglProjectChannels);
            }
            // 业务类型
            List<XmglProjectTypeRelevance> businessTypeData = xmglRelevanceServiceImpl.queryProjectObject("1", x.getId());
            String businessType = "";
            for (XmglProjectTypeRelevance businessTypeDatum : businessTypeData) {
                businessType = businessType + businessTypeDatum.getTypeName() + ",";
            }
            if (StringUtils.isNotEmpty(businessType)) {
                businessType = businessType.substring(0, businessType.lastIndexOf(","));
            }
            x.setBusinessType(businessType);
        });
        return xmglProjects;
    }


    public Map<String, List<XmglProjectCompanyRelevance>> repliceMap(Map<String, List<XmglProjectCompanyRelevance>> tableList) {
        Map<String, List<XmglProjectCompanyRelevance>> modifiedTableList = new HashMap<>();
        for (Map.Entry<String, List<XmglProjectCompanyRelevance>> entry : tableList.entrySet()) {
            String key = entry.getKey();
            List<XmglProjectCompanyRelevance> value = entry.getValue();
            // 根据 key 的值进行替换
            String newKey;
            switch (key) {
                case "custList":
                    newKey = "0List";
                    break;
                case "partnerList":
                    newKey = "1List";
                    break;
                case "fundList":
                    newKey = "2List";
                    break;
                case "otherList":
                    newKey = "3List";
                    break;
                default:
                    newKey = key; // 如果 key 不是上述值，保留原值
                    break;
            }
            // 将替换后的键值对放入新 Map
            modifiedTableList.put(newKey, value);
        }
        return modifiedTableList;
    }

    public Map<String, List<XmglProjectCompanyRelevance>> repliceTpyeMap(Map<String, List<XmglProjectCompanyRelevance>> tableList) {
        Map<String, List<XmglProjectCompanyRelevance>> modifiedTableList = new HashMap<>();
        for (Map.Entry<String, List<XmglProjectCompanyRelevance>> entry : tableList.entrySet()) {
            String key = entry.getKey();
            List<XmglProjectCompanyRelevance> value = entry.getValue();
            // 根据 key 的值进行替换
            String newKey;
            switch (key) {
                case "0List":
                    newKey = "custList";
                    break;
                case "1List":
                    newKey = "partnerList";
                    break;
                case "2List":
                    newKey = "fundList";
                    break;
                case "3List":
                    newKey = "otherList";
                    break;
                default:
                    newKey = key; // 如果 key 不是上述值，保留原值
                    break;
            }
            List<XmglProjectCompanyRelevance> updatedList = value.stream().map(
                    item -> {
                        item.setUnitType(newKey.replace("List", ""));
                        return item;
                    }
            ).collect(Collectors.toList());
            // 将替换后的键值对放入新 Map
            modifiedTableList.put(newKey, updatedList);
        }
        return modifiedTableList;
    }
}
