package org.ruoyi.core.xmglproject.service;



import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.SysCompany;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;
import org.ruoyi.core.xmglproject.domain.XmglFlowRelation;

import java.util.List;

public interface IXmglFlowRelationService {
    /**
     * 查询立项项目-审批流程关联
     *
     * @param flowId 流程id
     * @return 立项项目-审批流程关联
     */
    public XmglFlowRelation selectXmglFlowRelationById(String flowId);

    /**
     * 新增立项项目-审批流程关联
     *
     * @param xmglFlowRelation 立项项目-审批流程关联
     * @return 结果
     */
    public int insertXmglFlowRelation(XmglFlowRelation xmglFlowRelation);

    /**
     * 修改立项项目-审批流程关联
     *
     * @param xmglFlowRelation 立项项目-审批流程关联
     * @return 结果
     */
    public int updateXmglFlowRelation(XmglFlowRelation xmglFlowRelation);

    /**
     * 根据同一批次的uuid查询新增了哪些公司
     * @param addUuid
     * @return
     */
    List<Long> selectCompanyTemporarilyInfo(String addUuid);

    /**
     * 获取当前登陆人有哪些发起流程权限的公司
     * @return
     */
    Long selectLoginCompanyInfoByUserId();

    /**
     * 根据用户所选的公司，查询该公司下是否有流程模板
     * @param oaProcessTemplate
     * @return
     */
    AjaxResult getXmglProcessFlow(OaProcessTemplate oaProcessTemplate);

    /**
     * 根据立项项目id和流程分类查询流程
     * @param projectId
     * @param flowClassify
     * @return
     */
    XmglFlowRelation getFlowRelationInfoByProjectIdAndFlowClassify(Long projectId, String flowClassify);
}
