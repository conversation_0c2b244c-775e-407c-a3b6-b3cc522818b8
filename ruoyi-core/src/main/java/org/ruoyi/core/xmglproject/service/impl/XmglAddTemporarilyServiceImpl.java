package org.ruoyi.core.xmglproject.service.impl;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.vo.CompanyTypeMappingVo;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.domain.ProjectCompanyRelevance;
import org.ruoyi.core.xmglproject.domain.*;
import org.ruoyi.core.xmglproject.mapper.XmglAddTemporarilyMapper;
import org.ruoyi.core.xmglproject.mapper.XmglContactWayMapper;
import org.ruoyi.core.xmglproject.service.IXmgAddTemporarilyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class XmglAddTemporarilyServiceImpl implements IXmgAddTemporarilyService
{
    @Autowired
    private XmglAddTemporarilyMapper xmglAddTemporarilyMapper;

    /**
     * 查询新增立项项目临时
     *
     * @param id 新增立项项目临时主键
     * @return 新增立项项目临时
     */
    @Override
    public XmglAddTemporarily selectXmglAddTemporarilyById(Long id)
    {
        return xmglAddTemporarilyMapper.selectXmglAddTemporarilyById(id);
    }

    /**
     * 查询新增立项项目临时列表
     *
     * @param xmglAddTemporarily 新增立项项目临时
     * @return 新增立项项目临时
     */
    @Override
    public List<XmglAddTemporarily> selectXmglAddTemporarilyList(XmglAddTemporarily xmglAddTemporarily)
    {
        return xmglAddTemporarilyMapper.selectXmglAddTemporarilyList(xmglAddTemporarily);
    }

    /**
     * 新增新增立项项目临时表数据
     *
     * @param xmglAddTemporarily 新增立项项目临时
     * @return 结果
     */
    @Override
    public int insertXmglAddTemporarily(XmglAddTemporarily xmglAddTemporarily)
    {
        xmglAddTemporarily.setCreateTime(DateUtils.getNowDate());
        xmglAddTemporarily.setCreateBy(SecurityUtils.getUsername());
        return xmglAddTemporarilyMapper.insertXmglAddTemporarily(xmglAddTemporarily);
    }

    /**
     * 根据addUuid查询新增立项项目过程中创建的数据
     * @param addUuid
     * @return
     */
    @Override
    public List<XmglAddTemporarily> selectXmglAddTemporarilyByAddUuid(String addUuid) {
        return xmglAddTemporarilyMapper.selectXmglAddTemporarilyByAddUuid(addUuid);
    }

    /**
     * 根据项目名称id查询临时表中是否存在新创建的数据
     * @param deployId
     * @return
     */
    @Override
    public List<XmglAddTemporarily> selectXmglAddTemporarilyByDeployId(Long deployId) {
        return xmglAddTemporarilyMapper.selectXmglAddTemporarilyByDeployId(deployId);
    }

    /**
     * 新增立项项目审批不通过时，根据项目名称id删除创建的项目信息
     * @param deployIdList
     */
    @Override
    public void deleteOaProjectDeployByIds(List<Long> deployIdList) {
        xmglAddTemporarilyMapper.deleteOaProjectDeployByIds(deployIdList);
    }

    /**
     * 新增立项项目审批不通过时，根据项目名称id删除创建的公司
     * @param companyIdList
     */
    @Override
    public void deleteCompanyInfoByCompanyIds(List<Long> companyIdList) {
        xmglAddTemporarilyMapper.deleteCompanyInfoByCompanyIds(companyIdList);
    }

    /**
     * 新增立项项目审批不通过时，根据项目名称id删除创建的公司类型信息
     * @param companyTypeList
     */
    @Override
    public void deleteCompanyTypeByIds(List<Long> companyTypeList) {
        xmglAddTemporarilyMapper.deleteCompanyTypeByIds(companyTypeList);
    }

    /**
     * 根据项目名称id集合更新项目名称状态
     * @param deployIdList
     */
    @Override
    public void updateOaProjectDeployCheckStatusByIds(List<Long> deployIdList) {
        xmglAddTemporarilyMapper.updateOaProjectDeployCheckStatusByIds(deployIdList);
    }

    /**
     * 根据公司id集合更新项目公司状态
     * @param companyIdList
     */
    @Override
    public void updateCompanyCheckStatusByIds(List<Long> companyIdList) {
        xmglAddTemporarilyMapper.updateCompanyCheckStatusByIds(companyIdList);
    }

    /**
     * 根据项目id集合查询数据
     * @param deployIdList
     * @return
     */
    @Override
    public List<OaProjectDeploy> selectOaProjectDeployInfoById(List<Long> deployIdList) {
        return xmglAddTemporarilyMapper.selectOaProjectDeployInfoById(deployIdList);
    }

    /**
     * 根据公司id集合查询数据
     * @param companyIdList
     * @return
     */
    @Override
    public List<CompanyTypeMappingVo> selectSysCompanyInfoById(List<Long> companyIdList) {
        return xmglAddTemporarilyMapper.selectSysCompanyInfoById(companyIdList);
    }

    /**
     * 获取当前登陆人有哪些发起流程权限的公司
     * @return
     */
    @Override
    public List<SysCompany> selectLoginCompanyInfoByUserId(Long userId) {
        return xmglAddTemporarilyMapper.selectLoginCompanyInfoByUserId(userId);
    }

    /**
     * 发起流程时校验当前当前公司是否存在流程模板
     * 13新增立项项目申请 14修改立项项目申请 15认领立项项目申请 16修改项目项目申请 17延期立项项目申请 18终止立项项目申请
     * @param oaProcessTemplate
     * @return
     */
    @Override
    public AjaxResult getXmglProcessFlow(OaProcessTemplate oaProcessTemplate) {
        OaProcessTemplate template = new OaProcessTemplate();
        //28运营部新增立项项目申请
        if (oaProcessTemplate.getOaModuleType().equals("28")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的运营部新增立项项目申请模版或用户未授权，请联系管理员");
            }
        }
        //29运营部修改立项项目申请
        if (oaProcessTemplate.getOaModuleType().equals("29")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的运营部修改立项项目申请模版或用户未授权，请联系管理员");
            }
        }
        //30运营部认领立项项目申请
        if (oaProcessTemplate.getOaModuleType().equals("30")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的运营部认领立项项目申请模版或用户未授权，请联系管理员");
            }
        }
        //31运营部修改项目属性申请
        if (oaProcessTemplate.getOaModuleType().equals("31")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的运营部修改项目属性申请模版或用户未授权，请联系管理员");
            }
        }
        //32运营部延期立项项目申请
        if (oaProcessTemplate.getOaModuleType().equals("32")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的运营部延期立项项目申请模版或用户未授权，请联系管理员");
            }
        }
        //33运营部终止立项项目申请
        if (oaProcessTemplate.getOaModuleType().equals("33")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的运营部终止立项项目申请模版或用户未授权，请联系管理员");
            }
        }
        //34渠道部新增立项项目申请
        if (oaProcessTemplate.getOaModuleType().equals("34")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的渠道部新增立项项目申请模版或用户未授权，请联系管理员");
            }
        }
        //35渠道部修改立项项目申请
        if (oaProcessTemplate.getOaModuleType().equals("35")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的渠道部修改立项项目申请模版或用户未授权，请联系管理员");
            }
        }
        //36渠道部认领立项项目申请
        if (oaProcessTemplate.getOaModuleType().equals("36")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的渠道部认领立项项目申请模版或用户未授权，请联系管理员");
            }
        }
        //37渠道部修改项目属性申请
        if (oaProcessTemplate.getOaModuleType().equals("37")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的渠道部修改项目属性申请模版或用户未授权，请联系管理员");
            }
        }
        //38渠道部延期立项项目申请
        if (oaProcessTemplate.getOaModuleType().equals("38")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的渠道部延期立项项目申请模版或用户未授权，请联系管理员");
            }
        }
        //39渠道部终止立项项目申请
        if (oaProcessTemplate.getOaModuleType().equals("39")){
            template = xmglAddTemporarilyMapper.queryTemplateByOaModuleTypeCompanyIdAndModuleType(oaProcessTemplate);
            if (Objects.isNull(template)){
                return AjaxResult.error("没有查询到启用的渠道部终止立项项目申请模版或用户未授权，请联系管理员");
            }
        }
        return AjaxResult.success(template);
    }

    /**
     * 根据公司id集合查询公司信息
     * @param companyIdList
     * @return
     */
    @Override
    public List<SysCompanyVo> selectSysCompanyInfoListById(List<Long> companyIdList) {
        return xmglAddTemporarilyMapper.selectSysCompanyInfoListById(companyIdList);
    }

    /**
     * 根据本次统一批次新增的项目名称id查询立项项目id
     * @param deployIdList
     * @return
     */
    @Override
    public List<XmglDeployProject> selectXmglProjectInfoListByDeployIds(List<Long> deployIdList) {
        return xmglAddTemporarilyMapper.selectXmglProjectInfoListByDeployIds(deployIdList);
    }

    /**
     * 根据公司id集合获取所有公司的所有公司类型
     * @param allList
     * @return
     */
    @Override
    public List<Long> selectProjectCompanyRelevanceListByIds(List<Long> allList, Long deployId) {
        return xmglAddTemporarilyMapper.selectProjectCompanyRelevanceListByIds(allList,deployId);
    }

    /**
     * 根据项目id查询授权明细表信息
     * @param deployIds
     * @return
     */
    @Override
    public List<AuthDetail> selectAuthDetailByDeployId(List<Long> applyIds, String type) {
        return xmglAddTemporarilyMapper.selectAuthDetailByDeployId(applyIds, type);
    }

}
