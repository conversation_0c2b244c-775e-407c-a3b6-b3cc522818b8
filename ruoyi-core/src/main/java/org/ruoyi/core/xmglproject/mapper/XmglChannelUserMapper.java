package org.ruoyi.core.xmglproject.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.xmglproject.domain.ChannelUserTreeVo;
import org.ruoyi.core.xmglproject.domain.XmglChannelUser;

import java.util.List;

/**
 * 内部渠道方Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-30
 */
public interface XmglChannelUserMapper 
{
    /**
     * 查询内部渠道方
     * 
     * @param userId 内部渠道方主键
     * @return 内部渠道方
     */
    public XmglChannelUser selectXmglChannelUserByUserId(Long userId);

    /**
     * 查询内部渠道方列表
     * 
     * @param xmglChannelUser 内部渠道方
     * @return 内部渠道方集合
     */
    public List<XmglChannelUser> selectXmglChannelUserList(XmglChannelUser xmglChannelUser);

    /**
     * 新增内部渠道方
     * 
     * @param xmglChannelUser 内部渠道方
     * @return 结果
     */
    public int insertXmglChannelUser(XmglChannelUser xmglChannelUser);

    /**
     * 修改内部渠道方
     * 
     * @param xmglChannelUser 内部渠道方
     * @return 结果
     */
    public int updateXmglChannelUser(XmglChannelUser xmglChannelUser);

    /**
     * 删除内部渠道方
     * 
     * @param userId 内部渠道方主键
     * @return 结果
     */
    public int deleteXmglChannelUserByUserId(Long userId);

    /**
     * 批量删除内部渠道方
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXmglChannelUserByUserIds(@Param("userIds") List<Long> userIds);

    /**
     * 获取所有内部渠道方用户
     * @return
     */
    List<XmglChannelUser> selectChannelUserInfoList();

    /**
     * 获取所有内部公司信息
     * @return
     */
    List<ChannelUserTreeVo> selectCompanyInfoList();

    /**
     * 内部渠道方列表(有分页)
     * @param xmglChannelUser
     * @return
     */
    List<XmglChannelUser> queryChannelUserList(XmglChannelUser xmglChannelUser);

    /**
     * 新增之前先删除所有数据
     */
    void deleteXmglChannelUser();
}
