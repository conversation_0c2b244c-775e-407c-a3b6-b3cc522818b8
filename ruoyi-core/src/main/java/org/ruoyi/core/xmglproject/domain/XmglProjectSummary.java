package org.ruoyi.core.xmglproject.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 【请填写功能名称】对象 xmgl_project
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public class XmglProjectSummary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;
    // @Excel(name = "项目负责人")
    private String userName;

    /** 资金方简称 */
    // @Excel(name = "资金方简称")
    private String fundShortName;

    /** 资金方全称 */
    // @Excel(name = "资金方全称")
    private String fundFullName;

    /** 资产方简称 */
    // @Excel(name = "资产方简称")
    private String productShortName;

    /** 资产方全称 */
    // @Excel(name = "资产方全称")
    private String productFullName;

    /** 担保公司简称 */
    // @Excel(name = "担保公司简称")
    private String custShortName;

    /** 担保公司全称 */
    // @Excel(name = "担保公司全称")
    private String custFullName;

    /** 负责人id */
//    @Excel(name = "负责人id")
    private Long userId;

    /** 内部渠道方名称(用户id) */
    private String channelSideAcc;

    /** 项目描述 */
    //@Excel(name = "项目描述")
    private String projectDescribe;

    /** add by niey 新权限改造  2024-7-2 */
    /** add by niey 新权限改造  2024-7-2 */

    /** 项目名称 */
    @Excel(name = "项目名称",width = 40)
    private String projectName;

    /** 业务类型(反参用) */
    @Excel(name = "业务类型",width = 30)
    private String businessType;

    /** 项目类型(反参用) */
    private String projectType;

    /** 项目负责人 */
    private String projectPrincipal;

    /** 外部渠道方名称 */
    private String channelSide;

    /** 立项时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "立项时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date projectDate;

    /** 进度状态(1对接中 2尽调中 3待上会 4合作中 5合同签署 6新增立项审核中 7认领项目审核中 8延期项目审核中 9终止项目审核中 10修改立项审核中 11修改项目审核中 12已终止) */
    //@Excel(name = "项目状态",readConverterExp = "1=对接中, 2=尽调中, 3=待上会, 4=合作中, 5=合同签署, 6=新增立项审核中, 7=认领项目审核中, 8=延期项目审核中, 9=终止项目审核中, 10=修改立项审核中, 11=修改项目审核中, 12=已终止")
    private String projectStatus;

    /** 进度状态值 */
    @Excel(name = "进度状态")
    private String projectStatusLabel;

    /** 进度(1对接中 2尽调中 3待上会 4合作中 5合同签署) */
    // @Excel(name = "进度状态",readConverterExp = "0=已联系,1=洽谈中,2=准入中,3=合作中")
    private String scheduleStatus;

    /** 进度值 */
    @Excel(name = "进度")
    private String scheduleStatusLabel;

    /** 锁定状态(0已锁定 1未锁定) */
    private String lockStatus;

    /** 认领状态 */
    private String claimed;

    /** 锁定时间 */
    private String unlockDay;

    /** 项目名称id */
    private Long deployId;

    /** 项目负责人集合(接参用) */
    private List<Long> projectUser;

    /** 内部渠道方集合(接参用) */
    private List<Long> channelForm;

    /** 渠道方类型(1内部 2外部) */
    private String channelType;

    /** 查看范围(接参用) all所有 won当前本人正在担任项目负责人或内部渠道方的项目 */
    private String scope;

    /** 担保公司id */
    private Long custNo;

    /** 资产方id */
    private Long partnerNo;

    /** 资金方id */
    private Long fundNo;

    /** 分页参数(条数) */
    private Integer pageSize;

    /** 分页参数(页数) */
    private Integer pageNum;

    /** 项目id(项目名称模块的项目id) */
    private List<Long> deployIds;

    /** 资产方(反参用) */
    private List<XmglProjectCompanyRelevance> partnerList;

    /** 资金方(反参用) */
    private List<XmglProjectCompanyRelevance> fundList;

    /** 担保公司(反参用) */
    private List<XmglProjectCompanyRelevance> custList;

    /** 其他公司(反参用) */
    private List<XmglProjectCompanyRelevance> otherUnitList;

    /** 项目类型 */
    private List<XmglProjectTypeRelevance> xmglProjectTypeList;

    /** 业务类型 */
    private List<XmglProjectTypeRelevance> xmglBusinessTypeList;

    /** 内部渠道方集合(反参用) */
    private List<XmglProjectChannel> projectChannelList;

    /** 项目负责人(反参用) */
    private List<XmglProjectUser> projectPrincipalList;

    /** 立项项目列表页'操作'列表的权限标识(反参用) */
    private XmglPostRoleFlag postRoleFlag;

    /** 认领人id */
    private Long claimantId;

    /** 新增标识，用来判断此次新增是否有新添加的数据 */
    private String addUuid;

    public String getAddUuid() {
        return addUuid;
    }

    public void setAddUuid(String addUuid) {
        this.addUuid = addUuid;
    }

    public Long getClaimantId() {
        return claimantId;
    }

    public void setClaimantId(Long claimantId) {
        this.claimantId = claimantId;
    }

    public List<XmglProjectCompanyRelevance> getOtherUnitList() {
        return otherUnitList;
    }

    public void setOtherUnitList(List<XmglProjectCompanyRelevance> otherUnitList) {
        this.otherUnitList = otherUnitList;
    }

    public List<XmglProjectTypeRelevance> getXmglProjectTypeList() {
        return xmglProjectTypeList;
    }

    public void setXmglProjectTypeList(List<XmglProjectTypeRelevance> xmglProjectTypeList) {
        this.xmglProjectTypeList = xmglProjectTypeList;
    }

    public List<XmglProjectTypeRelevance> getXmglBusinessTypeList() {
        return xmglBusinessTypeList;
    }

    public void setXmglBusinessTypeList(List<XmglProjectTypeRelevance> xmglBusinessTypeList) {
        this.xmglBusinessTypeList = xmglBusinessTypeList;
    }

    public String getClaimed() {
        return claimed;
    }

    public void setClaimed(String claimed) {
        this.claimed = claimed;
    }

    public XmglPostRoleFlag getPostRoleFlag() {
        return postRoleFlag;
    }

    public void setPostRoleFlag(XmglPostRoleFlag postRoleFlag) {
        this.postRoleFlag = postRoleFlag;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public List<XmglProjectUser> getProjectPrincipalList() {
        return projectPrincipalList;
    }

    public void setProjectPrincipalList(List<XmglProjectUser> projectPrincipalList) {
        this.projectPrincipalList = projectPrincipalList;
    }

    public List<XmglProjectChannel> getProjectChannelList() {
        return projectChannelList;
    }

    public void setProjectChannelList(List<XmglProjectChannel> projectChannelList) {
        this.projectChannelList = projectChannelList;
    }

    public List<XmglProjectCompanyRelevance> getPartnerList() {
        return partnerList;
    }

    public void setPartnerList(List<XmglProjectCompanyRelevance> partnerList) {
        this.partnerList = partnerList;
    }

    public List<XmglProjectCompanyRelevance> getFundList() {
        return fundList;
    }

    public void setFundList(List<XmglProjectCompanyRelevance> fundList) {
        this.fundList = fundList;
    }

    public List<XmglProjectCompanyRelevance> getCustList() {
        return custList;
    }

    public void setCustList(List<XmglProjectCompanyRelevance> custList) {
        this.custList = custList;
    }

    public List<Long> getDeployIds() {
        return deployIds;
    }

    public void setDeployIds(List<Long> deployIds) {
        this.deployIds = deployIds;
    }

    public String getChannelSideAcc() {
        return channelSideAcc;
    }

    public void setChannelSideAcc(String channelSideAcc) {
        this.channelSideAcc = channelSideAcc;
    }

    public String getProjectPrincipal() {
        return projectPrincipal;
    }

    public void setProjectPrincipal(String projectPrincipal) {
        this.projectPrincipal = projectPrincipal;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public List<Long> getChannelForm() {
        return channelForm;
    }

    public void setChannelForm(List<Long> channelForm) {
        this.channelForm = channelForm;
    }

    public Long getCustNo() {
        return custNo;
    }

    public void setCustNo(Long custNo) {
        this.custNo = custNo;
    }

    public Long getPartnerNo() {
        return partnerNo;
    }

    public void setPartnerNo(Long partnerNo) {
        this.partnerNo = partnerNo;
    }

    public Long getFundNo() {
        return fundNo;
    }

    public void setFundNo(Long fundNo) {
        this.fundNo = fundNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public String getChannelSide() {
        return channelSide;
    }

    public void setChannelSide(String channelSide) {
        this.channelSide = channelSide;
    }

    public String getProjectDescribe() {
        return projectDescribe;
    }

    public void setProjectDescribe(String projectDescribe) {
        this.projectDescribe = projectDescribe;
    }

    public String getUnlockDay() {
        return unlockDay;
    }

    public void setUnlockDay(String unlockDay) {
        this.unlockDay = unlockDay;
    }

    public XmglProjectSummary() {
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public XmglProjectSummary(Long id, String businessType, String fundShortName, String fundFullName, String productShortName, String productFullName, String custShortName, String custFullName, Long userId, String userName, String channelSide, String projectDescribe, Date projectDate, String projectStatus, String scheduleStatus, String lockStatus, String unlockDay, Date overTime, String status, String createBr, String updateBr) {
        this.id = id;
        this.businessType = businessType;
        this.fundShortName = fundShortName;
        this.fundFullName = fundFullName;
        this.productShortName = productShortName;
        this.productFullName = productFullName;
        this.custShortName = custShortName;
        this.custFullName = custFullName;
        this.userId = userId;
        this.userName = userName;
        this.channelSide = channelSide;
        this.projectDescribe = projectDescribe;
        this.projectDate = projectDate;
        this.projectStatus = projectStatus;
        this.scheduleStatus = scheduleStatus;
        this.lockStatus = lockStatus;
        this.unlockDay = unlockDay;
        this.overTime = overTime;
        this.status = status;
        this.createBr = createBr;
        this.updateBr = updateBr;
    }

    /** 锁定期结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date overTime;

    /** 锁定期开始时间 */
    private Date beginTime;

    /** 锁定期时长 */
    private Integer someTimes;

    /** 最后一次更新项目进度时间 */
    private Date updateStatusTime;

    /** 已x天未更新进度(反参用) */
    private int lastUpdateTimes;

    /** 是否启用(0启用 1删除) */
//    @Excel(name = "是否启用(0启用 1删除)")
    private String status;

    /** 创建人 */
//    @Excel(name = "创建人")
    private String createBr;

    /** 修改人 */
//    @Excel(name = "修改人")
    private String updateBr;

    public int getLastUpdateTimes() {
        return lastUpdateTimes;
    }

    public void setLastUpdateTimes(int lastUpdateTimes) {
        this.lastUpdateTimes = lastUpdateTimes;
    }

    public Date getUpdateStatusTime() {
        return updateStatusTime;
    }

    public void setUpdateStatusTime(Date updateStatusTime) {
        this.updateStatusTime = updateStatusTime;
    }

    public Integer getSomeTimes() {
        return someTimes;
    }

    public void setSomeTimes(Integer someTimes) {
        this.someTimes = someTimes;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setBusinessType(String businessType) 
    {
        this.businessType = businessType;
    }

    public String getBusinessType() 
    {
        return businessType;
    }
    public void setFundShortName(String fundShortName) 
    {
        this.fundShortName = fundShortName;
    }

    public String getFundShortName() 
    {
        return fundShortName;
    }
    public void setFundFullName(String fundFullName) 
    {
        this.fundFullName = fundFullName;
    }

    public String getFundFullName() 
    {
        return fundFullName;
    }
    public void setProductShortName(String productShortName) 
    {
        this.productShortName = productShortName;
    }

    public String getProductShortName() 
    {
        return productShortName;
    }
    public void setProductFullName(String productFullName) 
    {
        this.productFullName = productFullName;
    }

    public String getProductFullName() 
    {
        return productFullName;
    }
    public void setCustShortName(String custShortName) 
    {
        this.custShortName = custShortName;
    }

    public String getCustShortName() 
    {
        return custShortName;
    }
    public void setCustFullName(String custFullName) 
    {
        this.custFullName = custFullName;
    }

    public String getCustFullName() 
    {
        return custFullName;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setProjectDate(Date projectDate) 
    {
        this.projectDate = projectDate;
    }

    public Date getProjectDate() 
    {
        return projectDate;
    }
    public void setProjectStatus(String projectStatus) 
    {
        this.projectStatus = projectStatus;
    }

    public String getProjectStatus() 
    {
        return projectStatus;
    }
    public void setScheduleStatus(String scheduleStatus) 
    {
        this.scheduleStatus = scheduleStatus;
    }

    public String getScheduleStatus() 
    {
        return scheduleStatus;
    }
    public void setLockStatus(String lockStatus) 
    {
        this.lockStatus = lockStatus;
    }

    public String getLockStatus() 
    {
        return lockStatus;
    }
    public void setOverTime(Date overTime) 
    {
        this.overTime = overTime;
    }

    public Date getOverTime() 
    {
        return overTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setCreateBr(String createBr) 
    {
        this.createBr = createBr;
    }

    public String getCreateBr() 
    {
        return createBr;
    }
    public void setUpdateBr(String updateBr) 
    {
        this.updateBr = updateBr;
    }

    public String getUpdateBr() 
    {
        return updateBr;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getDeployId() {
        return deployId;
    }

    public void setDeployId(Long deployId) {
        this.deployId = deployId;
    }

    public List<Long> getProjectUser() {
        return projectUser;
    }

    public void setProjectUser(List<Long> projectUser) {
        this.projectUser = projectUser;
    }

    public String getProjectStatusLabel() {
        return projectStatusLabel;
    }

    public void setProjectStatusLabel(String projectStatusLabel) {
        this.projectStatusLabel = projectStatusLabel;
    }

    public String getScheduleStatusLabel() {
        return scheduleStatusLabel;
    }

    public void setScheduleStatusLabel(String scheduleStatusLabel) {
        this.scheduleStatusLabel = scheduleStatusLabel;
    }

    @Override
    public String toString() {
        return "XmglProject{" +
                "id=" + id +
                ", businessType='" + businessType + '\'' +
                ", fundShortName='" + fundShortName + '\'' +
                ", fundFullName='" + fundFullName + '\'' +
                ", productShortName='" + productShortName + '\'' +
                ", productFullName='" + productFullName + '\'' +
                ", custShortName='" + custShortName + '\'' +
                ", custFullName='" + custFullName + '\'' +
                ", userId=" + userId +
                ", channelSide='" + channelSide + '\'' +
                ", projectDescribe='" + projectDescribe + '\'' +
                ", projectDate=" + projectDate +
                ", projectStatus='" + projectStatus + '\'' +
                ", scheduleStatus='" + scheduleStatus + '\'' +
                ", lockStatus='" + lockStatus + '\'' +
                ", unlockDay='" + unlockDay + '\'' +
                ", overTime=" + overTime +
                ", status='" + status + '\'' +
                ", createBr='" + createBr + '\'' +
                ", updateBr='" + updateBr + '\'' +
                '}';
    }


}
