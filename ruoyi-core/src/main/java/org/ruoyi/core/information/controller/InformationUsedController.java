package org.ruoyi.core.information.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.information.domain.InformationUsed;
import org.ruoyi.core.information.service.IInformationUsedService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 资料用印Controller
 *
 * <AUTHOR>
 * @date 2023-12-01
 *
 */
@RestController
@RequestMapping("/information/used")
public class InformationUsedController extends BaseController
{
    @Autowired
    private IInformationUsedService informationUsedService;

    /**
     * 查询资料用印列表
     */
    //@PreAuthorize("@ss.hasPermi('information:used:list')")
    @GetMapping("/list")
    public TableDataInfo list(InformationUsed informationUsed)
    {
        //startPage();
        List<InformationUsed> list = informationUsedService.selectInformationUsedList(informationUsed);
        return getDataTable(list);
    }

    /**
     * 导出资料用印列表
     */
    //@PreAuthorize("@ss.hasPermi('information:used:export')")
    @Log(title = "资料用印", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InformationUsed informationUsed)
    {
        List<InformationUsed> list = informationUsedService.selectInformationUsedList(informationUsed);
        ExcelUtil<InformationUsed> util = new ExcelUtil<InformationUsed>(InformationUsed.class);
        util.exportExcel(response, list, "资料用印数据");
    }

    /**
     * 获取资料用印详细信息
     */
    //@PreAuthorize("@ss.hasPermi('information:used:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(informationUsedService.selectInformationUsedById(id));
    }

    /**
     * 新增资料用印
     */
    //@PreAuthorize("@ss.hasPermi('information:used:add')")
    @Log(title = "资料用印", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InformationUsed informationUsed)
    {
        return toAjax(informationUsedService.insertInformationUsed(informationUsed));
    }

    /**
     * 修改资料用印
     */
    //@PreAuthorize("@ss.hasPermi('information:used:edit')")
    @Log(title = "资料用印", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InformationUsed informationUsed)
    {
        return toAjax(informationUsedService.updateInformationUsed(informationUsed));
    }

    /**
     * 删除资料用印
     */
    //@PreAuthorize("@ss.hasPermi('information:used:remove')")
    @Log(title = "资料用印", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(informationUsedService.deleteInformationUsedByIds(ids));
    }

}
