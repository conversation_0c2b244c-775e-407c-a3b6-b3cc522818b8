package org.ruoyi.core.information.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysPostAO;
import com.ruoyi.common.core.domain.entity.SysUnit;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.mapper.SysPostMapper;
import com.ruoyi.system.service.INewAuthorityService;
import com.ruoyi.system.service.ISysPostService;
import org.ruoyi.core.information.domain.*;
import org.ruoyi.core.information.domain.vo.InformationCatalogueVO;
import org.ruoyi.core.information.domain.vo.InformationVO;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.information.mapper.InformationCatalogueMapper;
import org.ruoyi.core.information.mapper.InformationMapper;
import org.ruoyi.core.information.service.IAuthorityService;
import org.ruoyi.core.information.service.IInformationCatalogueService;
import org.ruoyi.core.information.service.IInformationProcessService;
import org.ruoyi.core.information.service.IInformationService;
import org.ruoyi.core.oasystem.domain.vo.ProcessTemplateVo;
import org.ruoyi.core.oasystem.service.IOaProcessTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 资料Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-14
 *
 */
@Service
public class InformationServiceImpl implements IInformationService
{
    @Autowired
    private InformationMapper informationMapper;
    @Autowired
    private InformationCatalogueMapper informationCatalogueMapper;
    @Autowired
    private IInformationCatalogueService iInformationCatalogueService;
    @Autowired
    private SysPostMapper sysPostMapper;
    @Autowired
    private ISysPostService iSysPostService;
    @Autowired
    private IAuthorityService authorityService;
    @Autowired
    private IInformationProcessService informationProcessService;
    @Autowired
    private INewAuthorityService newAuthorityService;
    /**
     * 查询资料
     *
     * @param id 资料主键
     * @return 资料
     */
    @Override
    public Information selectInformationById(Long id)
    {
        return informationMapper.selectInformationById(id);
    }


    /**
     * 查询资料列表
     *
     * @param information 资料
     * @return 资料
     */
    @Override
    public List<InformationVO> selectInformationList(InformationVO information)
    {
        InformationCatalogue informationCatalogue = new InformationCatalogue();

        //查询登陆人的拥有权限目录
        informationCatalogue.setParentId(information.getCatalogueId());
        List<InformationCatalogueVO> informationCatalogueVOList = iInformationCatalogueService.selectInformationCatalogueListNoLimit(informationCatalogue);
        List<Long> catalogueIds = informationCatalogueVOList.stream().map(InformationCatalogueVO::getId).collect(Collectors.toList());
        catalogueIds.add(information.getCatalogueId());
        information.setCatalogueIds(catalogueIds);


        PageUtil.startPage();//解决若依框架一个接口查询多个list分页失效问题
        information.setAuditState("1");
        return informationMapper.selectAllInformationList(information);

//        if (!catalogueIds.isEmpty()) {
//            //查询资料目录的名称
//            Map<Long, String> idNameMap = informationCatalogueMapper.selectInformationCatalogueByIds(catalogueIds)
//                    .stream().collect(Collectors.toMap(InformationCatalogue::getId, InformationCatalogue::getCatalogueName));
//
//            Map<Long, String> structureMap = new HashMap<>();
//            //形成目录链条
//            catalogueIds.forEach(catalogueId -> {
//                structureMap.put(catalogueId, getStructure(catalogueId));
//            });
//            informationList.forEach(info -> {
//                info.setStructure(structureMap.get(info.getCatalogueId()));
//                info.setCatalogueName(idNameMap.get(info.getCatalogueId()));
//            });
//        }


    }

    @Override
    public List<InformationVO> selectAllInformationList(InformationVO information)
    {
        InformationCatalogue informationCatalogue = new InformationCatalogue();

        //查询登陆人的拥有权限目录
        informationCatalogue.setParentId(information.getCatalogueId());
        List<InformationCatalogueVO> informationCatalogueVOList = iInformationCatalogueService.selectInformationCatalogueListNoLimit(informationCatalogue);
        List<Long> catalogueIds = informationCatalogueVOList.stream().map(InformationCatalogueVO::getId).collect(Collectors.toList());
        catalogueIds.add(information.getCatalogueId());
        information.setCatalogueIds(catalogueIds);


        PageUtil.startPage();//解决若依框架一个接口查询多个list分页失效问题
        return informationMapper.selectAllInformationList(information);
    }

    @Override
    public List<InformationVO> selectInformationAbandonedList(InformationVO information)
    {
        InformationCatalogue informationCatalogue = new InformationCatalogue();
        //查询登陆人的拥有权限目录
        List<InformationCatalogueVO> informationCatalogueVOList = informationCatalogueMapper.selectInformationCatalogueList(informationCatalogue);
        if (informationCatalogueVOList.isEmpty()){
            return new ArrayList<InformationVO>();
        }
        information.setCatalogueIds(informationCatalogueVOList.stream().map(InformationCatalogueVO::getId).collect(Collectors.toList()));

        PageUtil.startPage();//解决若依框架一个接口查询多个list分页失效问题
        return informationMapper.selectInformationAbandonedList(information);
    }

    /**
     * 导出全部列表资料
     * @param information
     * @return
     */
    public List<InformationVO> exportAllInformationList(InformationVO information) {
        if (information.getIds() != null &&!"[]".equals(information.getIds())){
            information.setIds(information.getIds().substring(1, information.getIds().length() - 1));  // 去除首尾的方括号
            String[] array = information.getIds().split(",");
            List<String> list = new ArrayList<>();
            for (String s : array) {
                list.add(s.replaceAll("\"", ""));
            }
            information.setIdArray(list);
        }

        InformationCatalogue informationCatalogue = new InformationCatalogue();

        //查询登陆人的拥有权限目录
        informationCatalogue.setParentId(information.getCatalogueId());
        List<InformationCatalogueVO> informationCatalogueVOList = iInformationCatalogueService.selectInformationCatalogueListNoLimit(informationCatalogue);
        List<Long> auCatalogueIds = informationCatalogueVOList.stream().map(InformationCatalogueVO::getId).collect(Collectors.toList());
        auCatalogueIds.add(information.getCatalogueId());
        information.setCatalogueIds(auCatalogueIds);

        List<InformationVO> informationList = informationMapper.selectAllInformationList(information);

        Set<Long> catalogueIds= informationList.stream().map(Information::getCatalogueId).collect(Collectors.toSet());

        if (!catalogueIds.isEmpty()) {
            Map<Long, String> idNameMap = informationCatalogueMapper.selectInformationCatalogueByIds(catalogueIds)
                    .stream().collect(Collectors.toMap(InformationCatalogue::getId, InformationCatalogue::getCatalogueName));
            Map<Long, String> structureMap = new HashMap<>();
            catalogueIds.forEach(catalogueId -> {
                structureMap.put(catalogueId, getStructure(catalogueId));
            });
            informationList.forEach(info -> {
                info.setCatalogueName(idNameMap.get(info.getCatalogueId()));
                info.setStructure(structureMap.get(info.getCatalogueId()));
            });
        }

        return informationList.stream()
                .peek(info -> {
                    if ("1".equals(info.getAuditState())) {
                        info.setAuditStateName("审核通过");
                    } else if ("2".equals(info.getAuditState())) {
                        info.setAuditStateName("未审核");
                    } else if ("3".equals(info.getAuditState())) {
                        info.setAuditStateName("审核不通过");
                    } else if ("4".equals(info.getAuditState())) {
                        info.setAuditStateName("审核中");
                    }


                    if("0".equals(info.getSaveFlag())){
                        info.setSaveFlagName("非永久");
                    } else if ("1".equals(info.getSaveFlag())){
                        info.setSaveFlagName("永久");
                    }

                    if("0".equals(info.getSubmitState())){
                        info.setSubmitStateName("未提交");
                    } else if ("1".equals(info.getSubmitState())){
                        info.setSubmitStateName("已提交");
                    }

                })
                .collect(Collectors.toList());
    }
    /**
     * 导出授权列表资料
     * @param information
     * @return
     */
    public List<InformationVO> exportInformationList(InformationVO information) {
        if (information.getIds() != null && !"[]".equals(information.getIds())){
            information.setIds(information.getIds().substring(1, information.getIds().length() - 1));  // 去除首尾的方括号
            String[] array = information.getIds().split(",");
            List<String> list = new ArrayList<>();
            for (String s : array) {
                list.add(s.replaceAll("\"", ""));
            }
            information.setIdArray(list);
        }

        InformationCatalogue informationCatalogue = new InformationCatalogue();

        //查询登陆人的拥有权限目录
        informationCatalogue.setParentId(information.getCatalogueId());
        List<InformationCatalogueVO> informationCatalogueVOList = iInformationCatalogueService.selectInformationCatalogueListNoLimit(informationCatalogue);
        List<Long> auCatalogueIds = informationCatalogueVOList.stream().map(InformationCatalogueVO::getId).collect(Collectors.toList());
        auCatalogueIds.add(information.getCatalogueId());
        information.setCatalogueIds(auCatalogueIds);
        information.setAuditState("1");

        List<InformationVO> informationList = informationMapper.selectAllInformationList(information);

        Set<Long> catalogueIds= informationList.stream().map(Information::getCatalogueId).collect(Collectors.toSet());

        if (!catalogueIds.isEmpty()) {
            Map<Long, String> idNameMap = informationCatalogueMapper.selectInformationCatalogueByIds(catalogueIds)
                    .stream().collect(Collectors.toMap(InformationCatalogue::getId, InformationCatalogue::getCatalogueName));
            Map<Long, String> structureMap = new HashMap<>();
            catalogueIds.forEach(catalogueId -> {
                structureMap.put(catalogueId, getStructure(catalogueId));
            });
            informationList.forEach(info -> {
                info.setCatalogueName(idNameMap.get(info.getCatalogueId()));
                info.setStructure(structureMap.get(info.getCatalogueId()));
            });
        }

        return informationList.stream()
                .peek(info -> {
                    if ("1".equals(info.getAuditState())) {
                        info.setAuditStateName("审核通过");
                    } else if ("2".equals(info.getAuditState())) {
                        info.setAuditStateName("未审核");
                    } else if ("3".equals(info.getAuditState())) {
                        info.setAuditStateName("审核不通过");
                    } else if ("4".equals(info.getAuditState())) {
                        info.setAuditStateName("审核中");
                    }

                    if("0".equals(info.getSaveFlag())){
                        info.setSaveFlagName("非永久");
                    } else if ("1".equals(info.getSaveFlag())){
                        info.setSaveFlagName("永久");
                    }

                    if("0".equals(info.getSubmitState())){
                        info.setSubmitStateName("未提交");
                    } else if ("1".equals(info.getSubmitState())){
                        info.setSubmitStateName("已提交");
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 新增资料
     *
     * @param information 资料
     * @return 结果
     */
    @Override
    public int insertInformation(Information information)
    {
        information.setCreateBy(getUsername());
        information.setCreateTime(DateUtils.getNowDate());
        information.setId(IdUtils.fastSimpleUUID());
        information.setAuditState("2");
        information.setSubmitState("0");
        information.setIsTemporary("1");
        //系统编号逻辑
        int count = getCountByCreateTime(DateUtils.getDate()) + 1;
        String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
        information.setInformationSystemCode("ZL" + createTimeNum + String.format("%03d", count));

        return informationMapper.insertInformation(information);
    }


    /**
     * 新增资料
     *
     * @param information 资料
     * @return 结果
     */
    @Override
    public AjaxResult insertCommitInformation(InformationVO information)
    {
        if ("1".equals(information.getSubmitState())){
            throw new ServiceException("【"+ information.getInformationName() +"】已提交，请勿重复提交");
        }
        int row = 0;
        if (information.getId() == null){
            information.setCreateBy(getUsername());
            information.setCreateTime(DateUtils.getNowDate());
            information.setId(IdUtils.fastSimpleUUID());
            information.setAuditState("2");
            information.setSubmitState("0");
            information.setIsTemporary("1");
            //系统编号逻辑
            int count = getCountByCreateTime(DateUtils.getDate()) + 1;
            String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
            information.setInformationSystemCode("ZL" + createTimeNum + String.format("%03d", count));
            row = informationMapper.insertInformation(information);
        } else {
            information.setAuditState("2");
            information.setSubmitState("0");

            row = informationMapper.updateInformation(information);
        }

        if( row > 0){
//            SuperviseInformationProcess informationProcess = new SuperviseInformationProcess();
//            informationProcess.setInformationId(information.getId());
//            informationProcess.setProcessId(information.getProcessId());
//            informationProcess.setIsUsed("2");
//            informationProcess.setCreateBy(getUsername());
//            informationProcess.setCreateTime(DateUtils.getNowDate());
//            informationProcessService.insertInformationProcess(informationProcess);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult = AjaxResult.success();
            ajaxResult.put("id",information.getId());
            return ajaxResult;
        };

        return AjaxResult.error();
    }

    /**
     * 新增临时资料
     *
     * @param information 资料
     * @return 结果
     */
    @Override
    public AjaxResult insertTemporaryInformation(Information information)
    {
        information.setCreateBy(getUsername());
        information.setCreateTime(DateUtils.getNowDate());
        information.setId(IdUtils.fastSimpleUUID());
        information.setAuditState("4");
        information.setCatalogueId(0L);
        information.setSubmitState("1");
        information.setSaveFlag("0");
        information.setIsTemporary("0");
        information.setInformationCode("-");

        //系统编号逻辑
        int count = getTemporaryCountByCreateTime(DateUtils.getDate()) + 1;
        String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
        information.setInformationSystemCode("LS" + createTimeNum + String.format("%04d", count));
        AjaxResult ajaxResult = new AjaxResult();
        if (informationMapper.insertInformation(information)> 0){
            ajaxResult = AjaxResult.success();
            ajaxResult.put("id",information.getId());
            return ajaxResult;
        }
        return AjaxResult.error();
    }

    /**
     * 修改资料
     *
     * @param information 资料
     * @return 结果
     */
    @Override
    public int updateInformation(Information information)
    {
        information.setUpdateTime(DateUtils.getNowDate());
        if(information.getSaveFlag().equals("1")){
            information.setSaveEndTime(null);
            information.setSaveStartTime(null);
        }
        return informationMapper.updateInformation(information);
    }

    /**
     * 整理资料
     *
     * @param information 资料
     * @return 结果
     */
    @Override
    public int arrangeInformation(InformationVO information)
    {
        return informationMapper.arrangeInformation(information);
    }

    /**
     * 批量删除资料
     *
     * @param ids 需要删除的资料主键
     * @return 结果
     */
    @Override
    public int deleteInformationByIds(String[] ids)
    {
        return informationMapper.deleteInformationByIds(ids);
    }

    /**
     * 删除资料信息
     *
     * @param id 资料主键
     * @return 结果
     */
    @Override
    public int deleteInformationById(Long id)
    {
        return informationMapper.deleteInformationById(id);
    }

    public int getCountByCatalogueId(Long[] catalogueIds){
        return informationMapper.getCountByCatalogueId(catalogueIds);
    }

    @Override
    public int getCountByCreateTime(String createTime) {
        return informationMapper.getCountByCreateTime(createTime);
    }

    @Override
    public int getTemporaryCountByCreateTime(String createTime) {
        return informationMapper.getTemporaryCountByCreateTime(createTime);
    }


    /**
     * 根据ids批量提交
     *
     * @param ids
     * @return
     */
    @Override
    public int commitInformationByIds(String[] ids) {
        if (ids.length == 0) {
            throw new ServiceException("至少选择一条数据提交");
        }

        //校验已提交数据
//        List<SuperviseInformation> submitList = getSubmitList(ids);
//        if (!submitList.isEmpty()) {
//            String result = submitList.stream()
//                    .map(information -> "【" + information.getInformationName() + "】")
//                    .collect(Collectors.joining());
//            throw new ServiceException(result + "已提交，请勿重复提交");
//        }
//
//        //校验已提交数据
//        List<SuperviseInformation> passList = getAuditPassList(ids);
//        if (!passList.isEmpty()) {
//            String result = passList.stream()
//                    .map(information -> "【" + information.getInformationName() + "】")
//                    .collect(Collectors.joining());
//            throw new ServiceException(result + "已审核通过，无法再次提交");
//        }

        //提交
        return informationMapper.commitInformationByIds(ids);
    }

    /**
     * 根据ids批量提交
     *
     * @param ids
     * @return
     */
    @Override
    public AjaxResult commitCheck(String[] ids) {
        if (ids.length == 0) {
            throw new ServiceException("至少选择一条数据提交");
        }

        //校验已提交数据
        List<Information> submitList = getSubmitList(ids);
        if (!submitList.isEmpty()) {
            String result = submitList.stream()
                    .map(information -> "【" + information.getInformationName() + "】")
                    .collect(Collectors.joining());
            throw new ServiceException(result + "已提交，请勿重复提交");
        }

        //校验已提交数据
        List<Information> passList = getAuditPassList(ids);
        if (!passList.isEmpty()) {
            String result = passList.stream()
                    .map(information -> "【" + information.getInformationName() + "】")
                    .collect(Collectors.joining());
            throw new ServiceException(result + "已审核通过，无法再次提交");
        }
        return AjaxResult.success();
    }

    /**
     * 根据ids获取
     * @param ids 资料主键
     * @return 结果
     */
    @Override
    public List<Information> getSubmitList(String[] ids) {
        return informationMapper.getSubmitList(ids);
    }

    /**
     * 根据ids批量审核通过
     *
     * @param ids
     * @return
     */
    @Override
    public int passInformationByIds(String[] ids) {
        //提交
        return informationMapper.passInformationByIds(ids);
    }

    /**
     * 根据ids批量审核不通过
     *
     * @param ids
     * @return
     */
    @Override
    public int unPassInformationByIds(String[] ids) {
        //提交
        return informationMapper.unPassInformationByIds(ids);
    }

    @Override
    public int abandonedInformationByIds(String[] ids) {
        //提交
        return informationMapper.abandonedInformationByIds(ids);
    }

    @Override
    public int unabandonedInformationByIds(String[] ids) {
        //提交
        return informationMapper.unabandonedInformationByIds(ids);
    }

    @Override
    public List<Information> getAuditPassList(String[] ids) {
        return informationMapper.getAuditPassList(ids);
    }

    public int empowerInformationByIds(String[] ids){
        //校验数据
        List<Information> passList = getUnPassList(ids);
        if (!passList.isEmpty()) {
            String result = passList.stream()
                    .map(information -> "【" + information.getInformationName() + "】")
                    .collect(Collectors.joining());
            throw new ServiceException(result + "未审核通过，无法进行赋权");
        }
        return 1;
    }

    @Override
    public List<Information> getUnPassList(String[] ids) {
        return informationMapper.getUnPassList(ids);
    }

    /**
     * 拼接目录
     * @param id
     * @return
     */
    public String getStructure(Long id){
        if (id != 0){
            StringBuilder stringBuilder = new StringBuilder();
            while ( id != 0) {
                InformationCatalogue informationCatalogue = informationCatalogueMapper.selectInformationCatalogueById(id);
                stringBuilder.insert(0,">" + informationCatalogue.getCatalogueName());
                id = informationCatalogue.getParentId();
            }
            return stringBuilder.substring(1);
        }
        return null;
    }

    /**
     *
     * @return
     */
    @Override
    public List<Information> getDownloadList() {
        SysUser user = getLoginUser().getUser();
        Map<String, Object> map = new HashMap<>();
        map.put("userId", user.getUserId());
        map.put("postIds", user.getPostIds());
        map.put("deptId", user.getDeptId());
        return informationMapper.getDownloadList(map);
    }

    /**
     * 获取过期资料
     * @param information
     * @return
     */
    @Override
    public List<Information> getExpireList(Information information) {
        Calendar calendar = Calendar.getInstance();
        Date currentDate = DateUtils.getNowDate();
        calendar.setTime(currentDate);
        // 加一天
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        // 获取结果日期
        Date nextDay = calendar.getTime();
        information.setSaveEndTime(nextDay);
        return informationMapper.getExpireList(information);
    }

    @Override
    public List<Information> getListByIds(String[] ids) {
        return informationMapper.getListByIds(ids);
    }

    @Override
    public int isAuthority(Long catalogueId) {
        List<Authority> authorities = authorityService.selectAuthorityByBillId(catalogueId + "", "0");
        //  需要全部权限才能保存
        List<Long> auDeptIds = authorities.stream()
                .filter(authority -> Objects.equals(authority.getAuthorityType(), "0"))
                //.filter(authority -> DateUtils.getNowDate().after(authority.getStartTime()) && DateUtils.getNowDate().before(authority.getEndTime()))
                .map(Authority::getAuthorityId)
                .collect(Collectors.toList());
        List<Long> auUserIds = authorities.stream()
                .filter(authority -> Objects.equals(authority.getAuthorityType(), "1"))
                //.filter(authority -> DateUtils.getNowDate().after(authority.getStartTime()) && DateUtils.getNowDate().before(authority.getEndTime()))
                .map(Authority::getAuthorityId)
                .collect(Collectors.toList());
        List<Long> auPostIds = authorities.stream()
                .filter(authority -> Objects.equals(authority.getAuthorityType(), "2"))
                // .filter(authority -> DateUtils.getNowDate().after(authority.getStartTime()) && DateUtils.getNowDate().before(authority.getEndTime()))
                .map(Authority::getAuthorityId)
                .collect(Collectors.toList());
        SysUser user = getLoginUser().getUser();
        if (!(auDeptIds.contains(user.getDeptId()) || auUserIds.contains(user.getUserId()) || auPostIds.contains(user.getPostIds()))) {
            throw new ServiceException("没有目录权限，该目录无法录入资料。");
        }
        return 1;
    }

    /**
     * 资料发放统计
     * @param information   查询条件
     * @return
     */
    @Override
    public List<InformationReleaseStatistics> getReleaseStatistics(InformationReleaseStatistics information) {
        //通过流程获取资料信息
        List<InformationReleaseStatistics> infData = informationMapper.selectInformationByFlow(information);
        //获取目录数据
        List<InformationCatalogue> catalogue = informationCatalogueMapper.selectInformationCatalogues();

        //截取资方名称
        for (int i = 0; i < infData.size(); i++) {
            String theme = infData.get(i).getTheme();
            int length = theme.length();
            infData.get(i).setTheme(theme.substring(0, length - 14));
        }

        //递归找父目录
        for (int i = 0; i < infData.size(); i++) {
            findParentCatalogue(infData, catalogue, i);
        }

        //整合数据成树形结构
        List<InformationReleaseStatistics> returnData = new ArrayList<>();
        //存放所有数据的层级结构
        Map<String,InformationReleaseStatistics> checkData = new HashMap<>();
        //存放年以下的数据
        Map<String,List<InformationReleaseStatistics>> yearDatas = new HashMap<>();

        Map<String,List<InformationReleaseStatistics>> yearData = new HashMap<>();
        //存放包含的层级
        for (int i = 0; i < infData.size(); i++) {
            //所属公司
            checkData.put(infData.get(i).getCompanyId()+"",infData.get(i));
            //所属公司+所属目录
            checkData.put(infData.get(i).getCompanyId()+"#"+infData.get(i).getCatalogueName(),infData.get(i));
        }

        //层级目录年以下的数据
        for (int i = 0; i < infData.size(); i++) {
            String ancher = infData.get(i).getCompanyId()+"#"+infData.get(i).getCatalogueName()+"@"+infData.get(i).getInformationYear();
            boolean flag = yearDatas.containsKey(ancher);
            if(flag){
                //如果存在，则累加数据
                List<InformationReleaseStatistics> informationReleaseStatistics = yearDatas.get(ancher);
                informationReleaseStatistics.add(infData.get(i));
                yearDatas.put(ancher,informationReleaseStatistics);
            }else{
                //不存在则新建数据
                List<InformationReleaseStatistics> inf = new ArrayList<>();
                inf.add(infData.get(i));
                yearDatas.put(ancher,inf);
            }
        }

        //嵌套年锚点
        for (Map.Entry<String,List<InformationReleaseStatistics>> entry : yearDatas.entrySet()) {
            List<InformationReleaseStatistics> years = new ArrayList<>();
            InformationReleaseStatistics year = new InformationReleaseStatistics();
            year.setInformationYear(entry.getValue().get(0).getInformationYear());
            year.setCompany(entry.getValue().get(0).getCompany());
            year.setCompanyId(entry.getValue().get(0).getCompanyId());
            year.setCatalogueName(entry.getValue().get(0).getCatalogueName());
            year.setSonData(entry.getValue());
            years.add(year);
            yearData.put(entry.getKey(),years);
        }

        //一级结构：所属公司
        for (Map.Entry<String,InformationReleaseStatistics> entry : checkData.entrySet()) {
            if(-1 == entry.getKey().indexOf("#") && -1 == entry.getKey().indexOf("@")){
                InformationReleaseStatistics date = new InformationReleaseStatistics();
                date.setCompany(entry.getValue().getCompany());
                date.setCompanyId(entry.getValue().getCompanyId());
                returnData.add(date);
            }
        }

        //二级结构：所属目录
        for (int i = 0; i < returnData.size(); i++) {
            List<InformationReleaseStatistics> sonData = new ArrayList<>();
            for (Map.Entry<String,InformationReleaseStatistics> entry : checkData.entrySet()) {
                //如果属于同一目录
                if((-1 != entry.getKey().indexOf("#") && -1 == entry.getKey().indexOf("@")) && returnData.get(i).getCompanyId().equals(entry.getValue().getCompanyId()) ){
                    InformationReleaseStatistics date = new InformationReleaseStatistics();
                    date.setCompanyId(entry.getValue().getCompanyId());
                    date.setCompany(entry.getValue().getCompany());
                    date.setCatalogueName(entry.getValue().getCatalogueName());
                    sonData.add(date);
                }
            }
            returnData.get(i).setSonData(sonData);
        }

        //往层级里存放年锚点数据
        for (int i = 0; i < returnData.size(); i++) {
            List<InformationReleaseStatistics> sonData = returnData.get(i).getSonData();
            for (int j = 0; j < sonData.size(); j++) {
                for (Map.Entry<String,List<InformationReleaseStatistics>> entry : yearData.entrySet()) {
                    if(entry.getKey().contains(sonData.get(j).getCompanyId()+"#"+sonData.get(j).getCatalogueName()+"@")){
                        sonData.get(j).setSonData(entry.getValue());
                    }
                }
            }

        }

        return returnData;
    }

    @Override
    public List<InformationReleaseStatistics> exportReleaseStatistics(InformationReleaseStatistics information) {
        List<InformationReleaseStatistics> infData = informationMapper.selectInformationByFlow(information);
        //截取资方名称
        for (int i = 0; i < infData.size(); i++) {
            String theme = infData.get(i).getTheme();
            int length = theme.length();
            infData.get(i).setTheme(theme.substring(0, length - 14));
        }
        return infData;
    }

    @Override
    public List<SysUnit> getUnit() {
        return informationMapper.getUnit();
    }

    /**
     * 设置父子集目录拼接
     * @param infData       整体数据
     * @param catalogue     目录数据
     * @param i
     */
    private void findParentCatalogue(List<InformationReleaseStatistics> infData, List<InformationCatalogue> catalogue, int i) {
        if(infData.get(i).getParentId() != null){
            for (int j = 0; j < catalogue.size(); j++) {
                if (infData.get(i).getParentId() == catalogue.get(j).getId()){
                    //拼接父目录名称
                    String cataName = catalogue.get(j).getCatalogueName()+">"+ infData.get(i).getCatalogueName();
                    //设置目录层级
                    infData.get(i).setCatalogueName(cataName);
                    //设置父目录
                    infData.get(i).setParentId(catalogue.get(j).getParentId());
                    //递归设置目录
                    findParentCatalogue(infData, catalogue, i);
                }
            }
        }
    }

    /**
     * 资料发放统计(资料二期)
     * @param information
     * @return
     */
    @Override
    public List<InformationVO> getinformationStatisticsList(InformationVO information) {
        SysUser user = getLoginUser().getUser();
        List<Long> comapnyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(user.getUserId(), AuthModuleEnum.SXINFORMATION.getCode());
        if (comapnyIds.isEmpty()) {
            return new ArrayList<>();
        }
        return informationMapper.getinformationStatisticsList(information)
                      .stream().peek(vo -> {
                             //临时文件所属公司赋值
                             if (vo.getOrgName() == null || vo.getOrgName().isEmpty()) {
                                 vo.setOrgName(vo.getTemporaryInformationCompany());
                             }})
                             .filter(vo -> comapnyIds.contains(vo.getUnitId()))
                             //根据资料所属公司排序,空的排在末尾
                             .sorted(Comparator.comparing(InformationVO::getOrgName,Comparator.nullsLast(Comparator.naturalOrder())))
                             .collect(Collectors.toList());
    }
}
