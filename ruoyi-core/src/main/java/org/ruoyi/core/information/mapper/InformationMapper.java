package org.ruoyi.core.information.mapper;

import com.ruoyi.common.core.domain.entity.SysUnit;
import com.ruoyi.common.core.domain.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.ruoyi.core.information.domain.Information;
import org.ruoyi.core.information.domain.InformationReleaseStatistics;
import org.ruoyi.core.information.domain.vo.InformationVO;

import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-14
 *
 */
@Mapper
public interface InformationMapper
{

    /**
     * 查询资料
     *
     * @param id 资料主键
     * @return 资料
     */
    public Information selectInformationById(Long id);

    /**
     * 查询资料列表
     *
     * @param information 资料
     * @return 资料集合
     */
    public List<InformationVO> selectInformationList(InformationVO information);

    /**
     * 新增资料
     *
     * @param information 资料
     * @return 结果
     */
    public int insertInformation(Information information);

    /**
     * 修改资料
     *
     * @param information 资料
     * @return 结果
     */
    public int updateInformation(Information information);

    public int arrangeInformation(InformationVO information);

    /**
     * 删除资料
     *
     * @param id 资料主键
     * @return 结果
     */
    public int deleteInformationById(Long id);

    /**
     * 批量删除资料
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInformationByIds(String[] ids);

    int getCountByCatalogueId(Long[] catalogueIds);

    /**
     * 获取当天创建次数
     */
    int getCountByCreateTime(String createTime);

    /**
     * 获取当天临时创建次数
     */
    int getTemporaryCountByCreateTime(String createTime);

    /**
     * 批量提交资料
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int commitInformationByIds(String[] ids);

    List<Information> getSubmitList(String[] ids);

    List<Information> getAuditPassList(String[] ids);

    List<Information> getUnPassList(String[] ids);

    public int passInformationByIds(String[] ids);

    public int unPassInformationByIds(String[] ids);

    public int abandonedInformationByIds(String[] ids);

    public int unabandonedInformationByIds(String[] ids);
    List<Information> getDownloadList(Map<String,Object> map);

    public List<InformationVO> selectAllInformationList(InformationVO information);

    public List<InformationVO> selectInformationAbandonedList(InformationVO information);

    List<Information> getExpireList(Information information);

    List<Information> getListByIds(String[] ids);

    List<InformationReleaseStatistics> selectInformationByFlow(InformationReleaseStatistics information);

    List<SysUnit> getUnit();

    public List<InformationVO> getinformationStatisticsList(InformationVO information);
}
