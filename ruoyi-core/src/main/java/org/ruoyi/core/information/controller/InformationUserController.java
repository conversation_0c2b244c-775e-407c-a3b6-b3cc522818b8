package org.ruoyi.core.information.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.information.domain.InformationUser;
import org.ruoyi.core.information.service.IInformationUserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 资料获取方Controller
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@RestController
@RequestMapping("/information/user")
public class InformationUserController extends BaseController
{
    @Autowired
    private IInformationUserService informationUserService;

    /**
     * 查询资料获取方列表
     */
    //@PreAuthorize("@ss.hasPermi('information:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(InformationUser informationUser)
    {
        startPage();
        List<InformationUser> list = informationUserService.selectInformationUserList(informationUser);
        return getDataTable(list);
    }

    /**
     * 导出资料获取方列表
     */
    //@PreAuthorize("@ss.hasPermi('information:user:export')")
    @Log(title = "资料获取方", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InformationUser informationUser)
    {
        List<InformationUser> list = informationUserService.selectInformationUserList(informationUser);
        ExcelUtil<InformationUser> util = new ExcelUtil<InformationUser>(InformationUser.class);
        util.exportExcel(response, list, "资料获取方数据");
    }

    /**
     * 获取资料获取方详细信息
     */
    //@PreAuthorize("@ss.hasPermi('information:user:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(informationUserService.selectInformationUserById(id));
    }

    /**
     * 新增资料获取方
     */
    //@PreAuthorize("@ss.hasPermi('information:user:add')")
    @Log(title = "资料获取方", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InformationUser informationUser)
    {
        return toAjax(informationUserService.insertInformationUser(informationUser));
    }

    /**
     * 修改资料获取方
     */
    //@PreAuthorize("@ss.hasPermi('information:user:edit')")
    @Log(title = "资料获取方", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InformationUser informationUser)
    {
        return toAjax(informationUserService.updateInformationUser(informationUser));
    }

    /**
     * 删除资料获取方
     */
    //@PreAuthorize("@ss.hasPermi('information:user:remove')")
    @Log(title = "资料获取方", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(informationUserService.deleteInformationUserByIds(ids));
    }
}
