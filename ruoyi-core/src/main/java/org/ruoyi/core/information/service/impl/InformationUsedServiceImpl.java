package org.ruoyi.core.information.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserPost;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.mapper.SysPostMapper;
import com.ruoyi.system.service.impl.SysDeptServiceImpl;
import com.ruoyi.system.service.impl.SysPostServiceImpl;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import org.ruoyi.core.information.domain.InformationUsed;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.information.mapper.InformationUsedMapper;
import org.ruoyi.core.information.service.IInformationUsedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 资料用印Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-01
 *
 */
@Service
public class InformationUsedServiceImpl implements IInformationUsedService
{
    @Autowired
    private InformationUsedMapper informationUsedMapper;
    @Autowired
    private SysPostServiceImpl sysPostService;
    @Autowired
    private SysDeptServiceImpl sysDeptService;
    @Autowired
    private SysUserServiceImpl sysUserService;
    /**
     * 查询资料用印
     *
     * @param id 资料用印主键
     * @return 资料用印
     */
    @Override
    public InformationUsed selectInformationUsedById(Long id)
    {
        return informationUsedMapper.selectInformationUsedById(id);
    }

    /**
     * 查询资料用印列表
     *
     * @param informationUsed 资料用印
     * @return 资料用印
     */
    @Override
    public List<InformationUsed> selectInformationUsedList(InformationUsed informationUsed)
    {
        Set<String> nameSet = new HashSet<>();
        SysUser user = getLoginUser().getUser();
        nameSet.add(user.getUserName());
        //查询用户的属于哪些部门负责人
        List<SysDept> sysDeptList = sysDeptService.selectDeptByLeaderId(user.getUserId());
        Long[] deptIds = sysDeptList.stream().map(SysDept::getDeptId).toArray(Long[]::new);
        if (deptIds.length > 0){
            List<SysUser> sysUsers = sysUserService.selectUserListByDeptIds(deptIds);
            sysUsers.forEach(sysUser -> nameSet.add(sysUser.getUserName()));
        }

        Long[] leadIds = user.getUserPostList().stream().map(SysUserPost::getPostId).toArray(Long[]::new);
        if (leadIds.length > 0){
            //查询自己岗位有哪些属于岗位负责人
            Long[] postIds = sysPostService.selectPostByleader(leadIds).stream().map(SysPost::getPostId).toArray(Long[]::new);
            if(postIds.length > 0){
                List<SysUser> sysUsers = sysUserService.selectUserListByPostId(postIds);
                sysUsers.forEach(sysUser -> nameSet.add(sysUser.getUserName()));
            }
        }
        informationUsed.setSponsorNames(nameSet.toArray(new String[0]));
        PageUtil.startPage();//解决若依框架一个接口查询多个list分页失效问题
        return informationUsedMapper.selectInformationUsedList(informationUsed);
    }

    /**
     * 新增资料用印
     *
     * @param informationUsed 资料用印
     * @return 结果
     */
    @Override
    public int insertInformationUsed(InformationUsed informationUsed)
    {
        informationUsed.setCreateTime(DateUtils.getNowDate());
        informationUsed.setCreateBy(getUsername());
        return informationUsedMapper.insertInformationUsed(informationUsed);
    }

    /**
     * 修改资料用印
     *
     * @param informationUsed 资料用印
     * @return 结果
     */
    @Override
    public int updateInformationUsed(InformationUsed informationUsed)
    {
        informationUsed.setUpdateTime(DateUtils.getNowDate());
        return informationUsedMapper.updateInformationUsed(informationUsed);
    }

    /**
     * 批量删除资料用印
     *
     * @param ids 需要删除的资料用印主键
     * @return 结果
     */
    @Override
    public int deleteInformationUsedByIds(Long[] ids)
    {
        return informationUsedMapper.deleteInformationUsedByIds(ids);
    }

    /**
     * 删除资料用印信息
     *
     * @param id 资料用印主键
     * @return 结果
     */
    @Override
    public int deleteInformationUsedById(Long id)
    {
        return informationUsedMapper.deleteInformationUsedById(id);
    }

}
