package org.ruoyi.core.information.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.information.domain.Authority;
import org.ruoyi.core.information.domain.AuthorityRecord;

/**
 * 资料权限管理Mapper
 *
 * <AUTHOR>
 * @date 2023-11-14
 *
 */
@Mapper
public interface AuthorityMapper
{

    /**
     * 根据billId获取对应的单据权限
     *
     * @param billId 单据ID
     * @return 单据对应的权限
     */
    public List<Authority> selectAuthorityByBillId(@Param("billId") String billId,@Param("billType") String billType);

    /**
     * 查询权限管理列表
     *
     * @param authority 权限管理
     * @return 权限管理集合
     */
    public List<Authority> selectAuthorityList(Authority authority);

    /**
     * 新增权限管理
     *
     * @param authority 权限管理
     * @return 结果
     */
    public int insertAuthority(@Param("authority") List<Authority> authority);

    /**
     * 修改权限管理
     *
     * @param authority 权限管理
     * @return 结果
     */
    public int updateAuthority(Authority authority);

    /**
     * 删除权限管理
     *
     * @param billId 权限管理主键
     * @return 结果
     */
    public int deleteAuthorityByBillId(String billId);

    /**
     * 批量删除权限管理
     *
     * @param billIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAuthorityByBillIds(String[] billIds);

    /**
     * 查询资料权限版本
     * @param authority 资料权限对象
     * @return
     */
    List<Authority> selectAuthorityVersion(@Param("authority") List<Authority> authority);

    /**
     * 逻辑删除历史资料权限
     * @param version
     * @return
     */
    int deleteHisAuthority(@Param("version") List<Authority> version);

    /**
     * 根据billId获取对应的单据权限--详情用
     * @param billId
     * @param billType
     * @return
     */
    List<Authority> getInfoToDet(@Param("billId") String billId, @Param("billType") String billType);

    /**
     * 查询历史信息记录-授权记录接口
     * @param billName
     * @param billCode
     * @return
     */
    List<AuthorityRecord> getAuthorizationRecord(@Param("informationName") String billName,@Param("informationCode") String billCode);

}
