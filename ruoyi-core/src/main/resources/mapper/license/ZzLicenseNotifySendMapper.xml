<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.license.mapper.ZzLicenseNotifySendMapper">

    <resultMap type="ZzLicenseNotifySend" id="ZzLicenseNotifySendResult">
        <result property="licenseId"    column="license_id"    />
        <result property="qyvxIsSend"    column="qyvx_is_send"    />
        <result property="notifyIsSend"    column="notify_is_send"    />
        <result property="auditTypeAlready"    column="audit_type_already"    />
        <result property="expireTypeAlready"    column="expire_type_already"    />
        <result property="auditTypeAbout"    column="audit_type_about"    />
        <result property="expireTypeAbout"    column="expire_type_about"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteFlag"    column="delete_flag"    />
    </resultMap>

    <sql id="selectZzLicenseNotifySendVo">
        select license_id, qyvx_is_send, notify_is_send, audit_type_already, expire_type_already, audit_type_about, expire_type_about, create_time, update_time, delete_flag from zz_license_notify_send
    </sql>

    <select id="selectZzLicenseNotifySendList" parameterType="ZzLicenseNotifySend" resultMap="ZzLicenseNotifySendResult">
        <include refid="selectZzLicenseNotifySendVo"/>
        <where>
            <if test="licenseId != null  and licenseId != ''"> and license_id = #{licenseId}</if>
            <if test="qyvxIsSend != null  and qyvxIsSend != ''"> and qyvx_is_send = #{qyvxIsSend}</if>
            <if test="notifyIsSend != null  and notifyIsSend != ''"> and notify_is_send = #{notifyIsSend}</if>
            <if test="auditTypeAlready != null  and auditTypeAlready != ''"> and audit_type_already = #{auditTypeAlready}</if>
            <if test="expireTypeAlready != null  and expireTypeAlready != ''"> and expire_type_already = #{expireTypeAlready}</if>
            <if test="auditTypeAbout != null  and auditTypeAbout != ''"> and audit_type_about = #{auditTypeAbout}</if>
            <if test="expireTypeAbout != null  and expireTypeAbout != ''"> and expire_type_about = #{expireTypeAbout}</if>
            <if test="deleteFlag != null  and deleteFlag != ''"> and delete_flag = #{deleteFlag}</if>
        </where>
    </select>

    <select id="selectZzLicenseNotifySendByLicenseId" parameterType="String" resultMap="ZzLicenseNotifySendResult">
        <include refid="selectZzLicenseNotifySendVo"/>
        where license_id = #{licenseId} and delete_flag = '0'
    </select>

    <select id="selectZzLicenseSendNotifyByLicenseId"
            resultType="org.ruoyi.core.license.domain.ZzLicenseNotifySend">
        <include refid="selectZzLicenseNotifySendVo"/>
        where license_id = #{licenseId} and delete_flag = '0'
    </select>

    <insert id="insertZzLicenseNotifySend" parameterType="ZzLicenseNotifySend">
        insert into zz_license_notify_send
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="licenseId != null and licenseId != ''">license_id,</if>
            <if test="qyvxIsSend != null">qyvx_is_send,</if>
            <if test="notifyIsSend != null">notify_is_send,</if>
            <if test="auditTypeAlready != null">audit_type_already,</if>
            <if test="expireTypeAlready != null">expire_type_already,</if>
            <if test="auditTypeAbout != null">audit_type_about,</if>
            <if test="expireTypeAbout != null">expire_type_about,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteFlag != null">delete_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="licenseId != null and licenseId != ''">#{licenseId},</if>
            <if test="qyvxIsSend != null">#{qyvxIsSend},</if>
            <if test="notifyIsSend != null">#{notifyIsSend},</if>
            <if test="auditTypeAlready != null">#{auditTypeAlready},</if>
            <if test="expireTypeAlready != null">#{expireTypeAlready},</if>
            <if test="auditTypeAbout != null">#{auditTypeAbout},</if>
            <if test="expireTypeAbout != null">#{expireTypeAbout},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteFlag != null">#{deleteFlag},</if>
        </trim>
    </insert>

    <update id="updateZzLicenseNotifySend" parameterType="ZzLicenseNotifySend">
        <choose>
            <when test="auditTypeAlready != null and auditTypeAlready != '' and auditTypeAlready == '1'.toString">
                update zz_license_notify_send
                <trim prefix="SET" suffixOverrides=",">
                    <if test="qyvxIsSend != null">qyvx_is_send = #{qyvxIsSend},</if>
                    <if test="notifyIsSend != null">notify_is_send = #{notifyIsSend},</if>
                    <if test="auditTypeAlready != null">audit_type_already = #{auditTypeAlready},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                </trim>
                where license_id = #{licenseId} and audit_type_already = '1' and delete_flag = '0'
            </when>
            <when test="expireTypeAlready != null and expireTypeAlready != '' and expireTypeAlready == '1'.toString">
                update zz_license_notify_send
                <trim prefix="SET" suffixOverrides=",">
                    <if test="qyvxIsSend != null">qyvx_is_send = #{qyvxIsSend},</if>
                    <if test="notifyIsSend != null">notify_is_send = #{notifyIsSend},</if>
                    <if test="expireTypeAlready != null">expire_type_already = #{expireTypeAlready},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                </trim>
                where license_id = #{licenseId} and expire_type_already = '1' and delete_flag = '0'
            </when>
            <when test="auditTypeAbout != null and auditTypeAbout != '' and auditTypeAbout == '1'.toString">
                update zz_license_notify_send
                <trim prefix="SET" suffixOverrides=",">
                    <if test="qyvxIsSend != null">qyvx_is_send = #{qyvxIsSend},</if>
                    <if test="notifyIsSend != null">notify_is_send = #{notifyIsSend},</if>
                    <if test="auditTypeAbout != null">audit_type_about = #{auditTypeAbout},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                </trim>
                where license_id = #{licenseId} and audit_type_about = '1' and delete_flag = '0'
            </when>
            <otherwise>
                update zz_license_notify_send
                <trim prefix="SET" suffixOverrides=",">
                    <if test="qyvxIsSend != null">qyvx_is_send = #{qyvxIsSend},</if>
                    <if test="notifyIsSend != null">notify_is_send = #{notifyIsSend},</if>
                    <if test="expireTypeAbout != null">expire_type_about = #{expireTypeAbout},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                </trim>
                where license_id = #{licenseId} and expire_type_about = '1' and delete_flag = '0'
            </otherwise>
        </choose>
    </update>

    <delete id="deleteZzLicenseNotifySendByLicenseId" parameterType="String">
        delete from zz_license_notify_send where license_id = #{licenseId}
    </delete>

    <delete id="deleteZzLicenseNotifySendByLicenseIds" parameterType="String">
        delete from zz_license_notify_send where license_id in
        <foreach item="licenseId" collection="array" open="(" separator="," close=")">
            #{licenseId}
        </foreach>
    </delete>

    <select id="selectLicenseNotifyListToSend" resultType="org.ruoyi.core.license.domain.ZzLicenseNotifySend" resultMap="ZzLicenseNotifySendResult">
        <include refid="selectZzLicenseNotifySendVo"/>
        <choose>
            <when test="auditTypeAlready != null and auditTypeAlready != '' and auditTypeAlready == '1'.toString">
                <where>
                    and license_id = #{licenseId} and audit_type_already = '1' and delete_flag = '0'
                </where>
            </when>
            <when test="expireTypeAlready != null and expireTypeAlready != '' and expireTypeAlready == '1'.toString">
                <where>
                    license_id = #{licenseId} and expire_type_already = '1' and delete_flag = '0'
                </where>
            </when>
            <when test="auditTypeAbout != null and auditTypeAbout != '' and auditTypeAbout == '1'.toString">
                where license_id = #{licenseId} and audit_type_about = '1' and delete_flag = '0'
            </when>
            <otherwise>
                where license_id = #{licenseId} and expire_type_about = '1' and delete_flag = '0'
            </otherwise>
        </choose>
        </select>

</mapper>