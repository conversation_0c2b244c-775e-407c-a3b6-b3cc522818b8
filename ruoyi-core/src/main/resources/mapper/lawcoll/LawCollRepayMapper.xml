<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.modules.lawcoll.mapper.LawCollRepayMapper">
    
    <resultMap type="LawCollRepay" id="LawCollRepayResult">
        <result property="pk"    column="pk"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="repayNo"    column="repay_no"    />
        <result property="loanNo"    column="loan_no"    />
        <result property="fundCode"    column="fund_code"    />
        <result property="repaySrc"    column="repay_src"    />
        <result property="isCp"    column="is_cp"    />
        <result property="totalAmt"    column="total_amt"    />
        <result property="prinAmt"    column="prin_amt"    />
        <result property="intAmt"    column="int_amt"    />
        <result property="ointAmt"    column="oint_amt"    />
        <result property="fee1Amt"    column="fee1_amt"    />
        <result property="fee2Amt"    column="fee2_amt"    />
        <result property="fee3Amt"    column="fee3_amt"    />
        <result property="fee4Amt"    column="fee4_amt"    />
        <result property="fee5Amt"    column="fee5_amt"    />
        <result property="repayTime"    column="repay_time"    />
        <result property="status"    column="status"    />
        <result property="ifEnough"    column="if_enough"    />
        <result property="reconDate"    column="recon_date"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLawCollRepayVo">
        select pk,batch_no, repay_no, loan_no, fund_code, repay_src, is_cp, total_amt, prin_amt, int_amt, oint_amt, fee1_amt, fee2_amt, fee3_amt, fee4_amt, fee5_amt, repay_time, status, if_enough, recon_date, create_time, update_time from vw_law_coll_repay
    </sql>

    <select id="selectLcRepayByLoanNo" parameterType="java.lang.String" resultMap="LawCollRepayResult">
        select batch_no, loan_no, fund_code, repay_src, is_cp, repay_time, status, if_enough, recon_date,
               sum(total_amt) total_amt, sum(prin_amt) prin_amt, sum(int_amt) int_amt, sum(oint_amt) oint_amt,
               sum(fee1_amt) fee1_amt, sum(fee2_amt) fee2_amt, sum(fee3_amt) fee3_amt, sum(fee4_amt) fee4_amt, sum(fee5_amt) fee5_amt
        from vw_law_coll_repay
        where loan_no = #{loanNo}
    </select>

    <select id="selectLcRepayByLoanNoAndRepayTime" parameterType="java.lang.String" resultMap="LawCollRepayResult">
        select batch_no, loan_no, fund_code, repay_src, is_cp, repay_time, status, if_enough, recon_date,
               sum(total_amt) total_amt, sum(prin_amt) prin_amt, sum(int_amt) int_amt, sum(oint_amt) oint_amt,
               sum(fee1_amt) fee1_amt, sum(fee2_amt) fee2_amt, sum(fee3_amt) fee3_amt, sum(fee4_amt) fee4_amt, sum(fee5_amt) fee5_amt
        from vw_law_coll_repay
        where loan_no = #{loanNo} and repay_time = #{repayTime}
    </select>
</mapper>