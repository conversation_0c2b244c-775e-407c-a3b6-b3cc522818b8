<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.mapper.CustomLayoutMapper">
    
    <resultMap type="CustomLayout" id="CustomLayoutResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="balance"    column="balance"    />
        <result property="repayCount"    column="repay_count"    />
        <result property="addAmount"    column="add_amount"    />
        <result property="partnerPie"    column="partner_pie"    />
        <result property="partnerBar"    column="partner_bar"    />
        <result property="fundPie"    column="fund_pie"    />
        <result property="fundBar"    column="fund_bar"    />
        <result property="custPie"    column="cust_pie"    />
        <result property="custBar"    column="cust_bar"    />
        <result property="vintage"    column="vintage"    />
        <result property="profit"    column="profit"    />
        <result property="badDebt"    column="bad_Debt"    />
        <result property="balancePartnerStack"    column="balance_partner_stack"    />
        <result property="balanceFundStack"    column="balance_fund_stack"    />
    </resultMap>

    <sql id="selectCustomLayoutVo">
        select id, user_id, balance, repay_count, add_amount, partner_pie, partner_bar, fund_pie, fund_bar, cust_pie, cust_bar, vintage, profit, bad_Debt, balance_partner_stack, balance_fund_stack from custom_layout
    </sql>

    <select id="selectCustomLayoutList" parameterType="CustomLayout" resultMap="CustomLayoutResult">
        <include refid="selectCustomLayoutVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="balance != null  and balance != ''"> and balance = #{balance}</if>
            <if test="repayCount != null  and repayCount != ''"> and repay_count = #{repayCount}</if>
            <if test="addAmount != null  and addAmount != ''"> and add_amount = #{addAmount}</if>
            <if test="partnerPie != null  and partnerPie != ''"> and partner_pie = #{partnerPie}</if>
            <if test="partnerBar != null  and partnerBar != ''"> and partner_bar = #{partnerBar}</if>
            <if test="fundPie != null  and fundPie != ''"> and fund_pie = #{fundPie}</if>
            <if test="fundBar != null  and fundBar != ''"> and fund_bar = #{fundBar}</if>
            <if test="custPie != null  and custPie != ''"> and cust_pie = #{custPie}</if>
            <if test="custBar != null  and custBar != ''"> and cust_bar = #{custBar}</if>
            <if test="vintage != null  and vintage != ''"> and vintage = #{vintage}</if>
            <if test="profit != null  and profit != ''"> and profit = #{profit}</if>
            <if test="badDebt != null  and badDebt != ''"> and bad_Debt = #{badDebt}</if>
            <if test="balancePartnerStack != null  and balancePartnerStack != ''"> and balance_partner_stack = #{balancePartnerStack}</if>
            <if test="balanceFundStack != null  and balanceFundStack != ''"> and balance_fund_stack = #{balanceFundStack}</if>
        </where>
    </select>
    
    <select id="selectCustomLayoutById" parameterType="Long" resultMap="CustomLayoutResult">
        <include refid="selectCustomLayoutVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomLayout" parameterType="CustomLayout">
        insert into custom_layout
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="balance != null">balance,</if>
            <if test="repayCount != null">repay_count,</if>
            <if test="addAmount != null">add_amount,</if>
            <if test="partnerPie != null">partner_pie,</if>
            <if test="partnerBar != null">partner_bar,</if>
            <if test="fundPie != null">fund_pie,</if>
            <if test="fundBar != null">fund_bar,</if>
            <if test="custPie != null">cust_pie,</if>
            <if test="custBar != null">cust_bar,</if>
            <if test="vintage != null">vintage,</if>
            <if test="profit != null">profit,</if>
            <if test="badDebt != null">bad_Debt,</if>
            <if test="balancePartnerStack != null">balance_partner_stack,</if>
            <if test="balanceFundStack != null">balance_fund_stack,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="balance != null">#{balance},</if>
            <if test="repayCount != null">#{repayCount},</if>
            <if test="addAmount != null">#{addAmount},</if>
            <if test="partnerPie != null">#{partnerPie},</if>
            <if test="partnerBar != null">#{partnerBar},</if>
            <if test="fundPie != null">#{fundPie},</if>
            <if test="fundBar != null">#{fundBar},</if>
            <if test="custPie != null">#{custPie},</if>
            <if test="custBar != null">#{custBar},</if>
            <if test="vintage != null">#{vintage},</if>
            <if test="profit != null">#{profit},</if>
            <if test="badDebt != null">#{badDebt},</if>
            <if test="balancePartnerStack != null">#{balancePartnerStack},</if>
            <if test="balanceFundStack != null">#{balanceFundStack},</if>
         </trim>
    </insert>

    <update id="updateCustomLayout" parameterType="CustomLayout">
        update custom_layout
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="repayCount != null">repay_count = #{repayCount},</if>
            <if test="addAmount != null">add_amount = #{addAmount},</if>
            <if test="partnerPie != null">partner_pie = #{partnerPie},</if>
            <if test="partnerBar != null">partner_bar = #{partnerBar},</if>
            <if test="fundPie != null">fund_pie = #{fundPie},</if>
            <if test="fundBar != null">fund_bar = #{fundBar},</if>
            <if test="custPie != null">cust_pie = #{custPie},</if>
            <if test="custBar != null">cust_bar = #{custBar},</if>
            <if test="vintage != null">vintage = #{vintage},</if>
            <if test="profit != null">profit = #{profit},</if>
            <if test="badDebt != null">bad_Debt = #{badDebt},</if>
            <if test="balancePartnerStack != null">balance_partner_stack = #{balancePartnerStack},</if>
            <if test="balanceFundStack != null">balance_fund_stack = #{balanceFundStack},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomLayoutById" parameterType="Long">
        delete from custom_layout where id = #{id}
    </delete>

    <delete id="deleteCustomLayoutByIds" parameterType="String">
        delete from custom_layout where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getByLoginUserId" resultType="map">
        select id, user_id, balance as balance, repay_count as repayCount, add_amount as addAmount, partner_pie as partnerPie, partner_bar as partnerBar, fund_pie as fundPie, fund_bar as fundBar, cust_pie as custPie, cust_bar as custBar, vintage, profit, bad_Debt as badDebt, balance_partner_stack as balancePartnerStack, balance_fund_stack as balanceFundStack from custom_layout
        where user_id = #{userId}
    </select>


    <update id="updateDataByUserId" parameterType="CustomLayout">
        update custom_layout
        <trim prefix="SET" suffixOverrides=",">
            <if test="balance != null">balance = #{balance},</if>
            <if test="repayCount != null">repay_count = #{repayCount},</if>
            <if test="addAmount != null">add_amount = #{addAmount},</if>
            <if test="partnerPie != null">partner_pie = #{partnerPie},</if>
            <if test="partnerBar != null">partner_bar = #{partnerBar},</if>
            <if test="fundPie != null">fund_pie = #{fundPie},</if>
            <if test="fundBar != null">fund_bar = #{fundBar},</if>
            <if test="custPie != null">cust_pie = #{custPie},</if>
            <if test="custBar != null">cust_bar = #{custBar},</if>
            <if test="vintage != null">vintage = #{vintage},</if>
            <if test="profit != null">profit = #{profit},</if>
            <if test="badDebt != null">bad_Debt = #{badDebt},</if>
            <if test="balancePartnerStack != null">balance_partner_stack = #{balancePartnerStack},</if>
            <if test="balanceFundStack != null">balance_fund_stack = #{balanceFundStack},</if>
        </trim>
        where user_id = #{userId}
    </update>
</mapper>