<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.kaoqin.mapper.MonthLogMainMapper">

    <resultMap type="MonthLogMain" id="MonthLogMainResult">
        <result property="id"    column="id"    />
        <result property="logDate"    column="log_date"    />
        <result property="auditingStatus"    column="auditing_status"    />
        <result property="checkScore"    column="check_score"    />
        <result property="reviewScore"    column="review_score"    />
        <result property="reportingStatus"    column="reporting_status"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="auditingTime"    column="auditing_time"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="monthLogVoResultMap" type="MonthLogVo">
        <id property="id" column="id"/>
        <result property="logDate"    column="log_date"    />
        <result property="nickName"    column="nick_name"    />
        <result property="userName"    column="user_name"    />
        <result property="userId"    column="user_id" />
        <result property="deptId"    column="dept_id" />
        <result property="postName"    column="post_name" />
        <result property="deptName"    column="dept_name" />
        <result property="auditingStatus"    column="auditing_status"    />
        <result property="checkScore"    column="check_score"    />
        <result property="reviewScore"    column="review_score"    />
        <result property="reportingStatus"    column="reporting_status"    />
        <result property="reviewStatus"    column="review_status"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="auditingTime"    column="auditing_time"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="logDate" column="logDate"/>
        <result property="localScore" column="local_score"/>
    </resultMap>

    <sql id="selectMonthLogMainVo">
        select id, log_date, auditing_status, check_score, review_score, reporting_status, submit_time, auditing_time, remark, create_by, create_time, update_by, update_time from kq_month_log_main
    </sql>

    <select id="selectMonthLogMainList" parameterType="MonthLogMain" resultMap="MonthLogMainResult">
        <include refid="selectMonthLogMainVo"/>
        <where>
            <if test="logDate != null "> and log_date = #{logDate}</if>
            <if test="auditingStatus != null  and auditingStatus != ''"> and auditing_status = #{auditingStatus}</if>
            <if test="checkScore != null "> and check_score = #{checkScore}</if>
            <if test="reviewScore != null "> and review_score = #{reviewScore}</if>
            <if test="reportingStatus != null  and reportingStatus != ''"> and reporting_status = #{reportingStatus}</if>
            <if test="submitTime != null "> and submit_time = #{submitTime}</if>
            <if test="auditingTime != null "> and auditing_time = #{auditingTime}</if>
        </where>
    </select>

    <select id="selectMonthLogMainById" parameterType="Long" resultMap="MonthLogMainResult">
        <include refid="selectMonthLogMainVo"/>
        where id = #{id}
    </select>

    <insert id="insertMonthLogMain" parameterType="MonthLogMain" useGeneratedKeys="true" keyProperty="id">
        <selectKey keyProperty="id" resultType="Long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into kq_month_log_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logDate != null">log_date,</if>
            <if test="auditingStatus != null">auditing_status,</if>
            <if test="checkScore != null">check_score,</if>
            <if test="reviewScore != null">review_score,</if>
            <if test="reportingStatus != null">reporting_status,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="auditingTime != null">auditing_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logDate != null">#{logDate},</if>
            <if test="auditingStatus != null">#{auditingStatus},</if>
            <if test="checkScore != null">#{checkScore},</if>
            <if test="reviewScore != null">#{reviewScore},</if>
            <if test="reportingStatus != null">#{reportingStatus},</if>
            <if test="reviewStatus != null"> #{reviewStatus},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="auditingTime != null">#{auditingTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMonthLogMain" parameterType="MonthLogMain">
        update kq_month_log_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="logDate != null">log_date = #{logDate},</if>
            <if test="auditingStatus != null">auditing_status = #{auditingStatus},</if>
            <if test="checkScore != null">check_score = #{checkScore},</if>
            <if test="reviewScore != null">review_score = #{reviewScore},</if>
            <if test="reportingStatus != null">reporting_status = #{reportingStatus},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="auditingTime != null">auditing_time = #{auditingTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="localScore != null">local_score = #{localScore},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonthLogMainById" parameterType="Long">
        delete from kq_month_log_main where id = #{id}
    </delete>

    <delete id="deleteMonthLogMainByIds" parameterType="String">
        delete from kq_month_log_main where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getMonthLogMain" parameterType="MonthLogVo" resultType="MonthLogVo">
        <include refid="selectMonthLogMainVo"/>
        where
           log_date  = #{logDate}
           and create_by = #{createBy}
        LIMIT 1
    </select>

    <select id="getMonthLogMainList" parameterType="MonthLogVo" resultMap="monthLogVoResultMap">
        select main.id, log_date,
               CASE WHEN main.id IS NULL THEN 1 ELSE auditing_status END AS auditing_status,
               check_score, review_score,review_status,
               CASE WHEN main.id IS NULL THEN 3 ELSE reporting_status END AS reporting_status,
               submit_time,
               auditing_time, main.remark, main.create_by, main.create_time, main.update_by, main.update_time,main.local_score,
               user.nick_name,user.user_id,user.dept_id,user.user_name,
               dept.dept_name,post.post_name
        from sys_user user
        left join sys_dept dept on user.dept_id = dept.dept_id
        left join sys_user_post upost on user.user_id = upost.user_id and upost.home_post = 0
        left join sys_post post on post.post_id = upost.post_id
        left join kq_month_log_main main on user.user_name = main.create_by <if test="logDate != null "> and log_date = #{logDate}</if>
        <where>
            <if test="nickName != null  and nickName != ''"> and nick_name = #{nickName}</if>
            <if test="auditingStatus != null  and auditingStatus != ''"> and auditing_status = #{auditingStatus}</if>
            <if test="createByList != null and createByList.size() > 0">
                and user.user_name in
                <foreach collection="createByList" item="createBy" separator="," open="(" close=")">
                    #{createBy}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getMonthLogMainByUserId" parameterType="MonthLogVo" resultMap="monthLogVoResultMap">
        select main.id, log_date,
        CASE WHEN main.id IS NULL THEN 1 ELSE auditing_status END AS auditing_status,
        check_score, review_score,review_status,
        CASE WHEN main.id IS NULL THEN 3 ELSE reporting_status END AS reporting_status,
        submit_time,
        auditing_time, main.remark, main.create_by, main.create_time, main.update_by, main.update_time,
        user.nick_name,user.user_id,user.dept_id,user.user_name,
        dept.dept_name,post.post_name
        from sys_user user
        left join sys_dept dept on user.dept_id = dept.dept_id
        left join sys_user_post upost on user.user_id = upost.user_id and upost.home_post = 0
        left join sys_post post on post.post_id = upost.post_id
        left join kq_month_log_main main on user.user_name = main.create_by <if test="logDate != null "> and log_date = #{logDate}</if>
        <where>
            <if test="userId != null  and userId != ''"> and user.user_id = #{userId}</if>
        </where>
        LIMIT 1
    </select>
</mapper>
