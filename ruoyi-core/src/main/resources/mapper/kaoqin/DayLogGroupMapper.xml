<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.kaoqin.mapper.DayLogGroupMapper">

    <resultMap type="DayLogGroup" id="DayLogGroupResult">
        <result property="id"    column="id"    />
        <result property="groupName"    column="group_name"    />
        <result property="userIdList"    column="user_id_list"  typeHandler="org.ruoyi.core.meeting.domain.StringToLongListTypeHandler"  />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDayLogGroupVo">
        select id, group_name, user_id_list, create_by, create_time, update_by, update_time from kq_day_log_group
    </sql>

    <select id="selectDayLogGroupList" parameterType="DayLogGroup" resultMap="DayLogGroupResult">
        <include refid="selectDayLogGroupVo"/>
        <where>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
        </where>
    </select>

    <select id="getDayLogGroupList" parameterType="DayLogGroup" resultMap="DayLogGroupResult">
        <include refid="selectDayLogGroupVo"/>
        <where>
            <if test="groupName != null  and groupName != ''"> and group_name = #{groupName}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
        </where>
    </select>

    <select id="selectDayLogGroupById" parameterType="Long" resultMap="DayLogGroupResult">
        <include refid="selectDayLogGroupVo"/>
        where id = #{id}
    </select>

    <insert id="insertDayLogGroup" parameterType="DayLogGroup" useGeneratedKeys="true" keyProperty="id">
        insert into kq_day_log_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupName != null">group_name,</if>
            <if test="userIdList != null">user_id_list,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupName != null">#{groupName},</if>
            <if test="userIdList != null">#{userIdList , typeHandler=org.ruoyi.core.meeting.domain.StringToLongListTypeHandler},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDayLogGroup" parameterType="DayLogGroup">
        update kq_day_log_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="userIdList != null">user_id_list = #{userIdList , typeHandler=org.ruoyi.core.meeting.domain.StringToLongListTypeHandler},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDayLogGroupById" parameterType="Long">
        delete from kq_day_log_group where id = #{id}
    </delete>

    <delete id="deleteDayLogGroupByIds" parameterType="String">
        delete from kq_day_log_group where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
