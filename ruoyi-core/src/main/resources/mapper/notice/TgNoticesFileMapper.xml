<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.notice.mapper.TgNoticesFileMapper">

    <resultMap type="TgNoticesFile" id="TgNoticesFileResult">
        <result property="id"    column="id"    />
        <result property="relationId"    column="relation_id"    />
        <result property="relationType"    column="relation_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="processId"    column="process_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTgNoticesFileVo">
        select id, relation_id, relation_type, file_name, file_path, process_id, status, create_by, create_time, update_by, update_time from tg_notices_file
    </sql>

    <select id="selectTgNoticesFileList" parameterType="TgNoticesFile" resultMap="TgNoticesFileResult">
        <include refid="selectTgNoticesFileVo"/>
        <where>
            <if test="relationId != null "> and relation_id = #{relationId}</if>
            <if test="relationType != null  and relationType != ''"> and relation_type = #{relationType}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="processId != null  and processId != ''"> and process_id = #{processId}</if>
            and status = '0'
        </where>
    </select>

    <select id="selectTgNoticesFileById" parameterType="Long" resultMap="TgNoticesFileResult">
        <include refid="selectTgNoticesFileVo"/>
        where id = #{id} and status = '0'
    </select>

    <insert id="insertTgNoticesFile" parameterType="TgNoticesFile" useGeneratedKeys="true" keyProperty="id">
        insert into tg_notices_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="relationId != null">relation_id,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="processId != null">process_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="relationId != null">#{relationId},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="processId != null">#{processId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTgNoticesFile" parameterType="TgNoticesFile">
        update tg_notices_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTgNoticesFileById" parameterType="Long">
        update tg_notices_file set status = '1' where id = #{id}
    </delete>

    <delete id="deleteTgNoticesFileByIds" parameterType="String">
        delete from tg_notices_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateTgNoticesFileList">
        <if test="noticesFileList != null and noticesFileList.size() > 0" >
            <foreach collection="noticesFileList" item="item" separator=";">
                update tg_notices_file set relation_id = #{item.relationId}, status = '0' where id = #{item.id}
            </foreach>
        </if>
    </update>

    <delete id="deleteTgNoticesFileByRelationIdAndType">
        update tg_notices_file set status = '1' where relation_id = #{relationId} and relation_type = #{relationType} and status = '0'
    </delete>

    <select id="selectTgNoticesFileByNoticeId" resultType="org.ruoyi.core.notice.domain.TgNoticesFile">
        select id, relation_id, relation_type, file_name, file_path, process_id, status from tg_notices_file where relation_id = #{relationId} and status = '0' and relation_type = #{relationType}
    </select>

    <insert id="batchInsertFileList">
        insert into tg_notices_file (relation_id, relation_type, file_name, file_path, create_by, create_time) values
        <foreach collection="noticesFileList" item="item" separator=",">
            (#{item.relationId},#{item.relationType},#{item.fileName},#{item.filePath},#{item.createBy},#{item.createTime})
        </foreach>
    </insert>

    <select id="selectRqCodeAuditReportFile" resultType="org.ruoyi.core.notice.domain.TgNoticesFile">
        select id, relation_id, relation_type, file_name, file_path from tg_notices_file
        <where>
            <if test="relationId != null "> and relation_id = #{relationId}</if>
            <if test="relationType != null  and relationType != ''"> and relation_type = #{relationType}</if>
            and status = '0'
        </where>
    </select>

</mapper>