<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.notice.mapper.TgVersionRelationMapper">

    <resultMap type="TgVersionRelation" id="TgVersionRelationResult">
        <result property="noticeId"    column="notice_id"    />
        <result property="version"    column="version"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectTgVersionRelationVo">
        select notice_id, version, create_time, create_by, update_time, update_by from tg_version_relation
    </sql>

    <select id="selectTgVersionRelationList" parameterType="TgVersionRelation" resultMap="TgVersionRelationResult">
        <include refid="selectTgVersionRelationVo"/>
        <where>
            <if test="noticeId != null "> and notice_id = #{noticeId}</if>
            <if test="version != null "> and version = #{version}</if>
        </where>
    </select>

    <select id="selectTgVersionRelationByNoticeId" parameterType="Long" resultMap="TgVersionRelationResult">
        <include refid="selectTgVersionRelationVo"/>
        where notice_id = #{noticeId}
    </select>

    <insert id="insertTgVersionRelation" parameterType="TgVersionRelation">
        insert into tg_version_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">notice_id,</if>
            <if test="version != null">version,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">#{noticeId},</if>
            <if test="version != null">#{version},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateTgVersionRelation" parameterType="TgVersionRelation">
        update tg_version_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="version != null">version = #{version},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteTgVersionRelationByNoticeId" parameterType="Long">
        delete from tg_version_relation where notice_id = #{noticeId}
    </delete>

    <delete id="deleteTgVersionRelationByNoticeIds" parameterType="String">
        delete from tg_version_relation where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>
</mapper>