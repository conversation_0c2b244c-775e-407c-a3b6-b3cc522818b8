<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaCaiwuQueryMapper">

    <resultMap type="OaCaiwuQuery" id="OaCaiwuQueryResult">
        <result property="id"    column="id"    />
        <result property="pfdId"    column="pfd_id"    />
        <result property="companyId"    column="company_id"    />
        <result property="templateId"    column="template_id"    />
        <result property="userId"    column="user_id"    />
        <result property="theme"    column="theme"    />
        <result property="checkTime"    column="check_time"    />
        <result property="amount"    column="amount"    />
        <result property="chunaCheck"    column="chuna_check"    />
        <result property="voucharGenerate"    column="vouchar_generate"    />
        <result property="voucharNum"    column="vouchar_num"    />
    </resultMap>

    <sql id="selectOaCaiwuQueryVo">
        select id, pfd_id, company_id, template_id, user_id, theme, check_time, amount, chuna_check, vouchar_generate, vouchar_num from oa_caiwu_query
    </sql>

    <select id="selectOaCaiwuQueryList" parameterType="OaCaiwuQuery" resultMap="OaCaiwuQueryResult">
        <include refid="selectOaCaiwuQueryVo"/>
        <where>
            <if test="pfdId != null  and pfdId != ''"> and pfd_id = #{pfdId}</if>
            <if test="companyId != null "> and company_id = #{companyId}</if>
            <if test="templateId != null "> and template_id = #{templateId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="theme != null  and theme != ''"> and theme = #{theme}</if>
            <if test="checkTime != null "> and check_time = #{checkTime}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="chunaCheck != null  and chunaCheck != ''"> and chuna_check = #{chunaCheck}</if>
            <if test="voucharGenerate != null  and voucharGenerate != ''"> and vouchar_generate = #{voucharGenerate}</if>
            <if test="voucharNum != null  and voucharNum != ''"> and vouchar_num = #{voucharNum}</if>
        </where>
    </select>

    <select id="selectOaCaiwuQueryById" parameterType="Long" resultMap="OaCaiwuQueryResult">
        <include refid="selectOaCaiwuQueryVo"/>
        where id = #{id}
    </select>

    <insert id="insertOaCaiwuQuery" parameterType="OaCaiwuQuery" useGeneratedKeys="true" keyProperty="id">
        insert into oa_caiwu_query
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pfdId != null">pfd_id,</if>
            <if test="companyId != null">company_id,</if>
            <if test="templateId != null">template_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="theme != null">theme,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="amount != null">amount,</if>
            <if test="chunaCheck != null">chuna_check,</if>
            <if test="voucharGenerate != null">vouchar_generate,</if>
            <if test="voucharNum != null">vouchar_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pfdId != null">#{pfdId},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="theme != null">#{theme},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="amount != null">#{amount},</if>
            <if test="chunaCheck != null">#{chunaCheck},</if>
            <if test="voucharGenerate != null">#{voucharGenerate},</if>
            <if test="voucharNum != null">#{voucharNum},</if>
         </trim>
    </insert>

    <update id="updateOaCaiwuQuery" parameterType="OaCaiwuQuery">
        update oa_caiwu_query
        <trim prefix="SET" suffixOverrides=",">
            <if test="pfdId != null">pfd_id = #{pfdId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="theme != null">theme = #{theme},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="chunaCheck != null">chuna_check = #{chunaCheck},</if>
            <if test="voucharGenerate != null">vouchar_generate = #{voucharGenerate},</if>
            <if test="voucharNum != null">vouchar_num = #{voucharNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOaCaiwuQueryById" parameterType="Long">
        delete from oa_caiwu_query where id = #{id}
    </delete>

    <delete id="deleteOaCaiwuQueryByIds" parameterType="String">
        delete from oa_caiwu_query where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="selectDataByFpdId" resultMap="OaCaiwuQueryResult">
        <include refid="selectOaCaiwuQueryVo"/>
        where pfd_id = #{fpdId}
    </select>

    <select id="selectData" resultType="org.ruoyi.core.oasystem.domain.OaCaiwuQuery">
        SELECT distinct pfd.business_id pfdId,pfd.theme,com.company_name companyName,sur.nick_name userName,pfd.check_time checkTime,pfd.amount,opt.template_name templateName,pfd.chuna_check chunaCheck,pfd.vouchar_generate voucharGenerate,pfd.vouchar_num voucharNum
        FROM proc_form_data pfd
        LEFT JOIN sys_company com ON pfd.company_id = com.id and com.is_inside = '1'
        LEFT JOIN sys_user sur ON pfd.user_id = sur.user_id
        LEFT JOIN oa_process_template_his opt ON pfd.template_id = opt.id
        <if test='vo.queryScope == 2'>
        LEFT JOIN proc_workflow_formdata pwf ON pwf.business_key = pfd.business_id
        </if>
        <if test='vo.queryScope=="3"'>
        INNER JOIN act_ru_copy_task cpt ON cpt.business_key = pfd.business_id
        </if>
        where opt.template_type = '1'
            <if test='vo.queryScope =="1"'>and pfd.create_by = #{userName}</if>
            <if test="vo.theme != null  and vo.theme != ''"> and pfd.theme like concat('%', #{vo.theme}, '%')</if>
            <if test="vo.companyId != null "> and pfd.company_id = #{vo.companyId}</if>
            <if test="vo.userName != null and vo.userName != ''"> and sur.nick_name like concat('%', #{vo.userName}, '%') </if>
            <if test="vo.beginTime != null"> and pfd.check_time >= #{vo.beginTime}</if>
            <if test="vo.endTime != null"> and #{vo.endTime} >= pfd.check_time</if>
            <if test="vo.minAmount != null"> and pfd.amount >= #{vo.minAmount} </if>
            <if test="vo.maxAmount != null"> and #{vo.maxAmount} >= pfd.amount </if>
            <if test="vo.templateName != null and vo.templateName != ''"> and opt.template_name like concat('%', #{vo.templateName}, '%') </if>
            <if test="vo.chunaCheck != null  and vo.chunaCheck != ''"> and chuna_check = #{vo.chunaCheck}</if>
            <if test="vo.voucharGenerate != null  and vo.voucharGenerate != ''"> and vouchar_generate = #{vo.voucharGenerate}</if>
            <if test='vo.queryScope == 2'> and pwf.create_by =#{userName,jdbcType=VARCHAR} and pwf.pass in('1','9') and pwf.COMMENT != '提交申请'</if>
            <if test='vo.queryScope=="3"'> and cpt.user_name = #{userName,jdbcType=VARCHAR}</if>
        order by pfd.check_time DESC
    </select>
</mapper>
