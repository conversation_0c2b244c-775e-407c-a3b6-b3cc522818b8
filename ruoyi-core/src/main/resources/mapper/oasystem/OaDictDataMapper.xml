<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaDictDataMapper">
    
    <resultMap type="OaDictData" id="OaDictDataResult">
        <result property="id"    column="id"    />
        <result property="dictName"    column="dict_name"    />
        <result property="dictValue"    column="dict_value"    />
        <result property="dictTypeName"    column="dict_type_name"    />
        <result property="dictTypeIdent"    column="dict_type_ident"    />
        <result property="dictType"    column="dict_type"    />
        <result property="isEnable"    column="is_enable"    />
        <result property="endUpdateTime"    column="end_update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>


    <sql id="selectOaDictDataVo">
        select id, dict_name, dict_value, dict_type_name, dict_type_ident, dict_type, is_enable, end_update_time, create_by, create_time, update_by, update_time from oa_dict_data
    </sql>

    <select id="selectOaDictDataList" parameterType="OaDictData" resultMap="OaDictDataResult">
        <include refid="selectOaDictDataVo"/>
        <where>
            <if test="dictName != null  and dictName != ''"> and dict_name like concat('%', #{dictName}, '%')</if>
            <if test="dictTypeName != null  and dictTypeName != ''"> and dict_type_name like concat('%', #{dictTypeName}, '%')</if>
            <if test="dictTypeIdent != null and dictTypeIdent != '' "> and dict_type_ident = #{dictTypeIdent}</if>
            <if test="dictType != null  and dictType != ''"> and dict_type = #{dictType}</if>
            <if test="isEnable != null  and isEnable != ''"> and is_enable = #{isEnable}</if>
            <if test="endUpdateTime != null "> and end_update_time = #{endUpdateTime}</if>
        </where>
        group by dict_type_ident
        ORDER BY create_time DESC
    </select>
    
    <select id="selectOaDictDataById" parameterType="Long" resultMap="OaDictDataResult">
        <include refid="selectOaDictDataVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOaDictData" parameterType="OaDictData" useGeneratedKeys="true" keyProperty="id">
        insert into oa_dict_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictName != null">dict_name,</if>
            <if test="dictValue != null">dict_value,</if>
            <if test="dictTypeName != null">dict_type_name,</if>
            <if test="dictTypeIdent != null">dict_type_ident,</if>
            <if test="dictType != null">dict_type,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="endUpdateTime != null">end_update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictName != null">#{dictName},</if>
            <if test="dictValue != null">#{dictValue},</if>
            <if test="dictTypeName != null">#{dictTypeName},</if>
            <if test="dictTypeIdent != null">#{dictTypeIdent},</if>
            <if test="dictType != null">#{dictType},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="endUpdateTime != null">#{endUpdateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateOaDictData" parameterType="OaDictData">
        update oa_dict_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="dictName != null">dict_name = #{dictName},</if>
            <if test="dictValue != null">dict_value = #{dictValue},</if>
            <if test="dictTypeName != null">dict_type_name = #{dictTypeName},</if>
            <if test="dictTypeIdent != null">dict_type_ident = #{dictTypeIdent},</if>
            <if test="dictType != null">dict_type = #{dictType},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="endUpdateTime != null">end_update_time = #{endUpdateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOaDictDataById" parameterType="Long">
        delete from oa_dict_data where id = #{id}
    </delete>

    <delete id="deleteOaDictDataByIds" parameterType="String">
        delete from oa_dict_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into oa_dict_data
        (dict_name, dict_value,dict_type_name, dict_type_ident, dict_type, is_enable,end_update_time, create_by, create_time,
        update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.dictName,jdbcType=VARCHAR}, #{item.dictValue,jdbcType=VARCHAR},#{item.dictTypeName,jdbcType=VARCHAR},
            #{item.dictTypeIdent,jdbcType=VARCHAR},
            #{item.dictType,jdbcType=VARCHAR}, #{item.isEnable,jdbcType=VARCHAR} ,#{item.endUpdateTime,jdbcType=TIMESTAMP},#{item.createBy,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP}
         )
        </foreach>
    </insert>

    <update id="updateOaDictDataEnable" parameterType="OaDictData">

        update oa_dict_data set is_enable = #{isEnable},update_time = #{updateTime},end_update_time = #{endUpdateTime} where dict_type_ident = #{dictTypeIdent}
    </update>

    <delete id="deleteByDictTypeIdent">
        delete from oa_dict_data where dict_type_ident = #{dictTypeIdent}
    </delete>


    <select id="selectByDictIdel" resultMap="OaDictDataResult">
        <include refid="selectOaDictDataVo"/>
      where dict_type_ident = #{dictTypeIdent}

    </select>
</mapper>