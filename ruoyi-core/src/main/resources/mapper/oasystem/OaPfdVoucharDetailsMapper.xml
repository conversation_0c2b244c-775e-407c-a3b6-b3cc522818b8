<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaPfdVoucharDetailsMapper">
    
    <resultMap type="OaPfdVoucharDetails" id="OaPfdVoucharDetailsResult">
        <result property="id"    column="id"    />
        <result property="pfdId"    column="pfd_id"    />
        <result property="accountSetId"    column="account_set_id"    />
        <result property="voucharId" column="vouchar_id"/>
        <result property="generateStatus" column="generate_status"/>
        <result property="voucharStatus" column="vouchar_status"/>
        <result property="voucharCode"    column="vouchar_code"    />
        <result property="voucharDate"    column="vouchar_date"    />
        <result property="borrowSubject"    column="borrow_subject"    />
        <result property="loanSubject"    column="loan_subject"    />
        <result property="borrowAmount"    column="borrow_amount"    />
        <result property="loanAmount"    column="loan_amount"    />
        <result property="voucharAbstract"    column="vouchar_abstract"    />
        <result property="makeVoucharUser"    column="make_vouchar_user"    />
        <result property="amountSum"    column="amount_sum"    />
    </resultMap>

    <sql id="selectOaPfdVoucharDetailsVo">
        select id, pfd_id, account_set_id,vouchar_id, vouchar_code, vouchar_date, borrow_subject, loan_subject, borrow_amount, loan_amount, vouchar_abstract, make_vouchar_user, amount_sum from oa_pfd_vouchar_details
    </sql>

    <select id="selectOaPfdVoucharDetailsList" parameterType="OaPfdVoucharDetails" resultMap="OaPfdVoucharDetailsResult">
        <include refid="selectOaPfdVoucharDetailsVo"/>
        <where>  
            <if test="pfdId != null  and pfdId != ''"> and pfd_id = #{pfdId}</if>
            <if test="accountSetId != null "> and account_set_id = #{accountSetId}</if>
            <if test="voucharCode != null  and voucharCode != ''"> and vouchar_code = #{voucharCode}</if>
            <if test="voucharDate != null "> and vouchar_date = #{voucharDate}</if>
            <if test="borrowSubject != null  and borrowSubject != ''"> and borrow_subject = #{borrowSubject}</if>
            <if test="loanSubject != null  and loanSubject != ''"> and loan_subject = #{loanSubject}</if>
            <if test="borrowAmount != null "> and borrow_amount = #{borrowAmount}</if>
            <if test="loanAmount != null "> and loan_amount = #{loanAmount}</if>
            <if test="voucharAbstract != null  and voucharAbstract != ''"> and vouchar_abstract = #{voucharAbstract}</if>
            <if test="makeVoucharUser != null "> and make_vouchar_user = #{makeVoucharUser}</if>
            <if test="amountSum != null "> and amount_sum = #{amountSum}</if>
        </where>
    </select>
    
    <select id="selectOaPfdVoucharDetailsById" parameterType="Long" resultMap="OaPfdVoucharDetailsResult">
        <include refid="selectOaPfdVoucharDetailsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOaPfdVoucharDetails" parameterType="OaPfdVoucharDetails" useGeneratedKeys="true" keyProperty="id">
        insert into oa_pfd_vouchar_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pfdId != null">pfd_id,</if>
            <if test="accountSetId != null">account_set_id,</if>
            <if test="voucharId != null">vouchar_id,</if>
            <if test="generateStatus != null and generateStatus != ''">generate_status,</if>
            <if test="voucharStatus != null and voucharStatus != ''">vouchar_status,</if>
            <if test="voucharCode != null">vouchar_code,</if>
            <if test="voucharDate != null">vouchar_date,</if>
            <if test="borrowSubject != null">borrow_subject,</if>
            <if test="loanSubject != null">loan_subject,</if>
            <if test="borrowAmount != null">borrow_amount,</if>
            <if test="loanAmount != null">loan_amount,</if>
            <if test="voucharAbstract != null">vouchar_abstract,</if>
            <if test="makeVoucharUser != null">make_vouchar_user,</if>
            <if test="amountSum != null">amount_sum,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pfdId != null">#{pfdId},</if>
            <if test="accountSetId != null">#{accountSetId},</if>
            <if test="voucharId != null">#{voucharId},</if>
            <if test="generateStatus != null and generateStatus != ''">#{generateStatus},</if>
            <if test="voucharStatus != null and voucharStatus != ''">#{voucharStatus},</if>
            <if test="voucharCode != null">#{voucharCode},</if>
            <if test="voucharDate != null">#{voucharDate},</if>
            <if test="borrowSubject != null">#{borrowSubject},</if>
            <if test="loanSubject != null">#{loanSubject},</if>
            <if test="borrowAmount != null">#{borrowAmount},</if>
            <if test="loanAmount != null">#{loanAmount},</if>
            <if test="voucharAbstract != null">#{voucharAbstract},</if>
            <if test="makeVoucharUser != null">#{makeVoucharUser},</if>
            <if test="amountSum != null">#{amountSum},</if>
         </trim>
    </insert>

    <update id="updateOaPfdVoucharDetails" parameterType="OaPfdVoucharDetails">
        update oa_pfd_vouchar_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="pfdId != null">pfd_id = #{pfdId},</if>
            <if test="accountSetId != null">account_set_id = #{accountSetId},</if>
            <if test="voucharId != null">vouchar_id = #{voucharId},</if>
            <if test="generateStatus != null and generateStatus != ''">generate_status = #{generateStatus}</if>
            <if test="voucharStatus != null and voucharStatus != ''">vouchar_status = #{voucharStatus}</if>
            <if test="voucharCode != null">vouchar_code = #{voucharCode},</if>
            <if test="voucharDate != null">vouchar_date = #{voucharDate},</if>
            <if test="borrowSubject != null">borrow_subject = #{borrowSubject},</if>
            <if test="loanSubject != null">loan_subject = #{loanSubject},</if>
            <if test="borrowAmount != null">borrow_amount = #{borrowAmount},</if>
            <if test="loanAmount != null">loan_amount = #{loanAmount},</if>
            <if test="voucharAbstract != null">vouchar_abstract = #{voucharAbstract},</if>
            <if test="makeVoucharUser != null">make_vouchar_user = #{makeVoucharUser},</if>
            <if test="amountSum != null">amount_sum = #{amountSum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOaPfdVoucharDetailsById" parameterType="Long">
        delete from oa_pfd_vouchar_details where id = #{id}
    </delete>

    <delete id="deleteOaPfdVoucharDetailsByIds" parameterType="String">
        delete from oa_pfd_vouchar_details where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="selectPfdData" parameterType="OaPfdVoucharDetails" resultType="org.ruoyi.core.oasystem.domain.OaPfdVoucharDetails">
        SELECT fas.company_name accountSetName, opvd.vouchar_code voucharCode,opvd.vouchar_date voucharDate,opvd.borrow_subject borrowSubject,opvd.borrow_amount borrowAmount,opvd.loan_subject loanSubject,
        opvd.loan_amount loanAmount,opvd.account_set_id accountSetId,opvd.vouchar_abstract voucharAbstract,opvd.amount_sum amountSum,sur.nick_name userName,opvd.vouchar_id voucharId,opvd.generate_status generateStatus,opvd.vouchar_status voucharStatus
        FROM oa_pfd_vouchar_details opvd
                 LEFT JOIN financial_account_sets fas ON opvd.account_set_id = fas.id
                 LEFT JOIN sys_user sur ON opvd.make_vouchar_user = sur.user_id
        <where>
            <if test="pfdId != null  and pfdId != ''"> and opvd.pfd_id = #{pfdId}</if>
            <if test="accountSetId != null "> and opvd.account_set_id = #{accountSetId}</if>
            <if test="voucharCode != null  and voucharCode != ''"> and opvd.vouchar_code like concat('%', #{voucharCode}, '%')</if>
            <if test="userName != null and userName != '' "> and sur.nick_name like concat('%', #{userName}, '%')</if>
            <if test="borrowSubject != null  and borrowSubject != ''"> and borrow_subject = #{borrowSubject}</if>
            <if test="loanSubject != null  and loanSubject != ''"> and loan_subject = #{loanSubject}</if>

            <if test="borrowAmount != null "> and borrow_amount = #{borrowAmount}</if>
            <if test="loanAmount != null "> and loan_amount = #{loanAmount}</if>
            <if test="voucharAbstract != null  and voucharAbstract != ''"> and vouchar_abstract = #{voucharAbstract}</if>
            <if test="makeVoucharUser != null "> and make_vouchar_user = #{makeVoucharUser}</if>
            <if test="amountSum != null "> and amount_sum = #{amountSum}</if>
        </where>
    </select>
</mapper>