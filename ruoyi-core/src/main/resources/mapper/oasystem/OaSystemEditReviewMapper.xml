<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaSystemEditReviewMapper">


    <select id="selectIsEnableStatusByProjectType" resultType="java.lang.String">
        SELECT is_enable FROM oa_system_edit_review WHERE project_type=#{projectType,jdbcType=VARCHAR} AND status='0'
    </select>

    <update id="upadteIsEnable">
        UPDATE oa_system_edit_review
        <trim prefix="SET" suffixOverrides=",">
            <if test="isEnable != null and isEnable != ''">is_enable=#{isEnable,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">update_time=#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">update_by=#{updateBy,jdbcType=VARCHAR},</if>
        </trim>
        WHERE project_type=#{projectType,jdbcType=VARCHAR}
    </update>
</mapper>