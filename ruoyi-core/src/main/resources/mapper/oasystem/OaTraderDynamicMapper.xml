<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaTraderDynamicMapper">
    
    <resultMap type="OaTraderDynamic" id="OaTraderDynamicResult">
        <result property="id"    column="id"    />
        <result property="oaTraderId"    column="oa_trader_id"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="operationContent"    column="operation_content"    />
        <result property="operationBrId"    column="operation_br_id"    />
        <result property="operationBr"    column="operation_br"    />
    </resultMap>

    <sql id="selectOaTraderDynamicVo">
        select id, oa_trader_id, operation_time, operation_content, operation_br_id, operation_br from oa_trader_dynamic
    </sql>

    <select id="selectOaTraderDynamicList" parameterType="OaTraderDynamic" resultMap="OaTraderDynamicResult">
        <include refid="selectOaTraderDynamicVo"/>
        <where>  
            <if test="oaTraderId != null "> and oa_trader_id = #{oaTraderId}</if>
            <if test="operationTime != null "> and operation_time = #{operationTime}</if>
            <if test="operationContent != null  and operationContent != ''"> and operation_content = #{operationContent}</if>
            <if test="operationBrId != null "> and operation_br_id = #{operationBrId}</if>
            <if test="operationBr != null  and operationBr != ''"> and operation_br = #{operationBr}</if>
        </where>
        ORDER BY operation_time DESC
    </select>
    
    <select id="selectOaTraderDynamicById" parameterType="Long" resultMap="OaTraderDynamicResult">
        <include refid="selectOaTraderDynamicVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOaTraderDynamic" parameterType="OaTraderDynamic">
        insert into oa_trader_dynamic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oaTraderId != null">oa_trader_id,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="operationContent != null">operation_content,</if>
            <if test="operationBrId != null">operation_br_id,</if>
            <if test="operationBr != null">operation_br,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oaTraderId != null">#{oaTraderId},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="operationContent != null">#{operationContent},</if>
            <if test="operationBrId != null">#{operationBrId},</if>
            <if test="operationBr != null">#{operationBr},</if>
         </trim>
    </insert>

    <update id="updateOaTraderDynamic" parameterType="OaTraderDynamic">
        update oa_trader_dynamic
        <trim prefix="SET" suffixOverrides=",">
            <if test="oaTraderId != null">oa_trader_id = #{oaTraderId},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="operationContent != null">operation_content = #{operationContent},</if>
            <if test="operationBrId != null">operation_br_id = #{operationBrId},</if>
            <if test="operationBr != null">operation_br = #{operationBr},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOaTraderDynamicById" parameterType="Long">
        delete from oa_trader_dynamic where id = #{id}
    </delete>

    <delete id="deleteOaTraderDynamicByIds" parameterType="String">
        delete from oa_trader_dynamic where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByOaTraderId" parameterType="Long">
        delete from oa_trader_dynamic where oa_trader_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>