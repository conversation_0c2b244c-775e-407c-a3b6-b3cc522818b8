<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaProjectFlowAssociationMapper">
    
    <resultMap type="OaProjectFlowAssociation" id="OaProjectFlowAssociationResult">
        <result property="id"    column="id"    />
        <result property="projectFlowMainId"    column="project_flow_main_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="status"    column="status"    />
        <result property="createBr"    column="create_br"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBr"    column="update_br"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOaProjectFlowAssociationVo">
        select id, project_flow_main_id, project_id, project_name, status, create_br, create_time, update_br, update_time from oa_project_flow_association
    </sql>

    <select id="selectOaProjectFlowAssociationList" parameterType="OaProjectFlowAssociation" resultMap="OaProjectFlowAssociationResult">
        <include refid="selectOaProjectFlowAssociationVo"/>
        <where>  
            <if test="projectFlowMainId != null "> and project_flow_main_id = #{projectFlowMainId}</if>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
        </where>
    </select>
    
    <select id="selectOaProjectFlowAssociationById" parameterType="Long" resultMap="OaProjectFlowAssociationResult">
        <include refid="selectOaProjectFlowAssociationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOaProjectFlowAssociation" parameterType="OaProjectFlowAssociation" useGeneratedKeys="true" keyProperty="id">
        insert into oa_project_flow_association
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectFlowMainId != null">project_flow_main_id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="projectName != null">project_name,</if>
            <if test="status != null">status,</if>
            <if test="createBr != null">create_br,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBr != null">update_br,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectFlowMainId != null">#{projectFlowMainId},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="status != null">#{status},</if>
            <if test="createBr != null">#{createBr},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBr != null">#{updateBr},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOaProjectFlowAssociation" parameterType="OaProjectFlowAssociation">
        update oa_project_flow_association
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectFlowMainId != null">project_flow_main_id = #{projectFlowMainId},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBr != null">create_br = #{createBr},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBr != null">update_br = #{updateBr},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOaProjectFlowAssociationById" parameterType="Long">
        delete from oa_project_flow_association where id = #{id}
    </delete>

    <delete id="deleteOaProjectFlowAssociationByIds" parameterType="String">
        delete from oa_project_flow_association where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getProjectByMainId" resultType="map">
        select id,project_id as projectId,project_name as projectName from oa_project_flow_association where project_flow_main_id = #{id}
    </select>

    <delete id="deleteByMainId" parameterType="long">
        delete from oa_project_flow_association where project_flow_main_id = #{id}
    </delete>

    <select id="selectOaProjectFlowIdsAssociationByMainId" resultType="java.lang.Long">
        SELECT id FROM oa_project_flow_association WHERE project_flow_main_id=#{id,jdbcType=BIGINT}
    </select>
</mapper>