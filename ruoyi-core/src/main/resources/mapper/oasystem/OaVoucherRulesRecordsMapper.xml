<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaVoucherRulesRecordsMapper">







    <insert id="insertOaVoucherRulesRecords" parameterType="OaVoucherRulesRecords" useGeneratedKeys="true" keyProperty="id">
        insert into oa_voucher_rules_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oaVoucherRulesMainId != null">oa_voucher_rules_main_id,</if>
            <if test="data != null and data != ''">data,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oaVoucherRulesMainId != null">#{oaVoucherRulesMainId,jdbcType=BIGINT},</if>
            <if test="data != null and data != ''">#{data,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <select id="selectOaVoucherRulesRecordsById" resultType="org.ruoyi.core.oasystem.domain.OaVoucherRulesRecords">
        SELECT id, oa_voucher_rules_main_id AS oaVoucherRulesMainId, data, status, create_by AS createBy, create_time AS createTime, update_by AS updateBy, update_time AS updateTime FROM oa_voucher_rules_records WHERE id=#{id,jdbcType=BIGINT} AND status='0'
    </select>

    <delete id="deleteOaVoucherRulesRecordsByOaVoucherRulesMainId">
        DELETE FROM oa_voucher_rules_records WHERE oa_voucher_rules_main_id=#{oaVoucherRulesMainId,jdbcType=BIGINT}
    </delete>

</mapper>