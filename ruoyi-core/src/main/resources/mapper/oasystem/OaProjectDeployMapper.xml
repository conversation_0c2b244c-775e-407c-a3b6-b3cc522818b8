<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaProjectDeployMapper">

    <resultMap type="OaProjectDeploy" id="OaProjectDeployResult">
        <result property="id"    column="id"    />
        <result property="oaApplyId"    column="oa_apply_id"    />
        <result property="companyNo"    column="company_no"    />
        <result property="projectName"    column="project_name"    />

        <result property="channelType" column="channel_type"/>
        <result property="channelName" column="channel_name"/>
        <result property="creditAmount" column="credit_amount"/>
        <result property="checkStatus" column="check_status"/>

        <result property="projectType"    column="project_type"    />
        <result property="isEnable"    column="is_enable"    />
        <result property="createBr"    column="create_br"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBr"    column="update_br"    />
        <result property="updateTime"    column="update_time"    />
        <result property="cwRelevanceStatus"    column="cw_relevance_status"    />
        <result property="addNotApprove"    column="add_not_approve"    />
        <result property="companyName" column="companyName"/>
        <result property="cwProjectFeeFlag" column="cw_project_fee_flag"/>
        <result property="deployProcessFlag" column="deploy_process_flag"/>
        <result property="traderProcessFlag" column="trader_process_flag"/>
        <result property="feeProcessFlag" column="fee_process_flag"/>
    </resultMap>

    <resultMap type="OaProjectDeployBo" id="OaProjectDeployRepeatResult">
        <result property="id"    column="id"    />
        <!-- <result property="oaApplyId"    column="oa_apply_id"    />-->
        <result property="companyNo"    column="company_no"    />
        <result property="projectName"    column="project_name"    />
        <result property="projectType"    column="project_type"    />
        <result property="isEnable"    column="is_enable"    />
        <result property="createBr"    column="create_br"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBr"    column="update_br"    />
        <result property="updateTime"    column="update_time"    />
        <!-- <result property="cwRelevanceStatus"    column="cw_relevance_status"    />-->
        <result property="addNotApprove"    column="add_not_approve"    />
        <result property="companyName" column="companyName"/>
        <!-- 担保公司-->
        <!-- <collection property="custList" column="{id = id, dataType = '0'}" select="queryDataObjectByTypeAndId"/>-->
        <!-- 资金方-->
        <!-- <collection property="partnerList" column="{id = id, dataType = '2'}" select="queryDataObjectByTypeAndId"/>-->
        <!-- 资产方-->
        <!-- <collection property="fundList" column="{id = id, dataType = '1'}" select="queryDataObjectByTypeAndId"/>-->
        <!-- 其他公司-->
        <!-- <collection property="otherUnitList" column="{id = id, dataType = '3'}" select="queryDataObjectByTypeAndId"/>-->
         <collection property="otherUnitList" column="id" select="queryDataObjectById"/>
    </resultMap>

    <resultMap type="ProjectCompanyRelevance" id="ProjectCompanyRelevanceResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="unitId"    column="unit_id"    />
        <result property="unitType"    column="unit_type"    />
        <result property="unitTypeId"    column="unit_type_id"    />
        <result property="proportion"    column="proportion"    />
        <result property="isNecessity"    column="is_necessity"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="unitName" column="company_name"/>
        <result property="unitShortName" column="company_short_name"/>
    </resultMap>

    <sql id="selectOaProjectDeployVo">
        select id, id AS oa_apply_id, company_no, project_name,channel_type,channel_name,credit_amount,check_status, project_type, is_enable, create_br, create_time, update_br, update_time, cw_relevance_status, add_not_approve, cw_project_fee_flag, deploy_process_flag, trader_process_flag, fee_process_flag from oa_project_deploy
    </sql>

    <select id="selectOaProjectDeployList" parameterType="OaProjectDeploy" resultMap="OaProjectDeployResult">
        <include refid="selectOaProjectDeployVo"/>
        <where>
            <if test="companyNo != null"> and company_no = #{companyNo}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectType != null  and projectType != ''"> and project_type = #{projectType}</if>
            <if test="isEnable != null  and isEnable != ''"> and is_enable = #{isEnable}</if>
            <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
            <if test="addNotApprove != null and addNotApprove != ''"> and add_not_approve = #{addNotApprove,jdbcType=VARCHAR}</if>
        </where>
        ORDER BY create_time DESC
    </select>


    <select id="newSelectOaProjectDeployList" parameterType="OaProjectDeploy" resultMap="OaProjectDeployResult">
        SELECT
        opd.id, opd.id AS oa_apply_id, opd.company_no, opd.project_name, opd.project_type, opd.is_enable, opd.create_br, opd.create_time, opd.update_br, opd.update_time, opd.cw_relevance_status, opd.add_not_approve
        ,sc.company_short_name companyName
        FROM oa_project_deploy opd LEFT JOIN project_company_relevance pcr ON opd.id = pcr.project_id LEFT JOIN project_type_relevance ptr on opd.id = ptr.project_id
        left join sys_company sc on sc.id = opd.company_no
        <where>
            1=1
            <if test="projectName != null  and projectName != ''"> and opd.project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectType != null  and projectType != ''"> and ptr.type_id = #{projectType} and data_type ='0' </if>


            <if test="custNo != null or partnerNo != null or fundNo != null ">
                and (
                <if test="custNo != null">  (pcr.unit_id = #{custNo} and pcr.unit_type = '0')</if>
                <if test="custNo != null and partnerNo != null">
                    or
                </if>
                <if test="partnerNo != null">  (pcr.unit_id = #{partnerNo} and pcr.unit_type = '1')</if>
                <if test="fundNo != null and( custNo != null or partnerNo != null)">
                    or
                </if>
                <if test="fundNo != null">  (pcr.unit_id = #{fundNo} and pcr.unit_type = '2')</if>
                )
            </if>

            <if test="isEnable != null  and isEnable != ''"> and is_enable = #{isEnable}</if>
            <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
            <if test="addNotApprove != null  and addNotApprove != ''"> and add_not_approve = #{addNotApprove,jdbcType=VARCHAR}</if>
        </where>
        group by  opd.project_name
        ORDER BY opd.create_time DESC

    </select>

    <select id="selectOaProjectDeployById" parameterType="Long" resultMap="OaProjectDeployResult">
        <include refid="selectOaProjectDeployVo"/>
        where id = #{id}
    </select>
        <select id="selectDataById" resultMap="OaProjectDeployResult">
            SELECT
                opd.id, opd.id AS oa_apply_id, opd.company_no, opd.project_name,opd.channel_type,opd.channel_name,opd.credit_amount,opd.check_status, opd.project_type, opd.is_enable, opd.create_br, opd.create_time, opd.update_br, opd.update_time, opd.cw_relevance_status, opd.add_not_approve
                 ,sc.company_short_name companyName, opd.cw_project_fee_flag, opd.deploy_process_flag, opd.trader_process_flag, opd.fee_process_flag
            FROM oa_project_deploy opd LEFT JOIN project_company_relevance pcr ON opd.id = pcr.project_id LEFT JOIN project_type_relevance ptr on opd.id = ptr.project_id
                                       left join sys_company sc on sc.id = opd.company_no
            where  opd.id = #{id}
            group by opd.id
        </select>
    <insert id="insertOaProjectDeploy" parameterType="OaProjectDeploy" useGeneratedKeys="true" keyProperty="id">
        insert into oa_project_deploy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyNo != null">company_no,</if>
            <if test="projectName != null">project_name,</if>
            <if test="channelType != null and channelType != ''">channel_type,</if>
            <if test="channelName != null and channelName != ''">channel_name,</if>
            <if test="creditAmount != null">credit_amount,</if>
            <if test="checkStatus != null and checkStatus != ''">check_status,</if>
            <if test="projectType != null">project_type,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="createBr != null">create_br,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBr != null">update_br,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="cwRelevanceStatus != null and cwRelevanceStatus != ''">cw_relevance_status,</if>
            <if test="addNotApprove != null and addNotApprove != ''">add_not_approve,</if>
            <if test="cwProjectFeeFlag != null and cwProjectFeeFlag != ''">cw_project_fee_flag,</if>
            <if test="deployProcessFlag != null and deployProcessFlag != ''">deploy_process_flag,</if>
            <if test="traderProcessFlag != null and traderProcessFlag != ''">trader_process_flag,</if>
            <if test="feeProcessFlag != null and feeProcessFlag != ''">fee_process_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyNo != null">#{companyNo},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="channelType != null and channelType != ''">#{channelType},</if>
            <if test="channelName != null and channelName != ''">#{channelName},</if>
            <if test="creditAmount != null">#{creditAmount},</if>
            <if test="checkStatus != null and checkStatus != ''">#{checkStatus},</if>
            <if test="projectType != null">#{projectType},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="createBr != null">#{createBr},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBr != null">#{updateBr},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="cwRelevanceStatus != null and cwRelevanceStatus != ''">#{cwRelevanceStatus,jdbcType=VARCHAR},</if>
            <if test="addNotApprove != null and addNotApprove != ''">#{addNotApprove,jdbcType=VARCHAR},</if>
            <if test="cwProjectFeeFlag != null and cwProjectFeeFlag != ''">#{cwProjectFeeFlag,jdbcType=VARCHAR},</if>
            <if test="deployProcessFlag != null and deployProcessFlag != ''">#{deployProcessFlag,jdbcType=VARCHAR},</if>
            <if test="traderProcessFlag != null and traderProcessFlag != ''">#{traderProcessFlag,jdbcType=VARCHAR},</if>
            <if test="feeProcessFlag != null and feeProcessFlag != ''">#{feeProcessFlag,jdbcType=VARCHAR},</if>
         </trim>
    </insert>

    <update id="updateOaProjectDeploy" parameterType="OaProjectDeploy">
        update oa_project_deploy
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyNo != null">company_no = #{companyNo},</if>
            <if test="projectName != null">project_name = #{projectName},</if>

            <if test="channelType != null and channelType != ''">channel_type = #{channelType},</if>
            <if test="channelName != null and channelName != ''">channel_name = #{channelName},</if>
            <if test="creditAmount != null">credit_amount = #{creditAmount},</if>
            <if test="checkStatus != null and checkStatus != ''">check_status = #{checkStatus},</if>

            <if test="projectType != null">project_type = #{projectType},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="createBr != null">create_br = #{createBr},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBr != null">update_br = #{updateBr},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="cwRelevanceStatus != null and cwRelevanceStatus != ''">cw_relevance_status = #{cwRelevanceStatus,jdbcType=VARCHAR},</if>
            <if test="addNotApprove != null and addNotApprove != ''">add_not_approve = #{addNotApprove,jdbcType=VARCHAR},</if>
            <if test="cwProjectFeeFlag != null and cwProjectFeeFlag != ''">cw_project_fee_flag = #{cwProjectFeeFlag,jdbcType=VARCHAR},</if>
            <if test="deployProcessFlag != null and deployProcessFlag != ''">deploy_process_flag = #{deployProcessFlag,jdbcType=VARCHAR},</if>
            <if test="traderProcessFlag != null and traderProcessFlag != ''">trader_process_flag = #{traderProcessFlag,jdbcType=VARCHAR},</if>
            <if test="feeProcessFlag != null and feeProcessFlag != ''">fee_process_flag = #{feeProcessFlag,jdbcType=VARCHAR},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOaProjectDeployById" parameterType="Long">
        delete from oa_project_deploy where id = #{id}
    </delete>

    <delete id="deleteOaProjectDeployByIds" parameterType="String">
        delete from oa_project_deploy where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectProjectMapCollByProjectId" resultType="java.util.Map">
        select opd.id projectId, oppa.coll_name collName, oppa.coll_account_number collAccountNumber, oppa.coll_bank_of_deposit collBankOfDeposit,
               oppa.pay_name payName, oppa.pay_account_number payAccountNumber, oppa.pay_bank_of_deposit payBankOfDeposit
        from oa_project_deploy opd
        inner join oa_project_flow_association opfa on opd.id = opfa.project_id
        inner join oa_project_payer_association oppa on opfa.id = oppa.pfa_id
        where opd.id = #{projectId}
    </select>

    <select id="selectByData" resultMap="OaProjectDeployRepeatResult">
        <include refid="selectOaProjectDeployVo"/>
        where is_enable = 'Y'
        <!--<where>
            <if test="companyNo != null"> and company_no = #{companyNo}</if>
            <if test="projectName != null  and projectName != ''"> and project_name = #{projectName}</if>
            <if test="projectType != null  and projectType != ''"> and project_type = #{projectType}</if>
            <if test="isEnable != null  and isEnable != ''"> and is_enable = #{isEnable}</if>
            <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
            <if test="addNotApprove != null and addNotApprove != ''"> and add_not_approve = #{addNotApprove,jdbcType=VARCHAR}</if>
        </where>
        ORDER BY create_time DESC-->

    </select>

    <select id="selectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdList" resultMap="OaProjectDeployResult">
        <include refid="selectOaProjectDeployVo"/>
        <where>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND company_no IN
                <foreach collection="companyIdList" item="companyNo" open="(" separator="," close=")">
                    #{companyNo,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="filterOaApplyId != null and filterOaApplyId.size() != 0">
                AND id IN
                <foreach collection="filterOaApplyId" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="oaProjectDeploy.companyNo != null"> and company_no = #{oaProjectDeploy.companyNo}</if>
            <if test="oaProjectDeploy.projectName != null  and oaProjectDeploy.projectName != ''"> and project_name like concat('%', #{oaProjectDeploy.projectName}, '%')</if>
            <if test="oaProjectDeploy.projectType != null  and oaProjectDeploy.projectType != ''"> and project_type = #{oaProjectDeploy.projectType}</if>
            <if test="oaProjectDeploy.isEnable != null  and oaProjectDeploy.isEnable != ''"> and is_enable = #{oaProjectDeploy.isEnable}</if>
            <if test="oaProjectDeploy.createBr != null  and oaProjectDeploy.createBr != ''"> and create_br = #{oaProjectDeploy.createBr}</if>
            <if test="oaProjectDeploy.updateBr != null  and oaProjectDeploy.updateBr != ''"> and update_br = #{oaProjectDeploy.updateBr}</if>
            <if test="oaProjectDeploy.addNotApprove != null  and oaProjectDeploy.addNotApprove != ''"> and add_not_approve = #{oaProjectDeploy.addNotApprove,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.cwProjectFeeFlag != null and oaProjectDeploy.cwProjectFeeFlag != ''"> and cw_project_fee_flag = #{oaProjectDeploy.cwProjectFeeFlag,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.deployProcessFlag != null and oaProjectDeploy.deployProcessFlag != ''"> and deploy_process_flag = #{oaProjectDeploy.deployProcessFlag,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.traderProcessFlag != null and oaProjectDeploy.traderProcessFlag != ''"> and trader_process_flag = #{oaProjectDeploy.traderProcessFlag,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.feeProcessFlag != null and oaProjectDeploy.feeProcessFlag != ''"> and fee_process_flag = #{oaProjectDeploy.feeProcessFlag,jdbcType=VARCHAR}</if>
        </where>
        ORDER BY create_time DESC
    </select>


    <select id="newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdList" parameterType="OaProjectDeploy" resultMap="OaProjectDeployResult">
        SELECT opd.id, opd.id AS oa_apply_id, opd.company_no, opd.project_name,opd.channel_type,opd.channel_name,opd.credit_amount,opd.check_status, opd.project_type, opd.is_enable, opd.create_br, opd.create_time, opd.update_br, opd.update_time, opd.cw_relevance_status, opd.add_not_approve,
        sc.company_short_name companyName, opd.cw_project_fee_flag, opd.deploy_process_flag, opd.trader_process_flag, opd.fee_process_flag
        FROM oa_project_deploy opd
        LEFT JOIN project_company_relevance pcr ON opd.id = pcr.project_id
        LEFT JOIN project_type_relevance ptr on opd.id = ptr.project_id
        left join sys_company sc on sc.id = opd.company_no
        <where>
            1=1
            <if test="projectName != null  and projectName != ''"> and opd.project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectType != null  and projectType != ''"> and ptr.type_id = #{projectType} and data_type ='0' </if>


            <if test="custNo != null or partnerNo != null or fundNo != null ">
                and (
                <if test="custNo != null">  (pcr.unit_id = #{custNo} and pcr.unit_type = '0')</if>
                <if test="custNo != null and partnerNo != null">
                    and
                </if>
                <if test="partnerNo != null">  (pcr.unit_id = #{partnerNo} and pcr.unit_type = '1')</if>
                <if test="fundNo != null and( custNo != null or partnerNo != null)">
                    and
                </if>
                <if test="fundNo != null">  (pcr.unit_id = #{fundNo} and pcr.unit_type = '2')</if>
                )
            </if>

            <if test="moreSearch != null and moreSearch.size() > 0">
                <foreach collection="moreSearch" item="entry" index="key">
                    and (pcr.unit_id in
                    <foreach collection="entry" item="unitId" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                    and pcr.unit_type = #{key})
                </foreach>
            </if>
            <if test="businessTypeIds != null and businessTypeIds.size() > 0">
                and ptr.data_type = '1' and ptr.type_id in
                <foreach item="btypeId" collection="businessTypeIds" open="(" close=")" separator=",">
                    #{btypeId}
                </foreach>
            </if>
            <if test="projectTypeIds != null and projectTypeIds.size() > 0">
                and ptr.data_type = '0' and ptr.type_id in
                <foreach item="ptypeId" collection="projectTypeIds" open="(" close=")" separator=",">
                    #{ptypeId}
                </foreach>
            </if>
            <if test="isEnable != null  and isEnable != ''"> and is_enable = #{isEnable}</if>
            <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
            <if test="addNotApprove != null  and addNotApprove != ''"> and add_not_approve = #{addNotApprove,jdbcType=VARCHAR}</if>
            <if test="cwProjectFeeFlag != null and cwProjectFeeFlag != ''"> and cw_project_fee_flag = #{cwProjectFeeFlag,jdbcType=VARCHAR}</if>
            <if test="deployProcessFlag != null and deployProcessFlag != ''"> and deploy_process_flag = #{deployProcessFlag,jdbcType=VARCHAR}</if>
            <if test="traderProcessFlag != null and traderProcessFlag != ''"> and trader_process_flag = #{traderProcessFlag,jdbcType=VARCHAR}</if>
            <if test="feeProcessFlag != null and feeProcessFlag != ''"> and fee_process_flag = #{feeProcessFlag,jdbcType=VARCHAR}</if>
            and opd.is_enable != 'D'
        </where>
        group by  opd.id
        ORDER BY opd.create_time DESC
    </select>






    <select id="getFilterOaProjectDeployList" resultMap="OaProjectDeployResult">
        <include refid="selectOaProjectDeployVo"/>
        <where>
            AND id NOT IN
            <foreach collection="oaProjectDeployIdList" item="oaProjectDeployId" open="(" separator="," close=")">
                #{oaProjectDeployId,jdbcType=BIGINT}
            </foreach>
            AND is_enable='Y'
        </where>
    </select>

    <select id="getAllOaProjectDeployList" resultType="org.ruoyi.core.oasystem.domain.OaProjectDeploy">
        <include refid="selectOaProjectDeployVo"/> WHERE is_enable='Y'
    </select>

    <select id="selectOaProjectDeployListTotal" resultType="java.lang.Long">
        SELECT COUNT(*) FROM
        (

        SELECT opd.id, opd.id AS oa_apply_id, opd.company_no, opd.project_name, opd.project_type, opd.is_enable, opd.create_br, opd.create_time, opd.update_br, opd.update_time, opd.cw_relevance_status, opd.add_not_approve,
        sc.company_short_name companyName
        FROM oa_project_deploy opd
        LEFT JOIN project_company_relevance pcr ON opd.id = pcr.project_id
        LEFT JOIN project_type_relevance ptr on opd.id = ptr.project_id
        left join sys_company sc on sc.id = opd.company_no
        <where>
            1=1
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND company_no IN
                <foreach collection="companyIdList" item="companyNo" open="(" separator="," close=")">
                    #{companyNo,jdbcType=BIGINT}
                </foreach>
            </if>

<!--            <if test="filterOaApplyId != null and filterOaApplyId.size() != 0">-->
<!--                AND opd.id IN-->
<!--                <foreach collection="filterOaApplyId" item="id" open="(" separator="," close=")">-->
<!--                    #{id,jdbcType=BIGINT}-->
<!--                </foreach>-->

<!--            </if>-->

            <if test="oaProjectDeploy.projectName != null  and oaProjectDeploy.projectName != ''"> and opd.project_name like concat('%', #{oaProjectDeploy.projectName}, '%')</if>
            <if test="oaProjectDeploy.projectType != null  and oaProjectDeploy.projectType != ''"> and ptr.type_id = #{oaProjectDeploy.projectType} and data_type ='0' </if>


            <if test="oaProjectDeploy.custNo != null or oaProjectDeploy.partnerNo != null or oaProjectDeploy.fundNo != null ">
                and (
                <if test="oaProjectDeploy.custNo != null">  (pcr.unit_id = #{oaProjectDeploy.custNo} and pcr.unit_type = '0')</if>
                <if test="oaProjectDeploy.custNo != null and oaProjectDeploy.partnerNo != null">
                    and
                </if>
                <if test="oaProjectDeploy.partnerNo != null">  (pcr.unit_id = #{oaProjectDeploy.partnerNo} and pcr.unit_type = '1')</if>
                <if test="oaProjectDeploy.fundNo != null and( oaProjectDeploy.custNo != null or oaProjectDeploy.partnerNo != null)">
                    and
                </if>
                <if test="oaProjectDeploy.fundNo != null">  (pcr.unit_id = #{oaProjectDeploy.fundNo} and pcr.unit_type = '2')</if>
                )
            </if>

            <if test="oaProjectDeploy.isEnable != null  and oaProjectDeploy.isEnable != ''"> and is_enable = #{oaProjectDeploy.isEnable}</if>
            <if test="oaProjectDeploy.createBr != null  and oaProjectDeploy.createBr != ''"> and create_br = #{oaProjectDeploy.createBr}</if>
            <if test="oaProjectDeploy.updateBr != null  and oaProjectDeploy.updateBr != ''"> and update_br = #{oaProjectDeploy.updateBr}</if>
            <if test="oaProjectDeploy.addNotApprove != null  and oaProjectDeploy.addNotApprove != ''"> and add_not_approve = #{oaProjectDeploy.addNotApprove,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.cwProjectFeeFlag != null and oaProjectDeploy.cwProjectFeeFlag != ''"> and cw_project_fee_flag = #{oaProjectDeploy.cwProjectFeeFlag,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.deployProcessFlag != null and oaProjectDeploy.deployProcessFlag != ''"> and deploy_process_flag = #{oaProjectDeploy.deployProcessFlag,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.traderProcessFlag != null and oaProjectDeploy.traderProcessFlag != ''"> and trader_process_flag = #{oaProjectDeploy.traderProcessFlag,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.feeProcessFlag != null and oaProjectDeploy.feeProcessFlag != ''"> and fee_process_flag = #{oaProjectDeploy.feeProcessFlag,jdbcType=VARCHAR}</if>
        </where>
        group by  opd.id
        ORDER BY opd.create_time DESC
        ) a
    </select>

    <select id="selectOaProjectDeployListTotalByOaProjectDeployAndCompanyIdListAndFilterOaApplyIdList" resultType="java.lang.Long">
        SELECT COUNT(*) FROM oa_project_deploy
        <where>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND company_no IN
                <foreach collection="companyIdList" item="companyNo" open="(" separator="," close=")">
                    #{companyNo,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="filterOaApplyId != null and filterOaApplyId.size() != 0">
                AND id IN
                <foreach collection="filterOaApplyId" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="oaProjectDeploy.companyNo != null"> and company_no = #{oaProjectDeploy.companyNo}</if>
            <if test="oaProjectDeploy.projectName != null  and oaProjectDeploy.projectName != ''"> and project_name like concat('%', #{oaProjectDeploy.projectName}, '%')</if>
            <if test="oaProjectDeploy.projectType != null  and oaProjectDeploy.projectType != ''"> and project_type = #{oaProjectDeploy.projectType}</if>
            <if test="oaProjectDeploy.isEnable != null  and oaProjectDeploy.isEnable != ''"> and is_enable = #{oaProjectDeploy.isEnable}</if>
            <if test="oaProjectDeploy.createBr != null  and oaProjectDeploy.createBr != ''"> and create_br = #{oaProjectDeploy.createBr}</if>
            <if test="oaProjectDeploy.updateBr != null  and oaProjectDeploy.updateBr != ''"> and update_br = #{oaProjectDeploy.updateBr}</if>
            <if test="oaProjectDeploy.addNotApprove != null  and oaProjectDeploy.addNotApprove != ''"> and add_not_approve = #{oaProjectDeploy.addNotApprove,jdbcType=VARCHAR}</if>
        </where>
    </select>

    <select id="selectProjectByCompanyId" resultType="org.ruoyi.core.oasystem.domain.OaProjectDeploy">

        SELECT opd.id,opd.project_name  FROM project_company_relevance pcr
                                                 LEFT JOIN oa_project_deploy opd ON opd.id= pcr.project_id
        WHERE pcr.unit_id = #{id} GROUP BY opd.id
    </select>

    <select id="queryCooperationUnit" resultType="map">

        SELECT count(*) num FROM (
                                 SELECT  unit_id,unit_type FROM project_company_relevance WHERE project_id IN(

                                     SELECT project_id FROM project_company_relevance WHERE unit_id = #{companyId}) AND unit_type = #{companyType} GROUP BY unit_id,unit_type) a
    </select>

    <select id="getDataByTypelist" resultMap="OaProjectDeployResult">
        select opd.id, opd.project_name   FROM project_type_relevance  ptr LEFT JOIN   oa_project_deploy opd ON ptr.project_id = opd.id
        WHERE ptr.data_type = #{typeData} AND ptr.type_id = #{typeId} GROUP BY ptr.project_id
    </select>

    <select id="selectOaProjectDeployListByOaProjectDeployIdList" resultMap="OaProjectDeployResult">
        <include refid="selectOaProjectDeployVo"/>
        <where>
            AND id IN
            <foreach collection="oaProjectDeployIdList" item="oaProjectDeployId" open="(" separator="," close=")">
                #{oaProjectDeployId,jdbcType=BIGINT}
            </foreach>
            AND is_enable='Y'
            AND project_name NOT LIKE '%暂不确定%'
        </where>
    </select>

    <select id="getProjectDataById" resultType="map">
        select id value,project_name label from oa_project_deploy where 1=1

        <if test="projectIds != null and projectIds.size() != 0  ">
            and id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
    </select>

    <select id="getProjectTypeList" resultType="map">

        SELECT sdd.dict_label label,ptr.type_id value  FROM project_type_relevance ptr LEFT JOIN sys_dict_data sdd ON ptr.type_id = sdd.dict_code
        WHERE ptr.data_type = '0' AND ptr.project_id = #{id}

    </select>

    <select id="newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdListAndUser" parameterType="OaProjectDeploy" resultMap="OaProjectDeployResult">
        SELECT opd.channel_type,opd.channel_name,opd.credit_amount,opd.check_status,opd.id, opd.id AS oa_apply_id, opd.company_no, opd.project_name, opd.project_type, opd.is_enable, opd.create_br, opd.create_time, opd.update_br, opd.update_time, opd.cw_relevance_status, opd.add_not_approve,
        sc.company_short_name companyName, opd.cw_project_fee_flag, opd.deploy_process_flag, opd.trader_process_flag, opd.fee_process_flag
        FROM
        oa_edit_approve_generality_user oeagu LEFT JOIN
        oa_project_deploy opd	ON  oeagu.oa_apply_id = opd.id
        LEFT JOIN project_company_relevance pcr ON opd.id = pcr.project_id
        LEFT JOIN project_type_relevance ptr on opd.id = ptr.project_id
        left join sys_company sc on sc.id = opd.company_no
        <where>
            oeagu.oa_apply_type = '4'
            and oeagu.user_id = #{userid}

            <if test="projectName != null  and projectName != ''"> and opd.project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectType != null  and projectType != ''"> and ptr.type_id = #{projectType} and data_type ='0' </if>


            <if test="custNo != null or partnerNo != null or fundNo != null ">
                and (
                <if test="custNo != null">  (pcr.unit_id = #{custNo} and pcr.unit_type = '0')</if>
                <if test="custNo != null and partnerNo != null">
                    or
                </if>
                <if test="partnerNo != null">  (pcr.unit_id = #{partnerNo} and pcr.unit_type = '1')</if>
                <if test="fundNo != null and( custNo != null or partnerNo != null)">
                    or
                </if>
                <if test="fundNo != null">  (pcr.unit_id = #{fundNo} and pcr.unit_type = '2')</if>
                )
            </if>
            <if test="moreSearch != null and moreSearch.size() > 0">
                <foreach collection="moreSearch" item="entry" index="key">
                    and (pcr.unit_id in
                    <foreach collection="entry" item="unitId" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                    and pcr.unit_type = #{key})
                </foreach>
            </if>
            <if test="isEnable != null  and isEnable != ''"> and is_enable = #{isEnable}</if>
            <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
            <if test="addNotApprove != null  and addNotApprove != ''"> and add_not_approve = #{addNotApprove,jdbcType=VARCHAR}</if>
            <if test="cwProjectFeeFlag != null and cwProjectFeeFlag != ''"> and cw_project_fee_flag = #{cwProjectFeeFlag,jdbcType=VARCHAR}</if>
            <if test="deployProcessFlag != null and deployProcessFlag != ''"> and deploy_process_flag = #{deployProcessFlag,jdbcType=VARCHAR}</if>
            <if test="traderProcessFlag != null and traderProcessFlag != ''"> and trader_process_flag = #{traderProcessFlag,jdbcType=VARCHAR}</if>
            <if test="feeProcessFlag != null and feeProcessFlag != ''"> and fee_process_flag = #{feeProcessFlag,jdbcType=VARCHAR}</if>
            and opd.is_enable != 'D'
        </where>
        group by  opd.id
        ORDER BY opd.create_time DESC
    </select>



    <select id="newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdListTotal" parameterType="OaProjectDeploy" resultType="long">
        select  count(*) total from (SELECT opd.channel_type,opd.channel_name,opd.credit_amount,opd.check_status,opd.id, opd.id AS oa_apply_id, opd.company_no, opd.project_name, opd.project_type, opd.is_enable, opd.create_br, opd.create_time, opd.update_br, opd.update_time, opd.cw_relevance_status, opd.add_not_approve,
        sc.company_short_name companyName
        FROM oa_project_deploy opd
        LEFT JOIN project_company_relevance pcr ON opd.id = pcr.project_id
        LEFT JOIN project_type_relevance ptr on opd.id = ptr.project_id
        left join sys_company sc on sc.id = opd.company_no
        <where>
            1=1
            <if test="projectName != null  and projectName != ''"> and opd.project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectType != null  and projectType != ''"> and ptr.type_id = #{projectType} and data_type ='0' </if>


            <if test="custNo != null or partnerNo != null or fundNo != null ">
                and (
                <if test="custNo != null">  (pcr.unit_id = #{custNo} and pcr.unit_type = '0')</if>
                <if test="custNo != null and partnerNo != null">
                    and
                </if>
                <if test="partnerNo != null">  (pcr.unit_id = #{partnerNo} and pcr.unit_type = '1')</if>
                <if test="fundNo != null and( custNo != null or partnerNo != null)">
                    and
                </if>
                <if test="fundNo != null">  (pcr.unit_id = #{fundNo} and pcr.unit_type = '2')</if>
                )
            </if>
            <if test="moreSearch != null and moreSearch.size() > 0">
                <foreach collection="moreSearch" item="entry" index="key">
                    and (pcr.unit_id in
                    <foreach collection="entry" item="unitId" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                    and pcr.unit_type = #{key})
                </foreach>
            </if>
            <if test="businessTypeIds != null and businessTypeIds.size() > 0">
                and ptr.data_type = '1' and ptr.type_id in
                <foreach item="btypeId" collection="businessTypeIds" open="(" close=")" separator=",">
                    #{btypeId}
                </foreach>
            </if>
            <if test="projectTypeIds != null and projectTypeIds.size() > 0">
                and ptr.data_type = '0' and ptr.type_id in
                <foreach item="ptypeId" collection="projectTypeIds" open="(" close=")" separator=",">
                    #{ptypeId}
                </foreach>
            </if>
            <if test="isEnable != null  and isEnable != ''"> and is_enable = #{isEnable}</if>
            <if test="createBr != null  and createBr != ''"> and create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and update_br = #{updateBr}</if>
            <if test="addNotApprove != null  and addNotApprove != ''"> and add_not_approve = #{addNotApprove,jdbcType=VARCHAR}</if>
            <if test="cwProjectFeeFlag != null and cwProjectFeeFlag != ''"> and cw_project_fee_flag = #{cwProjectFeeFlag,jdbcType=VARCHAR}</if>
            <if test="deployProcessFlag != null and deployProcessFlag != ''"> and deploy_process_flag = #{deployProcessFlag,jdbcType=VARCHAR}</if>
            <if test="traderProcessFlag != null and traderProcessFlag != ''"> and trader_process_flag = #{traderProcessFlag,jdbcType=VARCHAR}</if>
            <if test="feeProcessFlag != null and feeProcessFlag != ''"> and fee_process_flag = #{feeProcessFlag,jdbcType=VARCHAR}</if>
            and opd.is_enable != 'D'
        </where>
        group by  opd.id
        ORDER BY opd.create_time DESC) a
    </select>

    <select id="newSelectOaProjectDeployListByOaProjectDeployAndFilterOaApplyIdAndCompanyIdListAndUserTotal" resultType="long">

       select  count(*) total from (SELECT opd.channel_type,opd.channel_name,opd.credit_amount,opd.check_status,opd.id, opd.id AS oa_apply_id, opd.company_no, opd.project_name, opd.project_type, opd.is_enable, opd.create_br, opd.create_time, opd.update_br, opd.update_time, opd.cw_relevance_status, opd.add_not_approve,
        sc.company_short_name companyName
        FROM
        oa_edit_approve_generality_user oeagu LEFT JOIN
        oa_project_deploy opd	ON  oeagu.oa_apply_id = opd.id
        LEFT JOIN project_company_relevance pcr ON opd.id = pcr.project_id
        LEFT JOIN project_type_relevance ptr on opd.id = ptr.project_id
        left join sys_company sc on sc.id = opd.company_no
        <where>
            oeagu.oa_apply_type = '4'
            and oeagu.user_id = #{userId}

            <if test="oaProjectDeploy.projectName != null  and oaProjectDeploy.projectName != ''"> and opd.project_name like concat('%', #{oaProjectDeploy.projectName}, '%')</if>
            <if test="oaProjectDeploy.projectType != null  and oaProjectDeploy.projectType != ''"> and ptr.type_id = #{oaProjectDeploy.projectType} and data_type ='0' </if>


            <if test="oaProjectDeploy.custNo != null or oaProjectDeploy.partnerNo != null or oaProjectDeploy.fundNo != null ">
                and (
                <if test="oaProjectDeploy.custNo != null">  (pcr.unit_id = #{oaProjectDeploy.custNo} and pcr.unit_type = '0')</if>
                <if test="oaProjectDeploy.custNo != null and oaProjectDeploy.partnerNo != null">
                    or
                </if>
                <if test="oaProjectDeploy.partnerNo != null">  (pcr.unit_id = #{oaProjectDeploy.partnerNo} and pcr.unit_type = '1')</if>
                <if test="oaProjectDeploy.fundNo != null and( oaProjectDeploy.custNo != null or oaProjectDeploy.partnerNo != null)">
                    or
                </if>
                <if test="oaProjectDeploy.fundNo != null">  (pcr.unit_id = #{oaProjectDeploy.fundNo} and pcr.unit_type = '2')</if>
                )
            </if>

            <if test="oaProjectDeploy.isEnable != null  and oaProjectDeploy.isEnable != ''"> and is_enable = #{oaProjectDeploy.isEnable}</if>
            <if test="oaProjectDeploy.createBr != null  and oaProjectDeploy.createBr != ''"> and create_br = #{oaProjectDeploy.createBr}</if>
            <if test="oaProjectDeploy.updateBr != null  and oaProjectDeploy.updateBr != ''"> and update_br = #{oaProjectDeploy.updateBr}</if>
            <if test="oaProjectDeploy.addNotApprove != null  and oaProjectDeploy.addNotApprove != ''"> and add_not_approve = #{oaProjectDeploy.addNotApprove,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.cwProjectFeeFlag != null and oaProjectDeploy.cwProjectFeeFlag != ''"> and cw_project_fee_flag = #{oaProjectDeploy.cwProjectFeeFlag,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.deployProcessFlag != null and oaProjectDeploy.deployProcessFlag != ''"> and deploy_process_flag = #{oaProjectDeploy.deployProcessFlag,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.traderProcessFlag != null and oaProjectDeploy.traderProcessFlag != ''"> and trader_process_flag = #{oaProjectDeploy.traderProcessFlag,jdbcType=VARCHAR}</if>
            <if test="oaProjectDeploy.feeProcessFlag != null and oaProjectDeploy.feeProcessFlag != ''"> and fee_process_flag = #{oaProjectDeploy.feeProcessFlag,jdbcType=VARCHAR}</if>
            and is_enable != 'D'
        </where>
        group by  opd.id
        ORDER BY opd.create_time DESC) a
    </select>



    <select id="queryDataObjectById" resultMap="ProjectCompanyRelevanceResult">
        select pcr.id, pcr.project_id, pcr.unit_id, pcr.unit_type, pcr.unit_type_id, pcr.proportion, pcr.is_necessity, pcr.remark, pcr.status,
               pcr.create_by, pcr.create_time, pcr.update_by, pcr.update_time,
               scom.company_name,scom.company_short_name
        from project_company_relevance pcr left join sys_company scom on pcr.unit_id = scom.id
        where pcr.project_id = #{id}
    </select>

    <select id="selectReceiptAndPaymentInfoByOaProjectDeployId" resultType="org.ruoyi.core.oasystem.domain.OaProjectDeployReceiptAndPaymentInfo">
        SELECT
            opdrapi.id,
            opdrapi.oa_project_deploy_id AS oaProjectDeployId,
            opdrapi.receipt_and_payment_type AS receiptAndPaymentType,
            opdrapi.item_name AS itemName,
            opdrapi.remark AS remark,
            opdrapi.serial_num AS serialNum,
            opdrapi.input_type AS inputType,
            opdrapi.oa_trader_id AS oaTraderId,
            CASE
                WHEN opdrapi.input_type = '1' AND opdrapi.trader_type IS NULL THEN ot.trader_type
                WHEN opdrapi.input_type = '1' AND opdrapi.trader_type IS NOT NULL THEN opdrapi.trader_type
                ELSE opdrapi.trader_type
            END AS traderType,
            CASE
                WHEN opdrapi.input_type = '1' THEN
                ot.user_name ELSE opdrapi.account_name
            END AS accountName,
            CASE
                WHEN opdrapi.input_type = '1' THEN
                ot.account_number ELSE opdrapi.account_number
            END AS accountNumber,
            CASE
                WHEN opdrapi.input_type = '1' THEN
                ot.bank_of_deposit ELSE opdrapi.bank_of_deposit
            END AS bankOfDeposit,
            ot.abbreviation,
            fas.company_name AS accountIdOfName,
            opdrapi.status,
            opdrapi.create_by AS createBy,
            opdrapi.create_id AS createId,
            opdrapi.create_time AS createTime,
            opdrapi.update_by AS updateBy,
            opdrapi.update_id AS updateId,
            opdrapi.update_time AS updateTime,
            opdrapi.business_scenario AS businessScenario
        FROM
            oa_project_deploy_receipt_and_payment_info opdrapi
            LEFT JOIN oa_trader ot ON opdrapi.oa_trader_id = ot.id
            LEFT JOIN financial_account_sets fas ON ot.account_id = fas.id
        WHERE
            opdrapi.oa_project_deploy_id = #{id,jdbcType=BIGINT}
            AND status = '0'
            ORDER BY item_name,serial_num
    </select>

    <insert id="insertReceiptAndPaymentInfo" useGeneratedKeys="true" keyProperty="id">
        insert into oa_project_deploy_receipt_and_payment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oaProjectDeployId != null">oa_project_deploy_id,</if>
            <if test="receiptAndPaymentType != null and receiptAndPaymentType != ''">receipt_and_payment_type,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="serialNum != null">serial_num,</if>
            <if test="inputType != null and inputType != ''">input_type,</if>
            <if test="oaTraderId != null">oa_trader_id,</if>
            <if test="traderType != null and traderType != ''">trader_type,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="accountNumber != null and accountNumber != ''">account_number,</if>
            <if test="bankOfDeposit != null and bankOfDeposit != ''">bank_of_deposit,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="businessScenario != null">business_scenario,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oaProjectDeployId != null">#{oaProjectDeployId,jdbcType=BIGINT},</if>
            <if test="receiptAndPaymentType != null and receiptAndPaymentType != ''">#{receiptAndPaymentType,jdbcType=VARCHAR},</if>
            <if test="itemName != null and itemName != ''">#{itemName,jdbcType=VARCHAR},</if>
            <if test="remark != null and remark != ''">#{remark,jdbcType=VARCHAR},</if>
            <if test="serialNum != null">#{serialNum,jdbcType=INTEGER},</if>
            <if test="inputType != null and inputType != ''">#{inputType,jdbcType=VARCHAR},</if>
            <if test="oaTraderId != null">#{oaTraderId,jdbcType=BIGINT},</if>
            <if test="traderType != null and traderType != ''">#{traderType,jdbcType=VARCHAR},</if>
            <if test="accountName != null and accountName != ''">#{accountName,jdbcType=VARCHAR},</if>
            <if test="accountNumber != null and accountNumber != ''">#{accountNumber,jdbcType=VARCHAR},</if>
            <if test="bankOfDeposit != null and bankOfDeposit != ''">#{bankOfDeposit,jdbcType=VARCHAR},</if>
            <if test="status != null and status != ''">#{status,jdbcType=VARCHAR},</if>
            <if test="createBy != null and createBy != ''">#{createBy,jdbcType=VARCHAR},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="businessScenario != null">#{businessScenario,jdbcType=VARCHAR},</if>
         </trim>
    </insert>

    <update id="updateReceiptAndPaymentInfo">
        update oa_project_deploy_receipt_and_payment_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="oaProjectDeployId != null">oa_project_deploy_id = #{oaProjectDeployId,jdbcType=BIGINT},</if>
            <if test="receiptAndPaymentType != null and receiptAndPaymentType != ''">receipt_and_payment_type = #{receiptAndPaymentType,jdbcType=VARCHAR},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName,jdbcType=VARCHAR},</if>
            <if test="remark != null and remark != ''">remark = #{remark,jdbcType=VARCHAR},</if>
            <if test="serialNum != null">serial_num = #{serialNum,jdbcType=INTEGER},</if>
            <if test="inputType != null and inputType != ''">input_type = #{inputType,jdbcType=VARCHAR},</if>
            <!--加入条件置空-->
            <if test="inputType == '1'.toString() and oaTraderId != null">
            oa_trader_id = #{oaTraderId,jdbcType=BIGINT},
            trader_type = #{traderType,jdbcType=VARCHAR},
            account_name = NULL,
            account_number = NULL,
            bank_of_deposit = NULL,
            </if>
            <!--正常入库-->
            <if test="inputType == '2'.toString() and oaTraderId == null">
            oa_trader_id = NULL,
            <if test="traderType != null and traderType != ''">trader_type = #{traderType,jdbcType=VARCHAR},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName,jdbcType=VARCHAR},</if>
            <if test="accountNumber != null and accountNumber != ''">account_number = #{accountNumber,jdbcType=VARCHAR},</if>
            <if test="bankOfDeposit != null and bankOfDeposit != ''">bank_of_deposit = #{bankOfDeposit,jdbcType=VARCHAR},</if>
            </if>
            <if test="status != null and status != ''">status = #{status,jdbcType=VARCHAR},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy,jdbcType=VARCHAR},</if>
            <if test="createId != null">create_id = #{createId,jdbcType=BIGINT},</if>
            <if test="createTime != null">create_time = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy,jdbcType=VARCHAR},</if>
            <if test="updateId != null">update_id = #{updateId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="businessScenario != null">business_scenario = #{businessScenario,jdbcType=VARCHAR},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReceiptAndPaymentInfoById">
        delete from oa_project_deploy_receipt_and_payment_info where id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteReceiptAndPaymentInfoByIds">
        delete from oa_project_deploy_receipt_and_payment_info where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCwProjectFeeInfoByOaProjectDeployId" resultType="org.ruoyi.core.oasystem.domain.OaProjectDeployCwProjectFeeInfo">
        SELECT
            opdcpfi.id,
            opdcpfi.oa_project_deploy_id AS oaProjectDeployId,
            opdcpfi.company_id AS companyId,
            sc.company_name AS companyName,
            opdcpfi.account_number AS accountNumber,
            opdcpfi.bank_of_deposit AS bankOfDeposit,
            opdcpfi.rate,
            opdcpfi.tax_rate AS taxRate,
            opdcpfi.order_num AS orderNum,
            opdcpfi.status,
            opdcpfi.create_by AS createBy,
            opdcpfi.create_id AS createId,
            opdcpfi.create_time AS createTime,
            opdcpfi.update_by AS updateBy,
            opdcpfi.update_id AS updateId,
            opdcpfi.update_time AS updateTime
        FROM
            oa_project_deploy_cw_project_fee_info opdcpfi
            LEFT JOIN sys_company sc ON opdcpfi.company_id = sc.id
        WHERE
            opdcpfi.status = '0'
            AND opdcpfi.oa_project_deploy_id = #{id,jdbcType=BIGINT}
            ORDER BY opdcpfi.order_num
    </select>

    <insert id="insertCwProjectFeeInfo" useGeneratedKeys="true" keyProperty="id">
        insert into oa_project_deploy_cw_project_fee_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oaProjectDeployId != null">oa_project_deploy_id,</if>
            <if test="companyId == null">
            company_id,
            account_number,
            bank_of_deposit,
            </if>
            <if test="companyId != null">
            company_id,
            <if test="accountNumber != null and accountNumber != ''">account_number,</if>
            <if test="bankOfDeposit != null and bankOfDeposit != ''">bank_of_deposit,</if>
            </if>
            <if test="rate != null">rate,</if>
            <if test="taxRate != null">tax_rate,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oaProjectDeployId != null">#{oaProjectDeployId,jdbcType=BIGINT},</if>
            <if test="companyId == null">
            null,
            null,
            null,
            </if>
            <if test="companyId != null">
            #{companyId,jdbcType=BIGINT},
            <if test="accountNumber != null and accountNumber != ''">#{accountNumber,jdbcType=VARCHAR},</if>
            <if test="bankOfDeposit != null and bankOfDeposit != ''">#{bankOfDeposit,jdbcType=VARCHAR},</if>
            </if>
            <if test="rate != null">#{rate,jdbcType=DECIMAL},</if>
            <if test="taxRate != null">#{taxRate,jdbcType=DECIMAL},</if>
            <if test="orderNum != null">#{orderNum,jdbcType=INTEGER},</if>
            <if test="status != null and status != ''">#{status,jdbcType=VARCHAR},</if>
            <if test="createBy != null and createBy != ''">#{createBy,jdbcType=VARCHAR},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
         </trim>
    </insert>

    <update id="updateCwProjectFeeInfo">
        update oa_project_deploy_cw_project_fee_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="oaProjectDeployId != null">oa_project_deploy_id = #{oaProjectDeployId,jdbcType=BIGINT},</if>
            <if test="companyId != null">company_id = #{companyId,jdbcType=BIGINT},</if>
            <if test="accountNumber != null and accountNumber != ''">account_number = #{accountNumber,jdbcType=VARCHAR},</if>
            <if test="bankOfDeposit != null and bankOfDeposit != ''">bank_of_deposit = #{bankOfDeposit,jdbcType=VARCHAR},</if>
            <if test="rate != null">rate = #{rate,jdbcType=DECIMAL},</if>
            <if test="taxRate != null">tax_rate = #{taxRate,jdbcType=DECIMAL},</if>
            <if test="orderNum != null">order_num = #{orderNum,jdbcType=INTEGER},</if>
            <if test="status != null and status != ''">status = #{status,jdbcType=VARCHAR},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy,jdbcType=VARCHAR},</if>
            <if test="createId != null">create_id = #{createId,jdbcType=BIGINT},</if>
            <if test="createTime != null">create_time = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy,jdbcType=VARCHAR},</if>
            <if test="updateId != null">update_id = #{updateId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateCwProjectFeeInfo1">
        update oa_project_deploy_cw_project_fee_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="oaProjectDeployId != null">oa_project_deploy_id = #{oaProjectDeployId,jdbcType=BIGINT},</if>
            <if test="companyId == null">
            company_id = NULL,
            account_number = NULL,
            bank_of_deposit = NULL,
            </if>
            <if test="companyId != null">
            company_id = #{companyId,jdbcType=BIGINT},
            <if test="accountNumber != null and accountNumber != ''">account_number = #{accountNumber,jdbcType=VARCHAR},</if>
            <if test="bankOfDeposit != null and bankOfDeposit != ''">bank_of_deposit = #{bankOfDeposit,jdbcType=VARCHAR},</if>
            </if>
            <if test="rate != null">rate = #{rate,jdbcType=DECIMAL},</if>
            <if test="taxRate != null">tax_rate = #{taxRate,jdbcType=DECIMAL},</if>
            <if test="orderNum != null">order_num = #{orderNum,jdbcType=INTEGER},</if>
            <if test="status != null and status != ''">status = #{status,jdbcType=VARCHAR},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy,jdbcType=VARCHAR},</if>
            <if test="createId != null">create_id = #{createId,jdbcType=BIGINT},</if>
            <if test="createTime != null">create_time = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy,jdbcType=VARCHAR},</if>
            <if test="updateId != null">update_id = #{updateId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCwProjectFeeInfoById">
        delete from oa_project_deploy_cw_project_fee_info where id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteCwProjectFeeInfoByIds">
        delete from oa_project_deploy_cw_project_fee_info where where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectOaProjectDeployListByProjectNameSet" resultMap="OaProjectDeployResult">
        <include refid="selectOaProjectDeployVo"/>
        <where>
<!--            is_enable = 'Y'-->
            <if test="collection != null and collection.size() != 0">
            and project_name in
                <foreach collection="collection" item="projectName" open="(" separator="," close=")">
                    #{projectName,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectOaprojectDeployByProjectName" resultMap="OaProjectDeployResult">
        <include refid="selectOaProjectDeployVo"/>
        WHERE project_name=#{projectName,jdbcType=VARCHAR}
    </select>
</mapper>
