<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaTraderMapper">
    
    <resultMap type="OaTrader" id="OaTraderResult">
        <result property="id"    column="id"    />
        <result property="companyNo"    column="company_no"    />
        <result property="traderType"    column="trader_type"    />
        <result property="type"    column="type"    />
        <result property="userName"    column="user_name"    />
        <result property="bankOfDeposit"    column="bank_of_deposit"    />
        <result property="accountNumber"    column="account_number"    />
        <result property="abbreviation"    column="abbreviation"    />
        <result property="accountId"    column="account_id"    />
        <result property="isAccount"    column="is_account"    />
        <result property="isEnable"    column="is_enable"    />
        <result property="endUpdateTime"    column="end_update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="addNotApprove"    column="add_not_approve"    />
    </resultMap>


    <sql id="selectOaTraderVo">
        select id, company_no, trader_type, type, user_name, bank_of_deposit, account_number, abbreviation, account_id, is_account, is_enable, end_update_time, create_by, create_time, update_by, update_time, add_not_approve from oa_trader
    </sql>


    <select id="selectOaTraderList" parameterType="OaTrader" resultMap="OaTraderResult">
        <include refid="selectOaTraderVo"/>
        <where>  
            <if test="companyNo != null"> and company_no = #{companyNo,jdbcType=BIGINT}</if>
            <if test="traderType != null  and traderType != ''"> and trader_type = #{traderType}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="bankOfDeposit != null  and bankOfDeposit != ''"> and bank_of_deposit = #{bankOfDeposit}</if>
            <if test="accountNumber != null  and accountNumber != ''"> and account_number  like concat('%', #{accountNumber}, '%')</if>
            <if test="abbreviation != null  and abbreviation != ''"> and abbreviation = #{abbreviation}</if>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="isAccount != null  and isAccount != ''"> and is_account = #{isAccount}</if>
            <if test="isEnable != null  and isEnable != ''"> and is_enable = #{isEnable}</if>
            <if test="endUpdateTime != null "> and end_update_time = #{endUpdateTime}</if>
            <if test="addNotApprove != null and addNotApprove != ''"> and add_not_approve = #{addNotApprove,jdbcType=VARCHAR}</if>
            AND del_flag = '0'
        </where>

        ORDER BY create_time DESC
    </select>



    <select id="queryDataByParams"  parameterType="OaTrader" resultType="map">
        select id, company_no AS companyNo, trader_type as traderType, type, user_name as userName, bank_of_deposit bankOfDeposit, account_number accountNumber, abbreviation, account_id accountId, is_account isAccount, is_enable as isEnable,
               end_update_time endUpdateTime, create_by createBy, create_time createTime, update_by updateBy, update_time updateTime ,
        IFNULL( CONCAT(user_name,'（',abbreviation,'）'),CONCAT(user_name,'（）') )as label
        from oa_trader
        <where>
            <if test="companyNo != null"> and company_no = #{companyNo,jdbcType=BIGINT}</if>
            <if test="traderType != null  and traderType != ''"> and trader_type = #{traderType}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="bankOfDeposit != null  and bankOfDeposit != ''"> and bank_of_deposit = #{bankOfDeposit}</if>
            <if test="accountNumber != null  and accountNumber != ''"> and account_number = #{accountNumber}</if>
            <if test="abbreviation != null  and abbreviation != ''"> and abbreviation = #{abbreviation}</if>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="isAccount != null  and isAccount != ''"> and is_account = #{isAccount}</if>
            <if test="isEnable != null  and isEnable != ''"> and is_enable = #{isEnable}</if>
            <if test="endUpdateTime != null "> and end_update_time = #{endUpdateTime}</if>
            AND del_flag = '0'
        </where>
    </select>



    <select id="selectOaTraderById" parameterType="Long" resultMap="OaTraderResult">
        <include refid="selectOaTraderVo"/>
        where id = #{id}
    </select>
    <select id="getDataByAccNum" resultMap="OaTraderResult">
        <include refid="selectOaTraderVo"/>
        where is_enable = 'Y'  and account_number = #{num} and (trader_type = #{traderType} or trader_type = '9' )  limit 0,1
    </select>

    <insert id="insertOaTrader" parameterType="OaTrader" useGeneratedKeys="true" keyProperty="id">
        insert into oa_trader
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyNo != null">company_no,</if>
            <if test="traderType != null">trader_type,</if>
            <if test="type != null">type,</if>
            <if test="userName != null">user_name,</if>
            <if test="bankOfDeposit != null">bank_of_deposit,</if>
            <if test="accountNumber != null">account_number,</if>
            <if test="abbreviation != null">abbreviation,</if>
            <if test="accountId != null">account_id,</if>
            <if test="isAccount != null">is_account,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="endUpdateTime != null">end_update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="addNotApprove != null and addNotApprove != ''">add_not_approve,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyNo != null">#{companyNo,jdbcType=BIGINT},</if>
            <if test="traderType != null">#{traderType},</if>
            <if test="type != null">#{type},</if>
            <if test="userName != null">#{userName},</if>
            <if test="bankOfDeposit != null">#{bankOfDeposit},</if>
            <if test="accountNumber != null">#{accountNumber},</if>
            <if test="abbreviation != null">#{abbreviation},</if>
            <if test="accountId != null">#{accountId},</if>
            <if test="isAccount != null">#{isAccount},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="endUpdateTime != null">#{endUpdateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="addNotApprove != null and addNotApprove != ''">#{addNotApprove,jdbcType=VARCHAR},</if>
        </trim>
    </insert>


    <update id="updateOaTrader" parameterType="OaTrader">
        update oa_trader
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyNo != null">company_no = #{companyNo},</if>
            <if test="traderType != null">trader_type = #{traderType},</if>
            <if test="type != null">type = #{type},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="bankOfDeposit != null">bank_of_deposit = #{bankOfDeposit},</if>
            <if test="accountNumber != null">account_number = #{accountNumber},</if>
            <if test="abbreviation != null">abbreviation = #{abbreviation},</if>
            <if test="accountId != null">account_id = #{accountId},</if>
            <if test="isAccount != null">is_account = #{isAccount},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="endUpdateTime != null">end_update_time = #{endUpdateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="addNotApprove != null and addNotApprove != ''">add_not_approve = #{addNotApprove,jdbcType=VARCHAR},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag,jdbcType=VARCHAR},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteOaTraderById" parameterType="Long">
        delete from oa_trader where id = #{id}
    </delete>

    <delete id="deleteOaTraderByIds" parameterType="String">
        delete from oa_trader where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectOaTraderListByOaTraderAndFilterOaApplyIdAndCompanyIdList"  resultMap="OaTraderResult">
        <include refid="selectOaTraderVo"/>
        <where>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND company_no IN
                <foreach collection="companyIdList" item="companyNo" open="(" separator="," close=")">
                    #{companyNo,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="filterOaApplyId != null and filterOaApplyId.size() != 0">
                AND id IN
                <foreach collection="filterOaApplyId" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="oaTrader.companyNo != null  and oaTrader.companyNo != ''"> and company_no = #{oaTrader.companyNo}</if>
            <if test="oaTrader.traderType != null and  oaTrader.traderType != ''"> and trader_type = #{oaTrader.traderType}</if>
            <if test="oaTrader.type != null  and oaTrader.type != ''"> and type = #{oaTrader.type}</if>
            <if test="oaTrader.userName != null  and oaTrader.userName != ''"> and user_name like concat('%', #{oaTrader.userName}, '%')</if>
            <if test="oaTrader.bankOfDeposit != null  and oaTrader.bankOfDeposit != ''"> and bank_of_deposit = #{oaTrader.bankOfDeposit}</if>
            <if test="oaTrader.accountNumber != null  and oaTrader.accountNumber != ''"> and account_number  like concat('%', #{oaTrader.accountNumber}, '%')</if>
            <if test="oaTrader.abbreviation != null  and oaTrader.abbreviation != ''"> and abbreviation = #{oaTrader.abbreviation}</if>
            <if test="oaTrader.accountId != null "> and account_id = #{oaTrader.accountId}</if>
            <if test="oaTrader.isAccount != null  and oaTrader.isAccount != ''"> and is_account = #{oaTrader.isAccount}</if>
            <if test="oaTrader.isEnable != null  and oaTrader.isEnable != ''"> and is_enable = #{oaTrader.isEnable}</if>
            <if test="oaTrader.endUpdateTime != null "> and end_update_time = #{oaTrader.endUpdateTime}</if>
        AND del_flag = '0'
        </where>
    </select>

    <select id="selectOaTraderListByIds"  resultMap="OaTraderResult">
        <include refid="selectOaTraderVo"/>
        WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectOaTraderListTotal" resultType="java.lang.Long">
        SELECT COUNT(*) FROM oa_trader
        <where>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND company_no IN
                <foreach collection="companyIdList" item="companyNo" open="(" separator="," close=")">
                    #{companyNo,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="oaTrader.companyNo != null  and oaTrader.companyNo != ''"> and company_no = #{oaTrader.companyNo}</if>
            <if test="oaTrader.traderType != null and  oaTrader.traderType != ''"> and trader_type = #{oaTrader.traderType}</if>
            <if test="oaTrader.type != null  and oaTrader.type != ''"> and type = #{oaTrader.type}</if>
            <if test="oaTrader.userName != null  and oaTrader.userName != ''"> and user_name like concat('%', #{oaTrader.userName}, '%')</if>
            <if test="oaTrader.bankOfDeposit != null  and oaTrader.bankOfDeposit != ''"> and bank_of_deposit = #{oaTrader.bankOfDeposit}</if>
            <if test="oaTrader.accountNumber != null  and oaTrader.accountNumber != ''"> and account_number  like concat('%', #{oaTrader.accountNumber}, '%')</if>
            <if test="oaTrader.abbreviation != null  and oaTrader.abbreviation != ''"> and abbreviation = #{oaTrader.abbreviation}</if>
            <if test="oaTrader.accountId != null "> and account_id = #{oaTrader.accountId}</if>
            <if test="oaTrader.isAccount != null  and oaTrader.isAccount != ''"> and is_account = #{oaTrader.isAccount}</if>
            <if test="oaTrader.isEnable != null  and oaTrader.isEnable != ''"> and is_enable = #{oaTrader.isEnable}</if>
            <if test="oaTrader.endUpdateTime != null "> and end_update_time = #{oaTrader.endUpdateTime}</if>
        </where>
    </select>

    <select id="selectOaTraderListTotalByOaTraderAndCompanyIdListAndFilterOaApplyIdList" resultType="java.lang.Long">
        SELECT COUNT(*) FROM oa_trader
        <where>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND company_no IN
                <foreach collection="companyIdList" item="companyNo" open="(" separator="," close=")">
                    #{companyNo,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="filterOaApplyId != null and filterOaApplyId.size() != 0">
                AND id IN
                <foreach collection="filterOaApplyId" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="oaTrader.companyNo != null  and oaTrader.companyNo != ''"> and company_no = #{oaTrader.companyNo}</if>
           <if test="oaTrader.traderType != null and  oaTrader.traderType != ''"> and trader_type = #{oaTrader.traderType}</if>
            <if test="oaTrader.type != null  and oaTrader.type != ''"> and type = #{oaTrader.type}</if>
            <if test="oaTrader.userName != null  and oaTrader.userName != ''"> and user_name like concat('%', #{oaTrader.userName}, '%')</if>
            <if test="oaTrader.bankOfDeposit != null  and oaTrader.bankOfDeposit != ''"> and bank_of_deposit = #{oaTrader.bankOfDeposit}</if>
            <if test="oaTrader.accountNumber != null  and oaTrader.accountNumber != ''"> and account_number  like concat('%', #{oaTrader.accountNumber}, '%')</if>
            <if test="oaTrader.abbreviation != null  and oaTrader.abbreviation != ''"> and abbreviation = #{oaTrader.abbreviation}</if>
            <if test="oaTrader.accountId != null "> and account_id = #{oaTrader.accountId}</if>
            <if test="oaTrader.isAccount != null  and oaTrader.isAccount != ''"> and is_account = #{oaTrader.isAccount}</if>
            <if test="oaTrader.isEnable != null  and oaTrader.isEnable != ''"> and is_enable = #{oaTrader.isEnable}</if>
            <if test="oaTrader.endUpdateTime != null "> and end_update_time = #{oaTrader.endUpdateTime}</if>
        </where>
    </select>

    <select id="selectOaTraderListByIdList" resultMap="OaTraderResult">
        <include refid="selectOaTraderVo"/>
        WHERE id IN
        <foreach item="id" collection="oaTraderIdList" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectOaTraderListByOaProjectDeployId" resultMap="OaTraderResult">
        <include refid="selectOaTraderVo"/>
        WHERE id IN
        (
        SELECT cpc.oa_trader_id FROM cw_project cp LEFT JOIN cw_project_cust cpc ON cp.id=cpc.project_id WHERE cp.project_flag='0' AND cp.status='0'
        AND cp.oa_project_deploy_id IN
        <foreach collection="list" item="oaProjectDeployId" open="(" separator="," close=")">
            #{oaProjectDeployId,jdbcType=BIGINT}
        </foreach>
        AND cpc.status='0' AND cpc.replace_flag='0'
        UNION ALL
        SELECT new_oa_trader_id FROM cw_project_replace_fee_company_info WHERE oa_trader_id IN
        (SELECT cpc.oa_trader_id FROM cw_project cp LEFT JOIN cw_project_cust cpc ON cp.id=cpc.project_id WHERE cp.project_flag='0' AND cp.status='0'
        AND cp.oa_project_deploy_id IN
        <foreach collection="list" item="oaProjectDeployId" open="(" separator="," close=")">
            #{oaProjectDeployId,jdbcType=BIGINT}
        </foreach>
        AND cpc.status='0' AND cpc.replace_flag='1')
        AND project_id IN
        (
        SELECT id FROM cw_project WHERE oa_project_deploy_id IN
        <foreach collection="list" item="oaProjectDeployId" open="(" separator="," close=")">
            #{oaProjectDeployId,jdbcType=BIGINT}
        </foreach>
        )
        AND status='0'
        )
        AND is_enable='Y'
        ORDER BY create_time DESC
    </select>

    <select id="selectOaTraderListByCompanyIdList" resultType="org.ruoyi.core.oasystem.domain.vo.OaTraderVo">
        SELECT
            ot.id,
            ot.company_no AS companyNo,
            sc.company_name AS companyName,
            ot.trader_type AS traderType,
            ot.type,
            ot.user_name AS userName,
            ot.bank_of_deposit AS bankOfDeposit,
            ot.account_number AS accountNumber,
            ot.abbreviation,
            ot.account_id AS accountId,
            fas.company_name AS accountName,
            ot.is_account AS isAccount,
            ot.is_enable AS isEnable,
            ot.end_update_time AS endUpdateTime,
            ot.add_not_approve AS addNotApprove
        FROM
            oa_trader ot
            LEFT JOIN financial_account_sets fas ON ot.account_id = fas.id
            LEFT JOIN sys_company sc ON ot.company_no = sc.id
        <where>
        AND ot.del_flag = '0'
        <if test="oaTrader.addNotApprove != null and oaTrader.addNotApprove != ''">
        AND ot.add_not_approve = #{oaTrader.addNotApprove}
        </if>
        <if test="oaTrader.userName != null and oaTrader.userName != ''">
        AND ot.user_name LIKE CONCAT('%', #{oaTrader.userName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="oaTrader.accountNumber != null and oaTrader.accountNumber != ''">
        AND ot.account_number LIKE CONCAT('%', #{oaTrader.accountNumber,jdbcType=VARCHAR}, '%')
        </if>
        <if test="oaTrader.bankOfDeposit != null and oaTrader.bankOfDeposit != ''">
        AND ot.bank_of_deposit LIKE CONCAT('%', #{oaTrader.bankOfDeposit,jdbcType=VARCHAR}, '%')
        </if>
        <if test="oaTrader.accountId != null">
        AND ot.account_id = #{oaTrader.accountId,jdbcType=BIGINT}
        </if>
        <if test="oaTrader.traderTypeList != null and oaTrader.traderTypeList.size() != 0">
        AND ot.trader_type IN
        <foreach collection="oaTrader.traderTypeList" item="traderTypeItem" open="(" separator="," close=")">
         #{traderTypeItem,jdbcType=VARCHAR}
        </foreach>
        </if>
        <if test="oaTrader.traderTypeList == null or oaTrader.traderTypeList.size() == 0">
        <if test="oaTrader.traderType != null and oaTrader.traderType != ''">
        AND ot.trader_type = #{oaTrader.traderType,jdbcType=VARCHAR}
        </if>
        </if>
        <if test="oaTrader.type != null and oaTrader.type != ''">
        AND ot.type = #{oaTrader.type,jdbcType=VARCHAR}
        </if>
        <if test="companyIdList != null and companyIdList.size() != 0">
        AND ot.company_no IN
        <foreach collection="companyIdList" item="companyId" open="(" separator="," close=")">
        #{companyId,jdbcType=BIGINT}
        </foreach>
        </if>
        <if test="oaTrader.isEnable != null and oaTrader.isEnable != ''">
        AND ot.is_enable = #{oaTrader.isEnable,jdbcType=VARCHAR}
        </if>
        </where>
    </select>

    <select id="selectOaTrader" resultMap="OaTraderResult">
        <include refid="selectOaTraderVo"/>
        <where>
            <if test="companyNo != null"> and company_no = #{companyNo,jdbcType=BIGINT}</if>
            <if test="traderType != null  and traderType != ''"> and trader_type = #{traderType}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="userName != null  and userName != ''"> and user_name = #{userName}</if>
            <if test="bankOfDeposit != null  and bankOfDeposit != ''"> and bank_of_deposit = #{bankOfDeposit}</if>
            <if test="accountNumber != null  and accountNumber != ''"> and account_number = #{accountNumber}</if>
            <if test="abbreviation != null  and abbreviation != ''"> and abbreviation = #{abbreviation}</if>
            <if test="accountId != null "> and account_id = #{accountId}</if>
            <if test="isAccount != null  and isAccount != ''"> and is_account = #{isAccount}</if>
            <if test="isEnable != null  and isEnable != ''"> and is_enable = #{isEnable}</if>
            <if test="endUpdateTime != null "> and end_update_time = #{endUpdateTime}</if>
            <if test="addNotApprove != null and addNotApprove != ''"> and add_not_approve = #{addNotApprove,jdbcType=VARCHAR}</if>
            AND del_flag = '0'
            <if test="id != null">AND id != #{id,jdbcType=BIGINT}</if>
        </where>
    </select>

<!--    <select id="selectOaTraderListByCompanyIdListByOaCheck" resultType="org.ruoyi.core.oasystem.domain.vo.OaTraderVo">-->
<!--        SELECT-->
<!--            ot.id,-->
<!--            ot.company_no AS companyNo,-->
<!--            sc.company_name AS companyName,-->
<!--            ot.trader_type AS traderType,-->
<!--            ot.type,-->
<!--            ot.user_name AS userName,-->
<!--            ot.bank_of_deposit AS bankOfDeposit,-->
<!--            ot.account_number AS accountNumber,-->
<!--            ot.abbreviation,-->
<!--            ot.account_id AS accountId,-->
<!--            fas.company_name AS accountName,-->
<!--            ot.is_account AS isAccount,-->
<!--            ot.is_enable AS isEnable,-->
<!--            ot.end_update_time AS endUpdateTime,-->
<!--            ot.add_not_approve AS addNotApprove-->
<!--        FROM-->
<!--            oa_trader ot-->
<!--            LEFT JOIN financial_account_sets fas ON ot.account_id = fas.id-->
<!--            LEFT JOIN sys_company sc ON ot.company_no = sc.id-->
<!--        WHERE-->
<!--        ot.del_flag = '0'-->
<!--        <if test="companyIdList != null and companyIdList.size() != 0">-->
<!--        AND ot.company_no IN-->
<!--        <foreach collection="companyIdList" item="companyId" open="(" separator="," close=")">-->
<!--        #{companyId,jdbcType=BIGINT}-->
<!--        </foreach>-->
<!--        </if>-->
<!--    </select>-->
</mapper>