<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.OASettingUp.mapper.NoaAuthorityMapper">

    <resultMap type="NoaAuthority" id="NoaAuthorityResult">
        <result property="remindId"    column="remind_id"    />
        <result property="flowId"    column="flow_id"    />
        <result property="nodeId"    column="node_id"    />
        <result property="authorityType"    column="authority_type"    />
        <result property="authorityId"    column="authority_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="selectNoaAuthorityVo">
        select flow_id,remind_id, node_id, authority_type, authority_id, create_time, create_by from noa_authority
    </sql>

    <select id="selectNoaAuthorityList" parameterType="NoaAuthority" resultMap="NoaAuthorityResult">
        <include refid="selectNoaAuthorityVo"/>
        <where>
            <if test="flowId != null  and flowId != ''"> and flow_id = #{flowId}</if>
            <if test="nodeId != null  and nodeId != ''"> and node_id = #{nodeId}</if>
            <if test="authorityType != null "> and authority_type = #{authorityType}</if>
            <if test="authorityId != null "> and authority_id = #{authorityId}</if>
        </where>
    </select>

    <select id="selectNoaAuthorityByFlowId" parameterType="String" resultMap="NoaAuthorityResult">
        <include refid="selectNoaAuthorityVo"/>
        where flow_id = #{flowId}
    </select>

    <select id="selectFlow" resultType="org.ruoyi.core.OASettingUp.domain.NoaFlow">
        SELECT
        tem.template_name flowName,
        tem.form_id formId,
        tem.flow_full_id flowFullId,
        tem.flow_id flowId,
        class.ancestors ancestors,
        class.name className
        FROM
        oa_process_template tem
        LEFT JOIN oa_process_classification class ON class.id = tem.classification_id
        <where>
            <if test="flowName != null  and flowName != ''"> and tem.template_name like concat('%', #{flowName}, '%')</if>
        </where>
    </select>

    <select id="selectNoaAuthorityListToRemind" resultType="org.ruoyi.core.OASettingUp.domain.NoaAuthority">
        SELECT
        noa.flow_id flowId,
        noa.node_id nodeId,
        noa.authority_type authorityType,
        noa.authority_id authorityId,
        rem.id workFlowId,
        CONCAT(de.dept_name,'(',un.company_short_name,')') authorityName
        FROM
        noa_authority noa
        left join sys_dept de on noa.authority_id = de.dept_id
        left join sys_company un on de.unit_id = un.id and un.is_inside = '1'
        left join noa_workflow_remind rem on noa.remind_id = rem.id
        WHERE noa.authority_type = 0 AND
        noa.flow_id IN (
        <foreach item="workFlowReminds" collection="workFlowReminds" index="index" separator=",">
            #{workFlowReminds.flowId}
        </foreach>)
        AND noa.node_id IN (
        <foreach item="workFlowReminds" collection="workFlowReminds" index="index" separator=",">
            #{workFlowReminds.nodeId}
        </foreach>)
        union all
        SELECT
        noa1.flow_id flowId,
        noa1.node_id nodeId,
        noa1.authority_type authorityType,
        noa1.authority_id authorityId,
        rem1.id workFlowId,
        po.post_name authorityName
        FROM
        noa_authority noa1
        left join sys_post po on noa1.authority_id = po.post_id
        left join noa_workflow_remind rem1 on noa1.remind_id = rem1.id
        WHERE noa1.authority_type = 1 AND
        noa1.flow_id IN (
        <foreach item="workFlowReminds" collection="workFlowReminds" index="index" separator=",">
            #{workFlowReminds.flowId}
        </foreach>)
        AND noa1.node_id IN (
        <foreach item="workFlowReminds" collection="workFlowReminds" index="index" separator=",">
            #{workFlowReminds.nodeId}
        </foreach>)
        union all
        SELECT
        noa2.flow_id flowId,
        noa2.node_id nodeId,
        noa2.authority_type authorityType,
        noa2.authority_id authorityId,
        rem2.id workFlowId,
        us.nick_name authorityName
        FROM
        noa_authority noa2
        left join sys_user us on noa2.authority_id = us.user_id
        left join noa_workflow_remind rem2 on noa2.remind_id = rem2.id
        WHERE noa2.authority_type = 2 AND
        noa2.flow_id IN (
        <foreach item="workFlowReminds" collection="workFlowReminds" index="index" separator=",">
            #{workFlowReminds.flowId}
        </foreach>)
        AND noa2.node_id IN (
        <foreach item="workFlowReminds" collection="workFlowReminds" index="index" separator=",">
            #{workFlowReminds.nodeId}
        </foreach>)

    </select>

    <select id="selectNoaAuthorityByRemind" resultType="org.ruoyi.core.OASettingUp.domain.NoaAuthority">
        SELECT
        noa.flow_id flowId,
        noa.node_id nodeId,
        noa.authority_type authorityType,
        noa.authority_id authorityId,
        CONCAT(de.dept_name,'(',un.company_short_name,')') authorityName
        FROM
        noa_authority noa
        left join sys_dept de on noa.authority_id = de.dept_id
        left join sys_company un on de.unit_id = un.id and un.is_inside = '1'
        WHERE noa.authority_type = 0 AND
        noa.remind_id = #{id}
        union all
        SELECT
        noa1.flow_id flowId,
        noa1.node_id nodeId,
        noa1.authority_type authorityType,
        noa1.authority_id authorityId,
        po.post_name authorityName
        FROM
        noa_authority noa1
        left join sys_post po on noa1.authority_id = po.post_id
        WHERE noa1.authority_type = 1 AND
        noa1.remind_id = #{id}
        union all
        SELECT
        noa2.flow_id flowId,
        noa2.node_id nodeId,
        noa2.authority_type authorityType,
        noa2.authority_id authorityId,
        us.nick_name authorityName
        FROM
        noa_authority noa2
        left join sys_user us on noa2.authority_id = us.user_id
        WHERE noa2.authority_type = 2 AND
        noa2.remind_id = #{id}
    </select>

    <select id="selectParentClass" resultType="org.ruoyi.core.OASettingUp.domain.NoaFlow">
        SELECT
            NAME parentName,
            id parentId
        FROM
            oa_process_classification
        WHERE
            id IN
        <foreach item="data" collection="noaFlows" open="(" separator="," close=")">
            #{data.parentId}
        </foreach>
    </select>

    <insert id="insertNoaAuthority" parameterType="NoaAuthority">
        insert into noa_authority
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flowId != null and flowId != ''">flow_id,</if>
            <if test="nodeId != null and nodeId != ''">node_id,</if>
            <if test="authorityType != null">authority_type,</if>
            <if test="authorityId != null">authority_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flowId != null and flowId != ''">#{flowId},</if>
            <if test="nodeId != null and nodeId != ''">#{nodeId},</if>
            <if test="authorityType != null">#{authorityType},</if>
            <if test="authorityId != null">#{authorityId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
         </trim>
    </insert>

    <insert id="insertNoaAuthoritys">
        INSERT INTO noa_authority ( flow_id,remind_id, node_id, authority_type, authority_id, create_time, create_by )
        VALUES
        <foreach item="authorits" collection="authorits" index="index" separator=",">
            (#{authorits.flowId},#{authorits.remindId},#{authorits.nodeId},#{authorits.authorityType},#{authorits.authorityId}
            ,#{authorits.createTime},#{authorits.createBy})
        </foreach>
    </insert>

    <update id="updateNoaAuthority" parameterType="NoaAuthority">
        update noa_authority
        <trim prefix="SET" suffixOverrides=",">
            <if test="nodeId != null and nodeId != ''">node_id = #{nodeId},</if>
            <if test="authorityType != null">authority_type = #{authorityType},</if>
            <if test="authorityId != null">authority_id = #{authorityId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
        </trim>
        where flow_id = #{flowId}
    </update>

    <delete id="deleteNoaAuthorityByFlowId" parameterType="String">
        delete from noa_authority where flow_id = #{flowId}
    </delete>

    <delete id="deleteNoaAuthorityByFlowIds" parameterType="String">
        delete from noa_authority where flow_id in
        <foreach item="flowId" collection="array" open="(" separator="," close=")">
            #{flowId}
        </foreach>
    </delete>

    <delete id="deleteAuthorityById">
        delete from noa_authority where remind_id = #{remindId}
    </delete>

</mapper>
