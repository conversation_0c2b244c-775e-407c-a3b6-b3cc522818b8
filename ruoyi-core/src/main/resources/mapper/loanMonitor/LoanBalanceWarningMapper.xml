<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.loanMonitor.mapper.LoanBalanceWarningMapper">

    <resultMap type="LoanBalanceWarning" id="LoanBalanceWarningResult">
        <result property="id"    column="id"    />
        <result property="projectCode"    column="project_code"    />
        <result property="productCode"    column="product_code"    />
        <result property="loanBalance"    column="loan_balance"    />
        <result property="marginRate"    column="margin_rate"    />
        <result property="marginAmount"    column="margin_amount"    />
        <result property="ratioMin"    column="ratio_min"    />
        <result property="ratioMax"    column="ratio_max"    />
        <result property="warningFlag"    column="warning_flag"    />
        <result property="remark"    column="remark"    />
        <result property="updateTime"    column="update_time"    />
        <result property="version"    column="version"    />
    </resultMap>

    <sql id="selectLoanBalanceWarningVo">
        select id, project_code, product_code, loan_balance, margin_rate, margin_amount, ratio_min, ratio_max, warning_flag, remark, update_time, version from loan_balance_warning
    </sql>

    <select id="selectMarginMonitoringList" parameterType="LoanBalanceWarning" resultMap="LoanBalanceWarningResult">
        <include refid="selectLoanBalanceWarningVo"/>
        <where>
            <if test="projectCode != null"> and project_code = #{projectCode}</if>
            <if test="productCode != null  and productCode != ''"> and product_code = #{productCode}</if>
            <if test="loanBalance != null "> and loan_balance = #{loanBalance}</if>
            <if test="marginRate != null "> and margin_rate = #{marginRate}</if>
            <if test="marginAmount != null "> and margin_amount = #{marginAmount}</if>
            <if test="ratioMin != null "> and ratio_min = #{ratioMin}</if>
            <if test="ratioMax != null "> and ratio_max = #{ratioMax}</if>
            <if test="warningFlag != null  and warningFlag != ''"> and warning_flag = #{warningFlag}</if>
            <if test="version != null "> and version = #{version}</if>
        </where>
    </select>

    <select id="selectLoanBalanceWarningById" parameterType="Integer" resultMap="LoanBalanceWarningResult">
        <include refid="selectLoanBalanceWarningVo"/>
        where project_code = #{projectCode}
    </select>

    <insert id="insertLoanBalanceWarning" parameterType="LoanBalanceWarning" useGeneratedKeys="true" keyProperty="id">
        insert into loan_balance_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectCode != null">project_code,</if>
            <if test="productCode != null and productCode != ''">product_code,</if>
            <if test="loanBalance != null">loan_balance,</if>
            <if test="marginRate != null">margin_rate,</if>
            <if test="marginAmount != null">margin_amount,</if>
            <if test="ratioMin != null">ratio_min,</if>
            <if test="ratioMax != null">ratio_max,</if>
            <if test="currentThreshold != null">current_threshold,</if>
            <if test="warningFlag != null">warning_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="version != null">version,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectCode != null">#{projectCode},</if>
            <if test="productCode != null and productCode != ''">#{productCode},</if>
            <if test="loanBalance != null">#{loanBalance},</if>
            <if test="marginRate != null">#{marginRate},</if>
            <if test="marginAmount != null">#{marginAmount},</if>
            <if test="ratioMin != null">#{ratioMin},</if>
            <if test="ratioMax != null">#{ratioMax},</if>
            <if test="currentThreshold != null">#{current_threshold},</if>
            <if test="warningFlag != null">#{warningFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="version != null">#{version},</if>
         </trim>
    </insert>

    <update id="updateLoanBalanceWarning" parameterType="LoanBalanceWarning">
        update loan_balance_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectCode != null">project_code = #{projectCode},</if>
            <if test="productCode != null and productCode != ''">product_code = #{productCode},</if>
            <if test="loanBalance != null">loan_balance = #{loanBalance},</if>
            <if test="marginRate != null">margin_rate = #{marginRate},</if>
            <if test="marginAmount != null">margin_amount = #{marginAmount},</if>
            <if test="ratioMin != null">ratio_min = #{ratioMin},</if>
            <if test="ratioMax != null">ratio_max = #{ratioMax},</if>
            <if test="currentThreshold != null">current_threshold = #{currentThreshold},</if>
            <if test="warningFlag != null">warning_flag = #{warningFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="version != null">version = #{version},</if>
        </trim>
        where project_code = #{projectCode}
    </update>

    <select id="selectBalanceLoanByProjectCode" resultMap="LoanBalanceWarningResult">
        select
            COALESCE(loan_balance, 0.00) as loan_balance,
            COALESCE(margin_rate, 0.00) as margin_rate,
            COALESCE(ratio_max, 0.00) as ratio_max,
            COALESCE(current_threshold, 0.00) as current_threshold
        from loan_balance_warning
        where project_code = #{projectCode};
    </select>

    <select id="selectUserQYWXInfoByUserName" resultType="org.ruoyi.core.qiyeVX.domain.VxUser">
        select * from sys_user su
        left join vx_user vu on su.user_id = vu.user_id
        where su.user_name = #{userName} and su.del_flag = 0 and su.status = '0'
    </select>
</mapper>