<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cwbbjy.mapper.FinancialStatementVerifyMapper">

    <resultMap type="FinancialStatementVerify" id="FinancialStatementVerifyResult">
        <result property="id" column="id"/>
        <result property="guaranteeCompanyId" column="guarantee_company_id"/>
        <result property="guaranteeCompanyName" column="guarantee_company_name"/>
        <result property="reportFormsName" column="report_forms_name"/>
        <result property="reportType" column="report_type"/>
        <result property="status" column="status"/>
        <result property="link" column="link"/>
        <result property="remark" column="remark"/>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
		<collection  property="ruleList"   javaType="java.util.List" select="selectFinancialStatementVerifyListById" column="id"/>
    </resultMap>
    
    
    <resultMap id="financialStatementVerifyRuleResult" type="FinancialStatementVerifyRule">
		<id property="pid" column="pid"/>
		<result property="ruleValue" column="rule_value"/>
	</resultMap>
	
	
	
	<resultMap type="FinancialStatementVerify" id="FinancialStatementVerifyResult2">
        <result property="id" column="id"/>
        <result property="guaranteeCompanyId" column="guarantee_company_id"/>
        <result property="guaranteeCompanyName" column="guarantee_company_name"/>
        <result property="reportFormsName" column="report_forms_name"/>
        <result property="reportType" column="report_type"/>
        <result property="remark" column="remark"/>
    </resultMap>
    
    

    <sql id="selectFinancialStatementVerifyVo">
        select id, guarantee_company_id, guarantee_company_name, report_forms_name,report_type, status, link, remark
        from financial_statement_verify
    </sql>

    <select id="selectFinancialStatementVerifyList"   resultMap="FinancialStatementVerifyResult">
        <include refid="selectFinancialStatementVerifyVo"/>
        <where>
        	<if test="info.id != null  and info.id != ''">and id =
                #{info.id}
            </if>
            <if test="info.guaranteeCompanyId != null  and info.guaranteeCompanyId != ''">and guarantee_company_id =
                #{info.guaranteeCompanyId}
            </if>
            <if test="info.reportType != null  and info.reportType != ''">and report_type =
                #{info.reportType}
            </if>
            <if test="info.guaranteeCompanyName != null  and info.guaranteeCompanyName != ''">and guarantee_company_name like
                concat('%', #{info.guaranteeCompanyName}, '%')
            </if>
            <if test="info.reportFormsName != null  and info.reportFormsName != ''">and report_forms_name like concat('%',
                #{info.reportFormsName}, '%')
            </if>
            <if test="info.status != null  and info.status != ''">and status = #{info.status}</if>
            <if test="info.link != null  and info.link != ''">and link = #{info.link}</if>
            <if test="userId != null  and userId != '' and userId != 1 ">and guarantee_company_id in (
            select distinct cust_no from financial_statement_verify_permission vp where vp.user_id=#{userId} ) </if>
        </where>
        order by id desc
    </select>
    
    
    
    <select id="selectFinancialStatementVerifyListById"  resultMap="financialStatementVerifyRuleResult">
        select pid,rule_value from financial_statement_verify_rule where pid = #{id}
    </select>

    <select id="selectFinancialStatementVerifyById" parameterType="Long" resultMap="FinancialStatementVerifyResult">
        <include refid="selectFinancialStatementVerifyVo"/>
        where id = #{id}
    </select>


    <insert id="insertFinancialStatementVerify" parameterType="FinancialStatementVerify" useGeneratedKeys="true" keyProperty="id">
        insert into financial_statement_verify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="guaranteeCompanyId != null">guarantee_company_id,</if>
            <if test="guaranteeCompanyName != null">guarantee_company_name,</if>
            <if test="reportFormsName != null">report_forms_name,</if>
            <if test="reportType != null">report_type,</if>
            <if test="status != null">status,</if>
            <if test="link != null">link,</if>
            <if test="remark != null">remark,</if>

            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="guaranteeCompanyId != null">#{guaranteeCompanyId},</if>
            <if test="guaranteeCompanyName != null">#{guaranteeCompanyName},</if>
            <if test="reportFormsName != null">#{reportFormsName},</if>
            <if test="reportType != null">#{reportType},</if>
            <if test="status != null">#{status},</if>
            <if test="link != null">#{link},</if>
            <if test="remark != null">#{remark},</if>

            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateFinancialStatementVerify" parameterType="FinancialStatementVerify">
        update financial_statement_verify
        <trim prefix="SET" suffixOverrides=",">
            <if test="guaranteeCompanyId != null">guarantee_company_id = #{guaranteeCompanyId},</if>
            <if test="guaranteeCompanyName != null">guarantee_company_name = #{guaranteeCompanyName},</if>
            <if test="reportFormsName != null">report_forms_name = #{reportFormsName},</if>
            <if test="reportType != null">report_type = #{reportType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="link != null">link = #{link},</if>
            <if test="remark != null">remark = #{remark},</if>

            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinancialStatementVerifyById" parameterType="Long">
        delete
        from financial_statement_verify
        where id = #{id}
    </delete>

    <delete id="deleteFinancialStatementVerifyByIds" parameterType="String">
        delete from financial_statement_verify where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    
    
    
    <select id="checkFinancialStatementVerifyUnique" parameterType="FinancialStatementVerify" resultMap="FinancialStatementVerifyResult">
        <include refid="selectFinancialStatementVerifyVo"/>
        <where>
            <if test="id != null  ">and id != #{id}</if>
            <if test="guaranteeCompanyId != null  and guaranteeCompanyId != ''">and guarantee_company_id = #{guaranteeCompanyId}  </if>
            <if test="reportType != null  and reportType != ''">and report_type = #{reportType}  </if>
            <if test="reportFormsName != null  and reportFormsName != ''">and report_forms_name = #{reportFormsName}  </if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="link != null  and link != ''">and link = #{link}</if>
        </where>
    </select>
    
    
    
    
    
    
    
    <insert id="insertFinancialStatementVerifyRules" >
        insert into financial_statement_verify_rule (pid,rule_value)
        values 
        <foreach collection="ruleList" item="item" index= "index" separator="," >
        (#{id},#{item.ruleValue})
        </foreach>
    </insert>
    
    
    <delete id="deleteFinancialStatementVerifyRulesById" parameterType="Long">
        delete
        from financial_statement_verify_rule
        where pid = #{pid}
    </delete>
    
    
    
    <select id="selectCustList" parameterType="FinancialStatementVerify"
            resultMap="FinancialStatementVerifyResult2">
        select dict_value as guarantee_company_id,dict_label as guarantee_company_name
        from sys_dict_data
        <where>
            and dict_type = 'cust_no'  
            <if test="userId != null  and userId != '' and userId != 1 ">and dict_value in (
            select distinct cust_no from financial_statement_verify_permission vp where vp.user_id=#{userId} ) </if>
        </where>
        order by dict_sort
    </select>
    
    <select id="selectReportTypeList" parameterType="FinancialStatementVerify"
            resultMap="FinancialStatementVerifyResult2">
        select DISTINCT report_type from financial_statement_verify
        <where>
            <if test="info.guaranteeCompanyId != null  and info.guaranteeCompanyId != ''">
              and guarantee_company_id = #{info.guaranteeCompanyId}
            </if>
            <if test="info.guaranteeCompanyName != null  and info.guaranteeCompanyName != ''">
              and guarantee_company_name = #{info.guaranteeCompanyName}
            </if>
            <if test="info.status != null  and info.status != ''">and status = #{info.status}</if>
            <if test="userId != null  and userId != '' and userId != 1 ">and guarantee_company_id in (
            select distinct cust_no from financial_statement_verify_permission vp where vp.user_id=#{userId} ) </if>
        </where>
        order by report_type desc
    </select>
    
    
    <select id="selectReportNameList" parameterType="FinancialStatementVerify"
            resultMap="FinancialStatementVerifyResult2">
        select DISTINCT id,report_forms_name from financial_statement_verify
        <where>
            <if test="info.guaranteeCompanyId != null  and info.guaranteeCompanyId != ''">
              and guarantee_company_id = #{info.guaranteeCompanyId}
            </if>
            <if test="info.guaranteeCompanyName != null  and info.guaranteeCompanyName != ''">
              and guarantee_company_name = #{info.guaranteeCompanyName}
            </if>
            <if test="info.reportFormsName != null  and info.reportFormsName != ''">
              and report_forms_name = #{info.reportFormsName}
            </if>
            <if test="info.reportType != null  and info.reportType != ''">
              and report_type = #{info.reportType}
            </if>
            <if test="info.status != null  and info.status != ''">and status = #{info.status}</if>
            <if test="userId != null  and userId != '' and userId != 1 ">and guarantee_company_id in (
            select distinct cust_no from financial_statement_verify_permission vp where vp.user_id=#{userId} ) </if>
        </where>
        order by report_forms_name desc
    </select>
    
    
    
    
    
    <resultMap type="FinancialStatementVerifyRecord" id="FinancialStatementVerifyRecordResult">
        <result property="id"    column="id"    />
        <result property="custNo"    column="cust_no"    />
        <result property="reportType"    column="report_type"    />
        <result property="reportFormsName"    column="report_forms_name"    />
        <result property="result"    column="result"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="fileName"    column="file_name"    />
        <result property="url"    column="url"    />
    </resultMap>
    
    <sql id="selectFinancialStatementVerifyRecordVo">
        select id, cust_no, report_type, report_forms_name, result, create_time, update_time, create_by, update_by,file_name,url from financial_statement_verify_record
    </sql>

    <select id="selectFinancialStatementVerifyRecordList" resultMap="FinancialStatementVerifyRecordResult">
        <include refid="selectFinancialStatementVerifyRecordVo"/>
        <where>  
            <if test="info.custNo != null  and info.custNo != ''"> and cust_no = #{info.custNo}</if>
            <if test="info.reportType != null  and info.reportType != ''"> and report_type = #{info.reportType}</if>
            <if test="info.reportFormsName != null  and info.reportFormsName != ''"> and report_forms_name like concat('%', #{info.reportFormsName}, '%')</if>
            <if test="info.createBy != null  and info.createBy != ''"> and create_by = #{info.createBy}</if>
            <if test="userId != null  and userId != '' and userId != 1 ">and cust_no in (
            select distinct cust_no from financial_statement_verify_permission vp where vp.user_id=#{userId} ) </if>
        </where>
        order by id desc
    </select>
    
    <select id="selectFinancialStatementVerifyRecordById" parameterType="Long" resultMap="FinancialStatementVerifyRecordResult">
        <include refid="selectFinancialStatementVerifyRecordVo"/>
        where id = #{id}
    </select>
    
    
    <insert id="insertFinancialStatementVerifyRecord" parameterType="FinancialStatementVerifyRecord">
        insert into financial_statement_verify_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="custNo != null">cust_no,</if>
            <if test="reportType != null">report_type,</if>
            <if test="reportFormsName != null">report_forms_name,</if>
            <if test="result != null">result,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="fileName != null">file_name,</if>
            <if test="url != null">url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="custNo != null">#{custNo},</if>
            <if test="reportType != null">#{reportType},</if>
            <if test="reportFormsName != null">#{reportFormsName},</if>
            <if test="result != null">#{result},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="url != null">#{url},</if>
         </trim>
    </insert>
    
    
    
    
    
    
    
    <select id="selectFinancialStatementVerifyPermissionById" parameterType="java.lang.String" resultType="java.lang.Long">
        select user_id from financial_statement_verify_permission
        where cust_no = #{custNo}
    </select>
    
    
    <insert id="insertFinancialStatementVerifyPermission" >
        insert into financial_statement_verify_permission (cust_no,user_id)
        values 
        <foreach collection="userList" item="item" index= "index" separator="," >
        (#{custNo},#{item})
        </foreach>
    </insert>
    
    
    <delete id="deleteFinancialStatementVerifyPermissionById" parameterType="java.lang.String">
        delete
        from financial_statement_verify_permission
        where cust_no = #{custNo}
    </delete>
    
    
    
</mapper>