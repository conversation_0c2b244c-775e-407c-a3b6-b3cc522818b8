<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.modules.fcdataquery.mapper.FcSubjectStsMapper">
    
    <resultMap type="FcSubjectSts" id="FcSubjectStsResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="projectType"    column="project_type"    />
        <result property="projectName"    column="project_name"    />
        <result property="accountSetsId"    column="account_sets_id"    />
        <result property="accountSetsName"    column="account_sets_name"    />
        <result property="dataDay"    column="data_day"    />
        <result property="dataYear"    column="data_year"    />
        <result property="dataQuarter"    column="data_quarter"    />
        <result property="dataMonth"    column="data_month"    />
        <result property="subjectId"    column="subject_id"    />
        <result property="subjectCode"    column="subject_code"    />
        <result property="subjectName"    column="subject_name"    />
        <result property="debitAmount"    column="debit_amount"    />
        <result property="creditAmount"    column="credit_amount"    />
        <result property="operateType"    column="operate_type"    />
        <result property="feeType"    column="fee_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFcSubjectStsVo">
        select id, project_id, project_type, project_name, account_sets_id, account_sets_name, data_day, data_year, data_quarter, data_month, subject_id, subject_code, subject_name, debit_amount, credit_amount, operate_type, fee_type, create_time, update_time from fc_subject_sts
    </sql>

    <select id="selectFcSubjectStsList" resultMap="FcSubjectStsResult">
        <include refid="selectFcSubjectStsVo"/>
        <where>
            <if test="projectIds != null and !projectIds.isEmpty()">
                AND project_id IN
                <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                    #{projectId}
                </foreach>
            </if>
            <if test="subjectCodes != null and !subjectCodes.isEmpty()">
                AND (
                <foreach collection="subjectCodes" item="subjectCode" index="index">
                    subject_code like concat(#{subjectCode}, '%')
                    <if test="index &lt; subjectCodes.size() - 1">OR</if>
                </foreach>
                )
            </if>
            <if test="params.timeType == 'month'">
                and data_month between #{params.startTime} and #{params.endTime}
            </if>

            <if test="params.timeType == 'year'">
                and data_month between #{params.startTime} and #{params.endTime}
            </if>
        </where>
    </select>
    
    <select id="selectFcSubjectStsById" parameterType="Long" resultMap="FcSubjectStsResult">
        <include refid="selectFcSubjectStsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFcSubjectSts" parameterType="FcSubjectSts" useGeneratedKeys="true" keyProperty="id">
        insert into fc_subject_sts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="projectType != null">project_type,</if>
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="accountSetsId != null">account_sets_id,</if>
            <if test="accountSetsName != null and accountSetsName != ''">account_sets_name,</if>
            <if test="dataDay != null">data_day,</if>
            <if test="dataYear != null">data_year,</if>
            <if test="dataQuarter != null">data_quarter,</if>
            <if test="dataMonth != null">data_month,</if>
            <if test="subjectId != null">subject_id,</if>
            <if test="subjectCode != null and subjectCode != ''">subject_code,</if>
            <if test="subjectName != null and subjectName != ''">subject_name,</if>
            <if test="debitAmount != null">debit_amount,</if>
            <if test="creditAmount != null">credit_amount,</if>
            <if test="operateType != null">operate_type,</if>
            <if test="feeType != null">fee_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="projectType != null">#{projectType},</if>
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="accountSetsId != null">#{accountSetsId},</if>
            <if test="accountSetsName != null and accountSetsName != ''">#{accountSetsName},</if>
            <if test="dataDay != null">#{dataDay},</if>
            <if test="dataYear != null">#{dataYear},</if>
            <if test="dataQuarter != null">#{dataQuarter},</if>
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="subjectId != null">#{subjectId},</if>
            <if test="subjectCode != null and subjectCode != ''">#{subjectCode},</if>
            <if test="subjectName != null and subjectName != ''">#{subjectName},</if>
            <if test="debitAmount != null">#{debitAmount},</if>
            <if test="creditAmount != null">#{creditAmount},</if>
            <if test="operateType != null">#{operateType},</if>
            <if test="feeType != null">#{feeType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateFcSubjectSts" parameterType="FcSubjectSts">
        update fc_subject_sts
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="projectType != null">project_type = #{projectType},</if>
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="accountSetsId != null">account_sets_id = #{accountSetsId},</if>
            <if test="accountSetsName != null and accountSetsName != ''">account_sets_name = #{accountSetsName},</if>
            <if test="dataDay != null">data_day = #{dataDay},</if>
            <if test="dataYear != null">data_year = #{dataYear},</if>
            <if test="dataQuarter != null">data_quarter = #{dataQuarter},</if>
            <if test="dataMonth != null">data_month = #{dataMonth},</if>
            <if test="subjectId != null">subject_id = #{subjectId},</if>
            <if test="subjectCode != null and subjectCode != ''">subject_code = #{subjectCode},</if>
            <if test="subjectName != null and subjectName != ''">subject_name = #{subjectName},</if>
            <if test="debitAmount != null">debit_amount = #{debitAmount},</if>
            <if test="creditAmount != null">credit_amount = #{creditAmount},</if>
            <if test="operateType != null">operate_type = #{operateType},</if>
            <if test="feeType != null">fee_type = #{feeType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>



    <insert id="batchInsertOrUpdateSubjectSts">
        insert into fc_subject_sts(account_sets_id, account_sets_name, data_day, data_year, data_quarter, data_month,
                                   subject_id, subject_code, subject_name, debit_amount, credit_amount)
        values
        <foreach collection="list" item="item" index= "index" separator="," >
            (
            #{item.accountSetsId}, #{item.accountSetsName},#{item.dataDay}, #{item.dataYear}, #{item.dataQuarter}, #{item.dataMonth},
             #{item.subjectId}, #{item.subjectCode}, #{item.subjectName}, #{item.debitAmount}, #{item.creditAmount}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        account_sets_id=values(account_sets_id), account_sets_name=values(account_sets_name), data_day=values(data_day), data_year=values(data_year), data_quarter=values(data_quarter), data_month=values(data_month),
        subject_id=values(subject_id), subject_code=values(subject_code), subject_name=values(subject_name), debit_amount=values(debit_amount), credit_amount=values(credit_amount)
    </insert>

    <!--更新项目信息-->
    <update id="updateProjectInfoToMappting">
        update fc_project_mapping fpm
        inner join oa_project_deploy opd on fpm.project_id = opd.id
        set fpm.project_type = opd.project_type, fpm.project_id = opd.id, fpm.project_name = opd.project_name
        where  fpm.project_name != opd.project_name
    </update>
    <!--更新项目信息-->
    <update id="updateProjectInfoToSubject">
        update fc_subject_sts fss
        inner join fc_project_mapping fpm on fss.subject_id = fpm.subject_id
        set fss.project_id = fpm.project_id, fss.project_name = fpm.project_name, fss.project_type = fpm.project_type
        where fss.data_day = #{dataDay} and fss.project_id is null or fss.project_name != fpm.project_name
    </update>

    <!--更新项目信息-->
    <update id="updateProjectInfoToProject">
        update fc_project_sts fps
        inner join oa_project_deploy opd on fps.project_id = opd.id
        set fps.project_type = opd.project_type, fps.project_id = opd.id, fps.project_name = opd.project_name
        where fps.data_day = #{dataDay} and fps.project_name != opd.project_name
    </update>

    <!--更新科目信息-->
    <update id="updateSubjectInfo">
        update fc_subject_sts fss
        inner join (select subject_id, subject_code, subject_name from financial_voucher_details group by subject_id) fvd on fvd.subject_id = fss.subject_id
        set fss.subject_code = fvd.subject_code, fss.subject_name = fvd.subject_name
        where fss.data_day = #{dataDay} and fss.subject_code != fvd.subject_code
    </update>

    <delete id="delSubjectStsNotExist">
        delete fss from fc_subject_sts fss
        left join `financial_subject` fs on fss.`subject_id` = fs.`id`
        where fss.data_day = #{dataDay} and fs.id is null
    </delete>
    
    <update id="updateSubjectSts">
        update fc_subject_sts fss
        left join (select fvd.subject_id, fv.voucher_date from financial_voucher_details fvd
        inner join financial_voucher fv on fvd.voucher_id = fv.id and fv.valid = 1
        group by fvd.subject_id, fv.voucher_date) a on a.subject_id = fss.subject_id and fss.data_day = a.voucher_date
        set fss.credit_amount = 0, fss.debit_amount = 0
        where fss.data_day = #{dataDay} and a.subject_id is null
    </update>

    <delete id="delSubjectStsByDay">
        delete fss from fc_subject_sts fss
        left join (select fvd.subject_id, fv.voucher_date from financial_voucher_details fvd
        inner join financial_voucher fv on fvd.voucher_id = fv.id and fv.valid = 1
        group by fvd.subject_id, fv.voucher_date) a on a.subject_id = fss.subject_id and fss.data_day = a.voucher_date
        where a.subject_id is null
    </delete>

    <select id="getSubjectStsGroupBy" resultMap="FcSubjectStsResult">
        select project_id, project_type, project_name, account_sets_id, account_sets_name, data_day, data_year, data_quarter, data_month,
        subject_id, subject_code, subject_name, ifnull(debit_amount, 0) debit_amount, ifnull(credit_amount,0) credit_amount
        from fc_subject_sts
        <where>
            and data_month = #{dataMonth}
            and account_sets_id = #{accountSetsId}
        </where>
    </select>

    <select id="getSubjectStsByProjectId" parameterType="org.ruoyi.core.modules.fcdataquery.vo.ProjectIncomeDetailVo" resultMap="FcSubjectStsResult">
        select project_id, project_type, project_name, account_sets_id, subject_id, subject_code, subject_name, ifnull(debit_amount,0.00) debit_amount, ifnull(credit_amount,0.00) credit_amount from fc_subject_sts
        <where>
            and project_id = #{projectId}
            <if test="timeType == 'month'">
                and data_month = #{time}
            </if>

            <if test="timeType == 'quarter'">
                and data_quarter = #{time}
            </if>

            <if test="timeType == 'year'">
                and data_year = #{time}
            </if>
        </where>
    </select>

    <delete id="delBySubjectId">
        delete from fc_subject_sts where subject_id = #{subjectId}
    </delete>
</mapper>