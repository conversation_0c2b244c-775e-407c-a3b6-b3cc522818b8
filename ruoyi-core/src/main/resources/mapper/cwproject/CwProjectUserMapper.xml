<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cwproject.mapper.CwProjectUserMapper">
    
    <resultMap type="org.ruoyi.core.cwproject.domain.CwProjectUser" id="CwProjectUserResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userFlag"    column="user_flag"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="cwpro" type="org.ruoyi.core.cwproject.domain.CwProjectUser">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userFlag"    column="user_flag"    />
    </resultMap>

    <resultMap id="CwProjectResult" type="org.ruoyi.core.cwproject.domain.CwProject">
        <result property="id"    column="id"    />
        <result property="projectName"    column="project_name"    />
    </resultMap>

    <sql id="selectCwProjectUserVo">
        select id, project_id, user_id, user_flag, status, create_by, create_time, update_by, update_time from cw_project_user
    </sql>

    <select id="selectCwProjectUserList" parameterType="org.ruoyi.core.cwproject.domain.CwProjectUser" resultMap="CwProjectUserResult">
        <include refid="selectCwProjectUserVo"/>
        <where>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userFlag != null  and userFlag != ''"> and user_flag = #{userFlag}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectCwProjectUserById" parameterType="Long" resultMap="CwProjectUserResult">
        <include refid="selectCwProjectUserVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCwProjectUser" parameterType="org.ruoyi.core.cwproject.domain.CwProjectUser" useGeneratedKeys="true" keyProperty="id">
        insert into cw_project_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userFlag != null and userFlag != ''">user_flag,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userFlag != null and userFlag != ''">#{userFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCwProjectUser" parameterType="org.ruoyi.core.cwproject.domain.CwProjectUser">
        update cw_project_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userFlag != null and userFlag != ''">user_flag = #{userFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCwProjectUserById" parameterType="Long">
        delete from cw_project_user where id = #{id}
    </delete>

    <delete id="deleteCwProjectUserByIds" parameterType="String">
        delete from cw_project_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

<!--    <select id="selectCwProjectUserByIncomeId" parameterType="Long" resultType="map">-->
<!--        SELECT su.nick_name,u.user_flag FROM sys_user su,cw_project_user u,cw_project_income i WHERE su.user_id=u.user_id AND u.project_id=i.project_id AND i.id=#{id,jdbcType=BIGINT} AND u.status='0'-->
<!--    </select>-->

<!--    <select id="selectCwProjectUserByFeeId" parameterType="Long" resultType="map">-->
<!--        SELECT su.nick_name,u.user_flag FROM sys_user su,cw_project_user u,cw_project_fee f WHERE su.user_id=u.user_id AND u.project_id=f.project_id AND f.id=#{id,jdbcType=BIGINT} AND u.status='0'-->
<!--    </select>-->

<!--    <select id="selectCwProjectUserByPayId" parameterType="Long" resultType="map">-->
<!--        SELECT su.nick_name,u.user_flag FROM sys_user su,cw_project_user u,cw_project_fee f WHERE su.user_id=u.user_id AND u.project_id=f.project_id AND f.id=#{id,jdbcType=BIGINT} AND u.status='0'-->
<!--    </select>-->
    <select id="selectCwProjectUserByProjectId" resultType="java.util.Map">
        SELECT s.user_id,s.nick_name,u.user_flag,opd.project_type FROM cw_project_user u,sys_user s,cw_project cp
        LEFT JOIN oa_project_deploy opd ON opd.id=cp.oa_project_deploy_id
        WHERE u.user_id=s.user_id AND u.project_id=cp.id AND u.project_id=#{id,jdbcType=BIGINT}
    </select>

    <select id="selectCwProjectUserByUserId" resultMap="CwProjectUserResult">
        SELECT project_id FROM cw_project_user WHERE user_id=#{userId,jdbcType=BIGINT}
    </select>

    <select id="selectCwProjectUserByUserIdWhenUserFlagZeroAndOne" resultMap="CwProjectResult">
        SELECT u.project_id id,p.project_name FROM cw_project_user u LEFT JOIN cw_project p ON u.project_id=p.id WHERE u.user_id=#{userId,jdbcType=BIGINT} AND (u.user_flag='0' OR u.user_flag='1')
    </select>

    <select id="selectCwProjectUserByUserIdWhenUserFlagTwo" resultMap="CwProjectResult">
        SELECT u.project_id id,p.project_name FROM cw_project_user u LEFT JOIN cw_project p ON u.project_id=p.id WHERE u.user_id=#{userId,jdbcType=BIGINT} AND u.user_flag='2'
    </select>

    <select id="selectCwProjectUserByUserIdWhenUserFlagZero" resultMap="CwProjectResult">
        SELECT u.project_id id,p.project_name FROM cw_project_user u LEFT JOIN cw_project p ON u.project_id=p.id WHERE u.user_id=#{userId,jdbcType=BIGINT} AND u.user_flag='0'
    </select>

    <select id="selectCwProjectUserByUserIdWhenUserFlagOne" resultMap="CwProjectResult">
        SELECT u.project_id id,p.project_name FROM cw_project_user u LEFT JOIN cw_project p ON u.project_id=p.id WHERE u.user_id=#{userId,jdbcType=BIGINT} AND u.user_flag='1'
    </select>

    <select id="selectUserRoleIsAdmin" resultType="java.lang.String">
        SELECT r.role_key FROM sys_user_role ur,sys_role r WHERE ur.role_id=r.role_id AND ur.user_id=#{userId,jdbcType=BIGINT}
    </select>

    <update id="updatePsalesmanStatus" parameterType="long">
        update cw_project_user cpu set cpu.status='1' where cpu.project_id=#{id} and cpu.user_flag='2'
    </update>
    <select id="selectCwProjectUserByProjectId1" parameterType="long" resultMap="CwProjectUserResult">
        select * from cw_project_user where project_id=#{id}
    </select>

    <select id="selectUserByProjectId" resultMap="cwpro">
        select cpu.* from cw_project_user cpu where cpu.project_id = #{projectId}
    </select>

    <select id="selectCwProjectUserByProjectId2" resultType="java.util.Map">
        SELECT u.id,u.project_id,s.user_id,s.nick_name,u.user_flag FROM cw_project_user u,sys_user s WHERE u.user_id=s.user_id AND u.project_id=#{id,jdbcType=BIGINT}
    </select>



    <delete id="deleteByprojectId">
        delete from cw_project_user where project_id  = #{id} and user_flag = #{userFlag}
    </delete>
    <select id="selectByprojectid" resultType="long">
        select user_id from cw_project_user where project_id  = #{id} and user_flag = '2'
    </select>

    <select id="getUserIdListByProId" resultType="java.lang.Long">
        select user_id from cw_project_user where project_id  = #{projectId} and user_flag = #{userFlag}
    </select>


    <select id="getUserNameList" resultType="string">
        SELECT nick_name AS nickName FROM sys_user WHERE user_id IN ( select user_id from cw_project_user where project_id  = #{id} and user_flag = #{userFlag} )
    </select>




    <select id="getProjectIdByUserId" resultType="java.lang.Long">
        select a.project_id from cw_project_user as a LEFT JOIN cw_project as p ON a.project_id = p.id  where a.user_id  = #{userId} and a.status = 0 AND p.project_flag = #{projectFlag} and p.`status` = 0 GROUP BY project_id
    </select>

    <select id="checkExport" resultType="java.lang.Long">
        select id from cw_project_user where project_id = #{projectId} AND user_id = #{userId} AND user_flag = #{userFlag}
    </select>


    <select id="getExportProjectIdByUserId" resultType="map">
        select a.project_id as id,opd.project_type as projectType from cw_project_user as a LEFT JOIN cw_project as p ON a.project_id = p.id
        LEFT JOIN oa_project_deploy opd ON opd.id=p.oa_project_deploy_id
         where a.user_id  = #{userId} and a.status = 0 AND a.user_flag != '3' AND p.project_flag = #{projectFlag} and p.`status` = 0 GROUP BY project_id
    </select>
    <select id="checkExportRoleNum" resultType="java.lang.Long">

        select id from cw_project_user where  user_id = #{userId} AND user_flag = #{userFlag}
    </select>
    <select id="selectByProIdAndType" resultType="org.ruoyi.core.cwproject.domain.CwProjectUser">
        <include refid="selectCwProjectUserVo"/>
        where status = 0 and project_id = #{projectId} and user_flag = #{userType} LIMIT 1
    </select>
</mapper>