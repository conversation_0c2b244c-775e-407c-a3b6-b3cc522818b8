<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.kaohe.mapper.ColumnsInitMapper">

    <resultMap type="ColumnsInit" id="ColumnsInitResult">
        <result property="id"    column="id"    />
        <result property="module"    column="module"    />
        <result property="columns"    column="columns"    />
        <result property="visible"    column="visible"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectColumnsInitVo">
        select id, module, columns, visible, create_by, create_time, update_by, update_time from kh_columns_init
    </sql>

    <select id="selectColumnsInitList" parameterType="ColumnsInit" resultMap="ColumnsInitResult">
        <include refid="selectColumnsInitVo"/>
        <where>
            <if test="module != null "> and module = #{module}</if>
            <if test="columns != null  and columns != ''"> and columns = #{columns}</if>
            <if test="visible != null  and visible != ''"> and visible = #{visible}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
        </where>
    </select>

    <select id="selectColumnsInitById" parameterType="Long" resultMap="ColumnsInitResult">
        <include refid="selectColumnsInitVo"/>
        where id = #{id}
    </select>

    <insert id="insertColumnsInit" parameterType="ColumnsInit" useGeneratedKeys="true" keyProperty="id">
        insert into kh_columns_init
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="module != null">module,</if>
            <if test="columns != null">columns,</if>
            <if test="visible != null">visible,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="module != null">#{module},</if>
            <if test="columns != null">#{columns},</if>
            <if test="visible != null">#{visible},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="batchColumnsInit" parameterType="java.util.List">
        INSERT INTO kh_columns_init
            (module,columns ,visible,create_by,create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.module}, #{item.columns},#{item.visible}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <update id="replaceColumnsInit" parameterType="java.util.List">
        REPLACE INTO kh_columns_init
            (id,module,columns ,visible,create_by,create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.module}, #{item.columns},#{item.visible}, #{item.createBy}, #{item.createTime})
        </foreach>
    </update>

    <update id="updateColumnsInit" parameterType="ColumnsInit">
        update kh_columns_init
        <trim prefix="SET" suffixOverrides=",">
            <if test="module != null">module = #{module},</if>
            <if test="columns != null">columns = #{columns},</if>
            <if test="visible != null">visible = #{visible},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteColumnsInitById" parameterType="Long">
        delete from kh_columns_init where id = #{id}
    </delete>

    <delete id="deleteColumnsInitByIds" parameterType="String">
        delete from kh_columns_init where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
