<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.qiyeVX.mapper.VXMapper">
    
    <resultMap type="VxUser" id="VxUserResult">
        <result property="id"    column="id"    />
        <result property="vxId"    column="vx_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />

        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectVxUserVo">
        select id, vx_id, user_id,user_Name, create_time, update_time from vx_user
    </sql>

    <select id="selectVxUserList" parameterType="VxUser" resultMap="VxUserResult">
        <include refid="selectVxUserVo"/>
        <where>  
            <if test="vxId != null  and vxId != ''"> and vx_id like  CONCAT('%',#{vxId},'%')  </if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
        </where>
    </select>
    
    <select id="selectVxUserById" parameterType="Long" resultMap="VxUserResult">
        <include refid="selectVxUserVo"/>
        where id = #{id}
    </select>

    <insert id="insertVxUser" parameterType="VxUser">
        insert into vx_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="vxId != null">vx_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="vxId != null">#{vxId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateVxUser" parameterType="VxUser">
        update vx_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="vxId != null">vx_id = #{vxId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_Name = #{userName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVxUserById" parameterType="Long">
        delete from vx_user where id = #{id}
    </delete>

    <delete id="deleteVxUserByIds" parameterType="String">
        delete from vx_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert  into vx_user
        (vx_id, user_id, user_Name, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.vxId,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR},#{item.userName,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},#{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
        on duplicate key update
        update_time = VALUES(update_time)
    </insert>


    <select id="selectByUserId" resultType="org.ruoyi.core.qiyeVX.domain.VxUser">
        select * from  vx_user where
        user_id in
        <foreach item="id" collection="userList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectSingleByUserId" resultType="org.ruoyi.core.qiyeVX.domain.VxUser">
        select * from  vx_user where
            user_id = #{userId}
    </select>

</mapper>